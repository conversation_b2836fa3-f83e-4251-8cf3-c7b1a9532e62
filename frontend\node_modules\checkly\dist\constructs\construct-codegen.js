"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConstructCodegen = exports.sortResources = void 0;
const codegen_1 = require("./internal/codegen");
const alert_channel_codegen_1 = require("./alert-channel-codegen");
const alert_channel_subscription_codegen_1 = require("./alert-channel-subscription-codegen");
const check_codegen_1 = require("./check-codegen");
const check_group_codegen_1 = require("./check-group-codegen");
const dashboard_codegen_1 = require("./dashboard-codegen");
const maintenance_window_codegen_1 = require("./maintenance-window-codegen");
const private_location_codegen_1 = require("./private-location-codegen");
const private_location_check_assignment_codegen_1 = require("./private-location-check-assignment-codegen");
const private_location_group_assignment_codegen_1 = require("./private-location-group-assignment-codegen");
const status_page_service_codegen_1 = require("./status-page-service-codegen");
const status_page_codegen_1 = require("./status-page-codegen");
const resourceOrder = {
    'alert-channel-subscription': 800,
    'alert-channel': 810,
    'check-group': 700,
    check: 600,
    dashboard: 0,
    'maintenance-window': 0,
    'private-location-check-assignment': 900,
    'private-location-group-assignment': 900,
    'private-location': 910,
    'status-page': 500,
    'status-page-service': 510,
};
function sortResources(resources) {
    return resources.sort((a, b) => {
        const ao = resourceOrder[a.type] ?? -1;
        const bo = resourceOrder[b.type] ?? -1;
        return bo - ao;
    });
}
exports.sortResources = sortResources;
class ConstructCodegen extends codegen_1.Codegen {
    alertChannelCodegen;
    alertChannelSubscriptionCodegen;
    checkCodegen;
    checkGroupCodegen;
    dashboardCodegen;
    maintenanceWindowCodegen;
    privateLocationCodegen;
    privateLocationCheckAssignmentCodegen;
    privateLocationGroupAssignmentCodegen;
    statusPageCodegen;
    statusPageServiceCodegen;
    codegensByType;
    constructor(program) {
        super(program);
        this.alertChannelCodegen = new alert_channel_codegen_1.AlertChannelCodegen(program);
        this.alertChannelSubscriptionCodegen = new alert_channel_subscription_codegen_1.AlertChannelSubscriptionCodegen(program);
        this.checkCodegen = new check_codegen_1.CheckCodegen(program);
        this.checkGroupCodegen = new check_group_codegen_1.CheckGroupCodegen(program);
        this.dashboardCodegen = new dashboard_codegen_1.DashboardCodegen(program);
        this.maintenanceWindowCodegen = new maintenance_window_codegen_1.MaintenanceWindowCodegen(program);
        this.privateLocationCodegen = new private_location_codegen_1.PrivateLocationCodegen(program);
        this.privateLocationCheckAssignmentCodegen = new private_location_check_assignment_codegen_1.PrivateLocationCheckAssignmentCodegen(program);
        this.privateLocationGroupAssignmentCodegen = new private_location_group_assignment_codegen_1.PrivateLocationGroupAssignmentCodegen(program);
        this.statusPageCodegen = new status_page_codegen_1.StatusPageCodegen(program);
        this.statusPageServiceCodegen = new status_page_service_codegen_1.StatusPageServiceCodegen(program);
        this.codegensByType = {
            'alert-channel-subscription': this.alertChannelSubscriptionCodegen,
            'alert-channel': this.alertChannelCodegen,
            'check-group': this.checkGroupCodegen,
            check: this.checkCodegen,
            dashboard: this.dashboardCodegen,
            'maintenance-window': this.maintenanceWindowCodegen,
            'private-location-check-assignment': this.privateLocationCheckAssignmentCodegen,
            'private-location-group-assignment': this.privateLocationGroupAssignmentCodegen,
            'private-location': this.privateLocationCodegen,
            'status-page': this.statusPageCodegen,
            'status-page-service': this.statusPageServiceCodegen,
        };
    }
    describe(resource) {
        const codegen = this.codegensByType[resource.type];
        if (codegen === undefined) {
            throw new Error(`Unable to describe unsupported resource type '${resource.type}'.`);
        }
        return codegen.describe(resource.payload);
    }
    prepare(logicalId, resource, context) {
        const codegen = this.codegensByType[resource.type];
        if (codegen === undefined) {
            throw new Error(`Unable to generate code for unsupported resource type '${resource.type}'.`);
        }
        codegen.prepare(resource.logicalId, resource.payload, context);
    }
    gencode(logicalId, resource, context) {
        const codegen = this.codegensByType[resource.type];
        if (codegen === undefined) {
            throw new Error(`Unable to generate code for unsupported resource type '${resource.type}'.`);
        }
        codegen.gencode(resource.logicalId, resource.payload, context);
    }
}
exports.ConstructCodegen = ConstructCodegen;
//# sourceMappingURL=construct-codegen.js.map