{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-http/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-http/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,kQAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-http/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-http/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,+OAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-http/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,kPAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-http/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,gPAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,wPAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-http/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,wPAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,gQAAI,UAAO,6PAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;oQAED,UAAO,6PAAC,sBAAmB,CAAC,mQAAG,aAAA,AAAU,6PACvC,uCAAmC,EACnC,QAAQ,qPACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,iQAAA,UAAO,6PAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,6PAA5B,UAAO,6PAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,mQAAO,UAAO,6PAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,wPAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-http/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,+OAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry/node/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 367, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry/node/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,wOAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry/node/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry/node/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,qNAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry/node/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,wNAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 526, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry/node/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,sNAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,8NAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 576, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry/node/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,8NAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,sOAAI,UAAO,mOAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;0OAED,UAAO,mOAAC,sBAAmB,CAAC,yOAAG,aAAA,AAAU,mOACvC,uCAAmC,EACnC,QAAQ,2NACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,uOAAA,UAAO,mOAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,mOAA5B,UAAO,mOAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,yOAAO,UAAO,mOAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,8NAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 646, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry/node/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,qNAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 677, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-undici/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 702, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-undici/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,oQAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 738, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-undici/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 769, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-undici/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,iPAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 804, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-undici/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,oPAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 861, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-undici/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,kPAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,0PAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 911, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-undici/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,0PAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,kQAAI,UAAO,+PAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;sQAED,UAAO,+PAAC,sBAAmB,CAAC,qQAAG,aAAA,AAAU,+PACvC,uCAAmC,EACnC,QAAQ,uPACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,mQAAA,UAAO,+PAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,+PAA5B,UAAO,+PAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,qQAAO,UAAO,+PAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,0PAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 981, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-undici/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,iPAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1012, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-fs/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1037, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-fs/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,gQAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1073, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-fs/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1104, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-fs/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,6OAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1139, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-fs/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,gPAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1196, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-fs/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,8OAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,sPAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1246, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-fs/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,sPAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,8PAAI,UAAO,2PAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;kQAED,UAAO,2PAAC,sBAAmB,CAAC,iQAAG,aAAA,AAAU,2PACvC,uCAAmC,EACnC,QAAQ,mPACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,+PAAA,UAAO,2PAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,2PAA5B,UAAO,2PAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,iQAAO,UAAO,2PAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,sPAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1316, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-fs/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,6OAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1347, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-express/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1372, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-express/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,qQAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1408, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-express/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1439, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-express/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,kPAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1474, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-express/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,qPAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1531, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-express/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,mPAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,2PAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1581, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-express/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,2PAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,mQAAI,UAAO,gQAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;uQAED,UAAO,gQAAC,sBAAmB,CAAC,sQAAG,aAAA,AAAU,gQACvC,uCAAmC,EACnC,QAAQ,wPACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,oQAAA,UAAO,gQAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,gQAA5B,UAAO,gQAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,sQAAO,UAAO,gQAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,2PAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1651, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-express/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,kPAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1682, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-graphql/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1707, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-graphql/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,qQAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1743, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-graphql/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1774, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-graphql/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,kPAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1809, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-graphql/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,qPAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1866, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-graphql/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,mPAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,2PAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1916, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-graphql/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,2PAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,mQAAI,UAAO,gQAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;uQAED,UAAO,gQAAC,sBAAmB,CAAC,sQAAG,aAAA,AAAU,gQACvC,uCAAmC,EACnC,QAAQ,wPACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,oQAAA,UAAO,gQAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,gQAA5B,UAAO,gQAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,sQAAO,UAAO,gQAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,2PAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1986, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-graphql/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,kPAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2017, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-kafkajs/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2042, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-kafkajs/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,qQAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2078, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-kafkajs/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2109, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-kafkajs/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,kPAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2144, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-kafkajs/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,qPAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2201, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-kafkajs/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,mPAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,2PAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2251, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-kafkajs/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,2PAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,mQAAI,UAAO,gQAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;uQAED,UAAO,gQAAC,sBAAmB,CAAC,sQAAG,aAAA,AAAU,gQACvC,uCAAmC,EACnC,QAAQ,wPACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,oQAAA,UAAO,gQAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,gQAA5B,UAAO,gQAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,sQAAO,UAAO,gQAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,2PAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2321, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-kafkajs/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,kPAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2352, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-lru-memoizer/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2377, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-lru-memoizer/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,6QAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2413, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-lru-memoizer/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2444, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-lru-memoizer/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,0PAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2479, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-lru-memoizer/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,6PAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2536, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-lru-memoizer/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,2PAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,mQAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2586, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-lru-memoizer/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,mQAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,2QAAI,UAAO,wQAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;+QAED,UAAO,wQAAC,sBAAmB,CAAC,8QAAG,aAAA,AAAU,wQACvC,uCAAmC,EACnC,QAAQ,gQACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,4QAAA,UAAO,wQAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,wQAA5B,UAAO,wQAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,8QAAO,UAAO,wQAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,mQAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2656, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-lru-memoizer/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,0PAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2687, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mongodb/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2712, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mongodb/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,qQAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2748, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mongodb/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2779, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mongodb/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,kPAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2814, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mongodb/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,qPAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2871, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mongodb/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,mPAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,2PAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2921, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mongodb/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,2PAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,mQAAI,UAAO,gQAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;uQAED,UAAO,gQAAC,sBAAmB,CAAC,sQAAG,aAAA,AAAU,gQACvC,uCAAmC,EACnC,QAAQ,wPACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,oQAAA,UAAO,gQAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,gQAA5B,UAAO,gQAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,sQAAO,UAAO,gQAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,2PAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2991, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mongodb/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,kPAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3022, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mongoose/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3047, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mongoose/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,sQAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3083, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mongoose/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3114, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mongoose/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,mPAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3149, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mongoose/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,sPAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3206, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mongoose/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,oPAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,4PAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3256, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mongoose/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,4PAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,oQAAI,UAAO,iQAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;wQAED,UAAO,iQAAC,sBAAmB,CAAC,uQAAG,aAAA,AAAU,iQACvC,uCAAmC,EACnC,QAAQ,yPACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,qQAAA,UAAO,iQAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,iQAA5B,UAAO,iQAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,uQAAO,UAAO,iQAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,4PAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3326, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mongoose/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,mPAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3357, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mysql/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3382, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mysql/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,mQAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3418, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mysql/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3449, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mysql/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,gPAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3484, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mysql/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,mPAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3541, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mysql/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,iPAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,yPAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3591, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mysql/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,yPAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,iQAAI,UAAO,8PAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;qQAED,UAAO,8PAAC,sBAAmB,CAAC,oQAAG,aAAA,AAAU,8PACvC,uCAAmC,EACnC,QAAQ,sPACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,kQAAA,UAAO,8PAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,8PAA5B,UAAO,8PAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,oQAAO,UAAO,8PAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,yPAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3661, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mysql/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,gPAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3692, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mysql2/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3717, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mysql2/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,oQAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3753, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mysql2/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3784, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mysql2/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,iPAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3819, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mysql2/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,oPAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3876, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mysql2/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,kPAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,0PAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3926, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mysql2/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,0PAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,kQAAI,UAAO,+PAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;sQAED,UAAO,+PAAC,sBAAmB,CAAC,qQAAG,aAAA,AAAU,+PACvC,uCAAmC,EACnC,QAAQ,uPACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,mQAAA,UAAO,+PAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,+PAA5B,UAAO,+PAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,qQAAO,UAAO,+PAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,0PAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3996, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-mysql2/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,iPAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4027, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-ioredis/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4052, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-ioredis/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,qQAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4088, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-ioredis/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4119, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-ioredis/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,kPAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4154, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-ioredis/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,qPAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4211, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-ioredis/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,mPAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,2PAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4261, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-ioredis/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,2PAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,mQAAI,UAAO,gQAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;uQAED,UAAO,gQAAC,sBAAmB,CAAC,sQAAG,aAAA,AAAU,gQACvC,uCAAmC,EACnC,QAAQ,wPACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,oQAAA,UAAO,gQAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,gQAA5B,UAAO,gQAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,sQAAO,UAAO,gQAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,2PAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4331, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-ioredis/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,kPAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4362, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-redis-4/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4387, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-redis-4/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,wQAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4423, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-redis-4/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4454, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-redis-4/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,qPAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4489, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-redis-4/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,wPAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4546, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-redis-4/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,sPAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,8PAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4596, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-redis-4/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,8PAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,sQAAI,UAAO,mQAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;0QAED,UAAO,mQAAC,sBAAmB,CAAC,yQAAG,aAAA,AAAU,mQACvC,uCAAmC,EACnC,QAAQ,2PACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,uQAAA,UAAO,mQAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,mQAA5B,UAAO,mQAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,yQAAO,UAAO,mQAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,8PAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4666, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-redis-4/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,qPAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4697, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-pg/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4722, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-pg/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,gQAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4758, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-pg/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4789, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-pg/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,6OAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4824, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-pg/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,gPAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4881, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-pg/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,8OAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,sPAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4931, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-pg/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,sPAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,8PAAI,UAAO,2PAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;kQAED,UAAO,2PAAC,sBAAmB,CAAC,iQAAG,aAAA,AAAU,2PACvC,uCAAmC,EACnC,QAAQ,mPACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,+PAAA,UAAO,2PAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,2PAA5B,UAAO,2PAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,iQAAO,UAAO,2PAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,sPAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5001, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-pg/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,6OAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5032, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40prisma/instrumentation/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5057, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40prisma/instrumentation/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,mPAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5093, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40prisma/instrumentation/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5124, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40prisma/instrumentation/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,gOAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5159, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40prisma/instrumentation/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,mOAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5216, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40prisma/instrumentation/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,iOAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,yOAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5266, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40prisma/instrumentation/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,yOAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,iPAAI,UAAO,8OAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;qPAED,UAAO,8OAAC,sBAAmB,CAAC,oPAAG,aAAA,AAAU,8OACvC,uCAAmC,EACnC,QAAQ,sOACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,kPAAA,UAAO,8OAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,8OAA5B,UAAO,8OAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,oPAAO,UAAO,8OAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,yOAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5336, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40prisma/instrumentation/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,gOAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5367, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-hapi/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5392, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-hapi/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,kQAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5428, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-hapi/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5459, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-hapi/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,+OAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5494, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-hapi/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,kPAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5551, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-hapi/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,gPAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,wPAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5601, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-hapi/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,wPAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,gQAAI,UAAO,6PAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;oQAED,UAAO,6PAAC,sBAAmB,CAAC,mQAAG,aAAA,AAAU,6PACvC,uCAAmC,EACnC,QAAQ,qPACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,iQAAA,UAAO,6PAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,6PAA5B,UAAO,6PAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,mQAAO,UAAO,6PAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,wPAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5671, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-hapi/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,+OAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5702, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-koa/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5727, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-koa/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,iQAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5763, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-koa/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5794, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-koa/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,8OAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5829, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-koa/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,iPAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5886, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-koa/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,+OAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,uPAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5936, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-koa/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,uPAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,+PAAI,UAAO,4PAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;mQAED,UAAO,4PAAC,sBAAmB,CAAC,kQAAG,aAAA,AAAU,4PACvC,uCAAmC,EACnC,QAAQ,oPACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,gQAAA,UAAO,4PAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,4PAA5B,UAAO,4PAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,kQAAO,UAAO,4PAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,uPAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6006, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-koa/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,8OAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6037, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-connect/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6062, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-connect/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,qQAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6098, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-connect/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6129, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-connect/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,kPAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6164, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-connect/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,qPAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6221, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-connect/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,mPAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,2PAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6271, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-connect/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,2PAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,mQAAI,UAAO,gQAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;uQAED,UAAO,gQAAC,sBAAmB,CAAC,sQAAG,aAAA,AAAU,gQACvC,uCAAmC,EACnC,QAAQ,wPACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,oQAAA,UAAO,gQAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,gQAA5B,UAAO,gQAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,sQAAO,UAAO,gQAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,2PAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6341, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-connect/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,kPAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6372, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-knex/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6397, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-knex/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,kQAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6433, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-knex/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6464, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-knex/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,+OAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6499, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-knex/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,kPAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6556, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-knex/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,gPAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,wPAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6606, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-knex/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,wPAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,gQAAI,UAAO,6PAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;oQAED,UAAO,6PAAC,sBAAmB,CAAC,mQAAG,aAAA,AAAU,6PACvC,uCAAmC,EACnC,QAAQ,qPACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,iQAAA,UAAO,6PAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,6PAA5B,UAAO,6PAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,mQAAO,UAAO,6PAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,wPAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6676, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-knex/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,+OAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6707, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-tedious/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6732, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-tedious/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,qQAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6768, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-tedious/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6799, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-tedious/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,kPAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6834, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-tedious/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,qPAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6891, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-tedious/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,mPAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,2PAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6941, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-tedious/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,2PAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,mQAAI,UAAO,gQAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;uQAED,UAAO,gQAAC,sBAAmB,CAAC,sQAAG,aAAA,AAAU,gQACvC,uCAAmC,EACnC,QAAQ,wPACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,oQAAA,UAAO,gQAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,gQAA5B,UAAO,gQAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,sQAAO,UAAO,gQAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,2PAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7011, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-tedious/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,kPAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7042, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-generic-pool/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7067, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-generic-pool/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,6QAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7103, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-generic-pool/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7134, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-generic-pool/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,0PAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7169, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-generic-pool/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,6PAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7226, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-generic-pool/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,2PAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,mQAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7276, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-generic-pool/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,mQAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,2QAAI,UAAO,wQAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;+QAED,UAAO,wQAAC,sBAAmB,CAAC,8QAAG,aAAA,AAAU,wQACvC,uCAAmC,EACnC,QAAQ,gQACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,4QAAA,UAAO,wQAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,wQAA5B,UAAO,wQAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,8QAAO,UAAO,wQAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,mQAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7346, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-generic-pool/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,0PAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7377, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-dataloader/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7402, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-dataloader/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,wQAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7438, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-dataloader/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7469, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-dataloader/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,qPAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7504, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-dataloader/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,wPAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7561, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-dataloader/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,sPAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,8PAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7611, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-dataloader/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,8PAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,sQAAI,UAAO,mQAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;0QAED,UAAO,mQAAC,sBAAmB,CAAC,yQAAG,aAAA,AAAU,mQACvC,uCAAmC,EACnC,QAAQ,2PACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,uQAAA,UAAO,mQAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,mQAA5B,UAAO,mQAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,yQAAO,UAAO,mQAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,8PAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7681, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-dataloader/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,qPAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7712, "column": 0}, "map": {"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-amqplib/node_modules/%40opentelemetry/api-logs/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7737, "column": 0}, "map": {"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-amqplib/node_modules/%40opentelemetry/api-logs/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { _globalThis } from '../platform';\n\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\n\ntype Get<T> = (version: number) => T;\ntype OtelGlobal = Partial<{\n  [GLOBAL_LOGS_API_KEY]: Get<LoggerProvider>;\n}>;\n\nexport const _global = _globalThis as OtelGlobal;\n\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter<T>(\n  requiredVersion: number,\n  instance: T,\n  fallback: T\n): Get<T> {\n  return (version: number): T =>\n    version === requiredVersion ? instance : fallback;\n}\n\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;;AAEnC,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;AAOvE,IAAM,OAAO,qQAAG,cAAyB,CAAC;AAU3C,SAAU,UAAU,CACxB,eAAuB,EACvB,QAAW,EACX,QAAW;IAEX,OAAO,SAAC,OAAe;QACrB,OAAA,OAAO,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAAjD,CAAiD,CAAC;AACtD,CAAC;AASM,IAAM,mCAAmC,GAAG,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7773, "column": 0}, "map": {"version": 3, "file": "NoopLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-amqplib/node_modules/%40opentelemetry/api-logs/src/NoopLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './types/Logger';\nimport { LogRecord } from './types/LogRecord';\n\nexport class NoopLogger implements Logger {\n  emit(_logRecord: LogRecord): void {}\n}\n\nexport const NOOP_LOGGER = new NoopLogger();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,IAAA,aAAA;IAAA,SAAA,cAEA,CAAC;IADC,WAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,UAAqB,GAAS,CAAC;IACtC,OAAA,UAAC;AAAD,CAAC,AAFD,IAEC;;AAEM,IAAM,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7804, "column": 0}, "map": {"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-amqplib/node_modules/%40opentelemetry/api-logs/src/NoopLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAKH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAE1C,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,kPAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC;;AAEM,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7839, "column": 0}, "map": {"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-amqplib/node_modules/%40opentelemetry/api-logs/src/ProxyLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;;AAK3C,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAqB;QAC5B,IAAA,CAAA,OAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG,CACH,YAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,qPAAO,cAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AAvCD,IAuCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7896, "column": 0}, "map": {"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-amqplib/node_modules/%40opentelemetry/api-logs/src/ProxyLoggerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;;;AAE5C,IAAA,sBAAA;IAAA,SAAA,uBAgCA,CAAC;IA7BC,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,mPAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,2PAAI,uBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AAhCD,IAgCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7946, "column": 0}, "map": {"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-amqplib/node_modules/%40opentelemetry/api-logs/src/api/logs.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private _proxyLoggerProvider = new ProxyLoggerProvider();\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n    this._proxyLoggerProvider.setDelegate(provider);\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      this._proxyLoggerProvider\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n    this._proxyLoggerProvider = new ProxyLoggerProvider();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAG7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;;;;AAE7D,IAAA,UAAA;IAKE,SAAA;QAFQ,IAAA,CAAA,oBAAoB,GAAG,2PAAI,sBAAmB,EAAE,CAAC;IAElC,CAAC;IAEV,QAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,QAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,mQAAI,UAAO,gQAAC,sBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;uQAED,UAAO,gQAAC,sBAAmB,CAAC,sQAAG,aAAA,AAAU,gQACvC,uCAAmC,EACnC,QAAQ,wPACR,uBAAoB,CACrB,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,iBAAiB,GAAxB;;QACE,OAAO,AACL,CAAA,KAAA,CAAA,oQAAA,UAAO,gQAAC,sBAAmB,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,gQAA5B,UAAO,gQAAwB,uCAAmC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KACnE,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,QAAA,SAAA,CAAA,SAAS,GAAhB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,sCAAA,EAAwC,CACjC,QAAA,SAAA,CAAA,OAAO,GAAd;QACE,sQAAO,UAAO,gQAAC,sBAAmB,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,GAAG,2PAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IACH,OAAA,OAAC;AAAD,CAAC,AA5DD,IA4DC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8016, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40opentelemetry/instrumentation-amqplib/node_modules/%40opentelemetry/api-logs/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Logger } from './types/Logger';\nexport { LoggerProvider } from './types/LoggerProvider';\nexport {\n  LogAttributes,\n  LogBody,\n  LogRecord,\n  SeverityNumber,\n} from './types/LogRecord';\nexport { LoggerOptions } from './types/LoggerOptions';\nexport { AnyValue, AnyValueMap } from './types/AnyValue';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAiBH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;;;;;;AAC9B,IAAM,IAAI,kPAAG,UAAO,CAAC,WAAW,EAAE,CAAC", "ignoreList": [0], "debugId": null}}]}