"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LookupContext = void 0;
const package_json_file_1 = require("./package-json-file");
const extension_1 = require("./extension");
class LookupContext {
    #plainJs;
    constructor(options) {
        this.#plainJs = options.plainJs ?? false;
    }
    static forFilePath(filePath) {
        return new LookupContext({
            plainJs: extension_1.FileExtPath.fromFilePath(filePath).hasCoreExtension(),
        });
    }
    collectLookupPaths(filePath) {
        const extPath = extension_1.FileExtPath.fromFilePath(filePath);
        if (this.#plainJs) {
            if (extPath.hasCoreExtension()) {
                return [extPath.self()];
            }
            return this.extlessCoreLookupPaths(extPath);
        }
        if (extPath.hasCoreExtension()) {
            const extensions = extension_1.tsCoreExtensionLookupOrder[extPath.ext];
            return extensions.map(ext => extPath.replaceExt(ext));
        }
        if (extPath.hasTypeScriptExtension()) {
            const extensions = extension_1.tsExtensionLookupOrder[extPath.ext];
            return extensions.map(ext => extPath.replaceExt(ext));
        }
        return this.extlessTSLookupPaths(extPath);
    }
    extlessCoreLookupPaths(extPath) {
        return [
            extPath.appendExt('.js'),
            extPath.appendExt('.mjs'),
            extPath.appendExt('.cjs'),
            extPath.appendExt('.json'),
            extPath.resolve(package_json_file_1.PackageJsonFile.FILENAME),
            extPath.resolve('index.js'),
            extPath.resolve('index.mjs'),
            extPath.resolve('index.cjs'),
            extPath.resolve('index.json'), // Yes, this works.
        ];
    }
    extlessTSLookupPaths(extPath) {
        return this.extlessCoreLookupPaths(extPath).flatMap(filePath => {
            const extPath = extension_1.FileExtPath.fromFilePath(filePath);
            const extensions = extension_1.tsCoreExtensionLookupOrder[extPath.ext];
            return extensions.map(ext => extPath.replaceExt(ext));
        });
    }
}
exports.LookupContext = LookupContext;
//# sourceMappingURL=lookup.js.map