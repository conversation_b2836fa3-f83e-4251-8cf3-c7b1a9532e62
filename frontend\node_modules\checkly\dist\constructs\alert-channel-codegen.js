"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlertChannelCodegen = exports.valueForAlertChannelFromId = exports.buildAlertChannelProps = void 0;
const codegen_1 = require("./internal/codegen");
const sourcegen_1 = require("../sourcegen");
const email_alert_channel_codegen_1 = require("./email-alert-channel-codegen");
const opsgenie_alert_channel_codegen_1 = require("./opsgenie-alert-channel-codegen");
const pagerduty_alert_channel_codegen_1 = require("./pagerduty-alert-channel-codegen");
const phone_call_alert_channel_codegen_1 = require("./phone-call-alert-channel-codegen");
const slack_alert_channel_codegen_1 = require("./slack-alert-channel-codegen");
const sms_alert_channel_codegen_1 = require("./sms-alert-channel-codegen");
const webhook_alert_channel_codegen_1 = require("./webhook-alert-channel-codegen");
function buildAlertChannelProps(builder, resource) {
    // The default value for sendRecovery is true, only include if false.
    if (resource.sendRecovery !== undefined && !resource.sendRecovery) {
        builder.boolean('sendRecovery', resource.sendRecovery);
    }
    // The default value for sendFailure is true, only include if false.
    if (resource.sendFailure !== undefined && !resource.sendFailure) {
        builder.boolean('sendFailure', resource.sendFailure);
    }
    // The default value for sendDegraded is false, only include if true.
    if (resource.sendDegraded !== undefined && resource.sendDegraded) {
        builder.boolean('sendDegraded', resource.sendDegraded);
    }
    // The default value for sslExpiry is false, only include if true.
    if (resource.sslExpiry !== undefined && resource.sslExpiry) {
        builder.boolean('sslExpiry', resource.sslExpiry);
    }
    // The default value for sslExpiryThreshold is 30, only include if other.
    if (resource.sslExpiryThreshold !== undefined && resource.sslExpiryThreshold !== 30) {
        builder.number('sslExpiryThreshold', resource.sslExpiryThreshold);
    }
}
exports.buildAlertChannelProps = buildAlertChannelProps;
const construct = 'AlertChannel';
function valueForAlertChannelFromId(genfile, physicalId) {
    genfile.namedImport(construct, 'checkly/constructs');
    return (0, sourcegen_1.expr)((0, sourcegen_1.ident)(construct), builder => {
        builder.member((0, sourcegen_1.ident)('fromId'));
        builder.call(builder => {
            builder.number(physicalId);
        });
    });
}
exports.valueForAlertChannelFromId = valueForAlertChannelFromId;
class AlertChannelCodegen extends codegen_1.Codegen {
    phoneCallCodegen;
    emailCodegen;
    opsgenieCodegen;
    pagerdutyCodegen;
    slackCodegen;
    smsCodegen;
    webhookCodegen;
    codegensByType;
    constructor(program) {
        super(program);
        this.phoneCallCodegen = new phone_call_alert_channel_codegen_1.PhoneCallAlertChannelCodegen(program);
        this.emailCodegen = new email_alert_channel_codegen_1.EmailAlertChannelCodegen(program);
        this.opsgenieCodegen = new opsgenie_alert_channel_codegen_1.OpsgenieAlertChannelCodegen(program);
        this.pagerdutyCodegen = new pagerduty_alert_channel_codegen_1.PagerdutyAlertChannelCodegen(program);
        this.slackCodegen = new slack_alert_channel_codegen_1.SlackAlertChannelCodegen(program);
        this.smsCodegen = new sms_alert_channel_codegen_1.SmsAlertChannelCodegen(program);
        this.webhookCodegen = new webhook_alert_channel_codegen_1.WebhookAlertChannelCodegen(program);
        this.codegensByType = {
            CALL: this.phoneCallCodegen,
            EMAIL: this.emailCodegen,
            OPSGENIE: this.opsgenieCodegen,
            PAGERDUTY: this.pagerdutyCodegen,
            SLACK: this.slackCodegen,
            SMS: this.smsCodegen,
            WEBHOOK: this.webhookCodegen,
        };
    }
    describe(resource) {
        const codegen = this.codegensByType[resource.type];
        if (codegen === undefined) {
            throw new Error(`Unable to describe unsupported alert channel type '${resource.type}'.`);
        }
        return codegen.describe(resource);
    }
    prepare(logicalId, resource, context) {
        const codegen = this.codegensByType[resource.type];
        if (codegen === undefined) {
            throw new Error(`Unable to generate code for unsupported alert channel type '${resource.type}'.`);
        }
        codegen.prepare(logicalId, resource, context);
    }
    gencode(logicalId, resource, context) {
        const codegen = this.codegensByType[resource.type];
        if (codegen === undefined) {
            throw new Error(`Unable to generate code for unsupported alert channel type '${resource.type}'.`);
        }
        codegen.gencode(logicalId, resource, context);
    }
}
exports.AlertChannelCodegen = AlertChannelCodegen;
//# sourceMappingURL=alert-channel-codegen.js.map