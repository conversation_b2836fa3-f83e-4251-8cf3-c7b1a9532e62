import { InstrumentationModuleFile } from './types';
export declare class InstrumentationNodeModuleFile implements InstrumentationModuleFile {
    supportedVersions: string[];
    patch: (moduleExports: any, moduleVersion?: string) => any;
    unpatch: (moduleExports?: any, moduleVersion?: string) => void;
    name: string;
    constructor(name: string, supportedVersions: string[], patch: (moduleExports: any, moduleVersion?: string) => any, unpatch: (moduleExports?: any, moduleVersion?: string) => void);
}
//# sourceMappingURL=instrumentationNodeModuleFile.d.ts.map