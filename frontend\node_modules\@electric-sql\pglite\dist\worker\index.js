import{a as j}from"../chunk-TGYMLQND.js";import{u as N,x as z}from"../chunk-7PRRATDV.js";import"../chunk-STOZMFXW.js";import{e as t,f as d,g as u,h as y,j as Q}from"../chunk-BTBUZ646.js";Q();var W,C,T,M,_,m,L,x,k,B,A,O,E,v,G,P,b,I,$,c,K,D,w,J,F=class F extends j{constructor(e,r){super();d(this,c);d(this,W);d(this,C,0);d(this,T,!1);d(this,M,!1);d(this,_,!1);d(this,m,new EventTarget);d(this,L);d(this,x,!1);d(this,k);d(this,B);d(this,A);d(this,O);d(this,E);d(this,v);d(this,G);d(this,P,new Map);d(this,b,new Set);d(this,I);d(this,$,[]);u(this,k,e),u(this,L,N()),u(this,I,r?.extensions??{}),u(this,A,new Promise(n=>{t(this,k).addEventListener("message",s=>{if(s.data.type==="here")n();else throw new Error("Invalid message")},{once:!0})})),u(this,O,new Promise(n=>{let s=p=>{p.data.type==="ready"&&(u(this,B,p.data.id),t(this,k).removeEventListener("message",s),n())};t(this,k).addEventListener("message",s)})),u(this,W,y(this,c,K).call(this,r))}static async create(e,r){let n=new F(e,r);return await t(n,W),n}get waitReady(){return new Promise(e=>{t(this,W).then(()=>{t(this,x)?e():e(new Promise(r=>{t(this,m).addEventListener("connected",()=>{r()})}))})})}get debug(){return t(this,C)}get ready(){return t(this,T)}get closed(){return t(this,M)}get isLeader(){return t(this,_)}async close(){var e;t(this,M)||(u(this,M,!0),t(this,E)?.close(),t(this,v)?.close(),(e=t(this,G))==null||e.call(this),t(this,k).terminate())}async[Symbol.asyncDispose](){await this.close()}async execProtocolRaw(e){return await y(this,c,w).call(this,"execProtocolRaw",e)}async execProtocol(e){return await y(this,c,w).call(this,"execProtocol",e)}async syncToFs(){await y(this,c,w).call(this,"syncToFs")}async listen(e,r){let n=z(e);return t(this,P).has(n)||t(this,P).set(n,new Set),t(this,P).get(n).add(r),await this.exec(`LISTEN ${e}`),async()=>{await this.unlisten(n,r)}}async unlisten(e,r){await this.waitReady,r?t(this,P).get(e)?.delete(r):t(this,P).delete(e),t(this,P).get(e)?.size===0&&await this.exec(`UNLISTEN ${e}`)}onNotification(e){return t(this,b).add(e),()=>{t(this,b).delete(e)}}offNotification(e){t(this,b).delete(e)}async dumpDataDir(){return await y(this,c,w).call(this,"dumpDataDir")}onLeaderChange(e){return t(this,m).addEventListener("leader-change",e),()=>{t(this,m).removeEventListener("leader-change",e)}}offLeaderChange(e){t(this,m).removeEventListener("leader-change",e)}async _handleBlob(e){await y(this,c,w).call(this,"_handleBlob",e)}async _getWrittenBlob(){return await y(this,c,w).call(this,"_getWrittenBlob")}async _cleanupBlob(){await y(this,c,w).call(this,"_cleanupBlob")}async _checkReady(){await this.waitReady}async _runExclusiveQuery(e){await y(this,c,w).call(this,"_acquireQueryLock");try{return await e()}finally{await y(this,c,w).call(this,"_releaseQueryLock")}}async _runExclusiveTransaction(e){await y(this,c,w).call(this,"_acquireTransactionLock");try{return await e()}finally{await y(this,c,w).call(this,"_releaseTransactionLock")}}};W=new WeakMap,C=new WeakMap,T=new WeakMap,M=new WeakMap,_=new WeakMap,m=new WeakMap,L=new WeakMap,x=new WeakMap,k=new WeakMap,B=new WeakMap,A=new WeakMap,O=new WeakMap,E=new WeakMap,v=new WeakMap,G=new WeakMap,P=new WeakMap,b=new WeakMap,I=new WeakMap,$=new WeakMap,c=new WeakSet,K=async function(e={}){for(let[o,h]of Object.entries(t(this,I))){if(h instanceof URL)throw new Error("URL extensions are not supported on the client side of a worker");{let l=await h.setup(this,{},!0);if(l.emscriptenOpts&&console.warn(`PGlite extension ${o} returned emscriptenOpts, these are not supported on the client side of a worker`),l.namespaceObj){let R=this;R[o]=l.namespaceObj}l.bundlePath&&console.warn(`PGlite extension ${o} returned bundlePath, this is not supported on the client side of a worker`),l.init&&await l.init(),l.close&&t(this,$).push(l.close)}}await t(this,A);let{extensions:r,...n}=e;t(this,k).postMessage({type:"init",options:n}),await t(this,O);let s=`pglite-tab-close:${t(this,L)}`;u(this,G,await q(s));let p=`pglite-broadcast:${t(this,B)}`;u(this,E,new BroadcastChannel(p));let a=`pglite-tab:${t(this,L)}`;u(this,v,new BroadcastChannel(a)),t(this,E).addEventListener("message",async o=>{o.data.type==="leader-here"?(u(this,x,!1),t(this,m).dispatchEvent(new Event("leader-change")),y(this,c,D).call(this)):o.data.type==="notify"&&y(this,c,J).call(this,o.data.channel,o.data.payload)}),t(this,v).addEventListener("message",async o=>{o.data.type==="connected"&&(u(this,x,!0),t(this,m).dispatchEvent(new Event("connected")),u(this,C,await y(this,c,w).call(this,"getDebugLevel")),u(this,T,!0))}),t(this,k).addEventListener("message",async o=>{o.data.type==="leader-now"&&(u(this,_,!0),t(this,m).dispatchEvent(new Event("leader-change")))}),y(this,c,D).call(this),this._initArrayTypes()},D=async function(){t(this,x)||(t(this,E).postMessage({type:"tab-here",id:t(this,L)}),setTimeout(()=>y(this,c,D).call(this),16))},w=async function(e,...r){let n=N(),s={type:"rpc-call",callId:n,method:e,args:r};return t(this,v).postMessage(s),await new Promise((p,a)=>{let o=R=>{if(R.data.callId!==n)return;l();let f=R.data;if(f.type==="rpc-return")p(f.result);else if(f.type==="rpc-error"){let S=new Error(f.error.message);Object.assign(S,f.error),a(S)}else a(new Error("Invalid message"))},h=()=>{l(),a(new U)},l=()=>{t(this,v).removeEventListener("message",o),t(this,m).removeEventListener("leader-change",h)};t(this,m).addEventListener("leader-change",h),t(this,v).addEventListener("message",o)})},J=function(e,r){let n=t(this,P).get(e);if(n)for(let s of n)queueMicrotask(()=>s(r));for(let s of t(this,b))queueMicrotask(()=>s(e,r))};var H=F;async function se({init:g}){postMessage({type:"here"});let i=await new Promise(h=>{addEventListener("message",l=>{l.data.type==="init"&&h(l.data.options)},{once:!0})}),e=i.id??`${import.meta.url}:${i.dataDir??""}`;postMessage({type:"ready",id:e});let r=`pglite-election-lock:${e}`,n=`pglite-broadcast:${e}`,s=new BroadcastChannel(n),p=new Set;await q(r);let a=g(i);s.onmessage=async h=>{let l=h.data;switch(l.type){case"tab-here":V(l.id,await a,p);break}},s.postMessage({type:"leader-here",id:e}),postMessage({type:"leader-now"}),(await a).onNotification((h,l)=>{s.postMessage({type:"notify",channel:h,payload:l})})}function V(g,i,e){if(e.has(g))return;e.add(g);let r=`pglite-tab:${g}`,n=`pglite-tab-close:${g}`,s=new BroadcastChannel(r);navigator.locks.request(n,()=>new Promise(a=>{s.close(),e.delete(g),a()}));let p=X(g,i);s.addEventListener("message",async a=>{let o=a.data;switch(o.type){case"rpc-call":{await i.waitReady;let{callId:h,method:l,args:R}=o;try{let f=await p[l](...R);s.postMessage({type:"rpc-return",callId:h,result:f})}catch(f){console.error(f),s.postMessage({type:"rpc-error",callId:h,error:{message:f.message}})}break}}}),s.postMessage({type:"connected"})}function X(g,i){let e=null,r=null,n=`pglite-tab-close:${g}`;return q(n).then(()=>{r&&i.exec("ROLLBACK"),e?.(),r?.()}),{async getDebugLevel(){return i.debug},async close(){await i.close()},async execProtocol(s){let{messages:p,data:a}=await i.execProtocol(s);if(a.byteLength!==a.buffer.byteLength){let o=new ArrayBuffer(a.byteLength),h=new Uint8Array(o);return h.set(a),{messages:p,data:h}}else return{messages:p,data:a}},async execProtocolRaw(s,p={}){let a=await i.execProtocolRaw(s,p);if(a.byteLength!==a.buffer.byteLength){let o=new ArrayBuffer(a.byteLength),h=new Uint8Array(o);return h.set(a),h}else return a},async dumpDataDir(){return await i.dumpDataDir()},async syncToFs(){return await i.syncToFs()},async _handleBlob(s){return await i._handleBlob(s)},async _getWrittenBlob(){return await i._getWrittenBlob()},async _cleanupBlob(){return await i._cleanupBlob()},async _checkReady(){return await i._checkReady()},async _acquireQueryLock(){return new Promise(s=>{i._runExclusiveQuery(()=>new Promise(p=>{e=p,s()}))})},async _releaseQueryLock(){e?.(),e=null},async _acquireTransactionLock(){return new Promise(s=>{i._runExclusiveTransaction(()=>new Promise(p=>{r=p,s()}))})},async _releaseTransactionLock(){r?.(),r=null}}}var U=class extends Error{constructor(){super("Leader changed, pending operation in indeterminate state")}};async function q(g){let i;return await new Promise(e=>{navigator.locks.request(g,()=>new Promise(r=>{i=r,e()}))}),i}export{U as LeaderChangedError,H as PGliteWorker,se as worker};
//# sourceMappingURL=index.js.map