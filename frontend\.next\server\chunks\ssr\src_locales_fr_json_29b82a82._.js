module.exports = {

"[project]/src/locales/fr.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"RootLayout\":{\"home_link\":\"Accueil\",\"about_link\":\"A propos\",\"counter_link\":\"Compteur\",\"portfolio_link\":\"Portfolio\",\"sign_in_link\":\"Se connecter\",\"sign_up_link\":\"S'inscrire\"},\"BaseTemplate\":{\"description\":\"Code de démarrage pour Next.js avec Tailwind CSS\",\"made_with\":\"Fait avec <author></author>.\"},\"Index\":{\"meta_title\":\"Présentation de Next.js Boilerplate\",\"meta_description\":\"Next js Boilerplate est le code de démarrage parfait pour votre projet. Construisez votre application React avec le framework Next.js.\",\"sponsors_title\":\"Partenaires\"},\"Counter\":{\"meta_title\":\"Compteur\",\"meta_description\":\"Un exemple d'opération DB\",\"loading_counter\":\"Chargement du compteur...\",\"security_powered_by\":\"Sécurité, détection de bot et rate limiting propulsés par\"},\"CounterForm\":{\"presentation\":\"Le compteur est stocké dans la base de données et incrémenté par la valeur que vous fournissez.\",\"label_increment\":\"Incrémenter de\",\"button_increment\":\"Incrémenter\"},\"CurrentCount\":{\"count\":\"Nombre : {count}\"},\"About\":{\"meta_title\":\"A propos\",\"meta_description\":\"A propos description\",\"about_paragraph\":\"Bienvenue sur notre page À propos ! Nous sommes une équipe de passionnés et dévoués à la création de logiciels.\",\"translation_powered_by\":\"Traduction propulsée par\"},\"Portfolio\":{\"meta_title\":\"Portfolio\",\"meta_description\":\"Bienvenue sur la page de mon portfolio !\",\"presentation\":\"Bienvenue sur ma page portfolio ! Vous trouverez ici une collection soigneusement organisée de mon travail et de mes réalisations. À travers ce portfolio, je mets en valeur mon expertise, ma créativité et la valeur que je peux apporter à vos projets.\",\"portfolio_name\":\"Portfolio {name}\",\"error_reporting_powered_by\":\"Rapport d'erreur propulsé par\",\"coverage_powered_by\":\"Couverture de code propulsée par\"},\"PortfolioSlug\":{\"meta_title\":\"Portfolio {slug}\",\"meta_description\":\"Description du Portfolio {slug}\",\"header\":\"Portfolio {slug}\",\"content\":\"Créé un ensemble de matériel promotionnel et d'éléments de marquage pour un événement d'entreprise. Conçu un thème visuellement unifié, englobant un logo, des affiches, des bannières et des actifs numériques. Intégrer l'identité de marque du client tout en l'insufflant à une approche contemporaine et innovante.\",\"code_review_powered_by\":\"Code review propulsé par\"},\"SignIn\":{\"meta_title\":\"Se connecter\",\"meta_description\":\"Connectez-vous à votre compte avec facilité.\"},\"SignUp\":{\"meta_title\":\"S'inscrire\",\"meta_description\":\"Créez un compte facilement grâce à notre processus d'inscription intuitif.\"},\"Dashboard\":{\"meta_title\":\"Tableau de bord\",\"hello_message\":\"Bonjour {email}!\",\"alternative_message\":\"Vous voulez créer votre SaaS plus rapidement en utilisant la même stack ? Essayez <url></url>.\"},\"UserProfile\":{\"meta_title\":\"Profil de l'utilisateur\"},\"DashboardLayout\":{\"dashboard_link\":\"Tableau de bord\",\"user_profile_link\":\"Gérer votre compte\",\"sign_out\":\"Se déconnecter\"}}"));}}),

};