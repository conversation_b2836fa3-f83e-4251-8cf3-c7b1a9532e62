import OriginalNextLegacyImage from 'next/legacy/image';
import { defaultLoader } from 'sb-original/default-loader';
import { ImageContext } from 'sb-original/image-context';
import React from 'next/dist/compiled/react';

// src/plugins/next-image/alias/next-legacy-image.tsx
function NextLegacyImage({ loader, ...props }) {
  const imageParameters = React.useContext(ImageContext);
  return /* @__PURE__ */ React.createElement(
    OriginalNextLegacyImage,
    {
      ...imageParameters,
      ...props,
      loader: loader ?? defaultLoader
    }
  );
}
var next_legacy_image_default = NextLegacyImage;

export { next_legacy_image_default as default };
