"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.JsonSourceFile = void 0;
class JsonSourceFile {
    static #id = 0;
    id = ++JsonSourceFile.#id;
    sourceFile;
    data;
    constructor(sourceFile, data) {
        this.sourceFile = sourceFile;
        this.data = data;
    }
    get meta() {
        return this.sourceFile.meta;
    }
    static loadFromSourceFile(sourceFile) {
        try {
            const data = JSON.parse(sourceFile.contents);
            return new JsonSourceFile(sourceFile, data);
        }
        catch {
            // Ignore.
        }
    }
}
exports.JsonSourceFile = JsonSourceFile;
//# sourceMappingURL=json-source-file.js.map