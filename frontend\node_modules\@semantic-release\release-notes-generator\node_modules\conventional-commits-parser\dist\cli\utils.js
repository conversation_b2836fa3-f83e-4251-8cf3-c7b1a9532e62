import fs from 'fs';
import readline from 'readline';
export async function* splitStream(stream, separator) {
    let chunk;
    let payload;
    let buffer = '';
    for await (chunk of stream) {
        buffer += chunk.toString();
        if (buffer.includes(separator)) {
            payload = buffer.split(separator);
            buffer = payload.pop() || '';
            yield* payload;
        }
    }
    if (buffer) {
        yield buffer;
    }
}
export async function* readRawCommitsFromFiles(files, separator) {
    for (const file of files) {
        try {
            yield* splitStream(fs.createReadStream(file), separator);
        }
        catch (err) {
            console.warn(`Failed to read file ${file}:\n  ${err}`);
        }
    }
}
export async function* readRawCommitsFromLine(separator) {
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout,
        terminal: true
    });
    let line = '';
    let commit = '';
    for await (line of rl) {
        commit += `${line}\n`;
        if (!commit.includes(separator)) {
            return;
        }
        yield commit;
        commit = '';
    }
}
export function readRawCommitsFromStdin(separator) {
    return splitStream(process.stdin, separator);
}
const JSON_STREAM_OPEN = '[\n';
const JSON_STREAM_SEPARATOR = '\n,\n';
const JSON_STREAM_CLOSE = '\n]\n';
export async function* stringify(commits) {
    let jsonStreamOpened = false;
    yield JSON_STREAM_OPEN;
    for await (const commit of commits) {
        if (jsonStreamOpened) {
            yield JSON_STREAM_SEPARATOR;
        }
        yield JSON.stringify(commit);
        jsonStreamOpened = true;
    }
    yield JSON_STREAM_CLOSE;
}
//# sourceMappingURL=data:application/json;base64,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