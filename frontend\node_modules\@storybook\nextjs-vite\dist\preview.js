"use strict";var __create=Object.create;var __defProp=Object.defineProperty;var __getOwnPropDesc=Object.getOwnPropertyDescriptor;var __getOwnPropNames=Object.getOwnPropertyNames;var __getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty;var __export=(target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0})},__copyProps=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key of __getOwnPropNames(from))!__hasOwnProp.call(to,key)&&key!==except&&__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to};var __toESM=(mod,isNodeMode,target)=>(target=mod!=null?__create(__getProtoOf(mod)):{},__copyProps(isNodeMode||!mod||!mod.__esModule?__defProp(target,"default",{value:mod,enumerable:!0}):target,mod)),__toCommonJS=mod=>__copyProps(__defProp({},"__esModule",{value:!0}),mod);var preview_exports={};__export(preview_exports,{decorators:()=>decorators,loaders:()=>loaders,parameters:()=>parameters});module.exports=__toCommonJS(preview_exports);var import_navigation2=require("@storybook/nextjs-vite/navigation.mock"),import_router2=require("@storybook/nextjs-vite/router.mock"),import_is_next_router_error=require("next/dist/client/components/is-next-router-error");var import_config=require("next/config");(0,import_config.setConfig)(process.env.__NEXT_RUNTIME_CONFIG);var React2=__toESM(require("react"));var import_react=__toESM(require("react")),import_head_manager=__toESM(require("next/dist/client/head-manager")),import_head_manager_context=require("next/dist/shared/lib/head-manager-context.shared-runtime"),HeadManagerProvider=({children})=>{let headManager=(0,import_react.useMemo)(import_head_manager.default,[]);return headManager.getIsSsr=()=>!1,import_react.default.createElement(import_head_manager_context.HeadManagerContext.Provider,{value:headManager},children)},head_manager_provider_default=HeadManagerProvider;var HeadManagerDecorator=Story=>React2.createElement(head_manager_provider_default,null,React2.createElement(Story,null));var React3=__toESM(require("react")),import_image_context=require("sb-original/image-context"),ImageDecorator=(Story,{parameters:parameters2})=>parameters2.nextjs?.image?React3.createElement(import_image_context.ImageContext.Provider,{value:parameters2.nextjs.image},React3.createElement(Story,null)):React3.createElement(Story,null);var React6=__toESM(require("react")),import_redirect_boundary=require("next/dist/client/components/redirect-boundary");var import_react2=__toESM(require("react")),import_navigation=require("@storybook/nextjs-vite/navigation.mock"),import_app_router_context=require("next/dist/shared/lib/app-router-context.shared-runtime"),import_hooks_client_context=require("next/dist/shared/lib/hooks-client-context.shared-runtime"),import_segment=require("next/dist/shared/lib/segment");function getSelectedParams(currentTree,params={}){let parallelRoutes=currentTree[1];for(let parallelRoute of Object.values(parallelRoutes)){let segment=parallelRoute[0],isDynamicParameter=Array.isArray(segment),segmentValue=isDynamicParameter?segment[1]:segment;if(!segmentValue||segmentValue.startsWith(import_segment.PAGE_SEGMENT_KEY))continue;isDynamicParameter&&(segment[2]==="c"||segment[2]==="oc")?params[segment[0]]=segment[1].split("/"):isDynamicParameter&&(params[segment[0]]=segment[1]),params=getSelectedParams(parallelRoute,params)}return params}var getParallelRoutes=segmentsList=>{let segment=segmentsList.shift();return segment?[segment,{children:getParallelRoutes(segmentsList)}]:[]},AppRouterProvider=({children,routeParams})=>{let{pathname,query,segments=[]}=routeParams,tree=[pathname,{children:getParallelRoutes([...segments])}],pathParams=(0,import_react2.useMemo)(()=>getSelectedParams(tree),[tree]),newLazyCacheNode={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null};return import_react2.default.createElement(import_hooks_client_context.PathParamsContext.Provider,{value:pathParams},import_react2.default.createElement(import_hooks_client_context.PathnameContext.Provider,{value:pathname},import_react2.default.createElement(import_hooks_client_context.SearchParamsContext.Provider,{value:new URLSearchParams(query)},import_react2.default.createElement(import_app_router_context.GlobalLayoutRouterContext.Provider,{value:{changeByServerResponse(){},buildId:"storybook",tree,focusAndScrollRef:{apply:!1,hashFragment:null,segmentPaths:[tree],onlyHashChange:!1},nextUrl:pathname}},import_react2.default.createElement(import_app_router_context.AppRouterContext.Provider,{value:(0,import_navigation.getRouter)()},import_react2.default.createElement(import_app_router_context.LayoutRouterContext.Provider,{value:{childNodes:new Map,tree,parentTree:tree,parentCacheNode:newLazyCacheNode,url:pathname,loading:null}},children))))))};var import_react3=__toESM(require("react")),import_router=require("@storybook/nextjs-vite/router.mock"),import_router_context=require("next/dist/shared/lib/router-context.shared-runtime"),PageRouterProvider=({children})=>import_react3.default.createElement(import_router_context.RouterContext.Provider,{value:(0,import_router.getRouter)()},children);var defaultRouterParams={pathname:"/",query:{}},RouterDecorator=(Story,{parameters:parameters2})=>parameters2.nextjs?.appDirectory??!1?AppRouterProvider?React6.createElement(AppRouterProvider,{routeParams:{...defaultRouterParams,...parameters2.nextjs?.navigation}},React6.createElement(import_redirect_boundary.RedirectBoundary,null,React6.createElement(Story,null))):null:React6.createElement(PageRouterProvider,null,React6.createElement(Story,null));var React7=__toESM(require("react")),import_styled_jsx=require("styled-jsx"),StyledJsxDecorator=Story=>React7.createElement(import_styled_jsx.StyleRegistry,null,React7.createElement(Story,null));function addNextHeadCount(){let meta=document.createElement("meta");meta.name="next-head-count",meta.content="0",document.head.appendChild(meta)}function isAsyncClientComponentError(error){return typeof error=="string"&&(error.includes("Only Server Components can be async at the moment.")||error.includes("A component was suspended by an uncached promise.")||error.includes("async/await is not yet supported in Client Components"))}addNextHeadCount();var origConsoleError=globalThis.console.error;globalThis.console.error=(...args)=>{let error=args[0];(0,import_is_next_router_error.isNextRouterError)(error)||isAsyncClientComponentError(error)||origConsoleError.apply(globalThis.console,args)};globalThis.addEventListener("error",ev=>{if((0,import_is_next_router_error.isNextRouterError)(ev.error)||isAsyncClientComponentError(ev.error)){ev.preventDefault();return}});var asDecorator=decorator=>decorator,decorators=[asDecorator(StyledJsxDecorator),asDecorator(ImageDecorator),asDecorator(RouterDecorator),asDecorator(HeadManagerDecorator)],loaders=async({globals,parameters:parameters2})=>{let{router,appDirectory}=parameters2.nextjs??{};appDirectory?(0,import_navigation2.createNavigation)(router):(0,import_router2.createRouter)({locale:globals.locale,...router})},parameters={docs:{source:{excludeDecorators:!0}},react:{rootOptions:{onCaughtError(error){(0,import_is_next_router_error.isNextRouterError)(error)||console.error(error)}}}};0&&(module.exports={decorators,loaders,parameters});
