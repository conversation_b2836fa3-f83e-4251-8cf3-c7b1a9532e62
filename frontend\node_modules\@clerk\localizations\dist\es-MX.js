"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/es-MX.ts
var es_MX_exports = {};
__export(es_MX_exports, {
  esMX: () => esMX
});
module.exports = __toCommonJS(es_MX_exports);
var esMX = {
  locale: "es-MX",
  backButton: "Atr\xE1s",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "Por defecto",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "Otro dispositivo de imitaci\xF3n",
  badge__primary: "Primario",
  badge__renewsAt: void 0,
  badge__requiresAction: "Requiere acci\xF3n",
  badge__startsAt: void 0,
  badge__thisDevice: "Este dispositivo",
  badge__unverified: "No confirmado",
  badge__upcomingPlan: void 0,
  badge__userDevice: "Dispositivo de usuario",
  badge__you: "Usted",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "Crear organizaci\xF3n",
    invitePage: {
      formButtonReset: "Saltar"
    },
    title: "Crear organizaci\xF3n"
  },
  dates: {
    lastDay: "Ayer a las {{ date | timeString('es-ES') }}",
    next6Days: "{{ date | weekday('es-ES','long') }} a las {{ date | timeString('es-ES') }}",
    nextDay: "Ma\xF1ana a las {{ date | timeString('es-ES') }}",
    numeric: "{{ date | numeric('es-ES') }}",
    previous6Days: "\xDAltimo {{ date | weekday('es-ES','long') }} en {{ date | timeString('es-ES') }}",
    sameDay: "Hoy a las {{ date | timeString('es-ES') }}"
  },
  dividerText: "o",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "Usar otro m\xE9todo",
  footerPageLink__help: "Ayuda",
  footerPageLink__privacy: "Privacidad",
  footerPageLink__terms: "T\xE9rminos",
  formButtonPrimary: "Continuar",
  formButtonPrimary__verify: "Verificar",
  formFieldAction__forgotPassword: "Has olvidado tu contrase\xF1a?",
  formFieldError__matchingPasswords: "Las contrase\xF1as coinciden.",
  formFieldError__notMatchingPasswords: "Las no contrase\xF1as coinciden.",
  formFieldError__verificationLinkExpired: "El link de verificaci\xF3n expiro. Porfavor vuelva a solicitarlo.",
  formFieldHintText__optional: "Opcional",
  formFieldHintText__slug: "Un slug es una identificaci\xF3n legible por humanos que debe ser \xFAnica. Se utiliza a menudo en URL.",
  formFieldInputPlaceholder__backupCode: void 0,
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Eliminar cuenta",
  formFieldInputPlaceholder__emailAddress: "Ingresa tu correo electr\xF3nico",
  formFieldInputPlaceholder__emailAddress_username: void 0,
  formFieldInputPlaceholder__emailAddresses: "Ingrese o pegue una o m\xE1s direcciones de correo electr\xF3nico, separadas por espacios o comas",
  formFieldInputPlaceholder__firstName: void 0,
  formFieldInputPlaceholder__lastName: void 0,
  formFieldInputPlaceholder__organizationDomain: void 0,
  formFieldInputPlaceholder__organizationDomainEmailAddress: void 0,
  formFieldInputPlaceholder__organizationName: void 0,
  formFieldInputPlaceholder__organizationSlug: void 0,
  formFieldInputPlaceholder__password: "Ingresa tu contrase\xF1a",
  formFieldInputPlaceholder__phoneNumber: void 0,
  formFieldInputPlaceholder__username: void 0,
  formFieldLabel__automaticInvitations: "Activar invitaciones automaticas para este dominio",
  formFieldLabel__backupCode: "C\xF3digo de respaldo",
  formFieldLabel__confirmDeletion: "Confirmarci\xF3n",
  formFieldLabel__confirmPassword: "Confirme la contrase\xF1a",
  formFieldLabel__currentPassword: "Contrase\xF1a actual",
  formFieldLabel__emailAddress: "Correo electr\xF3nico",
  formFieldLabel__emailAddress_username: "Correo electr\xF3nico o nombre de usuario",
  formFieldLabel__emailAddresses: "Direcciones de correo",
  formFieldLabel__firstName: "Nombre",
  formFieldLabel__lastName: "Apellido",
  formFieldLabel__newPassword: "Nueva contrase\xF1a",
  formFieldLabel__organizationDomain: "Domain",
  formFieldLabel__organizationDomainDeletePending: "Eliminar invitaciones y sugerencias pendientes",
  formFieldLabel__organizationDomainEmailAddress: "Verificaci\xF3n de correo",
  formFieldLabel__organizationDomainEmailAddressDescription: "Entrar una direcci\xF3n de correo electr\xF3nico bajo este dominio para recibir un c\xF3digo y verificarlo.",
  formFieldLabel__organizationName: "Nombre de la Organizaci\xF3n",
  formFieldLabel__organizationSlug: "Slug",
  formFieldLabel__passkeyName: "Nombre de llave de acceso",
  formFieldLabel__password: "Contrase\xF1a",
  formFieldLabel__phoneNumber: "N\xFAmero telef\xF3nico",
  formFieldLabel__role: "Rol",
  formFieldLabel__signOutOfOtherSessions: "Cerrar sesi\xF3n en todos los dem\xE1s dispositivos",
  formFieldLabel__username: "Nombre de usuario",
  impersonationFab: {
    action__signOut: "Cerrar",
    title: "Registrado como {{identifier}}"
  },
  maintenanceMode: "Actualmente estamos en mantenimiento, pero no te preocupes, no deber\xEDa llevar m\xE1s de unos minutos.",
  membershipRole__admin: "Administrador",
  membershipRole__basicMember: "Miembro",
  membershipRole__guestMember: "Invitado",
  organizationList: {
    action__createOrganization: "Crear organizaci\xF3n",
    action__invitationAccept: "Unirse",
    action__suggestionsAccept: "Pedir unirse",
    createOrganization: "Crear Organizaci\xF3n",
    invitationAcceptedLabel: "Unido",
    subtitle: "Para continuar con {{applicationName}}",
    suggestionsAcceptedLabel: "Aprobaci\xF3n pendiente",
    title: "Elige una cuenta",
    titleWithoutPersonal: "Elige una organizaci\xF3n"
  },
  organizationProfile: {
    badge__automaticInvitation: "Invitaciones automaticas",
    badge__automaticSuggestion: "Sugerencias automaticas",
    badge__manualInvitation: "Sin inscripci\xF3nes automaticas",
    badge__unverified: "No verificado",
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "A\xF1ada el email para verificar. Los usuarios con direcciones de correo electr\xF3nico en este dominio pueden unirse a la organizaci\xF3n aqu\xED o pedir unirse.",
      title: "Anadir dominio"
    },
    invitePage: {
      detailsTitle__inviteFailed: "No se pudieron enviar las invitaciones. Solucione lo siguiente y vuelva a intentarlo:",
      formButtonPrimary__continue: "Enviar invitaciones",
      selectDropdown__role: "Select role",
      subtitle: "Invitar nuevos miembros a esta organizaci\xF3n",
      successMessage: "Invitaciones enviadas con \xE9xito",
      title: "Invitar miembros"
    },
    membersPage: {
      action__invite: "Invitar",
      action__search: "Buscar",
      activeMembersTab: {
        menuAction__remove: "Eliminar miembro",
        tableHeader__actions: void 0,
        tableHeader__joined: "Se uni\xF3",
        tableHeader__role: "Rol",
        tableHeader__user: "Usuario"
      },
      detailsTitle__emptyRow: "No hay miembros para mostrar",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Invita usuarios conectando un dominio de correo electr\xF3nico con su organizaci\xF3n. Cualquiera que se registre con un dominio de correo electr\xF3nico coincidente podr\xE1 unirse a la organizaci\xF3n en cualquier momento.",
          headerTitle: "Invitaciones automaticas",
          primaryButton: "Gestionar dominios verificados"
        },
        table__emptyRow: "No hay invitaciones para mostrar"
      },
      invitedMembersTab: {
        menuAction__revoke: "Revocar invitaci\xF3n",
        tableHeader__invited: "Invitado"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Los usuarios que inicien sesi\xF3n con un dominio de correo electr\xF3nico coincidente podr\xE1n ver una sugerencia para solicitar unirse a su organizaci\xF3n.",
          headerTitle: "Sugerencias automaticas",
          primaryButton: "Gestionar dominios verificados"
        },
        menuAction__approve: "Aprobado",
        menuAction__reject: "Rechazado",
        tableHeader__requested: "Acceso solicitado",
        table__emptyRow: "No hay solicitudes para mostrar"
      },
      start: {
        headerTitle__invitations: "Invitaciones",
        headerTitle__members: "Miembros",
        headerTitle__requests: "Solicitudes"
      }
    },
    navbar: {
      billing: void 0,
      description: "Gestiona tu organizaci\xF3n.",
      general: "General",
      members: "Miembros",
      title: "Organizaci\xF3n"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: 'Escribe "{{organizationName}}" a continuaci\xF3n para continuar.',
          messageLine1: "\xBFEstas seguro que quieres eliminar esta organizaci\xF3n?",
          messageLine2: "Esta acci\xF3n es permanente e irreversible.",
          successMessage: "Haz eliminado la organizaci\xF3n.",
          title: "Eliminar la organizaci\xF3n"
        },
        leaveOrganization: {
          actionDescription: 'Escribe "{{organizationName}}" a continuaci\xF3n para continuar.',
          messageLine1: "\xBFEst\xE1 seguro de que desea abandonar esta organizaci\xF3n? Perder\xE1 el acceso a esta organizaci\xF3n y sus aplicaciones.",
          messageLine2: "Esta acci\xF3n es permanente e irreversible.",
          successMessage: "Has dejado la organizaci\xF3n.",
          title: "Abandonar la organizaci\xF3n"
        },
        title: "Peligro"
      },
      domainSection: {
        menuAction__manage: "Gestionar",
        menuAction__remove: "Eliminar",
        menuAction__verify: "Verificar",
        primaryButton: "A\xF1adir dominio",
        subtitle: "Permite a los usuarios conectarse automaticamente o solicitar unirse a la organizaci\xF3n basado en un dominio de correo electr\xF3nico verificado.",
        title: "Verified domains"
      },
      successMessage: "La organizaci\xF3n ha sido actualizada.",
      title: "Perfil de la organizaci\xF3n"
    },
    removeDomainPage: {
      messageLine1: "Se eliminar\xE1 el dominio de correo electr\xF3nico {{domain}}.",
      messageLine2: "Los usuarios no podr\xE1n unirse a la organizaci\xF3n de manera autom\xE1tica una vez que se haya eliminado.",
      successMessage: "{{domain}} se ha eliminado.",
      title: "Eliminar dominio"
    },
    start: {
      headerTitle__general: "General",
      headerTitle__members: "Miembros",
      profileSection: {
        primaryButton: "Actualizar perfil",
        title: "Perfil de la organizaci\xF3n",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "Eliminar este dominio afectar\xE1 a los usuarios invitados.",
        removeDomainActionLabel__remove: "Eliminar dominio",
        removeDomainSubtitle: "Eliminar este dominio de los dominios verificados",
        removeDomainTitle: "Eliminar dominio"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Los usuarios se unen autom\xE1ticamente a la organizaci\xF3n cuando se registran y pueden unirse en cualquier momento.",
        automaticInvitationOption__label: "Invitaciones autom\xE1ticas",
        automaticSuggestionOption__description: "Los usuarios reciben una sugerencia para solicitar unirse, pero deben ser aprobados por un administrador antes de poder unirse a la organizaci\xF3n.",
        automaticSuggestionOption__label: "Sugerencias autom\xE1ticas",
        calloutInfoLabel: "Cambiar el modo de inscripci\xF3n solo afectar\xE1 a los nuevos usuarios.",
        calloutInvitationCountLabel: "Invitaciones pendientes enviadas a usuarios: {{count}}",
        calloutSuggestionCountLabel: "Sugerencias pendientes enviadas a usuarios: {{count}}",
        manualInvitationOption__description: "Los usuarios solo pueden ser invitados manualmente a la organizaci\xF3n.",
        manualInvitationOption__label: "Sin inscripci\xF3n autom\xE1tica",
        subtitle: "Seleccione c\xF3mo los usuarios de este dominio pueden unirse a la organizaci\xF3n."
      },
      start: {
        headerTitle__danger: "Peligro",
        headerTitle__enrollment: "Opciones de inscripci\xF3n"
      },
      subtitle: "El dominio {{domain}} ahora est\xE1 verificado. Contin\xFAa seleccionando el modo de inscripci\xF3n.",
      title: "Actualizar {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Introduce el c\xF3digo de verificaci\xF3n enviado a su direcci\xF3n de correo electr\xF3nico",
      formTitle: "C\xF3digo de verificaci\xF3n",
      resendButton: "No recibiste un c\xF3digo? Reenviar",
      subtitle: "El dominio {{domainName}} necesita ser verificado v\xEDa correo electr\xF3nico.",
      subtitleVerificationCodeScreen: "Se envi\xF3 un c\xF3digo de verificaci\xF3n a {{emailAddress}}. Introduzca el c\xF3digo para continuar.",
      title: "Verificar dominio"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "Crear Organizaci\xF3n",
    action__invitationAccept: "Unirse",
    action__manageOrganization: "Administrar Organizaci\xF3n",
    action__suggestionsAccept: "Solicitar unirse",
    notSelected: "Ninguna organizaci\xF3n seleccionada",
    personalWorkspace: "Espacio personal",
    suggestionsAcceptedLabel: "Pendiente de aprobaci\xF3n"
  },
  paginationButton__next: "Siguiente",
  paginationButton__previous: "Anterior",
  paginationRowText__displaying: "Mostrando",
  paginationRowText__of: "de",
  reverification: {
    alternativeMethods: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__backupCode: void 0,
      blockButton__emailCode: void 0,
      blockButton__passkey: void 0,
      blockButton__password: void 0,
      blockButton__phoneCode: void 0,
      blockButton__totp: void 0,
      getHelp: {
        blockButton__emailSupport: void 0,
        content: void 0,
        title: void 0
      },
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: void 0,
      title: void 0
    },
    emailCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    noAvailableMethods: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCodeMfa: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    totpMfa: {
      formTitle: void 0,
      subtitle: void 0,
      title: void 0
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Agregar cuenta",
      action__signOutAll: "Cerrar sesi\xF3n de todas las cuentas",
      subtitle: "Seleccione la cuenta con la que desea continuar.",
      title: "Elija una cuenta"
    },
    alternativeMethods: {
      actionLink: "Obtener ayuda",
      actionText: "\xBFNo tienes ninguno de estos?",
      blockButton__backupCode: "Usa un c\xF3digo de respaldo",
      blockButton__emailCode: "Enviar c\xF3digo a{{identifier}}",
      blockButton__emailLink: "Enviar enlace a{{identifier}}",
      blockButton__passkey: "Inicia sesi\xF3n con tu llave de acceso",
      blockButton__password: "Inicia sesi\xF3n con tu contrase\xF1a",
      blockButton__phoneCode: "Enviar c\xF3digo a{{identifier}}",
      blockButton__totp: "Usa tu aplicaci\xF3n de autenticaci\xF3n",
      getHelp: {
        blockButton__emailSupport: "Soporte de correo electr\xF3nico",
        content: "Si tiene problemas para ingresar a su cuenta, env\xEDenos un correo electr\xF3nico y trabajaremos con usted para restablecer el acceso lo antes posible.",
        title: "Obtener ayuda"
      },
      subtitle: "Si est\xE1 experimentando problemas, puede utilizar uno de estos m\xE9todos para ingresar.",
      title: "Utiliza otro m\xE9todo"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "para continuar a {{applicationName}}",
      title: "Introduce un c\xF3digo de seguridad"
    },
    emailCode: {
      formTitle: "C\xF3digo de verificaci\xF3n",
      resendButton: "Re-enviar c\xF3digo",
      subtitle: "para continuar a {{applicationName}}",
      title: "Revise su correo electr\xF3nico"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      expired: {
        subtitle: "Regresa a la pesta\xF1a original para continuar.",
        title: "Este enlace de verificaci\xF3n ha expirado"
      },
      failed: {
        subtitle: "Regresa a la pesta\xF1a original para continuar.",
        title: "Este enlace de verificaci\xF3n es invalido"
      },
      formSubtitle: "Utilice el enlace de verificaci\xF3n enviado a su correo electr\xF3nico",
      formTitle: "Enlace de verificaci\xF3n",
      loading: {
        subtitle: "Ser\xE1s redirigido pronto",
        title: "Iniciando sesi\xF3n..."
      },
      resendButton: "Reenviar enlace",
      subtitle: "para continuar a {{applicationName}}",
      title: "Revise su correo electr\xF3nico",
      unusedTab: {
        title: "Puede cerrar esta pesta\xF1a"
      },
      verified: {
        subtitle: "Ser\xE1s redirigido pronto",
        title: "Inici\xF3 sesi\xF3n con \xE9xito"
      },
      verifiedSwitchTab: {
        subtitle: "Regrese a la pesta\xF1a original para continuar",
        subtitleNewTab: "Regrese a la pesta\xF1a reci\xE9n abierta para continuar",
        titleNewTab: "Inici\xF3 sesi\xF3n en otra pesta\xF1a"
      }
    },
    forgotPassword: {
      formTitle: "C\xF3digo para restablecer la contrase\xF1a",
      resendButton: "No recibiste un c\xF3digo? Reenviar",
      subtitle: "para restablecer tu contrase\xF1a",
      subtitle_email: "Primero, introduce el c\xF3digo enviado a tu {{email}}",
      subtitle_phone: "Primero, introduce el c\xF3digo enviado a tu {{phone}}",
      title: "Restablecer contrase\xF1a"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "Restablecer tu contrase\xF1a",
      label__alternativeMethods: "O, inicia sesi\xF3n con otro m\xE9todo",
      title: "\xBFOlvidaste tu contrase\xF1a?"
    },
    noAvailableMethods: {
      message: "No se puede continuar con el inicio de sesi\xF3n. No hay ning\xFAn factor de autenticaci\xF3n disponible.",
      subtitle: "Ocurri\xF3 un error",
      title: "No puedo iniciar sesi\xF3n"
    },
    passkey: {
      subtitle: "Usando tu llave de acceso confirmas que eres t\xFA. Tu dispositivo puede pedirte la huella dactilar, el rostro o el bloqueo de pantalla.",
      title: "Usa tu llave de acceso"
    },
    password: {
      actionLink: "Use otro m\xE9todo",
      subtitle: "para continuar a {{applicationName}}",
      title: "Introduzca su contrase\xF1a"
    },
    passwordPwned: {
      title: "Contrase\xF1a en peligro"
    },
    phoneCode: {
      formTitle: "C\xF3digo de verificaci\xF3n",
      resendButton: "Reenviar c\xF3digo",
      subtitle: "para continuar con {{applicationName}}",
      title: "Revisa tu tel\xE9fono"
    },
    phoneCodeMfa: {
      formTitle: "C\xF3digo de verificaci\xF3n",
      resendButton: "Reenviar c\xF3digo",
      subtitle: "para continuar con {{applicationName}}",
      title: "Revisa tu tel\xE9fono"
    },
    resetPassword: {
      formButtonPrimary: "Restablecer contrase\xF1a",
      requiredMessage: "Por razones de seguridad, es necesario restablecer su contrase\xF1a.",
      successMessage: "Tu contrase\xF1a se ha cambiado correctamente. Te estamos redirigiendo, por favor espera un momento.",
      title: "Establecer nueva contrase\xF1a"
    },
    resetPasswordMfa: {
      detailsLabel: "Es necesario verificar su identidad para restablecer su contrase\xF1a."
    },
    start: {
      actionLink: "Registrarse",
      actionLink__join_waitlist: void 0,
      actionLink__use_email: "Utilizar correo electr\xF3nico",
      actionLink__use_email_username: "Utilizar correo electr\xF3nico o nombre de usuario",
      actionLink__use_passkey: "Usar llave de acceso",
      actionLink__use_phone: "Utilizar tel\xE9fono",
      actionLink__use_username: "Utilizar nombre de usuario",
      actionText: "\xBFNo tiene cuenta?",
      actionText__join_waitlist: void 0,
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "para continuar con {{applicationName}}",
      subtitleCombined: void 0,
      title: "Iniciar sesi\xF3n",
      titleCombined: void 0
    },
    totpMfa: {
      formTitle: "C\xF3digo de verificaci\xF3n",
      subtitle: "Para continuar, por favor introduce el c\xF3digo generado por tu aplicaci\xF3n de autenticaci\xF3n",
      title: "Verificaci\xF3n de dos factores"
    }
  },
  signInEnterPasswordTitle: "Ingresa tu contrase\xF1a",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "Entrar",
      actionText: "\xBFTiene una cuenta?",
      subtitle: "para continuar a {{applicationName}}",
      title: "Rellene los campos que faltan"
    },
    emailCode: {
      formSubtitle: "Introduzca el c\xF3digo de verificaci\xF3n enviado a su correo electr\xF3nico",
      formTitle: "C\xF3digo de verificaci\xF3n",
      resendButton: "Reenviar c\xF3digo",
      subtitle: "para continuar a {{applicationName}}",
      title: "Verifique su correo electr\xF3nico"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      formSubtitle: "Utilice el enlace de verificaci\xF3n enviado a su direcci\xF3n de correo electr\xF3nico",
      formTitle: "Enlace de verificaci\xF3n",
      loading: {
        title: "Registrando..."
      },
      resendButton: "Reenviar enlace",
      subtitle: "para continuar a {{applicationName}}",
      title: "Verifica tu correo electr\xF3nico",
      verified: {
        title: "Registrado con \xE9xito"
      },
      verifiedSwitchTab: {
        subtitle: "Regrese a la pesta\xF1a reci\xE9n abierta para continuar",
        subtitleNewTab: "Volver a la pesta\xF1a anterior para continuar",
        title: "Correo electr\xF3nico verificado con \xE9xito"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: void 0,
        label__onlyTermsOfService: void 0,
        label__termsOfServiceAndPrivacyPolicy: void 0
      },
      continue: {
        subtitle: void 0,
        title: void 0
      }
    },
    phoneCode: {
      formSubtitle: "Introduzca el c\xF3digo de verificaci\xF3n enviado a su n\xFAmero de tel\xE9fono.",
      formTitle: "C\xF3digo de verificaci\xF3n",
      resendButton: "Reenviar c\xF3digo",
      subtitle: "para continuar a {{applicationName}}",
      title: "Verifique su tel\xE9fono"
    },
    restrictedAccess: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__emailSupport: void 0,
      blockButton__joinWaitlist: void 0,
      subtitle: void 0,
      subtitleWaitlist: void 0,
      title: void 0
    },
    start: {
      actionLink: "Acceder",
      actionLink__use_email: void 0,
      actionLink__use_phone: void 0,
      actionText: "\xBFTienes una cuenta?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "para continuar con {{applicationName}}",
      subtitleCombined: "para continuar con {{applicationName}}",
      title: "Crea tu cuenta",
      titleCombined: "Crea tu cuenta"
    }
  },
  socialButtonsBlockButton: "Continuar con {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: void 0,
  unstable__errors: {
    already_a_member_in_organization: void 0,
    captcha_invalid: "El registro fall\xF3 debido a fallos en la validaci\xF3n de seguridad. Por favor, recargue la p\xE1gina o cont\xE1ctenos para obtener m\xE1s asistencia.",
    captcha_unavailable: "El registro fall\xF3 debido a fallos en la validaci\xF3n de bot. Por favor, recargue la p\xE1gina o cont\xE1ctenos para obtener m\xE1s asistencia.",
    form_code_incorrect: "C\xF3digo incorrecto.",
    form_identifier_exists__email_address: "La direcci\xF3n de correo ya existe.",
    form_identifier_exists__phone_number: "El n\xFAmero de tel\xE9fono ya existe.",
    form_identifier_exists__username: "El nombre de usuario ya existe.",
    form_identifier_not_found: "No se encontr\xF3 una cuenta con esos detalles.",
    form_param_format_invalid: "Formato inv\xE1lido.",
    form_param_format_invalid__email_address: "La direcci\xF3n de correo debe ser v\xE1lida.",
    form_param_format_invalid__phone_number: "El n\xFAmero de tel\xE9fono debe ser en un formato v\xE1lido internacional.",
    form_param_max_length_exceeded__first_name: "El nombre debe tener menos de 256 caracteres.",
    form_param_max_length_exceeded__last_name: "El apellido debe tener menos de 256 caracteres.",
    form_param_max_length_exceeded__name: "El nombre debe tener menos de 256 caracteres.",
    form_param_nil: "Campo vac\xEDo.",
    form_param_value_invalid: void 0,
    form_password_incorrect: "Contrase\xF1a incorrecta.",
    form_password_length_too_short: "La contrase\xF1a es muy corta.",
    form_password_not_strong_enough: "La contrase\xF1a no es suficientemente segura.",
    form_password_pwned: "Esta contrase\xF1a se encontr\xF3 como parte de una infracci\xF3n y no se puede usar; pruebe con otra contrase\xF1a.",
    form_password_pwned__sign_in: "La contrase\xF1a es muy insegura.",
    form_password_size_in_bytes_exceeded: "La contrase\xF1a excede el n\xFAmero m\xE1ximo de bytes permitidos. Por favor, elimine algunos caracteres especiales o reduzca la longitud de la contrase\xF1a.",
    form_password_validation_failed: "Contrase\xF1a incorrecta",
    form_username_invalid_character: "Car\xE1cter inv\xE1lido.",
    form_username_invalid_length: "La longitud del nombre de usuario es demasiado corta.",
    identification_deletion_failed: "No se puede eliminar la \xFAltima identificaci\xF3n.",
    not_allowed_access: "La direcci\xF3n de correo electr\xF3nico o el n\xFAmero de tel\xE9fono no est\xE1 permitido para registrarse. Esto puede deberse al uso de '+', '=', '#' o '.' en tu direcci\xF3n de correo electr\xF3nico, el uso de un dominio conectado a un servicio de correo electr\xF3nico temporal o la exclusi\xF3n expl\xEDcita. Si cree que se trata de un error, p\xF3ngase en contacto con el soporte.",
    organization_domain_blocked: void 0,
    organization_domain_common: void 0,
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: void 0,
    organization_minimum_permissions_needed: void 0,
    passkey_already_exists: "Ya se ha registrado una llave de acceso en este dispositivo.",
    passkey_not_supported: "Las llaves de acceso no son compatibles con este dispositivo.",
    passkey_pa_not_supported: "El registro requiere un autenticador de plataforma, pero el dispositivo no lo admite.",
    passkey_registration_cancelled: "El registro de la llave de acceso se ha cancelado o ha expirado.",
    passkey_retrieval_cancelled: "La verificaci\xF3n de la llave de acceso se ha cancelado o ha expirado.",
    passwordComplexity: {
      maximumLength: "menos de {{length}} caracteres",
      minimumLength: "{{length}} o m\xE1s caracteres",
      requireLowercase: "al menos una letra min\xFAscula",
      requireNumbers: "al menos un n\xFAmero",
      requireSpecialCharacter: "al menos un caracter especial",
      requireUppercase: "al menos una letra may\xFAscula",
      sentencePrefix: "Tu contrase\xF1a debe contener"
    },
    phone_number_exists: "Este n\xFAmero de tel\xE9fono ya est\xE1 en uso. Por favor, trata con otro.",
    session_exists: "Ya has iniciado sesi\xF3n",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "Tu contrase\xF1a funciona, pero puede ser m\xE1s segura. Prueba a\xF1adiendo m\xE1s caracteres.",
      goodPassword: "Tu contrase\xF1a cumple con todos los requisitos necesarios.",
      notEnough: "Tu contrase\xF1a no es lo suficientemente segura.",
      suggestions: {
        allUppercase: "Escribe algunas letras en may\xFAsculas, pero no todas.",
        anotherWord: "A\xF1ade palabras menos comunes.",
        associatedYears: "Evita a\xF1os asociados contigo.",
        capitalization: "Escribe algunas letras en may\xFAsculas adem\xE1s de la primera.",
        dates: "Evita fechas asociadas contigo.",
        l33t: "Evita sustituciones predecibles como '@' por 'a'",
        longerKeyboardPattern: "Usa patrones de teclado m\xE1s largos y cambia la direcci\xF3n de escritura varias veces.",
        noNeed: "Puedes crear contrase\xF1as seguras sin usar s\xEDmbolos, n\xFAmeros o may\xFAsculas.",
        pwned: "Si utiliza esta contrase\xF1a en otro lugar, deber\xEDa cambiarla.",
        recentYears: "Evita a\xF1os recientes.",
        repeated: "Evita palabras y letras repetidas.",
        reverseWords: "Evita palabras comunes escritas al rev\xE9s",
        sequences: "Evita secuencias de letras comunes.",
        useWords: "Usa varias palabras, pero evita frases comunes."
      },
      warnings: {
        common: "Es una contrase\xF1a usada com\xFAnmente.",
        commonNames: "Nombres y apellidos comunes son f\xE1ciles de adivinar.",
        dates: "Las fechas son f\xE1ciles de adivinar.",
        extendedRepeat: 'Patrones repetidos como "abcabcabc" son f\xE1ciles de adivinar.',
        keyPattern: "Patrones cortos son f\xE1ciles de adivinar.",
        namesByThemselves: "Nombres y apellidos a solas son f\xE1ciles de adivinar.",
        pwned: "Su contrase\xF1a fue expuesta por una violaci\xF3n de datos en Internet.",
        recentYears: "Los a\xF1os recientes son f\xE1ciles de adivinar.",
        sequences: 'Patrones comunes como "abc" son f\xE1ciles de adivinar',
        similarToCommon: "Es similar a una contrase\xF1a usada habitualmente.",
        simpleRepeat: 'Caracteres repetidos como "aaa" son f\xE1ciles de adivinar',
        straightRow: "Teclas consecutivas en tu teclado son f\xE1ciles de adivinar.",
        topHundred: "Es una contrase\xF1a usada con mucha frecuencia.",
        topTen: "Es de las contrase\xF1as m\xE1s usadas.",
        userInputs: "No deber\xEDa haber datos personales o relacionados con esta p\xE1gina.",
        wordByItself: "Palabras \xFAnicas son f\xE1ciles de adivinar."
      }
    }
  },
  userButton: {
    action__addAccount: "A\xF1adir cuenta",
    action__manageAccount: "Administrar cuenta",
    action__signOut: "Cerrar sesi\xF3n",
    action__signOutAll: "Salir de todas las cuentas"
  },
  userProfile: {
    backupCodePage: {
      actionLabel__copied: "Copiado!",
      actionLabel__copy: "Copiar todo",
      actionLabel__download: "Descargar .txt",
      actionLabel__print: "Imprimir",
      infoText1: "Se habilitar\xE1n c\xF3digos de respaldo para esta cuenta.",
      infoText2: "Mantenga los c\xF3digos de respaldo en secreto y gu\xE1rdelos de forma segura. Puede regenerar c\xF3digos de respaldo si sospecha que se han visto comprometidos.",
      subtitle__codelist: "Guardelos de forma segura y mant\xE9ngalos en secreto.",
      successMessage: "Los c\xF3digos de respaldo ahora est\xE1n habilitados. Puede usar uno de estos para iniciar sesi\xF3n en su cuenta, si pierde el acceso a su dispositivo de autenticaci\xF3n. Cada c\xF3digo solo se puede utilizar una vez.",
      successSubtitle: "Puede usar uno de estos para iniciar sesi\xF3n en su cuenta, si pierde el acceso a su dispositivo de autenticaci\xF3n.",
      title: "Agregar verificaci\xF3n de c\xF3digo de respaldo",
      title__codelist: "C\xF3digos de respaldo"
    },
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "Seleccione un proveedor para conectar su cuenta.",
      formHint__noAccounts: "No hay proveedores de cuentas externas disponibles.",
      removeResource: {
        messageLine1: "{{identifier}} ser\xE1 eliminado de esta cuenta.",
        messageLine2: "Ya no podr\xE1 usar esta cuenta activa y las funciones dependientes ya no funcionar\xE1n.",
        successMessage: "{{connectedAccount}} ha sido eliminado de su cuenta.",
        title: "Eliminar cuenta conectada"
      },
      socialButtonsBlockButton: "Conectar cuenta de {{provider|titleize}}",
      successMessage: "El proveedor ha sido agregado a su cuenta",
      title: "Agregar cuenta conectada"
    },
    deletePage: {
      actionDescription: 'Escribe "Eliminar cuenta" a continuaci\xF3n para continuar',
      confirm: "Eliminar cuenta",
      messageLine1: "\xBFEstas seguro que quieres eliminar tu cuenta?",
      messageLine2: "Esta acci\xF3n es permanente e irreversible.",
      title: "Eliminar cuenta"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "A esta direcci\xF3n de correo electr\xF3nico se le enviar\xE1 un correo electr\xF3nico con un C\xF3digo de verificaci\xF3n.",
        formSubtitle: "Introduzca el c\xF3digo de verificaci\xF3n enviado a {{identifier}}",
        formTitle: "C\xF3digo de verificaci\xF3n",
        resendButton: "Re-enviar c\xF3digo",
        successMessage: "El correo electr\xF3nico {{identifier}} se ha agregado a su cuenta."
      },
      emailLink: {
        formHint: "Se enviar\xE1 un correo electr\xF3nico con un enlace de verificaci\xF3n a esta direcci\xF3n de correo electr\xF3nico.",
        formSubtitle: "Haga clic en el enlace de verificaci\xF3n en el correo electr\xF3nico enviado a {{identifier}}",
        formTitle: "Enlace de verificaci\xF3n",
        resendButton: "Reenviar enlace",
        successMessage: "El correo electr\xF3nico {{identifier}} se ha agregado a su cuenta."
      },
      enterpriseSSOLink: {
        formButton: void 0,
        formSubtitle: void 0
      },
      formHint: void 0,
      removeResource: {
        messageLine1: "{{identifier}} ser\xE1 eliminado de esta cuenta.",
        messageLine2: "Ya no podr\xE1 iniciar sesi\xF3n con esta direcci\xF3n de correo electr\xF3nico.",
        successMessage: "{{emailAddress}} ha sido eliminado de su cuenta.",
        title: "Eliminar direcci\xF3n de correo electr\xF3nico"
      },
      title: "Agregar direcci\xF3n de correo electr\xF3nico",
      verifyTitle: "Verificar direcci\xF3n de correo electr\xF3nico"
    },
    formButtonPrimary__add: "Agregar",
    formButtonPrimary__continue: "Continuar",
    formButtonPrimary__finish: "Terminar",
    formButtonPrimary__remove: "Eliminar",
    formButtonPrimary__save: "Guardar",
    formButtonReset: "Cancelar",
    mfaPage: {
      formHint: "Seleccione un m\xE9todo para agregar.",
      title: "Agregar verificaci\xF3n en dos pasos"
    },
    mfaPhoneCodePage: {
      backButton: "Usar n\xFAmero existente",
      primaryButton__addPhoneNumber: "Agregar n\xFAmero de tel\xE9fono",
      removeResource: {
        messageLine1: "{{identifier}} dejar\xE1 de recibir el C\xF3digo de verificaci\xF3n al iniciar sesi\xF3n.",
        messageLine2: "Es posible que su cuenta no sea tan segura. \xBFEst\xE1s seguro de que quieres continuar?",
        successMessage: "Se elimin\xF3 la verificaci\xF3n de dos pasos del c\xF3digo SMS para {{mfaPhoneCode}}",
        title: "Eliminar la verificaci\xF3n en dos pasos"
      },
      subtitle__availablePhoneNumbers: "Seleccione un n\xFAmero de tel\xE9fono para registrarse para la verificaci\xF3n en dos pasos del c\xF3digo SMS.",
      subtitle__unavailablePhoneNumbers: "No hay n\xFAmeros de tel\xE9fono disponibles para registrarse para la verificaci\xF3n en dos pasos del c\xF3digo SMS.",
      successMessage1: "Al iniciar sesi\xF3n, se le pedir\xE1 un c\xF3digo de verificaci\xF3n enviado a este n\xFAmero de tel\xE9fono como un paso adicional.",
      successMessage2: "Guarde estos c\xF3digos de respaldo y almac\xE9nelos en un lugar seguro. Si pierde el acceso a su dispositivo de autenticaci\xF3n, puede utilizar los c\xF3digos de respaldo para iniciar sesi\xF3n.",
      successTitle: "Verificaci\xF3n de c\xF3digo SMS habilitada",
      title: "Agregar verificaci\xF3n de c\xF3digo SMS"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Escanea el c\xF3digo QR en su lugar",
        buttonUnableToScan__nonPrimary: "\xBFNo puedes escanear el c\xF3digo QR?",
        infoText__ableToScan: "Configure un nuevo m\xE9todo de inicio de sesi\xF3n en su aplicaci\xF3n de autenticaci\xF3n y escanee el siguiente c\xF3digo QR para vincularlo a su cuenta.",
        infoText__unableToScan: "Configure un nuevo m\xE9todo de inicio de sesi\xF3n en su autenticador e ingrese la clave que se proporciona a continuaci\xF3n.",
        inputLabel__unableToScan1: "Aseg\xFArese de que las contrase\xF1as basadas en el tiempo o de un solo uso est\xE9n habilitadas, luego finalice de vincular su cuenta.",
        inputLabel__unableToScan2: "Alternativamente, si su autenticador admite URIs TOTP, tambi\xE9n puede copiar la URI completa."
      },
      removeResource: {
        messageLine1: "El c\xF3digo de verificaci\xF3n de este autenticador ya no ser\xE1 necesario al iniciar sesi\xF3n.",
        messageLine2: "Es posible que su cuenta no sea tan segura. \xBFEst\xE1s seguro de que quieres continuar?",
        successMessage: "Se elimin\xF3 la verificaci\xF3n en dos pasos a trav\xE9s de la aplicaci\xF3n de autenticaci\xF3n.",
        title: "Eliminar la verificaci\xF3n en dos pasos"
      },
      successMessage: "La verificaci\xF3n en dos pasos ahora est\xE1 habilitada. Al iniciar sesi\xF3n, deber\xE1 ingresar un C\xF3digo de verificaci\xF3n de este autenticador como un paso adicional.",
      title: "Agregar aplicaci\xF3n de autenticaci\xF3n",
      verifySubtitle: "Ingrese el C\xF3digo de verificaci\xF3n generado por su autenticador",
      verifyTitle: "C\xF3digo de verificaci\xF3n"
    },
    mobileButton__menu: "Men\xFA",
    navbar: {
      account: "Perfil",
      billing: void 0,
      description: "Administra tu informaci\xF3n de cuenta.",
      security: "Seguridad",
      title: "Cuenta"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: "{{name}} ser\xE1 eliminada de esta cuenta.",
        title: "Eliminar llave de acceso"
      },
      subtitle__rename: "Puedes cambiar el nombre de la llave de acceso para que sea m\xE1s f\xE1cil de encontrar.",
      title__rename: "Renombrar llave de acceso"
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "Se recomienda cerrar la sesi\xF3n de todos los otros dispositivos que hayan utilizado su antigua contrase\xF1a.",
      readonly: "Tu contrase\xF1a no se puede editar actualmente porque solo se puede acceder a trav\xE9s de la conexi\xF3n de empresa.",
      successMessage__set: "Se ha establecido tu contrase\xF1a.",
      successMessage__signOutOfOtherSessions: "Se cerr\xF3 la sesi\xF3n de todos los dem\xE1s dispositivos.",
      successMessage__update: "Tu contrase\xF1a ha sido actualizada.",
      title__set: "Configurar la clave",
      title__update: "Cambiar contrase\xF1a"
    },
    phoneNumberPage: {
      infoText: "Se enviar\xE1 un mensaje de texto con un enlace de verificaci\xF3n a este n\xFAmero de tel\xE9fono.",
      removeResource: {
        messageLine1: "{{identifier}} ser\xE1 eliminado de esta cuenta.",
        messageLine2: "Ya no podr\xE1 iniciar sesi\xF3n con este n\xFAmero de tel\xE9fono.",
        successMessage: "{{phoneNumber}} ha sido eliminado de su cuenta.",
        title: "Eliminar n\xFAmero de tel\xE9fono"
      },
      successMessage: "{{identifier}} ha sido a\xF1adido a tu cuenta.",
      title: "Agregar el n\xFAmero de tel\xE9fono",
      verifySubtitle: "Ingrese el c\xF3digo de verificaci\xF3n enviado a {{identifier}}",
      verifyTitle: "Verificar n\xFAmero de tel\xE9fono"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "Cargue una imagen JPG, PNG, GIF o WEBP de menos de 10 MB",
      imageFormDestructiveActionSubtitle: "Eliminar la imagen",
      imageFormSubtitle: "Cargar imagen",
      imageFormTitle: "Imagen de perfil",
      readonly: "Tu informaci\xF3n de perfil ha sido proporcionada por la conexi\xF3n de empresa y no se puede editar.",
      successMessage: "Tu perfil ha sido actualizado.",
      title: "Actualizar perfil"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "Cerrar sesi\xF3n en el dispositivo",
        title: "Dispositivos activos"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "Int\xE9ntelo nuevamente",
        actionLabel__reauthorize: "Autorizar ahora",
        destructiveActionTitle: "Quitar",
        primaryButton: "Conectar cuenta",
        subtitle__disconnected: void 0,
        subtitle__reauthorize: "Los permisos requeridos han sido actualizados, y podr\xEDa experimentar limitaciones. Por favor, autorice de nuevo esta aplicaci\xF3n para evitar cualquier problema",
        title: "Cuentas conectadas"
      },
      dangerSection: {
        deleteAccountButton: "Eliminar cuenta",
        title: "Peligro"
      },
      emailAddressesSection: {
        destructiveAction: "Eliminar direcci\xF3n de correo electr\xF3nico",
        detailsAction__nonPrimary: "Establecer como primario",
        detailsAction__primary: "Completar la verificaci\xF3n",
        detailsAction__unverified: "Completar la verificaci\xF3n",
        primaryButton: "Agregar una direcci\xF3n de correo electr\xF3nico",
        title: "Correos electr\xF3nicos"
      },
      enterpriseAccountsSection: {
        title: "Cuentas de empresa"
      },
      headerTitle__account: "Cuenta",
      headerTitle__security: "Seguridad",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "Regenerar c\xF3digos",
          headerTitle: "C\xF3digos de respaldo",
          subtitle__regenerate: "Obtenga un nuevo conjunto de c\xF3digos de respaldo seguros. Los c\xF3digos de respaldo anteriores se eliminar\xE1n y no podr\xE1n ser usados.",
          title__regenerate: "Regenerar c\xF3digos de respaldo"
        },
        phoneCode: {
          actionLabel__setDefault: "Establecer por defecto",
          destructiveActionLabel: "Eliminar n\xFAmero telef\xF3nico"
        },
        primaryButton: "A\xF1adir verificaci\xF3n de dos pasos",
        title: "Verificaci\xF3n de dos pasos",
        totp: {
          destructiveActionTitle: "Eliminar",
          headerTitle: "Aplicaci\xF3n de autenticaci\xF3n"
        }
      },
      passkeysSection: {
        menuAction__destructive: "Eliminar",
        menuAction__rename: "Renombrar",
        primaryButton: void 0,
        title: "Llaves de acceso"
      },
      passwordSection: {
        primaryButton__setPassword: "Establecer contrase\xF1a ",
        primaryButton__updatePassword: "Cambiar contrase\xF1a",
        title: "Contrase\xF1a"
      },
      phoneNumbersSection: {
        destructiveAction: "Quitar n\xFAmero de tel\xE9fono",
        detailsAction__nonPrimary: "Establecer como primario",
        detailsAction__primary: "Completar la verificaci\xF3n",
        detailsAction__unverified: "Completar la verificaci\xF3n",
        primaryButton: "Agregar un n\xFAmero de tel\xE9fono",
        title: "N\xFAmeros telef\xF3nicos"
      },
      profileSection: {
        primaryButton: "Actualizar perfil",
        title: "Perfil"
      },
      usernameSection: {
        primaryButton__setUsername: "Crear nombre de usuario",
        primaryButton__updateUsername: "Cambiar nombre de usuario",
        title: "Nombre de usuario"
      },
      web3WalletsSection: {
        destructiveAction: "Quitar cartera",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Web3 cartera",
        title: "Web3 cartera"
      }
    },
    usernamePage: {
      successMessage: "Su nombre de usuario ha sido actualizado.",
      title__set: "Actualizar nombre de usuario",
      title__update: "Actualizar nombre de usuario"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} ser\xE1 eliminado de esta cuenta.",
        messageLine2: "Ya no podr\xE1 iniciar sesi\xF3n con esta billetera web3.",
        successMessage: "{{web3Wallet}} ha sido eliminado de su cuenta.",
        title: "Eliminar la billetera web3"
      },
      subtitle__availableWallets: "Seleccione una billetera web3 para conectarse a su cuenta.",
      subtitle__unavailableWallets: "No hay billetera web3 disponibles.",
      successMessage: "La billetera ha sido agregada a su cuenta.",
      title: "A\xF1adir web3 billetera",
      web3WalletButtonsBlockButton: void 0
    }
  },
  waitlist: {
    start: {
      actionLink: "Acceder",
      actionText: "Cuentas con acceso?",
      formButton: "Unete a la lista de espera",
      subtitle: "Ingresa tu correo electr\xF3nico y te avisaremos cuando tu lugar est\xE9 listo",
      title: "Unete a la lista de espera"
    },
    success: {
      message: "Ser\xE1s redirigido pronto...",
      subtitle: "Nos pondremos en contacto contigo cuando tu lugar est\xE9 listo",
      title: "Gracias por unirte a la lista de espera!"
    }
  }
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  esMX
});
//# sourceMappingURL=es-MX.js.map