"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailAlertChannel = void 0;
const alert_channel_1 = require("./alert-channel");
const project_1 = require("./project");
/**
 * Creates an Email Alert Channel
 *
 * @remarks
 *
 * This class make use of the Alert Channel endpoints.
 */
class EmailAlertChannel extends alert_channel_1.AlertChannel {
    address;
    /**
     * Constructs the Email Alert Channel instance
     *
     * @param logicalId unique project-scoped resource name identification
     * @param props email alert channel configuration properties
     * {@link https://checklyhq.com/docs/cli/constructs-reference/#emailalertchannel Read more in the docs}
     */
    constructor(logicalId, props) {
        super(logicalId, props);
        this.address = props.address;
        project_1.Session.registerConstruct(this);
    }
    synthesize() {
        return {
            ...super.synthesize(),
            type: 'EMAIL',
            config: {
                address: this.address,
            },
        };
    }
}
exports.EmailAlertChannel = EmailAlertChannel;
//# sourceMappingURL=email-alert-channel.js.map