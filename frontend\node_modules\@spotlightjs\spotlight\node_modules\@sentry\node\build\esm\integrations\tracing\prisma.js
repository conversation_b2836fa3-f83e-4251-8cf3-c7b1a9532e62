import { _optional<PERSON>hain } from '@sentry/core';
import * as prismaInstrumentation from '@prisma/instrumentation';
import { defineIntegration, spanToJSON, SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, consoleSandbox } from '@sentry/core';
import { generateInstrumentOnce } from '../../otel/instrument.js';

const INTEGRATION_NAME = 'Prisma';

const EsmInteropPrismaInstrumentation =
  // @ts-expect-error We need to do the following for interop reasons
  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
  _optionalChain([prismaInstrumentation, 'access', _ => _.default, 'optionalAccess', _2 => _2.PrismaInstrumentation]) || prismaInstrumentation.PrismaInstrumentation;

function isPrismaV5TracingHelper(helper) {
  return !!helper && typeof helper === 'object' && 'createEngineSpan' in helper;
}

class SentryPrismaInteropInstrumentation extends EsmInteropPrismaInstrumentation {
   constructor() {
    super();
  }

   enable() {
    super.enable();

    // The PrismaIntegration (super class) defines a global variable `global["PRISMA_INSTRUMENTATION"]` when `enable()` is called. This global variable holds a "TracingHelper" which Prisma uses internally to create tracing data. It's their way of not depending on OTEL with their main package. The sucky thing is, prisma broke the interface of the tracing helper with the v6 major update. This means that if you use Prisma 6 with the v5 instrumentation (or vice versa) Prisma just blows up, because tries to call methods on the helper that no longer exist.
    // Because we actually want to use the v6 instrumentation and not blow up in Prisma 5 user's faces, what we're doing here is backfilling the v5 method (`createEngineSpan`) with a noop so that no longer crashes when it attempts to call that function.
    // We still won't fully emit all the spans, but this could potentially be implemented in the future.
    const prismaInstrumentationObject = (globalThis ).PRISMA_INSTRUMENTATION;
    const prismaTracingHelper =
      prismaInstrumentationObject &&
      typeof prismaInstrumentationObject === 'object' &&
      'helper' in prismaInstrumentationObject
        ? prismaInstrumentationObject.helper
        : undefined;

    let emittedWarning = false;

    if (isPrismaV5TracingHelper(prismaTracingHelper)) {
      (prismaTracingHelper ).dispatchEngineSpans = () => {
        consoleSandbox(() => {
          if (!emittedWarning) {
            emittedWarning = true;
            // eslint-disable-next-line no-console
            console.warn(
              '[Sentry] This version (v8) of the Sentry SDK does not support tracing with Prisma version 6 out of the box. To trace Prisma version 6, pass a `prismaInstrumentation` for version 6 to the Sentry `prismaIntegration`. Read more: https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/prisma/',
            );
          }
        });
      };
    }
  }
}

const instrumentPrisma = generateInstrumentOnce(
  INTEGRATION_NAME,
  options => {
    // Use a passed instrumentation instance to support older Prisma versions
    if (_optionalChain([options, 'optionalAccess', _3 => _3.prismaInstrumentation])) {
      return options.prismaInstrumentation;
    }

    return new SentryPrismaInteropInstrumentation();
  },
);

/**
 * Adds Sentry tracing instrumentation for the [Prisma](https://www.npmjs.com/package/prisma) ORM.
 * For more information, see the [`prismaIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/prisma/).
 *
 * Make sure `previewFeatures = ["tracing"]` is added to the generator block in your Prisma schema.
 *
 * ```prisma
 * generator client {
 *  provider = "prisma-client-js"
 *  previewFeatures = ["tracing"]
 * }
 * ```
 *
 * NOTE: By default, this integration works with Prisma version 5.
 * To get performance instrumentation for other Prisma versions,
 * 1. Install the `@prisma/instrumentation` package with the desired version.
 * 1. Pass a `new PrismaInstrumentation()` instance as exported from `@prisma/instrumentation` to the `prismaInstrumentation` option of this integration:
 *
 *    ```js
 *    import { PrismaInstrumentation } from '@prisma/instrumentation'
 *
 *    Sentry.init({
 *      integrations: [
 *        prismaIntegration({
 *          // Override the default instrumentation that Sentry uses
 *          prismaInstrumentation: new PrismaInstrumentation()
 *        })
 *      ]
 *    })
 *    ```
 *
 *    The passed instrumentation instance will override the default instrumentation instance the integration would use, while the `prismaIntegration` will still ensure data compatibility for the various Prisma versions.
 */
const prismaIntegration = defineIntegration(
  ({
    prismaInstrumentation,
  }

 = {}) => {
    return {
      name: INTEGRATION_NAME,
      setupOnce() {
        instrumentPrisma({ prismaInstrumentation });
      },
      setup(client) {
        client.on('spanStart', span => {
          const spanJSON = spanToJSON(span);
          if (_optionalChain([spanJSON, 'access', _4 => _4.description, 'optionalAccess', _5 => _5.startsWith, 'call', _6 => _6('prisma:')])) {
            span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, 'auto.db.otel.prisma');
          }

          // Make sure we use the query text as the span name, for ex. SELECT * FROM "User" WHERE "id" = $1
          if (spanJSON.description === 'prisma:engine:db_query' && _optionalChain([spanJSON, 'access', _7 => _7.data, 'optionalAccess', _8 => _8['db.query.text']])) {
            span.updateName(spanJSON.data['db.query.text'] );
          }

          // In Prisma v5.22+, the `db.system` attribute is automatically set
          // On older versions, this is missing, so we add it here
          if (spanJSON.description === 'prisma:engine:db_query' && !_optionalChain([spanJSON, 'access', _9 => _9.data, 'optionalAccess', _10 => _10['db.system']])) {
            span.setAttribute('db.system', 'prisma');
          }
        });
      },
    };
  },
);

export { instrumentPrisma, prismaIntegration };
//# sourceMappingURL=prisma.js.map
