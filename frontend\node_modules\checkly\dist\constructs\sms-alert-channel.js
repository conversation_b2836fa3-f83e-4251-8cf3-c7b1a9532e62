"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SmsAlertChannel = void 0;
const alert_channel_1 = require("./alert-channel");
const project_1 = require("./project");
/**
 * Creates a SMS Alert Channel
 *
 * @remarks
 *
 * This class make use of the Alert Channel endpoints.
 */
class SmsAlertChannel extends alert_channel_1.AlertChannel {
    phoneNumber;
    name;
    /**
     * Constructs the SMS Alert Channel instance
     *
     * @param logicalId unique project-scoped resource name identification
     * @param props SMS alert channel configuration properties
     *
     * {@link https://checklyhq.com/docs/cli/constructs-reference/#smsalertchannel Read more in the docs}
     */
    constructor(logicalId, props) {
        super(logicalId, props);
        this.phoneNumber = props.phoneNumber;
        this.name = props.name;
        project_1.Session.registerConstruct(this);
    }
    synthesize() {
        return {
            ...super.synthesize(),
            type: 'SMS',
            config: {
                number: this.phoneNumber,
                name: this.name,
            },
        };
    }
}
exports.SmsAlertChannel = SmsAlertChannel;
//# sourceMappingURL=sms-alert-channel.js.map