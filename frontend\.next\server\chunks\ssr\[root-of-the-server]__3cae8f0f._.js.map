{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/libs/Env.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\r\nimport { z } from 'zod/v4';\r\n\r\nexport const Env = createEnv({\r\n  server: {\r\n    // Security & Authentication\r\n    ARCJET_KEY: z.string().startsWith('ajkey_').optional(),\r\n    CLERK_SECRET_KEY: z.string().min(1),\r\n\r\n    // Database\r\n    DATABASE_URL: z.string().min(1),\r\n\r\n    // Logging & Monitoring\r\n    LOGTAIL_SOURCE_TOKEN: z.string().optional(),\r\n    SENTRY_DSN: z.string().optional(),\r\n\r\n    // Email & Communication\r\n    EMAIL_FROM: z.string().email().optional(),\r\n    EMAIL_REPLY_TO: z.string().email().optional(),\r\n    SMTP_HOST: z.string().optional(),\r\n    SMTP_PORT: z.string().optional(),\r\n    SMTP_USER: z.string().optional(),\r\n    SMTP_PASSWORD: z.string().optional(),\r\n    MAILCHIMP_API_KEY: z.string().optional(),\r\n    MAILCHIMP_AUDIENCE_ID: z.string().optional(),\r\n\r\n    // CRM Integration\r\n    HUBSPOT_API_KEY: z.string().optional(),\r\n    SALESFORCE_CLIENT_ID: z.string().optional(),\r\n    SALESFORCE_CLIENT_SECRET: z.string().optional(),\r\n\r\n    // Media & Content\r\n    CLOUDINARY_API_KEY: z.string().optional(),\r\n    CLOUDINARY_API_SECRET: z.string().optional(),\r\n\r\n    // Rate Limiting\r\n    RATE_LIMIT_MAX_REQUESTS: z.string().optional(),\r\n    RATE_LIMIT_WINDOW_MS: z.string().optional(),\r\n  },\r\n  client: {\r\n    // App Configuration\r\n    NEXT_PUBLIC_APP_URL: z.string().optional(),\r\n    NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: z.string().min(1),\r\n\r\n    // Analytics & Tracking\r\n    NEXT_PUBLIC_POSTHOG_KEY: z.string().optional(),\r\n    NEXT_PUBLIC_POSTHOG_HOST: z.string().optional(),\r\n    NEXT_PUBLIC_GA_MEASUREMENT_ID: z.string().optional(),\r\n\r\n    // Feature Flags\r\n    NEXT_PUBLIC_ENABLE_BLOG: z.string().optional(),\r\n    NEXT_PUBLIC_ENABLE_DOCUMENTATION: z.string().optional(),\r\n    NEXT_PUBLIC_ENABLE_NEWSLETTER: z.string().optional(),\r\n    NEXT_PUBLIC_ENABLE_CONTACT_FORM: z.string().optional(),\r\n    NEXT_PUBLIC_ENABLE_DEMO_REQUEST: z.string().optional(),\r\n\r\n    // Localization\r\n    NEXT_PUBLIC_DEFAULT_LOCALE: z.string().optional(),\r\n    NEXT_PUBLIC_SUPPORTED_LOCALES: z.string().optional(),\r\n\r\n    // API Configuration\r\n    NEXT_PUBLIC_API_URL: z.string().optional(),\r\n\r\n    // Media & Content\r\n    NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME: z.string().optional(),\r\n\r\n    // Site Metadata\r\n    NEXT_PUBLIC_SITE_NAME: z.string().optional(),\r\n    NEXT_PUBLIC_SITE_DESCRIPTION: z.string().optional(),\r\n    NEXT_PUBLIC_SITE_KEYWORDS: z.string().optional(),\r\n    NEXT_PUBLIC_TWITTER_HANDLE: z.string().optional(),\r\n    NEXT_PUBLIC_FACEBOOK_PAGE: z.string().optional(),\r\n    NEXT_PUBLIC_LINKEDIN_PAGE: z.string().optional(),\r\n  },\r\n  shared: {\r\n    NODE_ENV: z.enum(['test', 'development', 'production']).optional(),\r\n  },\r\n  // You need to destructure all the keys manually\r\n  runtimeEnv: {\r\n    // Security & Authentication\r\n    ARCJET_KEY: process.env.ARCJET_KEY,\r\n    CLERK_SECRET_KEY: process.env.CLERK_SECRET_KEY,\r\n\r\n    // Database\r\n    DATABASE_URL: process.env.DATABASE_URL,\r\n\r\n    // Logging & Monitoring\r\n    LOGTAIL_SOURCE_TOKEN: process.env.LOGTAIL_SOURCE_TOKEN,\r\n    SENTRY_DSN: process.env.SENTRY_DSN,\r\n\r\n    // Email & Communication\r\n    EMAIL_FROM: process.env.EMAIL_FROM,\r\n    EMAIL_REPLY_TO: process.env.EMAIL_REPLY_TO,\r\n    SMTP_HOST: process.env.SMTP_HOST,\r\n    SMTP_PORT: process.env.SMTP_PORT,\r\n    SMTP_USER: process.env.SMTP_USER,\r\n    SMTP_PASSWORD: process.env.SMTP_PASSWORD,\r\n    MAILCHIMP_API_KEY: process.env.MAILCHIMP_API_KEY,\r\n    MAILCHIMP_AUDIENCE_ID: process.env.MAILCHIMP_AUDIENCE_ID,\r\n\r\n    // CRM Integration\r\n    HUBSPOT_API_KEY: process.env.HUBSPOT_API_KEY,\r\n    SALESFORCE_CLIENT_ID: process.env.SALESFORCE_CLIENT_ID,\r\n    SALESFORCE_CLIENT_SECRET: process.env.SALESFORCE_CLIENT_SECRET,\r\n\r\n    // Media & Content\r\n    CLOUDINARY_API_KEY: process.env.CLOUDINARY_API_KEY,\r\n    CLOUDINARY_API_SECRET: process.env.CLOUDINARY_API_SECRET,\r\n\r\n    // Rate Limiting\r\n    RATE_LIMIT_MAX_REQUESTS: process.env.RATE_LIMIT_MAX_REQUESTS,\r\n    RATE_LIMIT_WINDOW_MS: process.env.RATE_LIMIT_WINDOW_MS,\r\n\r\n    // App Configuration\r\n    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,\r\n    NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY,\r\n\r\n    // Analytics & Tracking\r\n    NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY,\r\n    NEXT_PUBLIC_POSTHOG_HOST: process.env.NEXT_PUBLIC_POSTHOG_HOST,\r\n    NEXT_PUBLIC_GA_MEASUREMENT_ID: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID,\r\n\r\n    // Feature Flags\r\n    NEXT_PUBLIC_ENABLE_BLOG: process.env.NEXT_PUBLIC_ENABLE_BLOG,\r\n    NEXT_PUBLIC_ENABLE_DOCUMENTATION: process.env.NEXT_PUBLIC_ENABLE_DOCUMENTATION,\r\n    NEXT_PUBLIC_ENABLE_NEWSLETTER: process.env.NEXT_PUBLIC_ENABLE_NEWSLETTER,\r\n    NEXT_PUBLIC_ENABLE_CONTACT_FORM: process.env.NEXT_PUBLIC_ENABLE_CONTACT_FORM,\r\n    NEXT_PUBLIC_ENABLE_DEMO_REQUEST: process.env.NEXT_PUBLIC_ENABLE_DEMO_REQUEST,\r\n\r\n    // Localization\r\n    NEXT_PUBLIC_DEFAULT_LOCALE: process.env.NEXT_PUBLIC_DEFAULT_LOCALE,\r\n    NEXT_PUBLIC_SUPPORTED_LOCALES: process.env.NEXT_PUBLIC_SUPPORTED_LOCALES,\r\n\r\n    // API Configuration\r\n    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,\r\n\r\n    // Media & Content\r\n    NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,\r\n\r\n    // Site Metadata\r\n    NEXT_PUBLIC_SITE_NAME: process.env.NEXT_PUBLIC_SITE_NAME,\r\n    NEXT_PUBLIC_SITE_DESCRIPTION: process.env.NEXT_PUBLIC_SITE_DESCRIPTION,\r\n    NEXT_PUBLIC_SITE_KEYWORDS: process.env.NEXT_PUBLIC_SITE_KEYWORDS,\r\n    NEXT_PUBLIC_TWITTER_HANDLE: process.env.NEXT_PUBLIC_TWITTER_HANDLE,\r\n    NEXT_PUBLIC_FACEBOOK_PAGE: process.env.NEXT_PUBLIC_FACEBOOK_PAGE,\r\n    NEXT_PUBLIC_LINKEDIN_PAGE: process.env.NEXT_PUBLIC_LINKEDIN_PAGE,\r\n\r\n    // Shared\r\n    NODE_ENV: process.env.NODE_ENV,\r\n  },\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE;IAC3B,QAAQ;QACN,4BAA4B;QAC5B,YAAY,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC,UAAU,QAAQ;QACpD,kBAAkB,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;QAEjC,WAAW;QACX,cAAc,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;QAE7B,uBAAuB;QACvB,sBAAsB,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACzC,YAAY,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAE/B,wBAAwB;QACxB,YAAY,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,GAAG,QAAQ;QACvC,gBAAgB,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,GAAG,QAAQ;QAC3C,WAAW,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC9B,WAAW,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC9B,WAAW,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC9B,eAAe,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAClC,mBAAmB,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACtC,uBAAuB,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAE1C,kBAAkB;QAClB,iBAAiB,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACpC,sBAAsB,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACzC,0BAA0B,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAE7C,kBAAkB;QAClB,oBAAoB,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACvC,uBAAuB,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAE1C,gBAAgB;QAChB,yBAAyB,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5C,sBAAsB,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3C;IACA,QAAQ;QACN,oBAAoB;QACpB,qBAAqB,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACxC,mCAAmC,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;QAElD,uBAAuB;QACvB,yBAAyB,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5C,0BAA0B,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC7C,+BAA+B,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAElD,gBAAgB;QAChB,yBAAyB,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5C,kCAAkC,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACrD,+BAA+B,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAClD,iCAAiC,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACpD,iCAAiC,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAEpD,eAAe;QACf,4BAA4B,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC/C,+BAA+B,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAElD,oBAAoB;QACpB,qBAAqB,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAExC,kBAAkB;QAClB,mCAAmC,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAEtD,gBAAgB;QAChB,uBAAuB,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1C,8BAA8B,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACjD,2BAA2B,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC9C,4BAA4B,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC/C,2BAA2B,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC9C,2BAA2B,4LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChD;IACA,QAAQ;QACN,UAAU,4LAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAQ;YAAe;SAAa,EAAE,QAAQ;IAClE;IACA,gDAAgD;IAChD,YAAY;QACV,4BAA4B;QAC5B,YAAY,QAAQ,GAAG,CAAC,UAAU;QAClC,kBAAkB,QAAQ,GAAG,CAAC,gBAAgB;QAE9C,WAAW;QACX,cAAc,QAAQ,GAAG,CAAC,YAAY;QAEtC,uBAAuB;QACvB,sBAAsB,QAAQ,GAAG,CAAC,oBAAoB;QACtD,YAAY,QAAQ,GAAG,CAAC,UAAU;QAElC,wBAAwB;QACxB,YAAY,QAAQ,GAAG,CAAC,UAAU;QAClC,gBAAgB,QAAQ,GAAG,CAAC,cAAc;QAC1C,WAAW,QAAQ,GAAG,CAAC,SAAS;QAChC,WAAW,QAAQ,GAAG,CAAC,SAAS;QAChC,WAAW,QAAQ,GAAG,CAAC,SAAS;QAChC,eAAe,QAAQ,GAAG,CAAC,aAAa;QACxC,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;QAChD,uBAAuB,QAAQ,GAAG,CAAC,qBAAqB;QAExD,kBAAkB;QAClB,iBAAiB,QAAQ,GAAG,CAAC,eAAe;QAC5C,sBAAsB,QAAQ,GAAG,CAAC,oBAAoB;QACtD,0BAA0B,QAAQ,GAAG,CAAC,wBAAwB;QAE9D,kBAAkB;QAClB,oBAAoB,QAAQ,GAAG,CAAC,kBAAkB;QAClD,uBAAuB,QAAQ,GAAG,CAAC,qBAAqB;QAExD,gBAAgB;QAChB,yBAAyB,QAAQ,GAAG,CAAC,uBAAuB;QAC5D,sBAAsB,QAAQ,GAAG,CAAC,oBAAoB;QAEtD,oBAAoB;QACpB,qBAAqB,QAAQ,GAAG,CAAC,mBAAmB;QACpD,iCAAiC;QAEjC,uBAAuB;QACvB,uBAAuB;QACvB,wBAAwB;QACxB,+BAA+B,QAAQ,GAAG,CAAC,6BAA6B;QAExE,gBAAgB;QAChB,yBAAyB,QAAQ,GAAG,CAAC,uBAAuB;QAC5D,kCAAkC,QAAQ,GAAG,CAAC,gCAAgC;QAC9E,+BAA+B,QAAQ,GAAG,CAAC,6BAA6B;QACxE,iCAAiC,QAAQ,GAAG,CAAC,+BAA+B;QAC5E,iCAAiC,QAAQ,GAAG,CAAC,+BAA+B;QAE5E,eAAe;QACf,4BAA4B,QAAQ,GAAG,CAAC,0BAA0B;QAClE,+BAA+B,QAAQ,GAAG,CAAC,6BAA6B;QAExE,oBAAoB;QACpB,qBAAqB,QAAQ,GAAG,CAAC,mBAAmB;QAEpD,kBAAkB;QAClB,mCAAmC,QAAQ,GAAG,CAAC,iCAAiC;QAEhF,gBAAgB;QAChB,uBAAuB,QAAQ,GAAG,CAAC,qBAAqB;QACxD,8BAA8B,QAAQ,GAAG,CAAC,4BAA4B;QACtE,2BAA2B,QAAQ,GAAG,CAAC,yBAAyB;QAChE,4BAA4B,QAAQ,GAAG,CAAC,0BAA0B;QAClE,2BAA2B,QAAQ,GAAG,CAAC,yBAAyB;QAChE,2BAA2B,QAAQ,GAAG,CAAC,yBAAyB;QAEhE,SAAS;QACT,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/analytics/PostHogPageView.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { usePathname, useSearchParams } from 'next/navigation';\r\nimport { usePostHog } from 'posthog-js/react';\r\nimport { Suspense, useEffect } from 'react';\r\n\r\nconst PostHogPageView = () => {\r\n  const pathname = usePathname();\r\n  const searchParams = useSearchParams();\r\n  const posthog = usePostHog();\r\n\r\n  // Track pageviews\r\n  useEffect(() => {\r\n    if (pathname && posthog) {\r\n      let url = window.origin + pathname;\r\n      if (searchParams.toString()) {\r\n        url = `${url}?${searchParams.toString()}`;\r\n      }\r\n\r\n      posthog.capture('$pageview', { $current_url: url });\r\n    }\r\n  }, [pathname, searchParams, posthog]);\r\n\r\n  return null;\r\n};\r\n\r\n// Wrap this in Suspense to avoid the `useSearchParams` usage above\r\n// from de-opting the whole app into client-side rendering\r\n// See: https://nextjs.org/docs/messages/deopted-into-client-rendering\r\nexport const SuspendedPostHogPageView = () => {\r\n  return (\r\n    <Suspense fallback={null}>\r\n      <PostHogPageView />\r\n    </Suspense>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,kBAAkB;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD;IAEzB,kBAAkB;IAClB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,SAAS;YACvB,IAAI,MAAM,OAAO,MAAM,GAAG;YAC1B,IAAI,aAAa,QAAQ,IAAI;gBAC3B,MAAM,GAAG,IAAI,CAAC,EAAE,aAAa,QAAQ,IAAI;YAC3C;YAEA,QAAQ,OAAO,CAAC,aAAa;gBAAE,cAAc;YAAI;QACnD;IACF,GAAG;QAAC;QAAU;QAAc;KAAQ;IAEpC,OAAO;AACT;AAKO,MAAM,2BAA2B;IACtC,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QAAC,UAAU;kBAClB,cAAA,8OAAC;;;;;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/analytics/PostHogProvider.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport posthog from 'posthog-js';\r\nimport { PostHogProvider as PHProvider } from 'posthog-js/react';\r\nimport { useEffect } from 'react';\r\nimport { Env } from '@/libs/Env';\r\nimport { SuspendedPostHogPageView } from './PostHogPageView';\r\n\r\nexport const PostHogProvider = (props: { children: React.ReactNode }) => {\r\n  useEffect(() => {\r\n    if (Env.NEXT_PUBLIC_POSTHOG_KEY) {\r\n      posthog.init(Env.NEXT_PUBLIC_POSTHOG_KEY, {\r\n        api_host: Env.NEXT_PUBLIC_POSTHOG_HOST,\r\n        capture_pageview: false, // Disable automatic pageview capture, as we capture manually\r\n        capture_pageleave: true, // Enable pageleave capture\r\n      });\r\n    }\r\n  }, []);\r\n\r\n  if (!Env.NEXT_PUBLIC_POSTHOG_KEY) {\r\n    return props.children;\r\n  }\r\n\r\n  return (\r\n    <PHProvider client={posthog}>\r\n      <SuspendedPostHogPageView />\r\n      {props.children}\r\n    </PHProvider>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQO,MAAM,kBAAkB,CAAC;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,kHAAA,CAAA,MAAG,CAAC,uBAAuB,EAAE;YAC/B,+IAAA,CAAA,UAAO,CAAC,IAAI,CAAC,kHAAA,CAAA,MAAG,CAAC,uBAAuB,EAAE;gBACxC,UAAU,kHAAA,CAAA,MAAG,CAAC,wBAAwB;gBACtC,kBAAkB;gBAClB,mBAAmB;YACrB;QACF;IACF,GAAG,EAAE;IAEL,IAAI,CAAC,kHAAA,CAAA,MAAG,CAAC,uBAAuB,EAAE;QAChC,OAAO,MAAM,QAAQ;IACvB;IAEA,qBACE,8OAAC,8JAAA,CAAA,kBAAU;QAAC,QAAQ,+IAAA,CAAA,UAAO;;0BACzB,8OAAC,kJAAA,CAAA,2BAAwB;;;;;YACxB,MAAM,QAAQ;;;;;;;AAGrB", "debugId": null}}]}