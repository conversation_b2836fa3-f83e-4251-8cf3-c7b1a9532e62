"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SmsAlertChannelCodegen = void 0;
const codegen_1 = require("./internal/codegen");
const sourcegen_1 = require("../sourcegen");
const alert_channel_codegen_1 = require("./alert-channel-codegen");
const construct = 'SmsAlertChannel';
class SmsAlertChannelCodegen extends codegen_1.Codegen {
    describe(resource) {
        if (resource.config.name) {
            return `SMS Alert Channel: ${resource.config.name}`;
        }
        return `SMS Alert Channel: ${resource.config.number}`;
    }
    prepare(logicalId, resource, context) {
        const { name, number } = resource.config;
        const last4Digits = number.slice(-4);
        const fallbackName = `sms-${last4Digits}`;
        const filename = context.filePath('resources/alert-channels/sms', name || fallbackName, {
            unique: true,
        });
        context.registerAlertChannel(resource.id, name ? `${name} sms` : fallbackName, this.program.generatedConstructFile(filename.fullPath));
    }
    gencode(logicalId, resource, context) {
        const { id, file } = context.lookupAlertChannel(resource.id);
        file.namedImport(construct, 'checkly/constructs');
        const { config } = resource;
        file.section((0, sourcegen_1.decl)(id, builder => {
            builder.variable((0, sourcegen_1.expr)((0, sourcegen_1.ident)(construct), builder => {
                builder.new(builder => {
                    builder.string(logicalId);
                    builder.object(builder => {
                        if (config.name) {
                            builder.string('name', config.name);
                        }
                        builder.string('phoneNumber', config.number);
                        (0, alert_channel_codegen_1.buildAlertChannelProps)(builder, resource);
                    });
                });
            }));
            builder.export();
        }));
    }
}
exports.SmsAlertChannelCodegen = SmsAlertChannelCodegen;
//# sourceMappingURL=sms-alert-channel-codegen.js.map