'use strict';

// src/index.ts
var _ = void 0;
function not(predicate) {
  return (data) => !predicate(data);
}
function or(a, b) {
  return (data) => a(data) || b(data);
}
function isArray(data) {
  return Array.isArray(data);
}
function isObject(data) {
  return typeof data === "object" && data !== null;
}
function isTruthy(data) {
  return Boolean(data);
}
var isFunction = (input) => typeof input === "function";
function identity(x) {
  return x;
}
var dual = function(arity, body) {
  if (typeof arity === "function") {
    return function() {
      return arity(arguments) ? body.apply(this, arguments) : (self) => body(self, ...arguments);
    };
  }
  switch (arity) {
    case 0:
    case 1:
      throw new RangeError(`Invalid arity ${arity}`);
    case 2:
      return function(a, b) {
        if (arguments.length >= 2) {
          return body(a, b);
        }
        return function(self) {
          return body(self, a);
        };
      };
    case 3:
      return function(a, b, c) {
        if (arguments.length >= 3) {
          return body(a, b, c);
        }
        return function(self) {
          return body(self, a, b);
        };
      };
    default:
      return function() {
        if (arguments.length >= arity) {
          return body.apply(this, arguments);
        }
        const args = arguments;
        return function(self) {
          return body(self, ...args);
        };
      };
  }
};
var apply = (a) => (self) => self(a);
function constant(x) {
  return () => x;
}
function constVoid() {
}
function constNull() {
  return null;
}
function constTrue() {
  return true;
}
function constFalse() {
  return false;
}
var flip = (f) => (...b) => (...a) => f(...a)(...b);
var compose = dual(2, (ab, bc) => (a) => bc(ab(a)));
var absurd = (_2) => {
  throw new Error("Called `absurd` function which should be uncallable");
};
var tupled = (f) => (a) => f(...a);
var untupled = (f) => (...a) => f(a);
var pipeArguments = (self, args) => {
  switch (args.length) {
    case 0:
      return self;
    case 1:
      return args[0](self);
    case 2:
      return args[1](args[0](self));
    case 3:
      return args[2](args[1](args[0](self)));
    case 4:
      return args[3](args[2](args[1](args[0](self))));
    case 5:
      return args[4](args[3](args[2](args[1](args[0](self)))));
    case 6:
      return args[5](args[4](args[3](args[2](args[1](args[0](self))))));
    case 7:
      return args[6](args[5](args[4](args[3](args[2](args[1](args[0](self)))))));
    case 8:
      return args[7](args[6](args[5](args[4](args[3](args[2](args[1](args[0](self))))))));
    case 9:
      return args[8](args[7](args[6](args[5](args[4](args[3](args[2](args[1](args[0](self)))))))));
    default: {
      let ret = self;
      for (let i = 0, len = args.length; i < len; i++) {
        ret = args[i](ret);
      }
      return ret;
    }
  }
};
function pipe(a, ...args) {
  return pipeArguments(a, args);
}
function flow(ab, bc, cd, de, ef, fg, gh, hi, ij) {
  switch (arguments.length) {
    case 1:
      return ab;
    case 2:
      return function() {
        return bc(ab.apply(this, arguments));
      };
    case 3:
      return function() {
        return cd(bc(ab.apply(this, arguments)));
      };
    case 4:
      return function() {
        return de(cd(bc(ab.apply(this, arguments))));
      };
    case 5:
      return function() {
        return ef(de(cd(bc(ab.apply(this, arguments)))));
      };
    case 6:
      return function() {
        return fg(ef(de(cd(bc(ab.apply(this, arguments))))));
      };
    case 7:
      return function() {
        return gh(fg(ef(de(cd(bc(ab.apply(this, arguments)))))));
      };
    case 8:
      return function() {
        return hi(gh(fg(ef(de(cd(bc(ab.apply(this, arguments))))))));
      };
    case 9:
      return function() {
        return ij(hi(gh(fg(ef(de(cd(bc(ab.apply(this, arguments)))))))));
      };
  }
  return;
}
function chunk(array, size) {
  const chunks = [];
  if (size <= 0) {
    return chunks;
  }
  for (let i = 0, j = array.length; i < j; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}
function zipWith(arrayA, arrayB, callback) {
  const result = [];
  for (let i = 0; i < arrayA.length; i++) {
    result.push(callback(arrayA[i], arrayB[i], i));
  }
  return result;
}
function getOrElse(map, key, callback) {
  if (map.has(key)) {
    return map.get(key);
  }
  return callback();
}
function getOrElseUpdate(map, key, callback) {
  if (map.has(key)) {
    return map.get(key);
  }
  const value = callback();
  map.set(key, value);
  return value;
}
function tryAddToSet(set, value) {
  if (!set.has(value)) {
    set.add(value);
    return true;
  }
  return false;
}

exports._ = _;
exports.absurd = absurd;
exports.apply = apply;
exports.chunk = chunk;
exports.compose = compose;
exports.constFalse = constFalse;
exports.constNull = constNull;
exports.constTrue = constTrue;
exports.constVoid = constVoid;
exports.constant = constant;
exports.dual = dual;
exports.flip = flip;
exports.flow = flow;
exports.getOrElse = getOrElse;
exports.getOrElseUpdate = getOrElseUpdate;
exports.identity = identity;
exports.isArray = isArray;
exports.isFunction = isFunction;
exports.isObject = isObject;
exports.isTruthy = isTruthy;
exports.not = not;
exports.or = or;
exports.pipe = pipe;
exports.pipeArguments = pipeArguments;
exports.tryAddToSet = tryAddToSet;
exports.tupled = tupled;
exports.untupled = untupled;
exports.zipWith = zipWith;
