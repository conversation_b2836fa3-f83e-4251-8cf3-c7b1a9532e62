{"version": 3, "sources": [], "sections": [{"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/utils/AppConfig.ts"], "sourcesContent": ["import type { LocalizationResource } from '@clerk/types';\r\nimport type { LocalePrefixMode } from 'next-intl/routing';\r\nimport { enUS, frFR } from '@clerk/localizations';\r\n\r\nconst localePrefix: LocalePrefixMode = 'as-needed';\r\n\r\n// FIXME: Update this configuration file based on your project information\r\nexport const AppConfig = {\r\n  name: 'Nextjs Starter',\r\n  locales: ['en', 'fr'],\r\n  defaultLocale: 'en',\r\n  localePrefix,\r\n};\r\n\r\nconst supportedLocales: Record<string, LocalizationResource> = {\r\n  en: enUS,\r\n  fr: frFR,\r\n};\r\n\r\nexport const ClerkLocalizations = {\r\n  defaultLocale: enUS,\r\n  supportedLocales,\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;;AAEA,MAAM,eAAiC;AAGhC,MAAM,YAAY;IACvB,MAAM;IACN,SAAS;QAAC;QAAM;KAAK;IACrB,eAAe;IACf;AACF;AAEA,MAAM,mBAAyD;IAC7D,IAAI,0JAAA,CAAA,OAAI;IACR,IAAI,0JAAA,CAAA,OAAI;AACV;AAEO,MAAM,qBAAqB;IAChC,eAAe,0JAAA,CAAA,OAAI;IACnB;AACF", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/libs/I18nRouting.ts"], "sourcesContent": ["import { defineRouting } from 'next-intl/routing';\r\nimport { AppConfig } from '@/utils/AppConfig';\r\n\r\nexport const routing = defineRouting({\r\n  locales: AppConfig.locales,\r\n  localePrefix: AppConfig.localePrefix,\r\n  defaultLocale: AppConfig.defaultLocale,\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,UAAU,CAAA,GAAA,kOAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,SAAS,yHAAA,CAAA,YAAS,CAAC,OAAO;IAC1B,cAAc,yHAAA,CAAA,YAAS,CAAC,YAAY;IACpC,eAAe,yHAAA,CAAA,YAAS,CAAC,aAAa;AACxC", "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/app/global-error.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as Sentry from '@sentry/nextjs';\r\nimport NextError from 'next/error';\r\nimport { useEffect } from 'react';\r\nimport { routing } from '@/libs/I18nRouting';\r\n\r\nexport default function GlobalError(props: {\r\n  error: Error & { digest?: string };\r\n}) {\r\n  useEffect(() => {\r\n    Sentry.captureException(props.error);\r\n  }, [props.error]);\r\n\r\n  return (\r\n    <html lang={routing.defaultLocale}>\r\n      <body>\r\n        {/* `NextError` is the default Next.js error page component. Its type\r\n        definition requires a `statusCode` prop. However, since the App Router\r\n        does not expose status codes for errors, we simply pass 0 to render a\r\n        generic error message. */}\r\n        <NextError statusCode={0} />\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS,YAAY,KAEnC;IACC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,CAAA,GAAA,qKAAA,CAAA,mBAAuB,AAAD,EAAE,MAAM,KAAK;IACrC,GAAG;QAAC,MAAM,KAAK;KAAC;IAEhB,qBACE,8OAAC;QAAK,MAAM,0HAAA,CAAA,UAAO,CAAC,aAAa;kBAC/B,cAAA,8OAAC;sBAKC,cAAA,8OAAC,6HAAA,CAAA,UAAS;gBAAC,YAAY;;;;;;;;;;;;;;;;AAI/B", "debugId": null}}]}