"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PagerdutyAlertChannelCodegen = void 0;
const codegen_1 = require("./internal/codegen");
const sourcegen_1 = require("../sourcegen");
const alert_channel_codegen_1 = require("./alert-channel-codegen");
const construct = 'PagerdutyAlertChannel';
class PagerdutyAlertChannelCodegen extends codegen_1.Codegen {
    describe(resource) {
        if (resource.config.account) {
            return `Pagerduty Alert Channel: ${resource.config.account}`;
        }
        if (resource.config.serviceName) {
            return `Pagerduty Alert Channel: ${resource.config.serviceName}`;
        }
        return 'Pagerduty Alert Channel';
    }
    prepare(logicalId, resource, context) {
        const { serviceName, account, serviceKey } = resource.config;
        const prefix = serviceName ?? account;
        const last4Chars = serviceKey.slice(-4);
        const fallbackName = `pagerduty-${last4Chars}`;
        const filename = context.filePath('resources/alert-channels/pagerduty', prefix || fallbackName, {
            unique: true,
        });
        context.registerAlertChannel(resource.id, prefix ? `${prefix} pagerduty` : fallbackName, this.program.generatedConstructFile(filename.fullPath));
    }
    gencode(logicalId, resource, context) {
        const { id, file } = context.lookupAlertChannel(resource.id);
        file.namedImport(construct, 'checkly/constructs');
        const { config } = resource;
        file.section((0, sourcegen_1.decl)(id, builder => {
            builder.variable((0, sourcegen_1.expr)((0, sourcegen_1.ident)(construct), builder => {
                builder.new(builder => {
                    builder.string(logicalId);
                    builder.object(builder => {
                        if (config.account) {
                            builder.string('account', config.account);
                        }
                        if (config.serviceName) {
                            builder.string('serviceName', config.serviceName);
                        }
                        builder.string('serviceKey', config.serviceKey);
                        (0, alert_channel_codegen_1.buildAlertChannelProps)(builder, resource);
                    });
                });
            }));
            builder.export();
        }));
    }
}
exports.PagerdutyAlertChannelCodegen = PagerdutyAlertChannelCodegen;
//# sourceMappingURL=pagerduty-alert-channel-codegen.js.map