"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MaintenanceWindowCodegen = void 0;
const codegen_1 = require("./internal/codegen");
const sourcegen_1 = require("../sourcegen");
const construct = 'MaintenanceWindow';
class MaintenanceWindowCodegen extends codegen_1.Codegen {
    describe(resource) {
        return `Maintenance Window: ${resource.name}`;
    }
    gencode(logicalId, resource, context) {
        const filePath = context.filePath('resources/maintenance-windows', resource.name, {
            unique: true,
        });
        const file = this.program.generatedConstructFile(filePath.fullPath);
        file.namedImport(construct, 'checkly/constructs');
        file.section((0, sourcegen_1.expr)((0, sourcegen_1.ident)(construct), builder => {
            builder.new(builder => {
                builder.string(logicalId);
                builder.object(builder => {
                    builder.string('name', resource.name);
                    builder.array('tags', builder => {
                        for (const tag of resource.tags) {
                            builder.string(tag);
                        }
                    });
                    builder.expr('startsAt', (0, sourcegen_1.ident)('Date'), builder => {
                        builder.new(builder => {
                            builder.string(resource.startsAt);
                        });
                    });
                    builder.expr('endsAt', (0, sourcegen_1.ident)('Date'), builder => {
                        builder.new(builder => {
                            builder.string(resource.endsAt);
                        });
                    });
                    if (resource.repeatInterval !== undefined && resource.repeatInterval !== null) {
                        builder.number('repeatInterval', resource.repeatInterval);
                    }
                    if (resource.repeatUnit) {
                        builder.string('repeatUnit', resource.repeatUnit);
                    }
                    if (resource.repeatEndsAt) {
                        const repeatEndsAt = resource.repeatEndsAt;
                        builder.expr('repeatEndsAt', (0, sourcegen_1.ident)('Date'), builder => {
                            builder.new(builder => {
                                builder.string(repeatEndsAt);
                            });
                        });
                    }
                });
            });
        }));
    }
}
exports.MaintenanceWindowCodegen = MaintenanceWindowCodegen;
//# sourceMappingURL=maintenance-window-codegen.js.map