{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/utils/AppConfig.ts"], "sourcesContent": ["import type { LocalizationResource } from '@clerk/types';\r\nimport type { LocalePrefixMode } from 'next-intl/routing';\r\nimport { enUS, frFR } from '@clerk/localizations';\r\n\r\nconst localePrefix: LocalePrefixMode = 'as-needed';\r\n\r\n// FIXME: Update this configuration file based on your project information\r\nexport const AppConfig = {\r\n  name: 'Nextjs Starter',\r\n  locales: ['en', 'fr'],\r\n  defaultLocale: 'en',\r\n  localePrefix,\r\n};\r\n\r\nconst supportedLocales: Record<string, LocalizationResource> = {\r\n  en: enUS,\r\n  fr: frFR,\r\n};\r\n\r\nexport const ClerkLocalizations = {\r\n  defaultLocale: enUS,\r\n  supportedLocales,\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;;AAEA,MAAM,eAAiC;AAGhC,MAAM,YAAY;IACvB,MAAM;IACN,SAAS;QAAC;QAAM;KAAK;IACrB,eAAe;IACf;AACF;AAEA,MAAM,mBAAyD;IAC7D,IAAI,6JAAA,CAAA,OAAI;IACR,IAAI,6JAAA,CAAA,OAAI;AACV;AAEO,MAAM,qBAAqB;IAChC,eAAe,6JAAA,CAAA,OAAI;IACnB;AACF", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/libs/I18nRouting.ts"], "sourcesContent": ["import { defineRouting } from 'next-intl/routing';\r\nimport { AppConfig } from '@/utils/AppConfig';\r\n\r\nexport const routing = defineRouting({\r\n  locales: AppConfig.locales,\r\n  localePrefix: AppConfig.localePrefix,\r\n  defaultLocale: AppConfig.defaultLocale,\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,UAAU,CAAA,GAAA,qOAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,SAAS,4HAAA,CAAA,YAAS,CAAC,OAAO;IAC1B,cAAc,4HAAA,CAAA,YAAS,CAAC,YAAY;IACpC,eAAe,4HAAA,CAAA,YAAS,CAAC,aAAa;AACxC", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/libs/I18nNavigation.ts"], "sourcesContent": ["import { createNavigation } from 'next-intl/navigation';\r\nimport { routing } from './I18nRouting';\r\n\r\nexport const { usePathname } = createNavigation(routing);\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,iQAAA,CAAA,mBAAgB,AAAD,EAAE,6HAAA,CAAA,UAAO", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/LocaleSwitcher.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport type { ChangeEventHandler } from 'react';\r\nimport { useLocale } from 'next-intl';\r\nimport { useRouter } from 'next/navigation';\r\nimport { usePathname } from '@/libs/I18nNavigation';\r\nimport { routing } from '@/libs/I18nRouting';\r\n\r\nexport const LocaleSwitcher = () => {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const locale = useLocale();\r\n\r\n  const handleChange: ChangeEventHandler<HTMLSelectElement> = (event) => {\r\n    router.push(`/${event.target.value}${pathname}`);\r\n    router.refresh(); // Ensure the page takes the new locale into account related to the issue #395\r\n  };\r\n\r\n  return (\r\n    <select\r\n      defaultValue={locale}\r\n      onChange={handleChange}\r\n      className=\"border border-gray-300 font-medium focus:outline-hidden focus-visible:ring-3\"\r\n      aria-label=\"lang-switcher\"\r\n    >\r\n      {routing.locales.map(elt => (\r\n        <option key={elt} value={elt}>\r\n          {elt.toUpperCase()}\r\n        </option>\r\n      ))}\r\n    </select>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;;;AANA;;;;;AAQO,MAAM,iBAAiB;;IAC5B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAsD,CAAC;QAC3D,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,KAAK,GAAG,UAAU;QAC/C,OAAO,OAAO,IAAI,8EAA8E;IAClG;IAEA,qBACE,6LAAC;QACC,cAAc;QACd,UAAU;QACV,WAAU;QACV,cAAW;kBAEV,6HAAA,CAAA,UAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,oBACnB,6LAAC;gBAAiB,OAAO;0BACtB,IAAI,WAAW;eADL;;;;;;;;;;AAMrB;GAxBa;;QACI,qIAAA,CAAA,YAAS;QACP,gIAAA,CAAA,cAAW;QACb,qKAAA,CAAA,YAAS;;;KAHb", "debugId": null}}]}