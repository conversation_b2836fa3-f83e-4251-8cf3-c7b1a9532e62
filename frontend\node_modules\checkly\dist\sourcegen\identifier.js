"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ident = exports.IdentifierValue = void 0;
const value_1 = require("./value");
const case_1 = require("./case");
class IdentifierValue extends value_1.Value {
    value;
    constructor(value) {
        super();
        this.value = value;
    }
    render(output) {
        output.append(this.value);
    }
}
exports.IdentifierValue = IdentifierValue;
function ident(value, options) {
    const format = options?.format;
    if (format !== undefined) {
        value = (0, case_1.cased)(value, format);
    }
    // Get rid of any leading digits.
    value = value.replace(/^[0-9]+/, '');
    return new IdentifierValue(value);
}
exports.ident = ident;
//# sourceMappingURL=identifier.js.map