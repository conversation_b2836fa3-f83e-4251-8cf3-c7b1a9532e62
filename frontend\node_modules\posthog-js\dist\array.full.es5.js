!function(){"use strict";var e,t,n,r,i,o,s,a,u,l,c,d,f,p,_,h,v,g,m="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function y(){return t?e:(t=1,e=function(e){try{return!!e()}catch(e){return!0}})}function b(){if(r)return n;r=1;var e=y();return n=!e((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))}function w(){if(o)return i;o=1;var e=b(),t=Function.prototype,n=t.call,r=e&&t.bind.bind(n,n);return i=e?r:function(e){return function(){return n.apply(e,arguments)}},i}function S(){if(a)return s;a=1;var e=w(),t=e({}.toString),n=e("".slice);return s=function(e){return n(t(e),8,-1)}}function k(){return d?c:(d=1,c=function(e){return null==e})}function x(){if(p)return f;p=1;var e=k(),t=TypeError;return f=function(n){if(e(n))throw new t("Can't call method on "+n);return n}}function E(){if(h)return _;h=1;var e=function(){if(l)return u;l=1;var e=w(),t=y(),n=S(),r=Object,i=e("".split);return u=t((function(){return!r("z").propertyIsEnumerable(0)}))?function(e){return"String"===n(e)?i(e,""):r(e)}:r}(),t=x();return _=function(n){return e(t(n))}}function T(){if(g)return v;g=1;var e=function(e){return e&&e.Math===Math&&e};return v=e("object"==typeof globalThis&&globalThis)||e("object"==typeof window&&window)||e("object"==typeof self&&self)||e("object"==typeof m&&m)||e("object"==typeof v&&v)||function(){return this}()||Function("return this")()}var C,I,P,F,R,M,O,L,A,q,$,D,N,H,B,j,U,V,z,W,G,Z,Y,Q,J,X,K,ee,te,ne={exports:{}};function re(){return I?C:(I=1,C=!1)}function ie(){if(F)return P;F=1;var e=T(),t=Object.defineProperty;return P=function(n,r){try{t(e,n,{value:r,configurable:!0,writable:!0})}catch(t){e[n]=r}return r}}function oe(){if(R)return ne.exports;R=1;var e=re(),t=T(),n=ie(),r="__core-js_shared__",i=ne.exports=t[r]||n(r,{});return(i.versions||(i.versions=[])).push({version:"3.38.1",mode:e?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.38.1/LICENSE",source:"https://github.com/zloirock/core-js"}),ne.exports}function se(){if(O)return M;O=1;var e=oe();return M=function(t,n){return e[t]||(e[t]=n||{})}}function ae(){if(A)return L;A=1;var e=x(),t=Object;return L=function(n){return t(e(n))}}function ue(){if($)return q;$=1;var e=w(),t=ae(),n=e({}.hasOwnProperty);return q=Object.hasOwn||function(e,r){return n(t(e),r)}}function le(){if(N)return D;N=1;var e=w(),t=0,n=Math.random(),r=e(1..toString);return D=function(e){return"Symbol("+(void 0===e?"":e)+")_"+r(++t+n,36)}}function ce(){if(U)return j;U=1;var e,t,n=T(),r=function(){if(B)return H;B=1;var e=T().navigator,t=e&&e.userAgent;return H=t?String(t):""}(),i=n.process,o=n.Deno,s=i&&i.versions||o&&o.version,a=s&&s.v8;return a&&(t=(e=a.split("."))[0]>0&&e[0]<4?1:+(e[0]+e[1])),!t&&r&&(!(e=r.match(/Edge\/(\d+)/))||e[1]>=74)&&(e=r.match(/Chrome\/(\d+)/))&&(t=+e[1]),j=t}function de(){if(z)return V;z=1;var e=ce(),t=y(),n=T().String;return V=!!Object.getOwnPropertySymbols&&!t((function(){var t=Symbol("symbol detection");return!n(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&e&&e<41}))}function fe(){if(G)return W;G=1;var e=de();return W=e&&!Symbol.sham&&"symbol"==typeof Symbol.iterator}function pe(){if(Y)return Z;Y=1;var e=T(),t=se(),n=ue(),r=le(),i=de(),o=fe(),s=e.Symbol,a=t("wks"),u=o?s.for||s:s&&s.withoutSetter||r;return Z=function(e){return n(a,e)||(a[e]=i&&n(s,e)?s[e]:u("Symbol."+e)),a[e]},Z}function _e(){if(J)return Q;J=1;var e="object"==typeof document&&document.all;return Q=void 0===e&&void 0!==e?function(t){return"function"==typeof t||t===e}:function(e){return"function"==typeof e}}function he(){if(K)return X;K=1;var e=_e();return X=function(t){return"object"==typeof t?null!==t:e(t)}}function ve(){if(te)return ee;te=1;var e=he(),t=String,n=TypeError;return ee=function(r){if(e(r))return r;throw new n(t(r)+" is not an object")}}var ge,me,ye,be,we={};function Se(){if(me)return ge;me=1;var e=y();return ge=!e((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))}function ke(){if(be)return ye;be=1;var e=Se(),t=y();return ye=e&&t((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))}var xe,Ee,Te,Ce,Ie,Pe,Fe,Re,Me,Oe,Le,Ae,qe,$e,De,Ne,He,Be,je,Ue,Ve,ze,We,Ge,Ze,Ye,Qe,Je,Xe,Ke,et,tt,nt,rt,it,ot,st,at,ut,lt,ct,dt,ft,pt,_t,ht,vt,gt,mt,yt,bt,wt,St,kt,xt,Et,Tt,Ct,It,Pt,Ft,Rt,Mt,Ot,Lt={};function At(){if(Ee)return xe;Ee=1;var e=T(),t=he(),n=e.document,r=t(n)&&t(n.createElement);return xe=function(e){return r?n.createElement(e):{}}}function qt(){if(Ce)return Te;Ce=1;var e=Se(),t=y(),n=At();return Te=!e&&!t((function(){return 7!==Object.defineProperty(n("div"),"a",{get:function(){return 7}}).a}))}function $t(){if(Pe)return Ie;Pe=1;var e=b(),t=Function.prototype.call;return Ie=e?t.bind(t):function(){return t.apply(t,arguments)},Ie}function Dt(){if(Re)return Fe;Re=1;var e=T(),t=_e();return Fe=function(n,r){return arguments.length<2?(i=e[n],t(i)?i:void 0):e[n]&&e[n][r];var i},Fe}function Nt(){if(Oe)return Me;Oe=1;var e=w();return Me=e({}.isPrototypeOf)}function Ht(){if(Ae)return Le;Ae=1;var e=Dt(),t=_e(),n=Nt(),r=fe(),i=Object;return Le=r?function(e){return"symbol"==typeof e}:function(r){var o=e("Symbol");return t(o)&&n(o.prototype,i(r))}}function Bt(){if($e)return qe;$e=1;var e=String;return qe=function(t){try{return e(t)}catch(e){return"Object"}}}function jt(){if(Ne)return De;Ne=1;var e=_e(),t=Bt(),n=TypeError;return De=function(r){if(e(r))return r;throw new n(t(r)+" is not a function")}}function Ut(){if(Be)return He;Be=1;var e=jt(),t=k();return He=function(n,r){var i=n[r];return t(i)?void 0:e(i)},He}function Vt(){if(Ue)return je;Ue=1;var e=$t(),t=_e(),n=he(),r=TypeError;return je=function(i,o){var s,a;if("string"===o&&t(s=i.toString)&&!n(a=e(s,i)))return a;if(t(s=i.valueOf)&&!n(a=e(s,i)))return a;if("string"!==o&&t(s=i.toString)&&!n(a=e(s,i)))return a;throw new r("Can't convert object to primitive value")}}function zt(){if(ze)return Ve;ze=1;var e=$t(),t=he(),n=Ht(),r=Ut(),i=Vt(),o=pe(),s=TypeError,a=o("toPrimitive");return Ve=function(o,u){if(!t(o)||n(o))return o;var l,c=r(o,a);if(c){if(void 0===u&&(u="default"),l=e(c,o,u),!t(l)||n(l))return l;throw new s("Can't convert object to primitive value")}return void 0===u&&(u="number"),i(o,u)}}function Wt(){if(Ge)return We;Ge=1;var e=zt(),t=Ht();return We=function(n){var r=e(n,"string");return t(r)?r:r+""}}function Gt(){if(Ze)return Lt;Ze=1;var e=Se(),t=qt(),n=ke(),r=ve(),i=Wt(),o=TypeError,s=Object.defineProperty,a=Object.getOwnPropertyDescriptor,u="enumerable",l="configurable",c="writable";return Lt.f=e?n?function(e,t,n){if(r(e),t=i(t),r(n),"function"==typeof e&&"prototype"===t&&"value"in n&&c in n&&!n[c]){var o=a(e,t);o&&o[c]&&(e[t]=n.value,n={configurable:l in n?n[l]:o[l],enumerable:u in n?n[u]:o[u],writable:!1})}return s(e,t,n)}:s:function(e,n,a){if(r(e),n=i(n),r(a),t)try{return s(e,n,a)}catch(e){}if("get"in a||"set"in a)throw new o("Accessors not supported");return"value"in a&&(e[n]=a.value),e},Lt}function Zt(){if(Xe)return Je;Xe=1;var e=function(){if(Qe)return Ye;Qe=1;var e=Math.ceil,t=Math.floor;return Ye=Math.trunc||function(n){var r=+n;return(r>0?t:e)(r)},Ye}();return Je=function(t){var n=+t;return n!=n||0===n?0:e(n)}}function Yt(){if(et)return Ke;et=1;var e=Zt(),t=Math.max,n=Math.min;return Ke=function(r,i){var o=e(r);return o<0?t(o+i,0):n(o,i)}}function Qt(){if(nt)return tt;nt=1;var e=Zt(),t=Math.min;return tt=function(n){var r=e(n);return r>0?t(r,9007199254740991):0}}function Jt(){if(it)return rt;it=1;var e=Qt();return rt=function(t){return e(t.length)}}function Xt(){return ut?at:(ut=1,at={})}function Kt(){if(ct)return lt;ct=1;var e=w(),t=ue(),n=E(),r=function(){if(st)return ot;st=1;var e=E(),t=Yt(),n=Jt(),r=function(r){return function(i,o,s){var a=e(i),u=n(a);if(0===u)return!r&&-1;var l,c=t(s,u);if(r&&o!=o){for(;u>c;)if((l=a[c++])!=l)return!0}else for(;u>c;c++)if((r||c in a)&&a[c]===o)return r||c||0;return!r&&-1}};return ot={includes:r(!0),indexOf:r(!1)}}().indexOf,i=Xt(),o=e([].push);return lt=function(e,s){var a,u=n(e),l=0,c=[];for(a in u)!t(i,a)&&t(u,a)&&o(c,a);for(;s.length>l;)t(u,a=s[l++])&&(~r(c,a)||o(c,a));return c},lt}function en(){return ft?dt:(ft=1,dt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"])}function tn(){if(_t)return pt;_t=1;var e=Kt(),t=en();return pt=Object.keys||function(n){return e(n,t)},pt}function nn(){if(gt)return vt;gt=1;var e=Dt();return vt=e("document","documentElement")}function rn(){if(yt)return mt;yt=1;var e=se(),t=le(),n=e("keys");return mt=function(e){return n[e]||(n[e]=t(e))}}function on(){if(wt)return bt;wt=1;var e,t=ve(),n=function(){if(ht)return we;ht=1;var e=Se(),t=ke(),n=Gt(),r=ve(),i=E(),o=tn();return we.f=e&&!t?Object.defineProperties:function(e,t){r(e);for(var s,a=i(t),u=o(t),l=u.length,c=0;l>c;)n.f(e,s=u[c++],a[s]);return e},we}(),r=en(),i=Xt(),o=nn(),s=At(),a=rn(),u="prototype",l="script",c=a("IE_PROTO"),d=function(){},f=function(e){return"<"+l+">"+e+"</"+l+">"},p=function(e){e.write(f("")),e.close();var t=e.parentWindow.Object;return e=null,t},_=function(){try{e=new ActiveXObject("htmlfile")}catch(e){}var t,n,i;_="undefined"!=typeof document?document.domain&&e?p(e):(n=s("iframe"),i="java"+l+":",n.style.display="none",o.appendChild(n),n.src=String(i),(t=n.contentWindow.document).open(),t.write(f("document.F=Object")),t.close(),t.F):p(e);for(var a=r.length;a--;)delete _[u][r[a]];return _()};return i[c]=!0,bt=Object.create||function(e,r){var i;return null!==e?(d[u]=t(e),i=new d,d[u]=null,i[c]=e):i=_(),void 0===r?i:n.f(i,r)},bt}function sn(){if(kt)return St;kt=1;var e=pe(),t=on(),n=Gt().f,r=e("unscopables"),i=Array.prototype;return void 0===i[r]&&n(i,r,{configurable:!0,value:t(null)}),St=function(e){i[r][e]=!0}}function an(){return Et?xt:(Et=1,xt={})}function un(){return Pt?It:(Pt=1,It=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}})}function ln(){if(Rt)return Ft;Rt=1;var e=Se(),t=Gt(),n=un();return Ft=e?function(e,r,i){return t.f(e,r,n(1,i))}:function(e,t,n){return e[t]=n,e}}function cn(){if(Ot)return Mt;Ot=1;var e,t,n,r=function(){if(Ct)return Tt;Ct=1;var e=T(),t=_e(),n=e.WeakMap;return Tt=t(n)&&/native code/.test(String(n))}(),i=T(),o=he(),s=ln(),a=ue(),u=oe(),l=rn(),c=Xt(),d="Object already initialized",f=i.TypeError,p=i.WeakMap;if(r||u.state){var _=u.state||(u.state=new p);_.get=_.get,_.has=_.has,_.set=_.set,e=function(e,t){if(_.has(e))throw new f(d);return t.facade=e,_.set(e,t),t},t=function(e){return _.get(e)||{}},n=function(e){return _.has(e)}}else{var h=l("state");c[h]=!0,e=function(e,t){if(a(e,h))throw new f(d);return t.facade=e,s(e,h,t),t},t=function(e){return a(e,h)?e[h]:{}},n=function(e){return a(e,h)}}return Mt={set:e,get:t,has:n,enforce:function(r){return n(r)?t(r):e(r,{})},getterFor:function(e){return function(n){var r;if(!o(n)||(r=t(n)).type!==e)throw new f("Incompatible receiver, "+e+" required");return r}}}}var dn,fn,pn={},_n={};function hn(){if(fn)return pn;fn=1;var e=Se(),t=$t(),n=function(){if(dn)return _n;dn=1;var e={}.propertyIsEnumerable,t=Object.getOwnPropertyDescriptor,n=t&&!e.call({1:2},1);return _n.f=n?function(e){var n=t(this,e);return!!n&&n.enumerable}:e,_n}(),r=un(),i=E(),o=Wt(),s=ue(),a=qt(),u=Object.getOwnPropertyDescriptor;return pn.f=e?u:function(e,l){if(e=i(e),l=o(l),a)try{return u(e,l)}catch(e){}if(s(e,l))return r(!t(n.f,e,l),e[l])},pn}var vn,gn,mn,yn,bn,wn,Sn,kn={exports:{}};function xn(){if(gn)return vn;gn=1;var e=Se(),t=ue(),n=Function.prototype,r=e&&Object.getOwnPropertyDescriptor,i=t(n,"name"),o=i&&"something"===function(){}.name,s=i&&(!e||e&&r(n,"name").configurable);return vn={EXISTS:i,PROPER:o,CONFIGURABLE:s}}function En(){if(yn)return mn;yn=1;var e=w(),t=_e(),n=oe(),r=e(Function.toString);return t(n.inspectSource)||(n.inspectSource=function(e){return r(e)}),mn=n.inspectSource}function Tn(){if(bn)return kn.exports;bn=1;var e=w(),t=y(),n=_e(),r=ue(),i=Se(),o=xn().CONFIGURABLE,s=En(),a=cn(),u=a.enforce,l=a.get,c=String,d=Object.defineProperty,f=e("".slice),p=e("".replace),_=e([].join),h=i&&!t((function(){return 8!==d((function(){}),"length",{value:8}).length})),v=String(String).split("String"),g=kn.exports=function(e,t,n){"Symbol("===f(c(t),0,7)&&(t="["+p(c(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),n&&n.getter&&(t="get "+t),n&&n.setter&&(t="set "+t),(!r(e,"name")||o&&e.name!==t)&&(i?d(e,"name",{value:t,configurable:!0}):e.name=t),h&&n&&r(n,"arity")&&e.length!==n.arity&&d(e,"length",{value:n.arity});try{n&&r(n,"constructor")&&n.constructor?i&&d(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var s=u(e);return r(s,"source")||(s.source=_(v,"string"==typeof t?t:"")),e};return Function.prototype.toString=g((function(){return n(this)&&l(this).source||s(this)}),"toString"),kn.exports}function Cn(){if(Sn)return wn;Sn=1;var e=_e(),t=Gt(),n=Tn(),r=ie();return wn=function(i,o,s,a){a||(a={});var u=a.enumerable,l=void 0!==a.name?a.name:o;if(e(s)&&n(s,l,a),a.global)u?i[o]=s:r(o,s);else{try{a.unsafe?i[o]&&(u=!0):delete i[o]}catch(e){}u?i[o]=s:t.f(i,o,{value:s,enumerable:!1,configurable:!a.nonConfigurable,writable:!a.nonWritable})}return i},wn}var In,Pn={};var Fn,Rn,Mn,On,Ln,An,qn,$n,Dn,Nn,Hn,Bn,jn,Un,Vn,zn,Wn,Gn,Zn,Yn,Qn,Jn,Xn,Kn,er,tr,nr,rr,ir,or,sr,ar,ur,lr={};function cr(){if(Mn)return Rn;Mn=1;var e=Dt(),t=w(),n=function(){if(In)return Pn;In=1;var e=Kt(),t=en().concat("length","prototype");return Pn.f=Object.getOwnPropertyNames||function(n){return e(n,t)},Pn}(),r=(Fn||(Fn=1,lr.f=Object.getOwnPropertySymbols),lr),i=ve(),o=t([].concat);return Rn=e("Reflect","ownKeys")||function(e){var t=n.f(i(e)),s=r.f;return s?o(t,s(e)):t}}function dr(){if(Ln)return On;Ln=1;var e=ue(),t=cr(),n=hn(),r=Gt();return On=function(i,o,s){for(var a=t(o),u=r.f,l=n.f,c=0;c<a.length;c++){var d=a[c];e(i,d)||s&&e(s,d)||u(i,d,l(o,d))}},On}function fr(){if(Dn)return $n;Dn=1;var e=T(),t=hn().f,n=ln(),r=Cn(),i=ie(),o=dr(),s=function(){if(qn)return An;qn=1;var e=y(),t=_e(),n=/#|\.prototype\./,r=function(n,r){var u=o[i(n)];return u===a||u!==s&&(t(r)?e(r):!!r)},i=r.normalize=function(e){return String(e).replace(n,".").toLowerCase()},o=r.data={},s=r.NATIVE="N",a=r.POLYFILL="P";return An=r}();return $n=function(a,u){var l,c,d,f,p,_=a.target,h=a.global,v=a.stat;if(l=h?e:v?e[_]||i(_,{}):e[_]&&e[_].prototype)for(c in u){if(f=u[c],d=a.dontCallGetSet?(p=t(l,c))&&p.value:l[c],!s(h?c:_+(v?".":"#")+c,a.forced)&&void 0!==d){if(typeof f==typeof d)continue;o(f,d)}(a.sham||d&&d.sham)&&n(f,"sham",!0),r(l,c,f,a)}}}function pr(){if(jn)return Bn;jn=1;var e=ue(),t=_e(),n=ae(),r=rn(),i=function(){if(Hn)return Nn;Hn=1;var e=y();return Nn=!e((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),Nn}(),o=r("IE_PROTO"),s=Object,a=s.prototype;return Bn=i?s.getPrototypeOf:function(r){var i=n(r);if(e(i,o))return i[o];var u=i.constructor;return t(u)&&i instanceof u?u.prototype:i instanceof s?a:null},Bn}function _r(){if(Vn)return Un;Vn=1;var e,t,n,r=y(),i=_e(),o=he(),s=on(),a=pr(),u=Cn(),l=pe(),c=re(),d=l("iterator"),f=!1;return[].keys&&("next"in(n=[].keys())?(t=a(a(n)))!==Object.prototype&&(e=t):f=!0),!o(e)||r((function(){var t={};return e[d].call(t)!==t}))?e={}:c&&(e=s(e)),i(e[d])||u(e,d,(function(){return this})),Un={IteratorPrototype:e,BUGGY_SAFARI_ITERATORS:f}}function hr(){if(Wn)return zn;Wn=1;var e=Gt().f,t=ue(),n=pe()("toStringTag");return zn=function(r,i,o){r&&!o&&(r=r.prototype),r&&!t(r,n)&&e(r,n,{configurable:!0,value:i})}}function vr(){if(Xn)return Jn;Xn=1;var e=he();return Jn=function(t){return e(t)||null===t}}function gr(){if(er)return Kn;er=1;var e=vr(),t=String,n=TypeError;return Kn=function(r){if(e(r))return r;throw new n("Can't set "+t(r)+" as a prototype")}}function mr(){if(nr)return tr;nr=1;var e=function(){if(Qn)return Yn;Qn=1;var e=w(),t=jt();return Yn=function(n,r,i){try{return e(t(Object.getOwnPropertyDescriptor(n,r)[i]))}catch(e){}}}(),t=he(),n=x(),r=gr();return tr=Object.setPrototypeOf||("__proto__"in{}?function(){var i,o=!1,s={};try{(i=e(Object.prototype,"__proto__","set"))(s,[]),o=s instanceof Array}catch(e){}return function(e,s){return n(e),r(s),t(e)?(o?i(e,s):e.__proto__=s,e):e}}():void 0),tr}function yr(){if(ir)return rr;ir=1;var e=fr(),t=$t(),n=re(),r=xn(),i=_e(),o=function(){if(Zn)return Gn;Zn=1;var e=_r().IteratorPrototype,t=on(),n=un(),r=hr(),i=an(),o=function(){return this};return Gn=function(s,a,u,l){var c=a+" Iterator";return s.prototype=t(e,{next:n(+!l,u)}),r(s,c,!1,!0),i[c]=o,s},Gn}(),s=pr(),a=mr(),u=hr(),l=ln(),c=Cn(),d=pe(),f=an(),p=_r(),_=r.PROPER,h=r.CONFIGURABLE,v=p.IteratorPrototype,g=p.BUGGY_SAFARI_ITERATORS,m=d("iterator"),y="keys",b="values",w="entries",S=function(){return this};return rr=function(r,d,p,k,x,E,T){o(p,d,k);var C,I,P,F=function(e){if(e===x&&A)return A;if(!g&&e&&e in O)return O[e];switch(e){case y:case b:case w:return function(){return new p(this,e)}}return function(){return new p(this)}},R=d+" Iterator",M=!1,O=r.prototype,L=O[m]||O["@@iterator"]||x&&O[x],A=!g&&L||F(x),q="Array"===d&&O.entries||L;if(q&&(C=s(q.call(new r)))!==Object.prototype&&C.next&&(n||s(C)===v||(a?a(C,v):i(C[m])||c(C,m,S)),u(C,R,!0,!0),n&&(f[R]=S)),_&&x===b&&L&&L.name!==b&&(!n&&h?l(O,"name",b):(M=!0,A=function(){return t(L,this)})),x)if(I={values:F(b),keys:E?A:F(y),entries:F(w)},T)for(P in I)(g||M||!(P in O))&&c(O,P,I[P]);else e({target:d,proto:!0,forced:g||M},I);return n&&!T||O[m]===A||c(O,m,A,{name:x}),f[d]=A,I},rr}function br(){return sr?or:(sr=1,or=function(e,t){return{value:e,done:t}})}function wr(){if(ur)return ar;ur=1;var e=E(),t=sn(),n=an(),r=cn(),i=Gt().f,o=yr(),s=br(),a=re(),u=Se(),l="Array Iterator",c=r.set,d=r.getterFor(l);ar=o(Array,"Array",(function(t,n){c(this,{type:l,target:e(t),index:0,kind:n})}),(function(){var e=d(this),t=e.target,n=e.index++;if(!t||n>=t.length)return e.target=null,s(void 0,!0);switch(e.kind){case"keys":return s(n,!1);case"values":return s(t[n],!1)}return s([n,t[n]],!1)}),"values");var f=n.Arguments=n.Array;if(t("keys"),t("values"),t("entries"),!a&&u&&"values"!==f.name)try{i(f,"name",{value:"values"})}catch(e){}return ar}var Sr,kr,xr,Er,Tr,Cr,Ir,Pr,Fr,Rr,Mr,Or,Lr,Ar,qr,$r,Dr,Nr,Hr,Br,jr,Ur,Vr,zr,Wr,Gr={};function Zr(){if(Er)return xr;Er=1;var e=function(){if(kr)return Sr;kr=1;var e=S(),t=w();return Sr=function(n){if("Function"===e(n))return t(n)}}(),t=jt(),n=b(),r=e(e.bind);return xr=function(e,i){return t(e),void 0===i?e:n?r(e,i):function(){return e.apply(i,arguments)}},xr}function Yr(){if(Cr)return Tr;Cr=1;var e=pe(),t=an(),n=e("iterator"),r=Array.prototype;return Tr=function(e){return void 0!==e&&(t.Array===e||r[n]===e)}}function Qr(){if(Rr)return Fr;Rr=1;var e=function(){if(Pr)return Ir;Pr=1;var e={};return e[pe()("toStringTag")]="z",Ir="[object z]"===String(e)}(),t=_e(),n=S(),r=pe()("toStringTag"),i=Object,o="Arguments"===n(function(){return arguments}());return Fr=e?n:function(e){var s,a,u;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(a=function(e,t){try{return e[t]}catch(e){}}(s=i(e),r))?a:o?n(s):"Object"===(u=n(s))&&t(s.callee)?"Arguments":u},Fr}function Jr(){if(Or)return Mr;Or=1;var e=Qr(),t=Ut(),n=k(),r=an(),i=pe()("iterator");return Mr=function(o){if(!n(o))return t(o,i)||t(o,"@@iterator")||r[e(o)]}}function Xr(){if(Ar)return Lr;Ar=1;var e=$t(),t=jt(),n=ve(),r=Bt(),i=Jr(),o=TypeError;return Lr=function(s,a){var u=arguments.length<2?i(s):a;if(t(u))return n(e(u,s));throw new o(r(s)+" is not iterable")},Lr}function Kr(){if($r)return qr;$r=1;var e=$t(),t=ve(),n=Ut();return qr=function(r,i,o){var s,a;t(r);try{if(!(s=n(r,"return"))){if("throw"===i)throw o;return o}s=e(s,r)}catch(e){a=!0,s=e}if("throw"===i)throw o;if(a)throw s;return t(s),o}}function ei(){if(Nr)return Dr;Nr=1;var e=Zr(),t=$t(),n=ve(),r=Bt(),i=Yr(),o=Jt(),s=Nt(),a=Xr(),u=Jr(),l=Kr(),c=TypeError,d=function(e,t){this.stopped=e,this.result=t},f=d.prototype;return Dr=function(p,_,h){var v,g,m,y,b,w,S,k=h&&h.that,x=!(!h||!h.AS_ENTRIES),E=!(!h||!h.IS_RECORD),T=!(!h||!h.IS_ITERATOR),C=!(!h||!h.INTERRUPTED),I=e(_,k),P=function(e){return v&&l(v,"normal",e),new d(!0,e)},F=function(e){return x?(n(e),C?I(e[0],e[1],P):I(e[0],e[1])):C?I(e,P):I(e)};if(E)v=p.iterator;else if(T)v=p;else{if(!(g=u(p)))throw new c(r(p)+" is not iterable");if(i(g)){for(m=0,y=o(p);y>m;m++)if((b=F(p[m]))&&s(f,b))return b;return new d(!1)}v=a(p,g)}for(w=E?p.next:v.next;!(S=t(w,v)).done;){try{b=F(S.value)}catch(e){l(v,"throw",e)}if("object"==typeof b&&b&&s(f,b))return b}return new d(!1)}}function ti(){if(Br)return Hr;Br=1;var e=Se(),t=Gt(),n=un();return Hr=function(r,i,o){e?t.f(r,i,n(0,o)):r[i]=o}}function ni(){if(Vr)return Ur;Vr=1;var e=T();return Ur=e}function ri(){if(Wr)return zr;Wr=1,wr(),function(){if(jr)return Gr;jr=1;var e=fr(),t=ei(),n=ti();e({target:"Object",stat:!0},{fromEntries:function(e){var r={};return t(e,(function(e,t){n(r,e,t)}),{AS_ENTRIES:!0}),r}})}();var e=ni();return zr=e.Object.fromEntries}var ii,oi,si,ai,ui,li,ci,di,fi,pi,_i,hi,vi={};function gi(){if(ui)return vi;ui=1;var e=T(),t=oi?ii:(oi=1,ii={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}),n=function(){if(ai)return si;ai=1;var e=At()("span").classList,t=e&&e.constructor&&e.constructor.prototype;return si=t===Object.prototype?void 0:t}(),r=wr(),i=ln(),o=hr(),s=pe()("iterator"),a=r.values,u=function(e,n){if(e){if(e[s]!==a)try{i(e,s,a)}catch(t){e[s]=a}if(o(e,n,!0),t[n])for(var u in r)if(e[u]!==r[u])try{i(e,u,r[u])}catch(t){e[u]=r[u]}}};for(var l in t)u(e[l]&&e[l].prototype,l);return u(n,"DOMTokenList"),vi}function mi(){if(fi)return di;fi=1;var e=function(){if(ci)return li;ci=1;var e=ri();return gi(),li=e}();return di=e}function yi(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function bi(e,t,n,r,i,o,s){try{var a=e[o](s),u=a.value}catch(e){return void n(e)}a.done?t(u):Promise.resolve(u).then(r,i)}function wi(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Ti(r.key),r)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Si(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(n)return(n=n.call(e)).next.bind(n);if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return yi(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?yi(e,t):void 0}}(e))||t){n&&(e=n);var r=0;return function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ki(){return ki=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ki.apply(null,arguments)}function xi(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}function Ei(){Ei=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",a=i.toStringTag||"@@toStringTag";function u(e,t,n,r){return Object.defineProperty(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r})}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function l(t,n,r,i){var o=n&&n.prototype instanceof f?n:f,s=Object.create(o.prototype);return u(s,"_invoke",function(t,n,r){var i=1;return function(o,s){if(3===i)throw Error("Generator is already running");if(4===i){if("throw"===o)throw s;return{value:e,done:!0}}for(r.method=o,r.arg=s;;){var a=r.delegate;if(a){var u=w(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(1===i)throw i=4,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=3;var l=c(t,n,r);if("normal"===l.type){if(i=r.done?4:2,l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(i=4,r.method="throw",r.arg=l.arg)}}}(t,r,new x(i||[])),!0),s}function c(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=l;var d={};function f(){}function p(){}function _(){}var h={};u(h,o,(function(){return this}));var v=Object.getPrototypeOf,g=v&&v(v(E([])));g&&g!==n&&r.call(g,o)&&(h=g);var m=_.prototype=f.prototype=Object.create(h);function y(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function b(e,t){function n(i,o,s,a){var u=c(e[i],e,o);if("throw"!==u.type){var l=u.arg,d=l.value;return d&&"object"==typeof d&&r.call(d,"__await")?t.resolve(d.__await).then((function(e){n("next",e,s,a)}),(function(e){n("throw",e,s,a)})):t.resolve(d).then((function(e){l.value=e,s(l)}),(function(e){return n("throw",e,s,a)}))}a(u.arg)}var i;u(this,"_invoke",(function(e,r){function o(){return new t((function(t,i){n(e,r,t,i)}))}return i=i?i.then(o,o):o()}),!0)}function w(t,n){var r=n.method,i=t.i[r];if(i===e)return n.delegate=null,"throw"===r&&t.i.return&&(n.method="return",n.arg=e,w(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=c(i,t.i,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,d;var s=o.arg;return s?s.done?(n[t.r]=s.value,n.next=t.n,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,d):s:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,d)}function S(e){this.tryEntries.push(e)}function k(t){var n=t[4]||{};n.type="normal",n.arg=e,t[4]=n}function x(e){this.tryEntries=[[-1]],e.forEach(S,this),this.reset(!0)}function E(t){if(null!=t){var n=t[o];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,s=function n(){for(;++i<t.length;)if(r.call(t,i))return n.value=t[i],n.done=!1,n;return n.value=e,n.done=!0,n};return s.next=s}}throw new TypeError(typeof t+" is not iterable")}return p.prototype=_,u(m,"constructor",_),u(_,"constructor",p),p.displayName=u(_,a,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,u(e,a,"GeneratorFunction")),e.prototype=Object.create(m),e},t.awrap=function(e){return{__await:e}},y(b.prototype),u(b.prototype,s,(function(){return this})),t.AsyncIterator=b,t.async=function(e,n,r,i,o){void 0===o&&(o=Promise);var s=new b(l(e,n,r,i),o);return t.isGeneratorFunction(n)?s:s.next().then((function(e){return e.done?e.value:s.next()}))},y(m),u(m,a,"Generator"),u(m,o,(function(){return this})),u(m,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.unshift(r);return function e(){for(;n.length;)if((r=n.pop())in t)return e.value=r,e.done=!1,e;return e.done=!0,e}},t.values=E,x.prototype={constructor:x,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(k),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0][4];if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function r(e){s.type="throw",s.arg=t,n.next=e}for(var i=n.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o[4],a=this.prev,u=o[1],l=o[2];if(-1===o[0])return r("end"),!1;if(!u&&!l)throw Error("try statement without catch or finally");if(null!=o[0]&&o[0]<=a){if(a<u)return this.method="next",this.arg=e,r(u),!0;if(a<l)return r(l),!1}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r[0]>-1&&r[0]<=this.prev&&this.prev<r[2]){var i=r;break}}i&&("break"===e||"continue"===e)&&i[0]<=t&&t<=i[2]&&(i=null);var o=i?i[4]:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i[2],d):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n[2]===e)return this.complete(n[4],n[3]),k(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n[0]===e){var r=n[4];if("throw"===r.type){var i=r.arg;k(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={i:E(t),r:n,n:r},"next"===this.method&&(this.arg=e),d}},t}function Ti(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t);if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==typeof t?t:t+""}hi||(hi=1,function(){if(_i)return pi;_i=1;var e=mi();pi=e}());var Ci,Ii,Pi,Fi,Ri,Mi,Oi,Li,Ai={},qi=[],$i=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,Di=Array.isArray;function Ni(e,t){for(var n in t)e[n]=t[n];return e}function Hi(e){var t=e.parentNode;t&&t.removeChild(e)}function Bi(e,t,n){var r,i,o,s={};for(o in t)"key"==o?r=t[o]:"ref"==o?i=t[o]:s[o]=t[o];if(arguments.length>2&&(s.children=arguments.length>3?Ci.call(arguments,2):n),"function"==typeof e&&null!=e.defaultProps)for(o in e.defaultProps)void 0===s[o]&&(s[o]=e.defaultProps[o]);return ji(e,s,r,i,null)}function ji(e,t,n,r,i){var o={type:e,props:t,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==i?++Pi:i,__i:-1,__u:0};return null==i&&null!=Ii.vnode&&Ii.vnode(o),o}function Ui(e){return e.children}function Vi(e,t){this.props=e,this.context=t}function zi(e,t){if(null==t)return e.__?zi(e.__,e.__i+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?zi(e):null}function Wi(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return Wi(e)}}function Gi(e){(!e.__d&&(e.__d=!0)&&Fi.push(e)&&!Zi.__r++||Ri!==Ii.debounceRendering)&&((Ri=Ii.debounceRendering)||Mi)(Zi)}function Zi(){var e,t,n,r,i,o,s,a,u;for(Fi.sort(Oi);e=Fi.shift();)e.__d&&(t=Fi.length,r=void 0,o=(i=(n=e).__v).__e,a=[],u=[],(s=n.__P)&&((r=Ni({},i)).__v=i.__v+1,Ii.vnode&&Ii.vnode(r),no(s,r,i,n.__n,void 0!==s.ownerSVGElement,32&i.__u?[o]:null,a,null==o?zi(i):o,!!(32&i.__u),u),r.__.__k[r.__i]=r,ro(a,r,u),r.__e!=o&&Wi(r)),Fi.length>t&&Fi.sort(Oi));Zi.__r=0}function Yi(e,t,n,r,i,o,s,a,u,l,c){var d,f,p,_,h,v=r&&r.__k||qi,g=t.length;for(n.__d=u,function(e,t,n){var r,i,o,s,a,u=t.length,l=n.length,c=l,d=0;for(e.__k=[],r=0;r<u;r++)null!=(i=e.__k[r]=null==(i=t[r])||"boolean"==typeof i||"function"==typeof i?null:"string"==typeof i||"number"==typeof i||"bigint"==typeof i||i.constructor==String?ji(null,i,null,null,i):Di(i)?ji(Ui,{children:i},null,null,null):void 0===i.constructor&&i.__b>0?ji(i.type,i.props,i.key,i.ref?i.ref:null,i.__v):i)?(i.__=e,i.__b=e.__b+1,a=Ji(i,n,s=r+d,c),i.__i=a,o=null,-1!==a&&(c--,(o=n[a])&&(o.__u|=131072)),null==o||null===o.__v?(-1==a&&d--,"function"!=typeof i.type&&(i.__u|=65536)):a!==s&&(a===s+1?d++:a>s?c>u-s?d+=a-s:d--:d=a<s&&a==s-1?a-s:0,a!==r+d&&(i.__u|=65536))):(o=n[r])&&null==o.key&&o.__e&&(o.__e==e.__d&&(e.__d=zi(o)),so(o,o,!1),n[r]=null,c--);if(c)for(r=0;r<l;r++)null!=(o=n[r])&&0==(131072&o.__u)&&(o.__e==e.__d&&(e.__d=zi(o)),so(o,o))}(n,t,v),u=n.__d,d=0;d<g;d++)null!=(p=n.__k[d])&&"boolean"!=typeof p&&"function"!=typeof p&&(f=-1===p.__i?Ai:v[p.__i]||Ai,p.__i=d,no(e,p,f,i,o,s,a,u,l,c),_=p.__e,p.ref&&f.ref!=p.ref&&(f.ref&&oo(f.ref,null,p),c.push(p.ref,p.__c||_,p)),null==h&&null!=_&&(h=_),65536&p.__u||f.__k===p.__k?u=Qi(p,u,e):"function"==typeof p.type&&void 0!==p.__d?u=p.__d:_&&(u=_.nextSibling),p.__d=void 0,p.__u&=-196609);n.__d=u,n.__e=h}function Qi(e,t,n){var r,i;if("function"==typeof e.type){for(r=e.__k,i=0;r&&i<r.length;i++)r[i]&&(r[i].__=e,t=Qi(r[i],t,n));return t}return e.__e!=t&&(n.insertBefore(e.__e,t||null),t=e.__e),t&&t.nextSibling}function Ji(e,t,n,r){var i=e.key,o=e.type,s=n-1,a=n+1,u=t[n];if(null===u||u&&i==u.key&&o===u.type)return n;if(r>(null!=u&&0==(131072&u.__u)?1:0))for(;s>=0||a<t.length;){if(s>=0){if((u=t[s])&&0==(131072&u.__u)&&i==u.key&&o===u.type)return s;s--}if(a<t.length){if((u=t[a])&&0==(131072&u.__u)&&i==u.key&&o===u.type)return a;a++}}return-1}function Xi(e,t,n){"-"===t[0]?e.setProperty(t,null==n?"":n):e[t]=null==n?"":"number"!=typeof n||$i.test(t)?n:n+"px"}function Ki(e,t,n,r,i){var o;e:if("style"===t)if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof r&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||Xi(e.style,t,"");if(n)for(t in n)r&&n[t]===r[t]||Xi(e.style,t,n[t])}else if("o"===t[0]&&"n"===t[1])o=t!==(t=t.replace(/(PointerCapture)$|Capture$/,"$1")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+o]=n,n?r?n.u=r.u:(n.u=Date.now(),e.addEventListener(t,o?to:eo,o)):e.removeEventListener(t,o?to:eo,o);else{if(i)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!==t&&"height"!==t&&"href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&"rowSpan"!==t&&"colSpan"!==t&&"role"!==t&&t in e)try{e[t]=null==n?"":n;break e}catch(e){}"function"==typeof n||(null==n||!1===n&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,n))}}function eo(e){var t=this.l[e.type+!1];if(e.t){if(e.t<=t.u)return}else e.t=Date.now();return t(Ii.event?Ii.event(e):e)}function to(e){return this.l[e.type+!0](Ii.event?Ii.event(e):e)}function no(e,t,n,r,i,o,s,a,u,l){var c,d,f,p,_,h,v,g,m,y,b,w,S,k,x,E=t.type;if(void 0!==t.constructor)return null;128&n.__u&&(u=!!(32&n.__u),o=[a=t.__e=n.__e]),(c=Ii.__b)&&c(t);e:if("function"==typeof E)try{if(g=t.props,m=(c=E.contextType)&&r[c.__c],y=c?m?m.props.value:c.__:r,n.__c?v=(d=t.__c=n.__c).__=d.__E:("prototype"in E&&E.prototype.render?t.__c=d=new E(g,y):(t.__c=d=new Vi(g,y),d.constructor=E,d.render=ao),m&&m.sub(d),d.props=g,d.state||(d.state={}),d.context=y,d.__n=r,f=d.__d=!0,d.__h=[],d._sb=[]),null==d.__s&&(d.__s=d.state),null!=E.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=Ni({},d.__s)),Ni(d.__s,E.getDerivedStateFromProps(g,d.__s))),p=d.props,_=d.state,d.__v=t,f)null==E.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(null==E.getDerivedStateFromProps&&g!==p&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(g,y),!d.__e&&(null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(g,d.__s,y)||t.__v===n.__v)){for(t.__v!==n.__v&&(d.props=g,d.state=d.__s,d.__d=!1),t.__e=n.__e,t.__k=n.__k,t.__k.forEach((function(e){e&&(e.__=t)})),b=0;b<d._sb.length;b++)d.__h.push(d._sb[b]);d._sb=[],d.__h.length&&s.push(d);break e}null!=d.componentWillUpdate&&d.componentWillUpdate(g,d.__s,y),null!=d.componentDidUpdate&&d.__h.push((function(){d.componentDidUpdate(p,_,h)}))}if(d.context=y,d.props=g,d.__P=e,d.__e=!1,w=Ii.__r,S=0,"prototype"in E&&E.prototype.render){for(d.state=d.__s,d.__d=!1,w&&w(t),c=d.render(d.props,d.state,d.context),k=0;k<d._sb.length;k++)d.__h.push(d._sb[k]);d._sb=[]}else do{d.__d=!1,w&&w(t),c=d.render(d.props,d.state,d.context),d.state=d.__s}while(d.__d&&++S<25);d.state=d.__s,null!=d.getChildContext&&(r=Ni(Ni({},r),d.getChildContext())),f||null==d.getSnapshotBeforeUpdate||(h=d.getSnapshotBeforeUpdate(p,_)),Yi(e,Di(x=null!=c&&c.type===Ui&&null==c.key?c.props.children:c)?x:[x],t,n,r,i,o,s,a,u,l),d.base=t.__e,t.__u&=-161,d.__h.length&&s.push(d),v&&(d.__E=d.__=null)}catch(e){t.__v=null,u||null!=o?(t.__e=a,t.__u|=u?160:32,o[o.indexOf(a)]=null):(t.__e=n.__e,t.__k=n.__k),Ii.__e(e,t,n)}else null==o&&t.__v===n.__v?(t.__k=n.__k,t.__e=n.__e):t.__e=io(n.__e,t,n,r,i,o,s,u,l);(c=Ii.diffed)&&c(t)}function ro(e,t,n){t.__d=void 0;for(var r=0;r<n.length;r++)oo(n[r],n[++r],n[++r]);Ii.__c&&Ii.__c(t,e),e.some((function(t){try{e=t.__h,t.__h=[],e.some((function(e){e.call(t)}))}catch(e){Ii.__e(e,t.__v)}}))}function io(e,t,n,r,i,o,s,a,u){var l,c,d,f,p,_,h,v=n.props,g=t.props,m=t.type;if("svg"===m&&(i=!0),null!=o)for(l=0;l<o.length;l++)if((p=o[l])&&"setAttribute"in p==!!m&&(m?p.localName===m:3===p.nodeType)){e=p,o[l]=null;break}if(null==e){if(null===m)return document.createTextNode(g);e=i?document.createElementNS("http://www.w3.org/2000/svg",m):document.createElement(m,g.is&&g),o=null,a=!1}if(null===m)v===g||a&&e.data===g||(e.data=g);else{if(o=o&&Ci.call(e.childNodes),v=n.props||Ai,!a&&null!=o)for(v={},l=0;l<e.attributes.length;l++)v[(p=e.attributes[l]).name]=p.value;for(l in v)p=v[l],"children"==l||("dangerouslySetInnerHTML"==l?d=p:"key"===l||l in g||Ki(e,l,null,p,i));for(l in g)p=g[l],"children"==l?f=p:"dangerouslySetInnerHTML"==l?c=p:"value"==l?_=p:"checked"==l?h=p:"key"===l||a&&"function"!=typeof p||v[l]===p||Ki(e,l,p,v[l],i);if(c)a||d&&(c.__html===d.__html||c.__html===e.innerHTML)||(e.innerHTML=c.__html),t.__k=[];else if(d&&(e.innerHTML=""),Yi(e,Di(f)?f:[f],t,n,r,i&&"foreignObject"!==m,o,s,o?o[0]:n.__k&&zi(n,0),a,u),null!=o)for(l=o.length;l--;)null!=o[l]&&Hi(o[l]);a||(l="value",void 0!==_&&(_!==e[l]||"progress"===m&&!_||"option"===m&&_!==v[l])&&Ki(e,l,_,v[l],!1),l="checked",void 0!==h&&h!==e[l]&&Ki(e,l,h,v[l],!1))}return e}function oo(e,t,n){try{"function"==typeof e?e(t):e.current=t}catch(e){Ii.__e(e,n)}}function so(e,t,n){var r,i;if(Ii.unmount&&Ii.unmount(e),(r=e.ref)&&(r.current&&r.current!==e.__e||oo(r,null,t)),null!=(r=e.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(e){Ii.__e(e,t)}r.base=r.__P=null,e.__c=void 0}if(r=e.__k)for(i=0;i<r.length;i++)r[i]&&so(r[i],t,n||"function"!=typeof e.type);n||null==e.__e||Hi(e.__e),e.__=e.__e=e.__d=void 0}function ao(e,t,n){return this.constructor(e,n)}function uo(e,t,n){var r,i,o,s;Ii.__&&Ii.__(e,t),i=(r="function"==typeof n)?null:t.__k,o=[],s=[],no(t,e=(!r&&n||t).__k=Bi(Ui,null,[e]),i||Ai,Ai,void 0!==t.ownerSVGElement,!r&&n?[n]:i?null:t.firstChild?Ci.call(t.childNodes):null,o,!r&&n?n:i?i.__e:t.firstChild,r,s),ro(o,e,s)}function lo(e,t,n){var r,i,o,s,a=Ni({},e.props);for(o in e.type&&e.type.defaultProps&&(s=e.type.defaultProps),t)"key"==o?r=t[o]:"ref"==o?i=t[o]:a[o]=void 0===t[o]&&void 0!==s?s[o]:t[o];return arguments.length>2&&(a.children=arguments.length>3?Ci.call(arguments,2):n),ji(e.type,a,r||e.key,i||e.ref,null)}Ci=qi.slice,Ii={__e:function(e,t,n,r){for(var i,o,s;t=t.__;)if((i=t.__c)&&!i.__)try{if((o=i.constructor)&&null!=o.getDerivedStateFromError&&(i.setState(o.getDerivedStateFromError(e)),s=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(e,r||{}),s=i.__d),s)return i.__E=i}catch(t){e=t}throw e}},Pi=0,Vi.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=Ni({},this.state),"function"==typeof e&&(e=e(Ni({},n),this.props)),e&&Ni(n,e),null!=e&&this.__v&&(t&&this._sb.push(t),Gi(this))},Vi.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),Gi(this))},Vi.prototype.render=Ui,Fi=[],Mi="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,Oi=function(e,t){return e.__v.__b-t.__v.__b},Zi.__r=0,Li=0;var co,fo,po,_o,ho=0,vo=[],go=[],mo=Ii.__b,yo=Ii.__r,bo=Ii.diffed,wo=Ii.__c,So=Ii.unmount;function ko(e,t){Ii.__h&&Ii.__h(fo,e,ho||t),ho=0;var n=fo.__H||(fo.__H={__:[],__h:[]});return e>=n.__.length&&n.__.push({__V:go}),n.__[e]}function xo(e){return ho=1,function(e,t,n){var r=ko(co++,2);if(r.t=e,!r.__c&&(r.__=[Ao(void 0,t),function(e){var t=r.__N?r.__N[0]:r.__[0],n=r.t(t,e);t!==n&&(r.__N=[n,r.__[1]],r.__c.setState({}))}],r.__c=fo,!fo.u)){var i=function(e,t,n){if(!r.__c.__H)return!0;var i=r.__c.__H.__.filter((function(e){return e.__c}));if(i.every((function(e){return!e.__N})))return!o||o.call(this,e,t,n);var s=!1;return i.forEach((function(e){if(e.__N){var t=e.__[0];e.__=e.__N,e.__N=void 0,t!==e.__[0]&&(s=!0)}})),!(!s&&r.__c.props===e)&&(!o||o.call(this,e,t,n))};fo.u=!0;var o=fo.shouldComponentUpdate,s=fo.componentWillUpdate;fo.componentWillUpdate=function(e,t,n){if(this.__e){var r=o;o=void 0,i(e,t,n),o=r}s&&s.call(this,e,t,n)},fo.shouldComponentUpdate=i}return r.__N||r.__}(Ao,e)}function Eo(e,t){var n=ko(co++,3);!Ii.__s&&Lo(n.__H,t)&&(n.__=e,n.i=t,fo.__H.__h.push(n))}function To(e){return ho=5,Co((function(){return{current:e}}),[])}function Co(e,t){var n=ko(co++,7);return Lo(n.__H,t)?(n.__V=e(),n.i=t,n.__h=e,n.__V):n.__}function Io(e){var t=fo.context[e.__c],n=ko(co++,9);return n.c=e,t?(null==n.__&&(n.__=!0,t.sub(fo)),t.props.value):e.__}function Po(){for(var e;e=vo.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(Mo),e.__H.__h.forEach(Oo),e.__H.__h=[]}catch(t){e.__H.__h=[],Ii.__e(t,e.__v)}}Ii.__b=function(e){fo=null,mo&&mo(e)},Ii.__r=function(e){yo&&yo(e),co=0;var t=(fo=e.__c).__H;t&&(po===fo?(t.__h=[],fo.__h=[],t.__.forEach((function(e){e.__N&&(e.__=e.__N),e.__V=go,e.__N=e.i=void 0}))):(t.__h.forEach(Mo),t.__h.forEach(Oo),t.__h=[],co=0)),po=fo},Ii.diffed=function(e){bo&&bo(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(1!==vo.push(t)&&_o===Ii.requestAnimationFrame||((_o=Ii.requestAnimationFrame)||Ro)(Po)),t.__H.__.forEach((function(e){e.i&&(e.__H=e.i),e.__V!==go&&(e.__=e.__V),e.i=void 0,e.__V=go}))),po=fo=null},Ii.__c=function(e,t){t.some((function(e){try{e.__h.forEach(Mo),e.__h=e.__h.filter((function(e){return!e.__||Oo(e)}))}catch(n){t.some((function(e){e.__h&&(e.__h=[])})),t=[],Ii.__e(n,e.__v)}})),wo&&wo(e,t)},Ii.unmount=function(e){So&&So(e);var t,n=e.__c;n&&n.__H&&(n.__H.__.forEach((function(e){try{Mo(e)}catch(e){t=e}})),n.__H=void 0,t&&Ii.__e(t,n.__v))};var Fo="function"==typeof requestAnimationFrame;function Ro(e){var t,n=function(){clearTimeout(r),Fo&&cancelAnimationFrame(t),setTimeout(e)},r=setTimeout(n,100);Fo&&(t=requestAnimationFrame(n))}function Mo(e){var t=fo,n=e.__c;"function"==typeof n&&(e.__c=void 0,n()),fo=t}function Oo(e){var t=fo;e.__c=e.__(),fo=t}function Lo(e,t){return!e||e.length!==t.length||t.some((function(t,n){return t!==e[n]}))}function Ao(e,t){return"function"==typeof t?t(e):t}var qo=function(e){return e.Button="button",e.Tab="tab",e.Selector="selector",e}({}),$o=function(e){return e.TopLeft="top_left",e.TopRight="top_right",e.TopCenter="top_center",e.MiddleLeft="middle_left",e.MiddleRight="middle_right",e.MiddleCenter="middle_center",e.Left="left",e.Center="center",e.Right="right",e.NextToTrigger="next_to_trigger",e}({}),Do=function(e){return e.Popover="popover",e.API="api",e.Widget="widget",e}({}),No=function(e){return e.Open="open",e.MultipleChoice="multiple_choice",e.SingleChoice="single_choice",e.Rating="rating",e.Link="link",e}({}),Ho=function(e){return e.NextQuestion="next_question",e.End="end",e.ResponseBased="response_based",e.SpecificQuestion="specific_question",e}({}),Bo=function(e){return e.Once="once",e.Recurring="recurring",e.Always="always",e}({}),jo=function(e){return e.SHOWN="survey shown",e.DISMISSED="survey dismissed",e.SENT="survey sent",e}({}),Uo=function(e){return e.SURVEY_ID="$survey_id",e.SURVEY_NAME="$survey_name",e.SURVEY_RESPONSE="$survey_response",e.SURVEY_ITERATION="$survey_iteration",e.SURVEY_ITERATION_START_DATE="$survey_iteration_start_date",e.SURVEY_PARTIALLY_COMPLETED="$survey_partially_completed",e.SURVEY_SUBMISSION_ID="$survey_submission_id",e.SURVEY_QUESTIONS="$survey_questions",e.SURVEY_COMPLETED="$survey_completed",e}({}),Vo="undefined"!=typeof window?window:void 0,zo="undefined"!=typeof globalThis?globalThis:Vo,Wo=Array.prototype,Go=Wo.forEach,Zo=Wo.indexOf,Yo=null==zo?void 0:zo.navigator,Qo=null==zo?void 0:zo.document,Jo=null==zo?void 0:zo.location,Xo=null==zo?void 0:zo.fetch,Ko=null!=zo&&zo.XMLHttpRequest&&"withCredentials"in new zo.XMLHttpRequest?zo.XMLHttpRequest:void 0,es=null==zo?void 0:zo.AbortController,ts=null==Yo?void 0:Yo.userAgent,ns=null!=Vo?Vo:{},rs={DEBUG:!1,LIB_VERSION:"1.255.1"},is="$copy_autocapture",os=["$snapshot","$pageview","$pageleave","$set","survey dismissed","survey sent","survey shown","$identify","$groupidentify","$create_alias","$$client_ingestion_warning","$web_experiment_applied","$feature_enrollment_update","$feature_flag_called"],ss=function(e){return e.GZipJS="gzip-js",e.Base64="base64",e}({}),as=["fatal","error","warning","log","info","debug"];function us(e,t){return-1!==e.indexOf(t)}var ls=function(e){return e.trim()},cs=function(e){return e.replace(/^\$/,"")};var ds=Array.isArray,fs=Object.prototype,ps=fs.hasOwnProperty,_s=fs.toString,hs=ds||function(e){return"[object Array]"===_s.call(e)},vs=function(e){return"function"==typeof e},gs=function(e){return e===Object(e)&&!hs(e)},ms=function(e){if(gs(e)){for(var t in e)if(ps.call(e,t))return!1;return!0}return!1},ys=function(e){return void 0===e},bs=function(e){return"[object String]"==_s.call(e)},ws=function(e){return bs(e)&&0===e.trim().length},Ss=function(e){return null===e},ks=function(e){return ys(e)||Ss(e)},xs=function(e){return"[object Number]"==_s.call(e)},Es=function(e){return"[object Boolean]"===_s.call(e)},Ts=function(e){return e instanceof FormData},Cs=function(e){return us(os,e)},Is=function(e){var t={_log:function(t){if(Vo&&(rs.DEBUG||ns.POSTHOG_DEBUG)&&!ys(Vo.console)&&Vo.console){for(var n=("__rrweb_original__"in Vo.console[t]?Vo.console[t].__rrweb_original__:Vo.console[t]),r=arguments.length,i=new Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];n.apply(void 0,[e].concat(i))}},info:function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];t._log.apply(t,["log"].concat(n))},warn:function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];t._log.apply(t,["warn"].concat(n))},error:function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];t._log.apply(t,["error"].concat(n))},critical:function(){for(var t,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];(t=console).error.apply(t,[e].concat(r))},uninitializedWarning:function(e){t.error("You must initialize PostHog before calling "+e)},createLogger:function(t){return Is(e+" "+t)}};return t},Ps=Is("[PostHog.js]"),Fs=Ps.createLogger,Rs={};function Ms(e,t,n){if(hs(e))if(Go&&e.forEach===Go)e.forEach(t,n);else if("length"in e&&e.length===+e.length)for(var r=0,i=e.length;r<i;r++)if(r in e&&t.call(n,e[r],r)===Rs)return}function Os(e,t,n){if(!ks(e)){if(hs(e))return Ms(e,t,n);if(Ts(e))for(var r,i=Si(e.entries());!(r=i()).done;){var o=r.value;if(t.call(n,o[1],o[0])===Rs)return}else for(var s in e)if(ps.call(e,s)&&t.call(n,e[s],s)===Rs)return}}var Ls=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return Ms(n,(function(t){for(var n in t)void 0!==t[n]&&(e[n]=t[n])})),e},As=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return Ms(n,(function(t){Ms(t,(function(t){e.push(t)}))})),e};function qs(e){for(var t=Object.keys(e),n=t.length,r=new Array(n);n--;)r[n]=[t[n],e[t[n]]];return r}var $s=function(e){try{return e()}catch(e){return}},Ds=function(e){return function(){try{for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.apply(this,n)}catch(e){Ps.critical("Implementation error. Please turn on debug mode and open a ticket on https://app.posthog.com/home#panel=support%3Asupport%3A."),Ps.critical(e)}}},Ns=function(e){var t={};return Os(e,(function(e,n){(bs(e)&&e.length>0||xs(e))&&(t[n]=e)})),t};function Hs(e,t){return n=e,r=function(e){return bs(e)&&!Ss(t)?e.slice(0,t):e},i=new Set,function e(t,n){return t!==Object(t)?r?r(t,n):t:i.has(t)?void 0:(i.add(t),hs(t)?(o=[],Ms(t,(function(t){o.push(e(t))}))):(o={},Os(t,(function(t,n){i.has(t)||(o[n]=e(t,n))}))),o);var o}(n);var n,r,i}var Bs=["herokuapp.com","vercel.app","netlify.app"];function js(e){var t=null==e?void 0:e.hostname;if(!bs(t))return!1;for(var n,r=t.split(".").slice(-2).join("."),i=Si(Bs);!(n=i()).done;){if(r===n.value)return!1}return!0}function Us(e,t){for(var n=0;n<e.length;n++)if(t(e[n]))return e[n]}function Vs(e,t,n,r){var i=null!=r?r:{},o=i.capture,s=void 0!==o&&o,a=i.passive,u=void 0===a||a;null==e||e.addEventListener(t,n,{capture:s,passive:u})}var zs=Fs("[Surveys]");function Ws(e){return!(!e.start_date||e.end_date)}function Gs(e){var t;return!(null==(t=e.conditions)||null==(t=t.events)||null==(t=t.values)||!t.length)}function Zs(e){var t;return!(null==(t=e.conditions)||null==(t=t.actions)||null==(t=t.values)||!t.length)}var Ys="seenSurvey_",Qs="inProgressSurvey_";Math.trunc||(Math.trunc=function(e){return e<0?Math.ceil(e):Math.floor(e)}),Number.isInteger||(Number.isInteger=function(e){return xs(e)&&isFinite(e)&&Math.floor(e)===e});var Js="0123456789abcdef",Xs=function(){function e(e){if(this.bytes=e,16!==e.length)throw new TypeError("not 128-bit length")}e.fromFieldsV7=function(t,n,r,i){if(!Number.isInteger(t)||!Number.isInteger(n)||!Number.isInteger(r)||!Number.isInteger(i)||t<0||n<0||r<0||i<0||t>0xffffffffffff||n>4095||r>1073741823||i>4294967295)throw new RangeError("invalid field value");var o=new Uint8Array(16);return o[0]=t/Math.pow(2,40),o[1]=t/Math.pow(2,32),o[2]=t/Math.pow(2,24),o[3]=t/Math.pow(2,16),o[4]=t/Math.pow(2,8),o[5]=t,o[6]=112|n>>>8,o[7]=n,o[8]=128|r>>>24,o[9]=r>>>16,o[10]=r>>>8,o[11]=r,o[12]=i>>>24,o[13]=i>>>16,o[14]=i>>>8,o[15]=i,new e(o)};var t=e.prototype;return t.toString=function(){for(var e="",t=0;t<this.bytes.length;t++)e=e+Js.charAt(this.bytes[t]>>>4)+Js.charAt(15&this.bytes[t]),3!==t&&5!==t&&7!==t&&9!==t||(e+="-");if(36!==e.length)throw new Error("Invalid UUIDv7 was generated");return e},t.clone=function(){return new e(this.bytes.slice(0))},t.equals=function(e){return 0===this.compareTo(e)},t.compareTo=function(e){for(var t=0;t<16;t++){var n=this.bytes[t]-e.bytes[t];if(0!==n)return Math.sign(n)}return 0},e}(),Ks=function(){function e(){this._timestamp=0,this._counter=0,this._random=new ra}var t=e.prototype;return t.generate=function(){var e=this.generateOrAbort();if(ys(e)){this._timestamp=0;var t=this.generateOrAbort();if(ys(t))throw new Error("Could not generate UUID after timestamp reset");return t}return e},t.generateOrAbort=function(){var e=Date.now();if(e>this._timestamp)this._timestamp=e,this._resetCounter();else{if(!(e+1e4>this._timestamp))return;this._counter++,this._counter>4398046511103&&(this._timestamp++,this._resetCounter())}return Xs.fromFieldsV7(this._timestamp,Math.trunc(this._counter/Math.pow(2,30)),this._counter&Math.pow(2,30)-1,this._random.nextUint32())},t._resetCounter=function(){this._counter=1024*this._random.nextUint32()+(1023&this._random.nextUint32())},e}(),ea=function(e){if("undefined"!=typeof UUIDV7_DENY_WEAK_RNG&&UUIDV7_DENY_WEAK_RNG)throw new Error("no cryptographically strong RNG available");for(var t=0;t<e.length;t++)e[t]=65536*Math.trunc(65536*Math.random())+Math.trunc(65536*Math.random());return e};Vo&&!ys(Vo.crypto)&&crypto.getRandomValues&&(ea=function(e){return crypto.getRandomValues(e)});var ta,na,ra=function(){function e(){this._buffer=new Uint32Array(8),this._cursor=1/0}return e.prototype.nextUint32=function(){return this._cursor>=this._buffer.length&&(ea(this._buffer),this._cursor=0),this._buffer[this._cursor++]},e}(),ia=function(){return oa().toString()},oa=function(){return(ta||(ta=new Ks)).generate()},sa="Mobile",aa="iOS",ua="Android",la="Tablet",ca=ua+" "+la,da="iPad",fa="Apple",pa=fa+" Watch",_a="Safari",ha="BlackBerry",va="Samsung",ga=va+"Browser",ma=va+" Internet",ya="Chrome",ba=ya+" OS",wa=ya+" "+aa,Sa="Internet Explorer",ka=Sa+" "+sa,xa="Opera",Ea=xa+" Mini",Ta="Edge",Ca="Microsoft "+Ta,Ia="Firefox",Pa=Ia+" "+aa,Fa="Nintendo",Ra="PlayStation",Ma="Xbox",Oa=ua+" "+sa,La=sa+" "+_a,Aa="Windows",qa=Aa+" Phone",$a="Nokia",Da="Ouya",Na="Generic",Ha=Na+" "+sa.toLowerCase(),Ba=Na+" "+la.toLowerCase(),ja="Konqueror",Ua="(\\d+(\\.\\d+)?)",Va=new RegExp("Version/"+Ua),za=new RegExp(Ma,"i"),Wa=new RegExp(Ra+" \\w+","i"),Ga=new RegExp(Fa+" \\w+","i"),Za=new RegExp(ha+"|PlayBook|BB10","i"),Ya={"NT3.51":"NT 3.11","NT4.0":"NT 4.0","5.0":"2000",5.1:"XP",5.2:"XP","6.0":"Vista",6.1:"7",6.2:"8",6.3:"8.1",6.4:"10","10.0":"10"};var Qa=function(e,t){return t&&us(t,fa)||function(e){return us(e,_a)&&!us(e,ya)&&!us(e,ua)}(e)},Ja=function(e,t){return t=t||"",us(e," OPR/")&&us(e,"Mini")?Ea:us(e," OPR/")?xa:Za.test(e)?ha:us(e,"IE"+sa)||us(e,"WPDesktop")?ka:us(e,ga)?ma:us(e,Ta)||us(e,"Edg/")?Ca:us(e,"FBIOS")?"Facebook "+sa:us(e,"UCWEB")||us(e,"UCBrowser")?"UC Browser":us(e,"CriOS")?wa:us(e,"CrMo")||us(e,ya)?ya:us(e,ua)&&us(e,_a)?Oa:us(e,"FxiOS")?Pa:us(e.toLowerCase(),ja.toLowerCase())?ja:Qa(e,t)?us(e,sa)?La:_a:us(e,Ia)?Ia:us(e,"MSIE")||us(e,"Trident/")?Sa:us(e,"Gecko")?Ia:""},Xa=((na={})[ka]=[new RegExp("rv:"+Ua)],na[Ca]=[new RegExp(Ta+"?\\/"+Ua)],na[ya]=[new RegExp("("+ya+"|CrMo)\\/"+Ua)],na[wa]=[new RegExp("CriOS\\/"+Ua)],na["UC Browser"]=[new RegExp("(UCBrowser|UCWEB)\\/"+Ua)],na[_a]=[Va],na[La]=[Va],na[xa]=[new RegExp("(Opera|OPR)\\/"+Ua)],na[Ia]=[new RegExp(Ia+"\\/"+Ua)],na[Pa]=[new RegExp("FxiOS\\/"+Ua)],na[ja]=[new RegExp("Konqueror[:/]?"+Ua,"i")],na[ha]=[new RegExp(ha+" "+Ua),Va],na[Oa]=[new RegExp("android\\s"+Ua,"i")],na[ma]=[new RegExp(ga+"\\/"+Ua)],na[Sa]=[new RegExp("(rv:|MSIE )"+Ua)],na.Mozilla=[new RegExp("rv:"+Ua)],na),Ka=function(e,t){var n=Ja(e,t),r=Xa[n];if(ys(r))return null;for(var i=0;i<r.length;i++){var o=r[i],s=e.match(o);if(s)return parseFloat(s[s.length-2])}return null},eu=[[new RegExp(Ma+"; "+Ma+" (.*?)[);]","i"),function(e){return[Ma,e&&e[1]||""]}],[new RegExp(Fa,"i"),[Fa,""]],[new RegExp(Ra,"i"),[Ra,""]],[Za,[ha,""]],[new RegExp(Aa,"i"),function(e,t){if(/Phone/.test(t)||/WPDesktop/.test(t))return[qa,""];if(new RegExp(sa).test(t)&&!/IEMobile\b/.test(t))return[Aa+" "+sa,""];var n=/Windows NT ([0-9.]+)/i.exec(t);if(n&&n[1]){var r=n[1],i=Ya[r]||"";return/arm/i.test(t)&&(i="RT"),[Aa,i]}return[Aa,""]}],[/((iPhone|iPad|iPod).*?OS (\d+)_(\d+)_?(\d+)?|iPhone)/,function(e){if(e&&e[3]){var t=[e[3],e[4],e[5]||"0"];return[aa,t.join(".")]}return[aa,""]}],[/(watch.*\/(\d+\.\d+\.\d+)|watch os,(\d+\.\d+),)/i,function(e){var t="";return e&&e.length>=3&&(t=ys(e[2])?e[3]:e[2]),["watchOS",t]}],[new RegExp("("+ua+" (\\d+)\\.(\\d+)\\.?(\\d+)?|"+ua+")","i"),function(e){if(e&&e[2]){var t=[e[2],e[3],e[4]||"0"];return[ua,t.join(".")]}return[ua,""]}],[/Mac OS X (\d+)[_.](\d+)[_.]?(\d+)?/i,function(e){var t=["Mac OS X",""];if(e&&e[1]){var n=[e[1],e[2],e[3]||"0"];t[1]=n.join(".")}return t}],[/Mac/i,["Mac OS X",""]],[/CrOS/,[ba,""]],[/Linux|debian/i,["Linux",""]]],tu=function(e){return Ga.test(e)?Fa:Wa.test(e)?Ra:za.test(e)?Ma:new RegExp(Da,"i").test(e)?Da:new RegExp("("+qa+"|WPDesktop)","i").test(e)?qa:/iPad/.test(e)?da:/iPod/.test(e)?"iPod Touch":/iPhone/.test(e)?"iPhone":/(watch)(?: ?os[,/]|\d,\d\/)[\d.]+/i.test(e)?pa:Za.test(e)?ha:/(kobo)\s(ereader|touch)/i.test(e)?"Kobo":new RegExp($a,"i").test(e)?$a:/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i.test(e)||/(kf[a-z]+)( bui|\)).+silk\//i.test(e)?"Kindle Fire":/(Android|ZTE)/i.test(e)?!new RegExp(sa).test(e)||/(9138B|TB782B|Nexus [97]|pixel c|HUAWEISHT|BTV|noble nook|smart ultra 6)/i.test(e)?/pixel[\daxl ]{1,6}/i.test(e)&&!/pixel c/i.test(e)||/(huaweimed-al00|tah-|APA|SM-G92|i980|zte|U304AA)/i.test(e)||/lmy47v/i.test(e)&&!/QTAQZ3/i.test(e)?ua:ca:ua:new RegExp("(pda|"+sa+")","i").test(e)?Ha:new RegExp(la,"i").test(e)&&!new RegExp(la+" pc","i").test(e)?Ba:""},nu=function(e){var t=tu(e);return t===da||t===ca||"Kobo"===t||"Kindle Fire"===t||t===Ba?la:t===Fa||t===Ma||t===Ra||t===Da?"Console":t===pa?"Wearable":t?sa:"Desktop"},ru=["localhost","127.0.0.1"],iu=function(e){var t=null==Qo?void 0:Qo.createElement("a");return ys(t)?null:(t.href=e,t)},ou=function(e,t){var n,r;void 0===t&&(t="&");var i=[];return Os(e,(function(e,t){ys(e)||ys(t)||"undefined"===t||(n=encodeURIComponent(function(e){return e instanceof File}(e)?e.name:e.toString()),r=encodeURIComponent(t),i[i.length]=r+"="+n)})),i.join(t)},su=function(e,t){for(var n,r=((e.split("#")[0]||"").split(/\?(.*)/)[1]||"").replace(/^\?+/g,"").split("&"),i=0;i<r.length;i++){var o=r[i].split("=");if(o[0]===t){n=o;break}}if(!hs(n)||n.length<2)return"";var s=n[1];try{s=decodeURIComponent(s)}catch(e){Ps.error("Skipping decoding for malformed query param: "+s)}return s.replace(/\+/g," ")},au=function(e,t,n){if(!e||!t||!t.length)return e;for(var r=e.split("#"),i=r[0]||"",o=r[1],s=i.split("?"),a=s[1],u=s[0],l=(a||"").split("&"),c=[],d=0;d<l.length;d++){var f=l[d].split("=");hs(f)&&(t.includes(f[0])?c.push(f[0]+"="+n):c.push(l[d]))}var p=u;return null!=a&&(p+="?"+c.join("&")),null!=o&&(p+="#"+o),p},uu=function(e,t){var n=e.match(new RegExp(t+"=([^&]*)"));return n?n[1]:null},lu=Uint8Array,cu=Uint16Array,du=Uint32Array,fu=new lu([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),pu=new lu([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),_u=new lu([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),hu=function(e,t){for(var n=new cu(31),r=0;r<31;++r)n[r]=t+=1<<e[r-1];var i=new du(n[30]);for(r=1;r<30;++r)for(var o=n[r];o<n[r+1];++o)i[o]=o-n[r]<<5|r;return[n,i]},vu=hu(fu,2),gu=vu[0],mu=vu[1];gu[28]=258,mu[258]=28;for(var yu=hu(pu,0)[1],bu=new cu(32768),wu=0;wu<32768;++wu){var Su=(43690&wu)>>>1|(21845&wu)<<1;Su=(61680&(Su=(52428&Su)>>>2|(13107&Su)<<2))>>>4|(3855&Su)<<4,bu[wu]=((65280&Su)>>>8|(255&Su)<<8)>>>1}var ku=function(e,t,n){for(var r=e.length,i=0,o=new cu(t);i<r;++i)++o[e[i]-1];var s,a=new cu(t);for(i=0;i<t;++i)a[i]=a[i-1]+o[i-1]<<1;if(n){s=new cu(1<<t);var u=15-t;for(i=0;i<r;++i)if(e[i])for(var l=i<<4|e[i],c=t-e[i],d=a[e[i]-1]++<<c,f=d|(1<<c)-1;d<=f;++d)s[bu[d]>>>u]=l}else for(s=new cu(r),i=0;i<r;++i)s[i]=bu[a[e[i]-1]++]>>>15-e[i];return s},xu=new lu(288);for(wu=0;wu<144;++wu)xu[wu]=8;for(wu=144;wu<256;++wu)xu[wu]=9;for(wu=256;wu<280;++wu)xu[wu]=7;for(wu=280;wu<288;++wu)xu[wu]=8;var Eu=new lu(32);for(wu=0;wu<32;++wu)Eu[wu]=5;var Tu=ku(xu,9,0),Cu=ku(Eu,5,0),Iu=function(e){return(e/8>>0)+(7&e&&1)},Pu=function(e,t,n){(null==n||n>e.length)&&(n=e.length);var r=new(e instanceof cu?cu:e instanceof du?du:lu)(n-t);return r.set(e.subarray(t,n)),r},Fu=function(e,t,n){n<<=7&t;var r=t/8>>0;e[r]|=n,e[r+1]|=n>>>8},Ru=function(e,t,n){n<<=7&t;var r=t/8>>0;e[r]|=n,e[r+1]|=n>>>8,e[r+2]|=n>>>16},Mu=function(e,t){for(var n=[],r=0;r<e.length;++r)e[r]&&n.push({s:r,f:e[r]});var i=n.length,o=n.slice();if(!i)return[new lu(0),0];if(1==i){var s=new lu(n[0].s+1);return s[n[0].s]=1,[s,1]}n.sort((function(e,t){return e.f-t.f})),n.push({s:-1,f:25001});var a=n[0],u=n[1],l=0,c=1,d=2;for(n[0]={s:-1,f:a.f+u.f,l:a,r:u};c!=i-1;)a=n[n[l].f<n[d].f?l++:d++],u=n[l!=c&&n[l].f<n[d].f?l++:d++],n[c++]={s:-1,f:a.f+u.f,l:a,r:u};var f=o[0].s;for(r=1;r<i;++r)o[r].s>f&&(f=o[r].s);var p=new cu(f+1),_=Ou(n[c-1],p,0);if(_>t){r=0;var h=0,v=_-t,g=1<<v;for(o.sort((function(e,t){return p[t.s]-p[e.s]||e.f-t.f}));r<i;++r){var m=o[r].s;if(!(p[m]>t))break;h+=g-(1<<_-p[m]),p[m]=t}for(h>>>=v;h>0;){var y=o[r].s;p[y]<t?h-=1<<t-p[y]++-1:++r}for(;r>=0&&h;--r){var b=o[r].s;p[b]==t&&(--p[b],++h)}_=t}return[new lu(p),_]},Ou=function(e,t,n){return-1==e.s?Math.max(Ou(e.l,t,n+1),Ou(e.r,t,n+1)):t[e.s]=n},Lu=function(e){for(var t=e.length;t&&!e[--t];);for(var n=new cu(++t),r=0,i=e[0],o=1,s=function(e){n[r++]=e},a=1;a<=t;++a)if(e[a]==i&&a!=t)++o;else{if(!i&&o>2){for(;o>138;o-=138)s(32754);o>2&&(s(o>10?o-11<<5|28690:o-3<<5|12305),o=0)}else if(o>3){for(s(i),--o;o>6;o-=6)s(8304);o>2&&(s(o-3<<5|8208),o=0)}for(;o--;)s(i);o=1,i=e[a]}return[n.subarray(0,r),t]},Au=function(e,t){for(var n=0,r=0;r<t.length;++r)n+=e[r]*t[r];return n},qu=function(e,t,n){var r=n.length,i=Iu(t+2);e[i]=255&r,e[i+1]=r>>>8,e[i+2]=255^e[i],e[i+3]=255^e[i+1];for(var o=0;o<r;++o)e[i+o+4]=n[o];return 8*(i+4+r)},$u=function(e,t,n,r,i,o,s,a,u,l,c){Fu(t,c++,n),++i[256];for(var d=Mu(i,15),f=d[0],p=d[1],_=Mu(o,15),h=_[0],v=_[1],g=Lu(f),m=g[0],y=g[1],b=Lu(h),w=b[0],S=b[1],k=new cu(19),x=0;x<m.length;++x)k[31&m[x]]++;for(x=0;x<w.length;++x)k[31&w[x]]++;for(var E=Mu(k,7),T=E[0],C=E[1],I=19;I>4&&!T[_u[I-1]];--I);var P,F,R,M,O=l+5<<3,L=Au(i,xu)+Au(o,Eu)+s,A=Au(i,f)+Au(o,h)+s+14+3*I+Au(k,T)+(2*k[16]+3*k[17]+7*k[18]);if(O<=L&&O<=A)return qu(t,c,e.subarray(u,u+l));if(Fu(t,c,1+(A<L)),c+=2,A<L){P=ku(f,p,0),F=f,R=ku(h,v,0),M=h;var q=ku(T,C,0);Fu(t,c,y-257),Fu(t,c+5,S-1),Fu(t,c+10,I-4),c+=14;for(x=0;x<I;++x)Fu(t,c+3*x,T[_u[x]]);c+=3*I;for(var $=[m,w],D=0;D<2;++D){var N=$[D];for(x=0;x<N.length;++x){var H=31&N[x];Fu(t,c,q[H]),c+=T[H],H>15&&(Fu(t,c,N[x]>>>5&127),c+=N[x]>>>12)}}}else P=Tu,F=xu,R=Cu,M=Eu;for(x=0;x<a;++x)if(r[x]>255){H=r[x]>>>18&31;Ru(t,c,P[H+257]),c+=F[H+257],H>7&&(Fu(t,c,r[x]>>>23&31),c+=fu[H]);var B=31&r[x];Ru(t,c,R[B]),c+=M[B],B>3&&(Ru(t,c,r[x]>>>5&8191),c+=pu[B])}else Ru(t,c,P[r[x]]),c+=F[r[x]];return Ru(t,c,P[256]),c+F[256]},Du=new du([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),Nu=function(){for(var e=new du(256),t=0;t<256;++t){for(var n=t,r=9;--r;)n=(1&n&&3988292384)^n>>>1;e[t]=n}return e}(),Hu=function(){var e=4294967295;return{p:function(t){for(var n=e,r=0;r<t.length;++r)n=Nu[255&n^t[r]]^n>>>8;e=n},d:function(){return 4294967295^e}}},Bu=function(e,t,n,r,i){return function(e,t,n,r,i,o){var s=e.length,a=new lu(r+s+5*(1+Math.floor(s/7e3))+i),u=a.subarray(r,a.length-i),l=0;if(!t||s<8)for(var c=0;c<=s;c+=65535){var d=c+65535;d<s?l=qu(u,l,e.subarray(c,d)):(u[c]=o,l=qu(u,l,e.subarray(c,s)))}else{for(var f=Du[t-1],p=f>>>13,_=8191&f,h=(1<<n)-1,v=new cu(32768),g=new cu(h+1),m=Math.ceil(n/3),y=2*m,b=function(t){return(e[t]^e[t+1]<<m^e[t+2]<<y)&h},w=new du(25e3),S=new cu(288),k=new cu(32),x=0,E=0,T=(c=0,0),C=0,I=0;c<s;++c){var P=b(c),F=32767&c,R=g[P];if(v[F]=R,g[P]=F,C<=c){var M=s-c;if((x>7e3||T>24576)&&M>423){l=$u(e,u,0,w,S,k,E,T,I,c-I,l),T=x=E=0,I=c;for(var O=0;O<286;++O)S[O]=0;for(O=0;O<30;++O)k[O]=0}var L=2,A=0,q=_,$=F-R&32767;if(M>2&&P==b(c-$))for(var D=Math.min(p,M)-1,N=Math.min(32767,c),H=Math.min(258,M);$<=N&&--q&&F!=R;){if(e[c+L]==e[c+L-$]){for(var B=0;B<H&&e[c+B]==e[c+B-$];++B);if(B>L){if(L=B,A=$,B>D)break;var j=Math.min($,B-2),U=0;for(O=0;O<j;++O){var V=c-$+O+32768&32767,z=V-v[V]+32768&32767;z>U&&(U=z,R=V)}}}$+=(F=R)-(R=v[F])+32768&32767}if(A){w[T++]=268435456|mu[L]<<18|yu[A];var W=31&mu[L],G=31&yu[A];E+=fu[W]+pu[G],++S[257+W],++k[G],C=c+L,++x}else w[T++]=e[c],++S[e[c]]}}l=$u(e,u,o,w,S,k,E,T,I,c-I,l)}return Pu(a,0,r+Iu(l)+i)}(e,null==t.level?6:t.level,null==t.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(e.length)))):12+t.mem,n,r,!i)},ju=function(e,t,n){for(;n;++t)e[t]=n,n>>>=8},Uu=function(e,t){var n=t.filename;if(e[0]=31,e[1]=139,e[2]=8,e[8]=t.level<2?4:9==t.level?2:0,e[9]=3,0!=t.mtime&&ju(e,4,Math.floor(new Date(t.mtime||Date.now())/1e3)),n){e[3]=8;for(var r=0;r<=n.length;++r)e[r+10]=n.charCodeAt(r)}},Vu=function(e){return 10+(e.filename&&e.filename.length+1||0)};function zu(e,t){void 0===t&&(t={});var n=Hu(),r=e.length;n.p(e);var i=Bu(e,t,Vu(t),8),o=i.length;return Uu(i,t),ju(i,o-8,n.d()),ju(i,o-4,r),i}function Wu(e,t){var n=e.length;if("undefined"!=typeof TextEncoder)return(new TextEncoder).encode(e);for(var r=new lu(e.length+(e.length>>>1)),i=0,o=function(e){r[i++]=e},s=0;s<n;++s){if(i+5>r.length){var a=new lu(i+8+(n-s<<1));a.set(r),r=a}var u=e.charCodeAt(s);u<128||t?o(u):u<2048?(o(192|u>>>6),o(128|63&u)):u>55295&&u<57344?(o(240|(u=65536+(1047552&u)|1023&e.charCodeAt(++s))>>>18),o(128|u>>>12&63),o(128|u>>>6&63),o(128|63&u)):(o(224|u>>>12),o(128|u>>>6&63),o(128|63&u))}return Pu(r,0,i)}var Gu=function(e){var t,n,r,i,o="";for(t=n=0,r=(e=(e+"").replace(/\r\n/g,"\n").replace(/\r/g,"\n")).length,i=0;i<r;i++){var s=e.charCodeAt(i),a=null;s<128?n++:a=s>127&&s<2048?String.fromCharCode(s>>6|192,63&s|128):String.fromCharCode(s>>12|224,s>>6&63|128,63&s|128),Ss(a)||(n>t&&(o+=e.substring(t,n)),o+=a,t=n=i+1)}return n>t&&(o+=e.substring(t,e.length)),o},Zu=!!Ko||!!Xo,Yu="text/plain",Qu=function(e,t){var n=e.split("?"),r=n[0],i=n[1],o=ki({},t);null==i||i.split("&").forEach((function(e){var t=e.split("=")[0];delete o[t]}));var s=ou(o);return r+"?"+(s=s?(i?i+"&":"")+s:i)},Ju=function(e,t){return JSON.stringify(e,(function(e,t){return"bigint"==typeof t?t.toString():t}),t)},Xu=function(e){var t=e.data,n=e.compression;if(t){if(n===ss.GZipJS){var r=zu(Wu(Ju(t)),{mtime:0}),i=new Blob([r],{type:Yu});return{contentType:Yu,body:i,estimatedSize:i.size}}if(n===ss.Base64){var o=function(e){var t,n,r,i,o,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",a=0,u=0,l="",c=[];if(!e)return e;e=Gu(e);do{t=(o=e.charCodeAt(a++)<<16|e.charCodeAt(a++)<<8|e.charCodeAt(a++))>>18&63,n=o>>12&63,r=o>>6&63,i=63&o,c[u++]=s.charAt(t)+s.charAt(n)+s.charAt(r)+s.charAt(i)}while(a<e.length);switch(l=c.join(""),e.length%3){case 1:l=l.slice(0,-2)+"==";break;case 2:l=l.slice(0,-1)+"="}return l}(Ju(t)),s=function(e){return"data="+encodeURIComponent("string"==typeof e?e:Ju(e))}(o);return{contentType:"application/x-www-form-urlencoded",body:s,estimatedSize:new Blob([s]).size}}var a=Ju(t);return{contentType:"application/json",body:a,estimatedSize:new Blob([a]).size}}},Ku=[];Xo&&Ku.push({transport:"fetch",method:function(e){var t,n,r=null!==(t=Xu(e))&&void 0!==t?t:{},i=r.contentType,o=r.body,s=r.estimatedSize,a=new Headers;Os(e.headers,(function(e,t){a.append(t,e)})),i&&a.append("Content-Type",i);var u=e.url,l=null;if(es){var c=new es;l={signal:c.signal,timeout:setTimeout((function(){return c.abort()}),e.timeout)}}Xo(u,ki({method:(null==e?void 0:e.method)||"GET",headers:a,keepalive:"POST"===e.method&&(s||0)<52428.8,body:o,signal:null==(n=l)?void 0:n.signal},e.fetchOptions)).then((function(t){return t.text().then((function(n){var r={statusCode:t.status,text:n};if(200===t.status)try{r.json=JSON.parse(n)}catch(e){Ps.error(e)}null==e.callback||e.callback(r)}))})).catch((function(t){Ps.error(t),null==e.callback||e.callback({statusCode:0,text:t})})).finally((function(){return l?clearTimeout(l.timeout):null}))}}),Ko&&Ku.push({transport:"XHR",method:function(e){var t,n=new Ko;n.open(e.method||"GET",e.url,!0);var r=null!==(t=Xu(e))&&void 0!==t?t:{},i=r.contentType,o=r.body;Os(e.headers,(function(e,t){n.setRequestHeader(t,e)})),i&&n.setRequestHeader("Content-Type",i),e.timeout&&(n.timeout=e.timeout),n.withCredentials=!0,n.onreadystatechange=function(){if(4===n.readyState){var t={statusCode:n.status,text:n.responseText};if(200===n.status)try{t.json=JSON.parse(n.responseText)}catch(e){}null==e.callback||e.callback(t)}},n.send(o)}}),null!=Yo&&Yo.sendBeacon&&Ku.push({transport:"sendBeacon",method:function(e){var t=Qu(e.url,{beacon:"1"});try{var n,r=null!==(n=Xu(e))&&void 0!==n?n:{},i=r.contentType,o=r.body,s="string"==typeof o?new Blob([o],{type:i}):o;Yo.sendBeacon(t,s)}catch(e){}}});var el=function(e,t){if(!function(e){try{new RegExp(e)}catch(e){return!1}return!0}(t))return!1;try{return new RegExp(t).test(e)}catch(e){return!1}};function tl(e,t,n){return Ju({distinct_id:e,userPropertiesToSet:t,userPropertiesToSetOnce:n})}var nl={exact:function(e,t){return t.some((function(t){return e.some((function(e){return t===e}))}))},is_not:function(e,t){return t.every((function(t){return e.every((function(e){return t!==e}))}))},regex:function(e,t){return t.some((function(t){return e.some((function(e){return el(t,e)}))}))},not_regex:function(e,t){return t.every((function(t){return e.every((function(e){return!el(t,e)}))}))},icontains:function(e,t){return t.map(rl).some((function(t){return e.map(rl).some((function(e){return t.includes(e)}))}))},not_icontains:function(e,t){return t.map(rl).every((function(t){return e.map(rl).every((function(e){return!t.includes(e)}))}))}},rl=function(e){return e.toLowerCase()},il=Fs("[Stylesheet Loader]"),ol=Vo,sl=Qo;function al(e){return"$survey_response_"+e}var ul="#020617",ll={fontFamily:"inherit",backgroundColor:"#eeeded",submitButtonColor:"black",submitButtonTextColor:"white",ratingButtonColor:"white",ratingButtonActiveColor:"black",borderColor:"#c9c6c6",placeholder:"Start typing...",whiteLabel:!1,displayThankYouMessage:!0,thankYouMessageHeader:"Thank you for your feedback!",position:$o.Right,widgetType:qo.Tab,widgetLabel:"Feedback",widgetColor:"black",zIndex:"2147483647",disabledButtonOpacity:"0.6",maxWidth:"300px",textSubtleColor:"#939393",boxPadding:"20px 24px",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)",borderRadius:"10px",shuffleQuestions:!1,surveyPopupDelaySeconds:void 0,outlineColor:"rgba(59, 130, 246, 0.8)",inputBackground:"white",inputTextColor:ul,scrollbarThumbColor:"var(--ph-survey-border-color)",scrollbarTrackColor:"var(--ph-survey-background-color)"};function cl(e){if("#"===e[0]){var t=e.replace(/^#/,"");return"rgb("+parseInt(t.slice(0,2),16)+","+parseInt(t.slice(2,4),16)+","+parseInt(t.slice(4,6),16)+")"}return"rgb(255, 255, 255)"}function dl(e){var t;void 0===e&&(e=ll.backgroundColor),"#"===e[0]&&(t=cl(e)),e.startsWith("rgb")&&(t=e);var n=function(e){return{aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4","indianred ":"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370d8",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#d87093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"}[e.toLowerCase()]}(e);if(n&&(t=cl(n)),!t)return ul;var r=t.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/);if(r){var i=parseInt(r[1]),o=parseInt(r[2]),s=parseInt(r[3]);return Math.sqrt(i*i*.299+o*o*.587+s*s*.114)>127.5?ul:"white"}return ul}function fl(e){var t=function(e,t,n){var r,i=e.createElement("style");return i.innerText=t,null!=n&&null!=(r=n.config)&&r.prepare_external_dependency_stylesheet&&(i=n.config.prepare_external_dependency_stylesheet(i)),i||(il.error("prepare_external_dependency_stylesheet returned null"),null)}(sl,':host{--ph-survey-font-family:-apple-system,BlinkMacSystemFont,"Inter","Segoe UI","Roboto",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";--ph-survey-box-padding:20px 24px;--ph-survey-max-width:300px;--ph-survey-z-index:2147483647;--ph-survey-border-color:#dcdcdc;--ph-survey-border-bottom:1.5px solid var(--ph-survey-border-color);--ph-survey-border-radius:10px;--ph-survey-background-color:#eeeded;--ph-survey-box-shadow:0 4px 12px rgba(0,0,0,.15);--ph-survey-submit-button-color:#000;--ph-survey-submit-button-text-color:#fff;--ph-survey-rating-bg-color:#fff;--ph-survey-rating-text-color:#020617;--ph-survey-rating-active-bg-color:#000;--ph-survey-rating-active-text-color:#fff;--ph-survey-text-primary-color:#020617;--ph-survey-text-subtle-color:#939393;--ph-widget-color:#e0a045;--ph-widget-text-color:#fff;--ph-survey-scrollbar-thumb-color:var(--ph-survey-border-color);--ph-survey-scrollbar-track-color:var(--ph-survey-background-color);--ph-survey-outline-color:rgba(59,130,246,.8);--ph-survey-input-background:#fff;--ph-survey-input-text-color:#020617;--ph-survey-disabled-button-opacity:0.6}.ph-survey{bottom:0;height:fit-content;margin:0;max-width:85%;min-width:300px;position:fixed;width:var(--ph-survey-max-width);z-index:var(--ph-survey-z-index)}.ph-survey h3,.ph-survey p{margin:0}.ph-survey *{box-sizing:border-box;color:var(--ph-survey-text-primary-color);font-family:var(--ph-survey-font-family)}.ph-survey .multiple-choice-options label,.ph-survey input[type=text],.ph-survey textarea{background:var(--ph-survey-input-background);border:1.5px solid var(--ph-survey-border-color);border-radius:4px;color:var(--ph-survey-input-text-color);padding:10px;transition:border-color .2s ease-out,box-shadow .2s ease-out,transform .15s ease-out}.ph-survey input[type=text],.ph-survey textarea{transition:border-color .2s ease-out,box-shadow .2s ease-out,transform .15s ease-out}.ph-survey input{margin:0}.ph-survey .form-submit:focus,.ph-survey .form-submit:focus-visible,.ph-survey input[type=checkbox]:focus,.ph-survey input[type=checkbox]:focus-visible,.ph-survey input[type=radio]:focus,.ph-survey input[type=radio]:focus-visible,.ph-survey input[type=text]:focus,.ph-survey input[type=text]:focus-visible,.ph-survey textarea:focus,.ph-survey textarea:focus-visible{border-color:var(--ph-survey-rating-active-bg-color);outline:1.5px solid var(--ph-survey-outline-color);outline-offset:2px}.ph-survey button:focus:not(:focus-visible),.ph-survey input[type=checkbox]:focus:not(:focus-visible),.ph-survey input[type=radio]:focus:not(:focus-visible),.ph-survey input[type=text]:focus:not(:focus-visible),.ph-survey textarea:focus:not(:focus-visible){outline:none}.ph-survey input[type=text]:hover:not(:focus),.ph-survey textarea:hover:not(:focus){border-color:var(--ph-survey-rating-active-bg-color)}@media (max-width:768px){.ph-survey input[type=text],.ph-survey textarea{font-size:1rem}}.ph-survey .form-cancel,.ph-survey .multiple-choice-options label,.ph-survey .rating-options-number,.ph-survey input[type=checkbox],.ph-survey input[type=radio]{border:1.5px solid var(--ph-survey-border-color)}.ph-survey .footer-branding,.ph-survey .form-cancel,.ph-survey .form-submit,.ph-survey .ratings-emoji,.ph-survey .ratings-number,.ph-survey input[type=checkbox],.ph-survey input[type=radio],.ph-survey label{transition:all .2s ease-out}@media (prefers-reduced-motion:no-preference){.ph-survey button:active,.ph-survey input[type=checkbox]:active,.ph-survey input[type=radio]:active,.ph-survey label:active{transition-duration:.1s}}.ph-survey-widget-tab{background:var(--ph-widget-color);border:none;border-radius:3px 3px 0 0;color:var(--ph-widget-text-color);cursor:pointer;padding:10px 12px;position:fixed;right:0;text-align:center;top:50%;transform:rotate(-90deg) translateY(-100%);transform-origin:right top;transition:padding-bottom .2s ease-out;z-index:var(--ph-survey-z-index)}.ph-survey-widget-tab:hover{padding-bottom:16px}@keyframes ph-survey-fade-in{0%{opacity:0}to{opacity:1}}.survey-box{gap:16px}.bottom-section,.survey-box{display:flex;flex-direction:column}.bottom-section{gap:8px}.thank-you-message-header~.bottom-section{padding-top:16px}.question-container,.thank-you-message{display:flex;flex-direction:column;gap:8px}.survey-question{font-size:14px;font-weight:500}.survey-question-description{font-size:13px;opacity:.8;padding-top:4px}.question-textarea-wrapper{display:flex;flex-direction:column}.survey-form{animation:ph-survey-fade-in .3s ease-out forwards}.survey-form,.thank-you-message{background:var(--ph-survey-background-color);border:1.5px solid var(--ph-survey-border-color);border-bottom:var(--ph-survey-border-bottom);border-radius:var(--ph-survey-border-radius);box-shadow:var(--ph-survey-box-shadow);margin:0;padding:var(--ph-survey-box-padding);position:relative;text-align:left;width:100%;z-index:var(--ph-survey-z-index)}.survey-form input[type=text],.survey-form textarea{min-width:100%}:is(.survey-form textarea):focus,:is(.survey-form textarea):focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.08)}:is(.survey-form textarea):focus:not(:focus-visible){box-shadow:none}.survey-box:has(.survey-question:empty):not(:has(.survey-question-description)) .multiple-choice-options,.survey-box:has(.survey-question:empty):not(:has(.survey-question-description)) textarea{margin-top:0}.multiple-choice-options{border:none;display:flex;flex-direction:column;font-size:14px;gap:8px;margin:0;padding:1px 0}.multiple-choice-options label{align-items:center;cursor:pointer;display:flex;font-size:13px;gap:8px}:is(.multiple-choice-options label):hover:not(:has(input:checked)){border-color:var(--ph-survey-text-subtle-color);box-shadow:0 2px 8px rgba(0,0,0,.08)}:is(.multiple-choice-options label):has(input:checked){border-color:var(--ph-survey-rating-active-bg-color);box-shadow:0 1px 4px rgba(0,0,0,.05)}.choice-option-open:is(.multiple-choice-options label){flex-wrap:wrap}:is(.multiple-choice-options label) span{color:inherit}.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]{appearance:none;-webkit-appearance:none;-moz-appearance:none;background:var(--ph-survey-input-background);border-radius:3px;cursor:pointer;flex-shrink:0;height:1rem;position:relative;transition:all .2s cubic-bezier(.4,0,.2,1),transform .15s ease-out;width:1rem;z-index:1}:is(.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]):focus{transform:scale(1.05)}:is(.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]):hover{border-color:var(--ph-survey-rating-active-bg-color);transform:scale(1.05)}:is(.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]):active{transform:scale(.95)}:is(.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]):checked{background:var(--ph-survey-rating-active-bg-color);border-color:var(--ph-survey-rating-active-bg-color);transform:scale(1)}:is(.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]):checked:hover{transform:scale(1.05)}.multiple-choice-options input[type=checkbox]:checked:after{animation:ph-survey-checkmark-reveal .2s ease-out .1s forwards;border:solid var(--ph-survey-rating-active-text-color);border-width:0 2px 2px 0;height:8px;left:4px;transform:rotate(45deg) scale(0);width:4px}.multiple-choice-options input[type=radio]:checked:after{animation:ph-survey-radio-reveal .15s ease-out .05s forwards;background:var(--ph-survey-rating-active-text-color);border-radius:50%;height:6px;left:5px;top:5px;transform:scale(0);width:6px}.multiple-choice-options input[type=checkbox]:checked:after,.multiple-choice-options input[type=radio]:checked:after{box-sizing:content-box;content:"";position:absolute}.multiple-choice-options input[type=radio]{border-radius:50%}.multiple-choice-options input[type=radio]:checked{border:none}:is(.multiple-choice-options input[type=checkbox]:checked,.multiple-choice-options input[type=radio]:checked)~*{font-weight:700}:is(:is(.multiple-choice-options .choice-option-open) input[type=text])::placeholder{color:var(--ph-survey-text-subtle-color);font-weight:400}.rating-options-emoji{display:flex;justify-content:space-between}.ratings-emoji{background-color:transparent;border:none;font-size:16px;opacity:.5;padding:0}.ratings-emoji:hover{cursor:pointer;opacity:1;transform:scale(1.15)}.ratings-emoji.rating-active{opacity:1}.ratings-emoji svg{fill:var(--ph-survey-text-primary-color);transition:fill .2s ease-out}.rating-options-number{border-radius:6px;display:grid;grid-auto-columns:1fr;grid-auto-flow:column;overflow:hidden}.rating-options-number .ratings-number{border:none;border-right:1px solid var(--ph-survey-border-color);color:var(--ph-survey-rating-text-color);cursor:pointer;font-weight:700;text-align:center}:is(.rating-options-number .ratings-number):last-of-type{border-right:0}.rating-active:is(.rating-options-number .ratings-number){background:var(--ph-survey-rating-active-bg-color);color:var(--ph-survey-rating-active-text-color)}.ratings-number{background-color:var(--ph-survey-rating-bg-color);border:none;padding:8px 0}.ratings-number .rating-active{background-color:var(--ph-survey-rating-active-bg-color)}.ratings-number:hover{cursor:pointer}.rating-text{display:flex;flex-direction:row;font-size:11px;justify-content:space-between;opacity:.7}.form-submit{background:var(--ph-survey-submit-button-color);border:none;border-radius:6px;box-shadow:0 2px 0 rgba(0,0,0,.045);color:var(--ph-survey-submit-button-text-color);cursor:pointer;font-weight:700;min-width:100%;padding:12px;text-align:center;user-select:none}.form-submit:not([disabled]):hover{box-shadow:0 4px 8px rgba(0,0,0,.1);transform:scale(1.02)}.form-submit:not([disabled]):active{box-shadow:0 1px 2px rgba(0,0,0,.05);transform:scale(.98)}.form-submit[disabled]{cursor:not-allowed;opacity:var(--ph-survey-disabled-button-opacity)}.form-cancel{background:#fff;border-radius:100%;cursor:pointer;line-height:0;padding:12px;position:absolute;right:0;top:0;transform:translate(50%,-50%)}.form-cancel:hover{opacity:.7;transform:translate(50%,-50%) scale(1.1)}.footer-branding{align-items:center;display:flex;font-size:11px;font-weight:500;gap:4px;justify-content:center;opacity:.6;text-decoration:none}.footer-branding:hover{opacity:1}.footer-branding a{text-decoration:none}.thank-you-message{text-align:center}.thank-you-message-header{margin:10px 0 0}.thank-you-message-body{font-size:14px;opacity:.8}.limit-height{max-height:256px;overflow-x:hidden;overflow-y:auto;scrollbar-color:var(--ph-survey-scrollbar-thumb-color) var(--ph-survey-scrollbar-track-color);scrollbar-width:thin}.limit-height::-webkit-scrollbar{width:8px}.limit-height::-webkit-scrollbar-track{background:var(--ph-survey-scrollbar-track-color);border-radius:4px}.limit-height::-webkit-scrollbar-thumb{background-color:var(--ph-survey-scrollbar-thumb-color);border:2px solid var(--ph-survey-scrollbar-track-color);border-radius:4px}:is(.limit-height::-webkit-scrollbar-thumb):hover{background-color:var(--ph-survey-text-subtle-color)}.sr-only{clip:rect(0,0,0,0);border:0;height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;white-space:nowrap;width:1px}@media (prefers-reduced-motion:reduce){.ph-survey *{animation-duration:.01ms!important;animation-iteration-count:1!important;scroll-behavior:auto!important;transition-duration:.01ms!important}}@keyframes ph-survey-checkmark-reveal{0%{opacity:0;transform:rotate(45deg) scale(0)}50%{opacity:1;transform:rotate(45deg) scale(1.2)}to{opacity:1;transform:rotate(45deg) scale(1)}}@keyframes ph-survey-radio-reveal{0%{opacity:0;transform:scale(0)}50%{opacity:1;transform:scale(1.3)}to{opacity:1;transform:scale(1)}}',e);return null==t||t.setAttribute("data-ph-survey-style","true"),t}var pl=function(e,t,n){var r=Ml(e),i=sl.querySelector("."+r);if(i&&i.shadowRoot)return{shadow:i.shadowRoot,isNewlyCreated:!1};var o=sl.createElement("div");!function(e,t,n){var r=ki({},ll,n),i=e.style,o=![$o.Center,$o.Left,$o.Right].includes(r.position)||t===Do.Widget&&(null==n?void 0:n.widgetType)===qo.Tab;i.setProperty("--ph-survey-font-family",function(e){if("inherit"===e)return"inherit";var t='BlinkMacSystemFont, "Inter", "Segoe UI", "Roboto", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"';return e?e+", "+t:"-apple-system, "+t}(r.fontFamily)),i.setProperty("--ph-survey-box-padding",r.boxPadding),i.setProperty("--ph-survey-max-width",r.maxWidth),i.setProperty("--ph-survey-z-index",r.zIndex),i.setProperty("--ph-survey-border-color",r.borderColor),o?(i.setProperty("--ph-survey-border-radius",r.borderRadius),i.setProperty("--ph-survey-border-bottom","1.5px solid var(--ph-survey-border-color)")):(i.setProperty("--ph-survey-border-bottom","none"),i.setProperty("--ph-survey-border-radius",r.borderRadius+" "+r.borderRadius+" 0 0")),i.setProperty("--ph-survey-background-color",r.backgroundColor),i.setProperty("--ph-survey-box-shadow",r.boxShadow),i.setProperty("--ph-survey-disabled-button-opacity",r.disabledButtonOpacity),i.setProperty("--ph-survey-submit-button-color",r.submitButtonColor),i.setProperty("--ph-survey-submit-button-text-color",(null==n?void 0:n.submitButtonTextColor)||dl(r.submitButtonColor)),i.setProperty("--ph-survey-rating-bg-color",r.ratingButtonColor),i.setProperty("--ph-survey-rating-text-color",dl(r.ratingButtonColor)),i.setProperty("--ph-survey-rating-active-bg-color",r.ratingButtonActiveColor),i.setProperty("--ph-survey-rating-active-text-color",dl(r.ratingButtonActiveColor)),i.setProperty("--ph-survey-text-primary-color",dl(r.backgroundColor)),i.setProperty("--ph-survey-text-subtle-color",r.textSubtleColor),i.setProperty("--ph-widget-color",r.widgetColor),i.setProperty("--ph-widget-text-color",dl(r.widgetColor)),i.setProperty("--ph-widget-z-index",r.zIndex),"white"===r.backgroundColor&&i.setProperty("--ph-survey-input-background","#f8f8f8"),i.setProperty("--ph-survey-input-background",r.inputBackground),i.setProperty("--ph-survey-input-text-color",dl(r.inputBackground)),i.setProperty("--ph-survey-scrollbar-thumb-color",r.scrollbarThumbColor),i.setProperty("--ph-survey-scrollbar-track-color",r.scrollbarTrackColor),i.setProperty("--ph-survey-outline-color",r.outlineColor)}(o,e.type,e.appearance),o.className=r;var s=o.attachShadow({mode:"open"}),a=fl(t);if(a){var u=s.querySelector("style");u&&s.removeChild(u),s.appendChild(a)}return sl.body.appendChild(o),{shadow:s,isNewlyCreated:!0}},_l=function(e,t){if(!t)return null;var n=e[al(t)];return hs(n)?[].concat(n):n},hl=function(e){var t,n=e.responses,r=e.survey,i=e.surveySubmissionId,o=e.posthog,s=e.isSurveyCompleted;o?(o.capture(jo.SENT,ki(((t={})[Uo.SURVEY_NAME]=r.name,t[Uo.SURVEY_ID]=r.id,t[Uo.SURVEY_ITERATION]=r.current_iteration,t[Uo.SURVEY_ITERATION_START_DATE]=r.current_iteration_start_date,t[Uo.SURVEY_QUESTIONS]=r.questions.map((function(e){return{id:e.id,question:e.question,response:_l(n,e.id)}})),t[Uo.SURVEY_SUBMISSION_ID]=i,t[Uo.SURVEY_COMPLETED]=s,t.sessionRecordingUrl=null==o.get_session_replay_url?void 0:o.get_session_replay_url(),t),n)),s&&(ol.dispatchEvent(new CustomEvent("PHSurveySent",{detail:{surveyId:r.id}})),Rl(r))):zs.error("[survey sent] event not captured, PostHog instance not found.")},vl=function(e,t,n){var r,i;if(t){if(!n){var o=Pl(e);t.capture(jo.DISMISSED,ki(((r={})[Uo.SURVEY_NAME]=e.name,r[Uo.SURVEY_ID]=e.id,r[Uo.SURVEY_ITERATION]=e.current_iteration,r[Uo.SURVEY_ITERATION_START_DATE]=e.current_iteration_start_date,r[Uo.SURVEY_PARTIALLY_COMPLETED]=Object.values((null==o?void 0:o.responses)||{}).filter((function(e){return!ks(e)})).length>0,r.sessionRecordingUrl=null==t.get_session_replay_url?void 0:t.get_session_replay_url(),r),null==o?void 0:o.responses,((i={})[Uo.SURVEY_SUBMISSION_ID]=null==o?void 0:o.surveySubmissionId,i[Uo.SURVEY_QUESTIONS]=e.questions.map((function(e){return{id:e.id,question:e.question,response:_l((null==o?void 0:o.responses)||{},e.id)}})),i))),Rl(e),localStorage.setItem(wl(e),"true"),ol.dispatchEvent(new CustomEvent("PHSurveyClosed",{detail:{surveyId:e.id}}))}}else zs.error("[survey dismissed] event not captured, PostHog instance not found.")},gl=function(e){return e.map((function(e){return{sort:Math.floor(10*Math.random()),value:e}})).sort((function(e,t){return e.sort-t.sort})).map((function(e){return e.value}))},ml=function(e,t){return e.length===t.length&&e.every((function(e,n){return e===t[n]}))?t.reverse():t},yl=function(e){return e.appearance&&e.appearance.shuffleQuestions&&!e.enable_partial_responses?ml(e.questions,gl(e.questions)):e.questions},bl=function(e){var t;return!(null==(t=e.conditions)||null==(t=t.events)||!t.repeatedActivation||!function(e){var t,n;return null!=(null==(t=e.conditions)||null==(t=t.events)||null==(t=t.values)?void 0:t.length)&&(null==(n=e.conditions)||null==(n=n.events)||null==(n=n.values)?void 0:n.length)>0}(e))||e.schedule===Bo.Always||Fl(e)},wl=function(e){var t=""+Ys+e.id;return e.current_iteration&&e.current_iteration>0&&(t=""+Ys+e.id+"_"+e.current_iteration),t},Sl=function(e,t){var n={__c:t="__cC"+Li++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var n,r;return this.getChildContext||(n=[],(r={})[t]=this,this.getChildContext=function(){return r},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&n.some((function(e){e.__e=!0,Gi(e)}))},this.sub=function(e){n.push(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){n.splice(n.indexOf(e),1),t&&t.call(e)}}),e.children}};return n.Provider.__=n.Consumer.contextType=n}({isPreviewMode:!1,previewPageIndex:0,onPopupSurveyDismissed:function(){},isPopup:!0,onPreviewSubmit:function(){},surveySubmissionId:""}),kl=function(){return Io(Sl)},xl=function(e){var t=e.component,n=e.children,r=e.renderAsHtml,i=e.style;return lo(t,r?{dangerouslySetInnerHTML:{__html:n},style:i}:{children:n,style:i})};function El(e){return null!=e?e:"icontains"}function Tl(e){var t,n,r;if(null==(t=e.conditions)||!t.url)return!0;var i=null==ol||null==(n=ol.location)?void 0:n.href;if(!i)return!1;var o=[e.conditions.url],s=El(null==(r=e.conditions)?void 0:r.urlMatchType);return nl[s](o,[i])}var Cl=function(e){var t=""+Qs+e.id;return e.current_iteration&&e.current_iteration>0&&(t=""+Qs+e.id+"_"+e.current_iteration),t},Il=function(e,t){try{localStorage.setItem(Cl(e),JSON.stringify(t))}catch(e){zs.error("Error setting in-progress survey state in localStorage",e)}},Pl=function(e){try{var t=localStorage.getItem(Cl(e));if(t)return JSON.parse(t)}catch(e){zs.error("Error getting in-progress survey state from localStorage",e)}return null},Fl=function(e){var t=Pl(e);return!ks(null==t?void 0:t.surveySubmissionId)},Rl=function(e){try{localStorage.removeItem(Cl(e))}catch(e){zs.error("Error clearing in-progress survey state from localStorage",e)}};function Ml(e,t){void 0===t&&(t=!1);var n="PostHogSurvey-"+e.id;return t?"."+n:n}var Ol=0;function Ll(e,t,n,r,i,o){var s,a,u={};for(a in t)"ref"==a?s=t[a]:u[a]=t[a];var l={type:e,props:u,key:n,ref:s,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--Ol,__i:-1,__u:0,__source:i,__self:o};if("function"==typeof e&&(s=e.defaultProps))for(a in s)void 0===u[a]&&(u[a]=s[a]);return Ii.vnode&&Ii.vnode(l),l}var Al=Ll("svg",{className:"emoji-svg",xmlns:"http://www.w3.org/2000/svg",height:"48",viewBox:"0 -960 960 960",width:"48",children:Ll("path",{d:"M626-533q22.5 0 38.25-15.75T680-587q0-22.5-15.75-38.25T626-641q-22.5 0-38.25 15.75T572-587q0 22.5 15.75 38.25T626-533Zm-292 0q22.5 0 38.25-15.75T388-587q0-22.5-15.75-38.25T334-641q-22.5 0-38.25 15.75T280-587q0 22.5 15.75 38.25T334-533Zm146 272q66 0 121.5-35.5T682-393h-52q-23 40-63 61.5T480.5-310q-46.5 0-87-21T331-393h-53q26 61 81 96.5T480-261Zm0 181q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 340q142.375 0 241.188-98.812Q820-337.625 820-480t-98.812-241.188Q622.375-820 480-820t-241.188 98.812Q140-622.375 140-480t98.812 241.188Q337.625-140 480-140Z"})}),ql=Ll("svg",{className:"emoji-svg",xmlns:"http://www.w3.org/2000/svg",height:"48",viewBox:"0 -960 960 960",width:"48",children:Ll("path",{d:"M626-533q22.5 0 38.25-15.75T680-587q0-22.5-15.75-38.25T626-641q-22.5 0-38.25 15.75T572-587q0 22.5 15.75 38.25T626-533Zm-292 0q22.5 0 38.25-15.75T388-587q0-22.5-15.75-38.25T334-641q-22.5 0-38.25 15.75T280-587q0 22.5 15.75 38.25T334-533Zm20 194h253v-49H354v49ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 340q142.375 0 241.188-98.812Q820-337.625 820-480t-98.812-241.188Q622.375-820 480-820t-241.188 98.812Q140-622.375 140-480t98.812 241.188Q337.625-140 480-140Z"})}),$l=Ll("svg",{className:"emoji-svg",xmlns:"http://www.w3.org/2000/svg",height:"48",viewBox:"0 -960 960 960",width:"48",children:Ll("path",{d:"M626-533q22.5 0 38.25-15.75T680-587q0-22.5-15.75-38.25T626-641q-22.5 0-38.25 15.75T572-587q0 22.5 15.75 38.25T626-533Zm-292 0q22.5 0 38.25-15.75T388-587q0-22.5-15.75-38.25T334-641q-22.5 0-38.25 15.75T280-587q0 22.5 15.75 38.25T334-533Zm146.174 116Q413-417 358.5-379.5T278-280h53q22-42 62.173-65t87.5-23Q528-368 567.5-344.5T630-280h52q-25-63-79.826-100-54.826-37-122-37ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 340q142.375 0 241.188-98.812Q820-337.625 820-480t-98.812-241.188Q622.375-820 480-820t-241.188 98.812Q140-622.375 140-480t98.812 241.188Q337.625-140 480-140Z"})}),Dl=Ll("svg",{className:"emoji-svg",xmlns:"http://www.w3.org/2000/svg",height:"48",viewBox:"0 -960 960 960",width:"48",children:Ll("path",{d:"M480-417q-67 0-121.5 37.5T278-280h404q-25-63-80-100t-122-37Zm-183-72 50-45 45 45 31-36-45-45 45-45-31-36-45 45-50-45-31 36 45 45-45 45 31 36Zm272 0 44-45 51 45 31-36-45-45 45-45-31-36-51 45-44-45-31 36 44 45-44 45 31 36ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 340q142 0 241-99t99-241q0-142-99-241t-241-99q-142 0-241 99t-99 241q0 142 99 241t241 99Z"})}),Nl=Ll("svg",{className:"emoji-svg",xmlns:"http://www.w3.org/2000/svg",height:"48",viewBox:"0 -960 960 960",width:"48",children:Ll("path",{d:"M479.504-261Q537-261 585.5-287q48.5-26 78.5-72.4 6-11.6-.75-22.6-6.75-11-20.25-11H316.918Q303-393 296.5-382t-.5 22.6q30 46.4 78.5 72.4 48.5 26 105.004 26ZM347-578l27 27q7.636 8 17.818 8Q402-543 410-551q8-8 8-18t-8-18l-42-42q-8.8-9-20.9-9-12.1 0-21.1 9l-42 42q-8 7.636-8 17.818Q276-559 284-551q8 8 18 8t18-8l27-27Zm267 0 27 27q7.714 8 18 8t18-8q8-7.636 8-17.818Q685-579 677-587l-42-42q-8.8-9-20.9-9-12.1 0-21.1 9l-42 42q-8 7.714-8 18t8 18q7.636 8 17.818 8Q579-543 587-551l27-27ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 340q142.375 0 241.188-98.812Q820-337.625 820-480t-98.812-241.188Q622.375-820 480-820t-241.188 98.812Q140-622.375 140-480t98.812 241.188Q337.625-140 480-140Z"})}),Hl=Ll("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-labelledby":"close-survey-title",children:[Ll("title",{id:"close-survey-title",children:"Close survey"}),Ll("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M0.164752 0.164752C0.384422 -0.0549175 0.740578 -0.0549175 0.960248 0.164752L6 5.20451L11.0398 0.164752C11.2594 -0.0549175 11.6156 -0.0549175 11.8352 0.164752C12.0549 0.384422 12.0549 0.740578 11.8352 0.960248L6.79549 6L11.8352 11.0398C12.0549 11.2594 12.0549 11.6156 11.8352 11.8352C11.6156 12.0549 11.2594 12.0549 11.0398 11.8352L6 6.79549L0.960248 11.8352C0.740578 12.0549 0.384422 12.0549 0.164752 11.8352C-0.0549175 11.6156 -0.0549175 11.2594 0.164752 11.0398L5.20451 6L0.164752 0.960248C-0.0549175 0.740578 -0.0549175 0.384422 0.164752 0.164752Z",fill:"black"})]}),Bl=Ll("svg",{width:"77",height:"14",viewBox:"0 0 77 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[Ll("g",{"clip-path":"url(#clip0_2415_6911)",children:[Ll("mask",{id:"mask0_2415_6911",style:{maskType:"luminance"},maskUnits:"userSpaceOnUse",x:"0",y:"0",width:"77",height:"14",children:Ll("path",{d:"M0.5 0H76.5V14H0.5V0Z",fill:"white"})}),Ll("g",{mask:"url(#mask0_2415_6911)",children:[Ll("path",{d:"M5.77226 8.02931C5.59388 8.37329 5.08474 8.37329 4.90634 8.02931L4.4797 7.20672C4.41155 7.07535 4.41155 6.9207 4.4797 6.78933L4.90634 5.96669C5.08474 5.62276 5.59388 5.62276 5.77226 5.96669L6.19893 6.78933C6.26709 6.9207 6.26709 7.07535 6.19893 7.20672L5.77226 8.02931ZM5.77226 12.6946C5.59388 13.0386 5.08474 13.0386 4.90634 12.6946L4.4797 11.872C4.41155 11.7406 4.41155 11.586 4.4797 11.4546L4.90634 10.632C5.08474 10.288 5.59388 10.288 5.77226 10.632L6.19893 11.4546C6.26709 11.586 6.26709 11.7406 6.19893 11.872L5.77226 12.6946Z",fill:"#1D4AFF"}),Ll("path",{d:"M0.5 10.9238C0.5 10.508 1.02142 10.2998 1.32637 10.5938L3.54508 12.7327C3.85003 13.0267 3.63405 13.5294 3.20279 13.5294H0.984076C0.716728 13.5294 0.5 13.3205 0.5 13.0627V10.9238ZM0.5 8.67083C0.5 8.79459 0.551001 8.91331 0.641783 9.00081L5.19753 13.3927C5.28831 13.4802 5.41144 13.5294 5.53982 13.5294H8.0421C8.47337 13.5294 8.68936 13.0267 8.3844 12.7327L1.32637 5.92856C1.02142 5.63456 0.5 5.84278 0.5 6.25854V8.67083ZM0.5 4.00556C0.5 4.12932 0.551001 4.24802 0.641783 4.33554L10.0368 13.3927C10.1276 13.4802 10.2508 13.5294 10.3791 13.5294H12.8814C13.3127 13.5294 13.5287 13.0267 13.2237 12.7327L1.32637 1.26329C1.02142 0.969312 0.5 1.17752 0.5 1.59327V4.00556ZM5.33931 4.00556C5.33931 4.12932 5.39033 4.24802 5.4811 4.33554L14.1916 12.7327C14.4965 13.0267 15.0179 12.8185 15.0179 12.4028V9.99047C15.0179 9.86671 14.9669 9.74799 14.8762 9.66049L6.16568 1.26329C5.86071 0.969307 5.33931 1.17752 5.33931 1.59327V4.00556ZM11.005 1.26329C10.7 0.969307 10.1786 1.17752 10.1786 1.59327V4.00556C10.1786 4.12932 10.2296 4.24802 10.3204 4.33554L14.1916 8.06748C14.4965 8.36148 15.0179 8.15325 15.0179 7.7375V5.3252C15.0179 5.20144 14.9669 5.08272 14.8762 4.99522L11.005 1.26329Z",fill:"#F9BD2B"}),Ll("path",{d:"M21.0852 10.981L16.5288 6.58843C16.2238 6.29443 15.7024 6.50266 15.7024 6.91841V13.0627C15.7024 13.3205 15.9191 13.5294 16.1865 13.5294H23.2446C23.5119 13.5294 23.7287 13.3205 23.7287 13.0627V12.5032C23.7287 12.2455 23.511 12.0396 23.2459 12.0063C22.4323 11.9042 21.6713 11.546 21.0852 10.981ZM18.0252 12.0365C17.5978 12.0365 17.251 11.7021 17.251 11.2901C17.251 10.878 17.5978 10.5436 18.0252 10.5436C18.4527 10.5436 18.7996 10.878 18.7996 11.2901C18.7996 11.7021 18.4527 12.0365 18.0252 12.0365Z",fill:"currentColor"}),Ll("path",{d:"M0.5 13.0627C0.5 13.3205 0.716728 13.5294 0.984076 13.5294H3.20279C3.63405 13.5294 3.85003 13.0267 3.54508 12.7327L1.32637 10.5938C1.02142 10.2998 0.5 10.508 0.5 10.9238V13.0627ZM5.33931 5.13191L1.32637 1.26329C1.02142 0.969306 0.5 1.17752 0.5 1.59327V4.00556C0.5 4.12932 0.551001 4.24802 0.641783 4.33554L5.33931 8.86412V5.13191ZM1.32637 5.92855C1.02142 5.63455 0.5 5.84278 0.5 6.25853V8.67083C0.5 8.79459 0.551001 8.91331 0.641783 9.00081L5.33931 13.5294V9.79717L1.32637 5.92855Z",fill:"#1D4AFF"}),Ll("path",{d:"M10.1787 5.3252C10.1787 5.20144 10.1277 5.08272 10.0369 4.99522L6.16572 1.26329C5.8608 0.969306 5.33936 1.17752 5.33936 1.59327V4.00556C5.33936 4.12932 5.39037 4.24802 5.48114 4.33554L10.1787 8.86412V5.3252ZM5.33936 13.5294H8.04214C8.47341 13.5294 8.6894 13.0267 8.38443 12.7327L5.33936 9.79717V13.5294ZM5.33936 5.13191V8.67083C5.33936 8.79459 5.39037 8.91331 5.48114 9.00081L10.1787 13.5294V9.99047C10.1787 9.86671 10.1277 9.74803 10.0369 9.66049L5.33936 5.13191Z",fill:"#F54E00"}),Ll("path",{d:"M29.375 11.6667H31.3636V8.48772H33.0249C34.8499 8.48772 36.0204 7.4443 36.0204 5.83052C36.0204 4.21681 34.8499 3.17334 33.0249 3.17334H29.375V11.6667ZM31.3636 6.84972V4.81136H32.8236C33.5787 4.81136 34.0318 5.19958 34.0318 5.83052C34.0318 6.4615 33.5787 6.84972 32.8236 6.84972H31.3636ZM39.618 11.7637C41.5563 11.7637 42.9659 10.429 42.9659 8.60905C42.9659 6.78905 41.5563 5.45438 39.618 5.45438C37.6546 5.45438 36.2701 6.78905 36.2701 8.60905C36.2701 10.429 37.6546 11.7637 39.618 11.7637ZM38.1077 8.60905C38.1077 7.63838 38.7118 6.97105 39.618 6.97105C40.5116 6.97105 41.1157 7.63838 41.1157 8.60905C41.1157 9.57972 40.5116 10.2471 39.618 10.2471C38.7118 10.2471 38.1077 9.57972 38.1077 8.60905ZM46.1482 11.7637C47.6333 11.7637 48.6402 10.8658 48.6402 9.81025C48.6402 7.33505 45.2294 8.13585 45.2294 7.16518C45.2294 6.8983 45.5189 6.72843 45.9342 6.72843C46.3622 6.72843 46.8782 6.98318 47.0418 7.54132L48.527 6.94678C48.2375 6.06105 47.1677 5.45438 45.8713 5.45438C44.4743 5.45438 43.6058 6.25518 43.6058 7.21372C43.6058 9.53118 46.9663 8.88812 46.9663 9.84665C46.9663 10.1864 46.6391 10.417 46.1482 10.417C45.4434 10.417 44.9525 9.94376 44.8015 9.3735L43.3164 9.93158C43.6436 10.8537 44.6001 11.7637 46.1482 11.7637ZM53.4241 11.606L53.2982 10.0651C53.0843 10.1743 52.8074 10.2106 52.5808 10.2106C52.1278 10.2106 51.8257 9.89523 51.8257 9.34918V7.03172H53.3612V5.55145H51.8257V3.78001H49.9755V5.55145H48.9687V7.03172H49.9755V9.57972C49.9755 11.06 51.0202 11.7637 52.3921 11.7637C52.7696 11.7637 53.122 11.7031 53.4241 11.606ZM59.8749 3.17334V6.47358H56.376V3.17334H54.3874V11.6667H56.376V8.11158H59.8749V11.6667H61.8761V3.17334H59.8749ZM66.2899 11.7637C68.2281 11.7637 69.6378 10.429 69.6378 8.60905C69.6378 6.78905 68.2281 5.45438 66.2899 5.45438C64.3265 5.45438 62.942 6.78905 62.942 8.60905C62.942 10.429 64.3265 11.7637 66.2899 11.7637ZM64.7796 8.60905C64.7796 7.63838 65.3837 6.97105 66.2899 6.97105C67.1835 6.97105 67.7876 7.63838 67.7876 8.60905C67.7876 9.57972 67.1835 10.2471 66.2899 10.2471C65.3837 10.2471 64.7796 9.57972 64.7796 8.60905ZM73.2088 11.4725C73.901 11.4725 74.5177 11.242 74.845 10.8416V11.424C74.845 12.1034 74.2786 12.5767 73.4102 12.5767C72.7935 12.5767 72.2523 12.2854 72.1642 11.788L70.4776 12.0428C70.7042 13.1955 71.925 13.972 73.4102 13.972C75.361 13.972 76.6574 12.8679 76.6574 11.2298V5.55145H74.8324V6.07318C74.4926 5.69705 73.9136 5.45438 73.171 5.45438C71.409 5.45438 70.3014 6.61918 70.3014 8.46345C70.3014 10.3077 71.409 11.4725 73.2088 11.4725ZM72.1012 8.46345C72.1012 7.55345 72.655 6.97105 73.5109 6.97105C74.3793 6.97105 74.9331 7.55345 74.9331 8.46345C74.9331 9.37345 74.3793 9.95585 73.5109 9.95585C72.655 9.95585 72.1012 9.37345 72.1012 8.46345Z",fill:"currentColor"})]})]}),Ll("defs",{children:Ll("clipPath",{id:"clip0_2415_6911",children:Ll("rect",{width:"76",height:"14",fill:"white",transform:"translate(0.5)"})})})]});function jl(){return Ll("a",{href:"https://posthog.com/surveys",target:"_blank",rel:"noopener",className:"footer-branding",children:["Survey by ",Bl]})}function Ul(e){var t=e.text,n=e.submitDisabled,r=e.appearance,i=e.onSubmit,o=e.link,s=e.onPreviewSubmit,a=e.skipSubmitButton,u=Io(Sl).isPreviewMode;return Ll("div",{className:"bottom-section",children:[!a&&Ll("button",{className:"form-submit",disabled:n,"aria-label":"Submit survey",type:"button",onClick:function(){o&&(null==Vo||Vo.open(o)),u?null==s||s():i()},children:t}),!r.whiteLabel&&Ll(jl,{})]})}function Vl(e){var t=e.question,n=e.forceDisableHtml,r=e.htmlFor;return Ll("div",{class:"question-header",children:[Ll(t.type===No.Open?"label":"h3",{className:"survey-question",htmlFor:r,children:t.question}),t.description&&xl({component:Bi("p",{className:"survey-question-description"}),children:t.description,renderAsHtml:!n&&"text"!==t.descriptionContentType})]})}function zl(e){return Ll("button",{className:"form-cancel",onClick:e.onClick,disabled:Io(Sl).isPreviewMode,"aria-label":"Close survey",type:"button",children:Hl})}Ll("svg",{width:"16",height:"12",viewBox:"0 0 16 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:Ll("path",{d:"M5.30769 10.6923L4.77736 11.2226C4.91801 11.3633 5.10878 11.4423 5.30769 11.4423C5.5066 11.4423 5.69737 11.3633 5.83802 11.2226L5.30769 10.6923ZM15.5303 1.53033C15.8232 1.23744 15.8232 0.762563 15.5303 0.46967C15.2374 0.176777 14.7626 0.176777 14.4697 0.46967L15.5303 1.53033ZM1.53033 5.85429C1.23744 5.56139 0.762563 5.56139 0.46967 5.85429C0.176777 6.14718 0.176777 6.62205 0.46967 6.91495L1.53033 5.85429ZM5.83802 11.2226L15.5303 1.53033L14.4697 0.46967L4.77736 10.162L5.83802 11.2226ZM0.46967 6.91495L4.77736 11.2226L5.83802 10.162L1.53033 5.85429L0.46967 6.91495Z",fill:"currentColor"})});var Wl=Vo;function Gl(e){var t=e.header,n=e.description,r=e.contentType,i=e.forceDisableHtml,o=e.appearance,s=e.onClose,a=Io(Sl).isPopup;return Eo((function(){var e=function(e){"Enter"!==e.key&&"Escape"!==e.key||(e.preventDefault(),s())};return Vs(Wl,"keydown",e),function(){Wl.removeEventListener("keydown",e)}}),[s]),Ll("div",{className:"thank-you-message",role:"status",tabIndex:0,"aria-atomic":"true",children:[a&&Ll(zl,{onClick:function(){return s()}}),Ll("h3",{className:"thank-you-message-header",children:t}),n&&xl({component:Bi("p",{className:"thank-you-message-body"}),children:n,renderAsHtml:!i&&"text"!==r}),a&&Ll(Ul,{text:o.thankYouMessageCloseButtonText||"Close",submitDisabled:!1,appearance:o,onSubmit:function(){return s()}})]})}var Zl=function(e){return hs(e)&&e.every((function(e){return bs(e)}))};function Yl(e){var t=e.question,n=e.forceDisableHtml,r=e.appearance,i=e.onSubmit,o=e.onPreviewSubmit,s=e.displayQuestionIndex,a=e.initialValue,u=kl().isPreviewMode,l=To(null),c=xo((function(){return bs(a)?a:""})),d=c[0],f=c[1];Eo((function(){setTimeout((function(){var e;u||(null==(e=l.current)||e.focus())}),100)}),[u]);var p="surveyQuestion"+s;return Ll(Ui,{children:[Ll("div",{className:"question-container",children:[Ll(Vl,{question:t,forceDisableHtml:n,htmlFor:p}),Ll("textarea",{ref:l,id:p,rows:4,placeholder:null==r?void 0:r.placeholder,onInput:function(e){f(e.currentTarget.value),e.stopPropagation()},onKeyDown:function(e){e.stopPropagation()},value:d})]}),Ll(Ul,{text:t.buttonText||"Submit",submitDisabled:!d&&!t.optional,appearance:r,onSubmit:function(){return i(d)},onPreviewSubmit:function(){return o(d)}})]})}function Ql(e){var t=e.question,n=e.forceDisableHtml,r=e.appearance,i=e.onSubmit,o=e.onPreviewSubmit;return Ll(Ui,{children:[Ll("div",{className:"question-container",children:Ll(Vl,{question:t,forceDisableHtml:n})}),Ll(Ul,{text:t.buttonText||"Submit",submitDisabled:!1,link:t.link,appearance:r,onSubmit:function(){return i("link clicked")},onPreviewSubmit:function(){return o("link clicked")}})]})}function Jl(e){var t=e.question,n=e.forceDisableHtml,r=e.displayQuestionIndex,i=e.appearance,o=e.onSubmit,s=e.onPreviewSubmit,a=e.initialValue,u=t.scale,l=10===t.scale?0:1,c=xo((function(){return xs(a)?a:hs(a)&&a.length>0&&xs(parseInt(a[0]))?parseInt(a[0]):bs(a)&&xs(parseInt(a))?parseInt(a):null})),d=c[0],f=c[1],p=kl().isPreviewMode,_=function(e){return p?s(e):o(e)};return Ll(Ui,{children:[Ll("div",{className:"question-container",children:[Ll(Vl,{question:t,forceDisableHtml:n}),Ll("div",{className:"rating-section",children:[Ll("div",{className:"rating-options",children:["emoji"===t.display&&Ll("div",{className:"rating-options-emoji",children:(3===t.scale?ec:tc).map((function(e,n){return Ll("button",{"aria-label":"Rate "+(n+1),className:"ratings-emoji question-"+r+"-rating-"+n+" "+(n+1===d?"rating-active":""),value:n+1,type:"button",onClick:function(){var e=n+1;f(e),t.skipSubmitButton&&_(e)},children:e},n)}))}),"number"===t.display&&Ll("div",{className:"rating-options-number",style:{gridTemplateColumns:"repeat("+(u-l+1)+", minmax(0, 1fr))"},children:oc(t.scale).map((function(e,n){return Ll(Xl,{displayQuestionIndex:r,active:d===e,appearance:i,num:e,setActiveNumber:function(e){f(e),t.skipSubmitButton&&_(e)}},n)}))})]}),Ll("div",{className:"rating-text",children:[Ll("div",{children:t.lowerBoundLabel}),Ll("div",{children:t.upperBoundLabel})]})]})]}),Ll(Ul,{text:t.buttonText||(null==i?void 0:i.submitButtonText)||"Submit",submitDisabled:Ss(d)&&!t.optional,appearance:i,onSubmit:function(){return o(d)},onPreviewSubmit:function(){return s(d)},skipSubmitButton:t.skipSubmitButton})]})}function Xl(e){var t=e.num,n=e.active,r=e.displayQuestionIndex,i=e.setActiveNumber;return Ll("button",{"aria-label":"Rate "+t,className:"ratings-number question-"+r+"-rating-"+t+" "+(n?"rating-active":""),type:"button",onClick:function(){i(t)},children:t})}function Kl(e){var t=e.question,n=e.forceDisableHtml,r=e.displayQuestionIndex,i=e.appearance,o=e.onSubmit,s=e.onPreviewSubmit,a=e.initialValue,u=To(null),l=Co((function(){return function(e){if(!e.shuffleOptions)return e.choices;var t=e.choices,n="";e.hasOpenChoice&&(n=t.pop());var r=ml(t,gl(t));return e.hasOpenChoice&&(e.choices.push(n),r.push(n)),r}(t)}),[t]),c=xo((function(){return function(e,t){return bs(e)||Zl(e)?e:t===No.SingleChoice?null:[]}(a,t.type)})),d=c[0],f=c[1],p=xo((function(){return function(e,t){if(bs(e)&&!t.includes(e))return{isSelected:!0,inputValue:e};if(Zl(e)){var n=e.find((function(e){return!t.includes(e)}));if(n)return{isSelected:!0,inputValue:n}}return{isSelected:!1,inputValue:""}}(a,l)})),_=p[0],h=p[1],v=kl().isPreviewMode,g=t.type===No.SingleChoice,m=t.type===No.MultipleChoice,y=t.skipSubmitButton&&g&&!t.hasOpenChoice,b=function(e,t){if(t){var n=!_.isSelected;return h((function(e){return ki({},e,{isSelected:n,inputValue:n?e.inputValue:""})})),g&&f(""),void(n&&setTimeout((function(){var e;return null==(e=u.current)?void 0:e.focus()}),75))}if(g)return f(e),h((function(e){return ki({},e,{isSelected:!1,inputValue:""})})),void(y&&(o(e),v&&s(e)));m&&hs(d)&&(d.includes(e)?f(d.filter((function(t){return t!==e}))):f([].concat(d,[e])))},w=function(e){e.stopPropagation();var t=e.currentTarget.value;h((function(e){return ki({},e,{inputValue:t})})),g&&f(t)},S=function(e){e.stopPropagation(),"Enter"!==e.key||k()||(e.preventDefault(),x()),"Escape"===e.key&&(e.preventDefault(),h((function(e){return ki({},e,{isSelected:!1,inputValue:""})})),g&&f(null))},k=function(){return!t.optional&&(!!Ss(d)||(!(!hs(d)||_.isSelected||0!==d.length)||!(!_.isSelected||""!==_.inputValue.trim())))},x=function(){_.isSelected&&m?hs(d)&&(v?s([].concat(d,[_.inputValue])):o([].concat(d,[_.inputValue]))):v?s(d):o(d)};return Ll(Ui,{children:[Ll("div",{className:"question-container",children:[Ll(Vl,{question:t,forceDisableHtml:n}),Ll("fieldset",{className:"multiple-choice-options limit-height",children:[Ll("legend",{className:"sr-only",children:m?" Select all that apply":" Select one"}),l.map((function(e,n){var i=!!t.hasOpenChoice&&n===t.choices.length-1,o="surveyQuestion"+r+"Choice"+n,s=o+"Open",a=i?_.isSelected:g?d===e:hs(d)&&d.includes(e);return Ll("label",{className:i?"choice-option-open":"",children:[Ll("input",{type:g?"radio":"checkbox",name:o,checked:a,onChange:function(){return b(e,i)},id:o,"aria-controls":s}),Ll("span",{children:i?e+":":e}),i&&Ll("input",{type:"text",ref:u,id:s,name:"question"+r+"Open",value:_.inputValue,onKeyDown:S,onInput:w,onClick:function(t){_.isSelected||b(e,!0),t.stopPropagation()},"aria-label":e+" - please specify"})]},n)}))]})]}),Ll(Ul,{text:t.buttonText||"Submit",submitDisabled:k(),appearance:i,onSubmit:x,onPreviewSubmit:x,skipSubmitButton:y})]})}var ec=[$l,ql,Al],tc=[Dl,$l,ql,Al,Nl],nc=[1,2,3,4,5],rc=[1,2,3,4,5,6,7],ic=[0,1,2,3,4,5,6,7,8,9,10];function oc(e){switch(e){case 5:default:return nc;case 7:return rc;case 10:return ic}}var sc=Vo,ac=Qo,uc="ph:show_survey_widget",lc="PHWidgetSurveyClickListener";function cc(e,t,n){var r,i=e.questions[t],o=t+1;if(null==(r=i.branching)||!r.type)return t===e.questions.length-1?Ho.End:o;if(i.branching.type===Ho.End)return Ho.End;if(i.branching.type===Ho.SpecificQuestion){if(Number.isInteger(i.branching.index))return i.branching.index}else if(i.branching.type===Ho.ResponseBased){if(i.type===No.SingleChoice){var s,a=i.choices.indexOf(""+n);if(-1===a&&i.hasOpenChoice&&(a=i.choices.length-1),null!=(s=i.branching)&&null!=(s=s.responseValues)&&s.hasOwnProperty(a)){var u=i.branching.responseValues[a];return Number.isInteger(u)?u:u===Ho.End?Ho.End:o}}else if(i.type===No.Rating){var l;if("number"!=typeof n||!Number.isInteger(n))throw new Error("The response type must be an integer");var c=function(e,t){if(3===t){if(e<1||e>3)throw new Error("The response must be in range 1-3");return 1===e?"negative":2===e?"neutral":"positive"}if(5===t){if(e<1||e>5)throw new Error("The response must be in range 1-5");return e<=2?"negative":3===e?"neutral":"positive"}if(7===t){if(e<1||e>7)throw new Error("The response must be in range 1-7");return e<=3?"negative":4===e?"neutral":"positive"}if(10===t){if(e<0||e>10)throw new Error("The response must be in range 0-10");return e<=6?"detractors":e<=8?"passives":"promoters"}throw new Error("The scale must be one of: 3, 5, 7, 10")}(n,i.scale);if(null!=(l=i.branching)&&null!=(l=l.responseValues)&&l.hasOwnProperty(c)){var d=i.branching.responseValues[c];return Number.isInteger(d)?d:d===Ho.End?Ho.End:o}}return o}return zs.warn("Falling back to next question index due to unexpected branching type"),o}var dc=250,fc=20,pc=12;var _c=function(){function e(e){var t=this;this._surveyTimeouts=new Map,this._widgetSelectorListeners=new Map,this._handlePopoverSurvey=function(e){var n;t._clearSurveyTimeout(e.id),t._addSurveyToFocus(e);var r=(null==(n=e.appearance)?void 0:n.surveyPopupDelaySeconds)||0,i=pl(e,t._posthog).shadow;if(r<=0)return uo(Ll(mc,{posthog:t._posthog,survey:e,removeSurveyFromFocus:t._removeSurveyFromFocus}),i);var o=setTimeout((function(){if(!Tl(e))return t._removeSurveyFromFocus(e);uo(Ll(mc,{posthog:t._posthog,survey:ki({},e,{appearance:ki({},e.appearance,{surveyPopupDelaySeconds:0})}),removeSurveyFromFocus:t._removeSurveyFromFocus}),i)}),1e3*r);t._surveyTimeouts.set(e.id,o)},this._handleWidget=function(e){var n=pl(e,t._posthog),r=n.shadow;n.isNewlyCreated&&uo(Ll(bc,{posthog:t._posthog,survey:e},e.id),r)},this._removeWidgetSelectorListener=function(e){t._removeSurveyFromDom(e);var n=t._widgetSelectorListeners.get(e.id);n&&(n.element.removeEventListener("click",n.listener),n.element.removeAttribute(lc),t._widgetSelectorListeners.delete(e.id),zs.info("Removed click listener for survey "+e.id))},this._manageWidgetSelectorListener=function(e,n){var r=ac.querySelector(n),i=t._widgetSelectorListeners.get(e.id);if(r){if(t._handleWidget(e),i){if(r===i.element)return;zs.info("Selector element changed for survey "+e.id+". Re-attaching listener."),t._removeWidgetSelectorListener(e)}if(!r.hasAttribute(lc)){var o=function(t){var n,r;t.stopPropagation();var i=(null==(n=e.appearance)?void 0:n.position)===$o.NextToTrigger?function(e,t){try{var n=e.getBoundingClientRect(),r=sc.innerHeight,i=sc.innerWidth,o=dc,s=n.left+n.width/2-t/2;s+t>i-fc&&(s=i-t-fc),s<fc&&(s=fc);var a=pc,u=r-n.bottom,l=n.top,c=u<o&&l>u;return{position:"fixed",top:c?"auto":n.bottom+a+"px",left:s+"px",right:"auto",bottom:c?r-n.top+a+"px":"auto",zIndex:ll.zIndex}}catch(e){return zs.warn("Failed to calculate trigger position:",e),null}}(t.currentTarget,parseInt((null==(r=e.appearance)?void 0:r.maxWidth)||ll.maxWidth)):{};sc.dispatchEvent(new CustomEvent(uc,{detail:{surveyId:e.id,position:i}}))};Vs(r,"click",o),r.setAttribute(lc,"true"),t._widgetSelectorListeners.set(e.id,{element:r,listener:o,survey:e}),zs.info("Attached click listener for feedback button survey "+e.id)}}else i&&t._removeWidgetSelectorListener(e)},this.renderSurvey=function(e,n){uo(Ll(mc,{posthog:t._posthog,survey:e,removeSurveyFromFocus:t._removeSurveyFromFocus,isPopup:!1}),n)},this.getActiveMatchingSurveys=function(e,n){var r;void 0===n&&(n=!1),null==(r=t._posthog)||r.surveys.getSurveys((function(n){var r=n.filter((function(e){return t.checkSurveyEligibility(e).eligible&&t._isSurveyConditionMatched(e)&&t._hasActionOrEventTriggeredSurvey(e)&&t._checkFlags(e)}));e(r)}),n)},this.callSurveysAndEvaluateDisplayLogic=function(e){void 0===e&&(e=!1),t.getActiveMatchingSurveys((function(e){var n=e.filter((function(e){return e.type!==Do.API})),r=t._sortSurveysByAppearanceDelay(n),i=new Set;r.forEach((function(e){if(e.type===Do.Widget){var n,r,o,s;if((null==(n=e.appearance)?void 0:n.widgetType)===qo.Tab)return void t._handleWidget(e);if((null==(r=e.appearance)?void 0:r.widgetType)===qo.Selector&&null!=(o=e.appearance)&&o.widgetSelector)i.add(e.id),t._manageWidgetSelectorListener(e,null==(s=e.appearance)?void 0:s.widgetSelector)}Ss(t._surveyInFocus)&&e.type===Do.Popover&&t._handlePopoverSurvey(e)})),t._widgetSelectorListeners.forEach((function(e){var n=e.survey;i.has(n.id)||t._removeWidgetSelectorListener(n)}))}),e)},this._addSurveyToFocus=function(e){Ss(t._surveyInFocus)||zs.error("Survey "+[].concat(t._surveyInFocus)+" already in focus. Cannot add survey "+e.id+"."),t._surveyInFocus=e.id},this._removeSurveyFromFocus=function(e){t._surveyInFocus!==e.id&&zs.error("Survey "+e.id+" is not in focus. Cannot remove survey "+e.id+"."),t._clearSurveyTimeout(e.id),t._surveyInFocus=null,t._removeSurveyFromDom(e)},this._posthog=e,this._surveyInFocus=null}var t=e.prototype;return t._clearSurveyTimeout=function(e){var t=this._surveyTimeouts.get(e);t&&(clearTimeout(t),this._surveyTimeouts.delete(e))},t._sortSurveysByAppearanceDelay=function(e){return e.sort((function(e,t){var n,r,i=Fl(e),o=Fl(t);if(i&&!o)return-1;if(!i&&o)return 1;var s=e.schedule===Bo.Always,a=t.schedule===Bo.Always;return s&&!a?1:!s&&a?-1:((null==(n=e.appearance)?void 0:n.surveyPopupDelaySeconds)||0)-((null==(r=t.appearance)?void 0:r.surveyPopupDelaySeconds)||0)}))},t._isSurveyFeatureFlagEnabled=function(e){return!e||!!this._posthog.featureFlags.isFeatureEnabled(e,{send_event:!e.startsWith("survey-targeting-")})},t._isSurveyConditionMatched=function(e){return!e.conditions||Tl(e)&&function(e){var t,n,r;if(null==(t=e.conditions)||!t.deviceTypes||0===(null==(n=e.conditions)?void 0:n.deviceTypes.length))return!0;if(!ts)return!1;var i=nu(ts);return nl[El(null==(r=e.conditions)?void 0:r.deviceTypesMatchType)](e.conditions.deviceTypes,[i])}(e)&&function(e){var t;return null==(t=e.conditions)||!t.selector||!(null==sl||!sl.querySelector(e.conditions.selector))}(e)},t._internalFlagCheckSatisfied=function(e){return bl(e)||this._isSurveyFeatureFlagEnabled(e.internal_targeting_flag_key)||Fl(e)},t.checkSurveyEligibility=function(e){var t,n={eligible:!0,reason:void 0};return Ws(e)?this._isSurveyFeatureFlagEnabled(e.linked_flag_key)?this._isSurveyFeatureFlagEnabled(e.targeting_flag_key)?this._internalFlagCheckSatisfied(e)?function(e){var t=localStorage.getItem("lastSeenSurveyDate");if(!e||!t)return!0;var n=new Date,r=Math.abs(n.getTime()-new Date(t).getTime());return Math.ceil(r/864e5)>e}(null==(t=e.conditions)?void 0:t.seenSurveyWaitPeriodInDays)?function(e){return!!localStorage.getItem(wl(e))&&!bl(e)}(e)?(n.eligible=!1,n.reason="Survey has already been seen and it can't be activated again",n):n:(n.eligible=!1,n.reason="Survey wait period has not passed",n):(n.eligible=!1,n.reason="Survey internal targeting flag is not enabled and survey cannot activate repeatedly and survey is not in progress",n):(n.eligible=!1,n.reason="Survey targeting feature flag is not enabled",n):(n.eligible=!1,n.reason="Survey linked feature flag is not enabled",n):(n.eligible=!1,n.reason="Survey is not running. It was completed on "+e.end_date,n)},t._hasActionOrEventTriggeredSurvey=function(e){var t;if(!Gs(e)&&!Zs(e))return!0;var n=null==(t=this._posthog.surveys._surveyEventReceiver)?void 0:t.getSurveys();return!(null==n||!n.includes(e.id))},t._checkFlags=function(e){var t,n=this;return null==(t=e.feature_flag_keys)||!t.length||e.feature_flag_keys.every((function(e){var t=e.key,r=e.value;return!t||!r||n._isSurveyFeatureFlagEnabled(r)}))},t._removeSurveyFromDom=function(e){try{var t=ac.querySelector(Ml(e,!0));null!=t&&t.shadowRoot&&uo(null,t.shadowRoot),null==t||t.remove()}catch(t){zs.warn("Failed to remove survey "+e.id+" from DOM:",t)}},t.getTestAPI=function(){return{addSurveyToFocus:this._addSurveyToFocus,removeSurveyFromFocus:this._removeSurveyFromFocus,surveyInFocus:this._surveyInFocus,surveyTimeouts:this._surveyTimeouts,handleWidget:this._handleWidget,handlePopoverSurvey:this._handlePopoverSurvey,manageWidgetSelectorListener:this._manageWidgetSelectorListener,sortSurveysByAppearanceDelay:this._sortSurveysByAppearanceDelay,checkFlags:this._checkFlags.bind(this),isSurveyFeatureFlagEnabled:this._isSurveyFeatureFlagEnabled.bind(this)}},e}();function hc(e){if(ac&&sc){var t=new _c(e);return e.config.disable_surveys_automatic_display?(zs.info("Surveys automatic display is disabled. Skipping call surveys and evaluate display logic."),t):(t.callSurveysAndEvaluateDisplayLogic(!0),setInterval((function(){t.callSurveysAndEvaluateDisplayLogic(!1)}),1e3),t)}}function vc(e){var t=e.survey,n=e.removeSurveyFromFocus,r=void 0===n?function(){}:n,i=e.setSurveyVisible,o=e.isPreviewMode,s=void 0!==o&&o;Eo((function(){var e;if(!s&&null!=(e=t.conditions)&&e.url){var n=function(){var e,n=t.type===Do.Widget,o=Tl(t),s=(null==(e=t.appearance)?void 0:e.widgetType)===qo.Tab&&n;if(!o)return zs.info("Hiding survey "+t.id+" because URL does not match"),i(!1),r(t);s&&(zs.info("Showing survey "+t.id+" because it is a feedback button tab and URL matches"),i(!0))};Vs(sc,"popstate",n),Vs(sc,"hashchange",n);var o=sc.history.pushState,a=sc.history.replaceState;return sc.history.pushState=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];o.apply(this,t),n()},sc.history.replaceState=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];a.apply(this,t),n()},function(){sc.removeEventListener("popstate",n),sc.removeEventListener("hashchange",n),sc.history.pushState=o,sc.history.replaceState=a}}}),[s,t,r,i])}function gc(e,t,n){switch(void 0===t&&(t=$o.Right),t){case $o.TopLeft:return{top:"0",left:"0",transform:"translate(30px, 30px)"};case $o.TopRight:return{top:"0",right:"0",transform:"translate(-30px, 30px)"};case $o.TopCenter:return{top:"0",left:"50%",transform:"translate(-50%, 30px)"};case $o.MiddleLeft:return{top:"50%",left:"0",transform:"translate(30px, -50%)"};case $o.MiddleRight:return{top:"50%",right:"0",transform:"translate(-30px, -50%)"};case $o.MiddleCenter:return{top:"50%",left:"50%",transform:"translate(-50%, -50%)"};case $o.Left:return{left:"30px"};case $o.Center:return{left:"50%",transform:"translateX(-50%)"};default:case $o.Right:return{right:e===Do.Widget&&n===qo.Tab?"60px":"30px"}}}function mc(e){var t,n,r,i,o,s,a=e.survey,u=e.forceDisableHtml,l=e.posthog,c=e.style,d=void 0===c?{}:c,f=e.previewPageIndex,p=e.removeSurveyFromFocus,_=void 0===p?function(){}:p,h=e.isPopup,v=void 0===h||h,g=e.onPreviewSubmit,m=void 0===g?function(){}:g,y=e.onPopupSurveyDismissed,b=void 0===y?function(){}:y,w=e.onCloseConfirmationMessage,S=void 0===w?function(){}:w,k=To(null),x=Number.isInteger(f),E=null!=(t=a.appearance)&&t.surveyPopupDelaySeconds?1e3*a.appearance.surveyPopupDelaySeconds:0,T=function(e,t,n,r,i,o){var s=xo(r||0===n),a=s[0],u=s[1],l=xo(!1),c=l[0],d=l[1],f=function(){var t=function(){e.type===Do.Popover&&i(e),u(!1)};ac.startViewTransition?ac.startViewTransition((function(){var e;null==o||null==(e=o.current)||e.remove()})).finished.then((function(){setTimeout((function(){t()}),100)})):t()},p=function(t){t.detail.surveyId===e.id&&f()};return Eo((function(){if(t){if(!r){var i=function(t){var n,r;if(t.detail.surveyId===e.id){if(null==(n=e.appearance)||!n.displayThankYouMessage)return f();d(!0),null!=(r=e.appearance)&&r.autoDisappear&&setTimeout((function(){f()}),5e3)}},o=function(){var n;Tl(e)&&(u(!0),sc.dispatchEvent(new Event("PHSurveyShown")),t.capture(jo.SHOWN,((n={})[Uo.SURVEY_NAME]=e.name,n[Uo.SURVEY_ID]=e.id,n[Uo.SURVEY_ITERATION]=e.current_iteration,n[Uo.SURVEY_ITERATION_START_DATE]=e.current_iteration_start_date,n.sessionRecordingUrl=null==t.get_session_replay_url?void 0:t.get_session_replay_url(),n)),localStorage.setItem("lastSeenSurveyDate",(new Date).toISOString()))};if(Vs(sc,"PHSurveyClosed",p),Vs(sc,"PHSurveySent",i),n>0){var s=setTimeout(o,n);return function(){clearTimeout(s),sc.removeEventListener("PHSurveyClosed",p),sc.removeEventListener("PHSurveySent",i)}}return o(),function(){sc.removeEventListener("PHSurveyClosed",p),sc.removeEventListener("PHSurveySent",i)}}}else zs.error("usePopupVisibility hook called without a PostHog instance.")}),[]),vc({survey:e,removeSurveyFromFocus:i,setSurveyVisible:u,isPreviewMode:r}),{isPopupVisible:a,isSurveySent:c,setIsPopupVisible:u,hidePopupWithViewTransition:f}}(a,l,E,x,_,k),C=T.isPopupVisible,I=T.isSurveySent,P=T.hidePopupWithViewTransition,F=I||f===a.questions.length,R=Co((function(){var e=Pl(a);return{isPreviewMode:x,previewPageIndex:f,onPopupSurveyDismissed:function(){vl(a,l,x),b()},isPopup:v||!1,surveySubmissionId:(null==e?void 0:e.surveySubmissionId)||ia(),onPreviewSubmit:m,posthog:l}}),[x,f,v,l,a,b,m]);return C?Ll(Sl.Provider,{value:R,children:Ll("div",{className:"ph-survey",style:ki({},gc(a.type,null==(n=a.appearance)?void 0:n.position,null==(r=a.appearance)?void 0:r.widgetType),d),ref:k,children:F?Ll(Gl,{header:(null==(i=a.appearance)?void 0:i.thankYouMessageHeader)||"Thank you!",description:(null==(o=a.appearance)?void 0:o.thankYouMessageDescription)||"",forceDisableHtml:!!u,contentType:null==(s=a.appearance)?void 0:s.thankYouMessageDescriptionContentType,appearance:a.appearance||ll,onClose:function(){P(),S()}}):Ll(yc,{survey:a,forceDisableHtml:!!u,posthog:l})})}):null}function yc(e){var t=e.survey,n=e.forceDisableHtml,r=e.posthog,i=xo((function(){var e=Pl(t);return null!=e&&e.responses&&zs.info("Survey is already in progress, filling in initial responses"),(null==e?void 0:e.responses)||{}})),o=i[0],s=i[1],a=Io(Sl),u=a.previewPageIndex,l=a.onPopupSurveyDismissed,c=a.isPopup,d=a.onPreviewSubmit,f=a.surveySubmissionId,p=a.isPreviewMode,_=xo((function(){var e=Pl(t);return u||(null==e?void 0:e.lastQuestionIndex)||0})),h=_[0],v=_[1],g=Co((function(){return yl(t)}),[t]);Eo((function(){p&&!ys(u)&&v(u)}),[u,p]);var m=g.at(h);return m?Ll("form",{className:"survey-form",name:"surveyForm",children:[c&&Ll(zl,{onClick:function(){l()}}),Ll("div",{className:"survey-box",children:wc({question:m,forceDisableHtml:n,displayQuestionIndex:h,appearance:t.appearance||ll,onSubmit:function(e){return function(e){var n,i=e.res,a=e.displayQuestionIndex,u=e.questionId;if(r)if(u){var l=al(u),c=ki({},o,((n={})[l]=i,n));s(c);var d=cc(t,a,i),p=d===Ho.End;p||(v(d),Il(t,{surveySubmissionId:f,responses:c,lastQuestionIndex:d})),(t.enable_partial_responses||p)&&hl({responses:c,survey:t,surveySubmissionId:f,isSurveyCompleted:p,posthog:r})}else zs.error("onNextButtonClick called without a questionId.");else zs.error("onNextButtonClick called without a PostHog instance.")}({res:e,displayQuestionIndex:h,questionId:m.id})},onPreviewSubmit:d,initialValue:m.id?o[al(m.id)]:void 0})})]}):null}function bc(e){var t,n,r,i,o,s=e.survey,a=e.forceDisableHtml,u=e.posthog,l=e.readOnly,c=xo(!0),d=c[0],f=c[1],p=xo(!1),_=p[0],h=p[1],v=xo({}),g=v[0],m=v[1],y=function(){h(!_)};if(Eo((function(){var e;if(u){if(!l){"tab"===(null==(e=s.appearance)?void 0:e.widgetType)&&m({top:"50%",bottom:"auto"});var t=function(e){var t,n=e;(null==(t=n.detail)?void 0:t.surveyId)===s.id&&(zs.info("Received show event for feedback button survey "+s.id),m(n.detail.position||{}),y())};return Vs(sc,uc,t),function(){sc.removeEventListener(uc,t)}}}else zs.error("FeedbackWidget called without a PostHog instance.")}),[u,l,s.id,null==(t=s.appearance)?void 0:t.widgetType,null==(n=s.appearance)?void 0:n.widgetSelector,null==(r=s.appearance)?void 0:r.borderColor]),vc({survey:s,setSurveyVisible:f}),!d)return null;var b=function(){s.schedule!==Bo.Always&&f(!1),setTimeout((function(){h(!1)}),200)};return Ll(Ui,{children:["tab"===(null==(i=s.appearance)?void 0:i.widgetType)&&Ll("button",{className:"ph-survey-widget-tab",onClick:y,disabled:l,children:(null==(o=s.appearance)?void 0:o.widgetLabel)||""}),_&&Ll(mc,{posthog:u,survey:s,forceDisableHtml:a,style:g,onPopupSurveyDismissed:b,onCloseConfirmationMessage:b})]})}var wc=function(e){var t=e.question,n=e.forceDisableHtml,r=e.displayQuestionIndex,i=e.appearance,o=e.onSubmit,s=e.onPreviewSubmit,a={forceDisableHtml:n,appearance:i,onPreviewSubmit:function(e){s(e)},onSubmit:function(e){o(e)},initialValue:e.initialValue,displayQuestionIndex:r};switch(t.type){case No.Open:return Bi(Yl,ki({},a,{question:t,key:t.id}));case No.Link:return Bi(Ql,ki({},a,{question:t,key:t.id}));case No.Rating:return Bi(Jl,ki({},a,{question:t,key:t.id}));case No.SingleChoice:case No.MultipleChoice:return Bi(Kl,ki({},a,{question:t,key:t.id}));default:return zs.error("Unsupported question type: "+t.type),null}};function Sc(e){return!ys(Event)&&kc(e,Event)}function kc(e,t){try{return e instanceof t}catch(e){return!1}}function xc(e){return Ss(e)||!gs(e)&&!vs(e)}function Ec(e){switch(Object.prototype.toString.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object DOMError]":return!0;default:return kc(e,Error)}}function Tc(e,t){return Object.prototype.toString.call(e)==="[object "+t+"]"}function Cc(e){return Tc(e,"DOMError")}ns.__PosthogExtensions__=ns.__PosthogExtensions__||{},ns.__PosthogExtensions__.generateSurveys=hc,ns.extendPostHogWithSurveys=hc;var Ic=/\(error: (.*)\)/,Pc=50,Fc="?";function Rc(e,t,n,r){var i={platform:"web:javascript",filename:e,function:"<anonymous>"===t?Fc:t,in_app:!0};return ys(n)||(i.lineno=n),ys(r)||(i.colno=r),i}var Mc=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,Oc=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,Lc=/\((\S*)(?::(\d+))(?::(\d+))\)/,Ac=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,qc=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,$c=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t.sort((function(e,t){return e[0]-t[0]})).map((function(e){return e[1]}));return function(e,t){void 0===t&&(t=0);for(var n=[],i=e.split("\n"),o=t;o<i.length;o++){var s=i[o];if(!(s.length>1024)){var a=Ic.test(s)?s.replace(Ic,"$1"):s;if(!a.match(/\S*Error: /)){for(var u,l=Si(r);!(u=l()).done;){var c=(0,u.value)(a);if(c){n.push(c);break}}if(n.length>=Pc)break}}}return function(e){if(!e.length)return[];var t=Array.from(e);return t.reverse(),t.slice(0,Pc).map((function(e){return ki({},e,{filename:e.filename||Dc(t).filename,function:e.function||Fc})}))}(n)}}.apply(void 0,[[30,function(e){var t=Mc.exec(e);if(t){var n=t[1],r=t[2],i=t[3];return Rc(n,Fc,+r,+i)}var o=Oc.exec(e);if(o){if(o[2]&&0===o[2].indexOf("eval")){var s=Lc.exec(o[2]);s&&(o[2]=s[1],o[3]=s[2],o[4]=s[3])}var a=jc(o[1]||Fc,o[2]),u=a[0];return Rc(a[1],u,o[3]?+o[3]:void 0,o[4]?+o[4]:void 0)}}],[50,function(e){var t=Ac.exec(e);if(t){if(t[3]&&t[3].indexOf(" > eval")>-1){var n=qc.exec(t[3]);n&&(t[1]=t[1]||"eval",t[3]=n[1],t[4]=n[2],t[5]="")}var r=t[3],i=t[1]||Fc,o=jc(i,r);return i=o[0],Rc(r=o[1],i,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}}]]);function Dc(e){return e[e.length-1]||{}}var Nc,Hc,Bc,jc=function(e,t){var n=-1!==e.indexOf("safari-extension"),r=-1!==e.indexOf("safari-web-extension");return n||r?[-1!==e.indexOf("@")?e.split("@")[0]:Fc,n?"safari-extension:"+t:"safari-web-extension:"+t]:[e,t]};var Uc=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i;function Vc(e,t){void 0===t&&(t=0);var n=e.stacktrace||e.stack||"",r=function(e){if(e&&zc.test(e.message))return 1;return 0}(e);try{var i=$c,o=function(e,t){var n=function(e){var t=globalThis._posthogChunkIds;if(!t)return{};var n=Object.keys(t);return Bc&&n.length===Hc||(Hc=n.length,Bc=n.reduce((function(n,r){Nc||(Nc={});var i=Nc[r];if(i)n[i[0]]=i[1];else for(var o=e(r),s=o.length-1;s>=0;s--){var a=o[s],u=null==a?void 0:a.filename,l=t[r];if(u&&l){n[u]=l,Nc[r]=[u,l];break}}return n}),{})),Bc}(t);return e.forEach((function(e){e.filename&&(e.chunk_id=n[e.filename])})),e}(i(n,r),i);return o.slice(0,o.length-t)}catch(e){}return[]}var zc=/Minified React error #\d+;/i;function Wc(e,t){var n,r,i=Vc(e),o=null===(n=null==t?void 0:t.handled)||void 0===n||n,s=null!==(r=null==t?void 0:t.synthetic)&&void 0!==r&&r;return{type:null!=t&&t.overrideExceptionType?t.overrideExceptionType:e.name,value:function(e){var t=e.message;if(t.error&&"string"==typeof t.error.message)return String(t.error.message);return String(t)}(e),stacktrace:{frames:i,type:"raw"},mechanism:{handled:o,synthetic:s}}}function Gc(e,t){var n=Wc(e,t);return e.cause&&Ec(e.cause)&&e.cause!==e?[n].concat(Gc(e.cause,{handled:null==t?void 0:t.handled,synthetic:null==t?void 0:t.synthetic})):[n]}function Zc(e,t){return{$exception_list:Gc(e,t),$exception_level:"error"}}function Yc(e,t){var n,r,i,o=null===(n=null==t?void 0:t.handled)||void 0===n||n,s=null===(r=null==t?void 0:t.synthetic)||void 0===r||r,a={type:null!=t&&t.overrideExceptionType?t.overrideExceptionType:null!==(i=null==t?void 0:t.defaultExceptionType)&&void 0!==i?i:"Error",value:e||(null==t?void 0:t.defaultExceptionMessage),mechanism:{handled:o,synthetic:s}};if(null!=t&&t.syntheticException){var u=Vc(t.syntheticException,1);u.length&&(a.stacktrace={frames:u,type:"raw"})}return{$exception_list:[a],$exception_level:"error"}}function Qc(e){return bs(e)&&!ws(e)&&as.indexOf(e)>=0}function Jc(e,t){var n,r,i=null===(n=null==t?void 0:t.handled)||void 0===n||n,o=null===(r=null==t?void 0:t.synthetic)||void 0===r||r,s=null!=t&&t.overrideExceptionType?t.overrideExceptionType:Sc(e)?e.constructor.name:"Error",a="Non-Error 'exception' captured with keys: "+function(e,t){void 0===t&&(t=40);var n=Object.keys(e);if(n.sort(),!n.length)return"[object has no keys]";for(var r=n.length;r>0;r--){var i=n.slice(0,r).join(", ");if(!(i.length>t))return r===n.length||i.length<=t?i:i.slice(0,t)+"..."}return""}(e),u={type:s,value:a,mechanism:{handled:i,synthetic:o}};if(null!=t&&t.syntheticException){var l=Vc(null==t?void 0:t.syntheticException,1);l.length&&(u.stacktrace={frames:l,type:"raw"})}return{$exception_list:[u],$exception_level:Qc(e.level)?e.level:"error"}}function Xc(e,t){var n=e.error,r=e.event,i={$exception_list:[]},o=n||r;if(Cc(o)||function(e){return Tc(e,"DOMException")}(o)){var s=o;if(function(e){return"stack"in e}(o))i=Zc(o,t);else{var a=s.name||(Cc(s)?"DOMError":"DOMException"),u=s.message?a+": "+s.message:a;i=Yc(u,ki({},t,{overrideExceptionType:Cc(s)?"DOMError":"DOMException",defaultExceptionMessage:u}))}return"code"in s&&(i.$exception_DOMException_code=""+s.code),i}if(function(e){return Tc(e,"ErrorEvent")}(o)&&o.error)return Zc(o.error,t);if(Ec(o))return Zc(o,t);if(function(e){return Tc(e,"Object")}(o)||Sc(o))return Jc(o,t);if(ys(n)&&bs(r)){var l="Error",c=r,d=r.match(Uc);return d&&(l=d[1],c=d[2]),Yc(c,ki({},t,{overrideExceptionType:l,defaultExceptionMessage:c}))}return Yc(o,t)}function Kc(e){var t=function(e){if(xc(e))return e;try{if("reason"in e)return e.reason;if("detail"in e&&"reason"in e.detail)return e.detail.reason}catch(e){}return e}(e[0]);return xc(t)?Yc("Non-Error promise rejection captured with value: "+String(t),{handled:!1,synthetic:!1,overrideExceptionType:"UnhandledRejection"}):Xc({event:t},{handled:!1,overrideExceptionType:"UnhandledRejection",defaultExceptionMessage:String(t)})}var ed=Fs("[ExceptionAutocapture]"),td={wrapOnError:function(e){var t=Vo;t||ed.info("window not available, cannot wrap onerror");var n=t.onerror;return t.onerror=function(){for(var t,r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];var s=Xc({event:i[0],error:i[4]});return e(s),null!==(t=null==n?void 0:n.apply(void 0,i))&&void 0!==t&&t},t.onerror.__POSTHOG_INSTRUMENTED__=!0,function(){var e;null==(e=t.onerror)||delete e.__POSTHOG_INSTRUMENTED__,t.onerror=n}},wrapUnhandledRejection:function(e){var t=Vo;t||ed.info("window not available, cannot wrap onUnhandledRejection");var n=t.onunhandledrejection;return t.onunhandledrejection=function(){for(var r,i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];var a=Kc(o);return e(a),null!==(r=null==n?void 0:n.apply(t,o))&&void 0!==r&&r},t.onunhandledrejection.__POSTHOG_INSTRUMENTED__=!0,function(){var e;null==(e=t.onunhandledrejection)||delete e.__POSTHOG_INSTRUMENTED__,t.onunhandledrejection=n}},wrapConsoleError:function(e){var t=console;t||ed.info("console not available, cannot wrap console.error");var n=t.error;return t.error=function(){for(var t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];var o=r.join(" "),s=r.find((function(e){return e instanceof Error})),a=s?Xc({event:o,error:s}):Xc({event:o},{syntheticException:new Error("PostHog syntheticException")});return e(a),null==n?void 0:n.apply(void 0,r)},t.error.__POSTHOG_INSTRUMENTED__=!0,function(){var e;null==(e=t.error)||delete e.__POSTHOG_INSTRUMENTED__,t.error=n}}};function nd(e,t,n){try{if(!(t in e))return function(){};var r=e[t],i=n(r);return vs(i)&&(i.prototype=i.prototype||{},Object.defineProperties(i,{__posthog_wrapped__:{enumerable:!1,value:!0}})),e[t]=i,function(){e[t]=r}}catch(e){return function(){}}}ns.__PosthogExtensions__=ns.__PosthogExtensions__||{},ns.__PosthogExtensions__.errorWrappingFunctions=td,ns.posthogErrorWrappingFunctions=td;var rd=function(e,t,n){if(t){var r=t.checkAndGetSessionAndWindowId(!0),i=r.sessionId,o=r.windowId;n.headers.set("X-POSTHOG-SESSION-ID",i),n.headers.set("X-POSTHOG-WINDOW-ID",o)}n.headers.set("X-POSTHOG-DISTINCT-ID",e)};ns.__PosthogExtensions__=ns.__PosthogExtensions__||{};var id={_patchFetch:function(e,t){return nd(Vo,"fetch",(function(n){return function(){var r=function(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function s(e){bi(o,r,i,s,a,"next",e)}function a(e){bi(o,r,i,s,a,"throw",e)}s(void 0)}))}}(Ei().mark((function r(i,o){var s;return Ei().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return s=new Request(i,o),rd(e,t,s),r.abrupt("return",n(s));case 3:case"end":return r.stop()}}),r)})));return function(e,t){return r.apply(this,arguments)}}()}))},_patchXHR:function(e,t){return nd(Vo.XMLHttpRequest.prototype,"open",(function(n){return function(r,i,o,s,a){void 0===o&&(o=!0);var u=new Request(i);return rd(e,t,u),n.call(this,r,u.url,o,s,a)}}))}};ns.__PosthogExtensions__.tracingHeadersPatchFns=id,ns.postHogTracingHeadersPatchFns=id;var od="$people_distinct_id",sd="__alias",ad="__timers",ud="$autocapture_disabled_server_side",ld="$heatmaps_enabled_server_side",cd="$exception_capture_enabled_server_side",dd="$error_tracking_suppression_rules",fd="$web_vitals_enabled_server_side",pd="$dead_clicks_enabled_server_side",_d="$web_vitals_allowed_metrics",hd="$session_recording_enabled_server_side",vd="$console_log_recording_enabled_server_side",gd="$session_recording_network_payload_capture",md="$session_recording_masking",yd="$session_recording_canvas_recording",bd="$replay_sample_rate",wd="$replay_minimum_duration",Sd="$replay_script_config",kd="$sesid",xd="$session_is_sampled",Ed="$session_recording_url_trigger_activated_session",Td="$session_recording_event_trigger_activated_session",Cd="$enabled_feature_flags",Id="$early_access_features",Pd="$feature_flag_details",Fd="$stored_person_properties",Rd="$stored_group_properties",Md="$surveys",Od="$surveys_activated",Ld="$flag_call_reported",Ad="$user_state",qd="$client_session_props",$d="$capture_rate_limit",Dd="$initial_campaign_params",Nd="$initial_referrer_info",Hd="$initial_person_info",Bd="$epp",jd="__POSTHOG_TOOLBAR__",Ud="$posthog_cookieless",Vd=[od,sd,"__cmpns",ad,hd,ld,kd,Cd,dd,Ad,Id,Pd,Rd,Fd,Md,Ld,qd,$d,Dd,Nd,Bd,Hd];function zd(e){return e instanceof Element&&(e.id===jd||!(null==e.closest||!e.closest(".toolbar-global-fade-container")))}function Wd(e){return!!e&&1===e.nodeType}function Gd(e,t){return!!e&&!!e.tagName&&e.tagName.toLowerCase()===t.toLowerCase()}function Zd(e){return!!e&&3===e.nodeType}function Yd(e){return!!e&&11===e.nodeType}function Qd(e){return e?ls(e).split(/\s+/):[]}function Jd(e){var t=null==Vo?void 0:Vo.location.href;return!!(t&&e&&e.some((function(e){return t.match(e)})))}function Xd(e){var t="";switch(typeof e.className){case"string":t=e.className;break;case"object":t=(e.className&&"baseVal"in e.className?e.className.baseVal:null)||e.getAttribute("class")||"";break;default:t=""}return Qd(t)}function Kd(e){return ks(e)?null:ls(e).split(/(\s+)/).filter((function(e){return _f(e)})).join("").replace(/[\r\n]/g," ").replace(/[ ]+/g," ").substring(0,255)}function ef(e){var t="";return sf(e)&&!af(e)&&e.childNodes&&e.childNodes.length&&Os(e.childNodes,(function(e){var n;Zd(e)&&e.textContent&&(t+=null!==(n=Kd(e.textContent))&&void 0!==n?n:"")})),ls(t)}function tf(e){return ys(e.target)?e.srcElement||null:null!=(t=e.target)&&t.shadowRoot?e.composedPath()[0]||null:e.target||null;var t}var nf=["a","button","form","input","select","textarea","label"];function rf(e){var t=e.parentNode;return!(!t||!Wd(t))&&t}function of(e,t,n,r,i){var o,s,a;if(void 0===n&&(n=void 0),!Vo||!e||Gd(e,"html")||!Wd(e))return!1;if(null!=(o=n)&&o.url_allowlist&&!Jd(n.url_allowlist))return!1;if(null!=(s=n)&&s.url_ignorelist&&Jd(n.url_ignorelist))return!1;if(null!=(a=n)&&a.dom_event_allowlist){var u=n.dom_event_allowlist;if(u&&!u.some((function(e){return t.type===e})))return!1}for(var l=!1,c=[e],d=!0,f=e;f.parentNode&&!Gd(f,"body");)if(Yd(f.parentNode))c.push(f.parentNode.host),f=f.parentNode.host;else{if(!(d=rf(f)))break;if(r||nf.indexOf(d.tagName.toLowerCase())>-1)l=!0;else{var p=Vo.getComputedStyle(d);p&&"pointer"===p.getPropertyValue("cursor")&&(l=!0)}c.push(d),f=d}if(!function(e,t){var n=null==t?void 0:t.element_allowlist;if(ys(n))return!0;for(var r,i,o=function(){var e=i.value;if(n.some((function(t){return e.tagName.toLowerCase()===t})))return{v:!0}},s=Si(e);!(i=s()).done;)if(r=o())return r.v;return!1}(c,n))return!1;if(!function(e,t){var n=null==t?void 0:t.css_selector_allowlist;if(ys(n))return!0;for(var r,i,o=function(){var e=i.value;if(n.some((function(t){return e.matches(t)})))return{v:!0}},s=Si(e);!(i=s()).done;)if(r=o())return r.v;return!1}(c,n))return!1;var _=Vo.getComputedStyle(e);if(_&&"pointer"===_.getPropertyValue("cursor")&&"click"===t.type)return!0;var h=e.tagName.toLowerCase();switch(h){case"html":return!1;case"form":return(i||["submit"]).indexOf(t.type)>=0;case"input":case"select":case"textarea":return(i||["change","click"]).indexOf(t.type)>=0;default:return l?(i||["click"]).indexOf(t.type)>=0:(i||["click"]).indexOf(t.type)>=0&&(nf.indexOf(h)>-1||"true"===e.getAttribute("contenteditable"))}}function sf(e){for(var t=e;t.parentNode&&!Gd(t,"body");t=t.parentNode){var n=Xd(t);if(us(n,"ph-sensitive")||us(n,"ph-no-capture"))return!1}if(us(Xd(e),"ph-include"))return!0;var r=e.type||"";if(bs(r))switch(r.toLowerCase()){case"hidden":case"password":return!1}var i=e.name||e.id||"";if(bs(i)){if(/^cc|cardnum|ccnum|creditcard|csc|cvc|cvv|exp|pass|pwd|routing|seccode|securitycode|securitynum|socialsec|socsec|ssn/i.test(i.replace(/[^a-zA-Z0-9]/g,"")))return!1}return!0}function af(e){return!!(Gd(e,"input")&&!["button","checkbox","submit","reset"].includes(e.type)||Gd(e,"select")||Gd(e,"textarea")||"true"===e.getAttribute("contenteditable"))}var uf="(4[0-9]{12}(?:[0-9]{3})?)|(5[1-5][0-9]{14})|(6(?:011|5[0-9]{2})[0-9]{12})|(3[47][0-9]{13})|(3(?:0[0-5]|[68][0-9])[0-9]{11})|((?:2131|1800|35[0-9]{3})[0-9]{11})",lf=new RegExp("^(?:"+uf+")$"),cf=new RegExp(uf),df="\\d{3}-?\\d{2}-?\\d{4}",ff=new RegExp("^("+df+")$"),pf=new RegExp("("+df+")");function _f(e,t){if(void 0===t&&(t=!0),ks(e))return!1;if(bs(e)){if(e=ls(e),(t?lf:cf).test((e||"").replace(/[- ]/g,"")))return!1;if((t?ff:pf).test(e))return!1}return!0}function hf(e){var t=ef(e);return _f(t=(t+" "+vf(e)).trim())?t:""}function vf(e){var t="";return e&&e.childNodes&&e.childNodes.length&&Os(e.childNodes,(function(e){var n;if(e&&"span"===(null==(n=e.tagName)?void 0:n.toLowerCase()))try{var r=ef(e);t=(t+" "+r).trim(),e.childNodes&&e.childNodes.length&&(t=(t+" "+vf(e)).trim())}catch(e){Ps.error("[AutoCapture]",e)}})),t}function gf(e){return function(e){var t=e.map((function(e){var t,n,r="";if(e.tag_name&&(r+=e.tag_name),e.attr_class){e.attr_class.sort();for(var i,o=Si(e.attr_class);!(i=o()).done;){r+="."+i.value.replace(/"/g,"")}}var s=ki({},e.text?{text:e.text}:{},{"nth-child":null!==(t=e.nth_child)&&void 0!==t?t:0,"nth-of-type":null!==(n=e.nth_of_type)&&void 0!==n?n:0},e.href?{href:e.href}:{},e.attr_id?{attr_id:e.attr_id}:{},e.attributes),a={};return qs(s).sort((function(e,t){var n=e[0],r=t[0];return n.localeCompare(r)})).forEach((function(e){var t=e[0],n=e[1];return a[mf(t.toString())]=mf(n.toString())})),r+=":",r+=qs(a).map((function(e){return e[0]+'="'+e[1]+'"'})).join("")}));return t.join(";")}(function(e){return e.map((function(e){var t,n,r={text:null==(t=e.$el_text)?void 0:t.slice(0,400),tag_name:e.tag_name,href:null==(n=e.attr__href)?void 0:n.slice(0,2048),attr_class:yf(e),attr_id:e.attr__id,nth_child:e.nth_child,nth_of_type:e.nth_of_type,attributes:{}};return qs(e).filter((function(e){return 0===e[0].indexOf("attr__")})).forEach((function(e){var t=e[0],n=e[1];return r.attributes[t]=n})),r}))}(e))}function mf(e){return e.replace(/"|\\"/g,'\\"')}function yf(e){var t=e.attr__class;return t?hs(t)?t:Qd(t):void 0}var bf=function(){function e(){this.clicks=[]}return e.prototype.isRageClick=function(e,t,n){var r=this.clicks[this.clicks.length-1];if(r&&Math.abs(e-r.x)+Math.abs(t-r.y)<30&&n-r.timestamp<1e3){if(this.clicks.push({x:e,y:t,timestamp:n}),3===this.clicks.length)return!0}else this.clicks=[{x:e,y:t,timestamp:n}];return!1},e}(),wf=Fs("[AutoCapture]");function Sf(e,t){return t.length>e?t.slice(0,e)+"...":t}function kf(e){if(e.previousElementSibling)return e.previousElementSibling;var t=e;do{t=t.previousSibling}while(t&&!Wd(t));return t}function xf(e,t,n,r){var i=e.tagName.toLowerCase(),o={tag_name:i};nf.indexOf(i)>-1&&!n&&("a"===i.toLowerCase()||"button"===i.toLowerCase()?o.$el_text=Sf(1024,hf(e)):o.$el_text=Sf(1024,ef(e)));var s=Xd(e);s.length>0&&(o.classes=s.filter((function(e){return""!==e}))),Os(e.attributes,(function(n){var i;if((!af(e)||-1!==["name","id","class","aria-label"].indexOf(n.name))&&((null==r||!r.includes(n.name))&&!t&&_f(n.value)&&(i=n.name,!bs(i)||"_ngcontent"!==i.substring(0,10)&&"_nghost"!==i.substring(0,7)))){var s=n.value;"class"===n.name&&(s=Qd(s).join(" ")),o["attr__"+n.name]=Sf(1024,s)}}));for(var a=1,u=1,l=e;l=kf(l);)a++,l.tagName===e.tagName&&u++;return o.nth_child=a,o.nth_of_type=u,o}function Ef(e,t){for(var n,r,i=t.e,o=t.maskAllElementAttributes,s=t.maskAllText,a=t.elementAttributeIgnoreList,u=t.elementsChainAsString,l=[e],c=e;c.parentNode&&!Gd(c,"body");)Yd(c.parentNode)?(l.push(c.parentNode.host),c=c.parentNode.host):(l.push(c.parentNode),c=c.parentNode);var d,f=[],p={},_=!1,h=!1;if(Os(l,(function(e){var t=sf(e);"a"===e.tagName.toLowerCase()&&(_=e.getAttribute("href"),_=t&&_&&_f(_)&&_),us(Xd(e),"ph-no-capture")&&(h=!0),f.push(xf(e,o,s,a));var n=function(e){if(!sf(e))return{};var t={};return Os(e.attributes,(function(e){if(e.name&&0===e.name.indexOf("data-ph-capture-attribute")){var n=e.name.replace("data-ph-capture-attribute-",""),r=e.value;n&&r&&_f(r)&&(t[n]=r)}})),t}(e);Ls(p,n)})),h)return{props:{},explicitNoCapture:h};if(s||("a"===e.tagName.toLowerCase()||"button"===e.tagName.toLowerCase()?f[0].$el_text=hf(e):f[0].$el_text=ef(e)),_){var v,g;f[0].attr__href=_;var m=null==(v=iu(_))?void 0:v.host,y=null==Vo||null==(g=Vo.location)?void 0:g.host;m&&y&&m!==y&&(d=_)}return{props:Ls({$event_type:i.type,$ce_version:1},u?{}:{$elements:f},{$elements_chain:gf(f)},null!=(n=f[0])&&n.$el_text?{$el_text:null==(r=f[0])?void 0:r.$el_text}:{},d&&"click"===i.type?{$external_click_url:d}:{},p)}}var Tf=function(){function e(e){this._initialized=!1,this._isDisabledServerSide=null,this.rageclicks=new bf,this._elementsChainAsString=!1,this.instance=e,this._elementSelectors=null}var t=e.prototype;return t._addDomEventHandlers=function(){var e=this;if(this.isBrowserSupported()){if(Vo&&Qo){var t=function(t){t=t||(null==Vo?void 0:Vo.event);try{e._captureEvent(t)}catch(e){wf.error("Failed to capture event",e)}};if(Vs(Qo,"submit",t,{capture:!0}),Vs(Qo,"change",t,{capture:!0}),Vs(Qo,"click",t,{capture:!0}),this._config.capture_copied_text){var n=function(t){t=t||(null==Vo?void 0:Vo.event),e._captureEvent(t,is)};Vs(Qo,"copy",n,{capture:!0}),Vs(Qo,"cut",n,{capture:!0})}}}else wf.info("Disabling Automatic Event Collection because this browser is not supported")},t.startIfEnabled=function(){this.isEnabled&&!this._initialized&&(this._addDomEventHandlers(),this._initialized=!0)},t.onRemoteConfig=function(e){var t;(e.elementsChainAsString&&(this._elementsChainAsString=e.elementsChainAsString),this.instance.persistence)&&this.instance.persistence.register(((t={})[ud]=!!e.autocapture_opt_out,t));this._isDisabledServerSide=!!e.autocapture_opt_out,this.startIfEnabled()},t.setElementSelectors=function(e){this._elementSelectors=e},t.getElementSelectors=function(e){var t,n=[];return null==(t=this._elementSelectors)||t.forEach((function(t){var r=null==Qo?void 0:Qo.querySelectorAll(t);null==r||r.forEach((function(r){e===r&&n.push(t)}))})),n},t._captureEvent=function(e,t){if(void 0===t&&(t="$autocapture"),this.isEnabled){var n,r=tf(e);if(Zd(r)&&(r=r.parentNode||null),"$autocapture"===t&&"click"===e.type&&e instanceof MouseEvent)this.instance.config.rageclick&&null!=(n=this.rageclicks)&&n.isRageClick(e.clientX,e.clientY,(new Date).getTime())&&this._captureEvent(e,"$rageclick");var i=t===is;if(r&&of(r,e,this._config,i,i?["copy","cut"]:void 0)){var o=Ef(r,{e:e,maskAllElementAttributes:this.instance.config.mask_all_element_attributes,maskAllText:this.instance.config.mask_all_text,elementAttributeIgnoreList:this._config.element_attribute_ignorelist,elementsChainAsString:this._elementsChainAsString}),s=o.props;if(o.explicitNoCapture)return!1;var a=this.getElementSelectors(r);if(a&&a.length>0&&(s.$element_selectors=a),t===is){var u,l=Kd(null==Vo||null==(u=Vo.getSelection())?void 0:u.toString()),c=e.type||"clipboard";if(!l)return!1;s.$selected_content=l,s.$copy_type=c}return this.instance.capture(t,s),!0}}},t.isBrowserSupported=function(){return vs(null==Qo?void 0:Qo.querySelectorAll)},wi(e,[{key:"_config",get:function(){var e,t,n=gs(this.instance.config.autocapture)?this.instance.config.autocapture:{};return n.url_allowlist=null==(e=n.url_allowlist)?void 0:e.map((function(e){return new RegExp(e)})),n.url_ignorelist=null==(t=n.url_ignorelist)?void 0:t.map((function(e){return new RegExp(e)})),n}},{key:"isEnabled",get:function(){var e,t,n=null==(e=this.instance.persistence)?void 0:e.props[ud],r=this._isDisabledServerSide;if(Ss(r)&&!Es(n)&&!this.instance._shouldDisableFlags())return!1;var i=null!==(t=this._isDisabledServerSide)&&void 0!==t?t:!!n;return!!this.instance.config.autocapture&&!i}}])}(),Cf="";var If=/[a-z0-9][a-z0-9-]+\.[a-z]{2,}$/i;function Pf(e,t){if(t){var n=function(e,t){if(void 0===t&&(t=Qo),Cf)return Cf;if(!t)return"";if(["localhost","127.0.0.1"].includes(e))return"";for(var n=e.split("."),r=Math.min(n.length,8),i="dmn_chk_"+ia();!Cf&&r--;){var o=n.slice(r).join("."),s=i+"=1;domain=."+o+";path=/";t.cookie=s+";max-age=3",t.cookie.includes(i)&&(t.cookie=s+";max-age=0",Cf=o)}return Cf}(e);if(!n){var r=function(e){var t=e.match(If);return t?t[0]:""}(e);r!==n&&Ps.info("Warning: cookie subdomain discovery mismatch",r,n),n=r}return n?"; domain=."+n:""}return""}var Ff={_is_supported:function(){return!!Qo},_error:function(e){Ps.error("cookieStore error: "+e)},_get:function(e){if(Qo){try{for(var t=e+"=",n=Qo.cookie.split(";").filter((function(e){return e.length})),r=0;r<n.length;r++){for(var i=n[r];" "==i.charAt(0);)i=i.substring(1,i.length);if(0===i.indexOf(t))return decodeURIComponent(i.substring(t.length,i.length))}}catch(e){}return null}},_parse:function(e){var t;try{t=JSON.parse(Ff._get(e))||{}}catch(e){}return t},_set:function(e,t,n,r,i){if(Qo)try{var o="",s="",a=Pf(Qo.location.hostname,r);if(n){var u=new Date;u.setTime(u.getTime()+24*n*60*60*1e3),o="; expires="+u.toUTCString()}i&&(s="; secure");var l=e+"="+encodeURIComponent(JSON.stringify(t))+o+"; SameSite=Lax; path=/"+a+s;return l.length>3686.4&&Ps.warn("cookieStore warning: large cookie, len="+l.length),Qo.cookie=l,l}catch(e){return}},_remove:function(e,t){try{Ff._set(e,"",-1,t)}catch(e){return}}},Rf=null,Mf={_is_supported:function(){if(!Ss(Rf))return Rf;var e=!0;if(ys(Vo))e=!1;else try{var t="__mplssupport__";Mf._set(t,"xyz"),'"xyz"'!==Mf._get(t)&&(e=!1),Mf._remove(t)}catch(t){e=!1}return e||Ps.error("localStorage unsupported; falling back to cookie store"),Rf=e,e},_error:function(e){Ps.error("localStorage error: "+e)},_get:function(e){try{return null==Vo?void 0:Vo.localStorage.getItem(e)}catch(e){Mf._error(e)}return null},_parse:function(e){try{return JSON.parse(Mf._get(e))||{}}catch(e){}return null},_set:function(e,t){try{null==Vo||Vo.localStorage.setItem(e,JSON.stringify(t))}catch(e){Mf._error(e)}},_remove:function(e){try{null==Vo||Vo.localStorage.removeItem(e)}catch(e){Mf._error(e)}}},Of=["distinct_id",kd,xd,Bd,Hd],Lf=ki({},Mf,{_parse:function(e){try{var t={};try{t=Ff._parse(e)||{}}catch(e){}var n=Ls(t,JSON.parse(Mf._get(e)||"{}"));return Mf._set(e,n),n}catch(e){}return null},_set:function(e,t,n,r,i,o){try{Mf._set(e,t,void 0,void 0,o);var s={};Of.forEach((function(e){t[e]&&(s[e]=t[e])})),Object.keys(s).length&&Ff._set(e,s,n,r,i,o)}catch(e){Mf._error(e)}},_remove:function(e,t){try{null==Vo||Vo.localStorage.removeItem(e),Ff._remove(e,t)}catch(e){Mf._error(e)}}}),Af={},qf={_is_supported:function(){return!0},_error:function(e){Ps.error("memoryStorage error: "+e)},_get:function(e){return Af[e]||null},_parse:function(e){return Af[e]||null},_set:function(e,t){Af[e]=t},_remove:function(e){delete Af[e]}},$f=null,Df={_is_supported:function(){if(!Ss($f))return $f;if($f=!0,ys(Vo))$f=!1;else try{var e="__support__";Df._set(e,"xyz"),'"xyz"'!==Df._get(e)&&($f=!1),Df._remove(e)}catch(e){$f=!1}return $f},_error:function(e){Ps.error("sessionStorage error: ",e)},_get:function(e){try{return null==Vo?void 0:Vo.sessionStorage.getItem(e)}catch(e){Df._error(e)}return null},_parse:function(e){try{return JSON.parse(Df._get(e))||null}catch(e){}return null},_set:function(e,t){try{null==Vo||Vo.sessionStorage.setItem(e,JSON.stringify(t))}catch(e){Df._error(e)}},_remove:function(e){try{null==Vo||Vo.sessionStorage.removeItem(e)}catch(e){Df._error(e)}}},Nf=function(e){return e[e.PENDING=-1]="PENDING",e[e.DENIED=0]="DENIED",e[e.GRANTED=1]="GRANTED",e}({}),Hf=function(){function e(e){this._instance=e}var t=e.prototype;return t.isOptedOut=function(){return this.consent===Nf.DENIED||this.consent===Nf.PENDING&&this._config.opt_out_capturing_by_default},t.isOptedIn=function(){return!this.isOptedOut()},t.optInOut=function(e){this._storage._set(this._storageKey,e?1:0,this._config.cookie_expiration,this._config.cross_subdomain_cookie,this._config.secure_cookie)},t.reset=function(){this._storage._remove(this._storageKey,this._config.cross_subdomain_cookie)},t._getDnt=function(){return!!this._config.respect_dnt&&!!Us([null==Yo?void 0:Yo.doNotTrack,null==Yo?void 0:Yo.msDoNotTrack,ns.doNotTrack],(function(e){return us([!0,1,"1","yes"],e)}))},wi(e,[{key:"_config",get:function(){return this._instance.config}},{key:"consent",get:function(){return this._getDnt()?Nf.DENIED:this._storedConsent}},{key:"_storageKey",get:function(){var e=this._instance.config,t=e.token;return(e.opt_out_capturing_cookie_prefix||"__ph_opt_in_out_")+t}},{key:"_storedConsent",get:function(){var e=this._storage._get(this._storageKey);return"1"===e?Nf.GRANTED:"0"===e?Nf.DENIED:Nf.PENDING}},{key:"_storage",get:function(){if(!this._persistentStore){var e=this._config.opt_out_capturing_persistence_type;this._persistentStore="localStorage"===e?Mf:Ff;var t="localStorage"===e?Ff:Mf;t._get(this._storageKey)&&(this._persistentStore._get(this._storageKey)||this.optInOut("1"===t._get(this._storageKey)),t._remove(this._storageKey,this._config.cross_subdomain_cookie))}return this._persistentStore}}])}(),Bf=Fs("[Dead Clicks]"),jf=function(){return!0},Uf=function(e){var t,n=!(null==(t=e.instance.persistence)||!t.get_property(pd)),r=e.instance.config.capture_dead_clicks;return Es(r)?r:n},Vf=function(){function e(e,t,n){this.instance=e,this.isEnabled=t,this.onCapture=n,this.startIfEnabled()}var t=e.prototype;return t.onRemoteConfig=function(e){var t;this.instance.persistence&&this.instance.persistence.register(((t={})[pd]=null==e?void 0:e.captureDeadClicks,t));this.startIfEnabled()},t.startIfEnabled=function(){var e=this;this.isEnabled(this)&&this._loadScript((function(){e._start()}))},t._loadScript=function(e){var t,n;null!=(t=ns.__PosthogExtensions__)&&t.initDeadClicksAutocapture&&e(),null==(n=ns.__PosthogExtensions__)||null==n.loadExternalDependency||n.loadExternalDependency(this.instance,"dead-clicks-autocapture",(function(t){t?Bf.error("failed to load script",t):e()}))},t._start=function(){var e;if(Qo){if(!this._lazyLoadedDeadClicksAutocapture&&null!=(e=ns.__PosthogExtensions__)&&e.initDeadClicksAutocapture){var t=gs(this.instance.config.capture_dead_clicks)?this.instance.config.capture_dead_clicks:{};t.__onCapture=this.onCapture,this._lazyLoadedDeadClicksAutocapture=ns.__PosthogExtensions__.initDeadClicksAutocapture(this.instance,t),this._lazyLoadedDeadClicksAutocapture.start(Qo),Bf.info("starting...")}}else Bf.error("`document` not found. Cannot start.")},t.stop=function(){this._lazyLoadedDeadClicksAutocapture&&(this._lazyLoadedDeadClicksAutocapture.stop(),this._lazyLoadedDeadClicksAutocapture=void 0,Bf.info("stopping..."))},wi(e,[{key:"lazyLoadedDeadClicksAutocapture",get:function(){return this._lazyLoadedDeadClicksAutocapture}}])}();function zf(e,t,n,r,i){return t>n&&(Ps.warn("min cannot be greater than max."),t=n),xs(e)?e>n?(r&&Ps.warn(r+" cannot be  greater than max: "+n+". Using max value instead."),n):e<t?(r&&Ps.warn(r+" cannot be less than min: "+t+". Using min value instead."),t):e:(r&&Ps.warn(r+" must be a number. using max or fallback. max: "+n+", fallback: "+i),zf(i||n,t,n,r))}var Wf=function(e){var t=this;this._buckets={},this._refillBuckets=function(){Object.keys(t._buckets).forEach((function(e){var n=t._getBucket(e)+t._refillRate;n>=t._bucketSize?delete t._buckets[e]:t._setBucket(e,n)}))},this._getBucket=function(e){return t._buckets[String(e)]},this._setBucket=function(e,n){t._buckets[String(e)]=n},this.consumeRateLimit=function(e){var n,r=null!==(n=t._getBucket(e))&&void 0!==n?n:t._bucketSize;if(0===(r=Math.max(r-1,0)))return!0;t._setBucket(e,r);var i=0===r;return i&&(null==t._onBucketRateLimited||t._onBucketRateLimited(e)),i},this._options=e,this._onBucketRateLimited=this._options._onBucketRateLimited,this._bucketSize=zf(this._options.bucketSize,0,100,"rate limiter bucket size"),this._refillRate=zf(this._options.refillRate,0,this._bucketSize,"rate limiter refill rate"),this._refillInterval=zf(this._options.refillInterval,0,864e5,"rate limiter refill interval"),setInterval((function(){t._refillBuckets()}),this._refillInterval)},Gf=Fs("[ExceptionAutocapture]"),Zf=function(){function e(e){var t,n,r,i=this;this._startCapturing=function(){var e;if(Vo&&i.isEnabled&&null!=(e=ns.__PosthogExtensions__)&&e.errorWrappingFunctions){var t=ns.__PosthogExtensions__.errorWrappingFunctions.wrapOnError,n=ns.__PosthogExtensions__.errorWrappingFunctions.wrapUnhandledRejection,r=ns.__PosthogExtensions__.errorWrappingFunctions.wrapConsoleError;try{!i._unwrapOnError&&i._config.capture_unhandled_errors&&(i._unwrapOnError=t(i.captureException.bind(i))),!i._unwrapUnhandledRejection&&i._config.capture_unhandled_rejections&&(i._unwrapUnhandledRejection=n(i.captureException.bind(i))),!i._unwrapConsoleError&&i._config.capture_console_errors&&(i._unwrapConsoleError=r(i.captureException.bind(i)))}catch(e){Gf.error("failed to start",e),i._stopCapturing()}}},this._instance=e,this._remoteEnabled=!(null==(t=this._instance.persistence)||!t.props[cd]),this._config=this._requiredConfig(),this._rateLimiter=new Wf({refillRate:null!==(n=this._instance.config.error_tracking.__exceptionRateLimiterRefillRate)&&void 0!==n?n:1,bucketSize:null!==(r=this._instance.config.error_tracking.__exceptionRateLimiterBucketSize)&&void 0!==r?r:10,refillInterval:1e4}),this.startIfEnabled()}var t=e.prototype;return t._requiredConfig=function(){var e=this._instance.config.capture_exceptions,t={capture_unhandled_errors:!1,capture_unhandled_rejections:!1,capture_console_errors:!1};return gs(e)?t=ki({},t,e):(ys(e)?this._remoteEnabled:e)&&(t=ki({},t,{capture_unhandled_errors:!0,capture_unhandled_rejections:!0})),t},t.startIfEnabled=function(){this.isEnabled&&(Gf.info("enabled"),this._loadScript(this._startCapturing))},t._loadScript=function(e){var t,n;null!=(t=ns.__PosthogExtensions__)&&t.errorWrappingFunctions&&e(),null==(n=ns.__PosthogExtensions__)||null==n.loadExternalDependency||n.loadExternalDependency(this._instance,"exception-autocapture",(function(t){if(t)return Gf.error("failed to load script",t);e()}))},t._stopCapturing=function(){var e,t,n;null==(e=this._unwrapOnError)||e.call(this),this._unwrapOnError=void 0,null==(t=this._unwrapUnhandledRejection)||t.call(this),this._unwrapUnhandledRejection=void 0,null==(n=this._unwrapConsoleError)||n.call(this),this._unwrapConsoleError=void 0},t.onRemoteConfig=function(e){var t,n=e.autocaptureExceptions;(this._remoteEnabled=!!n||!1,this._config=this._requiredConfig(),this._instance.persistence)&&this._instance.persistence.register(((t={})[cd]=this._remoteEnabled,t));this.startIfEnabled()},t.captureException=function(e){var t,n=this._instance.requestRouter.endpointFor("ui");e.$exception_personURL=n+"/project/"+this._instance.config.token+"/person/"+this._instance.get_distinct_id();var r=null!==(t=e.$exception_list[0].type)&&void 0!==t?t:"Exception";this._rateLimiter.consumeRateLimit(r)?Gf.info("Skipping exception capture because of client rate limiting.",{exception:e.$exception_list[0].type}):this._instance.exceptions.sendExceptionEvent(e)},wi(e,[{key:"isEnabled",get:function(){return this._config.capture_console_errors||this._config.capture_unhandled_errors||this._config.capture_unhandled_rejections}}])}(),Yf=function(){function e(e){var t;this._instance=e,this._lastPathname=(null==Vo||null==(t=Vo.location)?void 0:t.pathname)||""}var t=e.prototype;return t.startIfEnabled=function(){this.isEnabled&&(Ps.info("History API monitoring enabled, starting..."),this.monitorHistoryChanges())},t.stop=function(){this._popstateListener&&this._popstateListener(),this._popstateListener=void 0,Ps.info("History API monitoring stopped")},t.monitorHistoryChanges=function(){var e,t;if(Vo&&Vo.history){var n=this;null!=(e=Vo.history.pushState)&&e.__posthog_wrapped__||nd(Vo.history,"pushState",(function(e){return function(t,r,i){e.call(this,t,r,i),n._capturePageview("pushState")}})),null!=(t=Vo.history.replaceState)&&t.__posthog_wrapped__||nd(Vo.history,"replaceState",(function(e){return function(t,r,i){e.call(this,t,r,i),n._capturePageview("replaceState")}})),this._setupPopstateListener()}},t._capturePageview=function(e){try{var t,n=null==Vo||null==(t=Vo.location)?void 0:t.pathname;if(!n)return;n!==this._lastPathname&&this.isEnabled&&this._instance.capture("$pageview",{navigation_type:e}),this._lastPathname=n}catch(t){Ps.error("Error capturing "+e+" pageview",t)}},t._setupPopstateListener=function(){var e=this;if(!this._popstateListener){var t=function(){e._capturePageview("popstate")};Vs(Vo,"popstate",t),this._popstateListener=function(){Vo&&Vo.removeEventListener("popstate",t)}}},wi(e,[{key:"isEnabled",get:function(){return"history_change"===this._instance.config.capture_pageview}}])}();function Qf(e){var t,n;return(null==(t=JSON.stringify(e,(n=[],function(e,t){if(gs(t)){for(;n.length>0&&n[n.length-1]!==this;)n.pop();return n.includes(t)?"[Circular]":(n.push(t),t)}return t})))?void 0:t.length)||0}function Jf(e,t){if(void 0===t&&(t=6606028.8),e.size>=t&&e.data.length>1){var n=Math.floor(e.data.length/2),r=e.data.slice(0,n),i=e.data.slice(n);return[Jf({size:Qf(r),data:r,sessionId:e.sessionId,windowId:e.windowId}),Jf({size:Qf(i),data:i,sessionId:e.sessionId,windowId:e.windowId})].flatMap((function(e){return e}))}return[e]}var Xf=function(e){return e[e.DomContentLoaded=0]="DomContentLoaded",e[e.Load=1]="Load",e[e.FullSnapshot=2]="FullSnapshot",e[e.IncrementalSnapshot=3]="IncrementalSnapshot",e[e.Meta=4]="Meta",e[e.Custom=5]="Custom",e[e.Plugin=6]="Plugin",e}(Xf||{}),Kf=function(e){return e[e.Mutation=0]="Mutation",e[e.MouseMove=1]="MouseMove",e[e.MouseInteraction=2]="MouseInteraction",e[e.Scroll=3]="Scroll",e[e.ViewportResize=4]="ViewportResize",e[e.Input=5]="Input",e[e.TouchMove=6]="TouchMove",e[e.MediaInteraction=7]="MediaInteraction",e[e.StyleSheetRule=8]="StyleSheetRule",e[e.CanvasMutation=9]="CanvasMutation",e[e.Font=10]="Font",e[e.Log=11]="Log",e[e.Drag=12]="Drag",e[e.StyleDeclaration=13]="StyleDeclaration",e[e.Selection=14]="Selection",e[e.AdoptedStyleSheet=15]="AdoptedStyleSheet",e[e.CustomElement=16]="CustomElement",e}(Kf||{}),ep="[SessionRecording]",tp="redacted",np={initiatorTypes:["audio","beacon","body","css","early-hint","embed","fetch","frame","iframe","icon","image","img","input","link","navigation","object","ping","script","track","video","xmlhttprequest"],maskRequestFn:function(e){return e},recordHeaders:!1,recordBody:!1,recordInitialRequests:!1,recordPerformance:!1,performanceEntryTypeToObserve:["first-input","navigation","paint","resource"],payloadSizeLimitBytes:1e6,payloadHostDenyList:[".lr-ingest.io",".ingest.sentry.io",".clarity.ms","analytics.google.com","bam.nr-data.net"]},rp=["authorization","x-forwarded-for","authorization","cookie","set-cookie","x-api-key","x-real-ip","remote-addr","forwarded","proxy-authorization","x-csrf-token","x-csrftoken","x-xsrf-token"],ip=["password","secret","passwd","api_key","apikey","auth","credentials","mysql_pwd","privatekey","private_key","token"],op=["/s/","/e/","/i/"];function sp(e,t,n,r){if(ks(e))return e;var i=(null==t?void 0:t["content-length"])||function(e){return new Blob([e]).size}(e);return bs(i)&&(i=parseInt(i)),i>n?ep+" "+r+" body too large to record ("+i+" bytes)":e}function ap(e,t){if(ks(e))return e;var n=e;return _f(n,!1)||(n=ep+" "+t+" body "+tp),Os(ip,(function(e){var r,i;null!=(r=n)&&r.length&&-1!==(null==(i=n)?void 0:i.indexOf(e))&&(n=ep+" "+t+" body "+tp+" as might contain: "+e)})),n}var up=function(e,t){var n,r,i,o={payloadSizeLimitBytes:np.payloadSizeLimitBytes,performanceEntryTypeToObserve:[].concat(np.performanceEntryTypeToObserve),payloadHostDenyList:[].concat(t.payloadHostDenyList||[],np.payloadHostDenyList)},s=!1!==e.session_recording.recordHeaders&&t.recordHeaders,a=!1!==e.session_recording.recordBody&&t.recordBody,u=!1!==e.capture_performance&&t.recordPerformance,l=(n=o,i=Math.min(1e6,null!==(r=n.payloadSizeLimitBytes)&&void 0!==r?r:1e6),function(e){return null!=e&&e.requestBody&&(e.requestBody=sp(e.requestBody,e.requestHeaders,i,"Request")),null!=e&&e.responseBody&&(e.responseBody=sp(e.responseBody,e.responseHeaders,i,"Response")),e}),c=function(t){return l(function(e,t){var n,r=iu(e.name),i=0===t.indexOf("http")?null==(n=iu(t))?void 0:n.pathname:t;"/"===i&&(i="");var o=null==r?void 0:r.pathname.replace(i||"","");if(!(r&&o&&op.some((function(e){return 0===o.indexOf(e)}))))return e}((r=(n=t).requestHeaders,ks(r)||Os(Object.keys(null!=r?r:{}),(function(e){rp.includes(e.toLowerCase())&&(r[e]=tp)})),n),e.api_host));var n,r},d=vs(e.session_recording.maskNetworkRequestFn);return d&&vs(e.session_recording.maskCapturedNetworkRequestFn)&&Ps.warn("Both `maskNetworkRequestFn` and `maskCapturedNetworkRequestFn` are defined. `maskNetworkRequestFn` will be ignored."),d&&(e.session_recording.maskCapturedNetworkRequestFn=function(t){var n=e.session_recording.maskNetworkRequestFn({url:t.name});return ki({},t,{name:null==n?void 0:n.url})}),o.maskRequestFn=vs(e.session_recording.maskCapturedNetworkRequestFn)?function(t){var n,r=c(t);return r&&null!==(n=null==e.session_recording.maskCapturedNetworkRequestFn?void 0:e.session_recording.maskCapturedNetworkRequestFn(r))&&void 0!==n?n:void 0}:function(e){return function(e){if(!ys(e))return e.requestBody=ap(e.requestBody,"Request"),e.responseBody=ap(e.responseBody,"Response"),e}(c(e))},ki({},np,o,{recordHeaders:s,recordBody:a,recordPerformance:u,recordInitialRequests:u})},lp=function(e,t){var n,r,i=this;void 0===t&&(t={}),this._loggedTracker={},this._onNodeRateLimited=function(e){if(!i._loggedTracker[e]){i._loggedTracker[e]=!0;var t=i._getNode(e);null==i._options.onBlockedNode||i._options.onBlockedNode(e,t)}},this._getNodeOrRelevantParent=function(e){var t=i._getNode(e);if("svg"!==(null==t?void 0:t.nodeName)&&t instanceof Element){var n=t.closest("svg");if(n)return[i._rrweb.mirror.getId(n),n]}return[e,t]},this._getNode=function(e){return i._rrweb.mirror.getNode(e)},this._numberOfChanges=function(e){var t,n,r,i,o,s,a,u;return(null!==(t=null==(n=e.removes)?void 0:n.length)&&void 0!==t?t:0)+(null!==(r=null==(i=e.attributes)?void 0:i.length)&&void 0!==r?r:0)+(null!==(o=null==(s=e.texts)?void 0:s.length)&&void 0!==o?o:0)+(null!==(a=null==(u=e.adds)?void 0:u.length)&&void 0!==a?a:0)},this.throttleMutations=function(e){if(3!==e.type||0!==e.data.source)return e;var t=e.data,n=i._numberOfChanges(t);t.attributes&&(t.attributes=t.attributes.filter((function(e){var t=i._getNodeOrRelevantParent(e.id)[0];return!i._rateLimiter.consumeRateLimit(t)&&e})));var r=i._numberOfChanges(t);return 0!==r||n===r?e:void 0},this._rrweb=e,this._options=t,this._rateLimiter=new Wf({bucketSize:null!==(n=this._options.bucketSize)&&void 0!==n?n:100,refillRate:null!==(r=this._options.refillRate)&&void 0!==r?r:10,refillInterval:1e3,_onBucketRateLimited:this._onNodeRateLimited})};function cp(e,t){return function(e){for(var t=0,n=0;n<e.length;n++)t=(t<<5)-t+e.charCodeAt(n),t|=0;return Math.abs(t)}(e)%100<zf(100*t,0,100)}var dp="disabled",fp="sampled",pp="active",_p="buffering",hp="paused",vp="trigger",gp=vp+"_activated",mp=vp+"_pending",yp=vp+"_"+dp;function bp(e,t){return t.some((function(t){return"regex"===t.matching&&new RegExp(t.url).test(e)}))}var wp=function(){function e(e){this._matchers=e}var t=e.prototype;return t.triggerStatus=function(e){var t=this._matchers.map((function(t){return t.triggerStatus(e)}));return t.includes(gp)?gp:t.includes(mp)?mp:yp},t.stop=function(){this._matchers.forEach((function(e){return e.stop()}))},e}(),Sp=function(){function e(e){this._matchers=e}var t=e.prototype;return t.triggerStatus=function(e){for(var t,n=new Set,r=Si(this._matchers);!(t=r()).done;){var i=t.value;n.add(i.triggerStatus(e))}switch(n.delete(yp),n.size){case 0:return yp;case 1:return Array.from(n)[0];default:return mp}},t.stop=function(){this._matchers.forEach((function(e){return e.stop()}))},e}(),kp=function(){function e(){}var t=e.prototype;return t.triggerStatus=function(){return mp},t.stop=function(){},e}(),xp=function(){function e(e){this._urlTriggers=[],this._urlBlocklist=[],this.urlBlocked=!1,this._instance=e}var t=e.prototype;return t.onRemoteConfig=function(e){var t,n;this._urlTriggers=(null==(t=e.sessionRecording)?void 0:t.urlTriggers)||[],this._urlBlocklist=(null==(n=e.sessionRecording)?void 0:n.urlBlocklist)||[]},t._urlTriggerStatus=function(e){var t;return 0===this._urlTriggers.length?yp:(null==(t=this._instance)?void 0:t.get_property(Ed))===e?gp:mp},t.triggerStatus=function(e){var t=this._urlTriggerStatus(e),n=t===gp?gp:t===mp?mp:yp;return this._instance.register_for_session({$sdk_debug_replay_url_trigger_status:n}),n},t.checkUrlTriggerConditions=function(e,t,n){if(void 0!==Vo&&Vo.location.href){var r=Vo.location.href,i=this.urlBlocked,o=bp(r,this._urlBlocklist);i&&o||(o&&!i?e():!o&&i&&t(),bp(r,this._urlTriggers)&&n("url"))}},t.stop=function(){},e}(),Ep=function(){function e(e){this.linkedFlag=null,this.linkedFlagSeen=!1,this._flaglistenerCleanup=function(){},this._instance=e}var t=e.prototype;return t.triggerStatus=function(){var e=mp;return ks(this.linkedFlag)&&(e=yp),this.linkedFlagSeen&&(e=gp),this._instance.register_for_session({$sdk_debug_replay_linked_flag_trigger_status:e}),e},t.onRemoteConfig=function(e,t){var n,r=this;if(this.linkedFlag=(null==(n=e.sessionRecording)?void 0:n.linkedFlag)||null,!ks(this.linkedFlag)&&!this.linkedFlagSeen){var i=bs(this.linkedFlag)?this.linkedFlag:this.linkedFlag.flag,o=bs(this.linkedFlag)?null:this.linkedFlag.variant;this._flaglistenerCleanup=this._instance.onFeatureFlags((function(e,n){var s=!1;if(gs(n)&&i in n){var a=n[i];s=Es(a)?!0===a:o?a===o:!!a}r.linkedFlagSeen=s,s&&t(i,o)}))}},t.stop=function(){this._flaglistenerCleanup()},e}(),Tp=function(){function e(e){this._eventTriggers=[],this._instance=e}var t=e.prototype;return t.onRemoteConfig=function(e){var t;this._eventTriggers=(null==(t=e.sessionRecording)?void 0:t.eventTriggers)||[]},t._eventTriggerStatus=function(e){var t;return 0===this._eventTriggers.length?yp:(null==(t=this._instance)?void 0:t.get_property(Td))===e?gp:mp},t.triggerStatus=function(e){var t=this._eventTriggerStatus(e),n=t===gp?gp:t===mp?mp:yp;return this._instance.register_for_session({$sdk_debug_replay_event_trigger_status:n}),n},t.stop=function(){},e}();function Cp(e){return e.isRecordingEnabled?_p:dp}function Ip(e){if(!e.receivedFlags)return _p;if(!e.isRecordingEnabled)return dp;if(e.urlTriggerMatching.urlBlocked)return hp;var t=!0===e.isSampled,n=new wp([e.eventTriggerMatching,e.urlTriggerMatching,e.linkedFlagMatching]).triggerStatus(e.sessionId);return t?fp:n===gp?pp:n===mp?_p:!1===e.isSampled?dp:pp}function Pp(e){if(!e.receivedFlags)return _p;if(!e.isRecordingEnabled)return dp;if(e.urlTriggerMatching.urlBlocked)return hp;var t=new Sp([e.eventTriggerMatching,e.urlTriggerMatching,e.linkedFlagMatching]).triggerStatus(e.sessionId),n=t!==yp,r=Es(e.isSampled);return n&&t===mp?_p:n&&t===yp||r&&!e.isSampled?dp:!0===e.isSampled?fp:pp}var Fp="[SessionRecording]",Rp=Fs(Fp);function Mp(){var e;return null==ns||null==(e=ns.__PosthogExtensions__)||null==(e=e.rrweb)?void 0:e.record}var Op=3e5,Lp=[Kf.MouseMove,Kf.MouseInteraction,Kf.Scroll,Kf.ViewportResize,Kf.Input,Kf.TouchMove,Kf.MediaInteraction,Kf.Drag],Ap=function(e){return{rrwebMethod:e,enqueuedAt:Date.now(),attempt:1}};function qp(e){return function(e,t){for(var n="",r=0;r<e.length;){var i=e[r++];i<128||t?n+=String.fromCharCode(i):i<224?n+=String.fromCharCode((31&i)<<6|63&e[r++]):i<240?n+=String.fromCharCode((15&i)<<12|(63&e[r++])<<6|63&e[r++]):(i=((15&i)<<18|(63&e[r++])<<12|(63&e[r++])<<6|63&e[r++])-65536,n+=String.fromCharCode(55296|i>>10,56320|1023&i))}return n}(zu(Wu(JSON.stringify(e))),!0)}function $p(e){return e.type===Xf.Custom&&"sessionIdle"===e.data.tag}var Dp=function(){function e(e){var t=this;if(this._statusMatcher=Cp,this._receivedFlags=!1,this._queuedRRWebEvents=[],this._isIdle="unknown",this._lastActivityTimestamp=Date.now(),this._triggerMatching=new kp,this._removePageViewCaptureHook=void 0,this._onSessionIdListener=void 0,this._persistFlagsOnSessionListener=void 0,this._samplingSessionListener=void 0,this._removeEventTriggerCaptureHook=void 0,this._forceAllowLocalhostNetworkCapture=!1,this._onBeforeUnload=function(){t._flushBuffer()},this._onOffline=function(){t._tryAddCustomEvent("browser offline",{})},this._onOnline=function(){t._tryAddCustomEvent("browser online",{})},this._onVisibilityChange=function(){if(null!=Qo&&Qo.visibilityState){var e="window "+Qo.visibilityState;t._tryAddCustomEvent(e,{})}},this._instance=e,this._captureStarted=!1,this._endpoint="/s/",this._stopRrweb=void 0,this._receivedFlags=!1,!this._instance.sessionManager)throw Rp.error("started without valid sessionManager"),new Error(Fp+" started without valid sessionManager. This is a bug.");if(this._instance.config.__preview_experimental_cookieless_mode)throw new Error(Fp+" cannot be used with __preview_experimental_cookieless_mode.");this._linkedFlagMatching=new Ep(this._instance),this._urlTriggerMatching=new xp(this._instance),this._eventTriggerMatching=new Tp(this._instance);var n=this._sessionManager.checkAndGetSessionAndWindowId(),r=n.sessionId,i=n.windowId;this._sessionId=r,this._windowId=i,this._buffer=this._clearBuffer(),this._sessionIdleThresholdMilliseconds>=this._sessionManager.sessionTimeoutMs&&Rp.warn("session_idle_threshold_ms ("+this._sessionIdleThresholdMilliseconds+") is greater than the session timeout ("+this._sessionManager.sessionTimeoutMs+"). Session will never be detected as idle")}var t=e.prototype;return t.startIfEnabledOrStop=function(e){var t=this;this._isRecordingEnabled?(this._startCapture(e),Vs(Vo,"beforeunload",this._onBeforeUnload),Vs(Vo,"offline",this._onOffline),Vs(Vo,"online",this._onOnline),Vs(Vo,"visibilitychange",this._onVisibilityChange),this._setupSampling(),this._addEventTriggerListener(),ks(this._removePageViewCaptureHook)&&(this._removePageViewCaptureHook=this._instance.on("eventCaptured",(function(e){try{if("$pageview"===e.event){var n=null!=e&&e.properties.$current_url?t._maskUrl(null==e?void 0:e.properties.$current_url):"";if(!n)return;t._tryAddCustomEvent("$pageview",{href:n})}}catch(e){Rp.error("Could not add $pageview to rrweb session",e)}}))),this._onSessionIdListener||(this._onSessionIdListener=this._sessionManager.onSessionId((function(e,n,r){var i,o;r&&(t._tryAddCustomEvent("$session_id_change",{sessionId:e,windowId:n,changeReason:r}),null==(i=t._instance)||null==(i=i.persistence)||i.unregister(Td),null==(o=t._instance)||null==(o=o.persistence)||o.unregister(Ed))})))):this.stopRecording()},t.stopRecording=function(){var e,t,n,r;this._captureStarted&&this._stopRrweb&&(this._stopRrweb(),this._stopRrweb=void 0,this._captureStarted=!1,null==Vo||Vo.removeEventListener("beforeunload",this._onBeforeUnload),null==Vo||Vo.removeEventListener("offline",this._onOffline),null==Vo||Vo.removeEventListener("online",this._onOnline),null==Vo||Vo.removeEventListener("visibilitychange",this._onVisibilityChange),this._clearBuffer(),clearInterval(this._fullSnapshotTimer),null==(e=this._removePageViewCaptureHook)||e.call(this),this._removePageViewCaptureHook=void 0,null==(t=this._removeEventTriggerCaptureHook)||t.call(this),this._removeEventTriggerCaptureHook=void 0,null==(n=this._onSessionIdListener)||n.call(this),this._onSessionIdListener=void 0,null==(r=this._samplingSessionListener)||r.call(this),this._samplingSessionListener=void 0,this._eventTriggerMatching.stop(),this._urlTriggerMatching.stop(),this._linkedFlagMatching.stop(),Rp.info("stopped"))},t._resetSampling=function(){var e;null==(e=this._instance.persistence)||e.unregister(xd)},t._makeSamplingDecision=function(e){var t,n,r=this._sessionId!==e,i=this._sampleRate;if(xs(i)){var o=this._isSampled,s=r||!Es(o),a=s?cp(e,i):o;s&&(a?this._reportStarted(fp):Rp.warn("Sample rate ("+i+") has determined that this sessionId ("+e+") will not be sent to the server."),this._tryAddCustomEvent("samplingDecisionMade",{sampleRate:i,isSampled:a})),null==(t=this._instance.persistence)||t.register(((n={})[xd]=a,n))}else this._resetSampling()},t.onRemoteConfig=function(e){var t,n,r,i,o=this;(this._tryAddCustomEvent("$remote_config_received",e),this._persistRemoteConfig(e),null!=(t=e.sessionRecording)&&t.endpoint)&&(this._endpoint=null==(i=e.sessionRecording)?void 0:i.endpoint);this._setupSampling(),"any"===(null==(n=e.sessionRecording)?void 0:n.triggerMatchType)?(this._statusMatcher=Ip,this._triggerMatching=new wp([this._eventTriggerMatching,this._urlTriggerMatching])):(this._statusMatcher=Pp,this._triggerMatching=new Sp([this._eventTriggerMatching,this._urlTriggerMatching])),this._instance.register_for_session({$sdk_debug_replay_remote_trigger_matching_config:null==(r=e.sessionRecording)?void 0:r.triggerMatchType}),this._urlTriggerMatching.onRemoteConfig(e),this._eventTriggerMatching.onRemoteConfig(e),this._linkedFlagMatching.onRemoteConfig(e,(function(e,t){o._reportStarted("linked_flag_matched",{flag:e,variant:t})})),this._receivedFlags=!0,this.startIfEnabledOrStop()},t._setupSampling=function(){var e=this;xs(this._sampleRate)&&ks(this._samplingSessionListener)&&(this._samplingSessionListener=this._sessionManager.onSessionId((function(t){e._makeSamplingDecision(t)})))},t._persistRemoteConfig=function(e){var t=this;if(this._instance.persistence){var n,r=this._instance.persistence,i=function(){var n,i,o,s,a,u,l,c,d,f,p=null==(n=e.sessionRecording)?void 0:n.sampleRate,_=ks(p)?null:parseFloat(p);ks(_)&&t._resetSampling();var h=null==(i=e.sessionRecording)?void 0:i.minimumDurationMilliseconds;r.register(((f={})[hd]=!!e.sessionRecording,f[vd]=null==(o=e.sessionRecording)?void 0:o.consoleLogRecordingEnabled,f[gd]=ki({capturePerformance:e.capturePerformance},null==(s=e.sessionRecording)?void 0:s.networkPayloadCapture),f[md]=null==(a=e.sessionRecording)?void 0:a.masking,f[yd]={enabled:null==(u=e.sessionRecording)?void 0:u.recordCanvas,fps:null==(l=e.sessionRecording)?void 0:l.canvasFps,quality:null==(c=e.sessionRecording)?void 0:c.canvasQuality},f[bd]=_,f[wd]=ys(h)?null:h,f[Sd]=null==(d=e.sessionRecording)?void 0:d.scriptConfig,f))};i(),null==(n=this._persistFlagsOnSessionListener)||n.call(this),this._persistFlagsOnSessionListener=this._sessionManager.onSessionId(i)}},t.log=function(e,t){var n;void 0===t&&(t="log"),null==(n=this._instance.sessionRecording)||n.onRRwebEmit({type:6,data:{plugin:"rrweb/console@1",payload:{level:t,trace:[],payload:[JSON.stringify(e)]}},timestamp:Date.now()})},t._startCapture=function(e){var t=this;if(!ys(Object.assign)&&!ys(Array.from)&&!(this._captureStarted||this._instance.config.disable_session_recording||this._instance.consent.isOptedOut())){var n;if(this._captureStarted=!0,this._sessionManager.checkAndGetSessionAndWindowId(),Mp())this._onScriptLoaded();else null==(n=ns.__PosthogExtensions__)||null==n.loadExternalDependency||n.loadExternalDependency(this._instance,this._scriptName,(function(e){if(e)return Rp.error("could not load recorder",e);t._onScriptLoaded()}));Rp.info("starting"),this.status===pp&&this._reportStarted(e||"recording_initialized")}},t._isInteractiveEvent=function(e){var t;return 3===e.type&&-1!==Lp.indexOf(null==(t=e.data)?void 0:t.source)},t._updateWindowAndSessionIds=function(e){var t=this._isInteractiveEvent(e);t||this._isIdle||e.timestamp-this._lastActivityTimestamp>this._sessionIdleThresholdMilliseconds&&(this._isIdle=!0,clearInterval(this._fullSnapshotTimer),this._tryAddCustomEvent("sessionIdle",{eventTimestamp:e.timestamp,lastActivityTimestamp:this._lastActivityTimestamp,threshold:this._sessionIdleThresholdMilliseconds,bufferLength:this._buffer.data.length,bufferSize:this._buffer.size}),this._flushBuffer());var n=!1;if(t&&(this._lastActivityTimestamp=e.timestamp,this._isIdle)){var r="unknown"===this._isIdle;this._isIdle=!1,r||(this._tryAddCustomEvent("sessionNoLongerIdle",{reason:"user activity",type:e.type}),n=!0)}if(!this._isIdle){var i=this._sessionManager.checkAndGetSessionAndWindowId(!t,e.timestamp),o=i.windowId,s=i.sessionId,a=this._sessionId!==s,u=this._windowId!==o;this._windowId=o,this._sessionId=s,a||u?(this.stopRecording(),this.startIfEnabledOrStop("session_id_changed")):n&&this._scheduleFullSnapshot()}},t._tryRRWebMethod=function(e){try{return e.rrwebMethod(),!0}catch(t){return this._queuedRRWebEvents.length<10?this._queuedRRWebEvents.push({enqueuedAt:e.enqueuedAt||Date.now(),attempt:e.attempt++,rrwebMethod:e.rrwebMethod}):Rp.warn("could not emit queued rrweb event.",t,e),!1}},t._tryAddCustomEvent=function(e,t){return this._tryRRWebMethod(Ap((function(){return Mp().addCustomEvent(e,t)})))},t._tryTakeFullSnapshot=function(){return this._tryRRWebMethod(Ap((function(){return Mp().takeFullSnapshot()})))},t._onScriptLoaded=function(){for(var e,t,n,r,i=this,o={blockClass:"ph-no-capture",blockSelector:void 0,ignoreClass:"ph-ignore-input",maskTextClass:"ph-mask",maskTextSelector:void 0,maskTextFn:void 0,maskAllInputs:!0,maskInputOptions:{password:!0},maskInputFn:void 0,slimDOMOptions:{},collectFonts:!1,inlineStylesheet:!0,recordCrossOriginIframes:!1},s=this._instance.config.session_recording,a=0,u=Object.entries(s||{});a<u.length;a++){var l=u[a],c=l[0],d=l[1];c in o&&("maskInputOptions"===c?o.maskInputOptions=ki({password:!0},d):o[c]=d)}(this._canvasRecording&&this._canvasRecording.enabled&&(o.recordCanvas=!0,o.sampling={canvas:this._canvasRecording.fps},o.dataURLOptions={type:"image/webp",quality:this._canvasRecording.quality}),this._masking)&&(o.maskAllInputs=null===(t=this._masking.maskAllInputs)||void 0===t||t,o.maskTextSelector=null!==(n=this._masking.maskTextSelector)&&void 0!==n?n:void 0,o.blockSelector=null!==(r=this._masking.blockSelector)&&void 0!==r?r:void 0);var f=Mp();if(f){this._mutationThrottler=null!==(e=this._mutationThrottler)&&void 0!==e?e:new lp(f,{refillRate:this._instance.config.session_recording.__mutationThrottlerRefillRate,bucketSize:this._instance.config.session_recording.__mutationThrottlerBucketSize,onBlockedNode:function(e,t){var n="Too many mutations on node '"+e+"'. Rate limiting. This could be due to SVG animations or something similar";Rp.info(n,{node:t}),i.log(Fp+" "+n,"warn")}});var p=this._gatherRRWebPlugins();this._stopRrweb=f(ki({emit:function(e){i.onRRwebEmit(e)},plugins:p},o)),this._lastActivityTimestamp=Date.now(),this._isIdle=Es(this._isIdle)?this._isIdle:"unknown",this._tryAddCustomEvent("$session_options",{sessionRecordingOptions:o,activePlugins:p.map((function(e){return null==e?void 0:e.name}))}),this._tryAddCustomEvent("$posthog_config",{config:this._instance.config})}else Rp.error("onScriptLoaded was called but rrwebRecord is not available. This indicates something has gone wrong.")},t._scheduleFullSnapshot=function(){var e=this;if(this._fullSnapshotTimer&&clearInterval(this._fullSnapshotTimer),!0!==this._isIdle){var t=this._fullSnapshotIntervalMillis;t&&(this._fullSnapshotTimer=setInterval((function(){e._tryTakeFullSnapshot()}),t))}},t._gatherRRWebPlugins=function(){var e,t,n=[],r=null==(e=ns.__PosthogExtensions__)||null==(e=e.rrwebPlugins)?void 0:e.getRecordConsolePlugin;r&&this._isConsoleLogCaptureEnabled&&n.push(r());var i=null==(t=ns.__PosthogExtensions__)||null==(t=t.rrwebPlugins)?void 0:t.getRecordNetworkPlugin;this._networkPayloadCapture&&vs(i)&&(!ru.includes(location.hostname)||this._forceAllowLocalhostNetworkCapture?n.push(i(up(this._instance.config,this._networkPayloadCapture))):Rp.info("NetworkCapture not started because we are on localhost."));return n},t.onRRwebEmit=function(e){var t,n=this;if(this._processQueuedEvents(),e&&gs(e)){if(e.type===Xf.Meta){var r=this._maskUrl(e.data.href);if(this._lastHref=r,!r)return;e.data.href=r}else this._pageViewFallBack();if(this._urlTriggerMatching.checkUrlTriggerConditions((function(){return n._pauseRecording()}),(function(){return n._resumeRecording()}),(function(e){return n._activateTrigger(e)})),!this._urlTriggerMatching.urlBlocked||function(e){return e.type===Xf.Custom&&"recording paused"===e.data.tag}(e)){e.type===Xf.FullSnapshot&&this._scheduleFullSnapshot(),e.type===Xf.FullSnapshot&&this._receivedFlags&&this._triggerMatching.triggerStatus(this.sessionId)===mp&&this._clearBuffer();var i=this._mutationThrottler?this._mutationThrottler.throttleMutations(e):e;if(i){var o=function(e){var t=e;if(t&&gs(t)&&6===t.type&&gs(t.data)&&"rrweb/console@1"===t.data.plugin){t.data.payload.payload.length>10&&(t.data.payload.payload=t.data.payload.payload.slice(0,10),t.data.payload.payload.push("...[truncated]"));for(var n=[],r=0;r<t.data.payload.payload.length;r++)t.data.payload.payload[r]&&t.data.payload.payload[r].length>2e3?n.push(t.data.payload.payload[r].slice(0,2e3)+"...[truncated]"):n.push(t.data.payload.payload[r]);return t.data.payload.payload=n,e}return e}(i);if(this._updateWindowAndSessionIds(o),!0!==this._isIdle||$p(o)){if($p(o)){var s=o.data.payload;if(s){var a=s.lastActivityTimestamp,u=s.threshold;o.timestamp=a+u}}var l=null===(t=this._instance.config.session_recording.compress_events)||void 0===t||t?function(e){if(Qf(e)<1024)return e;try{if(e.type===Xf.FullSnapshot)return ki({},e,{data:qp(e.data),cv:"2024-10"});if(e.type===Xf.IncrementalSnapshot&&e.data.source===Kf.Mutation)return ki({},e,{cv:"2024-10",data:ki({},e.data,{texts:qp(e.data.texts),attributes:qp(e.data.attributes),removes:qp(e.data.removes),adds:qp(e.data.adds)})});if(e.type===Xf.IncrementalSnapshot&&e.data.source===Kf.StyleSheetRule)return ki({},e,{cv:"2024-10",data:ki({},e.data,{adds:e.data.adds?qp(e.data.adds):void 0,removes:e.data.removes?qp(e.data.removes):void 0})})}catch(e){Rp.error("could not compress event - will use uncompressed event",e)}return e}(o):o,c={$snapshot_bytes:Qf(l),$snapshot_data:l,$session_id:this._sessionId,$window_id:this._windowId};this.status!==dp?this._captureSnapshotBuffered(c):this._clearBuffer()}}}}},t._pageViewFallBack=function(){if(!this._instance.config.capture_pageview&&Vo){var e=this._maskUrl(Vo.location.href);this._lastHref!==e&&(this._tryAddCustomEvent("$url_changed",{href:e}),this._lastHref=e)}},t._processQueuedEvents=function(){var e=this;if(this._queuedRRWebEvents.length){var t=[].concat(this._queuedRRWebEvents);this._queuedRRWebEvents=[],t.forEach((function(t){Date.now()-t.enqueuedAt<=2e3&&e._tryRRWebMethod(t)}))}},t._maskUrl=function(e){var t=this._instance.config.session_recording;if(t.maskNetworkRequestFn){var n,r={url:e};return null==(n=r=t.maskNetworkRequestFn(r))?void 0:n.url}return e},t._clearBuffer=function(){return this._buffer={size:0,data:[],sessionId:this._sessionId,windowId:this._windowId},this._buffer},t._flushBuffer=function(){var e=this;this._flushBufferTimer&&(clearTimeout(this._flushBufferTimer),this._flushBufferTimer=void 0);var t=this._minimumDuration,n=this._sessionDuration,r=xs(n)&&n>=0,i=xs(t)&&r&&n<t;if(this.status===_p||this.status===hp||this.status===dp||i)return this._flushBufferTimer=setTimeout((function(){e._flushBuffer()}),2e3),this._buffer;this._buffer.data.length>0&&Jf(this._buffer).forEach((function(t){e._captureSnapshot({$snapshot_bytes:t.size,$snapshot_data:t.data,$session_id:t.sessionId,$window_id:t.windowId,$lib:"web",$lib_version:rs.LIB_VERSION})}));return this._clearBuffer()},t._captureSnapshotBuffered=function(e){var t,n=this,r=2+((null==(t=this._buffer)?void 0:t.data.length)||0);!this._isIdle&&(this._buffer.size+e.$snapshot_bytes+r>943718.4||this._buffer.sessionId!==this._sessionId)&&(this._buffer=this._flushBuffer()),this._buffer.size+=e.$snapshot_bytes,this._buffer.data.push(e.$snapshot_data),this._flushBufferTimer||this._isIdle||(this._flushBufferTimer=setTimeout((function(){n._flushBuffer()}),2e3))},t._captureSnapshot=function(e){this._instance.capture("$snapshot",e,{_url:this._instance.requestRouter.endpointFor("api",this._endpoint),_noTruncate:!0,_batchKey:"recordings",skip_client_rate_limiting:!0})},t._activateTrigger=function(e){var t,n;this._triggerMatching.triggerStatus(this.sessionId)===mp&&(null==(t=this._instance)||null==(t=t.persistence)||t.register(((n={})["url"===e?Ed:Td]=this._sessionId,n)),this._flushBuffer(),this._reportStarted(e+"_trigger_matched"))},t._pauseRecording=function(){this._urlTriggerMatching.urlBlocked||(this._urlTriggerMatching.urlBlocked=!0,clearInterval(this._fullSnapshotTimer),Rp.info("recording paused due to URL blocker"),this._tryAddCustomEvent("recording paused",{reason:"url blocker"}))},t._resumeRecording=function(){this._urlTriggerMatching.urlBlocked&&(this._urlTriggerMatching.urlBlocked=!1,this._tryTakeFullSnapshot(),this._scheduleFullSnapshot(),this._tryAddCustomEvent("recording resumed",{reason:"left blocked url"}),Rp.info("recording resumed"))},t._addEventTriggerListener=function(){var e=this;0!==this._eventTriggerMatching._eventTriggers.length&&ks(this._removeEventTriggerCaptureHook)&&(this._removeEventTriggerCaptureHook=this._instance.on("eventCaptured",(function(t){try{e._eventTriggerMatching._eventTriggers.includes(t.event)&&e._activateTrigger("event")}catch(e){Rp.error("Could not activate event trigger",e)}})))},t.overrideLinkedFlag=function(){this._linkedFlagMatching.linkedFlagSeen=!0,this._tryTakeFullSnapshot(),this._reportStarted("linked_flag_overridden")},t.overrideSampling=function(){var e,t;null==(e=this._instance.persistence)||e.register(((t={})[xd]=!0,t)),this._tryTakeFullSnapshot(),this._reportStarted("sampling_overridden")},t.overrideTrigger=function(e){this._activateTrigger(e)},t._reportStarted=function(e,t){this._instance.register_for_session({$session_recording_start_reason:e}),Rp.info(e.replace("_"," "),t),us(["recording_initialized","session_id_changed"],e)||this._tryAddCustomEvent(e,t)},wi(e,[{key:"sessionId",get:function(){return this._sessionId}},{key:"_sessionIdleThresholdMilliseconds",get:function(){return this._instance.config.session_recording.session_idle_threshold_ms||3e5}},{key:"started",get:function(){return this._captureStarted}},{key:"_sessionManager",get:function(){if(!this._instance.sessionManager)throw new Error(Fp+" must be started with a valid sessionManager.");return this._instance.sessionManager}},{key:"_fullSnapshotIntervalMillis",get:function(){var e,t;return this._triggerMatching.triggerStatus(this.sessionId)===mp?6e4:null!==(e=null==(t=this._instance.config.session_recording)?void 0:t.full_snapshot_interval_millis)&&void 0!==e?e:Op}},{key:"_isSampled",get:function(){var e=this._instance.get_property(xd);return Es(e)?e:null}},{key:"_sessionDuration",get:function(){var e,t,n=null==(e=this._buffer)?void 0:e.data[(null==(t=this._buffer)?void 0:t.data.length)-1],r=this._sessionManager.checkAndGetSessionAndWindowId(!0).sessionStartTimestamp;return n?n.timestamp-r:null}},{key:"_isRecordingEnabled",get:function(){var e=!!this._instance.get_property(hd),t=!this._instance.config.disable_session_recording;return Vo&&e&&t}},{key:"_isConsoleLogCaptureEnabled",get:function(){var e=!!this._instance.get_property(vd),t=this._instance.config.enable_recording_console_log;return null!=t?t:e}},{key:"_canvasRecording",get:function(){var e,t,n,r,i,o,s=this._instance.config.session_recording.captureCanvas,a=this._instance.get_property(yd),u=null!==(e=null!==(t=null==s?void 0:s.recordCanvas)&&void 0!==t?t:null==a?void 0:a.enabled)&&void 0!==e&&e,l=null!==(n=null!==(r=null==s?void 0:s.canvasFps)&&void 0!==r?r:null==a?void 0:a.fps)&&void 0!==n?n:4,c=null!==(i=null!==(o=null==s?void 0:s.canvasQuality)&&void 0!==o?o:null==a?void 0:a.quality)&&void 0!==i?i:.4;if("string"==typeof c){var d=parseFloat(c);c=isNaN(d)?.4:d}return{enabled:u,fps:zf(l,0,12,"canvas recording fps",4),quality:zf(c,0,1,"canvas recording quality",.4)}}},{key:"_networkPayloadCapture",get:function(){var e,t,n=this._instance.get_property(gd),r={recordHeaders:null==(e=this._instance.config.session_recording)?void 0:e.recordHeaders,recordBody:null==(t=this._instance.config.session_recording)?void 0:t.recordBody},i=(null==r?void 0:r.recordHeaders)||(null==n?void 0:n.recordHeaders),o=(null==r?void 0:r.recordBody)||(null==n?void 0:n.recordBody),s=gs(this._instance.config.capture_performance)?this._instance.config.capture_performance.network_timing:this._instance.config.capture_performance,a=!!(Es(s)?s:null==n?void 0:n.capturePerformance);return i||o||a?{recordHeaders:i,recordBody:o,recordPerformance:a}:void 0}},{key:"_masking",get:function(){var e,t,n,r,i,o,s=this._instance.get_property(md),a={maskAllInputs:null==(e=this._instance.config.session_recording)?void 0:e.maskAllInputs,maskTextSelector:null==(t=this._instance.config.session_recording)?void 0:t.maskTextSelector,blockSelector:null==(n=this._instance.config.session_recording)?void 0:n.blockSelector},u=null!==(r=null==a?void 0:a.maskAllInputs)&&void 0!==r?r:null==s?void 0:s.maskAllInputs,l=null!==(i=null==a?void 0:a.maskTextSelector)&&void 0!==i?i:null==s?void 0:s.maskTextSelector,c=null!==(o=null==a?void 0:a.blockSelector)&&void 0!==o?o:null==s?void 0:s.blockSelector;return ys(u)&&ys(l)&&ys(c)?void 0:{maskAllInputs:null==u||u,maskTextSelector:l,blockSelector:c}}},{key:"_sampleRate",get:function(){var e=this._instance.get_property(bd);return xs(e)?e:null}},{key:"_minimumDuration",get:function(){var e=this._instance.get_property(wd);return xs(e)?e:null}},{key:"status",get:function(){return this._receivedFlags?this._statusMatcher({receivedFlags:this._receivedFlags,isRecordingEnabled:this._isRecordingEnabled,isSampled:this._isSampled,urlTriggerMatching:this._urlTriggerMatching,eventTriggerMatching:this._eventTriggerMatching,linkedFlagMatching:this._linkedFlagMatching,sessionId:this.sessionId}):_p}},{key:"_scriptName",get:function(){var e;return(null==(e=this._instance)||null==(e=e.persistence)||null==(e=e.get_property(Sd))?void 0:e.script)||"recorder"}},{key:"sdkDebugProperties",get:function(){var e=this._sessionManager.checkAndGetSessionAndWindowId(!0).sessionStartTimestamp;return{$recording_status:this.status,$sdk_debug_replay_internal_buffer_length:this._buffer.data.length,$sdk_debug_replay_internal_buffer_size:this._buffer.size,$sdk_debug_current_session_duration:this._sessionDuration,$sdk_debug_session_start:e}}}])}(),Np=Fs("[SegmentIntegration]");function Hp(e,t){var n=e.config.segment;if(!n)return t();!function(e,t){var n=e.config.segment;if(!n)return t();var r=function(n){var r=function(){return n.anonymousId()||ia()};e.config.get_device_id=r,n.id()&&(e.register({distinct_id:n.id(),$device_id:r()}),e.persistence.set_property(Ad,"identified")),t()},i=n.user();"then"in i&&vs(i.then)?i.then((function(e){return r(e)})):r(i)}(e,(function(){n.register(function(e){Promise&&Promise.resolve||Np.warn("This browser does not have Promise support, and can not use the segment integration");var t=function(t,n){if(!n)return t;t.event.userId||t.event.anonymousId===e.get_distinct_id()||(Np.info("No userId set, resetting PostHog"),e.reset()),t.event.userId&&t.event.userId!==e.get_distinct_id()&&(Np.info("UserId set, identifying with PostHog"),e.identify(t.event.userId));var r=e.calculateEventProperties(n,t.event.properties);return t.event.properties=Object.assign({},r,t.event.properties),t};return{name:"PostHog JS",type:"enrichment",version:"1.0.0",isLoaded:function(){return!0},load:function(){return Promise.resolve()},track:function(e){return t(e,e.event.event)},page:function(e){return t(e,"$pageview")},identify:function(e){return t(e,"$identify")},screen:function(e){return t(e,"$screen")}}}(e)).then((function(){t()}))}))}var Bp="posthog-js";function jp(e,t){var n=void 0===t?{}:t,r=n.organization,i=n.projectId,o=n.prefix,s=n.severityAllowList,a=void 0===s?["error"]:s;return function(t){var n,s,u,l,c;if(!("*"===a||a.includes(t.level))||!e.__loaded)return t;t.tags||(t.tags={});var d=e.requestRouter.endpointFor("ui","/project/"+e.config.token+"/person/"+e.get_distinct_id());t.tags["PostHog Person URL"]=d,e.sessionRecordingStarted()&&(t.tags["PostHog Recording URL"]=e.get_session_replay_url({withTimestamp:!0}));var f=(null==(n=t.exception)?void 0:n.values)||[],p=f.map((function(e){return ki({},e,{stacktrace:e.stacktrace?ki({},e.stacktrace,{type:"raw",frames:(e.stacktrace.frames||[]).map((function(e){return ki({},e,{platform:"web:javascript"})}))}):void 0})})),_={$exception_message:(null==(s=f[0])?void 0:s.value)||t.message,$exception_type:null==(u=f[0])?void 0:u.type,$exception_personURL:d,$exception_level:t.level,$exception_list:p,$sentry_event_id:t.event_id,$sentry_exception:t.exception,$sentry_exception_message:(null==(l=f[0])?void 0:l.value)||t.message,$sentry_exception_type:null==(c=f[0])?void 0:c.type,$sentry_tags:t.tags};return r&&i&&(_.$sentry_url=(o||"https://sentry.io/organizations/")+r+"/issues/?project="+i+"&query="+t.event_id),e.exceptions.sendExceptionEvent(_),t}}var Up=function(e,t,n,r,i){this.name=Bp,this.setupOnce=function(o){o(jp(e,{organization:t,projectId:n,prefix:r,severityAllowList:i}))}},Vp=null!=Vo&&Vo.location?uu(Vo.location.hash,"__posthog")||uu(location.hash,"state"):null,zp="_postHogToolbarParams",Wp=Fs("[Toolbar]"),Gp=function(e){return e[e.UNINITIALIZED=0]="UNINITIALIZED",e[e.LOADING=1]="LOADING",e[e.LOADED=2]="LOADED",e}(Gp||{}),Zp=function(){function e(e){this.instance=e}var t=e.prototype;return t._setToolbarState=function(e){ns.ph_toolbar_state=e},t._getToolbarState=function(){var e;return null!==(e=ns.ph_toolbar_state)&&void 0!==e?e:Gp.UNINITIALIZED},t.maybeLoadToolbar=function(e,t,n){if(void 0===e&&(e=void 0),void 0===t&&(t=void 0),void 0===n&&(n=void 0),!Vo||!Qo)return!1;e=null!=e?e:Vo.location,n=null!=n?n:Vo.history;try{if(!t){try{Vo.localStorage.setItem("test","test"),Vo.localStorage.removeItem("test")}catch(e){return!1}t=null==Vo?void 0:Vo.localStorage}var r,i=Vp||uu(e.hash,"__posthog")||uu(e.hash,"state"),o=i?$s((function(){return JSON.parse(atob(decodeURIComponent(i)))}))||$s((function(){return JSON.parse(decodeURIComponent(i))})):null;return o&&"ph_authorize"===o.action?((r=o).source="url",r&&Object.keys(r).length>0&&(o.desiredHash?e.hash=o.desiredHash:n?n.replaceState(n.state,"",e.pathname+e.search):e.hash="")):((r=JSON.parse(t.getItem(zp)||"{}")).source="localstorage",delete r.userIntent),!(!r.token||this.instance.config.token!==r.token)&&(this.loadToolbar(r),!0)}catch(e){return!1}},t._callLoadToolbar=function(e){var t=ns.ph_load_toolbar||ns.ph_load_editor;!ks(t)&&vs(t)?t(e,this.instance):Wp.warn("No toolbar load function found")},t.loadToolbar=function(e){var t=this,n=!(null==Qo||!Qo.getElementById(jd));if(!Vo||n)return!1;var r="custom"===this.instance.requestRouter.region&&this.instance.config.advanced_disable_toolbar_metrics,i=ki({token:this.instance.config.token},e,{apiURL:this.instance.requestRouter.endpointFor("ui")},r?{instrument:!1}:{});if(Vo.localStorage.setItem(zp,JSON.stringify(ki({},i,{source:void 0}))),this._getToolbarState()===Gp.LOADED)this._callLoadToolbar(i);else if(this._getToolbarState()===Gp.UNINITIALIZED){var o;this._setToolbarState(Gp.LOADING),null==(o=ns.__PosthogExtensions__)||null==o.loadExternalDependency||o.loadExternalDependency(this.instance,"toolbar",(function(e){if(e)return Wp.error("[Toolbar] Failed to load",e),void t._setToolbarState(Gp.UNINITIALIZED);t._setToolbarState(Gp.LOADED),t._callLoadToolbar(i)})),Vs(Vo,"turbolinks:load",(function(){t._setToolbarState(Gp.UNINITIALIZED),t.loadToolbar(i)}))}return!0},t._loadEditor=function(e){return this.loadToolbar(e)},t.maybeLoadEditor=function(e,t,n){return void 0===e&&(e=void 0),void 0===t&&(t=void 0),void 0===n&&(n=void 0),this.maybeLoadToolbar(e,t,n)},e}(),Yp=Fs("[TracingHeaders]"),Qp=function(){function e(e){var t=this;this._restoreXHRPatch=void 0,this._restoreFetchPatch=void 0,this._startCapturing=function(){var e,n;ys(t._restoreXHRPatch)&&(null==(e=ns.__PosthogExtensions__)||null==(e=e.tracingHeadersPatchFns)||e._patchXHR(t._instance.get_distinct_id(),t._instance.sessionManager));ys(t._restoreFetchPatch)&&(null==(n=ns.__PosthogExtensions__)||null==(n=n.tracingHeadersPatchFns)||n._patchFetch(t._instance.get_distinct_id(),t._instance.sessionManager))},this._instance=e}var t=e.prototype;return t._loadScript=function(e){var t,n;null!=(t=ns.__PosthogExtensions__)&&t.tracingHeadersPatchFns&&e(),null==(n=ns.__PosthogExtensions__)||null==n.loadExternalDependency||n.loadExternalDependency(this._instance,"tracing-headers",(function(t){if(t)return Yp.error("failed to load script",t);e()}))},t.startIfEnabledOrStop=function(){var e,t;this._instance.config.__add_tracing_headers?this._loadScript(this._startCapturing):(null==(e=this._restoreXHRPatch)||e.call(this),null==(t=this._restoreFetchPatch)||t.call(this),this._restoreXHRPatch=void 0,this._restoreFetchPatch=void 0)},e}(),Jp=Fs("[Web Vitals]"),Xp=9e5,Kp=function(){function e(e){var t,n=this;this._enabledServerSide=!1,this._initialized=!1,this._buffer={url:void 0,metrics:[],firstMetricTimestamp:void 0},this._flushToCapture=function(){clearTimeout(n._delayedFlushTimer),0!==n._buffer.metrics.length&&(n._instance.capture("$web_vitals",n._buffer.metrics.reduce((function(e,t){var n;return ki({},e,((n={})["$web_vitals_"+t.name+"_event"]=ki({},t),n["$web_vitals_"+t.name+"_value"]=t.value,n))}),{})),n._buffer={url:void 0,metrics:[],firstMetricTimestamp:void 0})},this._addToBuffer=function(e){var t,r=null==(t=n._instance.sessionManager)?void 0:t.checkAndGetSessionAndWindowId(!0);if(ys(r))Jp.error("Could not read session ID. Dropping metrics!");else{n._buffer=n._buffer||{url:void 0,metrics:[],firstMetricTimestamp:void 0};var i=n._currentURL();if(!ys(i))if(ks(null==e?void 0:e.name)||ks(null==e?void 0:e.value))Jp.error("Invalid metric received",e);else if(n._maxAllowedValue&&e.value>=n._maxAllowedValue)Jp.error("Ignoring metric with value >= "+n._maxAllowedValue,e);else n._buffer.url!==i&&(n._flushToCapture(),n._delayedFlushTimer=setTimeout(n._flushToCapture,n.flushToCaptureTimeoutMs)),ys(n._buffer.url)&&(n._buffer.url=i),n._buffer.firstMetricTimestamp=ys(n._buffer.firstMetricTimestamp)?Date.now():n._buffer.firstMetricTimestamp,e.attribution&&e.attribution.interactionTargetElement&&(e.attribution.interactionTargetElement=void 0),n._buffer.metrics.push(ki({},e,{$current_url:i,$session_id:r.sessionId,$window_id:r.windowId,timestamp:Date.now()})),n._buffer.metrics.length===n.allowedMetrics.length&&n._flushToCapture()}},this._startCapturing=function(){var e,t,r,i,o=ns.__PosthogExtensions__;if(!ys(o)&&!ys(o.postHogWebVitalsCallbacks)){var s=o.postHogWebVitalsCallbacks;e=s.onLCP,t=s.onCLS,r=s.onFCP,i=s.onINP}e&&t&&r&&i?(n.allowedMetrics.indexOf("LCP")>-1&&e(n._addToBuffer.bind(n)),n.allowedMetrics.indexOf("CLS")>-1&&t(n._addToBuffer.bind(n)),n.allowedMetrics.indexOf("FCP")>-1&&r(n._addToBuffer.bind(n)),n.allowedMetrics.indexOf("INP")>-1&&i(n._addToBuffer.bind(n)),n._initialized=!0):Jp.error("web vitals callbacks not loaded - not starting")},this._instance=e,this._enabledServerSide=!(null==(t=this._instance.persistence)||!t.props[fd]),this.startIfEnabled()}var t=e.prototype;return t.startIfEnabled=function(){this.isEnabled&&!this._initialized&&(Jp.info("enabled, starting..."),this._loadScript(this._startCapturing))},t.onRemoteConfig=function(e){var t,n,r=gs(e.capturePerformance)&&!!e.capturePerformance.web_vitals,i=gs(e.capturePerformance)?e.capturePerformance.web_vitals_allowed_metrics:void 0;this._instance.persistence&&(this._instance.persistence.register(((t={})[fd]=r,t)),this._instance.persistence.register(((n={})[_d]=i,n)));this._enabledServerSide=r,this.startIfEnabled()},t._loadScript=function(e){var t,n;null!=(t=ns.__PosthogExtensions__)&&t.postHogWebVitalsCallbacks&&e(),null==(n=ns.__PosthogExtensions__)||null==n.loadExternalDependency||n.loadExternalDependency(this._instance,"web-vitals",(function(t){t?Jp.error("failed to load script",t):e()}))},t._currentURL=function(){var e=Vo?Vo.location.href:void 0;return e||Jp.error("Could not determine current URL"),e},wi(e,[{key:"allowedMetrics",get:function(){var e,t,n=gs(this._instance.config.capture_performance)?null==(e=this._instance.config.capture_performance)?void 0:e.web_vitals_allowed_metrics:void 0;return ys(n)?(null==(t=this._instance.persistence)?void 0:t.props[_d])||["CLS","FCP","INP","LCP"]:n}},{key:"flushToCaptureTimeoutMs",get:function(){return(gs(this._instance.config.capture_performance)?this._instance.config.capture_performance.web_vitals_delayed_flush_ms:void 0)||5e3}},{key:"_maxAllowedValue",get:function(){var e=gs(this._instance.config.capture_performance)&&xs(this._instance.config.capture_performance.__web_vitals_max_value)?this._instance.config.capture_performance.__web_vitals_max_value:Xp;return 0<e&&e<=6e4?Xp:e}},{key:"isEnabled",get:function(){var e=null==Jo?void 0:Jo.protocol;if("http:"!==e&&"https:"!==e)return Jp.info("Web Vitals are disabled on non-http/https protocols"),!1;var t=gs(this._instance.config.capture_performance)?this._instance.config.capture_performance.web_vitals:Es(this._instance.config.capture_performance)?this._instance.config.capture_performance:void 0;return Es(t)?t:this._enabledServerSide}}])}(),e_=Fs("[Heatmaps]");function t_(e){return gs(e)&&"clientX"in e&&"clientY"in e&&xs(e.clientX)&&xs(e.clientY)}var n_=function(){function e(e){var t;this.rageclicks=new bf,this._enabledServerSide=!1,this._initialized=!1,this._flushInterval=null,this.instance=e,this._enabledServerSide=!(null==(t=this.instance.persistence)||!t.props[ld])}var t=e.prototype;return t.startIfEnabled=function(){if(this.isEnabled){if(this._initialized)return;e_.info("starting..."),this._setupListeners(),this._flushInterval=setInterval(this._flush.bind(this),this.flushIntervalMilliseconds)}else{var e,t;clearInterval(null!==(e=this._flushInterval)&&void 0!==e?e:void 0),null==(t=this._deadClicksCapture)||t.stop(),this.getAndClearBuffer()}},t.onRemoteConfig=function(e){var t,n=!!e.heatmaps;this.instance.persistence&&this.instance.persistence.register(((t={})[ld]=n,t));this._enabledServerSide=n,this.startIfEnabled()},t.getAndClearBuffer=function(){var e=this._buffer;return this._buffer=void 0,e},t._onDeadClick=function(e){this._onClick(e.originalEvent,"deadclick")},t._setupListeners=function(){var e=this;Vo&&Qo&&(Vs(Vo,"beforeunload",this._flush.bind(this)),Vs(Qo,"click",(function(t){return e._onClick(t||(null==Vo?void 0:Vo.event))}),{capture:!0}),Vs(Qo,"mousemove",(function(t){return e._onMouseMove(t||(null==Vo?void 0:Vo.event))}),{capture:!0}),this._deadClicksCapture=new Vf(this.instance,jf,this._onDeadClick.bind(this)),this._deadClicksCapture.startIfEnabled(),this._initialized=!0)},t._getProperties=function(e,t){var n=this.instance.scrollManager.scrollY(),r=this.instance.scrollManager.scrollX(),i=this.instance.scrollManager.scrollElement(),o=function(e,t,n){for(var r=e;r&&Wd(r)&&!Gd(r,"body");){if(r===n)return!1;if(us(t,null==Vo?void 0:Vo.getComputedStyle(r).position))return!0;r=rf(r)}return!1}(tf(e),["fixed","sticky"],i);return{x:e.clientX+(o?0:r),y:e.clientY+(o?0:n),target_fixed:o,type:t}},t._onClick=function(e,t){var n;if(void 0===t&&(t="click"),!zd(e.target)&&t_(e)){var r=this._getProperties(e,t);null!=(n=this.rageclicks)&&n.isRageClick(e.clientX,e.clientY,(new Date).getTime())&&this._capture(ki({},r,{type:"rageclick"})),this._capture(r)}},t._onMouseMove=function(e){var t=this;!zd(e.target)&&t_(e)&&(clearTimeout(this._mouseMoveTimeout),this._mouseMoveTimeout=setTimeout((function(){t._capture(t._getProperties(e,"mousemove"))}),500))},t._capture=function(e){if(Vo){var t=Vo.location.href;this._buffer=this._buffer||{},this._buffer[t]||(this._buffer[t]=[]),this._buffer[t].push(e)}},t._flush=function(){this._buffer&&!ms(this._buffer)&&this.instance.capture("$$heatmap",{$heatmap_data:this.getAndClearBuffer()})},wi(e,[{key:"flushIntervalMilliseconds",get:function(){var e=5e3;return gs(this.instance.config.capture_heatmaps)&&this.instance.config.capture_heatmaps.flush_interval_milliseconds&&(e=this.instance.config.capture_heatmaps.flush_interval_milliseconds),e}},{key:"isEnabled",get:function(){return ys(this.instance.config.capture_heatmaps)?ys(this.instance.config.enable_heatmaps)?this._enabledServerSide:this.instance.config.enable_heatmaps:!1!==this.instance.config.capture_heatmaps}}])}(),r_=function(){function e(e){this._instance=e}var t=e.prototype;return t.doPageView=function(e,t){var n,r=this._previousPageViewProperties(e,t);return this._currentPageview={pathname:null!==(n=null==Vo?void 0:Vo.location.pathname)&&void 0!==n?n:"",pageViewId:t,timestamp:e},this._instance.scrollManager.resetContext(),r},t.doPageLeave=function(e){var t;return this._previousPageViewProperties(e,null==(t=this._currentPageview)?void 0:t.pageViewId)},t.doEvent=function(){var e;return{$pageview_id:null==(e=this._currentPageview)?void 0:e.pageViewId}},t._previousPageViewProperties=function(e,t){var n=this._currentPageview;if(!n)return{$pageview_id:t};var r={$pageview_id:t,$prev_pageview_id:n.pageViewId},i=this._instance.scrollManager.getContext();if(i&&!this._instance.config.disable_scroll_properties){var o=i.maxScrollHeight,s=i.lastScrollY,a=i.maxScrollY,u=i.maxContentHeight,l=i.lastContentY,c=i.maxContentY;if(!(ys(o)||ys(s)||ys(a)||ys(u)||ys(l)||ys(c))){o=Math.ceil(o),s=Math.ceil(s),a=Math.ceil(a),u=Math.ceil(u),l=Math.ceil(l),c=Math.ceil(c);var d=o<=1?1:zf(s/o,0,1),f=o<=1?1:zf(a/o,0,1),p=u<=1?1:zf(l/u,0,1),_=u<=1?1:zf(c/u,0,1);r=Ls(r,{$prev_pageview_last_scroll:s,$prev_pageview_last_scroll_percentage:d,$prev_pageview_max_scroll:a,$prev_pageview_max_scroll_percentage:f,$prev_pageview_last_content:l,$prev_pageview_last_content_percentage:p,$prev_pageview_max_content:c,$prev_pageview_max_content_percentage:_})}}return n.pathname&&(r.$prev_pageview_pathname=n.pathname),n.timestamp&&(r.$prev_pageview_duration=(e.getTime()-n.timestamp.getTime())/1e3),r},e}(),i_=Fs("[Error tracking]"),o_=function(){function e(e){var t,n;this._suppressionRules=[],this._instance=e,this._suppressionRules=null!==(t=null==(n=this._instance.persistence)?void 0:n.get_property(dd))&&void 0!==t?t:[]}var t=e.prototype;return t.onRemoteConfig=function(e){var t,n,r,i=null!==(t=null==(n=e.errorTracking)?void 0:n.suppressionRules)&&void 0!==t?t:[];(this._suppressionRules=i,this._instance.persistence)&&this._instance.persistence.register(((r={})[dd]=this._suppressionRules,r))},t.sendExceptionEvent=function(e){this._matchesSuppressionRule(e)?i_.info("Skipping exception capture because a suppression rule matched"):this._instance.capture("$exception",e,{_noTruncate:!0,_batchKey:"exceptionEvent"})},t._matchesSuppressionRule=function(e){var t=e.$exception_list;if(!t||!hs(t)||0===t.length)return!1;var n=t.reduce((function(e,t){var n=t.type,r=t.value;return bs(n)&&n.length>0&&e.$exception_types.push(n),bs(r)&&r.length>0&&e.$exception_values.push(r),e}),{$exception_types:[],$exception_values:[]});return this._suppressionRules.some((function(e){var t=e.values.map((function(e){var t,r=nl[e.operator],i=hs(e.value)?e.value:[e.value],o=null!==(t=n[e.key])&&void 0!==t?t:[];return i.length>0&&r(i,o)}));return"OR"===e.type?t.some(Boolean):t.every(Boolean)}))},e}(),s_="https?://(.*)",a_=["gclid","gclsrc","dclid","gbraid","wbraid","fbclid","msclkid","twclid","li_fat_id","igshid","ttclid","rdt_cid","epik","qclid","sccid","irclid","_kx"],u_=As(["utm_source","utm_medium","utm_campaign","utm_content","utm_term","gad_source","mc_cid"],a_),l_="<masked>",c_=["li_fat_id"];function d_(e,t,n){if(!Qo)return{};var r,i=t?As([],a_,n||[]):[],o=f_(au(Qo.URL,i,l_),e),s=(r={},Os(c_,(function(e){var t=Ff._get(e);r[e]=t||null})),r);return Ls(s,o)}function f_(e,t){var n=u_.concat(t||[]),r={};return Os(n,(function(t){var n=su(e,t);r[t]=n||null})),r}function p_(e){var t=function(e){return e?0===e.search(s_+"google.([^/?]*)")?"google":0===e.search(s_+"bing.com")?"bing":0===e.search(s_+"yahoo.com")?"yahoo":0===e.search(s_+"duckduckgo.com")?"duckduckgo":null:null}(e),n="yahoo"!=t?"q":"p",r={};if(!Ss(t)){r.$search_engine=t;var i=Qo?su(Qo.referrer,n):"";i.length&&(r.ph_keyword=i)}return r}function __(){return navigator.language||navigator.userLanguage}function h_(){return(null==Qo?void 0:Qo.referrer)||"$direct"}function v_(e,t){var n=e?As([],a_,t||[]):[],r=null==Jo?void 0:Jo.href.substring(0,1e3);return{r:h_().substring(0,1e3),u:r?au(r,n,l_):void 0}}function g_(e){var t,n=e.r,r=e.u,i={$referrer:n,$referring_domain:null==n?void 0:"$direct"==n?"$direct":null==(t=iu(n))?void 0:t.host};if(r){i.$current_url=r;var o=iu(r);i.$host=null==o?void 0:o.host,i.$pathname=null==o?void 0:o.pathname;var s=f_(r);Ls(i,s)}if(n){var a=p_(n);Ls(i,a)}return i}function m_(){try{return Intl.DateTimeFormat().resolvedOptions().timeZone}catch(e){return}}function y_(){try{return(new Date).getTimezoneOffset()}catch(e){return}}function b_(e,t){if(!ts)return{};var n,r=e?As([],a_,t||[]):[],i=function(e){for(var t=0;t<eu.length;t++){var n=eu[t],r=n[0],i=n[1],o=r.exec(e),s=o&&(vs(i)?i(o,e):i);if(s)return s}return["",""]}(ts),o=i[0],s=i[1];return Ls(Ns({$os:o,$os_version:s,$browser:Ja(ts,navigator.vendor),$device:tu(ts),$device_type:nu(ts),$timezone:m_(),$timezone_offset:y_()}),{$current_url:au(null==Jo?void 0:Jo.href,r,l_),$host:null==Jo?void 0:Jo.host,$pathname:null==Jo?void 0:Jo.pathname,$raw_user_agent:ts.length>1e3?ts.substring(0,997)+"...":ts,$browser_version:Ka(ts,navigator.vendor),$browser_language:__(),$browser_language_prefix:(n=__(),"string"==typeof n?n.split("-")[0]:void 0),$screen_height:null==Vo?void 0:Vo.screen.height,$screen_width:null==Vo?void 0:Vo.screen.width,$viewport_height:null==Vo?void 0:Vo.innerHeight,$viewport_width:null==Vo?void 0:Vo.innerWidth,$lib:"web",$lib_version:rs.LIB_VERSION,$insert_id:Math.random().toString(36).substring(2,10)+Math.random().toString(36).substring(2,10),$time:Date.now()/1e3})}var w_=Fs("[FeatureFlags]"),S_="$active_feature_flags",k_="$override_feature_flags",x_="$feature_flag_payloads",E_="$override_feature_flag_payloads",T_="$feature_flag_request_id",C_=function(e){for(var t,n={},r=Si(qs(e||{}));!(t=r()).done;){var i=t.value,o=i[0],s=i[1];s&&(n[o]=s)}return n},I_=function(e){var t=e.flags;return t?(e.featureFlags=Object.fromEntries(Object.keys(t).map((function(e){var n;return[e,null!==(n=t[e].variant)&&void 0!==n?n:t[e].enabled]}))),e.featureFlagPayloads=Object.fromEntries(Object.keys(t).filter((function(e){return t[e].enabled})).filter((function(e){var n;return null==(n=t[e].metadata)?void 0:n.payload})).map((function(e){var n;return[e,null==(n=t[e].metadata)?void 0:n.payload]})))):w_.warn("Using an older version of the feature flags endpoint. Please upgrade your PostHog server to the latest version"),e},P_=function(e){return e.FeatureFlags="feature_flags",e.Recordings="recordings",e}({});var F_=new Set(["7c6f7b45","66c1f69c","2727f65a","f3287528","8cc9a311","eb9f671b","c0e1c6f9","057989ec","723f4019","7b102104","563359d3","bad973ea","f6f2c4f4","59454a61","89ad1076","4edd0da1","26c52e72","a970bd2e","89cf4454","16e2b4e7","fba0e7b6","301c8488","bc65d69e","fe66a3c5","37926ca6","52a196df","d32a7577","42c4c9ef","6883bd5a","04809ff7","e59430a8","61be3dd8","7fa5500b","bf027177","8cfdba9b","96f6df5f","569798e9","0ebc61a5","1b5d7b92","17ebb0a4","f97ea965","85cc817b","3044dfc1","0c3fe5c3","b1f95fa3","8a6342e8","72365c68","12d34ad9","733853ec","3beeb69a","0645bb64","32de7f98","5dcbee21","3fe85053","ad960278","9466e5dd","7ca97b2d","2ee2a65c","28fde5f2","85c52f49","0ad823f4","f11b6cc9","aacf8af9","ab3e62b3","3a85ff15","8a67d3c4","f5e91ef1","4b873698","c5dae949","5b643d76","9599c892","34377448","2189e408","3be9ad53","1a14ce7c","2a164ded","8d53ea86","53bdb37d","bfc3f590","8df38ede","bdb81e49","38fde5c0","8d707e6d","73cbc496","f9d8a5ef","d3a9f8c4","a980d8cd","5bcfe086","e4818f68","4f11fb39","a13c6ae3","150c7fbb","98f3d658","f84f7377","1924dd9c","1f6b63b3","24748755","7c0f717c","8a87f11b","49f57f22","3c9e9234","3772f65b","dff631b6","cd609d40","f853c7f7","952db5ee","c5aa8a79","2d21b6fd","79b7164c","4110e26c","a7d3b43f","84e1b8f6","75cc0998","07f78e33","10ca9b1a","ce441b18","01eb8256","c0ac4b67","8e8e5216","db7943dd","fa133a95","498a4508","21bbda67","7dbfed69","be3ec24c","fc80b8e2"]);var R_=function(){function e(e){this._override_warning=!1,this._hasLoadedFlags=!1,this._requestInFlight=!1,this._reloadingDisabled=!1,this._additionalReloadRequested=!1,this._flagsCalled=!1,this._flagsLoadedFromRemote=!1,this._instance=e,this.featureFlagEventHandlers=[]}var t=e.prototype;return t.flags=function(){if(this._instance.config.__preview_remote_config)this._flagsCalled=!0;else{var e=!this._reloadDebouncer&&(this._instance.config.advanced_disable_feature_flags||this._instance.config.advanced_disable_feature_flags_on_first_load);this._callFlagsEndpoint({disableFlags:e})}},t.getFlags=function(){return Object.keys(this.getFlagVariants())},t.getFlagsWithDetails=function(){var e=this._instance.get_property(Pd),t=this._instance.get_property(k_),n=this._instance.get_property(E_);if(!n&&!t)return e||{};for(var r,i=Ls({},e||{}),o=Si([].concat(new Set([].concat(Object.keys(n||{}),Object.keys(t||{})))));!(r=o()).done;){var s,a,u=r.value,l=i[u],c=null==t?void 0:t[u],d=ys(c)?null!==(s=null==l?void 0:l.enabled)&&void 0!==s&&s:!!c,f=ys(c)?l.variant:"string"==typeof c?c:void 0,p=null==n?void 0:n[u],_=ki({},l,{enabled:d,variant:d?null!=f?f:null==l?void 0:l.variant:void 0});if(d!==(null==l?void 0:l.enabled)&&(_.original_enabled=null==l?void 0:l.enabled),f!==(null==l?void 0:l.variant)&&(_.original_variant=null==l?void 0:l.variant),p)_.metadata=ki({},null==l?void 0:l.metadata,{payload:p,original_payload:null==l||null==(a=l.metadata)?void 0:a.payload});i[u]=_}return this._override_warning||(w_.warn(" Overriding feature flag details!",{flagDetails:e,overriddenPayloads:n,finalDetails:i}),this._override_warning=!0),i},t.getFlagVariants=function(){var e=this._instance.get_property(Cd),t=this._instance.get_property(k_);if(!t)return e||{};for(var n=Ls({},e),r=Object.keys(t),i=0;i<r.length;i++)n[r[i]]=t[r[i]];return this._override_warning||(w_.warn(" Overriding feature flags!",{enabledFlags:e,overriddenFlags:t,finalFlags:n}),this._override_warning=!0),n},t.getFlagPayloads=function(){var e=this._instance.get_property(x_),t=this._instance.get_property(E_);if(!t)return e||{};for(var n=Ls({},e||{}),r=Object.keys(t),i=0;i<r.length;i++)n[r[i]]=t[r[i]];return this._override_warning||(w_.warn(" Overriding feature flag payloads!",{flagPayloads:e,overriddenPayloads:t,finalPayloads:n}),this._override_warning=!0),n},t.reloadFeatureFlags=function(){var e=this;this._reloadingDisabled||this._instance.config.advanced_disable_feature_flags||this._reloadDebouncer||(this._reloadDebouncer=setTimeout((function(){e._callFlagsEndpoint()}),5))},t._clearDebouncer=function(){clearTimeout(this._reloadDebouncer),this._reloadDebouncer=void 0},t.ensureFlagsLoaded=function(){this._hasLoadedFlags||this._requestInFlight||this._reloadDebouncer||this.reloadFeatureFlags()},t.setAnonymousDistinctId=function(e){this.$anon_distinct_id=e},t.setReloadingPaused=function(e){this._reloadingDisabled=e},t._callFlagsEndpoint=function(e){var t,n=this;if(this._clearDebouncer(),!this._instance._shouldDisableFlags())if(this._requestInFlight)this._additionalReloadRequested=!0;else{var r={token:this._instance.config.token,distinct_id:this._instance.get_distinct_id(),groups:this._instance.getGroups(),$anon_distinct_id:this.$anon_distinct_id,person_properties:ki({},(null==(t=this._instance.persistence)?void 0:t.get_initial_props())||{},this._instance.get_property(Fd)||{}),group_properties:this._instance.get_property(Rd)};(null!=e&&e.disableFlags||this._instance.config.advanced_disable_feature_flags)&&(r.disable_flags=!0);var i=this._instance.config.__preview_flags_v2&&this._instance.config.__preview_remote_config,o=function(e){var t=function(e){for(var t=2166136261,n=0;n<e.length;n++)t^=e.charCodeAt(n),t+=(t<<1)+(t<<4)+(t<<7)+(t<<8)+(t<<24);return("00000000"+(t>>>0).toString(16)).slice(-8)}(e);return null==F_?void 0:F_.has(t)}(this._instance.config.token)?"/decide?v=4":i?"/flags/?v=2":"/flags/?v=2&config=true",s=this._instance.config.advanced_only_evaluate_survey_feature_flags?"&only_evaluate_survey_feature_flags=true":"",a=this._instance.requestRouter.endpointFor("api",o+s);i&&(r.timezone=m_()),this._requestInFlight=!0,this._instance._send_request({method:"POST",url:a,data:r,compression:this._instance.config.disable_compression?void 0:ss.Base64,timeout:this._instance.config.feature_flag_request_timeout_ms,callback:function(e){var t,i,o=!0;(200===e.statusCode&&(n._additionalReloadRequested||(n.$anon_distinct_id=void 0),o=!1),n._requestInFlight=!1,n._flagsCalled)||(n._flagsCalled=!0,n._instance._onRemoteConfig(null!==(i=e.json)&&void 0!==i?i:{}));if(!r.disable_flags||n._additionalReloadRequested)if(n._flagsLoadedFromRemote=!o,e.json&&null!=(t=e.json.quotaLimited)&&t.includes(P_.FeatureFlags))w_.warn("You have hit your feature flags quota limit, and will not be able to load feature flags until the quota is reset.  Please visit https://posthog.com/docs/billing/limits-alerts to learn more.");else{var s;if(!r.disable_flags)n.receivedFeatureFlags(null!==(s=e.json)&&void 0!==s?s:{},o);n._additionalReloadRequested&&(n._additionalReloadRequested=!1,n._callFlagsEndpoint())}}})}},t.getFeatureFlag=function(e,t){if(void 0===t&&(t={}),this._hasLoadedFlags||this.getFlags()&&this.getFlags().length>0){var n=this.getFlagVariants()[e],r=""+n,i=this._instance.get_property(T_)||void 0,o=this._instance.get_property(Ld)||{};if((t.send_event||!("send_event"in t))&&(!(e in o)||!o[e].includes(r))){var s,a,u,l,c,d,f,p,_,h;hs(o[e])?o[e].push(r):o[e]=[r],null==(s=this._instance.persistence)||s.register(((a={})[Ld]=o,a));var v=this.getFeatureFlagDetails(e),g={$feature_flag:e,$feature_flag_response:n,$feature_flag_payload:this.getFeatureFlagPayload(e)||null,$feature_flag_request_id:i,$feature_flag_bootstrapped_response:(null==(u=this._instance.config.bootstrap)||null==(u=u.featureFlags)?void 0:u[e])||null,$feature_flag_bootstrapped_payload:(null==(l=this._instance.config.bootstrap)||null==(l=l.featureFlagPayloads)?void 0:l[e])||null,$used_bootstrap_value:!this._flagsLoadedFromRemote};ys(null==v||null==(c=v.metadata)?void 0:c.version)||(g.$feature_flag_version=v.metadata.version);var m,y=null!==(d=null==v||null==(f=v.reason)?void 0:f.description)&&void 0!==d?d:null==v||null==(p=v.reason)?void 0:p.code;if(y&&(g.$feature_flag_reason=y),null!=v&&null!=(_=v.metadata)&&_.id&&(g.$feature_flag_id=v.metadata.id),ys(null==v?void 0:v.original_variant)&&ys(null==v?void 0:v.original_enabled)||(g.$feature_flag_original_response=ys(v.original_variant)?v.original_enabled:v.original_variant),null!=v&&null!=(h=v.metadata)&&h.original_payload)g.$feature_flag_original_payload=null==v||null==(m=v.metadata)?void 0:m.original_payload;this._instance.capture("$feature_flag_called",g)}return n}w_.warn('getFeatureFlag for key "'+e+"\" failed. Feature flags didn't load in time.")},t.getFeatureFlagDetails=function(e){return this.getFlagsWithDetails()[e]},t.getFeatureFlagPayload=function(e){return this.getFlagPayloads()[e]},t.getRemoteConfigPayload=function(e,t){var n=this._instance.config.token;this._instance._send_request({method:"POST",url:this._instance.requestRouter.endpointFor("api","/flags/?v=2&config=true"),data:{distinct_id:this._instance.get_distinct_id(),token:n},compression:this._instance.config.disable_compression?void 0:ss.Base64,timeout:this._instance.config.feature_flag_request_timeout_ms,callback:function(n){var r,i=null==(r=n.json)?void 0:r.featureFlagPayloads;t((null==i?void 0:i[e])||void 0)}})},t.isFeatureEnabled=function(e,t){if(void 0===t&&(t={}),this._hasLoadedFlags||this.getFlags()&&this.getFlags().length>0)return!!this.getFeatureFlag(e,t);w_.warn('isFeatureEnabled for key "'+e+"\" failed. Feature flags didn't load in time.")},t.addFeatureFlagsHandler=function(e){this.featureFlagEventHandlers.push(e)},t.removeFeatureFlagsHandler=function(e){this.featureFlagEventHandlers=this.featureFlagEventHandlers.filter((function(t){return t!==e}))},t.receivedFeatureFlags=function(e,t){if(this._instance.persistence){this._hasLoadedFlags=!0;var n=this.getFlagVariants(),r=this.getFlagPayloads(),i=this.getFlagsWithDetails();!function(e,t,n,r,i){var o,s;void 0===n&&(n={}),void 0===r&&(r={}),void 0===i&&(i={});var a=I_(e),u=a.flags,l=a.featureFlags,c=a.featureFlagPayloads;if(l){var d=e.requestId;if(hs(l)){var f;w_.warn("v1 of the feature flags endpoint is deprecated. Please use the latest version.");var p={};if(l)for(var _=0;_<l.length;_++)p[l[_]]=!0;t&&t.register(((f={})[S_]=l,f[Cd]=p,f))}else{var h=l,v=c,g=u;e.errorsWhileComputingFlags&&(h=ki({},n,h),v=ki({},r,v),g=ki({},i,g)),t&&t.register(ki(((o={})[S_]=Object.keys(C_(h)),o[Cd]=h||{},o[x_]=v||{},o[Pd]=g||{},o),d?((s={})[T_]=d,s):{}))}}}(e,this._instance.persistence,n,r,i),this._fireFeatureFlagsCallbacks(t)}},t.override=function(e,t){void 0===t&&(t=!1),w_.warn("override is deprecated. Please use overrideFeatureFlags instead."),this.overrideFeatureFlags({flags:e,suppressWarning:t})},t.overrideFeatureFlags=function(e){if(!this._instance.__loaded||!this._instance.persistence)return w_.uninitializedWarning("posthog.featureFlags.overrideFeatureFlags");if(!1===e)return this._instance.persistence.unregister(k_),this._instance.persistence.unregister(E_),void this._fireFeatureFlagsCallbacks();if(e&&"object"==typeof e&&("flags"in e||"payloads"in e)){var t,n=e;if(this._override_warning=Boolean(null!==(t=n.suppressWarning)&&void 0!==t&&t),"flags"in n)if(!1===n.flags)this._instance.persistence.unregister(k_);else if(n.flags)if(hs(n.flags)){for(var r,i={},o=0;o<n.flags.length;o++)i[n.flags[o]]=!0;this._instance.persistence.register(((r={})[k_]=i,r))}else{var s;this._instance.persistence.register(((s={})[k_]=n.flags,s))}if("payloads"in n)if(!1===n.payloads)this._instance.persistence.unregister(E_);else if(n.payloads){var a;this._instance.persistence.register(((a={})[E_]=n.payloads,a))}this._fireFeatureFlagsCallbacks()}else this._fireFeatureFlagsCallbacks()},t.onFeatureFlags=function(e){var t=this;if(this.addFeatureFlagsHandler(e),this._hasLoadedFlags){var n=this._prepareFeatureFlagsForCallbacks(),r=n.flags,i=n.flagVariants;e(r,i)}return function(){return t.removeFeatureFlagsHandler(e)}},t.updateEarlyAccessFeatureEnrollment=function(e,t){var n,r,i,o,s=(this._instance.get_property(Id)||[]).find((function(t){return t.flagKey===e})),a=((n={})["$feature_enrollment/"+e]=t,n),u={$feature_flag:e,$feature_enrollment:t,$set:a};s&&(u.$early_access_feature_name=s.name),this._instance.capture("$feature_enrollment_update",u),this.setPersonPropertiesForFlags(a,!1);var l=ki({},this.getFlagVariants(),((r={})[e]=t,r));null==(i=this._instance.persistence)||i.register(((o={})[S_]=Object.keys(C_(l)),o[Cd]=l,o)),this._fireFeatureFlagsCallbacks()},t.getEarlyAccessFeatures=function(e,t,n){var r=this;void 0===t&&(t=!1);var i=this._instance.get_property(Id),o=n?"&"+n.map((function(e){return"stage="+e})).join("&"):"";if(i&&!t)return e(i);this._instance._send_request({url:this._instance.requestRouter.endpointFor("api","/api/early_access_features/?token="+this._instance.config.token+o),method:"GET",callback:function(t){var n,i;if(t.json){var o=t.json.earlyAccessFeatures;return null==(n=r._instance.persistence)||n.register(((i={})[Id]=o,i)),e(o)}}})},t._prepareFeatureFlagsForCallbacks=function(){var e=this.getFlags(),t=this.getFlagVariants();return{flags:e.filter((function(e){return t[e]})),flagVariants:Object.keys(t).filter((function(e){return t[e]})).reduce((function(e,n){return e[n]=t[n],e}),{})}},t._fireFeatureFlagsCallbacks=function(e){var t=this._prepareFeatureFlagsForCallbacks(),n=t.flags,r=t.flagVariants;this.featureFlagEventHandlers.forEach((function(t){return t(n,r,{errorsLoading:e})}))},t.setPersonPropertiesForFlags=function(e,t){var n;void 0===t&&(t=!0);var r=this._instance.get_property(Fd)||{};this._instance.register(((n={})[Fd]=ki({},r,e),n)),t&&this._instance.reloadFeatureFlags()},t.resetPersonPropertiesForFlags=function(){this._instance.unregister(Fd)},t.setGroupPropertiesForFlags=function(e,t){var n;void 0===t&&(t=!0);var r=this._instance.get_property(Rd)||{};0!==Object.keys(r).length&&Object.keys(r).forEach((function(t){r[t]=ki({},r[t],e[t]),delete e[t]})),this._instance.register(((n={})[Rd]=ki({},r,e),n)),t&&this._instance.reloadFeatureFlags()},t.resetGroupPropertiesForFlags=function(e){if(e){var t,n,r=this._instance.get_property(Rd)||{};this._instance.register(((n={})[Rd]=ki({},r,((t={})[e]={},t)),n))}else this._instance.unregister(Rd)},wi(e,[{key:"hasLoadedFlags",get:function(){return this._hasLoadedFlags}}])}(),M_=["cookie","localstorage","localstorage+cookie","sessionstorage","memory"],O_=function(){function e(e){this._config=e,this.props={},this._campaign_params_saved=!1,this._name=function(e){var t="";return e.token&&(t=e.token.replace(/\+/g,"PL").replace(/\//g,"SL").replace(/=/g,"EQ")),e.persistence_name?"ph_"+e.persistence_name:"ph_"+t+"_posthog"}(e),this._storage=this._buildStorage(e),this.load(),e.debug&&Ps.info("Persistence loaded",e.persistence,ki({},this.props)),this.update_config(e,e),this.save()}var t=e.prototype;return t._buildStorage=function(e){-1===M_.indexOf(e.persistence.toLowerCase())&&(Ps.critical("Unknown persistence type "+e.persistence+"; falling back to localStorage+cookie"),e.persistence="localStorage+cookie");var t=e.persistence.toLowerCase();return"localstorage"===t&&Mf._is_supported()?Mf:"localstorage+cookie"===t&&Lf._is_supported()?Lf:"sessionstorage"===t&&Df._is_supported()?Df:"memory"===t?qf:"cookie"===t?Ff:Lf._is_supported()?Lf:Ff},t.properties=function(){var e={};return Os(this.props,(function(t,n){if(n===Cd&&gs(t))for(var r=Object.keys(t),i=0;i<r.length;i++)e["$feature/"+r[i]]=t[r[i]];else s=n,a=!1,(Ss(o=Vd)?a:Zo&&o.indexOf===Zo?-1!=o.indexOf(s):(Os(o,(function(e){if(a||(a=e===s))return Rs})),a))||(e[n]=t);var o,s,a})),e},t.load=function(){if(!this._disabled){var e=this._storage._parse(this._name);e&&(this.props=Ls({},e))}},t.save=function(){this._disabled||this._storage._set(this._name,this.props,this._expire_days,this._cross_subdomain,this._secure,this._config.debug)},t.remove=function(){this._storage._remove(this._name,!1),this._storage._remove(this._name,!0)},t.clear=function(){this.remove(),this.props={}},t.register_once=function(e,t,n){var r=this;if(gs(e)){ys(t)&&(t="None"),this._expire_days=ys(n)?this._default_expiry:n;var i=!1;if(Os(e,(function(e,n){r.props.hasOwnProperty(n)&&r.props[n]!==t||(r.props[n]=e,i=!0)})),i)return this.save(),!0}return!1},t.register=function(e,t){var n=this;if(gs(e)){this._expire_days=ys(t)?this._default_expiry:t;var r=!1;if(Os(e,(function(t,i){e.hasOwnProperty(i)&&n.props[i]!==t&&(n.props[i]=t,r=!0)})),r)return this.save(),!0}return!1},t.unregister=function(e){e in this.props&&(delete this.props[e],this.save())},t.update_campaign_params=function(){if(!this._campaign_params_saved){var e=d_(this._config.custom_campaign_params,this._config.mask_personal_data_properties,this._config.custom_personal_data_properties);ms(Ns(e))||this.register(e),this._campaign_params_saved=!0}},t.update_search_keyword=function(){var e;this.register((e=null==Qo?void 0:Qo.referrer)?p_(e):{})},t.update_referrer_info=function(){var e;this.register_once({$referrer:h_(),$referring_domain:null!=Qo&&Qo.referrer&&(null==(e=iu(Qo.referrer))?void 0:e.host)||"$direct"},void 0)},t.set_initial_person_info=function(){var e;this.props[Dd]||this.props[Nd]||this.register_once(((e={})[Hd]=v_(this._config.mask_personal_data_properties,this._config.custom_personal_data_properties),e),void 0)},t.get_initial_props=function(){var e=this,t={};Os([Nd,Dd],(function(n){var r=e.props[n];r&&Os(r,(function(e,n){t["$initial_"+cs(n)]=e}))}));var n,r,i=this.props[Hd];if(i){var o=(n=g_(i),r={},Os(n,(function(e,t){r["$initial_"+cs(t)]=e})),r);Ls(t,o)}return t},t.safe_merge=function(e){return Os(this.props,(function(t,n){n in e||(e[n]=t)})),e},t.update_config=function(e,t){if(this._default_expiry=this._expire_days=e.cookie_expiration,this.set_disabled(e.disable_persistence),this.set_cross_subdomain(e.cross_subdomain_cookie),this.set_secure(e.secure_cookie),e.persistence!==t.persistence){var n=this._buildStorage(e),r=this.props;this.clear(),this._storage=n,this.props=r,this.save()}},t.set_disabled=function(e){this._disabled=e,this._disabled?this.remove():this.save()},t.set_cross_subdomain=function(e){e!==this._cross_subdomain&&(this._cross_subdomain=e,this.remove(),this.save())},t.set_secure=function(e){e!==this._secure&&(this._secure=e,this.remove(),this.save())},t.set_event_timer=function(e,t){var n=this.props[ad]||{};n[e]=t,this.props[ad]=n,this.save()},t.remove_event_timer=function(e){var t=(this.props[ad]||{})[e];return ys(t)||(delete this.props[ad][e],this.save()),t},t.get_property=function(e){return this.props[e]},t.set_property=function(e,t){this.props[e]=t,this.save()},e}(),L_=function(){function e(){this._events={},this._events={}}var t=e.prototype;return t.on=function(e,t){var n=this;return this._events[e]||(this._events[e]=[]),this._events[e].push(t),function(){n._events[e]=n._events[e].filter((function(e){return e!==t}))}},t.emit=function(e,t){for(var n,r=Si(this._events[e]||[]);!(n=r()).done;){(0,n.value)(t)}for(var i,o=Si(this._events["*"]||[]);!(i=o()).done;){(0,i.value)(e,t)}},e}(),A_=function(){function e(e){var t=this;this._debugEventEmitter=new L_,this._checkStep=function(e,n){return t._checkStepEvent(e,n)&&t._checkStepUrl(e,n)&&t._checkStepElement(e,n)},this._checkStepEvent=function(e,t){return null==t||!t.event||(null==e?void 0:e.event)===(null==t?void 0:t.event)},this._instance=e,this._actionEvents=new Set,this._actionRegistry=new Set}var t=e.prototype;return t.init=function(){var e,t=this;if(!ys(null==(e=this._instance)?void 0:e._addCaptureHook)){var n;null==(n=this._instance)||n._addCaptureHook((function(e,n){t.on(e,n)}))}},t.register=function(e){var t,n,r=this;if(!ys(null==(t=this._instance)?void 0:t._addCaptureHook)&&(e.forEach((function(e){var t,n;null==(t=r._actionRegistry)||t.add(e),null==(n=e.steps)||n.forEach((function(e){var t;null==(t=r._actionEvents)||t.add((null==e?void 0:e.event)||"")}))})),null!=(n=this._instance)&&n.autocapture)){var i,o=new Set;e.forEach((function(e){var t;null==(t=e.steps)||t.forEach((function(e){null!=e&&e.selector&&o.add(null==e?void 0:e.selector)}))})),null==(i=this._instance)||i.autocapture.setElementSelectors(o)}},t.on=function(e,t){var n,r=this;null!=t&&0!=e.length&&(this._actionEvents.has(e)||this._actionEvents.has(null==t?void 0:t.event))&&this._actionRegistry&&(null==(n=this._actionRegistry)?void 0:n.size)>0&&this._actionRegistry.forEach((function(e){r._checkAction(t,e)&&r._debugEventEmitter.emit("actionCaptured",e.name)}))},t._addActionHook=function(e){this.onAction("actionCaptured",(function(t){return e(t)}))},t._checkAction=function(e,t){if(null==(null==t?void 0:t.steps))return!1;for(var n,r=Si(t.steps);!(n=r()).done;){var i=n.value;if(this._checkStep(e,i))return!0}return!1},t.onAction=function(e,t){return this._debugEventEmitter.on(e,t)},t._checkStepUrl=function(t,n){if(null!=n&&n.url){var r,i=null==t||null==(r=t.properties)?void 0:r.$current_url;if(!i||"string"!=typeof i)return!1;if(!e._matchString(i,null==n?void 0:n.url,(null==n?void 0:n.url_matching)||"contains"))return!1}return!0},e._matchString=function(t,n,r){switch(r){case"regex":return!!Vo&&el(t,n);case"exact":return n===t;case"contains":var i=e._escapeStringRegexp(n).replace(/_/g,".").replace(/%/g,".*");return el(t,i);default:return!1}},e._escapeStringRegexp=function(e){return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")},t._checkStepElement=function(t,n){if((null!=n&&n.href||null!=n&&n.tag_name||null!=n&&n.text)&&!this._getElementsList(t).some((function(t){return!(null!=n&&n.href&&!e._matchString(t.href||"",null==n?void 0:n.href,(null==n?void 0:n.href_matching)||"exact"))&&((null==n||!n.tag_name||t.tag_name===(null==n?void 0:n.tag_name))&&!(null!=n&&n.text&&!e._matchString(t.text||"",null==n?void 0:n.text,(null==n?void 0:n.text_matching)||"exact")&&!e._matchString(t.$el_text||"",null==n?void 0:n.text,(null==n?void 0:n.text_matching)||"exact")))})))return!1;if(null!=n&&n.selector){var r,i=null==t||null==(r=t.properties)?void 0:r.$element_selectors;if(!i)return!1;if(!i.includes(null==n?void 0:n.selector))return!1}return!0},t._getElementsList=function(e){return null==(null==e?void 0:e.properties.$elements)?[]:null==e?void 0:e.properties.$elements},e}(),q_=function(){function e(e){this._instance=e,this._eventToSurveys=new Map,this._actionToSurveys=new Map}var t=e.prototype;return t.register=function(e){var t;ys(null==(t=this._instance)?void 0:t._addCaptureHook)||(this._setupEventBasedSurveys(e),this._setupActionBasedSurveys(e))},t._setupActionBasedSurveys=function(e){var t=this,n=e.filter((function(e){var t,n;return(null==(t=e.conditions)?void 0:t.actions)&&(null==(n=e.conditions)||null==(n=n.actions)||null==(n=n.values)?void 0:n.length)>0}));if(0!==n.length){if(null==this._actionMatcher){this._actionMatcher=new A_(this._instance),this._actionMatcher.init();this._actionMatcher._addActionHook((function(e){t.onAction(e)}))}n.forEach((function(e){var n,r,i,o,s;e.conditions&&null!=(n=e.conditions)&&n.actions&&null!=(r=e.conditions)&&null!=(r=r.actions)&&r.values&&(null==(i=e.conditions)||null==(i=i.actions)||null==(i=i.values)?void 0:i.length)>0&&(null==(o=t._actionMatcher)||o.register(e.conditions.actions.values),null==(s=e.conditions)||null==(s=s.actions)||null==(s=s.values)||s.forEach((function(n){if(n&&n.name){var r=t._actionToSurveys.get(n.name);r&&r.push(e.id),t._actionToSurveys.set(n.name,r||[e.id])}})))}))}},t._setupEventBasedSurveys=function(e){var t,n=this;if(0!==e.filter((function(e){var t,n;return(null==(t=e.conditions)?void 0:t.events)&&(null==(n=e.conditions)||null==(n=n.events)||null==(n=n.values)?void 0:n.length)>0})).length){null==(t=this._instance)||t._addCaptureHook((function(e,t){n.onEvent(e,t)})),e.forEach((function(e){var t;null==(t=e.conditions)||null==(t=t.events)||null==(t=t.values)||t.forEach((function(t){if(t&&t.name){var r=n._eventToSurveys.get(t.name);r&&r.push(e.id),n._eventToSurveys.set(t.name,r||[e.id])}}))}))}},t.onEvent=function(e,t){var n,r=(null==(n=this._instance)||null==(n=n.persistence)?void 0:n.props[Od])||[];if("survey shown"===e&&t&&r.length>0){var i;zs.info("survey event matched, removing survey from activated surveys",{event:e,eventPayload:t,existingActivatedSurveys:r});var o=null==t||null==(i=t.properties)?void 0:i.$survey_id;if(o){var s=r.indexOf(o);s>=0&&(r.splice(s,1),this._updateActivatedSurveys(r))}}else this._eventToSurveys.has(e)&&(zs.info("survey event matched, updating activated surveys",{event:e,surveys:this._eventToSurveys.get(e)}),this._updateActivatedSurveys(r.concat(this._eventToSurveys.get(e)||[])))},t.onAction=function(e){var t,n=(null==(t=this._instance)||null==(t=t.persistence)?void 0:t.props[Od])||[];this._actionToSurveys.has(e)&&this._updateActivatedSurveys(n.concat(this._actionToSurveys.get(e)||[]))},t._updateActivatedSurveys=function(e){var t,n;null==(t=this._instance)||null==(t=t.persistence)||t.register(((n={})[Od]=[].concat(new Set(e)),n))},t.getSurveys=function(){var e,t=null==(e=this._instance)||null==(e=e.persistence)?void 0:e.props[Od];return t||[]},t.getEventToSurveys=function(){return this._eventToSurveys},t._getActionMatcher=function(){return this._actionMatcher},e}(),$_=function(){function e(e){this._surveyManager=null,this._isFetchingSurveys=!1,this._isInitializingSurveys=!1,this._surveyCallbacks=[],this._instance=e,this._surveyEventReceiver=null}var t=e.prototype;return t.onRemoteConfig=function(e){var t=e.surveys;if(ks(t))return zs.warn("Flags not loaded yet. Not loading surveys.");var n=hs(t);this._hasSurveys=n?t.length>0:t,zs.info("flags response received, hasSurveys: "+this._hasSurveys),this._hasSurveys&&this.loadIfEnabled()},t.reset=function(){localStorage.removeItem("lastSeenSurveyDate");for(var e=[],t=0;t<localStorage.length;t++){var n=localStorage.key(t);(null!=n&&n.startsWith(Ys)||null!=n&&n.startsWith(Qs))&&e.push(n)}e.forEach((function(e){return localStorage.removeItem(e)}))},t.loadIfEnabled=function(){var e=this;if(!this._surveyManager)if(this._isInitializingSurveys)zs.info("Already initializing surveys, skipping...");else if(this._instance.config.disable_surveys)zs.info("Disabled. Not loading surveys.");else if(this._hasSurveys){var t=null==ns?void 0:ns.__PosthogExtensions__;if(t){this._isInitializingSurveys=!0;try{var n=t.generateSurveys;if(n)return void this._completeSurveyInitialization(n);var r=t.loadExternalDependency;if(!r)return void this._handleSurveyLoadError("PostHog loadExternalDependency extension not found.");r(this._instance,"surveys",(function(n){n||!t.generateSurveys?e._handleSurveyLoadError("Could not load surveys script",n):e._completeSurveyInitialization(t.generateSurveys)}))}catch(e){throw this._handleSurveyLoadError("Error initializing surveys",e),e}finally{this._isInitializingSurveys=!1}}else zs.error("PostHog Extensions not found.")}else zs.info("No surveys to load.")},t._completeSurveyInitialization=function(e){this._surveyManager=e(this._instance),this._surveyEventReceiver=new q_(this._instance),zs.info("Surveys loaded successfully"),this._notifySurveyCallbacks({isLoaded:!0})},t._handleSurveyLoadError=function(e,t){zs.error(e,t),this._notifySurveyCallbacks({isLoaded:!1,error:e})},t.onSurveysLoaded=function(e){var t=this;return this._surveyCallbacks.push(e),this._surveyManager&&this._notifySurveyCallbacks({isLoaded:!0}),function(){t._surveyCallbacks=t._surveyCallbacks.filter((function(t){return t!==e}))}},t.getSurveys=function(e,t){var n=this;if(void 0===t&&(t=!1),this._instance.config.disable_surveys)return zs.info("Disabled. Not loading surveys."),e([]);var r=this._instance.get_property(Md);if(r&&!t)return e(r,{isLoaded:!0});if(this._isFetchingSurveys)return e([],{isLoaded:!1,error:"Surveys are already being loaded"});try{this._isFetchingSurveys=!0,this._instance._send_request({url:this._instance.requestRouter.endpointFor("api","/api/surveys/?token="+this._instance.config.token),method:"GET",timeout:this._instance.config.surveys_request_timeout_ms,callback:function(t){var r,i;n._isFetchingSurveys=!1;var o=t.statusCode;if(200!==o||!t.json){var s="Surveys API could not be loaded, status: "+o;return zs.error(s),e([],{isLoaded:!1,error:s})}var a,u=t.json.surveys||[],l=u.filter((function(e){return Ws(e)&&(Gs(e)||Zs(e))}));l.length>0&&(null==(a=n._surveyEventReceiver)||a.register(l));return null==(r=n._instance.persistence)||r.register(((i={})[Md]=u,i)),e(u,{isLoaded:!0})}})}catch(e){throw this._isFetchingSurveys=!1,e}},t._notifySurveyCallbacks=function(e){for(var t,n=Si(this._surveyCallbacks);!(t=n()).done;){var r=t.value;try{e.isLoaded?this.getSurveys(r):r([],e)}catch(e){zs.error("Error in survey callback",e)}}},t.getActiveMatchingSurveys=function(e,t){if(void 0===t&&(t=!1),!ks(this._surveyManager))return this._surveyManager.getActiveMatchingSurveys(e,t);zs.warn("init was not called")},t._getSurveyById=function(e){var t=null;return this.getSurveys((function(n){var r;t=null!==(r=n.find((function(t){return t.id===e})))&&void 0!==r?r:null})),t},t._checkSurveyEligibility=function(e){if(ks(this._surveyManager))return{eligible:!1,reason:"SDK is not enabled or survey functionality is not yet loaded"};var t="string"==typeof e?this._getSurveyById(e):e;return t?this._surveyManager.checkSurveyEligibility(t):{eligible:!1,reason:"Survey not found"}},t.canRenderSurvey=function(e){if(ks(this._surveyManager))return zs.warn("init was not called"),{visible:!1,disabledReason:"SDK is not enabled or survey functionality is not yet loaded"};var t=this._checkSurveyEligibility(e);return{visible:t.eligible,disabledReason:t.reason}},t.canRenderSurveyAsync=function(e,t){var n=this;return ks(this._surveyManager)?(zs.warn("init was not called"),Promise.resolve({visible:!1,disabledReason:"SDK is not enabled or survey functionality is not yet loaded"})):new Promise((function(r){n.getSurveys((function(t){var i,o=null!==(i=t.find((function(t){return t.id===e})))&&void 0!==i?i:null;if(o){var s=n._checkSurveyEligibility(o);r({visible:s.eligible,disabledReason:s.reason})}else r({visible:!1,disabledReason:"Survey not found"})}),t)}))},t.renderSurvey=function(e,t){if(ks(this._surveyManager))zs.warn("init was not called");else{var n=this._getSurveyById(e),r=null==Qo?void 0:Qo.querySelector(t);n?r?this._surveyManager.renderSurvey(n,r):zs.warn("Survey element not found"):zs.warn("Survey not found")}},e}(),D_=Fs("[RateLimiter]"),N_=function(){function e(e){var t,n,r=this;this.serverLimits={},this.lastEventRateLimited=!1,this.checkForLimiting=function(e){var t=e.text;if(t&&t.length)try{(JSON.parse(t).quota_limited||[]).forEach((function(e){D_.info((e||"events")+" is quota limited."),r.serverLimits[e]=(new Date).getTime()+6e4}))}catch(e){return void D_.warn('could not rate limit - continuing. Error: "'+(null==e?void 0:e.message)+'"',{text:t})}},this.instance=e,this.captureEventsPerSecond=(null==(t=e.config.rate_limiting)?void 0:t.events_per_second)||10,this.captureEventsBurstLimit=Math.max((null==(n=e.config.rate_limiting)?void 0:n.events_burst_limit)||10*this.captureEventsPerSecond,this.captureEventsPerSecond),this.lastEventRateLimited=this.clientRateLimitContext(!0).isRateLimited}var t=e.prototype;return t.clientRateLimitContext=function(e){var t,n,r;void 0===e&&(e=!1);var i=(new Date).getTime(),o=null!==(t=null==(n=this.instance.persistence)?void 0:n.get_property($d))&&void 0!==t?t:{tokens:this.captureEventsBurstLimit,last:i};o.tokens+=(i-o.last)/1e3*this.captureEventsPerSecond,o.last=i,o.tokens>this.captureEventsBurstLimit&&(o.tokens=this.captureEventsBurstLimit);var s=o.tokens<1;return s||e||(o.tokens=Math.max(0,o.tokens-1)),!s||this.lastEventRateLimited||e||this.instance.capture("$$client_ingestion_warning",{$$client_ingestion_warning_message:"posthog-js client rate limited. Config is set to "+this.captureEventsPerSecond+" events per second and "+this.captureEventsBurstLimit+" events burst limit."},{skip_client_rate_limiting:!0}),this.lastEventRateLimited=s,null==(r=this.instance.persistence)||r.set_property($d,o),{isRateLimited:s,remainingTokens:o.tokens}},t.isServerRateLimited=function(e){var t=this.serverLimits[e||"events"]||!1;return!1!==t&&(new Date).getTime()<t},e}(),H_=Fs("[RemoteConfig]"),B_=function(){function e(e){this._instance=e}var t=e.prototype;return t._loadRemoteConfigJs=function(e){var t,n,r=this;null!=(t=ns.__PosthogExtensions__)&&t.loadExternalDependency?null==(n=ns.__PosthogExtensions__)||null==n.loadExternalDependency||n.loadExternalDependency(this._instance,"remote-config",(function(){return e(r.remoteConfig)})):(H_.error("PostHog Extensions not found. Cannot load remote config."),e())},t._loadRemoteConfigJSON=function(e){this._instance._send_request({method:"GET",url:this._instance.requestRouter.endpointFor("assets","/array/"+this._instance.config.token+"/config"),callback:function(t){e(t.json)}})},t.load=function(){var e=this;try{if(this.remoteConfig)return H_.info("Using preloaded remote config",this.remoteConfig),void this._onRemoteConfig(this.remoteConfig);if(this._instance._shouldDisableFlags())return void H_.warn("Remote config is disabled. Falling back to local config.");this._loadRemoteConfigJs((function(t){if(!t)return H_.info("No config found after loading remote JS config. Falling back to JSON."),void e._loadRemoteConfigJSON((function(t){e._onRemoteConfig(t)}));e._onRemoteConfig(t)}))}catch(e){H_.error("Error loading remote config",e)}},t._onRemoteConfig=function(e){e?this._instance.config.__preview_remote_config?(this._instance._onRemoteConfig(e),!1!==e.hasFeatureFlags&&this._instance.featureFlags.ensureFlagsLoaded()):H_.info("__preview_remote_config is disabled. Logging config instead",e):H_.error("Failed to fetch remote config from PostHog.")},wi(e,[{key:"remoteConfig",get:function(){var e;return null==(e=ns._POSTHOG_REMOTE_CONFIG)||null==(e=e[this._instance.config.token])?void 0:e.config}}])}(),j_=3e3,U_=function(){function e(e,t){this._isPaused=!0,this._queue=[],this._flushTimeoutMs=zf((null==t?void 0:t.flush_interval_ms)||j_,250,5e3,"flush interval",j_),this._sendRequest=e}var t=e.prototype;return t.enqueue=function(e){this._queue.push(e),this._flushTimeout||this._setFlushTimeout()},t.unload=function(){var e=this;this._clearFlushTimeout();var t=this._queue.length>0?this._formatQueue():{},n=Object.values(t),r=[].concat(n.filter((function(e){return 0===e.url.indexOf("/e")})),n.filter((function(e){return 0!==e.url.indexOf("/e")})));r.map((function(t){e._sendRequest(ki({},t,{transport:"sendBeacon"}))}))},t.enable=function(){this._isPaused=!1,this._setFlushTimeout()},t._setFlushTimeout=function(){var e=this;this._isPaused||(this._flushTimeout=setTimeout((function(){if(e._clearFlushTimeout(),e._queue.length>0){var t=e._formatQueue(),n=function(){var n=t[r],i=(new Date).getTime();n.data&&hs(n.data)&&Os(n.data,(function(e){e.offset=Math.abs(e.timestamp-i),delete e.timestamp})),e._sendRequest(n)};for(var r in t)n()}}),this._flushTimeoutMs))},t._clearFlushTimeout=function(){clearTimeout(this._flushTimeout),this._flushTimeout=void 0},t._formatQueue=function(){var e={};return Os(this._queue,(function(t){var n,r=t,i=(r?r.batchKey:null)||r.url;ys(e[i])&&(e[i]=ki({},r,{data:[]})),null==(n=e[i].data)||n.push(r.data)})),this._queue=[],e},e}(),V_=["retriesPerformedSoFar"];var z_=function(){function e(e){var t=this;this._isPolling=!1,this._pollIntervalMs=3e3,this._queue=[],this._instance=e,this._queue=[],this._areWeOnline=!0,!ys(Vo)&&"onLine"in Vo.navigator&&(this._areWeOnline=Vo.navigator.onLine,Vs(Vo,"online",(function(){t._areWeOnline=!0,t._flush()})),Vs(Vo,"offline",(function(){t._areWeOnline=!1})))}var t=e.prototype;return t.retriableRequest=function(e){var t=this,n=e.retriesPerformedSoFar,r=xi(e,V_);xs(n)&&n>0&&(r.url=Qu(r.url,{retry_count:n})),this._instance._send_request(ki({},r,{callback:function(e){200!==e.statusCode&&(e.statusCode<400||e.statusCode>=500)&&(null!=n?n:0)<10?t._enqueue(ki({retriesPerformedSoFar:n},r)):null==r.callback||r.callback(e)}}))},t._enqueue=function(e){var t=e.retriesPerformedSoFar||0;e.retriesPerformedSoFar=t+1;var n=function(e){var t=3e3*Math.pow(2,e),n=t/2,r=Math.min(18e5,t),i=(Math.random()-.5)*(r-n);return Math.ceil(r+i)}(t),r=Date.now()+n;this._queue.push({retryAt:r,requestOptions:e});var i="Enqueued failed request for retry in "+n;navigator.onLine||(i+=" (Browser is offline)"),Ps.warn(i),this._isPolling||(this._isPolling=!0,this._poll())},t._poll=function(){var e=this;this._poller&&clearTimeout(this._poller),this._poller=setTimeout((function(){e._areWeOnline&&e._queue.length>0&&e._flush(),e._poll()}),this._pollIntervalMs)},t._flush=function(){var e=Date.now(),t=[],n=this._queue.filter((function(n){return n.retryAt<e||(t.push(n),!1)}));if(this._queue=t,n.length>0)for(var r,i=Si(n);!(r=i()).done;){var o=r.value.requestOptions;this.retriableRequest(o)}},t.unload=function(){this._poller&&(clearTimeout(this._poller),this._poller=void 0);for(var e,t=Si(this._queue);!(e=t()).done;){var n=e.value.requestOptions;try{this._instance._send_request(ki({},n,{transport:"sendBeacon"}))}catch(e){Ps.error(e)}}this._queue=[]},wi(e,[{key:"length",get:function(){return this._queue.length}}])}(),W_=function(){function e(e){var t=this;this._updateScrollData=function(){var e,n,r,i;t._context||(t._context={});var o=t.scrollElement(),s=t.scrollY(),a=o?Math.max(0,o.scrollHeight-o.clientHeight):0,u=s+((null==o?void 0:o.clientHeight)||0),l=(null==o?void 0:o.scrollHeight)||0;t._context.lastScrollY=Math.ceil(s),t._context.maxScrollY=Math.max(s,null!==(e=t._context.maxScrollY)&&void 0!==e?e:0),t._context.maxScrollHeight=Math.max(a,null!==(n=t._context.maxScrollHeight)&&void 0!==n?n:0),t._context.lastContentY=u,t._context.maxContentY=Math.max(u,null!==(r=t._context.maxContentY)&&void 0!==r?r:0),t._context.maxContentHeight=Math.max(l,null!==(i=t._context.maxContentHeight)&&void 0!==i?i:0)},this._instance=e}var t=e.prototype;return t.getContext=function(){return this._context},t.resetContext=function(){var e=this._context;return setTimeout(this._updateScrollData,0),e},t.startMeasuringScrollPosition=function(){Vs(Vo,"scroll",this._updateScrollData,{capture:!0}),Vs(Vo,"scrollend",this._updateScrollData,{capture:!0}),Vs(Vo,"resize",this._updateScrollData)},t.scrollElement=function(){if(!this._instance.config.scroll_root_selector)return null==Vo?void 0:Vo.document.documentElement;for(var e,t=Si(hs(this._instance.config.scroll_root_selector)?this._instance.config.scroll_root_selector:[this._instance.config.scroll_root_selector]);!(e=t()).done;){var n=e.value,r=null==Vo?void 0:Vo.document.querySelector(n);if(r)return r}},t.scrollY=function(){if(this._instance.config.scroll_root_selector){var e=this.scrollElement();return e&&e.scrollTop||0}return Vo&&(Vo.scrollY||Vo.pageYOffset||Vo.document.documentElement.scrollTop)||0},t.scrollX=function(){if(this._instance.config.scroll_root_selector){var e=this.scrollElement();return e&&e.scrollLeft||0}return Vo&&(Vo.scrollX||Vo.pageXOffset||Vo.document.documentElement.scrollLeft)||0},e}(),G_=function(e){return v_(null==e?void 0:e.config.mask_personal_data_properties,null==e?void 0:e.config.custom_personal_data_properties)},Z_=function(){function e(e,t,n,r){var i=this;this._onSessionIdCallback=function(e){var t,n=i._getStored();if(!n||n.sessionId!==e){var r={sessionId:e,props:i._sessionSourceParamGenerator(i._instance)};i._persistence.register(((t={})[qd]=r,t))}},this._instance=e,this._sessionIdManager=t,this._persistence=n,this._sessionSourceParamGenerator=r||G_,this._sessionIdManager.onSessionId(this._onSessionIdCallback)}var t=e.prototype;return t._getStored=function(){return this._persistence.props[qd]},t.getSetOnceProps=function(){var e,t=null==(e=this._getStored())?void 0:e.props;return t?"r"in t?g_(t):{$referring_domain:t.referringDomain,$pathname:t.initialPathName,utm_source:t.utm_source,utm_campaign:t.utm_campaign,utm_medium:t.utm_medium,utm_content:t.utm_content,utm_term:t.utm_term}:{}},t.getSessionProps=function(){var e={};return Os(Ns(this.getSetOnceProps()),(function(t,n){"$current_url"===n&&(n="url"),e["$session_entry_"+cs(n)]=t})),e},e}(),Y_=Fs("[SessionId]"),Q_=function(){function e(e,t,n){var r;if(this._sessionIdChangedHandlers=[],!e.persistence)throw new Error("SessionIdManager requires a PostHogPersistence instance");if(e.config.__preview_experimental_cookieless_mode)throw new Error("SessionIdManager cannot be used with __preview_experimental_cookieless_mode");this._config=e.config,this._persistence=e.persistence,this._windowId=void 0,this._sessionId=void 0,this._sessionStartTimestamp=null,this._sessionActivityTimestamp=null,this._sessionIdGenerator=t||ia,this._windowIdGenerator=n||ia;var i=this._config.persistence_name||this._config.token,o=this._config.session_idle_timeout_seconds||1800;if(this._sessionTimeoutMs=1e3*zf(o,60,36e3,"session_idle_timeout_seconds",1800),e.register({$configured_session_timeout_ms:this._sessionTimeoutMs}),this._resetIdleTimer(),this._window_id_storage_key="ph_"+i+"_window_id",this._primary_window_exists_storage_key="ph_"+i+"_primary_window_exists",this._canUseSessionStorage()){var s=Df._parse(this._window_id_storage_key),a=Df._parse(this._primary_window_exists_storage_key);s&&!a?this._windowId=s:Df._remove(this._window_id_storage_key),Df._set(this._primary_window_exists_storage_key,!0)}if(null!=(r=this._config.bootstrap)&&r.sessionID)try{var u=function(e){var t=e.replace(/-/g,"");if(32!==t.length)throw new Error("Not a valid UUID");if("7"!==t[12])throw new Error("Not a UUIDv7");return parseInt(t.substring(0,12),16)}(this._config.bootstrap.sessionID);this._setSessionId(this._config.bootstrap.sessionID,(new Date).getTime(),u)}catch(e){Y_.error("Invalid sessionID in bootstrap",e)}this._listenToReloadWindow()}var t=e.prototype;return t.onSessionId=function(e){var t=this;return ys(this._sessionIdChangedHandlers)&&(this._sessionIdChangedHandlers=[]),this._sessionIdChangedHandlers.push(e),this._sessionId&&e(this._sessionId,this._windowId),function(){t._sessionIdChangedHandlers=t._sessionIdChangedHandlers.filter((function(t){return t!==e}))}},t._canUseSessionStorage=function(){return"memory"!==this._config.persistence&&!this._persistence._disabled&&Df._is_supported()},t._setWindowId=function(e){e!==this._windowId&&(this._windowId=e,this._canUseSessionStorage()&&Df._set(this._window_id_storage_key,e))},t._getWindowId=function(){return this._windowId?this._windowId:this._canUseSessionStorage()?Df._parse(this._window_id_storage_key):null},t._setSessionId=function(e,t,n){var r;e===this._sessionId&&t===this._sessionActivityTimestamp&&n===this._sessionStartTimestamp||(this._sessionStartTimestamp=n,this._sessionActivityTimestamp=t,this._sessionId=e,this._persistence.register(((r={})[kd]=[t,e,n],r)))},t._getSessionId=function(){if(this._sessionId&&this._sessionActivityTimestamp&&this._sessionStartTimestamp)return[this._sessionActivityTimestamp,this._sessionId,this._sessionStartTimestamp];var e=this._persistence.props[kd];return hs(e)&&2===e.length&&e.push(e[0]),e||[0,null,0]},t.resetSessionId=function(){this._setSessionId(null,null,null)},t._listenToReloadWindow=function(){var e=this;Vs(Vo,"beforeunload",(function(){e._canUseSessionStorage()&&Df._remove(e._primary_window_exists_storage_key)}),{capture:!1})},t.checkAndGetSessionAndWindowId=function(e,t){if(void 0===e&&(e=!1),void 0===t&&(t=null),this._config.__preview_experimental_cookieless_mode)throw new Error("checkAndGetSessionAndWindowId should not be called in __preview_experimental_cookieless_mode");var n=t||(new Date).getTime(),r=this._getSessionId(),i=r[0],o=r[1],s=r[2],a=this._getWindowId(),u=xs(s)&&s>0&&Math.abs(n-s)>864e5,l=!1,c=!o,d=!e&&Math.abs(n-i)>this.sessionTimeoutMs;c||d||u?(o=this._sessionIdGenerator(),a=this._windowIdGenerator(),Y_.info("new session ID generated",{sessionId:o,windowId:a,changeReason:{noSessionId:c,activityTimeout:d,sessionPastMaximumLength:u}}),s=n,l=!0):a||(a=this._windowIdGenerator(),l=!0);var f=0===i||!e||u?n:i,p=0===s?(new Date).getTime():s;return this._setWindowId(a),this._setSessionId(o,f,p),e||this._resetIdleTimer(),l&&this._sessionIdChangedHandlers.forEach((function(e){return e(o,a,l?{noSessionId:c,activityTimeout:d,sessionPastMaximumLength:u}:void 0)})),{sessionId:o,windowId:a,sessionStartTimestamp:p,changeReason:l?{noSessionId:c,activityTimeout:d,sessionPastMaximumLength:u}:void 0,lastActivityTimestamp:i}},t._resetIdleTimer=function(){var e=this;clearTimeout(this._enforceIdleTimeout),this._enforceIdleTimeout=setTimeout((function(){e.resetSessionId()}),1.1*this.sessionTimeoutMs)},wi(e,[{key:"sessionTimeoutMs",get:function(){return this._sessionTimeoutMs}}])}(),J_=["$set_once","$set"],X_=Fs("[SiteApps]"),K_=function(){function e(e){this._instance=e,this._bufferedInvocations=[],this.apps={}}var t=e.prototype;return t._eventCollector=function(e,t){if(t){var n=this.globalsForEvent(t);this._bufferedInvocations.push(n),this._bufferedInvocations.length>1e3&&(this._bufferedInvocations=this._bufferedInvocations.slice(10))}},t.init=function(){var e=this;if(this.isEnabled){var t=this._instance._addCaptureHook(this._eventCollector.bind(this));this._stopBuffering=function(){t(),e._bufferedInvocations=[],e._stopBuffering=void 0}}},t.globalsForEvent=function(e){var t,n,r,i,o,s,a;if(!e)throw new Error("Event payload is required");for(var u={},l=this._instance.get_property("$groups")||[],c=this._instance.get_property("$stored_group_properties")||{},d=0,f=Object.entries(c);d<f.length;d++){var p=f[d],_=p[0],h=p[1];u[_]={id:l[_],type:_,properties:h}}var v=e.$set_once,g=e.$set;return{event:ki({},xi(e,J_),{properties:ki({},e.properties,g?{$set:ki({},null!==(t=null==(n=e.properties)?void 0:n.$set)&&void 0!==t?t:{},g)}:{},v?{$set_once:ki({},null!==(r=null==(i=e.properties)?void 0:i.$set_once)&&void 0!==r?r:{},v)}:{}),elements_chain:null!==(o=null==(s=e.properties)?void 0:s.$elements_chain)&&void 0!==o?o:"",distinct_id:null==(a=e.properties)?void 0:a.distinct_id}),person:{properties:this._instance.get_property("$stored_person_properties")},groups:u}},t.setupSiteApp=function(e){var t=this,n=this.apps[e.id],r=function(){!n.errored&&t._bufferedInvocations.length&&(X_.info("Processing "+t._bufferedInvocations.length+" events for site app with id "+e.id),t._bufferedInvocations.forEach((function(e){return null==n.processEvent?void 0:n.processEvent(e)})),n.processedBuffer=!0),Object.values(t.apps).every((function(e){return e.processedBuffer||e.errored}))&&(null==t._stopBuffering||t._stopBuffering())},i=!1,o=function(t){n.errored=!t,n.loaded=!0,X_.info("Site app with id "+e.id+" "+(t?"loaded":"errored")),i&&r()};try{var s=e.init({posthog:this._instance,callback:function(e){o(e)}}).processEvent;s&&(n.processEvent=s),i=!0}catch(t){X_.error("Error while initializing PostHog app with config id "+e.id,t),o(!1)}if(i&&n.loaded)try{r()}catch(t){X_.error("Error while processing buffered events PostHog app with config id "+e.id,t),n.errored=!0}},t._setupSiteApps=function(){for(var e,t=this.siteAppLoaders||[],n=Si(t);!(e=n()).done;){var r=e.value;this.apps[r.id]={id:r.id,loaded:!1,errored:!1,processedBuffer:!1}}for(var i,o=Si(t);!(i=o()).done;){var s=i.value;this.setupSiteApp(s)}},t._onCapturedEvent=function(e){if(0!==Object.keys(this.apps).length)for(var t=this.globalsForEvent(e),n=0,r=Object.values(this.apps);n<r.length;n++){var i=r[n];try{null==i.processEvent||i.processEvent(t)}catch(t){X_.error("Error while processing event "+e.event+" for site app "+i.id,t)}}},t.onRemoteConfig=function(e){var t,n,r,i=this;if(null!=(t=this.siteAppLoaders)&&t.length)return this.isEnabled?(this._setupSiteApps(),void this._instance.on("eventCaptured",(function(e){return i._onCapturedEvent(e)}))):void X_.error('PostHog site apps are disabled. Enable the "opt_in_site_apps" config to proceed.');if(null==(n=this._stopBuffering)||n.call(this),null!=(r=e.siteApps)&&r.length)if(this.isEnabled)for(var o,s=function(){var e,t=o.value,n=t.id,r=t.url;ns["__$$ph_site_app_"+n]=i._instance,null==(e=ns.__PosthogExtensions__)||null==e.loadSiteApp||e.loadSiteApp(i._instance,r,(function(e){if(e)return X_.error("Error while initializing PostHog app with config id "+n,e)}))},a=Si(e.siteApps);!(o=a()).done;)s();else X_.error('PostHog site apps are disabled. Enable the "opt_in_site_apps" config to proceed.')},wi(e,[{key:"isEnabled",get:function(){return!!this._instance.config.opt_in_site_apps}},{key:"siteAppLoaders",get:function(){var e;return null==(e=ns._POSTHOG_REMOTE_CONFIG)||null==(e=e[this._instance.config.token])?void 0:e.siteApps}}])}(),eh=["amazonbot","amazonproductbot","app.hypefactors.com","applebot","archive.org_bot","awariobot","backlinksextendedbot","baiduspider","bingbot","bingpreview","chrome-lighthouse","dataforseobot","deepscan","duckduckbot","facebookexternal","facebookcatalog","http://yandex.com/bots","hubspot","ia_archiver","leikibot","linkedinbot","meta-externalagent","mj12bot","msnbot","nessus","petalbot","pinterest","prerender","rogerbot","screaming frog","sebot-wa","sitebulb","slackbot","slurp","trendictionbot","turnitin","twitterbot","vercelbot","yahoo! slurp","yandexbot","zoombot","bot.htm","bot.php","(bot;","bot/","crawler","ahrefsbot","ahrefssiteaudit","semrushbot","siteauditbot","splitsignalbot","gptbot","oai-searchbot","chatgpt-user","perplexitybot","better uptime bot","sentryuptimebot","uptimerobot","headlesschrome","cypress","google-hoteladsverifier","adsbot-google","apis-google","duplexweb-google","feedfetcher-google","google favicon","google web preview","google-read-aloud","googlebot","googleother","google-cloudvertexbot","googleweblight","mediapartners-google","storebot-google","google-inspectiontool","bytespider"],th=function(e,t){if(!e)return!1;var n=e.toLowerCase();return eh.concat(t||[]).some((function(e){var t=e.toLowerCase();return-1!==n.indexOf(t)}))},nh=function(e,t){if(!e)return!1;var n=e.userAgent;if(n&&th(n,t))return!0;try{var r=null==e?void 0:e.userAgentData;if(null!=r&&r.brands&&r.brands.some((function(e){return th(null==e?void 0:e.brand,t)})))return!0}catch(e){}return!!e.webdriver},rh=function(e){return e.US="us",e.EU="eu",e.CUSTOM="custom",e}({}),ih="i.posthog.com",oh=function(){function e(e){this._regionCache={},this.instance=e}return e.prototype.endpointFor=function(e,t){if(void 0===t&&(t=""),t&&(t="/"===t[0]?t:"/"+t),"ui"===e)return this.uiHost+t;if(this.region===rh.CUSTOM)return this.apiHost+t;var n=ih+t;switch(e){case"assets":return"https://"+this.region+"-assets."+n;case"api":return"https://"+this.region+"."+n}},wi(e,[{key:"apiHost",get:function(){var e=this.instance.config.api_host.trim().replace(/\/$/,"");return"https://app.posthog.com"===e?"https://us.i.posthog.com":e}},{key:"uiHost",get:function(){var e,t=null==(e=this.instance.config.ui_host)?void 0:e.replace(/\/$/,"");return t||(t=this.apiHost.replace("."+ih,".posthog.com")),"https://app.posthog.com"===t?"https://us.posthog.com":t}},{key:"region",get:function(){return this._regionCache[this.apiHost]||(/https:\/\/(app|us|us-assets)(\.i)?\.posthog\.com/i.test(this.apiHost)?this._regionCache[this.apiHost]=rh.US:/https:\/\/(eu|eu-assets)(\.i)?\.posthog\.com/i.test(this.apiHost)?this._regionCache[this.apiHost]=rh.EU:this._regionCache[this.apiHost]=rh.CUSTOM),this._regionCache[this.apiHost]}}])}(),sh={icontains:function(e,t){return!!Vo&&t.href.toLowerCase().indexOf(e.toLowerCase())>-1},not_icontains:function(e,t){return!!Vo&&-1===t.href.toLowerCase().indexOf(e.toLowerCase())},regex:function(e,t){return!!Vo&&el(t.href,e)},not_regex:function(e,t){return!!Vo&&!el(t.href,e)},exact:function(e,t){return t.href===e},is_not:function(e,t){return t.href!==e}},ah=function(){function e(t){var n=this;this.getWebExperimentsAndEvaluateDisplayLogic=function(t){void 0===t&&(t=!1),n.getWebExperiments((function(t){e._logInfo("retrieved web experiments from the server"),n._flagToExperiments=new Map,t.forEach((function(t){if(t.feature_flag_key){var r;if(n._flagToExperiments)e._logInfo("setting flag key ",t.feature_flag_key," to web experiment ",t),null==(r=n._flagToExperiments)||r.set(t.feature_flag_key,t);var i=n._instance.getFeatureFlag(t.feature_flag_key);bs(i)&&t.variants[i]&&n._applyTransforms(t.name,i,t.variants[i].transforms)}else if(t.variants)for(var o in t.variants){var s=t.variants[o];e._matchesTestVariant(s)&&n._applyTransforms(t.name,o,s.transforms)}}))}),t)},this._instance=t,this._instance.onFeatureFlags((function(e){n.onFeatureFlags(e)}))}var t=e.prototype;return t.onFeatureFlags=function(t){var n=this;if(this._is_bot())e._logInfo("Refusing to render web experiment since the viewer is a likely bot");else if(!this._instance.config.disable_web_experiments){if(ks(this._flagToExperiments))return this._flagToExperiments=new Map,this.loadIfEnabled(),void this.previewWebExperiment();e._logInfo("applying feature flags",t),t.forEach((function(e){var t;if(n._flagToExperiments&&null!=(t=n._flagToExperiments)&&t.has(e)){var r,i=n._instance.getFeatureFlag(e),o=null==(r=n._flagToExperiments)?void 0:r.get(e);i&&null!=o&&o.variants[i]&&n._applyTransforms(o.name,i,o.variants[i].transforms)}}))}},t.previewWebExperiment=function(){var t=this,n=e.getWindowLocation();if(null!=n&&n.search){var r=su(null==n?void 0:n.search,"__experiment_id"),i=su(null==n?void 0:n.search,"__experiment_variant");r&&i&&(e._logInfo("previewing web experiments "+r+" && "+i),this.getWebExperiments((function(e){t._showPreviewWebExperiment(parseInt(r),i,e)}),!1,!0))}},t.loadIfEnabled=function(){this._instance.config.disable_web_experiments||this.getWebExperimentsAndEvaluateDisplayLogic()},t.getWebExperiments=function(e,t,n){if(this._instance.config.disable_web_experiments&&!n)return e([]);var r=this._instance.get_property("$web_experiments");if(r&&!t)return e(r);this._instance._send_request({url:this._instance.requestRouter.endpointFor("api","/api/web_experiments/?token="+this._instance.config.token),method:"GET",callback:function(t){if(200!==t.statusCode||!t.json)return e([]);var n=t.json.experiments||[];return e(n)}})},t._showPreviewWebExperiment=function(t,n,r){var i=r.filter((function(e){return e.id===t}));i&&i.length>0&&(e._logInfo("Previewing web experiment ["+i[0].name+"] with variant ["+n+"]"),this._applyTransforms(i[0].name,n,i[0].variants[n].transforms))},e._matchesTestVariant=function(t){return!ks(t.conditions)&&(e._matchUrlConditions(t)&&e._matchUTMConditions(t))},e._matchUrlConditions=function(t){var n;if(ks(t.conditions)||ks(null==(n=t.conditions)?void 0:n.url))return!0;var r,i,o,s=e.getWindowLocation();return!!s&&(null==(r=t.conditions)||!r.url||sh[null!==(i=null==(o=t.conditions)?void 0:o.urlMatchType)&&void 0!==i?i:"icontains"](t.conditions.url,s))},e.getWindowLocation=function(){return null==Vo?void 0:Vo.location},e._matchUTMConditions=function(e){var t;if(ks(e.conditions)||ks(null==(t=e.conditions)?void 0:t.utm))return!0;var n=d_();if(n.utm_source){var r,i,o,s,a,u,l,c,d=null==(r=e.conditions)||null==(r=r.utm)||!r.utm_campaign||(null==(i=e.conditions)||null==(i=i.utm)?void 0:i.utm_campaign)==n.utm_campaign,f=null==(o=e.conditions)||null==(o=o.utm)||!o.utm_source||(null==(s=e.conditions)||null==(s=s.utm)?void 0:s.utm_source)==n.utm_source,p=null==(a=e.conditions)||null==(a=a.utm)||!a.utm_medium||(null==(u=e.conditions)||null==(u=u.utm)?void 0:u.utm_medium)==n.utm_medium,_=null==(l=e.conditions)||null==(l=l.utm)||!l.utm_term||(null==(c=e.conditions)||null==(c=c.utm)?void 0:c.utm_term)==n.utm_term;return d&&p&&_&&f}return!1},e._logInfo=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];Ps.info("[WebExperiments] "+e,n)},t._applyTransforms=function(t,n,r){this._is_bot()?e._logInfo("Refusing to render web experiment since the viewer is a likely bot"):"control"!==n?r.forEach((function(r){if(r.selector){var i;e._logInfo("applying transform of variant "+n+" for experiment "+t+" ",r);var o=null==(i=document)?void 0:i.querySelectorAll(r.selector);null==o||o.forEach((function(e){var t=e;r.html&&(t.innerHTML=r.html),r.css&&t.setAttribute("style",r.css)}))}})):e._logInfo("Control variants leave the page unmodified.")},t._is_bot=function(){return Yo&&this._instance?nh(Yo,this._instance.config.custom_blocked_useragents):void 0},e}(),uh={},lh=function(){},ch="posthog",dh=!Zu&&-1===(null==ts?void 0:ts.indexOf("MSIE"))&&-1===(null==ts?void 0:ts.indexOf("Mozilla")),fh=function(e){var t;return{api_host:"https://us.i.posthog.com",ui_host:null,token:"",autocapture:!0,rageclick:!0,cross_subdomain_cookie:js(null==Qo?void 0:Qo.location),persistence:"localStorage+cookie",persistence_name:"",loaded:lh,save_campaign_params:!0,custom_campaign_params:[],custom_blocked_useragents:[],save_referrer:!0,capture_pageview:"2025-05-24"!==e||"history_change",capture_pageleave:"if_capture_pageview",defaults:null!=e?e:"unset",debug:Jo&&bs(null==Jo?void 0:Jo.search)&&-1!==Jo.search.indexOf("__posthog_debug=true")||!1,cookie_expiration:365,upgrade:!1,disable_session_recording:!1,disable_persistence:!1,disable_web_experiments:!0,disable_surveys:!1,disable_surveys_automatic_display:!1,disable_external_dependency_loading:!1,enable_recording_console_log:void 0,secure_cookie:"https:"===(null==Vo||null==(t=Vo.location)?void 0:t.protocol),ip:!0,opt_out_capturing_by_default:!1,opt_out_persistence_by_default:!1,opt_out_useragent_filter:!1,opt_out_capturing_persistence_type:"localStorage",opt_out_capturing_cookie_prefix:null,opt_in_site_apps:!1,property_denylist:[],respect_dnt:!1,sanitize_properties:null,request_headers:{},request_batching:!0,properties_string_max_length:65535,session_recording:{},mask_all_element_attributes:!1,mask_all_text:!1,mask_personal_data_properties:!1,custom_personal_data_properties:[],advanced_disable_flags:!1,advanced_disable_decide:!1,advanced_disable_feature_flags:!1,advanced_disable_feature_flags_on_first_load:!1,advanced_only_evaluate_survey_feature_flags:!1,advanced_disable_toolbar_metrics:!1,feature_flag_request_timeout_ms:3e3,surveys_request_timeout_ms:1e4,on_request_error:function(e){var t="Bad HTTP status: "+e.statusCode+" "+e.text;Ps.error(t)},get_device_id:function(e){return e},capture_performance:void 0,name:"posthog",bootstrap:{},disable_compression:!1,session_idle_timeout_seconds:1800,person_profiles:"identified_only",before_send:void 0,request_queue_config:{flush_interval_ms:j_},error_tracking:{},_onCapture:lh}},ph=function(e){var t={};ys(e.process_person)||(t.person_profiles=e.process_person),ys(e.xhr_headers)||(t.request_headers=e.xhr_headers),ys(e.cookie_name)||(t.persistence_name=e.cookie_name),ys(e.disable_cookie)||(t.disable_persistence=e.disable_cookie),ys(e.store_google)||(t.save_campaign_params=e.store_google),ys(e.verbose)||(t.debug=e.verbose);var n=Ls({},t,e);return hs(e.property_blacklist)&&(ys(e.property_denylist)?n.property_denylist=e.property_blacklist:hs(e.property_denylist)?n.property_denylist=[].concat(e.property_blacklist,e.property_denylist):Ps.error("Invalid value for property_denylist config: "+e.property_denylist)),n},_h=function(){return wi((function(){this.__forceAllowLocalhost=!1}),[{key:"_forceAllowLocalhost",get:function(){return this.__forceAllowLocalhost},set:function(e){Ps.error("WebPerformanceObserver is deprecated and has no impact on network capture. Use `_forceAllowLocalhostNetworkCapture` on `posthog.sessionRecording`"),this.__forceAllowLocalhost=e}}])}(),hh=function(){function e(){var e=this;this.webPerformance=new _h,this._personProcessingSetOncePropertiesSent=!1,this.version=rs.LIB_VERSION,this._internalEventEmitter=new L_,this._calculate_event_properties=this.calculateEventProperties.bind(this),this.config=fh(),this.SentryIntegration=Up,this.sentryIntegration=function(t){return function(e,t){var n=jp(e,t);return{name:Bp,processEvent:function(e){return n(e)}}}(e,t)},this.__request_queue=[],this.__loaded=!1,this.analyticsDefaultEndpoint="/e/",this._initialPageviewCaptured=!1,this._visibilityStateListener=null,this._initialPersonProfilesConfig=null,this._cachedPersonProperties=null,this.featureFlags=new R_(this),this.toolbar=new Zp(this),this.scrollManager=new W_(this),this.pageViewManager=new r_(this),this.surveys=new $_(this),this.experiments=new ah(this),this.exceptions=new o_(this),this.rateLimiter=new N_(this),this.requestRouter=new oh(this),this.consent=new Hf(this),this.people={set:function(t,n,r){var i,o=bs(t)?((i={})[t]=n,i):t;e.setPersonProperties(o),null==r||r({})},set_once:function(t,n,r){var i,o=bs(t)?((i={})[t]=n,i):t;e.setPersonProperties(void 0,o),null==r||r({})}},this.on("eventCaptured",(function(e){return Ps.info('send "'+(null==e?void 0:e.event)+'"',e)}))}var t=e.prototype;return t.init=function(t,n,r){if(r&&r!==ch){var i,o=null!==(i=uh[r])&&void 0!==i?i:new e;return o._init(t,n,r),uh[r]=o,uh[ch][r]=o,o}return this._init(t,n,r)},t._init=function(e,t,n){var r,i,o=this;if(void 0===t&&(t={}),ys(e)||ws(e))return Ps.critical("PostHog was initialized without a token. This likely indicates a misconfiguration. Please check the first argument passed to posthog.init()"),this;if(this.__loaded)return Ps.warn("You have already initialized PostHog! Re-initializing is a no-op"),this;this.__loaded=!0,this.config={},this._originalUserConfig=t,this._triggered_notifs=[],t.person_profiles&&(this._initialPersonProfilesConfig=t.person_profiles),this.set_config(Ls({},fh(t.defaults),ph(t),{name:n,token:e})),this.config.on_xhr_error&&Ps.error("on_xhr_error is deprecated. Use on_request_error instead"),this.compression=t.disable_compression?void 0:ss.GZipJS,this.persistence=new O_(this.config),this.sessionPersistence="sessionStorage"===this.config.persistence||"memory"===this.config.persistence?this.persistence:new O_(ki({},this.config,{persistence:"sessionStorage"}));var s=ki({},this.persistence.props),a=ki({},this.sessionPersistence.props);if(this.register({$initialization_time:(new Date).toISOString()}),this._requestQueue=new U_((function(e){return o._send_retriable_request(e)}),this.config.request_queue_config),this._retryQueue=new z_(this),this.__request_queue=[],this.config.__preview_experimental_cookieless_mode||(this.sessionManager=new Q_(this),this.sessionPropsManager=new Z_(this,this.sessionManager,this.persistence)),new Qp(this).startIfEnabledOrStop(),this.siteApps=new K_(this),null==(r=this.siteApps)||r.init(),this.config.__preview_experimental_cookieless_mode||(this.sessionRecording=new Dp(this),this.sessionRecording.startIfEnabledOrStop()),this.config.disable_scroll_properties||this.scrollManager.startMeasuringScrollPosition(),this.autocapture=new Tf(this),this.autocapture.startIfEnabled(),this.surveys.loadIfEnabled(),this.heatmaps=new n_(this),this.heatmaps.startIfEnabled(),this.webVitalsAutocapture=new Kp(this),this.exceptionObserver=new Zf(this),this.exceptionObserver.startIfEnabled(),this.deadClicksAutocapture=new Vf(this,Uf),this.deadClicksAutocapture.startIfEnabled(),this.historyAutocapture=new Yf(this),this.historyAutocapture.startIfEnabled(),rs.DEBUG=rs.DEBUG||this.config.debug,rs.DEBUG&&Ps.info("Starting in debug mode",{this:this,config:t,thisC:ki({},this.config),p:s,s:a}),this._sync_opt_out_with_persistence(),void 0!==(null==(i=t.bootstrap)?void 0:i.distinctID)){var u,l,c=this.config.get_device_id(ia()),d=null!=(u=t.bootstrap)&&u.isIdentifiedID?c:t.bootstrap.distinctID;this.persistence.set_property(Ad,null!=(l=t.bootstrap)&&l.isIdentifiedID?"identified":"anonymous"),this.register({distinct_id:t.bootstrap.distinctID,$device_id:d})}if(this._hasBootstrappedFeatureFlags()){var f,p,_=Object.keys((null==(f=t.bootstrap)?void 0:f.featureFlags)||{}).filter((function(e){var n;return!(null==(n=t.bootstrap)||null==(n=n.featureFlags)||!n[e])})).reduce((function(e,n){var r;return e[n]=(null==(r=t.bootstrap)||null==(r=r.featureFlags)?void 0:r[n])||!1,e}),{}),h=Object.keys((null==(p=t.bootstrap)?void 0:p.featureFlagPayloads)||{}).filter((function(e){return _[e]})).reduce((function(e,n){var r,i;null!=(r=t.bootstrap)&&null!=(r=r.featureFlagPayloads)&&r[n]&&(e[n]=null==(i=t.bootstrap)||null==(i=i.featureFlagPayloads)?void 0:i[n]);return e}),{});this.featureFlags.receivedFeatureFlags({featureFlags:_,featureFlagPayloads:h})}if(this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:Ud,$device_id:null},"");else if(!this.get_distinct_id()){var v=this.config.get_device_id(ia());this.register_once({distinct_id:v,$device_id:v},""),this.persistence.set_property(Ad,"anonymous")}return Vs(Vo,"onpagehide"in self?"pagehide":"unload",this._handle_unload.bind(this),{passive:!1}),this.toolbar.maybeLoadToolbar(),t.segment?Hp(this,(function(){return o._loaded()})):this._loaded(),vs(this.config._onCapture)&&this.config._onCapture!==lh&&(Ps.warn("onCapture is deprecated. Please use `before_send` instead"),this.on("eventCaptured",(function(e){return o.config._onCapture(e.event,e)}))),this},t._onRemoteConfig=function(e){var t,n,r,i,o,s,a,u,l=this;if(!Qo||!Qo.body)return Ps.info("document not ready yet, trying again in 500 milliseconds..."),void setTimeout((function(){l._onRemoteConfig(e)}),500);this.compression=void 0,e.supportedCompression&&!this.config.disable_compression&&(this.compression=us(e.supportedCompression,ss.GZipJS)?ss.GZipJS:us(e.supportedCompression,ss.Base64)?ss.Base64:void 0),null!=(t=e.analytics)&&t.endpoint&&(this.analyticsDefaultEndpoint=e.analytics.endpoint),this.set_config({person_profiles:this._initialPersonProfilesConfig?this._initialPersonProfilesConfig:"identified_only"}),null==(n=this.siteApps)||n.onRemoteConfig(e),null==(r=this.sessionRecording)||r.onRemoteConfig(e),null==(i=this.autocapture)||i.onRemoteConfig(e),null==(o=this.heatmaps)||o.onRemoteConfig(e),this.surveys.onRemoteConfig(e),null==(s=this.webVitalsAutocapture)||s.onRemoteConfig(e),null==(a=this.exceptionObserver)||a.onRemoteConfig(e),this.exceptions.onRemoteConfig(e),null==(u=this.deadClicksAutocapture)||u.onRemoteConfig(e)},t._loaded=function(){var e=this;try{this.config.loaded(this)}catch(e){Ps.critical("`loaded` function failed",e)}this._start_queue_if_opted_in(),this.config.capture_pageview&&setTimeout((function(){e.consent.isOptedIn()&&e._captureInitialPageview()}),1),new B_(this).load(),this.featureFlags.flags()},t._start_queue_if_opted_in=function(){var e;this.has_opted_out_capturing()||this.config.request_batching&&(null==(e=this._requestQueue)||e.enable())},t._dom_loaded=function(){var e=this;this.has_opted_out_capturing()||Ms(this.__request_queue,(function(t){return e._send_retriable_request(t)})),this.__request_queue=[],this._start_queue_if_opted_in()},t._handle_unload=function(){var e,t;this.config.request_batching?(this._shouldCapturePageleave()&&this.capture("$pageleave"),null==(e=this._requestQueue)||e.unload(),null==(t=this._retryQueue)||t.unload()):this._shouldCapturePageleave()&&this.capture("$pageleave",null,{transport:"sendBeacon"})},t._send_request=function(e){var t=this;this.__loaded&&(dh?this.__request_queue.push(e):this.rateLimiter.isServerRateLimited(e.batchKey)||(e.transport=e.transport||this.config.api_transport,e.url=Qu(e.url,{ip:this.config.ip?1:0}),e.headers=ki({},this.config.request_headers),e.compression="best-available"===e.compression?this.compression:e.compression,e.fetchOptions=e.fetchOptions||this.config.fetch_options,function(e){var t,n,r,i=ki({},e);i.timeout=i.timeout||6e4,i.url=Qu(i.url,{_:(new Date).getTime().toString(),ver:rs.LIB_VERSION,compression:i.compression});var o=null!==(t=i.transport)&&void 0!==t?t:"fetch",s=null!==(n=null==(r=Us(Ku,(function(e){return e.transport===o})))?void 0:r.method)&&void 0!==n?n:Ku[0].method;if(!s)throw new Error("No available transport method");s(i)}(ki({},e,{callback:function(n){t.rateLimiter.checkForLimiting(n),n.statusCode>=400&&(null==t.config.on_request_error||t.config.on_request_error(n)),null==e.callback||e.callback(n)}}))))},t._send_retriable_request=function(e){this._retryQueue?this._retryQueue.retriableRequest(e):this._send_request(e)},t._execute_array=function(e){var t,n=this,r=[],i=[],o=[];Ms(e,(function(e){e&&(t=e[0],hs(t)?o.push(e):vs(e)?e.call(n):hs(e)&&"alias"===t?r.push(e):hs(e)&&-1!==t.indexOf("capture")&&vs(n[t])?o.push(e):i.push(e))}));var s=function(e,t){Ms(e,(function(e){if(hs(e[0])){var n=t;Os(e,(function(e){n=n[e[0]].apply(n,e.slice(1))}))}else this[e[0]].apply(this,e.slice(1))}),t)};s(r,this),s(i,this),s(o,this)},t._hasBootstrappedFeatureFlags=function(){var e,t;return(null==(e=this.config.bootstrap)?void 0:e.featureFlags)&&Object.keys(null==(t=this.config.bootstrap)?void 0:t.featureFlags).length>0||!1},t.push=function(e){this._execute_array([e])},t.capture=function(e,t,n){var r;if(this.__loaded&&this.persistence&&this.sessionPersistence&&this._requestQueue){if(!this.consent.isOptedOut())if(!ys(e)&&bs(e)){if(this.config.opt_out_useragent_filter||!this._is_bot()){var i=null!=n&&n.skip_client_rate_limiting?void 0:this.rateLimiter.clientRateLimitContext();if(null==i||!i.isRateLimited){null!=t&&t.$current_url&&!bs(null==t?void 0:t.$current_url)&&(Ps.error("Invalid `$current_url` property provided to `posthog.capture`. Input must be a string. Ignoring provided value."),null==t||delete t.$current_url),this.sessionPersistence.update_search_keyword(),this.config.save_campaign_params&&this.sessionPersistence.update_campaign_params(),this.config.save_referrer&&this.sessionPersistence.update_referrer_info(),(this.config.save_campaign_params||this.config.save_referrer)&&this.persistence.set_initial_person_info();var o=new Date,s=(null==n?void 0:n.timestamp)||o,a=ia(),u={uuid:a,event:e,properties:this.calculateEventProperties(e,t||{},s,a)};i&&(u.properties.$lib_rate_limit_remaining_tokens=i.remainingTokens),(null==n?void 0:n.$set)&&(u.$set=null==n?void 0:n.$set);var l,c,d=this._calculate_set_once_properties(null==n?void 0:n.$set_once);if(d&&(u.$set_once=d),(u=Hs(u,null!=n&&n._noTruncate?null:this.config.properties_string_max_length)).timestamp=s,ys(null==n?void 0:n.timestamp)||(u.properties.$event_time_override_provided=!0,u.properties.$event_time_override_system_time=o),e===jo.DISMISSED||e===jo.SENT){var f,p=null==t?void 0:t[Uo.SURVEY_ID],_=null==t?void 0:t[Uo.SURVEY_ITERATION];localStorage.setItem((c=""+Ys+(l={id:p,current_iteration:_}).id,l.current_iteration&&l.current_iteration>0&&(c=""+Ys+l.id+"_"+l.current_iteration),c),"true"),u.$set=ki({},u.$set,((f={})[function(e,t){var n="$survey_"+t+"/"+e.id;return e.current_iteration&&e.current_iteration>0&&(n="$survey_"+t+"/"+e.id+"/"+e.current_iteration),n}({id:p,current_iteration:_},e===jo.SENT?"responded":"dismissed")]=!0,f))}var h=ki({},u.properties.$set,u.$set);if(ms(h)||this.setPersonPropertiesForFlags(h),!ks(this.config.before_send)){var v=this._runBeforeSend(u);if(!v)return;u=v}this._internalEventEmitter.emit("eventCaptured",u);var g={method:"POST",url:null!==(r=null==n?void 0:n._url)&&void 0!==r?r:this.requestRouter.endpointFor("api",this.analyticsDefaultEndpoint),data:u,compression:"best-available",batchKey:null==n?void 0:n._batchKey};return!this.config.request_batching||n&&(null==n||!n._batchKey)||null!=n&&n.send_instantly?this._send_retriable_request(g):this._requestQueue.enqueue(g),u}Ps.critical("This capture call is ignored due to client rate limiting.")}}else Ps.error("No event name provided to posthog.capture")}else Ps.uninitializedWarning("posthog.capture")},t._addCaptureHook=function(e){return this.on("eventCaptured",(function(t){return e(t.event,t)}))},t.calculateEventProperties=function(e,t,n,r,i){if(n=n||new Date,!this.persistence||!this.sessionPersistence)return t;var o=i?void 0:this.persistence.remove_event_timer(e),s=ki({},t);if(s.token=this.config.token,s.$config_defaults=this.config.defaults,this.config.__preview_experimental_cookieless_mode&&(s.$cookieless_mode=!0),"$snapshot"===e){var a=ki({},this.persistence.properties(),this.sessionPersistence.properties());return s.distinct_id=a.distinct_id,(!bs(s.distinct_id)&&!xs(s.distinct_id)||ws(s.distinct_id))&&Ps.error("Invalid distinct_id for replay event. This indicates a bug in your implementation"),s}var u,l=b_(this.config.mask_personal_data_properties,this.config.custom_personal_data_properties);if(this.sessionManager){var c=this.sessionManager.checkAndGetSessionAndWindowId(i,n.getTime()),d=c.sessionId,f=c.windowId;s.$session_id=d,s.$window_id=f}this.sessionPropsManager&&Ls(s,this.sessionPropsManager.getSessionProps());try{var p;this.sessionRecording&&Ls(s,this.sessionRecording.sdkDebugProperties),s.$sdk_debug_retry_queue_size=null==(p=this._retryQueue)?void 0:p.length}catch(e){s.$sdk_debug_error_capturing_properties=String(e)}if(this.requestRouter.region===rh.CUSTOM&&(s.$lib_custom_api_host=this.config.api_host),u="$pageview"!==e||i?"$pageleave"!==e||i?this.pageViewManager.doEvent():this.pageViewManager.doPageLeave(n):this.pageViewManager.doPageView(n,r),s=Ls(s,u),"$pageview"===e&&Qo&&(s.title=Qo.title),!ys(o)){var _=n.getTime()-o;s.$duration=parseFloat((_/1e3).toFixed(3))}ts&&this.config.opt_out_useragent_filter&&(s.$browser_type=this._is_bot()?"bot":"browser"),(s=Ls({},l,this.persistence.properties(),this.sessionPersistence.properties(),s)).$is_identified=this._isIdentified(),hs(this.config.property_denylist)?Os(this.config.property_denylist,(function(e){delete s[e]})):Ps.error("Invalid value for property_denylist config: "+this.config.property_denylist+" or property_blacklist config: "+this.config.property_blacklist);var h=this.config.sanitize_properties;h&&(Ps.error("sanitize_properties is deprecated. Use before_send instead"),s=h(s,e));var v=this._hasPersonProcessing();return s.$process_person_profile=v,v&&!i&&this._requirePersonProcessing("_calculate_event_properties"),s},t._calculate_set_once_properties=function(e){var t;if(!this.persistence||!this._hasPersonProcessing())return e;if(this._personProcessingSetOncePropertiesSent)return e;var n=this.persistence.get_initial_props(),r=null==(t=this.sessionPropsManager)?void 0:t.getSetOnceProps(),i=Ls({},n,r||{},e||{}),o=this.config.sanitize_properties;return o&&(Ps.error("sanitize_properties is deprecated. Use before_send instead"),i=o(i,"$set_once")),this._personProcessingSetOncePropertiesSent=!0,ms(i)?void 0:i},t.register=function(e,t){var n;null==(n=this.persistence)||n.register(e,t)},t.register_once=function(e,t,n){var r;null==(r=this.persistence)||r.register_once(e,t,n)},t.register_for_session=function(e){var t;null==(t=this.sessionPersistence)||t.register(e)},t.unregister=function(e){var t;null==(t=this.persistence)||t.unregister(e)},t.unregister_for_session=function(e){var t;null==(t=this.sessionPersistence)||t.unregister(e)},t._register_single=function(e,t){var n;this.register(((n={})[e]=t,n))},t.getFeatureFlag=function(e,t){return this.featureFlags.getFeatureFlag(e,t)},t.getFeatureFlagPayload=function(e){var t=this.featureFlags.getFeatureFlagPayload(e);try{return JSON.parse(t)}catch(e){return t}},t.isFeatureEnabled=function(e,t){return this.featureFlags.isFeatureEnabled(e,t)},t.reloadFeatureFlags=function(){this.featureFlags.reloadFeatureFlags()},t.updateEarlyAccessFeatureEnrollment=function(e,t){this.featureFlags.updateEarlyAccessFeatureEnrollment(e,t)},t.getEarlyAccessFeatures=function(e,t,n){return void 0===t&&(t=!1),this.featureFlags.getEarlyAccessFeatures(e,t,n)},t.on=function(e,t){return this._internalEventEmitter.on(e,t)},t.onFeatureFlags=function(e){return this.featureFlags.onFeatureFlags(e)},t.onSurveysLoaded=function(e){return this.surveys.onSurveysLoaded(e)},t.onSessionId=function(e){var t,n;return null!==(t=null==(n=this.sessionManager)?void 0:n.onSessionId(e))&&void 0!==t?t:function(){}},t.getSurveys=function(e,t){void 0===t&&(t=!1),this.surveys.getSurveys(e,t)},t.getActiveMatchingSurveys=function(e,t){void 0===t&&(t=!1),this.surveys.getActiveMatchingSurveys(e,t)},t.renderSurvey=function(e,t){this.surveys.renderSurvey(e,t)},t.canRenderSurvey=function(e){return this.surveys.canRenderSurvey(e)},t.canRenderSurveyAsync=function(e,t){return void 0===t&&(t=!1),this.surveys.canRenderSurveyAsync(e,t)},t.identify=function(e,t,n){if(!this.__loaded||!this.persistence)return Ps.uninitializedWarning("posthog.identify");if(xs(e)&&(e=e.toString(),Ps.warn("The first argument to posthog.identify was a number, but it should be a string. It has been converted to a string.")),e)if(["distinct_id","distinctid"].includes(e.toLowerCase()))Ps.critical('The string "'+e+'" was set in posthog.identify which indicates an error. This ID should be unique to the user and not a hardcoded string.');else if(e!==Ud){if(this._requirePersonProcessing("posthog.identify")){var r=this.get_distinct_id();if(this.register({$user_id:e}),!this.get_property("$device_id")){var i=r;this.register_once({$had_persisted_distinct_id:!0,$device_id:i},"")}e!==r&&e!==this.get_property(sd)&&(this.unregister(sd),this.register({distinct_id:e}));var o="anonymous"===(this.persistence.get_property(Ad)||"anonymous");e!==r&&o?(this.persistence.set_property(Ad,"identified"),this.setPersonPropertiesForFlags(ki({},n||{},t||{}),!1),this.capture("$identify",{distinct_id:e,$anon_distinct_id:r},{$set:t||{},$set_once:n||{}}),this._cachedPersonProperties=tl(e,t,n),this.featureFlags.setAnonymousDistinctId(r)):(t||n)&&this.setPersonProperties(t,n),e!==r&&(this.reloadFeatureFlags(),this.unregister(Ld))}}else Ps.critical('The string "'+Ud+'" was set in posthog.identify which indicates an error. This ID is only used as a sentinel value.');else Ps.error("Unique user id has not been set in posthog.identify")},t.setPersonProperties=function(e,t){if((e||t)&&this._requirePersonProcessing("posthog.setPersonProperties")){var n=tl(this.get_distinct_id(),e,t);this._cachedPersonProperties!==n?(this.setPersonPropertiesForFlags(ki({},t||{},e||{})),this.capture("$set",{$set:e||{},$set_once:t||{}}),this._cachedPersonProperties=n):Ps.info("A duplicate setPersonProperties call was made with the same properties. It has been ignored.")}},t.group=function(e,t,n){var r;if(e&&t){if(this._requirePersonProcessing("posthog.group")){var i,o=this.getGroups();if(o[e]!==t&&this.resetGroupPropertiesForFlags(e),this.register({$groups:ki({},o,(r={},r[e]=t,r))}),n)this.capture("$groupidentify",{$group_type:e,$group_key:t,$group_set:n}),this.setGroupPropertiesForFlags(((i={})[e]=n,i));o[e]===t||n||this.reloadFeatureFlags()}}else Ps.error("posthog.group requires a group type and group key")},t.resetGroups=function(){this.register({$groups:{}}),this.resetGroupPropertiesForFlags(),this.reloadFeatureFlags()},t.setPersonPropertiesForFlags=function(e,t){void 0===t&&(t=!0),this.featureFlags.setPersonPropertiesForFlags(e,t)},t.resetPersonPropertiesForFlags=function(){this.featureFlags.resetPersonPropertiesForFlags()},t.setGroupPropertiesForFlags=function(e,t){void 0===t&&(t=!0),this._requirePersonProcessing("posthog.setGroupPropertiesForFlags")&&this.featureFlags.setGroupPropertiesForFlags(e,t)},t.resetGroupPropertiesForFlags=function(e){this.featureFlags.resetGroupPropertiesForFlags(e)},t.reset=function(e){var t,n,r,i;if(Ps.info("reset"),!this.__loaded)return Ps.uninitializedWarning("posthog.reset");var o=this.get_property("$device_id");if(this.consent.reset(),null==(t=this.persistence)||t.clear(),null==(n=this.sessionPersistence)||n.clear(),this.surveys.reset(),null==(r=this.persistence)||r.set_property(Ad,"anonymous"),null==(i=this.sessionManager)||i.resetSessionId(),this._cachedPersonProperties=null,this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:Ud,$device_id:null},"");else{var s=this.config.get_device_id(ia());this.register_once({distinct_id:s,$device_id:e?s:o},"")}this.register({$last_posthog_reset:(new Date).toISOString()},1)},t.get_distinct_id=function(){return this.get_property("distinct_id")},t.getGroups=function(){return this.get_property("$groups")||{}},t.get_session_id=function(){var e,t;return null!==(e=null==(t=this.sessionManager)?void 0:t.checkAndGetSessionAndWindowId(!0).sessionId)&&void 0!==e?e:""},t.get_session_replay_url=function(e){if(!this.sessionManager)return"";var t=this.sessionManager.checkAndGetSessionAndWindowId(!0),n=t.sessionId,r=t.sessionStartTimestamp,i=this.requestRouter.endpointFor("ui","/project/"+this.config.token+"/replay/"+n);if(null!=e&&e.withTimestamp&&r){var o,s=null!==(o=e.timestampLookBack)&&void 0!==o?o:10;if(!r)return i;i+="?t="+Math.max(Math.floor(((new Date).getTime()-r)/1e3)-s,0)}return i},t.alias=function(e,t){return e===this.get_property(od)?(Ps.critical("Attempting to create alias for existing People user - aborting."),-2):this._requirePersonProcessing("posthog.alias")?(ys(t)&&(t=this.get_distinct_id()),e!==t?(this._register_single(sd,e),this.capture("$create_alias",{alias:e,distinct_id:t})):(Ps.warn("alias matches current distinct_id - skipping api call."),this.identify(e),-1)):void 0},t.set_config=function(e){var t,n,r,i,o=ki({},this.config);gs(e)&&(Ls(this.config,ph(e)),null==(t=this.persistence)||t.update_config(this.config,o),this.sessionPersistence="sessionStorage"===this.config.persistence||"memory"===this.config.persistence?this.persistence:new O_(ki({},this.config,{persistence:"sessionStorage"})),Mf._is_supported()&&"true"===Mf._get("ph_debug")&&(this.config.debug=!0),this.config.debug&&(rs.DEBUG=!0,Ps.info("set_config",{config:e,oldConfig:o,newConfig:ki({},this.config)})),null==(n=this.sessionRecording)||n.startIfEnabledOrStop(),null==(r=this.autocapture)||r.startIfEnabled(),null==(i=this.heatmaps)||i.startIfEnabled(),this.surveys.loadIfEnabled(),this._sync_opt_out_with_persistence())},t.startSessionRecording=function(e){var t=!0===e,n={sampling:t||!(null==e||!e.sampling),linked_flag:t||!(null==e||!e.linked_flag),url_trigger:t||!(null==e||!e.url_trigger),event_trigger:t||!(null==e||!e.event_trigger)};if(Object.values(n).some(Boolean)){var r,i,o,s,a;if(null==(r=this.sessionManager)||r.checkAndGetSessionAndWindowId(),n.sampling)null==(i=this.sessionRecording)||i.overrideSampling();if(n.linked_flag)null==(o=this.sessionRecording)||o.overrideLinkedFlag();if(n.url_trigger)null==(s=this.sessionRecording)||s.overrideTrigger("url");if(n.event_trigger)null==(a=this.sessionRecording)||a.overrideTrigger("event")}this.set_config({disable_session_recording:!1})},t.stopSessionRecording=function(){this.set_config({disable_session_recording:!0})},t.sessionRecordingStarted=function(){var e;return!(null==(e=this.sessionRecording)||!e.started)},t.captureException=function(e,t){var n=new Error("PostHog syntheticException");this.exceptions.sendExceptionEvent(ki({},Xc(function(e){return e instanceof Error}(e)?{error:e,event:e.message}:{event:e},{syntheticException:n}),t))},t.loadToolbar=function(e){return this.toolbar.loadToolbar(e)},t.get_property=function(e){var t;return null==(t=this.persistence)?void 0:t.props[e]},t.getSessionProperty=function(e){var t;return null==(t=this.sessionPersistence)?void 0:t.props[e]},t.toString=function(){var e,t=null!==(e=this.config.name)&&void 0!==e?e:ch;return t!==ch&&(t=ch+"."+t),t},t._isIdentified=function(){var e,t;return"identified"===(null==(e=this.persistence)?void 0:e.get_property(Ad))||"identified"===(null==(t=this.sessionPersistence)?void 0:t.get_property(Ad))},t._hasPersonProcessing=function(){var e,t;return!("never"===this.config.person_profiles||"identified_only"===this.config.person_profiles&&!this._isIdentified()&&ms(this.getGroups())&&(null==(e=this.persistence)||null==(e=e.props)||!e[sd])&&(null==(t=this.persistence)||null==(t=t.props)||!t[Bd]))},t._shouldCapturePageleave=function(){return!0===this.config.capture_pageleave||"if_capture_pageview"===this.config.capture_pageleave&&(!0===this.config.capture_pageview||"history_change"===this.config.capture_pageview)},t.createPersonProfile=function(){this._hasPersonProcessing()||this._requirePersonProcessing("posthog.createPersonProfile")&&this.setPersonProperties({},{})},t._requirePersonProcessing=function(e){return"never"===this.config.person_profiles?(Ps.error(e+' was called, but process_person is set to "never". This call will be ignored.'),!1):(this._register_single(Bd,!0),!0)},t._sync_opt_out_with_persistence=function(){var e,t,n,r,i=this.consent.isOptedOut(),o=this.config.opt_out_persistence_by_default,s=this.config.disable_persistence||i&&!!o;(null==(e=this.persistence)?void 0:e._disabled)!==s&&(null==(n=this.persistence)||n.set_disabled(s));(null==(t=this.sessionPersistence)?void 0:t._disabled)!==s&&(null==(r=this.sessionPersistence)||r.set_disabled(s))},t.opt_in_capturing=function(e){var t;(this.consent.optInOut(!0),this._sync_opt_out_with_persistence(),ys(null==e?void 0:e.captureEventName)||null!=e&&e.captureEventName)&&this.capture(null!==(t=null==e?void 0:e.captureEventName)&&void 0!==t?t:"$opt_in",null==e?void 0:e.captureProperties,{send_instantly:!0});this.config.capture_pageview&&this._captureInitialPageview()},t.opt_out_capturing=function(){this.consent.optInOut(!1),this._sync_opt_out_with_persistence()},t.has_opted_in_capturing=function(){return this.consent.isOptedIn()},t.has_opted_out_capturing=function(){return this.consent.isOptedOut()},t.clear_opt_in_out_capturing=function(){this.consent.reset(),this._sync_opt_out_with_persistence()},t._is_bot=function(){return Yo?nh(Yo,this.config.custom_blocked_useragents):void 0},t._captureInitialPageview=function(){Qo&&("visible"===Qo.visibilityState?this._initialPageviewCaptured||(this._initialPageviewCaptured=!0,this.capture("$pageview",{title:Qo.title},{send_instantly:!0}),this._visibilityStateListener&&(Qo.removeEventListener("visibilitychange",this._visibilityStateListener),this._visibilityStateListener=null)):this._visibilityStateListener||(this._visibilityStateListener=this._captureInitialPageview.bind(this),Vs(Qo,"visibilitychange",this._visibilityStateListener)))},t.debug=function(e){!1===e?(null==Vo||Vo.console.log("You've disabled debug mode."),localStorage&&localStorage.removeItem("ph_debug"),this.set_config({debug:!1})):(null==Vo||Vo.console.log("You're now in debug mode. All calls to PostHog will be logged in your console.\nYou can disable this with `posthog.debug(false)`."),localStorage&&localStorage.setItem("ph_debug","true"),this.set_config({debug:!0}))},t._shouldDisableFlags=function(){var e,t,n,r,i,o,s,a=this._originalUserConfig||{};return"advanced_disable_flags"in a?!!a.advanced_disable_flags:!1!==this.config.advanced_disable_flags?!!this.config.advanced_disable_flags:!0===this.config.advanced_disable_decide?(Ps.warn("Config field 'advanced_disable_decide' is deprecated. Please use 'advanced_disable_flags' instead. The old field will be removed in a future major version."),!0):(n="advanced_disable_decide",r=!1,i=Ps,o=(t="advanced_disable_flags")in(e=a)&&!ys(e[t]),s=n in e&&!ys(e[n]),o?e[t]:s?(i&&i.warn("Config field '"+n+"' is deprecated. Please use '"+t+"' instead. The old field will be removed in a future major version."),e[n]):r)},t._runBeforeSend=function(e){if(ks(this.config.before_send))return e;for(var t,n=e,r=Si(hs(this.config.before_send)?this.config.before_send:[this.config.before_send]);!(t=r()).done;){if(n=(0,t.value)(n),ks(n)){var i="Event '"+e.event+"' was rejected in beforeSend function";return Cs(e.event)?Ps.warn(i+". This can cause unexpected behavior."):Ps.info(i),null}n.properties&&!ms(n.properties)||Ps.warn("Event '"+e.event+"' has no properties after beforeSend function, this is likely an error.")}return n},t.getPageViewId=function(){var e;return null==(e=this.pageViewManager._currentPageview)?void 0:e.pageViewId},t.captureTraceFeedback=function(e,t){this.capture("$ai_feedback",{$ai_trace_id:String(e),$ai_feedback_text:t})},t.captureTraceMetric=function(e,t,n){this.capture("$ai_metric",{$ai_trace_id:String(e),$ai_metric_name:t,$ai_metric_value:String(n)})},wi(e,[{key:"decideEndpointWasHit",get:function(){var e,t;return null!==(e=null==(t=this.featureFlags)?void 0:t.hasLoadedFlags)&&void 0!==e&&e}},{key:"flagsEndpointWasHit",get:function(){var e,t;return null!==(e=null==(t=this.featureFlags)?void 0:t.hasLoadedFlags)&&void 0!==e&&e}}])}();!function(e,t){for(var n=0;n<t.length;n++)e.prototype[t[n]]=Ds(e.prototype[t[n]])}(hh,["identify"]);var vh,gh;vh=uh[ch]=new hh,(gh=ns.posthog)&&Os(gh._i,(function(e){if(e&&hs(e)){var t=vh.init(e[0],e[1],e[2]),n=gh[e[2]]||gh;t&&(t._execute_array.call(t.people,n.people),t._execute_array(n))}})),ns.posthog=vh,function(){function e(){e.done||(e.done=!0,dh=!1,Os(uh,(function(e){e._dom_loaded()})))}null!=Qo&&Qo.addEventListener?"complete"===Qo.readyState?e():Vs(Qo,"DOMContentLoaded",e,{capture:!1}):Vo&&Ps.error("Browser doesn't support `document.addEventListener` so PostHog couldn't be initialized")}()}();
//# sourceMappingURL=array.full.es5.js.map
