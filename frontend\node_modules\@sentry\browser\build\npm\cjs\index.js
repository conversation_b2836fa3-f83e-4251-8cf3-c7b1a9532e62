Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

const feedbackAsync = require('./feedbackAsync.js');
const feedbackSync = require('./feedbackSync.js');
const log = require('./log.js');
const core = require('@sentry/core');
const helpers = require('./helpers.js');
const client = require('./client.js');
const fetch = require('./transports/fetch.js');
const stackParsers = require('./stack-parsers.js');
const eventbuilder = require('./eventbuilder.js');
const userfeedback = require('./userfeedback.js');
const sdk = require('./sdk.js');
const reportDialog = require('./report-dialog.js');
const breadcrumbs = require('./integrations/breadcrumbs.js');
const globalhandlers = require('./integrations/globalhandlers.js');
const httpcontext = require('./integrations/httpcontext.js');
const linkederrors = require('./integrations/linkederrors.js');
const browserapierrors = require('./integrations/browserapierrors.js');
const lazyLoadIntegration = require('./utils/lazyLoadIntegration.js');
const reportingobserver = require('./integrations/reportingobserver.js');
const httpclient = require('./integrations/httpclient.js');
const contextlines = require('./integrations/contextlines.js');
const graphqlClient = require('./integrations/graphqlClient.js');
const replay = require('@sentry-internal/replay');
const replayCanvas = require('@sentry-internal/replay-canvas');
const feedback = require('@sentry-internal/feedback');
const request = require('./tracing/request.js');
const browserTracingIntegration = require('./tracing/browserTracingIntegration.js');
const offline = require('./transports/offline.js');
const integration = require('./profiling/integration.js');
const spotlight = require('./integrations/spotlight.js');
const browsersession = require('./integrations/browsersession.js');
const featureFlagsIntegration = require('./integrations/featureFlags/featureFlagsIntegration.js');
const integration$1 = require('./integrations/featureFlags/launchdarkly/integration.js');
const integration$2 = require('./integrations/featureFlags/openfeature/integration.js');
const integration$3 = require('./integrations/featureFlags/unleash/integration.js');
const integration$4 = require('./integrations/featureFlags/statsig/integration.js');
const diagnoseSdk = require('./diagnose-sdk.js');



exports.feedbackAsyncIntegration = feedbackAsync.feedbackAsyncIntegration;
exports.feedbackIntegration = feedbackSync.feedbackSyncIntegration;
exports.feedbackSyncIntegration = feedbackSync.feedbackSyncIntegration;
exports.logger = log;
exports.SDK_VERSION = core.SDK_VERSION;
exports.SEMANTIC_ATTRIBUTE_SENTRY_OP = core.SEMANTIC_ATTRIBUTE_SENTRY_OP;
exports.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN = core.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN;
exports.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE = core.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE;
exports.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE = core.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE;
exports.Scope = core.Scope;
exports.addBreadcrumb = core.addBreadcrumb;
exports.addEventProcessor = core.addEventProcessor;
exports.addIntegration = core.addIntegration;
exports.captureConsoleIntegration = core.captureConsoleIntegration;
exports.captureEvent = core.captureEvent;
exports.captureException = core.captureException;
exports.captureFeedback = core.captureFeedback;
exports.captureMessage = core.captureMessage;
exports.captureSession = core.captureSession;
exports.close = core.close;
exports.consoleLoggingIntegration = core.consoleLoggingIntegration;
exports.continueTrace = core.continueTrace;
exports.createTransport = core.createTransport;
exports.dedupeIntegration = core.dedupeIntegration;
exports.endSession = core.endSession;
exports.eventFiltersIntegration = core.eventFiltersIntegration;
exports.extraErrorDataIntegration = core.extraErrorDataIntegration;
exports.flush = core.flush;
exports.functionToStringIntegration = core.functionToStringIntegration;
exports.getActiveSpan = core.getActiveSpan;
exports.getClient = core.getClient;
exports.getCurrentScope = core.getCurrentScope;
exports.getGlobalScope = core.getGlobalScope;
exports.getIsolationScope = core.getIsolationScope;
exports.getRootSpan = core.getRootSpan;
exports.getSpanDescendants = core.getSpanDescendants;
exports.getSpanStatusFromHttpCode = core.getSpanStatusFromHttpCode;
exports.getTraceData = core.getTraceData;
exports.inboundFiltersIntegration = core.inboundFiltersIntegration;
exports.instrumentSupabaseClient = core.instrumentSupabaseClient;
exports.isEnabled = core.isEnabled;
exports.isInitialized = core.isInitialized;
exports.lastEventId = core.lastEventId;
exports.makeMultiplexedTransport = core.makeMultiplexedTransport;
exports.moduleMetadataIntegration = core.moduleMetadataIntegration;
exports.parameterize = core.parameterize;
exports.registerSpanErrorInstrumentation = core.registerSpanErrorInstrumentation;
exports.rewriteFramesIntegration = core.rewriteFramesIntegration;
exports.setContext = core.setContext;
exports.setCurrentClient = core.setCurrentClient;
exports.setExtra = core.setExtra;
exports.setExtras = core.setExtras;
exports.setHttpStatus = core.setHttpStatus;
exports.setMeasurement = core.setMeasurement;
exports.setTag = core.setTag;
exports.setTags = core.setTags;
exports.setUser = core.setUser;
exports.spanToBaggageHeader = core.spanToBaggageHeader;
exports.spanToJSON = core.spanToJSON;
exports.spanToTraceHeader = core.spanToTraceHeader;
exports.startInactiveSpan = core.startInactiveSpan;
exports.startNewTrace = core.startNewTrace;
exports.startSession = core.startSession;
exports.startSpan = core.startSpan;
exports.startSpanManual = core.startSpanManual;
exports.supabaseIntegration = core.supabaseIntegration;
exports.suppressTracing = core.suppressTracing;
exports.thirdPartyErrorFilterIntegration = core.thirdPartyErrorFilterIntegration;
exports.updateSpanName = core.updateSpanName;
exports.withActiveSpan = core.withActiveSpan;
exports.withIsolationScope = core.withIsolationScope;
exports.withScope = core.withScope;
exports.zodErrorsIntegration = core.zodErrorsIntegration;
exports.WINDOW = helpers.WINDOW;
exports.BrowserClient = client.BrowserClient;
exports.makeFetchTransport = fetch.makeFetchTransport;
exports.chromeStackLineParser = stackParsers.chromeStackLineParser;
exports.defaultStackLineParsers = stackParsers.defaultStackLineParsers;
exports.defaultStackParser = stackParsers.defaultStackParser;
exports.geckoStackLineParser = stackParsers.geckoStackLineParser;
exports.opera10StackLineParser = stackParsers.opera10StackLineParser;
exports.opera11StackLineParser = stackParsers.opera11StackLineParser;
exports.winjsStackLineParser = stackParsers.winjsStackLineParser;
exports.eventFromException = eventbuilder.eventFromException;
exports.eventFromMessage = eventbuilder.eventFromMessage;
exports.exceptionFromError = eventbuilder.exceptionFromError;
exports.createUserFeedbackEnvelope = userfeedback.createUserFeedbackEnvelope;
exports.forceLoad = sdk.forceLoad;
exports.getDefaultIntegrations = sdk.getDefaultIntegrations;
exports.init = sdk.init;
exports.onLoad = sdk.onLoad;
exports.showReportDialog = reportDialog.showReportDialog;
exports.breadcrumbsIntegration = breadcrumbs.breadcrumbsIntegration;
exports.globalHandlersIntegration = globalhandlers.globalHandlersIntegration;
exports.httpContextIntegration = httpcontext.httpContextIntegration;
exports.linkedErrorsIntegration = linkederrors.linkedErrorsIntegration;
exports.browserApiErrorsIntegration = browserapierrors.browserApiErrorsIntegration;
exports.lazyLoadIntegration = lazyLoadIntegration.lazyLoadIntegration;
exports.reportingObserverIntegration = reportingobserver.reportingObserverIntegration;
exports.httpClientIntegration = httpclient.httpClientIntegration;
exports.contextLinesIntegration = contextlines.contextLinesIntegration;
exports.graphqlClientIntegration = graphqlClient.graphqlClientIntegration;
exports.getReplay = replay.getReplay;
exports.replayIntegration = replay.replayIntegration;
exports.replayCanvasIntegration = replayCanvas.replayCanvasIntegration;
exports.getFeedback = feedback.getFeedback;
exports.sendFeedback = feedback.sendFeedback;
exports.defaultRequestInstrumentationOptions = request.defaultRequestInstrumentationOptions;
exports.instrumentOutgoingRequests = request.instrumentOutgoingRequests;
exports.browserTracingIntegration = browserTracingIntegration.browserTracingIntegration;
exports.startBrowserTracingNavigationSpan = browserTracingIntegration.startBrowserTracingNavigationSpan;
exports.startBrowserTracingPageLoadSpan = browserTracingIntegration.startBrowserTracingPageLoadSpan;
exports.makeBrowserOfflineTransport = offline.makeBrowserOfflineTransport;
exports.browserProfilingIntegration = integration.browserProfilingIntegration;
exports.spotlightBrowserIntegration = spotlight.spotlightBrowserIntegration;
exports.browserSessionIntegration = browsersession.browserSessionIntegration;
exports.featureFlagsIntegration = featureFlagsIntegration.featureFlagsIntegration;
exports.buildLaunchDarklyFlagUsedHandler = integration$1.buildLaunchDarklyFlagUsedHandler;
exports.launchDarklyIntegration = integration$1.launchDarklyIntegration;
exports.OpenFeatureIntegrationHook = integration$2.OpenFeatureIntegrationHook;
exports.openFeatureIntegration = integration$2.openFeatureIntegration;
exports.unleashIntegration = integration$3.unleashIntegration;
exports.statsigIntegration = integration$4.statsigIntegration;
exports.diagnoseSdkConnectivity = diagnoseSdk.diagnoseSdkConnectivity;
//# sourceMappingURL=index.js.map
