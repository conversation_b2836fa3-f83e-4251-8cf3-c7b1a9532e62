{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/DemoBanner.tsx"], "sourcesContent": ["import Link from 'next/link';\r\n\r\nexport const DemoBanner = () => (\r\n  <div className=\"sticky top-0 z-50 bg-gray-900 p-4 text-center text-lg font-semibold text-gray-100 [&_a:hover]:text-indigo-500 [&_a]:text-fuchsia-500\">\r\n    Live Demo of Next.js Boilerplate -\r\n    {' '}\r\n    <Link href=\"/sign-up\">Explore the Authentication</Link>\r\n  </div>\r\n);\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,MAAM,aAAa,kBACxB,8OAAC;QAAI,WAAU;;YAAuI;YAEnJ;0BACD,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAK;0BAAW", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/LocaleSwitcher.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LocaleSwitcher = registerClientReference(\n    function() { throw new Error(\"Attempted to call LocaleSwitcher() from the server but LocaleSwitcher is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/LocaleSwitcher.tsx <module evaluation>\",\n    \"LocaleSwitcher\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,mEACA", "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/LocaleSwitcher.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LocaleSwitcher = registerClientReference(\n    function() { throw new Error(\"Attempted to call LocaleSwitcher() from the server but LocaleSwitcher is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/LocaleSwitcher.tsx\",\n    \"LocaleSwitcher\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,+CACA", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/templates/BaseTemplate.tsx"], "sourcesContent": ["import { useTranslations } from 'next-intl';\r\nimport { AppConfig } from '@/utils/AppConfig';\r\n\r\nexport const BaseTemplate = (props: {\r\n  leftNav: React.ReactNode;\r\n  rightNav?: React.ReactNode;\r\n  children: React.ReactNode;\r\n}) => {\r\n  const t = useTranslations('BaseTemplate');\r\n\r\n  return (\r\n    <div className=\"w-full px-1 text-gray-700 antialiased\">\r\n      <div className=\"mx-auto max-w-screen-md\">\r\n        <header className=\"border-b border-gray-300\">\r\n          <div className=\"pb-8 pt-16\">\r\n            <h1 className=\"text-3xl font-bold text-gray-900\">\r\n              {AppConfig.name}\r\n            </h1>\r\n            <h2 className=\"text-xl\">{t('description')}</h2>\r\n          </div>\r\n\r\n          <div className=\"flex justify-between\">\r\n            <nav>\r\n              <ul className=\"flex flex-wrap gap-x-5 text-xl\">\r\n                {props.leftNav}\r\n              </ul>\r\n            </nav>\r\n\r\n            <nav>\r\n              <ul className=\"flex flex-wrap gap-x-5 text-xl\">\r\n                {props.rightNav}\r\n              </ul>\r\n            </nav>\r\n          </div>\r\n        </header>\r\n\r\n        <main>{props.children}</main>\r\n\r\n        <footer className=\"border-t border-gray-300 py-8 text-center text-sm\">\r\n          {`© Copyright ${new Date().getFullYear()} ${AppConfig.name}. `}\r\n          {t.rich('made_with', {\r\n            author: () => (\r\n              <a\r\n                href=\"https://creativedesignsguru.com\"\r\n                className=\"text-blue-700 hover:border-b-2 hover:border-blue-700\"\r\n              >\r\n                CreativeDesignsGuru\r\n              </a>\r\n            ),\r\n          })}\r\n          {/*\r\n           * PLEASE READ THIS SECTION\r\n           * I'm an indie maker with limited resources and funds, I'll really appreciate if you could have a link to my website.\r\n           * The link doesn't need to appear on every pages, one link on one page is enough.\r\n           * For example, in the `About` page. Thank you for your support, it'll mean a lot to me.\r\n           */}\r\n        </footer>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEO,MAAM,eAAe,CAAC;IAK3B,MAAM,IAAI,CAAA,GAAA,8OAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,yHAAA,CAAA,YAAS,CAAC,IAAI;;;;;;8CAEjB,8OAAC;oCAAG,WAAU;8CAAW,EAAE;;;;;;;;;;;;sCAG7B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CACC,cAAA,8OAAC;wCAAG,WAAU;kDACX,MAAM,OAAO;;;;;;;;;;;8CAIlB,8OAAC;8CACC,cAAA,8OAAC;wCAAG,WAAU;kDACX,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;8BAMvB,8OAAC;8BAAM,MAAM,QAAQ;;;;;;8BAErB,8OAAC;oBAAO,WAAU;;wBACf,CAAC,YAAY,EAAE,IAAI,OAAO,WAAW,GAAG,CAAC,EAAE,yHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC7D,EAAE,IAAI,CAAC,aAAa;4BACnB,QAAQ,kBACN,8OAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;wBAIL;;;;;;;;;;;;;;;;;;AAWV", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/app/%5Blocale%5D/%28marketing%29/layout.tsx"], "sourcesContent": ["import { getTranslations, setRequestLocale } from 'next-intl/server';\r\nimport Link from 'next/link';\r\nimport { DemoBanner } from '@/components/DemoBanner';\r\nimport { LocaleSwitcher } from '@/components/LocaleSwitcher';\r\nimport { BaseTemplate } from '@/templates/BaseTemplate';\r\n\r\nexport default async function Layout(props: {\r\n  children: React.ReactNode;\r\n  params: Promise<{ locale: string }>;\r\n}) {\r\n  const { locale } = await props.params;\r\n  setRequestLocale(locale);\r\n  const t = await getTranslations({\r\n    locale,\r\n    namespace: 'RootLayout',\r\n  });\r\n\r\n  return (\r\n    <>\r\n      <DemoBanner />\r\n      <BaseTemplate\r\n        leftNav={(\r\n          <>\r\n            <li>\r\n              <Link\r\n                href=\"/\"\r\n                className=\"border-none text-gray-700 hover:text-gray-900\"\r\n              >\r\n                {t('home_link')}\r\n              </Link>\r\n            </li>\r\n            <li>\r\n              <Link\r\n                href=\"/about/\"\r\n                className=\"border-none text-gray-700 hover:text-gray-900\"\r\n              >\r\n                {t('about_link')}\r\n              </Link>\r\n            </li>\r\n            <li>\r\n              <Link\r\n                href=\"/counter/\"\r\n                className=\"border-none text-gray-700 hover:text-gray-900\"\r\n              >\r\n                {t('counter_link')}\r\n              </Link>\r\n            </li>\r\n            <li>\r\n              <Link\r\n                href=\"/portfolio/\"\r\n                className=\"border-none text-gray-700 hover:text-gray-900\"\r\n              >\r\n                {t('portfolio_link')}\r\n              </Link>\r\n            </li>\r\n            <li>\r\n              <a\r\n                className=\"border-none text-gray-700 hover:text-gray-900\"\r\n                href=\"https://github.com/ixartz/Next-js-Boilerplate\"\r\n              >\r\n                GitHub\r\n              </a>\r\n            </li>\r\n          </>\r\n        )}\r\n        rightNav={(\r\n          <>\r\n            <li>\r\n              <Link\r\n                href=\"/sign-in/\"\r\n                className=\"border-none text-gray-700 hover:text-gray-900\"\r\n              >\r\n                {t('sign_in_link')}\r\n              </Link>\r\n            </li>\r\n\r\n            <li>\r\n              <Link\r\n                href=\"/sign-up/\"\r\n                className=\"border-none text-gray-700 hover:text-gray-900\"\r\n              >\r\n                {t('sign_up_link')}\r\n              </Link>\r\n            </li>\r\n\r\n            <li>\r\n              <LocaleSwitcher />\r\n            </li>\r\n          </>\r\n        )}\r\n      >\r\n        <div className=\"py-5 text-xl [&_p]:my-6\">{props.children}</div>\r\n      </BaseTemplate>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEe,eAAe,OAAO,KAGpC;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,MAAM;IACrC,CAAA,GAAA,2QAAA,CAAA,mBAAgB,AAAD,EAAE;IACjB,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAC9B;QACA,WAAW;IACb;IAEA,qBACE;;0BACE,8OAAC,gIAAA,CAAA,aAAU;;;;;0BACX,8OAAC,iIAAA,CAAA,eAAY;gBACX,uBACE;;sCACE,8OAAC;sCACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAET,EAAE;;;;;;;;;;;sCAGP,8OAAC;sCACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCA<PERSON>,MAAK;gCACL,WAAU;0CAET,EAAE;;;;;;;;;;;sCAGP,8OAAC;sCACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAET,EAAE;;;;;;;;;;;sCAGP,8OAAC;sCACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAET,EAAE;;;;;;;;;;;sCAGP,8OAAC;sCACC,cAAA,8OAAC;gCACC,WAAU;gCACV,MAAK;0CACN;;;;;;;;;;;;;gBAMP,wBACE;;sCACE,8OAAC;sCACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAET,EAAE;;;;;;;;;;;sCAIP,8OAAC;sCACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAET,EAAE;;;;;;;;;;;sCAIP,8OAAC;sCACC,cAAA,8OAAC,oIAAA,CAAA,iBAAc;;;;;;;;;;;;0BAKrB,cAAA,8OAAC;oBAAI,WAAU;8BAA2B,MAAM,QAAQ;;;;;;;;;;;;;AAIhE", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next-intl/dist/esm/development/server/react-server/getServerTranslator.js"], "sourcesContent": ["import { cache } from 'react';\nimport { createTranslator } from 'use-intl/core';\n\nfunction getServerTranslatorImpl(config, namespace) {\n  return createTranslator({\n    ...config,\n    namespace\n  });\n}\nvar getServerTranslator = cache(getServerTranslatorImpl);\n\nexport { getServerTranslator as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,wBAAwB,MAAM,EAAE,SAAS;IAChD,OAAO,CAAA,GAAA,iLAAA,CAAA,mBAAgB,AAAD,EAAE;QACtB,GAAG,MAAM;QACT;IACF;AACF;AACA,IAAI,sBAAsB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 408, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next-intl/dist/esm/development/server/react-server/getTranslations.js"], "sourcesContent": ["import { cache } from 'react';\nimport getConfig from './getConfig.js';\nimport getServerTranslator from './getServerTranslator.js';\n\n// Maintainer note: `getTranslations` has two different call signatures.\n// We need to define these with function overloads, otherwise TypeScript\n// messes up the return type.\n\n// Call signature 1: `getTranslations(namespace)`\n\n// Call signature 2: `getTranslations({locale, namespace})`\n\n// Implementation\nasync function getTranslations(namespaceOrOpts) {\n  let namespace;\n  let locale;\n  if (typeof namespaceOrOpts === 'string') {\n    namespace = namespaceOrOpts;\n  } else if (namespaceOrOpts) {\n    locale = namespaceOrOpts.locale;\n    namespace = namespaceOrOpts.namespace;\n  }\n  const config = await getConfig(locale);\n  return getServerTranslator(config, namespace);\n}\nvar getTranslations$1 = cache(getTranslations);\n\nexport { getTranslations$1 as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,wEAAwE;AACxE,wEAAwE;AACxE,6BAA6B;AAE7B,iDAAiD;AAEjD,2DAA2D;AAE3D,iBAAiB;AACjB,eAAe,gBAAgB,eAAe;IAC5C,IAAI;IACJ,IAAI;IACJ,IAAI,OAAO,oBAAoB,UAAU;QACvC,YAAY;IACd,OAAO,IAAI,iBAAiB;QAC1B,SAAS,gBAAgB,MAAM;QAC/B,YAAY,gBAAgB,SAAS;IACvC;IACA,MAAM,SAAS,MAAM,CAAA,GAAA,oMAAA,CAAA,UAAS,AAAD,EAAE;IAC/B,OAAO,CAAA,GAAA,8MAAA,CAAA,UAAmB,AAAD,EAAE,QAAQ;AACrC;AACA,IAAI,oBAAoB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next/dist/client/app-dir/link.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/client/app-dir/link.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 459, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next/dist/client/app-dir/link.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/client/app-dir/link.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next/src/client/app-dir/link.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useOptimistic, useRef } from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport type { PENDING_LINK_STATUS } from '../components/links'\nimport {\n  IDLE_LINK_STATUS,\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkForCurrentNavigation,\n  unmountPrefetchableInstance,\n  type LinkInstance,\n} from '../components/links'\nimport { isLocalURL } from '../../shared/lib/router/utils/is-local-url'\nimport { dispatchNavigateAction } from '../components/app-router-instance'\nimport { errorOnce } from '../../shared/lib/utils/error-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `null` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | null\n\n  /**\n   * (unstable) Switch to a dynamic prefetch on hover. Effectively the same as\n   * updating the prefetch prop to `true` in a mouse event.\n   */\n  unstable_dynamicOnHover?: boolean\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @deprecated This will be removed in v16\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  href: string,\n  as: string,\n  linkInstanceRef: React.RefObject<LinkInstance | null>,\n  replace?: boolean,\n  scroll?: boolean,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (\n    (isAnchorNodeName && isModifiedEvent(e)) ||\n    e.currentTarget.hasAttribute('download')\n  ) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    if (replace) {\n      // browser default behavior does not replace the history state\n      // so we need to do it manually\n      e.preventDefault()\n      location.replace(href)\n    }\n\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    if (onNavigate) {\n      let isDefaultPrevented = false\n\n      onNavigate({\n        preventDefault: () => {\n          isDefaultPrevented = true\n        },\n      })\n\n      if (isDefaultPrevented) {\n        return\n      }\n    }\n\n    dispatchNavigateAction(\n      as || href,\n      replace ? 'replace' : 'push',\n      scroll ?? true,\n      linkInstanceRef.current\n    )\n  }\n\n  React.startTransition(navigate)\n}\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nexport default function LinkComponent(\n  props: LinkProps & {\n    children: React.ReactNode\n    ref: React.Ref<HTMLAnchorElement>\n  }\n) {\n  const [linkStatus, setOptimisticLinkStatus] = useOptimistic(IDLE_LINK_STATUS)\n\n  let children: React.ReactNode\n\n  const linkInstanceRef = useRef<LinkInstance | null>(null)\n\n  const {\n    href: hrefProp,\n    as: asProp,\n    children: childrenProp,\n    prefetch: prefetchProp = null,\n    passHref,\n    replace,\n    shallow,\n    scroll,\n    onClick,\n    onMouseEnter: onMouseEnterProp,\n    onTouchStart: onTouchStartProp,\n    legacyBehavior = false,\n    onNavigate,\n    ref: forwardedRef,\n    unstable_dynamicOnHover,\n    ...restProps\n  } = props\n\n  children = childrenProp\n\n  if (\n    legacyBehavior &&\n    (typeof children === 'string' || typeof children === 'number')\n  ) {\n    children = <a>{children}</a>\n  }\n\n  const router = React.useContext(AppRouterContext)\n\n  const prefetchEnabled = prefetchProp !== false\n  /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */\n  const appPrefetchKind =\n    prefetchProp === null ? PrefetchKind.AUTO : PrefetchKind.FULL\n\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      unstable_dynamicOnHover: true,\n      onClick: true,\n      onMouseEnter: true,\n      onTouchStart: true,\n      legacyBehavior: true,\n      onNavigate: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'onClick' ||\n        key === 'onMouseEnter' ||\n        key === 'onTouchStart' ||\n        key === 'onNavigate'\n      ) {\n        if (props[key] && valType !== 'function') {\n          throw createPropError({\n            key,\n            expected: '`function`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'prefetch' ||\n        key === 'legacyBehavior' ||\n        key === 'unstable_dynamicOnHover'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.locale) {\n      warnOnce(\n        'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n      )\n    }\n    if (!asProp) {\n      let href: string | undefined\n      if (typeof hrefProp === 'string') {\n        href = hrefProp\n      } else if (\n        typeof hrefProp === 'object' &&\n        typeof hrefProp.pathname === 'string'\n      ) {\n        href = hrefProp.pathname\n      }\n\n      if (href) {\n        const hasDynamicSegment = href\n          .split('/')\n          .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n        if (hasDynamicSegment) {\n          throw new Error(\n            `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n          )\n        }\n      }\n    }\n  }\n\n  const { href, as } = React.useMemo(() => {\n    const resolvedHref = formatStringOrUrl(hrefProp)\n    return {\n      href: resolvedHref,\n      as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n    }\n  }, [hrefProp, asProp])\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      if (onClick) {\n        console.warn(\n          `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n        )\n      }\n      if (onMouseEnterProp) {\n        console.warn(\n          `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n        )\n      }\n      try {\n        child = React.Children.only(children)\n      } catch (err) {\n        if (!children) {\n          throw new Error(\n            `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n          )\n        }\n        throw new Error(\n          `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n            (typeof window !== 'undefined'\n              ? \" \\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n    } else {\n      child = React.Children.only(children)\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if ((children as any)?.type === 'a') {\n        throw new Error(\n          'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n        )\n      }\n    }\n  }\n\n  const childRef: any = legacyBehavior\n    ? child && typeof child === 'object' && child.ref\n    : forwardedRef\n\n  // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n  // mount. In the future we will also use this to keep track of all the\n  // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n  // a revalidation or refresh.\n  const observeLinkVisibilityOnMount = React.useCallback(\n    (element: HTMLAnchorElement | SVGAElement) => {\n      if (router !== null) {\n        linkInstanceRef.current = mountLinkInstance(\n          element,\n          href,\n          router,\n          appPrefetchKind,\n          prefetchEnabled,\n          setOptimisticLinkStatus\n        )\n      }\n\n      return () => {\n        if (linkInstanceRef.current) {\n          unmountLinkForCurrentNavigation(linkInstanceRef.current)\n          linkInstanceRef.current = null\n        }\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [prefetchEnabled, href, router, appPrefetchKind, setOptimisticLinkStatus]\n  )\n\n  const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n  const childProps: {\n    onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n    onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n    onClick: React.MouseEventHandler<HTMLAnchorElement>\n    href?: string\n    ref?: any\n  } = {\n    ref: mergedRef,\n    onClick(e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!e) {\n          throw new Error(\n            `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n          )\n        }\n      }\n\n      if (!legacyBehavior && typeof onClick === 'function') {\n        onClick(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onClick === 'function'\n      ) {\n        child.props.onClick(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (e.defaultPrevented) {\n        return\n      }\n\n      linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate)\n    },\n    onMouseEnter(e) {\n      if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n        onMouseEnterProp(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onMouseEnter === 'function'\n      ) {\n        child.props.onMouseEnter(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n        return\n      }\n\n      const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n      onNavigationIntent(\n        e.currentTarget as HTMLAnchorElement | SVGAElement,\n        upgradeToDynamicPrefetch\n      )\n    },\n    onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n      ? undefined\n      : function onTouchStart(e) {\n          if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n            onTouchStartProp(e)\n          }\n\n          if (\n            legacyBehavior &&\n            child.props &&\n            typeof child.props.onTouchStart === 'function'\n          ) {\n            child.props.onTouchStart(e)\n          }\n\n          if (!router) {\n            return\n          }\n\n          if (!prefetchEnabled) {\n            return\n          }\n\n          const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n          onNavigationIntent(\n            e.currentTarget as HTMLAnchorElement | SVGAElement,\n            upgradeToDynamicPrefetch\n          )\n        },\n  }\n\n  // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n  // defined, we specify the current 'href', so that repetition is not needed by the user.\n  // If the url is absolute, we can bypass the logic to prepend the basePath.\n  if (isAbsoluteUrl(as)) {\n    childProps.href = as\n  } else if (\n    !legacyBehavior ||\n    passHref ||\n    (child.type === 'a' && !('href' in child.props))\n  ) {\n    childProps.href = addBasePath(as)\n  }\n\n  let link: React.ReactNode\n\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      errorOnce(\n        '`legacyBehavior` is deprecated and will be removed in a future ' +\n          'release. A codemod is available to upgrade your components:\\n\\n' +\n          'npx @next/codemod@latest new-link .\\n\\n' +\n          'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n      )\n    }\n    link = React.cloneElement(child, childProps)\n  } else {\n    link = (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n\n  return (\n    <LinkStatusContext.Provider value={linkStatus}>\n      {link}\n    </LinkStatusContext.Provider>\n  )\n}\n\nconst LinkStatusContext = createContext<\n  typeof PENDING_LINK_STATUS | typeof IDLE_LINK_STATUS\n>(IDLE_LINK_STATUS)\n\nexport const useLinkStatus = () => {\n  return useContext(LinkStatusContext)\n}\n"], "names": ["LinkComponent", "useLinkStatus", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "href", "as", "linkInstanceRef", "replace", "scroll", "onNavigate", "nodeName", "isAnchorNodeName", "toUpperCase", "hasAttribute", "isLocalURL", "preventDefault", "location", "navigate", "isDefaultPrevented", "dispatchNavigateAction", "current", "React", "startTransition", "formatStringOrUrl", "urlObjOrString", "formatUrl", "props", "linkStatus", "setOptimisticLinkStatus", "useOptimistic", "IDLE_LINK_STATUS", "children", "useRef", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "shallow", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "ref", "forwardedRef", "unstable_dynamicOnHover", "restProps", "a", "router", "useContext", "AppRouterContext", "prefetchEnabled", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "process", "env", "NODE_ENV", "createPropError", "args", "Error", "key", "expected", "actual", "window", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "locale", "warnOnce", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "child", "console", "warn", "Children", "only", "err", "type", "childRef", "observeLinkVisibilityOnMount", "useCallback", "element", "mountLinkInstance", "unmountLinkForCurrentNavigation", "unmountPrefetchableInstance", "mergedRef", "useMergedRef", "childProps", "defaultPrevented", "upgradeToDynamicPrefetch", "onNavigationIntent", "__NEXT_LINK_NO_TOUCH_START", "undefined", "isAbsoluteUrl", "addBasePath", "link", "errorOnce", "cloneElement", "LinkStatusContext", "Provider", "value", "createContext"], "mappings": "", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next-intl/dist/esm/development/shared/use.js"], "sourcesContent": ["import * as react from 'react';\n\n// @ts-expect-error -- Ooof, Next.js doesn't make this easy.\n// `use` is only available in React 19 canary, but we can\n// use it in Next.js already as Next.js \"vendors\" a fixed\n// version of React. However, if we'd simply put `use` in\n// ESM code, then the build doesn't work since React does\n// not export `use` officially. Therefore, we have to use\n// something that is not statically analyzable. Once React\n// 19 is out, we can remove this in the next major version.\nvar use = react['use'.trim()];\n\nexport { use as default };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,4DAA4D;AAC5D,yDAAyD;AACzD,yDAAyD;AACzD,yDAAyD;AACzD,yDAAyD;AACzD,yDAAyD;AACzD,0DAA0D;AAC1D,2DAA2D;AAC3D,IAAI,MAAM,qMAAK,CAAC,MAAM,IAAI,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next-intl/dist/esm/development/react-server/useConfig.js"], "sourcesContent": ["import getConfig from '../server/react-server/getConfig.js';\nimport use from '../shared/use.js';\n\nfunction useHook(hookName, promise) {\n  try {\n    return use(promise);\n  } catch (error) {\n    if (error instanceof TypeError && error.message.includes(\"Cannot read properties of null (reading 'use')\")) {\n      throw new Error(`\\`${hookName}\\` is not callable within an async component. Please refer to https://next-intl.dev/docs/environments/server-client-components#async-components`, {\n        cause: error\n      });\n    } else {\n      throw error;\n    }\n  }\n}\nfunction useConfig(hookName) {\n  return useHook(hookName, getConfig());\n}\n\nexport { useConfig as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,QAAQ,QAAQ,EAAE,OAAO;IAChC,IAAI;QACF,OAAO,CAAA,GAAA,2KAAA,CAAA,UAAG,AAAD,EAAE;IACb,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,aAAa,MAAM,OAAO,CAAC,QAAQ,CAAC,mDAAmD;YAC1G,MAAM,IAAI,MAAM,CAAC,EAAE,EAAE,SAAS,+IAA+I,CAAC,EAAE;gBAC9K,OAAO;YACT;QACF,OAAO;YACL,MAAM;QACR;IACF;AACF;AACA,SAAS,UAAU,QAAQ;IACzB,OAAO,QAAQ,UAAU,CAAA,GAAA,oMAAA,CAAA,UAAS,AAAD;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next-intl/dist/esm/development/react-server/useTranslations.js"], "sourcesContent": ["import getServerTranslator from '../server/react-server/getServerTranslator.js';\nimport useConfig from './useConfig.js';\n\nfunction useTranslations(...[namespace]) {\n  const config = useConfig('useTranslations');\n  return getServerTranslator(config, namespace);\n}\n\nexport { useTranslations as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,gBAAgB,GAAG,CAAC,UAAU;IACrC,MAAM,SAAS,CAAA,GAAA,0LAAA,CAAA,UAAS,AAAD,EAAE;IACzB,OAAO,CAAA,GAAA,8MAAA,CAAA,UAAmB,AAAD,EAAE,QAAQ;AACrC", "ignoreList": [0], "debugId": null}}]}