'use strict';

module.exports = new Map([
	['[0-9]', {value: '\\d'}],
	['[^0-9]', {value: '\\D'}],

	// Word
	['[a-zA-Z0-9_]', {value: '\\w'}],
	['[a-zA-Z_0-9]', {value: '\\w'}],
	['[a-z0-9A-Z_]', {value: '\\w'}],
	['[a-z0-9_A-Z]', {value: '\\w'}],
	['[a-z_A-Z0-9]', {value: '\\w'}],
	['[a-z_0-9A-Z]', {value: '\\w'}],
	['[A-Za-z0-9_]', {value: '\\w'}],
	['[A-Za-z_0-9]', {value: '\\w'}],
	['[A-Z0-9a-z_]', {value: '\\w'}],
	['[A-Z0-9_a-z]', {value: '\\w'}],
	['[A-Z_a-z0-9]', {value: '\\w'}],
	['[A-Z_0-9a-z]', {value: '\\w'}],
	['[0-9a-zA-Z_]', {value: '\\w'}],
	['[0-9a-z_A-Z]', {value: '\\w'}],
	['[0-9A-Za-z_]', {value: '\\w'}],
	['[0-9A-Z_a-z]', {value: '\\w'}],
	['[0-9_a-zA-Z]', {value: '\\w'}],
	['[0-9_A-Za-z]', {value: '\\w'}],
	['[_a-zA-Z0-9]', {value: '\\w'}],
	['[_a-z0-9A-Z]', {value: '\\w'}],
	['[_A-Za-z0-9]', {value: '\\w'}],
	['[_A-Z0-9a-z]', {value: '\\w'}],
	['[_0-9a-zA-Z]', {value: '\\w'}],
	['[_0-9A-Za-z]', {value: '\\w'}],

	// Word with digit
	['[a-zA-Z\\d_]', {value: '\\w'}],
	['[a-zA-Z_\\d]', {value: '\\w'}],
	['[a-z\\dA-Z_]', {value: '\\w'}],
	['[a-z\\d_A-Z]', {value: '\\w'}],
	['[a-z_A-Z\\d]', {value: '\\w'}],
	['[a-z_\\dA-Z]', {value: '\\w'}],
	['[A-Za-z\\d_]', {value: '\\w'}],
	['[A-Za-z_\\d]', {value: '\\w'}],
	['[A-Z\\da-z_]', {value: '\\w'}],
	['[A-Z\\d_a-z]', {value: '\\w'}],
	['[A-Z_a-z\\d]', {value: '\\w'}],
	['[A-Z_\\da-z]', {value: '\\w'}],
	['[\\da-zA-Z_]', {value: '\\w'}],
	['[\\da-z_A-Z]', {value: '\\w'}],
	['[\\dA-Za-z_]', {value: '\\w'}],
	['[\\dA-Z_a-z]', {value: '\\w'}],
	['[\\d_a-zA-Z]', {value: '\\w'}],
	['[\\d_A-Za-z]', {value: '\\w'}],
	['[_a-zA-Z\\d]', {value: '\\w'}],
	['[_a-z\\dA-Z]', {value: '\\w'}],
	['[_A-Za-z\\d]', {value: '\\w'}],
	['[_A-Z\\da-z]', {value: '\\w'}],
	['[_\\da-zA-Z]', {value: '\\w'}],
	['[_\\dA-Za-z]', {value: '\\w'}],

	// Non-word
	['[^a-zA-Z0-9_]', {value: '\\W'}],
	['[^a-zA-Z_0-9]', {value: '\\W'}],
	['[^a-z0-9A-Z_]', {value: '\\W'}],
	['[^a-z0-9_A-Z]', {value: '\\W'}],
	['[^a-z_A-Z0-9]', {value: '\\W'}],
	['[^a-z_0-9A-Z]', {value: '\\W'}],
	['[^A-Za-z0-9_]', {value: '\\W'}],
	['[^A-Za-z_0-9]', {value: '\\W'}],
	['[^A-Z0-9a-z_]', {value: '\\W'}],
	['[^A-Z0-9_a-z]', {value: '\\W'}],
	['[^A-Z_a-z0-9]', {value: '\\W'}],
	['[^A-Z_0-9a-z]', {value: '\\W'}],
	['[^0-9a-zA-Z_]', {value: '\\W'}],
	['[^0-9a-z_A-Z]', {value: '\\W'}],
	['[^0-9A-Za-z_]', {value: '\\W'}],
	['[^0-9A-Z_a-z]', {value: '\\W'}],
	['[^0-9_a-zA-Z]', {value: '\\W'}],
	['[^0-9_A-Za-z]', {value: '\\W'}],
	['[^_a-zA-Z0-9]', {value: '\\W'}],
	['[^_a-z0-9A-Z]', {value: '\\W'}],
	['[^_A-Za-z0-9]', {value: '\\W'}],
	['[^_A-Z0-9a-z]', {value: '\\W'}],
	['[^_0-9a-zA-Z]', {value: '\\W'}],
	['[^_0-9A-Za-z]', {value: '\\W'}],

	// Non-word with digit
	['[^a-zA-Z\\d_]', {value: '\\W'}],
	['[^a-zA-Z_\\d]', {value: '\\W'}],
	['[^a-z\\dA-Z_]', {value: '\\W'}],
	['[^a-z\\d_A-Z]', {value: '\\W'}],
	['[^a-z_A-Z\\d]', {value: '\\W'}],
	['[^a-z_\\dA-Z]', {value: '\\W'}],
	['[^A-Za-z\\d_]', {value: '\\W'}],
	['[^A-Za-z_\\d]', {value: '\\W'}],
	['[^A-Z\\da-z_]', {value: '\\W'}],
	['[^A-Z\\d_a-z]', {value: '\\W'}],
	['[^A-Z_a-z\\d]', {value: '\\W'}],
	['[^A-Z_\\da-z]', {value: '\\W'}],
	['[^\\da-zA-Z_]', {value: '\\W'}],
	['[^\\da-z_A-Z]', {value: '\\W'}],
	['[^\\dA-Za-z_]', {value: '\\W'}],
	['[^\\dA-Z_a-z]', {value: '\\W'}],
	['[^\\d_a-zA-Z]', {value: '\\W'}],
	['[^\\d_A-Za-z]', {value: '\\W'}],
	['[^_a-zA-Z\\d]', {value: '\\W'}],
	['[^_a-z\\dA-Z]', {value: '\\W'}],
	['[^_A-Za-z\\d]', {value: '\\W'}],
	['[^_A-Z\\da-z]', {value: '\\W'}],
	['[^_\\da-zA-Z]', {value: '\\W'}],
	['[^_\\dA-Za-z]', {value: '\\W'}],

	// Word with case insensitivity
	['[a-z0-9_]', {value: '\\w', flags: 'i'}],
	['[a-z_0-9]', {value: '\\w', flags: 'i'}],
	['[0-9a-z_]', {value: '\\w', flags: 'i'}],
	['[0-9_a-z]', {value: '\\w', flags: 'i'}],
	['[_a-z0-9]', {value: '\\w', flags: 'i'}],
	['[_0-9a-z]', {value: '\\w', flags: 'i'}],
	['[^a-z0-9_]', {value: '\\W', flags: 'i'}],

	// Word with case insensitivity and digit
	['[a-z\\d_]', {value: '\\w', flags: 'i'}],
	['[a-z_\\d]', {value: '\\w', flags: 'i'}],
	['[\\da-z_]', {value: '\\w', flags: 'i'}],
	['[\\d_a-z]', {value: '\\w', flags: 'i'}],
	['[_a-z\\d]', {value: '\\w', flags: 'i'}],
	['[_\\da-z]', {value: '\\w', flags: 'i'}],

	// Non-word with case insensitivity
	['[^a-z0-9_]', {value: '\\W', flags: 'i'}],
	['[^a-z_0-9]', {value: '\\W', flags: 'i'}],
	['[^0-9a-z_]', {value: '\\W', flags: 'i'}],
	['[^0-9_a-z]', {value: '\\W', flags: 'i'}],
	['[^_a-z0-9]', {value: '\\W', flags: 'i'}],
	['[^_0-9a-z]', {value: '\\W', flags: 'i'}],

	// Non-word with case insensitivity and digit
	['[^a-z\\d_]', {value: '\\W', flags: 'i'}],
	['[^a-z_\\d]', {value: '\\W', flags: 'i'}],
	['[^\\da-z_]', {value: '\\W', flags: 'i'}],
	['[^\\d_a-z]', {value: '\\W', flags: 'i'}],
	['[^_a-z\\d]', {value: '\\W', flags: 'i'}],
	['[^_\\da-z]', {value: '\\W', flags: 'i'}]
]);
