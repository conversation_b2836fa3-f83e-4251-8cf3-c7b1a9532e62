{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry/node/node_modules/%40opentelemetry/core/node_modules/%40opentelemetry/semantic-conventions/src/internal/utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Creates a const map from the given values\n * @param values - An array of values to be used as keys and values in the map.\n * @returns A populated version of the map with the values and keys derived from the values.\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function createConstMap<T>(values: Array<T[keyof T]>): T {\n  // eslint-disable-next-line prefer-const, @typescript-eslint/no-explicit-any\n  let res: any = {};\n  const len = values.length;\n  for (let lp = 0; lp < len; lp++) {\n    const val = values[lp];\n    if (val) {\n      res[String(val).toUpperCase().replace(/[-.]/g, '_')] = val;\n    }\n  }\n\n  return res as T;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH;;;;GAIG,CACH,sBAAA,EAAwB;;;AAClB,SAAU,cAAc,CAAI,MAAyB;IACzD,4EAA4E;IAC5E,IAAI,GAAG,GAAQ,CAAA,CAAE,CAAC;IAClB,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;IAC1B,IAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,CAAE;QAC/B,IAAM,GAAG,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;QACvB,IAAI,GAAG,EAAE;YACP,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;SAC5D;KACF;IAED,OAAO,GAAQ,CAAC;AAClB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "file": "SemanticResourceAttributes.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry/node/node_modules/%40opentelemetry/core/node_modules/%40opentelemetry/semantic-conventions/src/resource/SemanticResourceAttributes.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createConstMap } from '../internal/utils';\n\n//----------------------------------------------------------------------------------------------------------\n// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates//templates/SemanticAttributes.ts.j2\n//----------------------------------------------------------------------------------------------------------\n\n//----------------------------------------------------------------------------------------------------------\n// Constant values for SemanticResourceAttributes\n//----------------------------------------------------------------------------------------------------------\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_CLOUD_PROVIDER = 'cloud.provider';\nconst TMP_CLOUD_ACCOUNT_ID = 'cloud.account.id';\nconst TMP_CLOUD_REGION = 'cloud.region';\nconst TMP_CLOUD_AVAILABILITY_ZONE = 'cloud.availability_zone';\nconst TMP_CLOUD_PLATFORM = 'cloud.platform';\nconst TMP_AWS_ECS_CONTAINER_ARN = 'aws.ecs.container.arn';\nconst TMP_AWS_ECS_CLUSTER_ARN = 'aws.ecs.cluster.arn';\nconst TMP_AWS_ECS_LAUNCHTYPE = 'aws.ecs.launchtype';\nconst TMP_AWS_ECS_TASK_ARN = 'aws.ecs.task.arn';\nconst TMP_AWS_ECS_TASK_FAMILY = 'aws.ecs.task.family';\nconst TMP_AWS_ECS_TASK_REVISION = 'aws.ecs.task.revision';\nconst TMP_AWS_EKS_CLUSTER_ARN = 'aws.eks.cluster.arn';\nconst TMP_AWS_LOG_GROUP_NAMES = 'aws.log.group.names';\nconst TMP_AWS_LOG_GROUP_ARNS = 'aws.log.group.arns';\nconst TMP_AWS_LOG_STREAM_NAMES = 'aws.log.stream.names';\nconst TMP_AWS_LOG_STREAM_ARNS = 'aws.log.stream.arns';\nconst TMP_CONTAINER_NAME = 'container.name';\nconst TMP_CONTAINER_ID = 'container.id';\nconst TMP_CONTAINER_RUNTIME = 'container.runtime';\nconst TMP_CONTAINER_IMAGE_NAME = 'container.image.name';\nconst TMP_CONTAINER_IMAGE_TAG = 'container.image.tag';\nconst TMP_DEPLOYMENT_ENVIRONMENT = 'deployment.environment';\nconst TMP_DEVICE_ID = 'device.id';\nconst TMP_DEVICE_MODEL_IDENTIFIER = 'device.model.identifier';\nconst TMP_DEVICE_MODEL_NAME = 'device.model.name';\nconst TMP_FAAS_NAME = 'faas.name';\nconst TMP_FAAS_ID = 'faas.id';\nconst TMP_FAAS_VERSION = 'faas.version';\nconst TMP_FAAS_INSTANCE = 'faas.instance';\nconst TMP_FAAS_MAX_MEMORY = 'faas.max_memory';\nconst TMP_HOST_ID = 'host.id';\nconst TMP_HOST_NAME = 'host.name';\nconst TMP_HOST_TYPE = 'host.type';\nconst TMP_HOST_ARCH = 'host.arch';\nconst TMP_HOST_IMAGE_NAME = 'host.image.name';\nconst TMP_HOST_IMAGE_ID = 'host.image.id';\nconst TMP_HOST_IMAGE_VERSION = 'host.image.version';\nconst TMP_K8S_CLUSTER_NAME = 'k8s.cluster.name';\nconst TMP_K8S_NODE_NAME = 'k8s.node.name';\nconst TMP_K8S_NODE_UID = 'k8s.node.uid';\nconst TMP_K8S_NAMESPACE_NAME = 'k8s.namespace.name';\nconst TMP_K8S_POD_UID = 'k8s.pod.uid';\nconst TMP_K8S_POD_NAME = 'k8s.pod.name';\nconst TMP_K8S_CONTAINER_NAME = 'k8s.container.name';\nconst TMP_K8S_REPLICASET_UID = 'k8s.replicaset.uid';\nconst TMP_K8S_REPLICASET_NAME = 'k8s.replicaset.name';\nconst TMP_K8S_DEPLOYMENT_UID = 'k8s.deployment.uid';\nconst TMP_K8S_DEPLOYMENT_NAME = 'k8s.deployment.name';\nconst TMP_K8S_STATEFULSET_UID = 'k8s.statefulset.uid';\nconst TMP_K8S_STATEFULSET_NAME = 'k8s.statefulset.name';\nconst TMP_K8S_DAEMONSET_UID = 'k8s.daemonset.uid';\nconst TMP_K8S_DAEMONSET_NAME = 'k8s.daemonset.name';\nconst TMP_K8S_JOB_UID = 'k8s.job.uid';\nconst TMP_K8S_JOB_NAME = 'k8s.job.name';\nconst TMP_K8S_CRONJOB_UID = 'k8s.cronjob.uid';\nconst TMP_K8S_CRONJOB_NAME = 'k8s.cronjob.name';\nconst TMP_OS_TYPE = 'os.type';\nconst TMP_OS_DESCRIPTION = 'os.description';\nconst TMP_OS_NAME = 'os.name';\nconst TMP_OS_VERSION = 'os.version';\nconst TMP_PROCESS_PID = 'process.pid';\nconst TMP_PROCESS_EXECUTABLE_NAME = 'process.executable.name';\nconst TMP_PROCESS_EXECUTABLE_PATH = 'process.executable.path';\nconst TMP_PROCESS_COMMAND = 'process.command';\nconst TMP_PROCESS_COMMAND_LINE = 'process.command_line';\nconst TMP_PROCESS_COMMAND_ARGS = 'process.command_args';\nconst TMP_PROCESS_OWNER = 'process.owner';\nconst TMP_PROCESS_RUNTIME_NAME = 'process.runtime.name';\nconst TMP_PROCESS_RUNTIME_VERSION = 'process.runtime.version';\nconst TMP_PROCESS_RUNTIME_DESCRIPTION = 'process.runtime.description';\nconst TMP_SERVICE_NAME = 'service.name';\nconst TMP_SERVICE_NAMESPACE = 'service.namespace';\nconst TMP_SERVICE_INSTANCE_ID = 'service.instance.id';\nconst TMP_SERVICE_VERSION = 'service.version';\nconst TMP_TELEMETRY_SDK_NAME = 'telemetry.sdk.name';\nconst TMP_TELEMETRY_SDK_LANGUAGE = 'telemetry.sdk.language';\nconst TMP_TELEMETRY_SDK_VERSION = 'telemetry.sdk.version';\nconst TMP_TELEMETRY_AUTO_VERSION = 'telemetry.auto.version';\nconst TMP_WEBENGINE_NAME = 'webengine.name';\nconst TMP_WEBENGINE_VERSION = 'webengine.version';\nconst TMP_WEBENGINE_DESCRIPTION = 'webengine.description';\n\n/**\n * Name of the cloud provider.\n *\n * @deprecated Use ATTR_CLOUD_PROVIDER in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CLOUD_PROVIDER = TMP_CLOUD_PROVIDER;\n\n/**\n * The cloud account ID the resource is assigned to.\n *\n * @deprecated Use ATTR_CLOUD_ACCOUNT_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CLOUD_ACCOUNT_ID = TMP_CLOUD_ACCOUNT_ID;\n\n/**\n * The geographical region the resource is running. Refer to your provider&#39;s docs to see the available regions, for example [Alibaba Cloud regions](https://www.alibabacloud.com/help/doc-detail/40654.htm), [AWS regions](https://aws.amazon.com/about-aws/global-infrastructure/regions_az/), [Azure regions](https://azure.microsoft.com/en-us/global-infrastructure/geographies/), or [Google Cloud regions](https://cloud.google.com/about/locations).\n *\n * @deprecated Use ATTR_CLOUD_REGION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CLOUD_REGION = TMP_CLOUD_REGION;\n\n/**\n * Cloud regions often have multiple, isolated locations known as zones to increase availability. Availability zone represents the zone where the resource is running.\n *\n * Note: Availability zones are called &#34;zones&#34; on Alibaba Cloud and Google Cloud.\n *\n * @deprecated Use ATTR_CLOUD_AVAILABILITY_ZONE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CLOUD_AVAILABILITY_ZONE = TMP_CLOUD_AVAILABILITY_ZONE;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use ATTR_CLOUD_PLATFORM in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CLOUD_PLATFORM = TMP_CLOUD_PLATFORM;\n\n/**\n * The Amazon Resource Name (ARN) of an [ECS container instance](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/ECS_instances.html).\n *\n * @deprecated Use ATTR_AWS_ECS_CONTAINER_ARN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_ECS_CONTAINER_ARN = TMP_AWS_ECS_CONTAINER_ARN;\n\n/**\n * The ARN of an [ECS cluster](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/clusters.html).\n *\n * @deprecated Use ATTR_AWS_ECS_CLUSTER_ARN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_ECS_CLUSTER_ARN = TMP_AWS_ECS_CLUSTER_ARN;\n\n/**\n * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.\n *\n * @deprecated Use ATTR_AWS_ECS_LAUNCHTYPE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_ECS_LAUNCHTYPE = TMP_AWS_ECS_LAUNCHTYPE;\n\n/**\n * The ARN of an [ECS task definition](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/task_definitions.html).\n *\n * @deprecated Use ATTR_AWS_ECS_TASK_ARN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_ECS_TASK_ARN = TMP_AWS_ECS_TASK_ARN;\n\n/**\n * The task definition family this task definition is a member of.\n *\n * @deprecated Use ATTR_AWS_ECS_TASK_FAMILY in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_ECS_TASK_FAMILY = TMP_AWS_ECS_TASK_FAMILY;\n\n/**\n * The revision for this task definition.\n *\n * @deprecated Use ATTR_AWS_ECS_TASK_REVISION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_ECS_TASK_REVISION = TMP_AWS_ECS_TASK_REVISION;\n\n/**\n * The ARN of an EKS cluster.\n *\n * @deprecated Use ATTR_AWS_EKS_CLUSTER_ARN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_EKS_CLUSTER_ARN = TMP_AWS_EKS_CLUSTER_ARN;\n\n/**\n * The name(s) of the AWS log group(s) an application is writing to.\n *\n * Note: Multiple log groups must be supported for cases like multi-container applications, where a single application has sidecar containers, and each write to their own log group.\n *\n * @deprecated Use ATTR_AWS_LOG_GROUP_NAMES in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_LOG_GROUP_NAMES = TMP_AWS_LOG_GROUP_NAMES;\n\n/**\n * The Amazon Resource Name(s) (ARN) of the AWS log group(s).\n *\n * Note: See the [log group ARN format documentation](https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/iam-access-control-overview-cwl.html#CWL_ARN_Format).\n *\n * @deprecated Use ATTR_AWS_LOG_GROUP_ARNS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_LOG_GROUP_ARNS = TMP_AWS_LOG_GROUP_ARNS;\n\n/**\n * The name(s) of the AWS log stream(s) an application is writing to.\n *\n * @deprecated Use ATTR_AWS_LOG_STREAM_NAMES in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_LOG_STREAM_NAMES = TMP_AWS_LOG_STREAM_NAMES;\n\n/**\n * The ARN(s) of the AWS log stream(s).\n *\n * Note: See the [log stream ARN format documentation](https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/iam-access-control-overview-cwl.html#CWL_ARN_Format). One log group can contain several log streams, so these ARNs necessarily identify both a log group and a log stream.\n *\n * @deprecated Use ATTR_AWS_LOG_STREAM_ARNS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_LOG_STREAM_ARNS = TMP_AWS_LOG_STREAM_ARNS;\n\n/**\n * Container name.\n *\n * @deprecated Use ATTR_CONTAINER_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CONTAINER_NAME = TMP_CONTAINER_NAME;\n\n/**\n * Container ID. Usually a UUID, as for example used to [identify Docker containers](https://docs.docker.com/engine/reference/run/#container-identification). The UUID might be abbreviated.\n *\n * @deprecated Use ATTR_CONTAINER_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CONTAINER_ID = TMP_CONTAINER_ID;\n\n/**\n * The container runtime managing this container.\n *\n * @deprecated Use ATTR_CONTAINER_RUNTIME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CONTAINER_RUNTIME = TMP_CONTAINER_RUNTIME;\n\n/**\n * Name of the image the container was built on.\n *\n * @deprecated Use ATTR_CONTAINER_IMAGE_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CONTAINER_IMAGE_NAME = TMP_CONTAINER_IMAGE_NAME;\n\n/**\n * Container image tag.\n *\n * @deprecated Use ATTR_CONTAINER_IMAGE_TAGS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CONTAINER_IMAGE_TAG = TMP_CONTAINER_IMAGE_TAG;\n\n/**\n * Name of the [deployment environment](https://en.wikipedia.org/wiki/Deployment_environment) (aka deployment tier).\n *\n * @deprecated Use ATTR_DEPLOYMENT_ENVIRONMENT in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_DEPLOYMENT_ENVIRONMENT = TMP_DEPLOYMENT_ENVIRONMENT;\n\n/**\n * A unique identifier representing the device.\n *\n * Note: The device identifier MUST only be defined using the values outlined below. This value is not an advertising identifier and MUST NOT be used as such. On iOS (Swift or Objective-C), this value MUST be equal to the [vendor identifier](https://developer.apple.com/documentation/uikit/uidevice/1620059-identifierforvendor). On Android (Java or Kotlin), this value MUST be equal to the Firebase Installation ID or a globally unique UUID which is persisted across sessions in your application. More information can be found [here](https://developer.android.com/training/articles/user-data-ids) on best practices and exact implementation details. Caution should be taken when storing personal data or anything which can identify a user. GDPR and data protection laws may apply, ensure you do your own due diligence.\n *\n * @deprecated Use ATTR_DEVICE_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_DEVICE_ID = TMP_DEVICE_ID;\n\n/**\n * The model identifier for the device.\n *\n * Note: It&#39;s recommended this value represents a machine readable version of the model identifier rather than the market or consumer-friendly name of the device.\n *\n * @deprecated Use ATTR_DEVICE_MODEL_IDENTIFIER in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_DEVICE_MODEL_IDENTIFIER = TMP_DEVICE_MODEL_IDENTIFIER;\n\n/**\n * The marketing name for the device model.\n *\n * Note: It&#39;s recommended this value represents a human readable version of the device model rather than a machine readable alternative.\n *\n * @deprecated Use ATTR_DEVICE_MODEL_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_DEVICE_MODEL_NAME = TMP_DEVICE_MODEL_NAME;\n\n/**\n * The name of the single function that this runtime instance executes.\n *\n * Note: This is the name of the function as configured/deployed on the FaaS platform and is usually different from the name of the callback function (which may be stored in the [`code.namespace`/`code.function`](../../trace/semantic_conventions/span-general.md#source-code-attributes) span attributes).\n *\n * @deprecated Use ATTR_FAAS_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_FAAS_NAME = TMP_FAAS_NAME;\n\n/**\n* The unique ID of the single function that this runtime instance executes.\n*\n* Note: Depending on the cloud provider, use:\n\n* **AWS Lambda:** The function [ARN](https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html).\nTake care not to use the &#34;invoked ARN&#34; directly but replace any\n[alias suffix](https://docs.aws.amazon.com/lambda/latest/dg/configuration-aliases.html) with the resolved function version, as the same runtime instance may be invokable with multiple\ndifferent aliases.\n* **GCP:** The [URI of the resource](https://cloud.google.com/iam/docs/full-resource-names)\n* **Azure:** The [Fully Qualified Resource ID](https://docs.microsoft.com/en-us/rest/api/resources/resources/get-by-id).\n\nOn some providers, it may not be possible to determine the full ID at startup,\nwhich is why this field cannot be made required. For example, on AWS the account ID\npart of the ARN is not available without calling another AWS API\nwhich may be deemed too slow for a short-running lambda function.\nAs an alternative, consider setting `faas.id` as a span attribute instead.\n*\n* @deprecated Use ATTR_CLOUD_RESOURCE_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n*/\nexport const SEMRESATTRS_FAAS_ID = TMP_FAAS_ID;\n\n/**\n* The immutable version of the function being executed.\n*\n* Note: Depending on the cloud provider and platform, use:\n\n* **AWS Lambda:** The [function version](https://docs.aws.amazon.com/lambda/latest/dg/configuration-versions.html)\n  (an integer represented as a decimal string).\n* **Google Cloud Run:** The [revision](https://cloud.google.com/run/docs/managing/revisions)\n  (i.e., the function name plus the revision suffix).\n* **Google Cloud Functions:** The value of the\n  [`K_REVISION` environment variable](https://cloud.google.com/functions/docs/env-var#runtime_environment_variables_set_automatically).\n* **Azure Functions:** Not applicable. Do not set this attribute.\n*\n* @deprecated Use ATTR_FAAS_VERSION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n*/\nexport const SEMRESATTRS_FAAS_VERSION = TMP_FAAS_VERSION;\n\n/**\n * The execution environment ID as a string, that will be potentially reused for other invocations to the same function/function version.\n *\n * Note: * **AWS Lambda:** Use the (full) log stream name.\n *\n * @deprecated Use ATTR_FAAS_INSTANCE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_FAAS_INSTANCE = TMP_FAAS_INSTANCE;\n\n/**\n * The amount of memory available to the serverless function in MiB.\n *\n * Note: It&#39;s recommended to set this attribute since e.g. too little memory can easily stop a Java AWS Lambda function from working correctly. On AWS Lambda, the environment variable `AWS_LAMBDA_FUNCTION_MEMORY_SIZE` provides this information.\n *\n * @deprecated Use ATTR_FAAS_MAX_MEMORY in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_FAAS_MAX_MEMORY = TMP_FAAS_MAX_MEMORY;\n\n/**\n * Unique host ID. For Cloud, this must be the instance_id assigned by the cloud provider.\n *\n * @deprecated Use ATTR_HOST_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_HOST_ID = TMP_HOST_ID;\n\n/**\n * Name of the host. On Unix systems, it may contain what the hostname command returns, or the fully qualified hostname, or another name specified by the user.\n *\n * @deprecated Use ATTR_HOST_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_HOST_NAME = TMP_HOST_NAME;\n\n/**\n * Type of host. For Cloud, this must be the machine type.\n *\n * @deprecated Use ATTR_HOST_TYPE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_HOST_TYPE = TMP_HOST_TYPE;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use ATTR_HOST_ARCH in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_HOST_ARCH = TMP_HOST_ARCH;\n\n/**\n * Name of the VM image or OS install the host was instantiated from.\n *\n * @deprecated Use ATTR_HOST_IMAGE_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_HOST_IMAGE_NAME = TMP_HOST_IMAGE_NAME;\n\n/**\n * VM image ID. For Cloud, this value is from the provider.\n *\n * @deprecated Use ATTR_HOST_IMAGE_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_HOST_IMAGE_ID = TMP_HOST_IMAGE_ID;\n\n/**\n * The version string of the VM image as defined in [Version Attributes](README.md#version-attributes).\n *\n * @deprecated Use ATTR_HOST_IMAGE_VERSION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_HOST_IMAGE_VERSION = TMP_HOST_IMAGE_VERSION;\n\n/**\n * The name of the cluster.\n *\n * @deprecated Use ATTR_K8S_CLUSTER_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_CLUSTER_NAME = TMP_K8S_CLUSTER_NAME;\n\n/**\n * The name of the Node.\n *\n * @deprecated Use ATTR_K8S_NODE_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_NODE_NAME = TMP_K8S_NODE_NAME;\n\n/**\n * The UID of the Node.\n *\n * @deprecated Use ATTR_K8S_NODE_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_NODE_UID = TMP_K8S_NODE_UID;\n\n/**\n * The name of the namespace that the pod is running in.\n *\n * @deprecated Use ATTR_K8S_NAMESPACE_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_NAMESPACE_NAME = TMP_K8S_NAMESPACE_NAME;\n\n/**\n * The UID of the Pod.\n *\n * @deprecated Use ATTR_K8S_POD_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_POD_UID = TMP_K8S_POD_UID;\n\n/**\n * The name of the Pod.\n *\n * @deprecated Use ATTR_K8S_POD_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_POD_NAME = TMP_K8S_POD_NAME;\n\n/**\n * The name of the Container in a Pod template.\n *\n * @deprecated Use ATTR_K8S_CONTAINER_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_CONTAINER_NAME = TMP_K8S_CONTAINER_NAME;\n\n/**\n * The UID of the ReplicaSet.\n *\n * @deprecated Use ATTR_K8S_REPLICASET_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_REPLICASET_UID = TMP_K8S_REPLICASET_UID;\n\n/**\n * The name of the ReplicaSet.\n *\n * @deprecated Use ATTR_K8S_REPLICASET_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_REPLICASET_NAME = TMP_K8S_REPLICASET_NAME;\n\n/**\n * The UID of the Deployment.\n *\n * @deprecated Use ATTR_K8S_DEPLOYMENT_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_DEPLOYMENT_UID = TMP_K8S_DEPLOYMENT_UID;\n\n/**\n * The name of the Deployment.\n *\n * @deprecated Use ATTR_K8S_DEPLOYMENT_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_DEPLOYMENT_NAME = TMP_K8S_DEPLOYMENT_NAME;\n\n/**\n * The UID of the StatefulSet.\n *\n * @deprecated Use ATTR_K8S_STATEFULSET_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_STATEFULSET_UID = TMP_K8S_STATEFULSET_UID;\n\n/**\n * The name of the StatefulSet.\n *\n * @deprecated Use ATTR_K8S_STATEFULSET_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_STATEFULSET_NAME = TMP_K8S_STATEFULSET_NAME;\n\n/**\n * The UID of the DaemonSet.\n *\n * @deprecated Use ATTR_K8S_DAEMONSET_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_DAEMONSET_UID = TMP_K8S_DAEMONSET_UID;\n\n/**\n * The name of the DaemonSet.\n *\n * @deprecated Use ATTR_K8S_DAEMONSET_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_DAEMONSET_NAME = TMP_K8S_DAEMONSET_NAME;\n\n/**\n * The UID of the Job.\n *\n * @deprecated Use ATTR_K8S_JOB_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_JOB_UID = TMP_K8S_JOB_UID;\n\n/**\n * The name of the Job.\n *\n * @deprecated Use ATTR_K8S_JOB_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_JOB_NAME = TMP_K8S_JOB_NAME;\n\n/**\n * The UID of the CronJob.\n *\n * @deprecated Use ATTR_K8S_CRONJOB_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_CRONJOB_UID = TMP_K8S_CRONJOB_UID;\n\n/**\n * The name of the CronJob.\n *\n * @deprecated Use ATTR_K8S_CRONJOB_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_CRONJOB_NAME = TMP_K8S_CRONJOB_NAME;\n\n/**\n * The operating system type.\n *\n * @deprecated Use ATTR_OS_TYPE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_OS_TYPE = TMP_OS_TYPE;\n\n/**\n * Human readable (not intended to be parsed) OS version information, like e.g. reported by `ver` or `lsb_release -a` commands.\n *\n * @deprecated Use ATTR_OS_DESCRIPTION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_OS_DESCRIPTION = TMP_OS_DESCRIPTION;\n\n/**\n * Human readable operating system name.\n *\n * @deprecated Use ATTR_OS_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_OS_NAME = TMP_OS_NAME;\n\n/**\n * The version string of the operating system as defined in [Version Attributes](../../resource/semantic_conventions/README.md#version-attributes).\n *\n * @deprecated Use ATTR_OS_VERSION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_OS_VERSION = TMP_OS_VERSION;\n\n/**\n * Process identifier (PID).\n *\n * @deprecated Use ATTR_PROCESS_PID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_PID = TMP_PROCESS_PID;\n\n/**\n * The name of the process executable. On Linux based systems, can be set to the `Name` in `proc/[pid]/status`. On Windows, can be set to the base name of `GetProcessImageFileNameW`.\n *\n * @deprecated Use ATTR_PROCESS_EXECUTABLE_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_EXECUTABLE_NAME = TMP_PROCESS_EXECUTABLE_NAME;\n\n/**\n * The full path to the process executable. On Linux based systems, can be set to the target of `proc/[pid]/exe`. On Windows, can be set to the result of `GetProcessImageFileNameW`.\n *\n * @deprecated Use ATTR_PROCESS_EXECUTABLE_PATH in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_EXECUTABLE_PATH = TMP_PROCESS_EXECUTABLE_PATH;\n\n/**\n * The command used to launch the process (i.e. the command name). On Linux based systems, can be set to the zeroth string in `proc/[pid]/cmdline`. On Windows, can be set to the first parameter extracted from `GetCommandLineW`.\n *\n * @deprecated Use ATTR_PROCESS_COMMAND in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_COMMAND = TMP_PROCESS_COMMAND;\n\n/**\n * The full command used to launch the process as a single string representing the full command. On Windows, can be set to the result of `GetCommandLineW`. Do not set this if you have to assemble it just for monitoring; use `process.command_args` instead.\n *\n * @deprecated Use ATTR_PROCESS_COMMAND_LINE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_COMMAND_LINE = TMP_PROCESS_COMMAND_LINE;\n\n/**\n * All the command arguments (including the command/executable itself) as received by the process. On Linux-based systems (and some other Unixoid systems supporting procfs), can be set according to the list of null-delimited strings extracted from `proc/[pid]/cmdline`. For libc-based executables, this would be the full argv vector passed to `main`.\n *\n * @deprecated Use ATTR_PROCESS_COMMAND_ARGS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_COMMAND_ARGS = TMP_PROCESS_COMMAND_ARGS;\n\n/**\n * The username of the user that owns the process.\n *\n * @deprecated Use ATTR_PROCESS_OWNER in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_OWNER = TMP_PROCESS_OWNER;\n\n/**\n * The name of the runtime of this process. For compiled native binaries, this SHOULD be the name of the compiler.\n *\n * @deprecated Use ATTR_PROCESS_RUNTIME_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_RUNTIME_NAME = TMP_PROCESS_RUNTIME_NAME;\n\n/**\n * The version of the runtime of this process, as returned by the runtime without modification.\n *\n * @deprecated Use ATTR_PROCESS_RUNTIME_VERSION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_RUNTIME_VERSION = TMP_PROCESS_RUNTIME_VERSION;\n\n/**\n * An additional description about the runtime of the process, for example a specific vendor customization of the runtime environment.\n *\n * @deprecated Use ATTR_PROCESS_RUNTIME_DESCRIPTION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION =\n  TMP_PROCESS_RUNTIME_DESCRIPTION;\n\n/**\n * Logical name of the service.\n *\n * Note: MUST be the same for all instances of horizontally scaled services. If the value was not specified, SDKs MUST fallback to `unknown_service:` concatenated with [`process.executable.name`](process.md#process), e.g. `unknown_service:bash`. If `process.executable.name` is not available, the value MUST be set to `unknown_service`.\n *\n * @deprecated Use ATTR_SERVICE_NAME.\n */\nexport const SEMRESATTRS_SERVICE_NAME = TMP_SERVICE_NAME;\n\n/**\n * A namespace for `service.name`.\n *\n * Note: A string value having a meaning that helps to distinguish a group of services, for example the team name that owns a group of services. `service.name` is expected to be unique within the same namespace. If `service.namespace` is not specified in the Resource then `service.name` is expected to be unique for all services that have no explicit namespace defined (so the empty/unspecified namespace is simply one more valid namespace). Zero-length namespace string is assumed equal to unspecified namespace.\n *\n * @deprecated Use ATTR_SERVICE_NAMESPACE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_SERVICE_NAMESPACE = TMP_SERVICE_NAMESPACE;\n\n/**\n * The string ID of the service instance.\n *\n * Note: MUST be unique for each instance of the same `service.namespace,service.name` pair (in other words `service.namespace,service.name,service.instance.id` triplet MUST be globally unique). The ID helps to distinguish instances of the same service that exist at the same time (e.g. instances of a horizontally scaled service). It is preferable for the ID to be persistent and stay the same for the lifetime of the service instance, however it is acceptable that the ID is ephemeral and changes during important lifetime events for the service (e.g. service restarts). If the service has no inherent unique ID that can be used as the value of this attribute it is recommended to generate a random Version 1 or Version 4 RFC 4122 UUID (services aiming for reproducible UUIDs may also use Version 5, see RFC 4122 for more recommendations).\n *\n * @deprecated Use ATTR_SERVICE_INSTANCE_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_SERVICE_INSTANCE_ID = TMP_SERVICE_INSTANCE_ID;\n\n/**\n * The version string of the service API or implementation.\n *\n * @deprecated Use ATTR_SERVICE_VERSION.\n */\nexport const SEMRESATTRS_SERVICE_VERSION = TMP_SERVICE_VERSION;\n\n/**\n * The name of the telemetry SDK as defined above.\n *\n * @deprecated Use ATTR_TELEMETRY_SDK_NAME.\n */\nexport const SEMRESATTRS_TELEMETRY_SDK_NAME = TMP_TELEMETRY_SDK_NAME;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use ATTR_TELEMETRY_SDK_LANGUAGE.\n */\nexport const SEMRESATTRS_TELEMETRY_SDK_LANGUAGE = TMP_TELEMETRY_SDK_LANGUAGE;\n\n/**\n * The version string of the telemetry SDK.\n *\n * @deprecated Use ATTR_TELEMETRY_SDK_VERSION.\n */\nexport const SEMRESATTRS_TELEMETRY_SDK_VERSION = TMP_TELEMETRY_SDK_VERSION;\n\n/**\n * The version string of the auto instrumentation agent, if used.\n *\n * @deprecated Use ATTR_TELEMETRY_DISTRO_VERSION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_TELEMETRY_AUTO_VERSION = TMP_TELEMETRY_AUTO_VERSION;\n\n/**\n * The name of the web engine.\n *\n * @deprecated Use ATTR_WEBENGINE_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_WEBENGINE_NAME = TMP_WEBENGINE_NAME;\n\n/**\n * The version of the web engine.\n *\n * @deprecated Use ATTR_WEBENGINE_VERSION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_WEBENGINE_VERSION = TMP_WEBENGINE_VERSION;\n\n/**\n * Additional description of the web engine (e.g. detailed version and edition information).\n *\n * @deprecated Use ATTR_WEBENGINE_DESCRIPTION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_WEBENGINE_DESCRIPTION = TMP_WEBENGINE_DESCRIPTION;\n\n/**\n * Definition of available values for SemanticResourceAttributes\n * This type is used for backward compatibility, you should use the individual exported\n * constants SemanticResourceAttributes_XXXXX rather than the exported constant map. As any single reference\n * to a constant map value will result in all strings being included into your bundle.\n * @deprecated Use the SEMRESATTRS_XXXXX constants rather than the SemanticResourceAttributes.XXXXX for bundle minification.\n */\nexport type SemanticResourceAttributes = {\n  /**\n   * Name of the cloud provider.\n   */\n  CLOUD_PROVIDER: 'cloud.provider';\n\n  /**\n   * The cloud account ID the resource is assigned to.\n   */\n  CLOUD_ACCOUNT_ID: 'cloud.account.id';\n\n  /**\n   * The geographical region the resource is running. Refer to your provider&#39;s docs to see the available regions, for example [Alibaba Cloud regions](https://www.alibabacloud.com/help/doc-detail/40654.htm), [AWS regions](https://aws.amazon.com/about-aws/global-infrastructure/regions_az/), [Azure regions](https://azure.microsoft.com/en-us/global-infrastructure/geographies/), or [Google Cloud regions](https://cloud.google.com/about/locations).\n   */\n  CLOUD_REGION: 'cloud.region';\n\n  /**\n   * Cloud regions often have multiple, isolated locations known as zones to increase availability. Availability zone represents the zone where the resource is running.\n   *\n   * Note: Availability zones are called &#34;zones&#34; on Alibaba Cloud and Google Cloud.\n   */\n  CLOUD_AVAILABILITY_ZONE: 'cloud.availability_zone';\n\n  /**\n   * The cloud platform in use.\n   *\n   * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n   */\n  CLOUD_PLATFORM: 'cloud.platform';\n\n  /**\n   * The Amazon Resource Name (ARN) of an [ECS container instance](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/ECS_instances.html).\n   */\n  AWS_ECS_CONTAINER_ARN: 'aws.ecs.container.arn';\n\n  /**\n   * The ARN of an [ECS cluster](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/clusters.html).\n   */\n  AWS_ECS_CLUSTER_ARN: 'aws.ecs.cluster.arn';\n\n  /**\n   * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.\n   */\n  AWS_ECS_LAUNCHTYPE: 'aws.ecs.launchtype';\n\n  /**\n   * The ARN of an [ECS task definition](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/task_definitions.html).\n   */\n  AWS_ECS_TASK_ARN: 'aws.ecs.task.arn';\n\n  /**\n   * The task definition family this task definition is a member of.\n   */\n  AWS_ECS_TASK_FAMILY: 'aws.ecs.task.family';\n\n  /**\n   * The revision for this task definition.\n   */\n  AWS_ECS_TASK_REVISION: 'aws.ecs.task.revision';\n\n  /**\n   * The ARN of an EKS cluster.\n   */\n  AWS_EKS_CLUSTER_ARN: 'aws.eks.cluster.arn';\n\n  /**\n   * The name(s) of the AWS log group(s) an application is writing to.\n   *\n   * Note: Multiple log groups must be supported for cases like multi-container applications, where a single application has sidecar containers, and each write to their own log group.\n   */\n  AWS_LOG_GROUP_NAMES: 'aws.log.group.names';\n\n  /**\n   * The Amazon Resource Name(s) (ARN) of the AWS log group(s).\n   *\n   * Note: See the [log group ARN format documentation](https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/iam-access-control-overview-cwl.html#CWL_ARN_Format).\n   */\n  AWS_LOG_GROUP_ARNS: 'aws.log.group.arns';\n\n  /**\n   * The name(s) of the AWS log stream(s) an application is writing to.\n   */\n  AWS_LOG_STREAM_NAMES: 'aws.log.stream.names';\n\n  /**\n   * The ARN(s) of the AWS log stream(s).\n   *\n   * Note: See the [log stream ARN format documentation](https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/iam-access-control-overview-cwl.html#CWL_ARN_Format). One log group can contain several log streams, so these ARNs necessarily identify both a log group and a log stream.\n   */\n  AWS_LOG_STREAM_ARNS: 'aws.log.stream.arns';\n\n  /**\n   * Container name.\n   */\n  CONTAINER_NAME: 'container.name';\n\n  /**\n   * Container ID. Usually a UUID, as for example used to [identify Docker containers](https://docs.docker.com/engine/reference/run/#container-identification). The UUID might be abbreviated.\n   */\n  CONTAINER_ID: 'container.id';\n\n  /**\n   * The container runtime managing this container.\n   */\n  CONTAINER_RUNTIME: 'container.runtime';\n\n  /**\n   * Name of the image the container was built on.\n   */\n  CONTAINER_IMAGE_NAME: 'container.image.name';\n\n  /**\n   * Container image tag.\n   */\n  CONTAINER_IMAGE_TAG: 'container.image.tag';\n\n  /**\n   * Name of the [deployment environment](https://en.wikipedia.org/wiki/Deployment_environment) (aka deployment tier).\n   */\n  DEPLOYMENT_ENVIRONMENT: 'deployment.environment';\n\n  /**\n   * A unique identifier representing the device.\n   *\n   * Note: The device identifier MUST only be defined using the values outlined below. This value is not an advertising identifier and MUST NOT be used as such. On iOS (Swift or Objective-C), this value MUST be equal to the [vendor identifier](https://developer.apple.com/documentation/uikit/uidevice/1620059-identifierforvendor). On Android (Java or Kotlin), this value MUST be equal to the Firebase Installation ID or a globally unique UUID which is persisted across sessions in your application. More information can be found [here](https://developer.android.com/training/articles/user-data-ids) on best practices and exact implementation details. Caution should be taken when storing personal data or anything which can identify a user. GDPR and data protection laws may apply, ensure you do your own due diligence.\n   */\n  DEVICE_ID: 'device.id';\n\n  /**\n   * The model identifier for the device.\n   *\n   * Note: It&#39;s recommended this value represents a machine readable version of the model identifier rather than the market or consumer-friendly name of the device.\n   */\n  DEVICE_MODEL_IDENTIFIER: 'device.model.identifier';\n\n  /**\n   * The marketing name for the device model.\n   *\n   * Note: It&#39;s recommended this value represents a human readable version of the device model rather than a machine readable alternative.\n   */\n  DEVICE_MODEL_NAME: 'device.model.name';\n\n  /**\n   * The name of the single function that this runtime instance executes.\n   *\n   * Note: This is the name of the function as configured/deployed on the FaaS platform and is usually different from the name of the callback function (which may be stored in the [`code.namespace`/`code.function`](../../trace/semantic_conventions/span-general.md#source-code-attributes) span attributes).\n   */\n  FAAS_NAME: 'faas.name';\n\n  /**\n  * The unique ID of the single function that this runtime instance executes.\n  *\n  * Note: Depending on the cloud provider, use:\n\n* **AWS Lambda:** The function [ARN](https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html).\nTake care not to use the &#34;invoked ARN&#34; directly but replace any\n[alias suffix](https://docs.aws.amazon.com/lambda/latest/dg/configuration-aliases.html) with the resolved function version, as the same runtime instance may be invokable with multiple\ndifferent aliases.\n* **GCP:** The [URI of the resource](https://cloud.google.com/iam/docs/full-resource-names)\n* **Azure:** The [Fully Qualified Resource ID](https://docs.microsoft.com/en-us/rest/api/resources/resources/get-by-id).\n\nOn some providers, it may not be possible to determine the full ID at startup,\nwhich is why this field cannot be made required. For example, on AWS the account ID\npart of the ARN is not available without calling another AWS API\nwhich may be deemed too slow for a short-running lambda function.\nAs an alternative, consider setting `faas.id` as a span attribute instead.\n  */\n  FAAS_ID: 'faas.id';\n\n  /**\n  * The immutable version of the function being executed.\n  *\n  * Note: Depending on the cloud provider and platform, use:\n\n* **AWS Lambda:** The [function version](https://docs.aws.amazon.com/lambda/latest/dg/configuration-versions.html)\n  (an integer represented as a decimal string).\n* **Google Cloud Run:** The [revision](https://cloud.google.com/run/docs/managing/revisions)\n  (i.e., the function name plus the revision suffix).\n* **Google Cloud Functions:** The value of the\n  [`K_REVISION` environment variable](https://cloud.google.com/functions/docs/env-var#runtime_environment_variables_set_automatically).\n* **Azure Functions:** Not applicable. Do not set this attribute.\n  */\n  FAAS_VERSION: 'faas.version';\n\n  /**\n   * The execution environment ID as a string, that will be potentially reused for other invocations to the same function/function version.\n   *\n   * Note: * **AWS Lambda:** Use the (full) log stream name.\n   */\n  FAAS_INSTANCE: 'faas.instance';\n\n  /**\n   * The amount of memory available to the serverless function in MiB.\n   *\n   * Note: It&#39;s recommended to set this attribute since e.g. too little memory can easily stop a Java AWS Lambda function from working correctly. On AWS Lambda, the environment variable `AWS_LAMBDA_FUNCTION_MEMORY_SIZE` provides this information.\n   */\n  FAAS_MAX_MEMORY: 'faas.max_memory';\n\n  /**\n   * Unique host ID. For Cloud, this must be the instance_id assigned by the cloud provider.\n   */\n  HOST_ID: 'host.id';\n\n  /**\n   * Name of the host. On Unix systems, it may contain what the hostname command returns, or the fully qualified hostname, or another name specified by the user.\n   */\n  HOST_NAME: 'host.name';\n\n  /**\n   * Type of host. For Cloud, this must be the machine type.\n   */\n  HOST_TYPE: 'host.type';\n\n  /**\n   * The CPU architecture the host system is running on.\n   */\n  HOST_ARCH: 'host.arch';\n\n  /**\n   * Name of the VM image or OS install the host was instantiated from.\n   */\n  HOST_IMAGE_NAME: 'host.image.name';\n\n  /**\n   * VM image ID. For Cloud, this value is from the provider.\n   */\n  HOST_IMAGE_ID: 'host.image.id';\n\n  /**\n   * The version string of the VM image as defined in [Version Attributes](README.md#version-attributes).\n   */\n  HOST_IMAGE_VERSION: 'host.image.version';\n\n  /**\n   * The name of the cluster.\n   */\n  K8S_CLUSTER_NAME: 'k8s.cluster.name';\n\n  /**\n   * The name of the Node.\n   */\n  K8S_NODE_NAME: 'k8s.node.name';\n\n  /**\n   * The UID of the Node.\n   */\n  K8S_NODE_UID: 'k8s.node.uid';\n\n  /**\n   * The name of the namespace that the pod is running in.\n   */\n  K8S_NAMESPACE_NAME: 'k8s.namespace.name';\n\n  /**\n   * The UID of the Pod.\n   */\n  K8S_POD_UID: 'k8s.pod.uid';\n\n  /**\n   * The name of the Pod.\n   */\n  K8S_POD_NAME: 'k8s.pod.name';\n\n  /**\n   * The name of the Container in a Pod template.\n   */\n  K8S_CONTAINER_NAME: 'k8s.container.name';\n\n  /**\n   * The UID of the ReplicaSet.\n   */\n  K8S_REPLICASET_UID: 'k8s.replicaset.uid';\n\n  /**\n   * The name of the ReplicaSet.\n   */\n  K8S_REPLICASET_NAME: 'k8s.replicaset.name';\n\n  /**\n   * The UID of the Deployment.\n   */\n  K8S_DEPLOYMENT_UID: 'k8s.deployment.uid';\n\n  /**\n   * The name of the Deployment.\n   */\n  K8S_DEPLOYMENT_NAME: 'k8s.deployment.name';\n\n  /**\n   * The UID of the StatefulSet.\n   */\n  K8S_STATEFULSET_UID: 'k8s.statefulset.uid';\n\n  /**\n   * The name of the StatefulSet.\n   */\n  K8S_STATEFULSET_NAME: 'k8s.statefulset.name';\n\n  /**\n   * The UID of the DaemonSet.\n   */\n  K8S_DAEMONSET_UID: 'k8s.daemonset.uid';\n\n  /**\n   * The name of the DaemonSet.\n   */\n  K8S_DAEMONSET_NAME: 'k8s.daemonset.name';\n\n  /**\n   * The UID of the Job.\n   */\n  K8S_JOB_UID: 'k8s.job.uid';\n\n  /**\n   * The name of the Job.\n   */\n  K8S_JOB_NAME: 'k8s.job.name';\n\n  /**\n   * The UID of the CronJob.\n   */\n  K8S_CRONJOB_UID: 'k8s.cronjob.uid';\n\n  /**\n   * The name of the CronJob.\n   */\n  K8S_CRONJOB_NAME: 'k8s.cronjob.name';\n\n  /**\n   * The operating system type.\n   */\n  OS_TYPE: 'os.type';\n\n  /**\n   * Human readable (not intended to be parsed) OS version information, like e.g. reported by `ver` or `lsb_release -a` commands.\n   */\n  OS_DESCRIPTION: 'os.description';\n\n  /**\n   * Human readable operating system name.\n   */\n  OS_NAME: 'os.name';\n\n  /**\n   * The version string of the operating system as defined in [Version Attributes](../../resource/semantic_conventions/README.md#version-attributes).\n   */\n  OS_VERSION: 'os.version';\n\n  /**\n   * Process identifier (PID).\n   */\n  PROCESS_PID: 'process.pid';\n\n  /**\n   * The name of the process executable. On Linux based systems, can be set to the `Name` in `proc/[pid]/status`. On Windows, can be set to the base name of `GetProcessImageFileNameW`.\n   */\n  PROCESS_EXECUTABLE_NAME: 'process.executable.name';\n\n  /**\n   * The full path to the process executable. On Linux based systems, can be set to the target of `proc/[pid]/exe`. On Windows, can be set to the result of `GetProcessImageFileNameW`.\n   */\n  PROCESS_EXECUTABLE_PATH: 'process.executable.path';\n\n  /**\n   * The command used to launch the process (i.e. the command name). On Linux based systems, can be set to the zeroth string in `proc/[pid]/cmdline`. On Windows, can be set to the first parameter extracted from `GetCommandLineW`.\n   */\n  PROCESS_COMMAND: 'process.command';\n\n  /**\n   * The full command used to launch the process as a single string representing the full command. On Windows, can be set to the result of `GetCommandLineW`. Do not set this if you have to assemble it just for monitoring; use `process.command_args` instead.\n   */\n  PROCESS_COMMAND_LINE: 'process.command_line';\n\n  /**\n   * All the command arguments (including the command/executable itself) as received by the process. On Linux-based systems (and some other Unixoid systems supporting procfs), can be set according to the list of null-delimited strings extracted from `proc/[pid]/cmdline`. For libc-based executables, this would be the full argv vector passed to `main`.\n   */\n  PROCESS_COMMAND_ARGS: 'process.command_args';\n\n  /**\n   * The username of the user that owns the process.\n   */\n  PROCESS_OWNER: 'process.owner';\n\n  /**\n   * The name of the runtime of this process. For compiled native binaries, this SHOULD be the name of the compiler.\n   */\n  PROCESS_RUNTIME_NAME: 'process.runtime.name';\n\n  /**\n   * The version of the runtime of this process, as returned by the runtime without modification.\n   */\n  PROCESS_RUNTIME_VERSION: 'process.runtime.version';\n\n  /**\n   * An additional description about the runtime of the process, for example a specific vendor customization of the runtime environment.\n   */\n  PROCESS_RUNTIME_DESCRIPTION: 'process.runtime.description';\n\n  /**\n   * Logical name of the service.\n   *\n   * Note: MUST be the same for all instances of horizontally scaled services. If the value was not specified, SDKs MUST fallback to `unknown_service:` concatenated with [`process.executable.name`](process.md#process), e.g. `unknown_service:bash`. If `process.executable.name` is not available, the value MUST be set to `unknown_service`.\n   */\n  SERVICE_NAME: 'service.name';\n\n  /**\n   * A namespace for `service.name`.\n   *\n   * Note: A string value having a meaning that helps to distinguish a group of services, for example the team name that owns a group of services. `service.name` is expected to be unique within the same namespace. If `service.namespace` is not specified in the Resource then `service.name` is expected to be unique for all services that have no explicit namespace defined (so the empty/unspecified namespace is simply one more valid namespace). Zero-length namespace string is assumed equal to unspecified namespace.\n   */\n  SERVICE_NAMESPACE: 'service.namespace';\n\n  /**\n   * The string ID of the service instance.\n   *\n   * Note: MUST be unique for each instance of the same `service.namespace,service.name` pair (in other words `service.namespace,service.name,service.instance.id` triplet MUST be globally unique). The ID helps to distinguish instances of the same service that exist at the same time (e.g. instances of a horizontally scaled service). It is preferable for the ID to be persistent and stay the same for the lifetime of the service instance, however it is acceptable that the ID is ephemeral and changes during important lifetime events for the service (e.g. service restarts). If the service has no inherent unique ID that can be used as the value of this attribute it is recommended to generate a random Version 1 or Version 4 RFC 4122 UUID (services aiming for reproducible UUIDs may also use Version 5, see RFC 4122 for more recommendations).\n   */\n  SERVICE_INSTANCE_ID: 'service.instance.id';\n\n  /**\n   * The version string of the service API or implementation.\n   */\n  SERVICE_VERSION: 'service.version';\n\n  /**\n   * The name of the telemetry SDK as defined above.\n   */\n  TELEMETRY_SDK_NAME: 'telemetry.sdk.name';\n\n  /**\n   * The language of the telemetry SDK.\n   */\n  TELEMETRY_SDK_LANGUAGE: 'telemetry.sdk.language';\n\n  /**\n   * The version string of the telemetry SDK.\n   */\n  TELEMETRY_SDK_VERSION: 'telemetry.sdk.version';\n\n  /**\n   * The version string of the auto instrumentation agent, if used.\n   */\n  TELEMETRY_AUTO_VERSION: 'telemetry.auto.version';\n\n  /**\n   * The name of the web engine.\n   */\n  WEBENGINE_NAME: 'webengine.name';\n\n  /**\n   * The version of the web engine.\n   */\n  WEBENGINE_VERSION: 'webengine.version';\n\n  /**\n   * Additional description of the web engine (e.g. detailed version and edition information).\n   */\n  WEBENGINE_DESCRIPTION: 'webengine.description';\n};\n\n/**\n * Create exported Value Map for SemanticResourceAttributes values\n * @deprecated Use the SEMRESATTRS_XXXXX constants rather than the SemanticResourceAttributes.XXXXX for bundle minification\n */\nexport const SemanticResourceAttributes: SemanticResourceAttributes =\n  /*#__PURE__*/ createConstMap<SemanticResourceAttributes>([\n    TMP_CLOUD_PROVIDER,\n    TMP_CLOUD_ACCOUNT_ID,\n    TMP_CLOUD_REGION,\n    TMP_CLOUD_AVAILABILITY_ZONE,\n    TMP_CLOUD_PLATFORM,\n    TMP_AWS_ECS_CONTAINER_ARN,\n    TMP_AWS_ECS_CLUSTER_ARN,\n    TMP_AWS_ECS_LAUNCHTYPE,\n    TMP_AWS_ECS_TASK_ARN,\n    TMP_AWS_ECS_TASK_FAMILY,\n    TMP_AWS_ECS_TASK_REVISION,\n    TMP_AWS_EKS_CLUSTER_ARN,\n    TMP_AWS_LOG_GROUP_NAMES,\n    TMP_AWS_LOG_GROUP_ARNS,\n    TMP_AWS_LOG_STREAM_NAMES,\n    TMP_AWS_LOG_STREAM_ARNS,\n    TMP_CONTAINER_NAME,\n    TMP_CONTAINER_ID,\n    TMP_CONTAINER_RUNTIME,\n    TMP_CONTAINER_IMAGE_NAME,\n    TMP_CONTAINER_IMAGE_TAG,\n    TMP_DEPLOYMENT_ENVIRONMENT,\n    TMP_DEVICE_ID,\n    TMP_DEVICE_MODEL_IDENTIFIER,\n    TMP_DEVICE_MODEL_NAME,\n    TMP_FAAS_NAME,\n    TMP_FAAS_ID,\n    TMP_FAAS_VERSION,\n    TMP_FAAS_INSTANCE,\n    TMP_FAAS_MAX_MEMORY,\n    TMP_HOST_ID,\n    TMP_HOST_NAME,\n    TMP_HOST_TYPE,\n    TMP_HOST_ARCH,\n    TMP_HOST_IMAGE_NAME,\n    TMP_HOST_IMAGE_ID,\n    TMP_HOST_IMAGE_VERSION,\n    TMP_K8S_CLUSTER_NAME,\n    TMP_K8S_NODE_NAME,\n    TMP_K8S_NODE_UID,\n    TMP_K8S_NAMESPACE_NAME,\n    TMP_K8S_POD_UID,\n    TMP_K8S_POD_NAME,\n    TMP_K8S_CONTAINER_NAME,\n    TMP_K8S_REPLICASET_UID,\n    TMP_K8S_REPLICASET_NAME,\n    TMP_K8S_DEPLOYMENT_UID,\n    TMP_K8S_DEPLOYMENT_NAME,\n    TMP_K8S_STATEFULSET_UID,\n    TMP_K8S_STATEFULSET_NAME,\n    TMP_K8S_DAEMONSET_UID,\n    TMP_K8S_DAEMONSET_NAME,\n    TMP_K8S_JOB_UID,\n    TMP_K8S_JOB_NAME,\n    TMP_K8S_CRONJOB_UID,\n    TMP_K8S_CRONJOB_NAME,\n    TMP_OS_TYPE,\n    TMP_OS_DESCRIPTION,\n    TMP_OS_NAME,\n    TMP_OS_VERSION,\n    TMP_PROCESS_PID,\n    TMP_PROCESS_EXECUTABLE_NAME,\n    TMP_PROCESS_EXECUTABLE_PATH,\n    TMP_PROCESS_COMMAND,\n    TMP_PROCESS_COMMAND_LINE,\n    TMP_PROCESS_COMMAND_ARGS,\n    TMP_PROCESS_OWNER,\n    TMP_PROCESS_RUNTIME_NAME,\n    TMP_PROCESS_RUNTIME_VERSION,\n    TMP_PROCESS_RUNTIME_DESCRIPTION,\n    TMP_SERVICE_NAME,\n    TMP_SERVICE_NAMESPACE,\n    TMP_SERVICE_INSTANCE_ID,\n    TMP_SERVICE_VERSION,\n    TMP_TELEMETRY_SDK_NAME,\n    TMP_TELEMETRY_SDK_LANGUAGE,\n    TMP_TELEMETRY_SDK_VERSION,\n    TMP_TELEMETRY_AUTO_VERSION,\n    TMP_WEBENGINE_NAME,\n    TMP_WEBENGINE_VERSION,\n    TMP_WEBENGINE_DESCRIPTION,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for CloudProviderValues enum definition\n *\n * Name of the cloud provider.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_CLOUDPROVIDERVALUES_ALIBABA_CLOUD = 'alibaba_cloud';\nconst TMP_CLOUDPROVIDERVALUES_AWS = 'aws';\nconst TMP_CLOUDPROVIDERVALUES_AZURE = 'azure';\nconst TMP_CLOUDPROVIDERVALUES_GCP = 'gcp';\n\n/**\n * Name of the cloud provider.\n *\n * @deprecated Use CLOUD_PROVIDER_VALUE_ALIBABA_CLOUD in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPROVIDERVALUES_ALIBABA_CLOUD =\n  TMP_CLOUDPROVIDERVALUES_ALIBABA_CLOUD;\n\n/**\n * Name of the cloud provider.\n *\n * @deprecated Use CLOUD_PROVIDER_VALUE_AWS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPROVIDERVALUES_AWS = TMP_CLOUDPROVIDERVALUES_AWS;\n\n/**\n * Name of the cloud provider.\n *\n * @deprecated Use CLOUD_PROVIDER_VALUE_AZURE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPROVIDERVALUES_AZURE = TMP_CLOUDPROVIDERVALUES_AZURE;\n\n/**\n * Name of the cloud provider.\n *\n * @deprecated Use CLOUD_PROVIDER_VALUE_GCP in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPROVIDERVALUES_GCP = TMP_CLOUDPROVIDERVALUES_GCP;\n\n/**\n * Identifies the Values for CloudProviderValues enum definition\n *\n * Name of the cloud provider.\n * @deprecated Use the CLOUDPROVIDERVALUES_XXXXX constants rather than the CloudProviderValues.XXXXX for bundle minification.\n */\nexport type CloudProviderValues = {\n  /** Alibaba Cloud. */\n  ALIBABA_CLOUD: 'alibaba_cloud';\n\n  /** Amazon Web Services. */\n  AWS: 'aws';\n\n  /** Microsoft Azure. */\n  AZURE: 'azure';\n\n  /** Google Cloud Platform. */\n  GCP: 'gcp';\n};\n\n/**\n * The constant map of values for CloudProviderValues.\n * @deprecated Use the CLOUDPROVIDERVALUES_XXXXX constants rather than the CloudProviderValues.XXXXX for bundle minification.\n */\nexport const CloudProviderValues: CloudProviderValues =\n  /*#__PURE__*/ createConstMap<CloudProviderValues>([\n    TMP_CLOUDPROVIDERVALUES_ALIBABA_CLOUD,\n    TMP_CLOUDPROVIDERVALUES_AWS,\n    TMP_CLOUDPROVIDERVALUES_AZURE,\n    TMP_CLOUDPROVIDERVALUES_GCP,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for CloudPlatformValues enum definition\n *\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS = 'alibaba_cloud_ecs';\nconst TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC = 'alibaba_cloud_fc';\nconst TMP_CLOUDPLATFORMVALUES_AWS_EC2 = 'aws_ec2';\nconst TMP_CLOUDPLATFORMVALUES_AWS_ECS = 'aws_ecs';\nconst TMP_CLOUDPLATFORMVALUES_AWS_EKS = 'aws_eks';\nconst TMP_CLOUDPLATFORMVALUES_AWS_LAMBDA = 'aws_lambda';\nconst TMP_CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK = 'aws_elastic_beanstalk';\nconst TMP_CLOUDPLATFORMVALUES_AZURE_VM = 'azure_vm';\nconst TMP_CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES =\n  'azure_container_instances';\nconst TMP_CLOUDPLATFORMVALUES_AZURE_AKS = 'azure_aks';\nconst TMP_CLOUDPLATFORMVALUES_AZURE_FUNCTIONS = 'azure_functions';\nconst TMP_CLOUDPLATFORMVALUES_AZURE_APP_SERVICE = 'azure_app_service';\nconst TMP_CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE = 'gcp_compute_engine';\nconst TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_RUN = 'gcp_cloud_run';\nconst TMP_CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE = 'gcp_kubernetes_engine';\nconst TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS = 'gcp_cloud_functions';\nconst TMP_CLOUDPLATFORMVALUES_GCP_APP_ENGINE = 'gcp_app_engine';\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_ECS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS =\n  TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_FC in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC =\n  TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AWS_EC2 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AWS_EC2 = TMP_CLOUDPLATFORMVALUES_AWS_EC2;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AWS_ECS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AWS_ECS = TMP_CLOUDPLATFORMVALUES_AWS_ECS;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AWS_EKS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AWS_EKS = TMP_CLOUDPLATFORMVALUES_AWS_EKS;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AWS_LAMBDA in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AWS_LAMBDA =\n  TMP_CLOUDPLATFORMVALUES_AWS_LAMBDA;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AWS_ELASTIC_BEANSTALK in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK =\n  TMP_CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AZURE_VM in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AZURE_VM = TMP_CLOUDPLATFORMVALUES_AZURE_VM;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AZURE_CONTAINER_INSTANCES in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES =\n  TMP_CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AZURE_AKS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AZURE_AKS = TMP_CLOUDPLATFORMVALUES_AZURE_AKS;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AZURE_FUNCTIONS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AZURE_FUNCTIONS =\n  TMP_CLOUDPLATFORMVALUES_AZURE_FUNCTIONS;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AZURE_APP_SERVICE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AZURE_APP_SERVICE =\n  TMP_CLOUDPLATFORMVALUES_AZURE_APP_SERVICE;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_GCP_COMPUTE_ENGINE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE =\n  TMP_CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_GCP_CLOUD_RUN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_GCP_CLOUD_RUN =\n  TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_RUN;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_GCP_KUBERNETES_ENGINE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE =\n  TMP_CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_GCP_CLOUD_FUNCTIONS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS =\n  TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_GCP_APP_ENGINE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_GCP_APP_ENGINE =\n  TMP_CLOUDPLATFORMVALUES_GCP_APP_ENGINE;\n\n/**\n * Identifies the Values for CloudPlatformValues enum definition\n *\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n * @deprecated Use the CLOUDPLATFORMVALUES_XXXXX constants rather than the CloudPlatformValues.XXXXX for bundle minification.\n */\nexport type CloudPlatformValues = {\n  /** Alibaba Cloud Elastic Compute Service. */\n  ALIBABA_CLOUD_ECS: 'alibaba_cloud_ecs';\n\n  /** Alibaba Cloud Function Compute. */\n  ALIBABA_CLOUD_FC: 'alibaba_cloud_fc';\n\n  /** AWS Elastic Compute Cloud. */\n  AWS_EC2: 'aws_ec2';\n\n  /** AWS Elastic Container Service. */\n  AWS_ECS: 'aws_ecs';\n\n  /** AWS Elastic Kubernetes Service. */\n  AWS_EKS: 'aws_eks';\n\n  /** AWS Lambda. */\n  AWS_LAMBDA: 'aws_lambda';\n\n  /** AWS Elastic Beanstalk. */\n  AWS_ELASTIC_BEANSTALK: 'aws_elastic_beanstalk';\n\n  /** Azure Virtual Machines. */\n  AZURE_VM: 'azure_vm';\n\n  /** Azure Container Instances. */\n  AZURE_CONTAINER_INSTANCES: 'azure_container_instances';\n\n  /** Azure Kubernetes Service. */\n  AZURE_AKS: 'azure_aks';\n\n  /** Azure Functions. */\n  AZURE_FUNCTIONS: 'azure_functions';\n\n  /** Azure App Service. */\n  AZURE_APP_SERVICE: 'azure_app_service';\n\n  /** Google Cloud Compute Engine (GCE). */\n  GCP_COMPUTE_ENGINE: 'gcp_compute_engine';\n\n  /** Google Cloud Run. */\n  GCP_CLOUD_RUN: 'gcp_cloud_run';\n\n  /** Google Cloud Kubernetes Engine (GKE). */\n  GCP_KUBERNETES_ENGINE: 'gcp_kubernetes_engine';\n\n  /** Google Cloud Functions (GCF). */\n  GCP_CLOUD_FUNCTIONS: 'gcp_cloud_functions';\n\n  /** Google Cloud App Engine (GAE). */\n  GCP_APP_ENGINE: 'gcp_app_engine';\n};\n\n/**\n * The constant map of values for CloudPlatformValues.\n * @deprecated Use the CLOUDPLATFORMVALUES_XXXXX constants rather than the CloudPlatformValues.XXXXX for bundle minification.\n */\nexport const CloudPlatformValues: CloudPlatformValues =\n  /*#__PURE__*/ createConstMap<CloudPlatformValues>([\n    TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS,\n    TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC,\n    TMP_CLOUDPLATFORMVALUES_AWS_EC2,\n    TMP_CLOUDPLATFORMVALUES_AWS_ECS,\n    TMP_CLOUDPLATFORMVALUES_AWS_EKS,\n    TMP_CLOUDPLATFORMVALUES_AWS_LAMBDA,\n    TMP_CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK,\n    TMP_CLOUDPLATFORMVALUES_AZURE_VM,\n    TMP_CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES,\n    TMP_CLOUDPLATFORMVALUES_AZURE_AKS,\n    TMP_CLOUDPLATFORMVALUES_AZURE_FUNCTIONS,\n    TMP_CLOUDPLATFORMVALUES_AZURE_APP_SERVICE,\n    TMP_CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE,\n    TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_RUN,\n    TMP_CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE,\n    TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS,\n    TMP_CLOUDPLATFORMVALUES_GCP_APP_ENGINE,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for AwsEcsLaunchtypeValues enum definition\n *\n * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_AWSECSLAUNCHTYPEVALUES_EC2 = 'ec2';\nconst TMP_AWSECSLAUNCHTYPEVALUES_FARGATE = 'fargate';\n\n/**\n * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.\n *\n * @deprecated Use AWS_ECS_LAUNCHTYPE_VALUE_EC2 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const AWSECSLAUNCHTYPEVALUES_EC2 = TMP_AWSECSLAUNCHTYPEVALUES_EC2;\n\n/**\n * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.\n *\n * @deprecated Use AWS_ECS_LAUNCHTYPE_VALUE_FARGATE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const AWSECSLAUNCHTYPEVALUES_FARGATE =\n  TMP_AWSECSLAUNCHTYPEVALUES_FARGATE;\n\n/**\n * Identifies the Values for AwsEcsLaunchtypeValues enum definition\n *\n * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.\n * @deprecated Use the AWSECSLAUNCHTYPEVALUES_XXXXX constants rather than the AwsEcsLaunchtypeValues.XXXXX for bundle minification.\n */\nexport type AwsEcsLaunchtypeValues = {\n  /** ec2. */\n  EC2: 'ec2';\n\n  /** fargate. */\n  FARGATE: 'fargate';\n};\n\n/**\n * The constant map of values for AwsEcsLaunchtypeValues.\n * @deprecated Use the AWSECSLAUNCHTYPEVALUES_XXXXX constants rather than the AwsEcsLaunchtypeValues.XXXXX for bundle minification.\n */\nexport const AwsEcsLaunchtypeValues: AwsEcsLaunchtypeValues =\n  /*#__PURE__*/ createConstMap<AwsEcsLaunchtypeValues>([\n    TMP_AWSECSLAUNCHTYPEVALUES_EC2,\n    TMP_AWSECSLAUNCHTYPEVALUES_FARGATE,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for HostArchValues enum definition\n *\n * The CPU architecture the host system is running on.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_HOSTARCHVALUES_AMD64 = 'amd64';\nconst TMP_HOSTARCHVALUES_ARM32 = 'arm32';\nconst TMP_HOSTARCHVALUES_ARM64 = 'arm64';\nconst TMP_HOSTARCHVALUES_IA64 = 'ia64';\nconst TMP_HOSTARCHVALUES_PPC32 = 'ppc32';\nconst TMP_HOSTARCHVALUES_PPC64 = 'ppc64';\nconst TMP_HOSTARCHVALUES_X86 = 'x86';\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use HOST_ARCH_VALUE_AMD64 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HOSTARCHVALUES_AMD64 = TMP_HOSTARCHVALUES_AMD64;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use HOST_ARCH_VALUE_ARM32 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HOSTARCHVALUES_ARM32 = TMP_HOSTARCHVALUES_ARM32;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use HOST_ARCH_VALUE_ARM64 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HOSTARCHVALUES_ARM64 = TMP_HOSTARCHVALUES_ARM64;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use HOST_ARCH_VALUE_IA64 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HOSTARCHVALUES_IA64 = TMP_HOSTARCHVALUES_IA64;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use HOST_ARCH_VALUE_PPC32 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HOSTARCHVALUES_PPC32 = TMP_HOSTARCHVALUES_PPC32;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use HOST_ARCH_VALUE_PPC64 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HOSTARCHVALUES_PPC64 = TMP_HOSTARCHVALUES_PPC64;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use HOST_ARCH_VALUE_X86 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HOSTARCHVALUES_X86 = TMP_HOSTARCHVALUES_X86;\n\n/**\n * Identifies the Values for HostArchValues enum definition\n *\n * The CPU architecture the host system is running on.\n * @deprecated Use the HOSTARCHVALUES_XXXXX constants rather than the HostArchValues.XXXXX for bundle minification.\n */\nexport type HostArchValues = {\n  /** AMD64. */\n  AMD64: 'amd64';\n\n  /** ARM32. */\n  ARM32: 'arm32';\n\n  /** ARM64. */\n  ARM64: 'arm64';\n\n  /** Itanium. */\n  IA64: 'ia64';\n\n  /** 32-bit PowerPC. */\n  PPC32: 'ppc32';\n\n  /** 64-bit PowerPC. */\n  PPC64: 'ppc64';\n\n  /** 32-bit x86. */\n  X86: 'x86';\n};\n\n/**\n * The constant map of values for HostArchValues.\n * @deprecated Use the HOSTARCHVALUES_XXXXX constants rather than the HostArchValues.XXXXX for bundle minification.\n */\nexport const HostArchValues: HostArchValues =\n  /*#__PURE__*/ createConstMap<HostArchValues>([\n    TMP_HOSTARCHVALUES_AMD64,\n    TMP_HOSTARCHVALUES_ARM32,\n    TMP_HOSTARCHVALUES_ARM64,\n    TMP_HOSTARCHVALUES_IA64,\n    TMP_HOSTARCHVALUES_PPC32,\n    TMP_HOSTARCHVALUES_PPC64,\n    TMP_HOSTARCHVALUES_X86,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for OsTypeValues enum definition\n *\n * The operating system type.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_OSTYPEVALUES_WINDOWS = 'windows';\nconst TMP_OSTYPEVALUES_LINUX = 'linux';\nconst TMP_OSTYPEVALUES_DARWIN = 'darwin';\nconst TMP_OSTYPEVALUES_FREEBSD = 'freebsd';\nconst TMP_OSTYPEVALUES_NETBSD = 'netbsd';\nconst TMP_OSTYPEVALUES_OPENBSD = 'openbsd';\nconst TMP_OSTYPEVALUES_DRAGONFLYBSD = 'dragonflybsd';\nconst TMP_OSTYPEVALUES_HPUX = 'hpux';\nconst TMP_OSTYPEVALUES_AIX = 'aix';\nconst TMP_OSTYPEVALUES_SOLARIS = 'solaris';\nconst TMP_OSTYPEVALUES_Z_OS = 'z_os';\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_WINDOWS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_WINDOWS = TMP_OSTYPEVALUES_WINDOWS;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_LINUX in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_LINUX = TMP_OSTYPEVALUES_LINUX;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_DARWIN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_DARWIN = TMP_OSTYPEVALUES_DARWIN;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_FREEBSD in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_FREEBSD = TMP_OSTYPEVALUES_FREEBSD;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_NETBSD in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_NETBSD = TMP_OSTYPEVALUES_NETBSD;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_OPENBSD in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_OPENBSD = TMP_OSTYPEVALUES_OPENBSD;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_DRAGONFLYBSD in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_DRAGONFLYBSD = TMP_OSTYPEVALUES_DRAGONFLYBSD;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_HPUX in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_HPUX = TMP_OSTYPEVALUES_HPUX;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_AIX in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_AIX = TMP_OSTYPEVALUES_AIX;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_SOLARIS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_SOLARIS = TMP_OSTYPEVALUES_SOLARIS;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_Z_OS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_Z_OS = TMP_OSTYPEVALUES_Z_OS;\n\n/**\n * Identifies the Values for OsTypeValues enum definition\n *\n * The operating system type.\n * @deprecated Use the OSTYPEVALUES_XXXXX constants rather than the OsTypeValues.XXXXX for bundle minification.\n */\nexport type OsTypeValues = {\n  /** Microsoft Windows. */\n  WINDOWS: 'windows';\n\n  /** Linux. */\n  LINUX: 'linux';\n\n  /** Apple Darwin. */\n  DARWIN: 'darwin';\n\n  /** FreeBSD. */\n  FREEBSD: 'freebsd';\n\n  /** NetBSD. */\n  NETBSD: 'netbsd';\n\n  /** OpenBSD. */\n  OPENBSD: 'openbsd';\n\n  /** DragonFly BSD. */\n  DRAGONFLYBSD: 'dragonflybsd';\n\n  /** HP-UX (Hewlett Packard Unix). */\n  HPUX: 'hpux';\n\n  /** AIX (Advanced Interactive eXecutive). */\n  AIX: 'aix';\n\n  /** Oracle Solaris. */\n  SOLARIS: 'solaris';\n\n  /** IBM z/OS. */\n  Z_OS: 'z_os';\n};\n\n/**\n * The constant map of values for OsTypeValues.\n * @deprecated Use the OSTYPEVALUES_XXXXX constants rather than the OsTypeValues.XXXXX for bundle minification.\n */\nexport const OsTypeValues: OsTypeValues =\n  /*#__PURE__*/ createConstMap<OsTypeValues>([\n    TMP_OSTYPEVALUES_WINDOWS,\n    TMP_OSTYPEVALUES_LINUX,\n    TMP_OSTYPEVALUES_DARWIN,\n    TMP_OSTYPEVALUES_FREEBSD,\n    TMP_OSTYPEVALUES_NETBSD,\n    TMP_OSTYPEVALUES_OPENBSD,\n    TMP_OSTYPEVALUES_DRAGONFLYBSD,\n    TMP_OSTYPEVALUES_HPUX,\n    TMP_OSTYPEVALUES_AIX,\n    TMP_OSTYPEVALUES_SOLARIS,\n    TMP_OSTYPEVALUES_Z_OS,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for TelemetrySdkLanguageValues enum definition\n *\n * The language of the telemetry SDK.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_CPP = 'cpp';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_DOTNET = 'dotnet';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_ERLANG = 'erlang';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_GO = 'go';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_JAVA = 'java';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_NODEJS = 'nodejs';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_PHP = 'php';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_PYTHON = 'python';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_RUBY = 'ruby';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_WEBJS = 'webjs';\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_CPP.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_CPP =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_CPP;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_DOTNET.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_DOTNET =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_DOTNET;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_ERLANG.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_ERLANG =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_ERLANG;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_GO.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_GO = TMP_TELEMETRYSDKLANGUAGEVALUES_GO;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_JAVA.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_JAVA =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_JAVA;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_NODEJS.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_NODEJS =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_NODEJS;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_PHP.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_PHP =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_PHP;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_PYTHON.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_PYTHON =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_PYTHON;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_RUBY.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_RUBY =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_RUBY;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_WEBJS.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_WEBJS =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_WEBJS;\n\n/**\n * Identifies the Values for TelemetrySdkLanguageValues enum definition\n *\n * The language of the telemetry SDK.\n * @deprecated Use the TELEMETRYSDKLANGUAGEVALUES_XXXXX constants rather than the TelemetrySdkLanguageValues.XXXXX for bundle minification.\n */\nexport type TelemetrySdkLanguageValues = {\n  /** cpp. */\n  CPP: 'cpp';\n\n  /** dotnet. */\n  DOTNET: 'dotnet';\n\n  /** erlang. */\n  ERLANG: 'erlang';\n\n  /** go. */\n  GO: 'go';\n\n  /** java. */\n  JAVA: 'java';\n\n  /** nodejs. */\n  NODEJS: 'nodejs';\n\n  /** php. */\n  PHP: 'php';\n\n  /** python. */\n  PYTHON: 'python';\n\n  /** ruby. */\n  RUBY: 'ruby';\n\n  /** webjs. */\n  WEBJS: 'webjs';\n};\n\n/**\n * The constant map of values for TelemetrySdkLanguageValues.\n * @deprecated Use the TELEMETRYSDKLANGUAGEVALUES_XXXXX constants rather than the TelemetrySdkLanguageValues.XXXXX for bundle minification.\n */\nexport const TelemetrySdkLanguageValues: TelemetrySdkLanguageValues =\n  /*#__PURE__*/ createConstMap<TelemetrySdkLanguageValues>([\n    TMP_TELEMETRYSDKLANGUAGEVALUES_CPP,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_DOTNET,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_ERLANG,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_GO,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_JAVA,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_NODEJS,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_PHP,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_PYTHON,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_RUBY,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_WEBJS,\n  ]);\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;;AAEnD,4GAA4G;AAC5G,iHAAiH;AACjH,4GAA4G;AAE5G,4GAA4G;AAC5G,iDAAiD;AACjD,4GAA4G;AAE5G,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,oBAAoB,GAAG,kBAAkB,CAAC;AAChD,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAC1D,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,oBAAoB,GAAG,kBAAkB,CAAC;AAChD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAC1D,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAClD,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,0BAA0B,GAAG,wBAAwB,CAAC;AAC5D,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAClD,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,WAAW,GAAG,SAAS,CAAC;AAC9B,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAC9C,IAAM,WAAW,GAAG,SAAS,CAAC;AAC9B,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAC9C,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,oBAAoB,GAAG,kBAAkB,CAAC;AAChD,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAClD,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAC9C,IAAM,oBAAoB,GAAG,kBAAkB,CAAC;AAChD,IAAM,WAAW,GAAG,SAAS,CAAC;AAC9B,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,WAAW,GAAG,SAAS,CAAC;AAC9B,IAAM,cAAc,GAAG,YAAY,CAAC;AACpC,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAC9C,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,+BAA+B,GAAG,6BAA6B,CAAC;AACtE,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAClD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAC9C,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,0BAA0B,GAAG,wBAAwB,CAAC;AAC5D,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAC1D,IAAM,0BAA0B,GAAG,wBAAwB,CAAC;AAC5D,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAClD,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAOnD,IAAM,0BAA0B,GAAG,kBAAkB,CAAC;AAOtD,IAAM,4BAA4B,GAAG,oBAAoB,CAAC;AAO1D,IAAM,wBAAwB,GAAG,gBAAgB,CAAC;AASlD,IAAM,mCAAmC,GAAG,2BAA2B,CAAC;AASxE,IAAM,0BAA0B,GAAG,kBAAkB,CAAC;AAOtD,IAAM,iCAAiC,GAAG,yBAAyB,CAAC;AAOpE,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAOhE,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAO9D,IAAM,4BAA4B,GAAG,oBAAoB,CAAC;AAO1D,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAOhE,IAAM,iCAAiC,GAAG,yBAAyB,CAAC;AAOpE,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAShE,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAShE,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAO9D,IAAM,gCAAgC,GAAG,wBAAwB,CAAC;AASlE,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAOhE,IAAM,0BAA0B,GAAG,kBAAkB,CAAC;AAOtD,IAAM,wBAAwB,GAAG,gBAAgB,CAAC;AAOlD,IAAM,6BAA6B,GAAG,qBAAqB,CAAC;AAO5D,IAAM,gCAAgC,GAAG,wBAAwB,CAAC;AAOlE,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAOhE,IAAM,kCAAkC,GAAG,0BAA0B,CAAC;AAStE,IAAM,qBAAqB,GAAG,aAAa,CAAC;AAS5C,IAAM,mCAAmC,GAAG,2BAA2B,CAAC;AASxE,IAAM,6BAA6B,GAAG,qBAAqB,CAAC;AAS5D,IAAM,qBAAqB,GAAG,aAAa,CAAC;AAsB5C,IAAM,mBAAmB,GAAG,WAAW,CAAC;AAiBxC,IAAM,wBAAwB,GAAG,gBAAgB,CAAC;AASlD,IAAM,yBAAyB,GAAG,iBAAiB,CAAC;AASpD,IAAM,2BAA2B,GAAG,mBAAmB,CAAC;AAOxD,IAAM,mBAAmB,GAAG,WAAW,CAAC;AAOxC,IAAM,qBAAqB,GAAG,aAAa,CAAC;AAO5C,IAAM,qBAAqB,GAAG,aAAa,CAAC;AAO5C,IAAM,qBAAqB,GAAG,aAAa,CAAC;AAO5C,IAAM,2BAA2B,GAAG,mBAAmB,CAAC;AAOxD,IAAM,yBAAyB,GAAG,iBAAiB,CAAC;AAOpD,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAO9D,IAAM,4BAA4B,GAAG,oBAAoB,CAAC;AAO1D,IAAM,yBAAyB,GAAG,iBAAiB,CAAC;AAOpD,IAAM,wBAAwB,GAAG,gBAAgB,CAAC;AAOlD,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAO9D,IAAM,uBAAuB,GAAG,eAAe,CAAC;AAOhD,IAAM,wBAAwB,GAAG,gBAAgB,CAAC;AAOlD,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAO9D,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAO9D,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAOhE,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAO9D,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAOhE,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAOhE,IAAM,gCAAgC,GAAG,wBAAwB,CAAC;AAOlE,IAAM,6BAA6B,GAAG,qBAAqB,CAAC;AAO5D,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAO9D,IAAM,uBAAuB,GAAG,eAAe,CAAC;AAOhD,IAAM,wBAAwB,GAAG,gBAAgB,CAAC;AAOlD,IAAM,2BAA2B,GAAG,mBAAmB,CAAC;AAOxD,IAAM,4BAA4B,GAAG,oBAAoB,CAAC;AAO1D,IAAM,mBAAmB,GAAG,WAAW,CAAC;AAOxC,IAAM,0BAA0B,GAAG,kBAAkB,CAAC;AAOtD,IAAM,mBAAmB,GAAG,WAAW,CAAC;AAOxC,IAAM,sBAAsB,GAAG,cAAc,CAAC;AAO9C,IAAM,uBAAuB,GAAG,eAAe,CAAC;AAOhD,IAAM,mCAAmC,GAAG,2BAA2B,CAAC;AAOxE,IAAM,mCAAmC,GAAG,2BAA2B,CAAC;AAOxE,IAAM,2BAA2B,GAAG,mBAAmB,CAAC;AAOxD,IAAM,gCAAgC,GAAG,wBAAwB,CAAC;AAOlE,IAAM,gCAAgC,GAAG,wBAAwB,CAAC;AAOlE,IAAM,yBAAyB,GAAG,iBAAiB,CAAC;AAOpD,IAAM,gCAAgC,GAAG,wBAAwB,CAAC;AAOlE,IAAM,mCAAmC,GAAG,2BAA2B,CAAC;AAOxE,IAAM,uCAAuC,GAClD,+BAA+B,CAAC;AAS3B,IAAM,wBAAwB,GAAG,gBAAgB,CAAC;AASlD,IAAM,6BAA6B,GAAG,qBAAqB,CAAC;AAS5D,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAOhE,IAAM,2BAA2B,GAAG,mBAAmB,CAAC;AAOxD,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAO9D,IAAM,kCAAkC,GAAG,0BAA0B,CAAC;AAOtE,IAAM,iCAAiC,GAAG,yBAAyB,CAAC;AAOpE,IAAM,kCAAkC,GAAG,0BAA0B,CAAC;AAOtE,IAAM,0BAA0B,GAAG,kBAAkB,CAAC;AAOtD,IAAM,6BAA6B,GAAG,qBAAqB,CAAC;AAO5D,IAAM,iCAAiC,GAAG,yBAAyB,CAAC;AAydpE,IAAM,0BAA0B,GACrC,WAAA,EAAa,oRAAC,iBAAA,AAAc,EAA6B;IACvD,kBAAkB;IAClB,oBAAoB;IACpB,gBAAgB;IAChB,2BAA2B;IAC3B,kBAAkB;IAClB,yBAAyB;IACzB,uBAAuB;IACvB,sBAAsB;IACtB,oBAAoB;IACpB,uBAAuB;IACvB,yBAAyB;IACzB,uBAAuB;IACvB,uBAAuB;IACvB,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,kBAAkB;IAClB,gBAAgB;IAChB,qBAAqB;IACrB,wBAAwB;IACxB,uBAAuB;IACvB,0BAA0B;IAC1B,aAAa;IACb,2BAA2B;IAC3B,qBAAqB;IACrB,aAAa;IACb,WAAW;IACX,gBAAgB;IAChB,iBAAiB;IACjB,mBAAmB;IACnB,WAAW;IACX,aAAa;IACb,aAAa;IACb,aAAa;IACb,mBAAmB;IACnB,iBAAiB;IACjB,sBAAsB;IACtB,oBAAoB;IACpB,iBAAiB;IACjB,gBAAgB;IAChB,sBAAsB;IACtB,eAAe;IACf,gBAAgB;IAChB,sBAAsB;IACtB,sBAAsB;IACtB,uBAAuB;IACvB,sBAAsB;IACtB,uBAAuB;IACvB,uBAAuB;IACvB,wBAAwB;IACxB,qBAAqB;IACrB,sBAAsB;IACtB,eAAe;IACf,gBAAgB;IAChB,mBAAmB;IACnB,oBAAoB;IACpB,WAAW;IACX,kBAAkB;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,2BAA2B;IAC3B,2BAA2B;IAC3B,mBAAmB;IACnB,wBAAwB;IACxB,wBAAwB;IACxB,iBAAiB;IACjB,wBAAwB;IACxB,2BAA2B;IAC3B,+BAA+B;IAC/B,gBAAgB;IAChB,qBAAqB;IACrB,uBAAuB;IACvB,mBAAmB;IACnB,sBAAsB;IACtB,0BAA0B;IAC1B,yBAAyB;IACzB,0BAA0B;IAC1B,kBAAkB;IAClB,qBAAqB;IACrB,yBAAyB;CAC1B,CAAC,CAAC;AAEL;;;;gHAIgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,qCAAqC,GAAG,eAAe,CAAC;AAC9D,IAAM,2BAA2B,GAAG,KAAK,CAAC;AAC1C,IAAM,6BAA6B,GAAG,OAAO,CAAC;AAC9C,IAAM,2BAA2B,GAAG,KAAK,CAAC;AAOnC,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAOjC,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAO5D,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAOhE,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AA0B5D,IAAM,mBAAmB,GAC9B,WAAA,EAAa,oRAAC,iBAAA,AAAc,EAAsB;IAChD,qCAAqC;IACrC,2BAA2B;IAC3B,6BAA6B;IAC7B,2BAA2B;CAC5B,CAAC,CAAC;AAEL;;;;;;gHAMgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,yCAAyC,GAAG,mBAAmB,CAAC;AACtE,IAAM,wCAAwC,GAAG,kBAAkB,CAAC;AACpE,IAAM,+BAA+B,GAAG,SAAS,CAAC;AAClD,IAAM,+BAA+B,GAAG,SAAS,CAAC;AAClD,IAAM,+BAA+B,GAAG,SAAS,CAAC;AAClD,IAAM,kCAAkC,GAAG,YAAY,CAAC;AACxD,IAAM,6CAA6C,GAAG,uBAAuB,CAAC;AAC9E,IAAM,gCAAgC,GAAG,UAAU,CAAC;AACpD,IAAM,iDAAiD,GACrD,2BAA2B,CAAC;AAC9B,IAAM,iCAAiC,GAAG,WAAW,CAAC;AACtD,IAAM,uCAAuC,GAAG,iBAAiB,CAAC;AAClE,IAAM,yCAAyC,GAAG,mBAAmB,CAAC;AACtE,IAAM,0CAA0C,GAAG,oBAAoB,CAAC;AACxE,IAAM,qCAAqC,GAAG,eAAe,CAAC;AAC9D,IAAM,6CAA6C,GAAG,uBAAuB,CAAC;AAC9E,IAAM,2CAA2C,GAAG,qBAAqB,CAAC;AAC1E,IAAM,sCAAsC,GAAG,gBAAgB,CAAC;AASzD,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AASrC,IAAM,oCAAoC,GAC/C,wCAAwC,CAAC;AASpC,IAAM,2BAA2B,GAAG,+BAA+B,CAAC;AASpE,IAAM,2BAA2B,GAAG,+BAA+B,CAAC;AASpE,IAAM,2BAA2B,GAAG,+BAA+B,CAAC;AASpE,IAAM,8BAA8B,GACzC,kCAAkC,CAAC;AAS9B,IAAM,yCAAyC,GACpD,6CAA6C,CAAC;AASzC,IAAM,4BAA4B,GAAG,gCAAgC,CAAC;AAStE,IAAM,6CAA6C,GACxD,iDAAiD,CAAC;AAS7C,IAAM,6BAA6B,GAAG,iCAAiC,CAAC;AASxE,IAAM,mCAAmC,GAC9C,uCAAuC,CAAC;AASnC,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AASrC,IAAM,sCAAsC,GACjD,0CAA0C,CAAC;AAStC,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AASjC,IAAM,yCAAyC,GACpD,6CAA6C,CAAC;AASzC,IAAM,uCAAuC,GAClD,2CAA2C,CAAC;AASvC,IAAM,kCAAkC,GAC7C,sCAAsC,CAAC;AAmElC,IAAM,mBAAmB,GAC9B,WAAA,EAAa,oRAAC,iBAAA,AAAc,EAAsB;IAChD,yCAAyC;IACzC,wCAAwC;IACxC,+BAA+B;IAC/B,+BAA+B;IAC/B,+BAA+B;IAC/B,kCAAkC;IAClC,6CAA6C;IAC7C,gCAAgC;IAChC,iDAAiD;IACjD,iCAAiC;IACjC,uCAAuC;IACvC,yCAAyC;IACzC,0CAA0C;IAC1C,qCAAqC;IACrC,6CAA6C;IAC7C,2CAA2C;IAC3C,sCAAsC;CACvC,CAAC,CAAC;AAEL;;;;gHAIgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,8BAA8B,GAAG,KAAK,CAAC;AAC7C,IAAM,kCAAkC,GAAG,SAAS,CAAC;AAO9C,IAAM,0BAA0B,GAAG,8BAA8B,CAAC;AAOlE,IAAM,8BAA8B,GACzC,kCAAkC,CAAC;AAoB9B,IAAM,sBAAsB,GACjC,WAAA,EAAa,oRAAC,iBAAA,AAAc,EAAyB;IACnD,8BAA8B;IAC9B,kCAAkC;CACnC,CAAC,CAAC;AAEL;;;;gHAIgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,uBAAuB,GAAG,MAAM,CAAC;AACvC,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,sBAAsB,GAAG,KAAK,CAAC;AAO9B,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,mBAAmB,GAAG,uBAAuB,CAAC;AAOpD,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,kBAAkB,GAAG,sBAAsB,CAAC;AAmClD,IAAM,cAAc,GACzB,WAAA,EAAa,oRAAC,iBAAA,AAAc,EAAiB;IAC3C,wBAAwB;IACxB,wBAAwB;IACxB,wBAAwB;IACxB,uBAAuB;IACvB,wBAAwB;IACxB,wBAAwB;IACxB,sBAAsB;CACvB,CAAC,CAAC;AAEL;;;;gHAIgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,wBAAwB,GAAG,SAAS,CAAC;AAC3C,IAAM,sBAAsB,GAAG,OAAO,CAAC;AACvC,IAAM,uBAAuB,GAAG,QAAQ,CAAC;AACzC,IAAM,wBAAwB,GAAG,SAAS,CAAC;AAC3C,IAAM,uBAAuB,GAAG,QAAQ,CAAC;AACzC,IAAM,wBAAwB,GAAG,SAAS,CAAC;AAC3C,IAAM,6BAA6B,GAAG,cAAc,CAAC;AACrD,IAAM,qBAAqB,GAAG,MAAM,CAAC;AACrC,IAAM,oBAAoB,GAAG,KAAK,CAAC;AACnC,IAAM,wBAAwB,GAAG,SAAS,CAAC;AAC3C,IAAM,qBAAqB,GAAG,MAAM,CAAC;AAO9B,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,kBAAkB,GAAG,sBAAsB,CAAC;AAOlD,IAAM,mBAAmB,GAAG,uBAAuB,CAAC;AAOpD,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,mBAAmB,GAAG,uBAAuB,CAAC;AAOpD,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAOhE,IAAM,iBAAiB,GAAG,qBAAqB,CAAC;AAOhD,IAAM,gBAAgB,GAAG,oBAAoB,CAAC;AAO9C,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,iBAAiB,GAAG,qBAAqB,CAAC;AA+ChD,IAAM,YAAY,GACvB,WAAA,EAAa,oRAAC,iBAAA,AAAc,EAAe;IACzC,wBAAwB;IACxB,sBAAsB;IACtB,uBAAuB;IACvB,wBAAwB;IACxB,uBAAuB;IACvB,wBAAwB;IACxB,6BAA6B;IAC7B,qBAAqB;IACrB,oBAAoB;IACpB,wBAAwB;IACxB,qBAAqB;CACtB,CAAC,CAAC;AAEL;;;;gHAIgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,kCAAkC,GAAG,KAAK,CAAC;AACjD,IAAM,qCAAqC,GAAG,QAAQ,CAAC;AACvD,IAAM,qCAAqC,GAAG,QAAQ,CAAC;AACvD,IAAM,iCAAiC,GAAG,IAAI,CAAC;AAC/C,IAAM,mCAAmC,GAAG,MAAM,CAAC;AACnD,IAAM,qCAAqC,GAAG,QAAQ,CAAC;AACvD,IAAM,kCAAkC,GAAG,KAAK,CAAC;AACjD,IAAM,qCAAqC,GAAG,QAAQ,CAAC;AACvD,IAAM,mCAAmC,GAAG,MAAM,CAAC;AACnD,IAAM,oCAAoC,GAAG,OAAO,CAAC;AAO9C,IAAM,8BAA8B,GACzC,kCAAkC,CAAC;AAO9B,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAOjC,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAOjC,IAAM,6BAA6B,GAAG,iCAAiC,CAAC;AAOxE,IAAM,+BAA+B,GAC1C,mCAAmC,CAAC;AAO/B,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAOjC,IAAM,8BAA8B,GACzC,kCAAkC,CAAC;AAO9B,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAOjC,IAAM,+BAA+B,GAC1C,mCAAmC,CAAC;AAO/B,IAAM,gCAAgC,GAC3C,oCAAoC,CAAC;AA4ChC,IAAM,0BAA0B,GACrC,WAAA,EAAa,oRAAC,iBAAA,AAAc,EAA6B;IACvD,kCAAkC;IAClC,qCAAqC;IACrC,qCAAqC;IACrC,iCAAiC;IACjC,mCAAmC;IACnC,qCAAqC;IACrC,kCAAkC;IAClC,qCAAqC;IACrC,mCAAmC;IACnC,oCAAoC;CACrC,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 665, "column": 0}, "map": {"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry/node/node_modules/%40opentelemetry/resources/node_modules/%40opentelemetry/semantic-conventions/src/internal/utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Creates a const map from the given values\n * @param values - An array of values to be used as keys and values in the map.\n * @returns A populated version of the map with the values and keys derived from the values.\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function createConstMap<T>(values: Array<T[keyof T]>): T {\n  // eslint-disable-next-line prefer-const, @typescript-eslint/no-explicit-any\n  let res: any = {};\n  const len = values.length;\n  for (let lp = 0; lp < len; lp++) {\n    const val = values[lp];\n    if (val) {\n      res[String(val).toUpperCase().replace(/[-.]/g, '_')] = val;\n    }\n  }\n\n  return res as T;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH;;;;GAIG,CACH,sBAAA,EAAwB;;;AAClB,SAAU,cAAc,CAAI,MAAyB;IACzD,4EAA4E;IAC5E,IAAI,GAAG,GAAQ,CAAA,CAAE,CAAC;IAClB,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;IAC1B,IAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,CAAE;QAC/B,IAAM,GAAG,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;QACvB,IAAI,GAAG,EAAE;YACP,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;SAC5D;KACF;IAED,OAAO,GAAQ,CAAC;AAClB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 704, "column": 0}, "map": {"version": 3, "file": "SemanticResourceAttributes.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry/node/node_modules/%40opentelemetry/resources/node_modules/%40opentelemetry/semantic-conventions/src/resource/SemanticResourceAttributes.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createConstMap } from '../internal/utils';\n\n//----------------------------------------------------------------------------------------------------------\n// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates//templates/SemanticAttributes.ts.j2\n//----------------------------------------------------------------------------------------------------------\n\n//----------------------------------------------------------------------------------------------------------\n// Constant values for SemanticResourceAttributes\n//----------------------------------------------------------------------------------------------------------\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_CLOUD_PROVIDER = 'cloud.provider';\nconst TMP_CLOUD_ACCOUNT_ID = 'cloud.account.id';\nconst TMP_CLOUD_REGION = 'cloud.region';\nconst TMP_CLOUD_AVAILABILITY_ZONE = 'cloud.availability_zone';\nconst TMP_CLOUD_PLATFORM = 'cloud.platform';\nconst TMP_AWS_ECS_CONTAINER_ARN = 'aws.ecs.container.arn';\nconst TMP_AWS_ECS_CLUSTER_ARN = 'aws.ecs.cluster.arn';\nconst TMP_AWS_ECS_LAUNCHTYPE = 'aws.ecs.launchtype';\nconst TMP_AWS_ECS_TASK_ARN = 'aws.ecs.task.arn';\nconst TMP_AWS_ECS_TASK_FAMILY = 'aws.ecs.task.family';\nconst TMP_AWS_ECS_TASK_REVISION = 'aws.ecs.task.revision';\nconst TMP_AWS_EKS_CLUSTER_ARN = 'aws.eks.cluster.arn';\nconst TMP_AWS_LOG_GROUP_NAMES = 'aws.log.group.names';\nconst TMP_AWS_LOG_GROUP_ARNS = 'aws.log.group.arns';\nconst TMP_AWS_LOG_STREAM_NAMES = 'aws.log.stream.names';\nconst TMP_AWS_LOG_STREAM_ARNS = 'aws.log.stream.arns';\nconst TMP_CONTAINER_NAME = 'container.name';\nconst TMP_CONTAINER_ID = 'container.id';\nconst TMP_CONTAINER_RUNTIME = 'container.runtime';\nconst TMP_CONTAINER_IMAGE_NAME = 'container.image.name';\nconst TMP_CONTAINER_IMAGE_TAG = 'container.image.tag';\nconst TMP_DEPLOYMENT_ENVIRONMENT = 'deployment.environment';\nconst TMP_DEVICE_ID = 'device.id';\nconst TMP_DEVICE_MODEL_IDENTIFIER = 'device.model.identifier';\nconst TMP_DEVICE_MODEL_NAME = 'device.model.name';\nconst TMP_FAAS_NAME = 'faas.name';\nconst TMP_FAAS_ID = 'faas.id';\nconst TMP_FAAS_VERSION = 'faas.version';\nconst TMP_FAAS_INSTANCE = 'faas.instance';\nconst TMP_FAAS_MAX_MEMORY = 'faas.max_memory';\nconst TMP_HOST_ID = 'host.id';\nconst TMP_HOST_NAME = 'host.name';\nconst TMP_HOST_TYPE = 'host.type';\nconst TMP_HOST_ARCH = 'host.arch';\nconst TMP_HOST_IMAGE_NAME = 'host.image.name';\nconst TMP_HOST_IMAGE_ID = 'host.image.id';\nconst TMP_HOST_IMAGE_VERSION = 'host.image.version';\nconst TMP_K8S_CLUSTER_NAME = 'k8s.cluster.name';\nconst TMP_K8S_NODE_NAME = 'k8s.node.name';\nconst TMP_K8S_NODE_UID = 'k8s.node.uid';\nconst TMP_K8S_NAMESPACE_NAME = 'k8s.namespace.name';\nconst TMP_K8S_POD_UID = 'k8s.pod.uid';\nconst TMP_K8S_POD_NAME = 'k8s.pod.name';\nconst TMP_K8S_CONTAINER_NAME = 'k8s.container.name';\nconst TMP_K8S_REPLICASET_UID = 'k8s.replicaset.uid';\nconst TMP_K8S_REPLICASET_NAME = 'k8s.replicaset.name';\nconst TMP_K8S_DEPLOYMENT_UID = 'k8s.deployment.uid';\nconst TMP_K8S_DEPLOYMENT_NAME = 'k8s.deployment.name';\nconst TMP_K8S_STATEFULSET_UID = 'k8s.statefulset.uid';\nconst TMP_K8S_STATEFULSET_NAME = 'k8s.statefulset.name';\nconst TMP_K8S_DAEMONSET_UID = 'k8s.daemonset.uid';\nconst TMP_K8S_DAEMONSET_NAME = 'k8s.daemonset.name';\nconst TMP_K8S_JOB_UID = 'k8s.job.uid';\nconst TMP_K8S_JOB_NAME = 'k8s.job.name';\nconst TMP_K8S_CRONJOB_UID = 'k8s.cronjob.uid';\nconst TMP_K8S_CRONJOB_NAME = 'k8s.cronjob.name';\nconst TMP_OS_TYPE = 'os.type';\nconst TMP_OS_DESCRIPTION = 'os.description';\nconst TMP_OS_NAME = 'os.name';\nconst TMP_OS_VERSION = 'os.version';\nconst TMP_PROCESS_PID = 'process.pid';\nconst TMP_PROCESS_EXECUTABLE_NAME = 'process.executable.name';\nconst TMP_PROCESS_EXECUTABLE_PATH = 'process.executable.path';\nconst TMP_PROCESS_COMMAND = 'process.command';\nconst TMP_PROCESS_COMMAND_LINE = 'process.command_line';\nconst TMP_PROCESS_COMMAND_ARGS = 'process.command_args';\nconst TMP_PROCESS_OWNER = 'process.owner';\nconst TMP_PROCESS_RUNTIME_NAME = 'process.runtime.name';\nconst TMP_PROCESS_RUNTIME_VERSION = 'process.runtime.version';\nconst TMP_PROCESS_RUNTIME_DESCRIPTION = 'process.runtime.description';\nconst TMP_SERVICE_NAME = 'service.name';\nconst TMP_SERVICE_NAMESPACE = 'service.namespace';\nconst TMP_SERVICE_INSTANCE_ID = 'service.instance.id';\nconst TMP_SERVICE_VERSION = 'service.version';\nconst TMP_TELEMETRY_SDK_NAME = 'telemetry.sdk.name';\nconst TMP_TELEMETRY_SDK_LANGUAGE = 'telemetry.sdk.language';\nconst TMP_TELEMETRY_SDK_VERSION = 'telemetry.sdk.version';\nconst TMP_TELEMETRY_AUTO_VERSION = 'telemetry.auto.version';\nconst TMP_WEBENGINE_NAME = 'webengine.name';\nconst TMP_WEBENGINE_VERSION = 'webengine.version';\nconst TMP_WEBENGINE_DESCRIPTION = 'webengine.description';\n\n/**\n * Name of the cloud provider.\n *\n * @deprecated Use ATTR_CLOUD_PROVIDER in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CLOUD_PROVIDER = TMP_CLOUD_PROVIDER;\n\n/**\n * The cloud account ID the resource is assigned to.\n *\n * @deprecated Use ATTR_CLOUD_ACCOUNT_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CLOUD_ACCOUNT_ID = TMP_CLOUD_ACCOUNT_ID;\n\n/**\n * The geographical region the resource is running. Refer to your provider&#39;s docs to see the available regions, for example [Alibaba Cloud regions](https://www.alibabacloud.com/help/doc-detail/40654.htm), [AWS regions](https://aws.amazon.com/about-aws/global-infrastructure/regions_az/), [Azure regions](https://azure.microsoft.com/en-us/global-infrastructure/geographies/), or [Google Cloud regions](https://cloud.google.com/about/locations).\n *\n * @deprecated Use ATTR_CLOUD_REGION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CLOUD_REGION = TMP_CLOUD_REGION;\n\n/**\n * Cloud regions often have multiple, isolated locations known as zones to increase availability. Availability zone represents the zone where the resource is running.\n *\n * Note: Availability zones are called &#34;zones&#34; on Alibaba Cloud and Google Cloud.\n *\n * @deprecated Use ATTR_CLOUD_AVAILABILITY_ZONE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CLOUD_AVAILABILITY_ZONE = TMP_CLOUD_AVAILABILITY_ZONE;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use ATTR_CLOUD_PLATFORM in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CLOUD_PLATFORM = TMP_CLOUD_PLATFORM;\n\n/**\n * The Amazon Resource Name (ARN) of an [ECS container instance](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/ECS_instances.html).\n *\n * @deprecated Use ATTR_AWS_ECS_CONTAINER_ARN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_ECS_CONTAINER_ARN = TMP_AWS_ECS_CONTAINER_ARN;\n\n/**\n * The ARN of an [ECS cluster](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/clusters.html).\n *\n * @deprecated Use ATTR_AWS_ECS_CLUSTER_ARN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_ECS_CLUSTER_ARN = TMP_AWS_ECS_CLUSTER_ARN;\n\n/**\n * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.\n *\n * @deprecated Use ATTR_AWS_ECS_LAUNCHTYPE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_ECS_LAUNCHTYPE = TMP_AWS_ECS_LAUNCHTYPE;\n\n/**\n * The ARN of an [ECS task definition](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/task_definitions.html).\n *\n * @deprecated Use ATTR_AWS_ECS_TASK_ARN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_ECS_TASK_ARN = TMP_AWS_ECS_TASK_ARN;\n\n/**\n * The task definition family this task definition is a member of.\n *\n * @deprecated Use ATTR_AWS_ECS_TASK_FAMILY in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_ECS_TASK_FAMILY = TMP_AWS_ECS_TASK_FAMILY;\n\n/**\n * The revision for this task definition.\n *\n * @deprecated Use ATTR_AWS_ECS_TASK_REVISION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_ECS_TASK_REVISION = TMP_AWS_ECS_TASK_REVISION;\n\n/**\n * The ARN of an EKS cluster.\n *\n * @deprecated Use ATTR_AWS_EKS_CLUSTER_ARN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_EKS_CLUSTER_ARN = TMP_AWS_EKS_CLUSTER_ARN;\n\n/**\n * The name(s) of the AWS log group(s) an application is writing to.\n *\n * Note: Multiple log groups must be supported for cases like multi-container applications, where a single application has sidecar containers, and each write to their own log group.\n *\n * @deprecated Use ATTR_AWS_LOG_GROUP_NAMES in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_LOG_GROUP_NAMES = TMP_AWS_LOG_GROUP_NAMES;\n\n/**\n * The Amazon Resource Name(s) (ARN) of the AWS log group(s).\n *\n * Note: See the [log group ARN format documentation](https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/iam-access-control-overview-cwl.html#CWL_ARN_Format).\n *\n * @deprecated Use ATTR_AWS_LOG_GROUP_ARNS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_LOG_GROUP_ARNS = TMP_AWS_LOG_GROUP_ARNS;\n\n/**\n * The name(s) of the AWS log stream(s) an application is writing to.\n *\n * @deprecated Use ATTR_AWS_LOG_STREAM_NAMES in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_LOG_STREAM_NAMES = TMP_AWS_LOG_STREAM_NAMES;\n\n/**\n * The ARN(s) of the AWS log stream(s).\n *\n * Note: See the [log stream ARN format documentation](https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/iam-access-control-overview-cwl.html#CWL_ARN_Format). One log group can contain several log streams, so these ARNs necessarily identify both a log group and a log stream.\n *\n * @deprecated Use ATTR_AWS_LOG_STREAM_ARNS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_AWS_LOG_STREAM_ARNS = TMP_AWS_LOG_STREAM_ARNS;\n\n/**\n * Container name.\n *\n * @deprecated Use ATTR_CONTAINER_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CONTAINER_NAME = TMP_CONTAINER_NAME;\n\n/**\n * Container ID. Usually a UUID, as for example used to [identify Docker containers](https://docs.docker.com/engine/reference/run/#container-identification). The UUID might be abbreviated.\n *\n * @deprecated Use ATTR_CONTAINER_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CONTAINER_ID = TMP_CONTAINER_ID;\n\n/**\n * The container runtime managing this container.\n *\n * @deprecated Use ATTR_CONTAINER_RUNTIME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CONTAINER_RUNTIME = TMP_CONTAINER_RUNTIME;\n\n/**\n * Name of the image the container was built on.\n *\n * @deprecated Use ATTR_CONTAINER_IMAGE_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CONTAINER_IMAGE_NAME = TMP_CONTAINER_IMAGE_NAME;\n\n/**\n * Container image tag.\n *\n * @deprecated Use ATTR_CONTAINER_IMAGE_TAGS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_CONTAINER_IMAGE_TAG = TMP_CONTAINER_IMAGE_TAG;\n\n/**\n * Name of the [deployment environment](https://en.wikipedia.org/wiki/Deployment_environment) (aka deployment tier).\n *\n * @deprecated Use ATTR_DEPLOYMENT_ENVIRONMENT in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_DEPLOYMENT_ENVIRONMENT = TMP_DEPLOYMENT_ENVIRONMENT;\n\n/**\n * A unique identifier representing the device.\n *\n * Note: The device identifier MUST only be defined using the values outlined below. This value is not an advertising identifier and MUST NOT be used as such. On iOS (Swift or Objective-C), this value MUST be equal to the [vendor identifier](https://developer.apple.com/documentation/uikit/uidevice/1620059-identifierforvendor). On Android (Java or Kotlin), this value MUST be equal to the Firebase Installation ID or a globally unique UUID which is persisted across sessions in your application. More information can be found [here](https://developer.android.com/training/articles/user-data-ids) on best practices and exact implementation details. Caution should be taken when storing personal data or anything which can identify a user. GDPR and data protection laws may apply, ensure you do your own due diligence.\n *\n * @deprecated Use ATTR_DEVICE_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_DEVICE_ID = TMP_DEVICE_ID;\n\n/**\n * The model identifier for the device.\n *\n * Note: It&#39;s recommended this value represents a machine readable version of the model identifier rather than the market or consumer-friendly name of the device.\n *\n * @deprecated Use ATTR_DEVICE_MODEL_IDENTIFIER in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_DEVICE_MODEL_IDENTIFIER = TMP_DEVICE_MODEL_IDENTIFIER;\n\n/**\n * The marketing name for the device model.\n *\n * Note: It&#39;s recommended this value represents a human readable version of the device model rather than a machine readable alternative.\n *\n * @deprecated Use ATTR_DEVICE_MODEL_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_DEVICE_MODEL_NAME = TMP_DEVICE_MODEL_NAME;\n\n/**\n * The name of the single function that this runtime instance executes.\n *\n * Note: This is the name of the function as configured/deployed on the FaaS platform and is usually different from the name of the callback function (which may be stored in the [`code.namespace`/`code.function`](../../trace/semantic_conventions/span-general.md#source-code-attributes) span attributes).\n *\n * @deprecated Use ATTR_FAAS_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_FAAS_NAME = TMP_FAAS_NAME;\n\n/**\n* The unique ID of the single function that this runtime instance executes.\n*\n* Note: Depending on the cloud provider, use:\n\n* **AWS Lambda:** The function [ARN](https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html).\nTake care not to use the &#34;invoked ARN&#34; directly but replace any\n[alias suffix](https://docs.aws.amazon.com/lambda/latest/dg/configuration-aliases.html) with the resolved function version, as the same runtime instance may be invokable with multiple\ndifferent aliases.\n* **GCP:** The [URI of the resource](https://cloud.google.com/iam/docs/full-resource-names)\n* **Azure:** The [Fully Qualified Resource ID](https://docs.microsoft.com/en-us/rest/api/resources/resources/get-by-id).\n\nOn some providers, it may not be possible to determine the full ID at startup,\nwhich is why this field cannot be made required. For example, on AWS the account ID\npart of the ARN is not available without calling another AWS API\nwhich may be deemed too slow for a short-running lambda function.\nAs an alternative, consider setting `faas.id` as a span attribute instead.\n*\n* @deprecated Use ATTR_CLOUD_RESOURCE_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n*/\nexport const SEMRESATTRS_FAAS_ID = TMP_FAAS_ID;\n\n/**\n* The immutable version of the function being executed.\n*\n* Note: Depending on the cloud provider and platform, use:\n\n* **AWS Lambda:** The [function version](https://docs.aws.amazon.com/lambda/latest/dg/configuration-versions.html)\n  (an integer represented as a decimal string).\n* **Google Cloud Run:** The [revision](https://cloud.google.com/run/docs/managing/revisions)\n  (i.e., the function name plus the revision suffix).\n* **Google Cloud Functions:** The value of the\n  [`K_REVISION` environment variable](https://cloud.google.com/functions/docs/env-var#runtime_environment_variables_set_automatically).\n* **Azure Functions:** Not applicable. Do not set this attribute.\n*\n* @deprecated Use ATTR_FAAS_VERSION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n*/\nexport const SEMRESATTRS_FAAS_VERSION = TMP_FAAS_VERSION;\n\n/**\n * The execution environment ID as a string, that will be potentially reused for other invocations to the same function/function version.\n *\n * Note: * **AWS Lambda:** Use the (full) log stream name.\n *\n * @deprecated Use ATTR_FAAS_INSTANCE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_FAAS_INSTANCE = TMP_FAAS_INSTANCE;\n\n/**\n * The amount of memory available to the serverless function in MiB.\n *\n * Note: It&#39;s recommended to set this attribute since e.g. too little memory can easily stop a Java AWS Lambda function from working correctly. On AWS Lambda, the environment variable `AWS_LAMBDA_FUNCTION_MEMORY_SIZE` provides this information.\n *\n * @deprecated Use ATTR_FAAS_MAX_MEMORY in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_FAAS_MAX_MEMORY = TMP_FAAS_MAX_MEMORY;\n\n/**\n * Unique host ID. For Cloud, this must be the instance_id assigned by the cloud provider.\n *\n * @deprecated Use ATTR_HOST_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_HOST_ID = TMP_HOST_ID;\n\n/**\n * Name of the host. On Unix systems, it may contain what the hostname command returns, or the fully qualified hostname, or another name specified by the user.\n *\n * @deprecated Use ATTR_HOST_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_HOST_NAME = TMP_HOST_NAME;\n\n/**\n * Type of host. For Cloud, this must be the machine type.\n *\n * @deprecated Use ATTR_HOST_TYPE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_HOST_TYPE = TMP_HOST_TYPE;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use ATTR_HOST_ARCH in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_HOST_ARCH = TMP_HOST_ARCH;\n\n/**\n * Name of the VM image or OS install the host was instantiated from.\n *\n * @deprecated Use ATTR_HOST_IMAGE_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_HOST_IMAGE_NAME = TMP_HOST_IMAGE_NAME;\n\n/**\n * VM image ID. For Cloud, this value is from the provider.\n *\n * @deprecated Use ATTR_HOST_IMAGE_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_HOST_IMAGE_ID = TMP_HOST_IMAGE_ID;\n\n/**\n * The version string of the VM image as defined in [Version Attributes](README.md#version-attributes).\n *\n * @deprecated Use ATTR_HOST_IMAGE_VERSION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_HOST_IMAGE_VERSION = TMP_HOST_IMAGE_VERSION;\n\n/**\n * The name of the cluster.\n *\n * @deprecated Use ATTR_K8S_CLUSTER_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_CLUSTER_NAME = TMP_K8S_CLUSTER_NAME;\n\n/**\n * The name of the Node.\n *\n * @deprecated Use ATTR_K8S_NODE_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_NODE_NAME = TMP_K8S_NODE_NAME;\n\n/**\n * The UID of the Node.\n *\n * @deprecated Use ATTR_K8S_NODE_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_NODE_UID = TMP_K8S_NODE_UID;\n\n/**\n * The name of the namespace that the pod is running in.\n *\n * @deprecated Use ATTR_K8S_NAMESPACE_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_NAMESPACE_NAME = TMP_K8S_NAMESPACE_NAME;\n\n/**\n * The UID of the Pod.\n *\n * @deprecated Use ATTR_K8S_POD_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_POD_UID = TMP_K8S_POD_UID;\n\n/**\n * The name of the Pod.\n *\n * @deprecated Use ATTR_K8S_POD_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_POD_NAME = TMP_K8S_POD_NAME;\n\n/**\n * The name of the Container in a Pod template.\n *\n * @deprecated Use ATTR_K8S_CONTAINER_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_CONTAINER_NAME = TMP_K8S_CONTAINER_NAME;\n\n/**\n * The UID of the ReplicaSet.\n *\n * @deprecated Use ATTR_K8S_REPLICASET_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_REPLICASET_UID = TMP_K8S_REPLICASET_UID;\n\n/**\n * The name of the ReplicaSet.\n *\n * @deprecated Use ATTR_K8S_REPLICASET_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_REPLICASET_NAME = TMP_K8S_REPLICASET_NAME;\n\n/**\n * The UID of the Deployment.\n *\n * @deprecated Use ATTR_K8S_DEPLOYMENT_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_DEPLOYMENT_UID = TMP_K8S_DEPLOYMENT_UID;\n\n/**\n * The name of the Deployment.\n *\n * @deprecated Use ATTR_K8S_DEPLOYMENT_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_DEPLOYMENT_NAME = TMP_K8S_DEPLOYMENT_NAME;\n\n/**\n * The UID of the StatefulSet.\n *\n * @deprecated Use ATTR_K8S_STATEFULSET_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_STATEFULSET_UID = TMP_K8S_STATEFULSET_UID;\n\n/**\n * The name of the StatefulSet.\n *\n * @deprecated Use ATTR_K8S_STATEFULSET_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_STATEFULSET_NAME = TMP_K8S_STATEFULSET_NAME;\n\n/**\n * The UID of the DaemonSet.\n *\n * @deprecated Use ATTR_K8S_DAEMONSET_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_DAEMONSET_UID = TMP_K8S_DAEMONSET_UID;\n\n/**\n * The name of the DaemonSet.\n *\n * @deprecated Use ATTR_K8S_DAEMONSET_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_DAEMONSET_NAME = TMP_K8S_DAEMONSET_NAME;\n\n/**\n * The UID of the Job.\n *\n * @deprecated Use ATTR_K8S_JOB_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_JOB_UID = TMP_K8S_JOB_UID;\n\n/**\n * The name of the Job.\n *\n * @deprecated Use ATTR_K8S_JOB_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_JOB_NAME = TMP_K8S_JOB_NAME;\n\n/**\n * The UID of the CronJob.\n *\n * @deprecated Use ATTR_K8S_CRONJOB_UID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_CRONJOB_UID = TMP_K8S_CRONJOB_UID;\n\n/**\n * The name of the CronJob.\n *\n * @deprecated Use ATTR_K8S_CRONJOB_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_K8S_CRONJOB_NAME = TMP_K8S_CRONJOB_NAME;\n\n/**\n * The operating system type.\n *\n * @deprecated Use ATTR_OS_TYPE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_OS_TYPE = TMP_OS_TYPE;\n\n/**\n * Human readable (not intended to be parsed) OS version information, like e.g. reported by `ver` or `lsb_release -a` commands.\n *\n * @deprecated Use ATTR_OS_DESCRIPTION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_OS_DESCRIPTION = TMP_OS_DESCRIPTION;\n\n/**\n * Human readable operating system name.\n *\n * @deprecated Use ATTR_OS_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_OS_NAME = TMP_OS_NAME;\n\n/**\n * The version string of the operating system as defined in [Version Attributes](../../resource/semantic_conventions/README.md#version-attributes).\n *\n * @deprecated Use ATTR_OS_VERSION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_OS_VERSION = TMP_OS_VERSION;\n\n/**\n * Process identifier (PID).\n *\n * @deprecated Use ATTR_PROCESS_PID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_PID = TMP_PROCESS_PID;\n\n/**\n * The name of the process executable. On Linux based systems, can be set to the `Name` in `proc/[pid]/status`. On Windows, can be set to the base name of `GetProcessImageFileNameW`.\n *\n * @deprecated Use ATTR_PROCESS_EXECUTABLE_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_EXECUTABLE_NAME = TMP_PROCESS_EXECUTABLE_NAME;\n\n/**\n * The full path to the process executable. On Linux based systems, can be set to the target of `proc/[pid]/exe`. On Windows, can be set to the result of `GetProcessImageFileNameW`.\n *\n * @deprecated Use ATTR_PROCESS_EXECUTABLE_PATH in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_EXECUTABLE_PATH = TMP_PROCESS_EXECUTABLE_PATH;\n\n/**\n * The command used to launch the process (i.e. the command name). On Linux based systems, can be set to the zeroth string in `proc/[pid]/cmdline`. On Windows, can be set to the first parameter extracted from `GetCommandLineW`.\n *\n * @deprecated Use ATTR_PROCESS_COMMAND in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_COMMAND = TMP_PROCESS_COMMAND;\n\n/**\n * The full command used to launch the process as a single string representing the full command. On Windows, can be set to the result of `GetCommandLineW`. Do not set this if you have to assemble it just for monitoring; use `process.command_args` instead.\n *\n * @deprecated Use ATTR_PROCESS_COMMAND_LINE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_COMMAND_LINE = TMP_PROCESS_COMMAND_LINE;\n\n/**\n * All the command arguments (including the command/executable itself) as received by the process. On Linux-based systems (and some other Unixoid systems supporting procfs), can be set according to the list of null-delimited strings extracted from `proc/[pid]/cmdline`. For libc-based executables, this would be the full argv vector passed to `main`.\n *\n * @deprecated Use ATTR_PROCESS_COMMAND_ARGS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_COMMAND_ARGS = TMP_PROCESS_COMMAND_ARGS;\n\n/**\n * The username of the user that owns the process.\n *\n * @deprecated Use ATTR_PROCESS_OWNER in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_OWNER = TMP_PROCESS_OWNER;\n\n/**\n * The name of the runtime of this process. For compiled native binaries, this SHOULD be the name of the compiler.\n *\n * @deprecated Use ATTR_PROCESS_RUNTIME_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_RUNTIME_NAME = TMP_PROCESS_RUNTIME_NAME;\n\n/**\n * The version of the runtime of this process, as returned by the runtime without modification.\n *\n * @deprecated Use ATTR_PROCESS_RUNTIME_VERSION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_RUNTIME_VERSION = TMP_PROCESS_RUNTIME_VERSION;\n\n/**\n * An additional description about the runtime of the process, for example a specific vendor customization of the runtime environment.\n *\n * @deprecated Use ATTR_PROCESS_RUNTIME_DESCRIPTION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION =\n  TMP_PROCESS_RUNTIME_DESCRIPTION;\n\n/**\n * Logical name of the service.\n *\n * Note: MUST be the same for all instances of horizontally scaled services. If the value was not specified, SDKs MUST fallback to `unknown_service:` concatenated with [`process.executable.name`](process.md#process), e.g. `unknown_service:bash`. If `process.executable.name` is not available, the value MUST be set to `unknown_service`.\n *\n * @deprecated Use ATTR_SERVICE_NAME.\n */\nexport const SEMRESATTRS_SERVICE_NAME = TMP_SERVICE_NAME;\n\n/**\n * A namespace for `service.name`.\n *\n * Note: A string value having a meaning that helps to distinguish a group of services, for example the team name that owns a group of services. `service.name` is expected to be unique within the same namespace. If `service.namespace` is not specified in the Resource then `service.name` is expected to be unique for all services that have no explicit namespace defined (so the empty/unspecified namespace is simply one more valid namespace). Zero-length namespace string is assumed equal to unspecified namespace.\n *\n * @deprecated Use ATTR_SERVICE_NAMESPACE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_SERVICE_NAMESPACE = TMP_SERVICE_NAMESPACE;\n\n/**\n * The string ID of the service instance.\n *\n * Note: MUST be unique for each instance of the same `service.namespace,service.name` pair (in other words `service.namespace,service.name,service.instance.id` triplet MUST be globally unique). The ID helps to distinguish instances of the same service that exist at the same time (e.g. instances of a horizontally scaled service). It is preferable for the ID to be persistent and stay the same for the lifetime of the service instance, however it is acceptable that the ID is ephemeral and changes during important lifetime events for the service (e.g. service restarts). If the service has no inherent unique ID that can be used as the value of this attribute it is recommended to generate a random Version 1 or Version 4 RFC 4122 UUID (services aiming for reproducible UUIDs may also use Version 5, see RFC 4122 for more recommendations).\n *\n * @deprecated Use ATTR_SERVICE_INSTANCE_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_SERVICE_INSTANCE_ID = TMP_SERVICE_INSTANCE_ID;\n\n/**\n * The version string of the service API or implementation.\n *\n * @deprecated Use ATTR_SERVICE_VERSION.\n */\nexport const SEMRESATTRS_SERVICE_VERSION = TMP_SERVICE_VERSION;\n\n/**\n * The name of the telemetry SDK as defined above.\n *\n * @deprecated Use ATTR_TELEMETRY_SDK_NAME.\n */\nexport const SEMRESATTRS_TELEMETRY_SDK_NAME = TMP_TELEMETRY_SDK_NAME;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use ATTR_TELEMETRY_SDK_LANGUAGE.\n */\nexport const SEMRESATTRS_TELEMETRY_SDK_LANGUAGE = TMP_TELEMETRY_SDK_LANGUAGE;\n\n/**\n * The version string of the telemetry SDK.\n *\n * @deprecated Use ATTR_TELEMETRY_SDK_VERSION.\n */\nexport const SEMRESATTRS_TELEMETRY_SDK_VERSION = TMP_TELEMETRY_SDK_VERSION;\n\n/**\n * The version string of the auto instrumentation agent, if used.\n *\n * @deprecated Use ATTR_TELEMETRY_DISTRO_VERSION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_TELEMETRY_AUTO_VERSION = TMP_TELEMETRY_AUTO_VERSION;\n\n/**\n * The name of the web engine.\n *\n * @deprecated Use ATTR_WEBENGINE_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_WEBENGINE_NAME = TMP_WEBENGINE_NAME;\n\n/**\n * The version of the web engine.\n *\n * @deprecated Use ATTR_WEBENGINE_VERSION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_WEBENGINE_VERSION = TMP_WEBENGINE_VERSION;\n\n/**\n * Additional description of the web engine (e.g. detailed version and edition information).\n *\n * @deprecated Use ATTR_WEBENGINE_DESCRIPTION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMRESATTRS_WEBENGINE_DESCRIPTION = TMP_WEBENGINE_DESCRIPTION;\n\n/**\n * Definition of available values for SemanticResourceAttributes\n * This type is used for backward compatibility, you should use the individual exported\n * constants SemanticResourceAttributes_XXXXX rather than the exported constant map. As any single reference\n * to a constant map value will result in all strings being included into your bundle.\n * @deprecated Use the SEMRESATTRS_XXXXX constants rather than the SemanticResourceAttributes.XXXXX for bundle minification.\n */\nexport type SemanticResourceAttributes = {\n  /**\n   * Name of the cloud provider.\n   */\n  CLOUD_PROVIDER: 'cloud.provider';\n\n  /**\n   * The cloud account ID the resource is assigned to.\n   */\n  CLOUD_ACCOUNT_ID: 'cloud.account.id';\n\n  /**\n   * The geographical region the resource is running. Refer to your provider&#39;s docs to see the available regions, for example [Alibaba Cloud regions](https://www.alibabacloud.com/help/doc-detail/40654.htm), [AWS regions](https://aws.amazon.com/about-aws/global-infrastructure/regions_az/), [Azure regions](https://azure.microsoft.com/en-us/global-infrastructure/geographies/), or [Google Cloud regions](https://cloud.google.com/about/locations).\n   */\n  CLOUD_REGION: 'cloud.region';\n\n  /**\n   * Cloud regions often have multiple, isolated locations known as zones to increase availability. Availability zone represents the zone where the resource is running.\n   *\n   * Note: Availability zones are called &#34;zones&#34; on Alibaba Cloud and Google Cloud.\n   */\n  CLOUD_AVAILABILITY_ZONE: 'cloud.availability_zone';\n\n  /**\n   * The cloud platform in use.\n   *\n   * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n   */\n  CLOUD_PLATFORM: 'cloud.platform';\n\n  /**\n   * The Amazon Resource Name (ARN) of an [ECS container instance](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/ECS_instances.html).\n   */\n  AWS_ECS_CONTAINER_ARN: 'aws.ecs.container.arn';\n\n  /**\n   * The ARN of an [ECS cluster](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/clusters.html).\n   */\n  AWS_ECS_CLUSTER_ARN: 'aws.ecs.cluster.arn';\n\n  /**\n   * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.\n   */\n  AWS_ECS_LAUNCHTYPE: 'aws.ecs.launchtype';\n\n  /**\n   * The ARN of an [ECS task definition](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/task_definitions.html).\n   */\n  AWS_ECS_TASK_ARN: 'aws.ecs.task.arn';\n\n  /**\n   * The task definition family this task definition is a member of.\n   */\n  AWS_ECS_TASK_FAMILY: 'aws.ecs.task.family';\n\n  /**\n   * The revision for this task definition.\n   */\n  AWS_ECS_TASK_REVISION: 'aws.ecs.task.revision';\n\n  /**\n   * The ARN of an EKS cluster.\n   */\n  AWS_EKS_CLUSTER_ARN: 'aws.eks.cluster.arn';\n\n  /**\n   * The name(s) of the AWS log group(s) an application is writing to.\n   *\n   * Note: Multiple log groups must be supported for cases like multi-container applications, where a single application has sidecar containers, and each write to their own log group.\n   */\n  AWS_LOG_GROUP_NAMES: 'aws.log.group.names';\n\n  /**\n   * The Amazon Resource Name(s) (ARN) of the AWS log group(s).\n   *\n   * Note: See the [log group ARN format documentation](https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/iam-access-control-overview-cwl.html#CWL_ARN_Format).\n   */\n  AWS_LOG_GROUP_ARNS: 'aws.log.group.arns';\n\n  /**\n   * The name(s) of the AWS log stream(s) an application is writing to.\n   */\n  AWS_LOG_STREAM_NAMES: 'aws.log.stream.names';\n\n  /**\n   * The ARN(s) of the AWS log stream(s).\n   *\n   * Note: See the [log stream ARN format documentation](https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/iam-access-control-overview-cwl.html#CWL_ARN_Format). One log group can contain several log streams, so these ARNs necessarily identify both a log group and a log stream.\n   */\n  AWS_LOG_STREAM_ARNS: 'aws.log.stream.arns';\n\n  /**\n   * Container name.\n   */\n  CONTAINER_NAME: 'container.name';\n\n  /**\n   * Container ID. Usually a UUID, as for example used to [identify Docker containers](https://docs.docker.com/engine/reference/run/#container-identification). The UUID might be abbreviated.\n   */\n  CONTAINER_ID: 'container.id';\n\n  /**\n   * The container runtime managing this container.\n   */\n  CONTAINER_RUNTIME: 'container.runtime';\n\n  /**\n   * Name of the image the container was built on.\n   */\n  CONTAINER_IMAGE_NAME: 'container.image.name';\n\n  /**\n   * Container image tag.\n   */\n  CONTAINER_IMAGE_TAG: 'container.image.tag';\n\n  /**\n   * Name of the [deployment environment](https://en.wikipedia.org/wiki/Deployment_environment) (aka deployment tier).\n   */\n  DEPLOYMENT_ENVIRONMENT: 'deployment.environment';\n\n  /**\n   * A unique identifier representing the device.\n   *\n   * Note: The device identifier MUST only be defined using the values outlined below. This value is not an advertising identifier and MUST NOT be used as such. On iOS (Swift or Objective-C), this value MUST be equal to the [vendor identifier](https://developer.apple.com/documentation/uikit/uidevice/1620059-identifierforvendor). On Android (Java or Kotlin), this value MUST be equal to the Firebase Installation ID or a globally unique UUID which is persisted across sessions in your application. More information can be found [here](https://developer.android.com/training/articles/user-data-ids) on best practices and exact implementation details. Caution should be taken when storing personal data or anything which can identify a user. GDPR and data protection laws may apply, ensure you do your own due diligence.\n   */\n  DEVICE_ID: 'device.id';\n\n  /**\n   * The model identifier for the device.\n   *\n   * Note: It&#39;s recommended this value represents a machine readable version of the model identifier rather than the market or consumer-friendly name of the device.\n   */\n  DEVICE_MODEL_IDENTIFIER: 'device.model.identifier';\n\n  /**\n   * The marketing name for the device model.\n   *\n   * Note: It&#39;s recommended this value represents a human readable version of the device model rather than a machine readable alternative.\n   */\n  DEVICE_MODEL_NAME: 'device.model.name';\n\n  /**\n   * The name of the single function that this runtime instance executes.\n   *\n   * Note: This is the name of the function as configured/deployed on the FaaS platform and is usually different from the name of the callback function (which may be stored in the [`code.namespace`/`code.function`](../../trace/semantic_conventions/span-general.md#source-code-attributes) span attributes).\n   */\n  FAAS_NAME: 'faas.name';\n\n  /**\n  * The unique ID of the single function that this runtime instance executes.\n  *\n  * Note: Depending on the cloud provider, use:\n\n* **AWS Lambda:** The function [ARN](https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html).\nTake care not to use the &#34;invoked ARN&#34; directly but replace any\n[alias suffix](https://docs.aws.amazon.com/lambda/latest/dg/configuration-aliases.html) with the resolved function version, as the same runtime instance may be invokable with multiple\ndifferent aliases.\n* **GCP:** The [URI of the resource](https://cloud.google.com/iam/docs/full-resource-names)\n* **Azure:** The [Fully Qualified Resource ID](https://docs.microsoft.com/en-us/rest/api/resources/resources/get-by-id).\n\nOn some providers, it may not be possible to determine the full ID at startup,\nwhich is why this field cannot be made required. For example, on AWS the account ID\npart of the ARN is not available without calling another AWS API\nwhich may be deemed too slow for a short-running lambda function.\nAs an alternative, consider setting `faas.id` as a span attribute instead.\n  */\n  FAAS_ID: 'faas.id';\n\n  /**\n  * The immutable version of the function being executed.\n  *\n  * Note: Depending on the cloud provider and platform, use:\n\n* **AWS Lambda:** The [function version](https://docs.aws.amazon.com/lambda/latest/dg/configuration-versions.html)\n  (an integer represented as a decimal string).\n* **Google Cloud Run:** The [revision](https://cloud.google.com/run/docs/managing/revisions)\n  (i.e., the function name plus the revision suffix).\n* **Google Cloud Functions:** The value of the\n  [`K_REVISION` environment variable](https://cloud.google.com/functions/docs/env-var#runtime_environment_variables_set_automatically).\n* **Azure Functions:** Not applicable. Do not set this attribute.\n  */\n  FAAS_VERSION: 'faas.version';\n\n  /**\n   * The execution environment ID as a string, that will be potentially reused for other invocations to the same function/function version.\n   *\n   * Note: * **AWS Lambda:** Use the (full) log stream name.\n   */\n  FAAS_INSTANCE: 'faas.instance';\n\n  /**\n   * The amount of memory available to the serverless function in MiB.\n   *\n   * Note: It&#39;s recommended to set this attribute since e.g. too little memory can easily stop a Java AWS Lambda function from working correctly. On AWS Lambda, the environment variable `AWS_LAMBDA_FUNCTION_MEMORY_SIZE` provides this information.\n   */\n  FAAS_MAX_MEMORY: 'faas.max_memory';\n\n  /**\n   * Unique host ID. For Cloud, this must be the instance_id assigned by the cloud provider.\n   */\n  HOST_ID: 'host.id';\n\n  /**\n   * Name of the host. On Unix systems, it may contain what the hostname command returns, or the fully qualified hostname, or another name specified by the user.\n   */\n  HOST_NAME: 'host.name';\n\n  /**\n   * Type of host. For Cloud, this must be the machine type.\n   */\n  HOST_TYPE: 'host.type';\n\n  /**\n   * The CPU architecture the host system is running on.\n   */\n  HOST_ARCH: 'host.arch';\n\n  /**\n   * Name of the VM image or OS install the host was instantiated from.\n   */\n  HOST_IMAGE_NAME: 'host.image.name';\n\n  /**\n   * VM image ID. For Cloud, this value is from the provider.\n   */\n  HOST_IMAGE_ID: 'host.image.id';\n\n  /**\n   * The version string of the VM image as defined in [Version Attributes](README.md#version-attributes).\n   */\n  HOST_IMAGE_VERSION: 'host.image.version';\n\n  /**\n   * The name of the cluster.\n   */\n  K8S_CLUSTER_NAME: 'k8s.cluster.name';\n\n  /**\n   * The name of the Node.\n   */\n  K8S_NODE_NAME: 'k8s.node.name';\n\n  /**\n   * The UID of the Node.\n   */\n  K8S_NODE_UID: 'k8s.node.uid';\n\n  /**\n   * The name of the namespace that the pod is running in.\n   */\n  K8S_NAMESPACE_NAME: 'k8s.namespace.name';\n\n  /**\n   * The UID of the Pod.\n   */\n  K8S_POD_UID: 'k8s.pod.uid';\n\n  /**\n   * The name of the Pod.\n   */\n  K8S_POD_NAME: 'k8s.pod.name';\n\n  /**\n   * The name of the Container in a Pod template.\n   */\n  K8S_CONTAINER_NAME: 'k8s.container.name';\n\n  /**\n   * The UID of the ReplicaSet.\n   */\n  K8S_REPLICASET_UID: 'k8s.replicaset.uid';\n\n  /**\n   * The name of the ReplicaSet.\n   */\n  K8S_REPLICASET_NAME: 'k8s.replicaset.name';\n\n  /**\n   * The UID of the Deployment.\n   */\n  K8S_DEPLOYMENT_UID: 'k8s.deployment.uid';\n\n  /**\n   * The name of the Deployment.\n   */\n  K8S_DEPLOYMENT_NAME: 'k8s.deployment.name';\n\n  /**\n   * The UID of the StatefulSet.\n   */\n  K8S_STATEFULSET_UID: 'k8s.statefulset.uid';\n\n  /**\n   * The name of the StatefulSet.\n   */\n  K8S_STATEFULSET_NAME: 'k8s.statefulset.name';\n\n  /**\n   * The UID of the DaemonSet.\n   */\n  K8S_DAEMONSET_UID: 'k8s.daemonset.uid';\n\n  /**\n   * The name of the DaemonSet.\n   */\n  K8S_DAEMONSET_NAME: 'k8s.daemonset.name';\n\n  /**\n   * The UID of the Job.\n   */\n  K8S_JOB_UID: 'k8s.job.uid';\n\n  /**\n   * The name of the Job.\n   */\n  K8S_JOB_NAME: 'k8s.job.name';\n\n  /**\n   * The UID of the CronJob.\n   */\n  K8S_CRONJOB_UID: 'k8s.cronjob.uid';\n\n  /**\n   * The name of the CronJob.\n   */\n  K8S_CRONJOB_NAME: 'k8s.cronjob.name';\n\n  /**\n   * The operating system type.\n   */\n  OS_TYPE: 'os.type';\n\n  /**\n   * Human readable (not intended to be parsed) OS version information, like e.g. reported by `ver` or `lsb_release -a` commands.\n   */\n  OS_DESCRIPTION: 'os.description';\n\n  /**\n   * Human readable operating system name.\n   */\n  OS_NAME: 'os.name';\n\n  /**\n   * The version string of the operating system as defined in [Version Attributes](../../resource/semantic_conventions/README.md#version-attributes).\n   */\n  OS_VERSION: 'os.version';\n\n  /**\n   * Process identifier (PID).\n   */\n  PROCESS_PID: 'process.pid';\n\n  /**\n   * The name of the process executable. On Linux based systems, can be set to the `Name` in `proc/[pid]/status`. On Windows, can be set to the base name of `GetProcessImageFileNameW`.\n   */\n  PROCESS_EXECUTABLE_NAME: 'process.executable.name';\n\n  /**\n   * The full path to the process executable. On Linux based systems, can be set to the target of `proc/[pid]/exe`. On Windows, can be set to the result of `GetProcessImageFileNameW`.\n   */\n  PROCESS_EXECUTABLE_PATH: 'process.executable.path';\n\n  /**\n   * The command used to launch the process (i.e. the command name). On Linux based systems, can be set to the zeroth string in `proc/[pid]/cmdline`. On Windows, can be set to the first parameter extracted from `GetCommandLineW`.\n   */\n  PROCESS_COMMAND: 'process.command';\n\n  /**\n   * The full command used to launch the process as a single string representing the full command. On Windows, can be set to the result of `GetCommandLineW`. Do not set this if you have to assemble it just for monitoring; use `process.command_args` instead.\n   */\n  PROCESS_COMMAND_LINE: 'process.command_line';\n\n  /**\n   * All the command arguments (including the command/executable itself) as received by the process. On Linux-based systems (and some other Unixoid systems supporting procfs), can be set according to the list of null-delimited strings extracted from `proc/[pid]/cmdline`. For libc-based executables, this would be the full argv vector passed to `main`.\n   */\n  PROCESS_COMMAND_ARGS: 'process.command_args';\n\n  /**\n   * The username of the user that owns the process.\n   */\n  PROCESS_OWNER: 'process.owner';\n\n  /**\n   * The name of the runtime of this process. For compiled native binaries, this SHOULD be the name of the compiler.\n   */\n  PROCESS_RUNTIME_NAME: 'process.runtime.name';\n\n  /**\n   * The version of the runtime of this process, as returned by the runtime without modification.\n   */\n  PROCESS_RUNTIME_VERSION: 'process.runtime.version';\n\n  /**\n   * An additional description about the runtime of the process, for example a specific vendor customization of the runtime environment.\n   */\n  PROCESS_RUNTIME_DESCRIPTION: 'process.runtime.description';\n\n  /**\n   * Logical name of the service.\n   *\n   * Note: MUST be the same for all instances of horizontally scaled services. If the value was not specified, SDKs MUST fallback to `unknown_service:` concatenated with [`process.executable.name`](process.md#process), e.g. `unknown_service:bash`. If `process.executable.name` is not available, the value MUST be set to `unknown_service`.\n   */\n  SERVICE_NAME: 'service.name';\n\n  /**\n   * A namespace for `service.name`.\n   *\n   * Note: A string value having a meaning that helps to distinguish a group of services, for example the team name that owns a group of services. `service.name` is expected to be unique within the same namespace. If `service.namespace` is not specified in the Resource then `service.name` is expected to be unique for all services that have no explicit namespace defined (so the empty/unspecified namespace is simply one more valid namespace). Zero-length namespace string is assumed equal to unspecified namespace.\n   */\n  SERVICE_NAMESPACE: 'service.namespace';\n\n  /**\n   * The string ID of the service instance.\n   *\n   * Note: MUST be unique for each instance of the same `service.namespace,service.name` pair (in other words `service.namespace,service.name,service.instance.id` triplet MUST be globally unique). The ID helps to distinguish instances of the same service that exist at the same time (e.g. instances of a horizontally scaled service). It is preferable for the ID to be persistent and stay the same for the lifetime of the service instance, however it is acceptable that the ID is ephemeral and changes during important lifetime events for the service (e.g. service restarts). If the service has no inherent unique ID that can be used as the value of this attribute it is recommended to generate a random Version 1 or Version 4 RFC 4122 UUID (services aiming for reproducible UUIDs may also use Version 5, see RFC 4122 for more recommendations).\n   */\n  SERVICE_INSTANCE_ID: 'service.instance.id';\n\n  /**\n   * The version string of the service API or implementation.\n   */\n  SERVICE_VERSION: 'service.version';\n\n  /**\n   * The name of the telemetry SDK as defined above.\n   */\n  TELEMETRY_SDK_NAME: 'telemetry.sdk.name';\n\n  /**\n   * The language of the telemetry SDK.\n   */\n  TELEMETRY_SDK_LANGUAGE: 'telemetry.sdk.language';\n\n  /**\n   * The version string of the telemetry SDK.\n   */\n  TELEMETRY_SDK_VERSION: 'telemetry.sdk.version';\n\n  /**\n   * The version string of the auto instrumentation agent, if used.\n   */\n  TELEMETRY_AUTO_VERSION: 'telemetry.auto.version';\n\n  /**\n   * The name of the web engine.\n   */\n  WEBENGINE_NAME: 'webengine.name';\n\n  /**\n   * The version of the web engine.\n   */\n  WEBENGINE_VERSION: 'webengine.version';\n\n  /**\n   * Additional description of the web engine (e.g. detailed version and edition information).\n   */\n  WEBENGINE_DESCRIPTION: 'webengine.description';\n};\n\n/**\n * Create exported Value Map for SemanticResourceAttributes values\n * @deprecated Use the SEMRESATTRS_XXXXX constants rather than the SemanticResourceAttributes.XXXXX for bundle minification\n */\nexport const SemanticResourceAttributes: SemanticResourceAttributes =\n  /*#__PURE__*/ createConstMap<SemanticResourceAttributes>([\n    TMP_CLOUD_PROVIDER,\n    TMP_CLOUD_ACCOUNT_ID,\n    TMP_CLOUD_REGION,\n    TMP_CLOUD_AVAILABILITY_ZONE,\n    TMP_CLOUD_PLATFORM,\n    TMP_AWS_ECS_CONTAINER_ARN,\n    TMP_AWS_ECS_CLUSTER_ARN,\n    TMP_AWS_ECS_LAUNCHTYPE,\n    TMP_AWS_ECS_TASK_ARN,\n    TMP_AWS_ECS_TASK_FAMILY,\n    TMP_AWS_ECS_TASK_REVISION,\n    TMP_AWS_EKS_CLUSTER_ARN,\n    TMP_AWS_LOG_GROUP_NAMES,\n    TMP_AWS_LOG_GROUP_ARNS,\n    TMP_AWS_LOG_STREAM_NAMES,\n    TMP_AWS_LOG_STREAM_ARNS,\n    TMP_CONTAINER_NAME,\n    TMP_CONTAINER_ID,\n    TMP_CONTAINER_RUNTIME,\n    TMP_CONTAINER_IMAGE_NAME,\n    TMP_CONTAINER_IMAGE_TAG,\n    TMP_DEPLOYMENT_ENVIRONMENT,\n    TMP_DEVICE_ID,\n    TMP_DEVICE_MODEL_IDENTIFIER,\n    TMP_DEVICE_MODEL_NAME,\n    TMP_FAAS_NAME,\n    TMP_FAAS_ID,\n    TMP_FAAS_VERSION,\n    TMP_FAAS_INSTANCE,\n    TMP_FAAS_MAX_MEMORY,\n    TMP_HOST_ID,\n    TMP_HOST_NAME,\n    TMP_HOST_TYPE,\n    TMP_HOST_ARCH,\n    TMP_HOST_IMAGE_NAME,\n    TMP_HOST_IMAGE_ID,\n    TMP_HOST_IMAGE_VERSION,\n    TMP_K8S_CLUSTER_NAME,\n    TMP_K8S_NODE_NAME,\n    TMP_K8S_NODE_UID,\n    TMP_K8S_NAMESPACE_NAME,\n    TMP_K8S_POD_UID,\n    TMP_K8S_POD_NAME,\n    TMP_K8S_CONTAINER_NAME,\n    TMP_K8S_REPLICASET_UID,\n    TMP_K8S_REPLICASET_NAME,\n    TMP_K8S_DEPLOYMENT_UID,\n    TMP_K8S_DEPLOYMENT_NAME,\n    TMP_K8S_STATEFULSET_UID,\n    TMP_K8S_STATEFULSET_NAME,\n    TMP_K8S_DAEMONSET_UID,\n    TMP_K8S_DAEMONSET_NAME,\n    TMP_K8S_JOB_UID,\n    TMP_K8S_JOB_NAME,\n    TMP_K8S_CRONJOB_UID,\n    TMP_K8S_CRONJOB_NAME,\n    TMP_OS_TYPE,\n    TMP_OS_DESCRIPTION,\n    TMP_OS_NAME,\n    TMP_OS_VERSION,\n    TMP_PROCESS_PID,\n    TMP_PROCESS_EXECUTABLE_NAME,\n    TMP_PROCESS_EXECUTABLE_PATH,\n    TMP_PROCESS_COMMAND,\n    TMP_PROCESS_COMMAND_LINE,\n    TMP_PROCESS_COMMAND_ARGS,\n    TMP_PROCESS_OWNER,\n    TMP_PROCESS_RUNTIME_NAME,\n    TMP_PROCESS_RUNTIME_VERSION,\n    TMP_PROCESS_RUNTIME_DESCRIPTION,\n    TMP_SERVICE_NAME,\n    TMP_SERVICE_NAMESPACE,\n    TMP_SERVICE_INSTANCE_ID,\n    TMP_SERVICE_VERSION,\n    TMP_TELEMETRY_SDK_NAME,\n    TMP_TELEMETRY_SDK_LANGUAGE,\n    TMP_TELEMETRY_SDK_VERSION,\n    TMP_TELEMETRY_AUTO_VERSION,\n    TMP_WEBENGINE_NAME,\n    TMP_WEBENGINE_VERSION,\n    TMP_WEBENGINE_DESCRIPTION,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for CloudProviderValues enum definition\n *\n * Name of the cloud provider.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_CLOUDPROVIDERVALUES_ALIBABA_CLOUD = 'alibaba_cloud';\nconst TMP_CLOUDPROVIDERVALUES_AWS = 'aws';\nconst TMP_CLOUDPROVIDERVALUES_AZURE = 'azure';\nconst TMP_CLOUDPROVIDERVALUES_GCP = 'gcp';\n\n/**\n * Name of the cloud provider.\n *\n * @deprecated Use CLOUD_PROVIDER_VALUE_ALIBABA_CLOUD in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPROVIDERVALUES_ALIBABA_CLOUD =\n  TMP_CLOUDPROVIDERVALUES_ALIBABA_CLOUD;\n\n/**\n * Name of the cloud provider.\n *\n * @deprecated Use CLOUD_PROVIDER_VALUE_AWS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPROVIDERVALUES_AWS = TMP_CLOUDPROVIDERVALUES_AWS;\n\n/**\n * Name of the cloud provider.\n *\n * @deprecated Use CLOUD_PROVIDER_VALUE_AZURE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPROVIDERVALUES_AZURE = TMP_CLOUDPROVIDERVALUES_AZURE;\n\n/**\n * Name of the cloud provider.\n *\n * @deprecated Use CLOUD_PROVIDER_VALUE_GCP in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPROVIDERVALUES_GCP = TMP_CLOUDPROVIDERVALUES_GCP;\n\n/**\n * Identifies the Values for CloudProviderValues enum definition\n *\n * Name of the cloud provider.\n * @deprecated Use the CLOUDPROVIDERVALUES_XXXXX constants rather than the CloudProviderValues.XXXXX for bundle minification.\n */\nexport type CloudProviderValues = {\n  /** Alibaba Cloud. */\n  ALIBABA_CLOUD: 'alibaba_cloud';\n\n  /** Amazon Web Services. */\n  AWS: 'aws';\n\n  /** Microsoft Azure. */\n  AZURE: 'azure';\n\n  /** Google Cloud Platform. */\n  GCP: 'gcp';\n};\n\n/**\n * The constant map of values for CloudProviderValues.\n * @deprecated Use the CLOUDPROVIDERVALUES_XXXXX constants rather than the CloudProviderValues.XXXXX for bundle minification.\n */\nexport const CloudProviderValues: CloudProviderValues =\n  /*#__PURE__*/ createConstMap<CloudProviderValues>([\n    TMP_CLOUDPROVIDERVALUES_ALIBABA_CLOUD,\n    TMP_CLOUDPROVIDERVALUES_AWS,\n    TMP_CLOUDPROVIDERVALUES_AZURE,\n    TMP_CLOUDPROVIDERVALUES_GCP,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for CloudPlatformValues enum definition\n *\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS = 'alibaba_cloud_ecs';\nconst TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC = 'alibaba_cloud_fc';\nconst TMP_CLOUDPLATFORMVALUES_AWS_EC2 = 'aws_ec2';\nconst TMP_CLOUDPLATFORMVALUES_AWS_ECS = 'aws_ecs';\nconst TMP_CLOUDPLATFORMVALUES_AWS_EKS = 'aws_eks';\nconst TMP_CLOUDPLATFORMVALUES_AWS_LAMBDA = 'aws_lambda';\nconst TMP_CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK = 'aws_elastic_beanstalk';\nconst TMP_CLOUDPLATFORMVALUES_AZURE_VM = 'azure_vm';\nconst TMP_CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES =\n  'azure_container_instances';\nconst TMP_CLOUDPLATFORMVALUES_AZURE_AKS = 'azure_aks';\nconst TMP_CLOUDPLATFORMVALUES_AZURE_FUNCTIONS = 'azure_functions';\nconst TMP_CLOUDPLATFORMVALUES_AZURE_APP_SERVICE = 'azure_app_service';\nconst TMP_CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE = 'gcp_compute_engine';\nconst TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_RUN = 'gcp_cloud_run';\nconst TMP_CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE = 'gcp_kubernetes_engine';\nconst TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS = 'gcp_cloud_functions';\nconst TMP_CLOUDPLATFORMVALUES_GCP_APP_ENGINE = 'gcp_app_engine';\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_ECS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS =\n  TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_FC in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC =\n  TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AWS_EC2 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AWS_EC2 = TMP_CLOUDPLATFORMVALUES_AWS_EC2;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AWS_ECS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AWS_ECS = TMP_CLOUDPLATFORMVALUES_AWS_ECS;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AWS_EKS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AWS_EKS = TMP_CLOUDPLATFORMVALUES_AWS_EKS;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AWS_LAMBDA in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AWS_LAMBDA =\n  TMP_CLOUDPLATFORMVALUES_AWS_LAMBDA;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AWS_ELASTIC_BEANSTALK in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK =\n  TMP_CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AZURE_VM in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AZURE_VM = TMP_CLOUDPLATFORMVALUES_AZURE_VM;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AZURE_CONTAINER_INSTANCES in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES =\n  TMP_CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AZURE_AKS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AZURE_AKS = TMP_CLOUDPLATFORMVALUES_AZURE_AKS;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AZURE_FUNCTIONS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AZURE_FUNCTIONS =\n  TMP_CLOUDPLATFORMVALUES_AZURE_FUNCTIONS;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_AZURE_APP_SERVICE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_AZURE_APP_SERVICE =\n  TMP_CLOUDPLATFORMVALUES_AZURE_APP_SERVICE;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_GCP_COMPUTE_ENGINE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE =\n  TMP_CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_GCP_CLOUD_RUN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_GCP_CLOUD_RUN =\n  TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_RUN;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_GCP_KUBERNETES_ENGINE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE =\n  TMP_CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_GCP_CLOUD_FUNCTIONS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS =\n  TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS;\n\n/**\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n *\n * @deprecated Use CLOUD_PLATFORM_VALUE_GCP_APP_ENGINE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const CLOUDPLATFORMVALUES_GCP_APP_ENGINE =\n  TMP_CLOUDPLATFORMVALUES_GCP_APP_ENGINE;\n\n/**\n * Identifies the Values for CloudPlatformValues enum definition\n *\n * The cloud platform in use.\n *\n * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.\n * @deprecated Use the CLOUDPLATFORMVALUES_XXXXX constants rather than the CloudPlatformValues.XXXXX for bundle minification.\n */\nexport type CloudPlatformValues = {\n  /** Alibaba Cloud Elastic Compute Service. */\n  ALIBABA_CLOUD_ECS: 'alibaba_cloud_ecs';\n\n  /** Alibaba Cloud Function Compute. */\n  ALIBABA_CLOUD_FC: 'alibaba_cloud_fc';\n\n  /** AWS Elastic Compute Cloud. */\n  AWS_EC2: 'aws_ec2';\n\n  /** AWS Elastic Container Service. */\n  AWS_ECS: 'aws_ecs';\n\n  /** AWS Elastic Kubernetes Service. */\n  AWS_EKS: 'aws_eks';\n\n  /** AWS Lambda. */\n  AWS_LAMBDA: 'aws_lambda';\n\n  /** AWS Elastic Beanstalk. */\n  AWS_ELASTIC_BEANSTALK: 'aws_elastic_beanstalk';\n\n  /** Azure Virtual Machines. */\n  AZURE_VM: 'azure_vm';\n\n  /** Azure Container Instances. */\n  AZURE_CONTAINER_INSTANCES: 'azure_container_instances';\n\n  /** Azure Kubernetes Service. */\n  AZURE_AKS: 'azure_aks';\n\n  /** Azure Functions. */\n  AZURE_FUNCTIONS: 'azure_functions';\n\n  /** Azure App Service. */\n  AZURE_APP_SERVICE: 'azure_app_service';\n\n  /** Google Cloud Compute Engine (GCE). */\n  GCP_COMPUTE_ENGINE: 'gcp_compute_engine';\n\n  /** Google Cloud Run. */\n  GCP_CLOUD_RUN: 'gcp_cloud_run';\n\n  /** Google Cloud Kubernetes Engine (GKE). */\n  GCP_KUBERNETES_ENGINE: 'gcp_kubernetes_engine';\n\n  /** Google Cloud Functions (GCF). */\n  GCP_CLOUD_FUNCTIONS: 'gcp_cloud_functions';\n\n  /** Google Cloud App Engine (GAE). */\n  GCP_APP_ENGINE: 'gcp_app_engine';\n};\n\n/**\n * The constant map of values for CloudPlatformValues.\n * @deprecated Use the CLOUDPLATFORMVALUES_XXXXX constants rather than the CloudPlatformValues.XXXXX for bundle minification.\n */\nexport const CloudPlatformValues: CloudPlatformValues =\n  /*#__PURE__*/ createConstMap<CloudPlatformValues>([\n    TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS,\n    TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC,\n    TMP_CLOUDPLATFORMVALUES_AWS_EC2,\n    TMP_CLOUDPLATFORMVALUES_AWS_ECS,\n    TMP_CLOUDPLATFORMVALUES_AWS_EKS,\n    TMP_CLOUDPLATFORMVALUES_AWS_LAMBDA,\n    TMP_CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK,\n    TMP_CLOUDPLATFORMVALUES_AZURE_VM,\n    TMP_CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES,\n    TMP_CLOUDPLATFORMVALUES_AZURE_AKS,\n    TMP_CLOUDPLATFORMVALUES_AZURE_FUNCTIONS,\n    TMP_CLOUDPLATFORMVALUES_AZURE_APP_SERVICE,\n    TMP_CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE,\n    TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_RUN,\n    TMP_CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE,\n    TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS,\n    TMP_CLOUDPLATFORMVALUES_GCP_APP_ENGINE,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for AwsEcsLaunchtypeValues enum definition\n *\n * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_AWSECSLAUNCHTYPEVALUES_EC2 = 'ec2';\nconst TMP_AWSECSLAUNCHTYPEVALUES_FARGATE = 'fargate';\n\n/**\n * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.\n *\n * @deprecated Use AWS_ECS_LAUNCHTYPE_VALUE_EC2 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const AWSECSLAUNCHTYPEVALUES_EC2 = TMP_AWSECSLAUNCHTYPEVALUES_EC2;\n\n/**\n * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.\n *\n * @deprecated Use AWS_ECS_LAUNCHTYPE_VALUE_FARGATE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const AWSECSLAUNCHTYPEVALUES_FARGATE =\n  TMP_AWSECSLAUNCHTYPEVALUES_FARGATE;\n\n/**\n * Identifies the Values for AwsEcsLaunchtypeValues enum definition\n *\n * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.\n * @deprecated Use the AWSECSLAUNCHTYPEVALUES_XXXXX constants rather than the AwsEcsLaunchtypeValues.XXXXX for bundle minification.\n */\nexport type AwsEcsLaunchtypeValues = {\n  /** ec2. */\n  EC2: 'ec2';\n\n  /** fargate. */\n  FARGATE: 'fargate';\n};\n\n/**\n * The constant map of values for AwsEcsLaunchtypeValues.\n * @deprecated Use the AWSECSLAUNCHTYPEVALUES_XXXXX constants rather than the AwsEcsLaunchtypeValues.XXXXX for bundle minification.\n */\nexport const AwsEcsLaunchtypeValues: AwsEcsLaunchtypeValues =\n  /*#__PURE__*/ createConstMap<AwsEcsLaunchtypeValues>([\n    TMP_AWSECSLAUNCHTYPEVALUES_EC2,\n    TMP_AWSECSLAUNCHTYPEVALUES_FARGATE,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for HostArchValues enum definition\n *\n * The CPU architecture the host system is running on.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_HOSTARCHVALUES_AMD64 = 'amd64';\nconst TMP_HOSTARCHVALUES_ARM32 = 'arm32';\nconst TMP_HOSTARCHVALUES_ARM64 = 'arm64';\nconst TMP_HOSTARCHVALUES_IA64 = 'ia64';\nconst TMP_HOSTARCHVALUES_PPC32 = 'ppc32';\nconst TMP_HOSTARCHVALUES_PPC64 = 'ppc64';\nconst TMP_HOSTARCHVALUES_X86 = 'x86';\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use HOST_ARCH_VALUE_AMD64 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HOSTARCHVALUES_AMD64 = TMP_HOSTARCHVALUES_AMD64;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use HOST_ARCH_VALUE_ARM32 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HOSTARCHVALUES_ARM32 = TMP_HOSTARCHVALUES_ARM32;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use HOST_ARCH_VALUE_ARM64 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HOSTARCHVALUES_ARM64 = TMP_HOSTARCHVALUES_ARM64;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use HOST_ARCH_VALUE_IA64 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HOSTARCHVALUES_IA64 = TMP_HOSTARCHVALUES_IA64;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use HOST_ARCH_VALUE_PPC32 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HOSTARCHVALUES_PPC32 = TMP_HOSTARCHVALUES_PPC32;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use HOST_ARCH_VALUE_PPC64 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HOSTARCHVALUES_PPC64 = TMP_HOSTARCHVALUES_PPC64;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @deprecated Use HOST_ARCH_VALUE_X86 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HOSTARCHVALUES_X86 = TMP_HOSTARCHVALUES_X86;\n\n/**\n * Identifies the Values for HostArchValues enum definition\n *\n * The CPU architecture the host system is running on.\n * @deprecated Use the HOSTARCHVALUES_XXXXX constants rather than the HostArchValues.XXXXX for bundle minification.\n */\nexport type HostArchValues = {\n  /** AMD64. */\n  AMD64: 'amd64';\n\n  /** ARM32. */\n  ARM32: 'arm32';\n\n  /** ARM64. */\n  ARM64: 'arm64';\n\n  /** Itanium. */\n  IA64: 'ia64';\n\n  /** 32-bit PowerPC. */\n  PPC32: 'ppc32';\n\n  /** 64-bit PowerPC. */\n  PPC64: 'ppc64';\n\n  /** 32-bit x86. */\n  X86: 'x86';\n};\n\n/**\n * The constant map of values for HostArchValues.\n * @deprecated Use the HOSTARCHVALUES_XXXXX constants rather than the HostArchValues.XXXXX for bundle minification.\n */\nexport const HostArchValues: HostArchValues =\n  /*#__PURE__*/ createConstMap<HostArchValues>([\n    TMP_HOSTARCHVALUES_AMD64,\n    TMP_HOSTARCHVALUES_ARM32,\n    TMP_HOSTARCHVALUES_ARM64,\n    TMP_HOSTARCHVALUES_IA64,\n    TMP_HOSTARCHVALUES_PPC32,\n    TMP_HOSTARCHVALUES_PPC64,\n    TMP_HOSTARCHVALUES_X86,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for OsTypeValues enum definition\n *\n * The operating system type.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_OSTYPEVALUES_WINDOWS = 'windows';\nconst TMP_OSTYPEVALUES_LINUX = 'linux';\nconst TMP_OSTYPEVALUES_DARWIN = 'darwin';\nconst TMP_OSTYPEVALUES_FREEBSD = 'freebsd';\nconst TMP_OSTYPEVALUES_NETBSD = 'netbsd';\nconst TMP_OSTYPEVALUES_OPENBSD = 'openbsd';\nconst TMP_OSTYPEVALUES_DRAGONFLYBSD = 'dragonflybsd';\nconst TMP_OSTYPEVALUES_HPUX = 'hpux';\nconst TMP_OSTYPEVALUES_AIX = 'aix';\nconst TMP_OSTYPEVALUES_SOLARIS = 'solaris';\nconst TMP_OSTYPEVALUES_Z_OS = 'z_os';\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_WINDOWS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_WINDOWS = TMP_OSTYPEVALUES_WINDOWS;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_LINUX in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_LINUX = TMP_OSTYPEVALUES_LINUX;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_DARWIN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_DARWIN = TMP_OSTYPEVALUES_DARWIN;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_FREEBSD in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_FREEBSD = TMP_OSTYPEVALUES_FREEBSD;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_NETBSD in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_NETBSD = TMP_OSTYPEVALUES_NETBSD;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_OPENBSD in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_OPENBSD = TMP_OSTYPEVALUES_OPENBSD;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_DRAGONFLYBSD in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_DRAGONFLYBSD = TMP_OSTYPEVALUES_DRAGONFLYBSD;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_HPUX in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_HPUX = TMP_OSTYPEVALUES_HPUX;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_AIX in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_AIX = TMP_OSTYPEVALUES_AIX;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_SOLARIS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_SOLARIS = TMP_OSTYPEVALUES_SOLARIS;\n\n/**\n * The operating system type.\n *\n * @deprecated Use OS_TYPE_VALUE_Z_OS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const OSTYPEVALUES_Z_OS = TMP_OSTYPEVALUES_Z_OS;\n\n/**\n * Identifies the Values for OsTypeValues enum definition\n *\n * The operating system type.\n * @deprecated Use the OSTYPEVALUES_XXXXX constants rather than the OsTypeValues.XXXXX for bundle minification.\n */\nexport type OsTypeValues = {\n  /** Microsoft Windows. */\n  WINDOWS: 'windows';\n\n  /** Linux. */\n  LINUX: 'linux';\n\n  /** Apple Darwin. */\n  DARWIN: 'darwin';\n\n  /** FreeBSD. */\n  FREEBSD: 'freebsd';\n\n  /** NetBSD. */\n  NETBSD: 'netbsd';\n\n  /** OpenBSD. */\n  OPENBSD: 'openbsd';\n\n  /** DragonFly BSD. */\n  DRAGONFLYBSD: 'dragonflybsd';\n\n  /** HP-UX (Hewlett Packard Unix). */\n  HPUX: 'hpux';\n\n  /** AIX (Advanced Interactive eXecutive). */\n  AIX: 'aix';\n\n  /** Oracle Solaris. */\n  SOLARIS: 'solaris';\n\n  /** IBM z/OS. */\n  Z_OS: 'z_os';\n};\n\n/**\n * The constant map of values for OsTypeValues.\n * @deprecated Use the OSTYPEVALUES_XXXXX constants rather than the OsTypeValues.XXXXX for bundle minification.\n */\nexport const OsTypeValues: OsTypeValues =\n  /*#__PURE__*/ createConstMap<OsTypeValues>([\n    TMP_OSTYPEVALUES_WINDOWS,\n    TMP_OSTYPEVALUES_LINUX,\n    TMP_OSTYPEVALUES_DARWIN,\n    TMP_OSTYPEVALUES_FREEBSD,\n    TMP_OSTYPEVALUES_NETBSD,\n    TMP_OSTYPEVALUES_OPENBSD,\n    TMP_OSTYPEVALUES_DRAGONFLYBSD,\n    TMP_OSTYPEVALUES_HPUX,\n    TMP_OSTYPEVALUES_AIX,\n    TMP_OSTYPEVALUES_SOLARIS,\n    TMP_OSTYPEVALUES_Z_OS,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for TelemetrySdkLanguageValues enum definition\n *\n * The language of the telemetry SDK.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_CPP = 'cpp';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_DOTNET = 'dotnet';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_ERLANG = 'erlang';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_GO = 'go';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_JAVA = 'java';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_NODEJS = 'nodejs';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_PHP = 'php';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_PYTHON = 'python';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_RUBY = 'ruby';\nconst TMP_TELEMETRYSDKLANGUAGEVALUES_WEBJS = 'webjs';\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_CPP.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_CPP =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_CPP;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_DOTNET.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_DOTNET =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_DOTNET;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_ERLANG.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_ERLANG =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_ERLANG;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_GO.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_GO = TMP_TELEMETRYSDKLANGUAGEVALUES_GO;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_JAVA.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_JAVA =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_JAVA;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_NODEJS.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_NODEJS =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_NODEJS;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_PHP.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_PHP =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_PHP;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_PYTHON.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_PYTHON =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_PYTHON;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_RUBY.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_RUBY =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_RUBY;\n\n/**\n * The language of the telemetry SDK.\n *\n * @deprecated Use TELEMETRY_SDK_LANGUAGE_VALUE_WEBJS.\n */\nexport const TELEMETRYSDKLANGUAGEVALUES_WEBJS =\n  TMP_TELEMETRYSDKLANGUAGEVALUES_WEBJS;\n\n/**\n * Identifies the Values for TelemetrySdkLanguageValues enum definition\n *\n * The language of the telemetry SDK.\n * @deprecated Use the TELEMETRYSDKLANGUAGEVALUES_XXXXX constants rather than the TelemetrySdkLanguageValues.XXXXX for bundle minification.\n */\nexport type TelemetrySdkLanguageValues = {\n  /** cpp. */\n  CPP: 'cpp';\n\n  /** dotnet. */\n  DOTNET: 'dotnet';\n\n  /** erlang. */\n  ERLANG: 'erlang';\n\n  /** go. */\n  GO: 'go';\n\n  /** java. */\n  JAVA: 'java';\n\n  /** nodejs. */\n  NODEJS: 'nodejs';\n\n  /** php. */\n  PHP: 'php';\n\n  /** python. */\n  PYTHON: 'python';\n\n  /** ruby. */\n  RUBY: 'ruby';\n\n  /** webjs. */\n  WEBJS: 'webjs';\n};\n\n/**\n * The constant map of values for TelemetrySdkLanguageValues.\n * @deprecated Use the TELEMETRYSDKLANGUAGEVALUES_XXXXX constants rather than the TelemetrySdkLanguageValues.XXXXX for bundle minification.\n */\nexport const TelemetrySdkLanguageValues: TelemetrySdkLanguageValues =\n  /*#__PURE__*/ createConstMap<TelemetrySdkLanguageValues>([\n    TMP_TELEMETRYSDKLANGUAGEVALUES_CPP,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_DOTNET,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_ERLANG,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_GO,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_JAVA,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_NODEJS,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_PHP,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_PYTHON,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_RUBY,\n    TMP_TELEMETRYSDKLANGUAGEVALUES_WEBJS,\n  ]);\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;;AAEnD,4GAA4G;AAC5G,iHAAiH;AACjH,4GAA4G;AAE5G,4GAA4G;AAC5G,iDAAiD;AACjD,4GAA4G;AAE5G,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,oBAAoB,GAAG,kBAAkB,CAAC;AAChD,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAC1D,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,oBAAoB,GAAG,kBAAkB,CAAC;AAChD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAC1D,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAClD,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,0BAA0B,GAAG,wBAAwB,CAAC;AAC5D,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAClD,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,WAAW,GAAG,SAAS,CAAC;AAC9B,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAC9C,IAAM,WAAW,GAAG,SAAS,CAAC;AAC9B,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAC9C,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,oBAAoB,GAAG,kBAAkB,CAAC;AAChD,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAClD,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAC9C,IAAM,oBAAoB,GAAG,kBAAkB,CAAC;AAChD,IAAM,WAAW,GAAG,SAAS,CAAC;AAC9B,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,WAAW,GAAG,SAAS,CAAC;AAC9B,IAAM,cAAc,GAAG,YAAY,CAAC;AACpC,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAC9C,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,+BAA+B,GAAG,6BAA6B,CAAC;AACtE,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAClD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAC9C,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,0BAA0B,GAAG,wBAAwB,CAAC;AAC5D,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAC1D,IAAM,0BAA0B,GAAG,wBAAwB,CAAC;AAC5D,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAClD,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAOnD,IAAM,0BAA0B,GAAG,kBAAkB,CAAC;AAOtD,IAAM,4BAA4B,GAAG,oBAAoB,CAAC;AAO1D,IAAM,wBAAwB,GAAG,gBAAgB,CAAC;AASlD,IAAM,mCAAmC,GAAG,2BAA2B,CAAC;AASxE,IAAM,0BAA0B,GAAG,kBAAkB,CAAC;AAOtD,IAAM,iCAAiC,GAAG,yBAAyB,CAAC;AAOpE,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAOhE,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAO9D,IAAM,4BAA4B,GAAG,oBAAoB,CAAC;AAO1D,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAOhE,IAAM,iCAAiC,GAAG,yBAAyB,CAAC;AAOpE,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAShE,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAShE,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAO9D,IAAM,gCAAgC,GAAG,wBAAwB,CAAC;AASlE,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAOhE,IAAM,0BAA0B,GAAG,kBAAkB,CAAC;AAOtD,IAAM,wBAAwB,GAAG,gBAAgB,CAAC;AAOlD,IAAM,6BAA6B,GAAG,qBAAqB,CAAC;AAO5D,IAAM,gCAAgC,GAAG,wBAAwB,CAAC;AAOlE,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAOhE,IAAM,kCAAkC,GAAG,0BAA0B,CAAC;AAStE,IAAM,qBAAqB,GAAG,aAAa,CAAC;AAS5C,IAAM,mCAAmC,GAAG,2BAA2B,CAAC;AASxE,IAAM,6BAA6B,GAAG,qBAAqB,CAAC;AAS5D,IAAM,qBAAqB,GAAG,aAAa,CAAC;AAsB5C,IAAM,mBAAmB,GAAG,WAAW,CAAC;AAiBxC,IAAM,wBAAwB,GAAG,gBAAgB,CAAC;AASlD,IAAM,yBAAyB,GAAG,iBAAiB,CAAC;AASpD,IAAM,2BAA2B,GAAG,mBAAmB,CAAC;AAOxD,IAAM,mBAAmB,GAAG,WAAW,CAAC;AAOxC,IAAM,qBAAqB,GAAG,aAAa,CAAC;AAO5C,IAAM,qBAAqB,GAAG,aAAa,CAAC;AAO5C,IAAM,qBAAqB,GAAG,aAAa,CAAC;AAO5C,IAAM,2BAA2B,GAAG,mBAAmB,CAAC;AAOxD,IAAM,yBAAyB,GAAG,iBAAiB,CAAC;AAOpD,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAO9D,IAAM,4BAA4B,GAAG,oBAAoB,CAAC;AAO1D,IAAM,yBAAyB,GAAG,iBAAiB,CAAC;AAOpD,IAAM,wBAAwB,GAAG,gBAAgB,CAAC;AAOlD,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAO9D,IAAM,uBAAuB,GAAG,eAAe,CAAC;AAOhD,IAAM,wBAAwB,GAAG,gBAAgB,CAAC;AAOlD,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAO9D,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAO9D,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAOhE,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAO9D,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAOhE,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAOhE,IAAM,gCAAgC,GAAG,wBAAwB,CAAC;AAOlE,IAAM,6BAA6B,GAAG,qBAAqB,CAAC;AAO5D,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAO9D,IAAM,uBAAuB,GAAG,eAAe,CAAC;AAOhD,IAAM,wBAAwB,GAAG,gBAAgB,CAAC;AAOlD,IAAM,2BAA2B,GAAG,mBAAmB,CAAC;AAOxD,IAAM,4BAA4B,GAAG,oBAAoB,CAAC;AAO1D,IAAM,mBAAmB,GAAG,WAAW,CAAC;AAOxC,IAAM,0BAA0B,GAAG,kBAAkB,CAAC;AAOtD,IAAM,mBAAmB,GAAG,WAAW,CAAC;AAOxC,IAAM,sBAAsB,GAAG,cAAc,CAAC;AAO9C,IAAM,uBAAuB,GAAG,eAAe,CAAC;AAOhD,IAAM,mCAAmC,GAAG,2BAA2B,CAAC;AAOxE,IAAM,mCAAmC,GAAG,2BAA2B,CAAC;AAOxE,IAAM,2BAA2B,GAAG,mBAAmB,CAAC;AAOxD,IAAM,gCAAgC,GAAG,wBAAwB,CAAC;AAOlE,IAAM,gCAAgC,GAAG,wBAAwB,CAAC;AAOlE,IAAM,yBAAyB,GAAG,iBAAiB,CAAC;AAOpD,IAAM,gCAAgC,GAAG,wBAAwB,CAAC;AAOlE,IAAM,mCAAmC,GAAG,2BAA2B,CAAC;AAOxE,IAAM,uCAAuC,GAClD,+BAA+B,CAAC;AAS3B,IAAM,wBAAwB,GAAG,gBAAgB,CAAC;AASlD,IAAM,6BAA6B,GAAG,qBAAqB,CAAC;AAS5D,IAAM,+BAA+B,GAAG,uBAAuB,CAAC;AAOhE,IAAM,2BAA2B,GAAG,mBAAmB,CAAC;AAOxD,IAAM,8BAA8B,GAAG,sBAAsB,CAAC;AAO9D,IAAM,kCAAkC,GAAG,0BAA0B,CAAC;AAOtE,IAAM,iCAAiC,GAAG,yBAAyB,CAAC;AAOpE,IAAM,kCAAkC,GAAG,0BAA0B,CAAC;AAOtE,IAAM,0BAA0B,GAAG,kBAAkB,CAAC;AAOtD,IAAM,6BAA6B,GAAG,qBAAqB,CAAC;AAO5D,IAAM,iCAAiC,GAAG,yBAAyB,CAAC;AAydpE,IAAM,0BAA0B,GACrC,WAAA,EAAa,yRAAC,iBAAA,AAAc,EAA6B;IACvD,kBAAkB;IAClB,oBAAoB;IACpB,gBAAgB;IAChB,2BAA2B;IAC3B,kBAAkB;IAClB,yBAAyB;IACzB,uBAAuB;IACvB,sBAAsB;IACtB,oBAAoB;IACpB,uBAAuB;IACvB,yBAAyB;IACzB,uBAAuB;IACvB,uBAAuB;IACvB,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,kBAAkB;IAClB,gBAAgB;IAChB,qBAAqB;IACrB,wBAAwB;IACxB,uBAAuB;IACvB,0BAA0B;IAC1B,aAAa;IACb,2BAA2B;IAC3B,qBAAqB;IACrB,aAAa;IACb,WAAW;IACX,gBAAgB;IAChB,iBAAiB;IACjB,mBAAmB;IACnB,WAAW;IACX,aAAa;IACb,aAAa;IACb,aAAa;IACb,mBAAmB;IACnB,iBAAiB;IACjB,sBAAsB;IACtB,oBAAoB;IACpB,iBAAiB;IACjB,gBAAgB;IAChB,sBAAsB;IACtB,eAAe;IACf,gBAAgB;IAChB,sBAAsB;IACtB,sBAAsB;IACtB,uBAAuB;IACvB,sBAAsB;IACtB,uBAAuB;IACvB,uBAAuB;IACvB,wBAAwB;IACxB,qBAAqB;IACrB,sBAAsB;IACtB,eAAe;IACf,gBAAgB;IAChB,mBAAmB;IACnB,oBAAoB;IACpB,WAAW;IACX,kBAAkB;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,2BAA2B;IAC3B,2BAA2B;IAC3B,mBAAmB;IACnB,wBAAwB;IACxB,wBAAwB;IACxB,iBAAiB;IACjB,wBAAwB;IACxB,2BAA2B;IAC3B,+BAA+B;IAC/B,gBAAgB;IAChB,qBAAqB;IACrB,uBAAuB;IACvB,mBAAmB;IACnB,sBAAsB;IACtB,0BAA0B;IAC1B,yBAAyB;IACzB,0BAA0B;IAC1B,kBAAkB;IAClB,qBAAqB;IACrB,yBAAyB;CAC1B,CAAC,CAAC;AAEL;;;;gHAIgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,qCAAqC,GAAG,eAAe,CAAC;AAC9D,IAAM,2BAA2B,GAAG,KAAK,CAAC;AAC1C,IAAM,6BAA6B,GAAG,OAAO,CAAC;AAC9C,IAAM,2BAA2B,GAAG,KAAK,CAAC;AAOnC,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAOjC,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAO5D,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAOhE,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AA0B5D,IAAM,mBAAmB,GAC9B,WAAA,EAAa,yRAAC,iBAAA,AAAc,EAAsB;IAChD,qCAAqC;IACrC,2BAA2B;IAC3B,6BAA6B;IAC7B,2BAA2B;CAC5B,CAAC,CAAC;AAEL;;;;;;gHAMgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,yCAAyC,GAAG,mBAAmB,CAAC;AACtE,IAAM,wCAAwC,GAAG,kBAAkB,CAAC;AACpE,IAAM,+BAA+B,GAAG,SAAS,CAAC;AAClD,IAAM,+BAA+B,GAAG,SAAS,CAAC;AAClD,IAAM,+BAA+B,GAAG,SAAS,CAAC;AAClD,IAAM,kCAAkC,GAAG,YAAY,CAAC;AACxD,IAAM,6CAA6C,GAAG,uBAAuB,CAAC;AAC9E,IAAM,gCAAgC,GAAG,UAAU,CAAC;AACpD,IAAM,iDAAiD,GACrD,2BAA2B,CAAC;AAC9B,IAAM,iCAAiC,GAAG,WAAW,CAAC;AACtD,IAAM,uCAAuC,GAAG,iBAAiB,CAAC;AAClE,IAAM,yCAAyC,GAAG,mBAAmB,CAAC;AACtE,IAAM,0CAA0C,GAAG,oBAAoB,CAAC;AACxE,IAAM,qCAAqC,GAAG,eAAe,CAAC;AAC9D,IAAM,6CAA6C,GAAG,uBAAuB,CAAC;AAC9E,IAAM,2CAA2C,GAAG,qBAAqB,CAAC;AAC1E,IAAM,sCAAsC,GAAG,gBAAgB,CAAC;AASzD,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AASrC,IAAM,oCAAoC,GAC/C,wCAAwC,CAAC;AASpC,IAAM,2BAA2B,GAAG,+BAA+B,CAAC;AASpE,IAAM,2BAA2B,GAAG,+BAA+B,CAAC;AASpE,IAAM,2BAA2B,GAAG,+BAA+B,CAAC;AASpE,IAAM,8BAA8B,GACzC,kCAAkC,CAAC;AAS9B,IAAM,yCAAyC,GACpD,6CAA6C,CAAC;AASzC,IAAM,4BAA4B,GAAG,gCAAgC,CAAC;AAStE,IAAM,6CAA6C,GACxD,iDAAiD,CAAC;AAS7C,IAAM,6BAA6B,GAAG,iCAAiC,CAAC;AASxE,IAAM,mCAAmC,GAC9C,uCAAuC,CAAC;AASnC,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AASrC,IAAM,sCAAsC,GACjD,0CAA0C,CAAC;AAStC,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AASjC,IAAM,yCAAyC,GACpD,6CAA6C,CAAC;AASzC,IAAM,uCAAuC,GAClD,2CAA2C,CAAC;AASvC,IAAM,kCAAkC,GAC7C,sCAAsC,CAAC;AAmElC,IAAM,mBAAmB,GAC9B,WAAA,EAAa,yRAAC,iBAAA,AAAc,EAAsB;IAChD,yCAAyC;IACzC,wCAAwC;IACxC,+BAA+B;IAC/B,+BAA+B;IAC/B,+BAA+B;IAC/B,kCAAkC;IAClC,6CAA6C;IAC7C,gCAAgC;IAChC,iDAAiD;IACjD,iCAAiC;IACjC,uCAAuC;IACvC,yCAAyC;IACzC,0CAA0C;IAC1C,qCAAqC;IACrC,6CAA6C;IAC7C,2CAA2C;IAC3C,sCAAsC;CACvC,CAAC,CAAC;AAEL;;;;gHAIgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,8BAA8B,GAAG,KAAK,CAAC;AAC7C,IAAM,kCAAkC,GAAG,SAAS,CAAC;AAO9C,IAAM,0BAA0B,GAAG,8BAA8B,CAAC;AAOlE,IAAM,8BAA8B,GACzC,kCAAkC,CAAC;AAoB9B,IAAM,sBAAsB,GACjC,WAAA,EAAa,yRAAC,iBAAA,AAAc,EAAyB;IACnD,8BAA8B;IAC9B,kCAAkC;CACnC,CAAC,CAAC;AAEL;;;;gHAIgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,uBAAuB,GAAG,MAAM,CAAC;AACvC,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,sBAAsB,GAAG,KAAK,CAAC;AAO9B,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,mBAAmB,GAAG,uBAAuB,CAAC;AAOpD,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,kBAAkB,GAAG,sBAAsB,CAAC;AAmClD,IAAM,cAAc,GACzB,WAAA,EAAa,yRAAC,iBAAA,AAAc,EAAiB;IAC3C,wBAAwB;IACxB,wBAAwB;IACxB,wBAAwB;IACxB,uBAAuB;IACvB,wBAAwB;IACxB,wBAAwB;IACxB,sBAAsB;CACvB,CAAC,CAAC;AAEL;;;;gHAIgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,wBAAwB,GAAG,SAAS,CAAC;AAC3C,IAAM,sBAAsB,GAAG,OAAO,CAAC;AACvC,IAAM,uBAAuB,GAAG,QAAQ,CAAC;AACzC,IAAM,wBAAwB,GAAG,SAAS,CAAC;AAC3C,IAAM,uBAAuB,GAAG,QAAQ,CAAC;AACzC,IAAM,wBAAwB,GAAG,SAAS,CAAC;AAC3C,IAAM,6BAA6B,GAAG,cAAc,CAAC;AACrD,IAAM,qBAAqB,GAAG,MAAM,CAAC;AACrC,IAAM,oBAAoB,GAAG,KAAK,CAAC;AACnC,IAAM,wBAAwB,GAAG,SAAS,CAAC;AAC3C,IAAM,qBAAqB,GAAG,MAAM,CAAC;AAO9B,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,kBAAkB,GAAG,sBAAsB,CAAC;AAOlD,IAAM,mBAAmB,GAAG,uBAAuB,CAAC;AAOpD,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,mBAAmB,GAAG,uBAAuB,CAAC;AAOpD,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAOhE,IAAM,iBAAiB,GAAG,qBAAqB,CAAC;AAOhD,IAAM,gBAAgB,GAAG,oBAAoB,CAAC;AAO9C,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,iBAAiB,GAAG,qBAAqB,CAAC;AA+ChD,IAAM,YAAY,GACvB,WAAA,EAAa,yRAAC,iBAAA,AAAc,EAAe;IACzC,wBAAwB;IACxB,sBAAsB;IACtB,uBAAuB;IACvB,wBAAwB;IACxB,uBAAuB;IACvB,wBAAwB;IACxB,6BAA6B;IAC7B,qBAAqB;IACrB,oBAAoB;IACpB,wBAAwB;IACxB,qBAAqB;CACtB,CAAC,CAAC;AAEL;;;;gHAIgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,kCAAkC,GAAG,KAAK,CAAC;AACjD,IAAM,qCAAqC,GAAG,QAAQ,CAAC;AACvD,IAAM,qCAAqC,GAAG,QAAQ,CAAC;AACvD,IAAM,iCAAiC,GAAG,IAAI,CAAC;AAC/C,IAAM,mCAAmC,GAAG,MAAM,CAAC;AACnD,IAAM,qCAAqC,GAAG,QAAQ,CAAC;AACvD,IAAM,kCAAkC,GAAG,KAAK,CAAC;AACjD,IAAM,qCAAqC,GAAG,QAAQ,CAAC;AACvD,IAAM,mCAAmC,GAAG,MAAM,CAAC;AACnD,IAAM,oCAAoC,GAAG,OAAO,CAAC;AAO9C,IAAM,8BAA8B,GACzC,kCAAkC,CAAC;AAO9B,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAOjC,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAOjC,IAAM,6BAA6B,GAAG,iCAAiC,CAAC;AAOxE,IAAM,+BAA+B,GAC1C,mCAAmC,CAAC;AAO/B,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAOjC,IAAM,8BAA8B,GACzC,kCAAkC,CAAC;AAO9B,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAOjC,IAAM,+BAA+B,GAC1C,mCAAmC,CAAC;AAO/B,IAAM,gCAAgC,GAC3C,oCAAoC,CAAC;AA4ChC,IAAM,0BAA0B,GACrC,WAAA,EAAa,yRAAC,iBAAA,AAAc,EAA6B;IACvD,kCAAkC;IAClC,qCAAqC;IACrC,qCAAqC;IACrC,iCAAiC;IACjC,mCAAmC;IACnC,qCAAqC;IACrC,kCAAkC;IAClC,qCAAqC;IACrC,mCAAmC;IACnC,oCAAoC;CACrC,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1323, "column": 0}, "map": {"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry/node/node_modules/%40opentelemetry/sdk-trace-base/node_modules/%40opentelemetry/semantic-conventions/src/internal/utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Creates a const map from the given values\n * @param values - An array of values to be used as keys and values in the map.\n * @returns A populated version of the map with the values and keys derived from the values.\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function createConstMap<T>(values: Array<T[keyof T]>): T {\n  // eslint-disable-next-line prefer-const, @typescript-eslint/no-explicit-any\n  let res: any = {};\n  const len = values.length;\n  for (let lp = 0; lp < len; lp++) {\n    const val = values[lp];\n    if (val) {\n      res[String(val).toUpperCase().replace(/[-.]/g, '_')] = val;\n    }\n  }\n\n  return res as T;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH;;;;GAIG,CACH,sBAAA,EAAwB;;;AAClB,SAAU,cAAc,CAAI,MAAyB;IACzD,4EAA4E;IAC5E,IAAI,GAAG,GAAQ,CAAA,CAAE,CAAC;IAClB,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;IAC1B,IAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,CAAE;QAC/B,IAAM,GAAG,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;QACvB,IAAI,GAAG,EAAE;YACP,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;SAC5D;KACF;IAED,OAAO,GAAQ,CAAC;AAClB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1362, "column": 0}, "map": {"version": 3, "file": "SemanticAttributes.js", "sourceRoot": "", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry/node/node_modules/%40opentelemetry/sdk-trace-base/node_modules/%40opentelemetry/semantic-conventions/src/trace/SemanticAttributes.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createConstMap } from '../internal/utils';\n\n//----------------------------------------------------------------------------------------------------------\n// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates//templates/SemanticAttributes.ts.j2\n//----------------------------------------------------------------------------------------------------------\n\n//----------------------------------------------------------------------------------------------------------\n// Constant values for SemanticAttributes\n//----------------------------------------------------------------------------------------------------------\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_AWS_LAMBDA_INVOKED_ARN = 'aws.lambda.invoked_arn';\nconst TMP_DB_SYSTEM = 'db.system';\nconst TMP_DB_CONNECTION_STRING = 'db.connection_string';\nconst TMP_DB_USER = 'db.user';\nconst TMP_DB_JDBC_DRIVER_CLASSNAME = 'db.jdbc.driver_classname';\nconst TMP_DB_NAME = 'db.name';\nconst TMP_DB_STATEMENT = 'db.statement';\nconst TMP_DB_OPERATION = 'db.operation';\nconst TMP_DB_MSSQL_INSTANCE_NAME = 'db.mssql.instance_name';\nconst TMP_DB_CASSANDRA_KEYSPACE = 'db.cassandra.keyspace';\nconst TMP_DB_CASSANDRA_PAGE_SIZE = 'db.cassandra.page_size';\nconst TMP_DB_CASSANDRA_CONSISTENCY_LEVEL = 'db.cassandra.consistency_level';\nconst TMP_DB_CASSANDRA_TABLE = 'db.cassandra.table';\nconst TMP_DB_CASSANDRA_IDEMPOTENCE = 'db.cassandra.idempotence';\nconst TMP_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT =\n  'db.cassandra.speculative_execution_count';\nconst TMP_DB_CASSANDRA_COORDINATOR_ID = 'db.cassandra.coordinator.id';\nconst TMP_DB_CASSANDRA_COORDINATOR_DC = 'db.cassandra.coordinator.dc';\nconst TMP_DB_HBASE_NAMESPACE = 'db.hbase.namespace';\nconst TMP_DB_REDIS_DATABASE_INDEX = 'db.redis.database_index';\nconst TMP_DB_MONGODB_COLLECTION = 'db.mongodb.collection';\nconst TMP_DB_SQL_TABLE = 'db.sql.table';\nconst TMP_EXCEPTION_TYPE = 'exception.type';\nconst TMP_EXCEPTION_MESSAGE = 'exception.message';\nconst TMP_EXCEPTION_STACKTRACE = 'exception.stacktrace';\nconst TMP_EXCEPTION_ESCAPED = 'exception.escaped';\nconst TMP_FAAS_TRIGGER = 'faas.trigger';\nconst TMP_FAAS_EXECUTION = 'faas.execution';\nconst TMP_FAAS_DOCUMENT_COLLECTION = 'faas.document.collection';\nconst TMP_FAAS_DOCUMENT_OPERATION = 'faas.document.operation';\nconst TMP_FAAS_DOCUMENT_TIME = 'faas.document.time';\nconst TMP_FAAS_DOCUMENT_NAME = 'faas.document.name';\nconst TMP_FAAS_TIME = 'faas.time';\nconst TMP_FAAS_CRON = 'faas.cron';\nconst TMP_FAAS_COLDSTART = 'faas.coldstart';\nconst TMP_FAAS_INVOKED_NAME = 'faas.invoked_name';\nconst TMP_FAAS_INVOKED_PROVIDER = 'faas.invoked_provider';\nconst TMP_FAAS_INVOKED_REGION = 'faas.invoked_region';\nconst TMP_NET_TRANSPORT = 'net.transport';\nconst TMP_NET_PEER_IP = 'net.peer.ip';\nconst TMP_NET_PEER_PORT = 'net.peer.port';\nconst TMP_NET_PEER_NAME = 'net.peer.name';\nconst TMP_NET_HOST_IP = 'net.host.ip';\nconst TMP_NET_HOST_PORT = 'net.host.port';\nconst TMP_NET_HOST_NAME = 'net.host.name';\nconst TMP_NET_HOST_CONNECTION_TYPE = 'net.host.connection.type';\nconst TMP_NET_HOST_CONNECTION_SUBTYPE = 'net.host.connection.subtype';\nconst TMP_NET_HOST_CARRIER_NAME = 'net.host.carrier.name';\nconst TMP_NET_HOST_CARRIER_MCC = 'net.host.carrier.mcc';\nconst TMP_NET_HOST_CARRIER_MNC = 'net.host.carrier.mnc';\nconst TMP_NET_HOST_CARRIER_ICC = 'net.host.carrier.icc';\nconst TMP_PEER_SERVICE = 'peer.service';\nconst TMP_ENDUSER_ID = 'enduser.id';\nconst TMP_ENDUSER_ROLE = 'enduser.role';\nconst TMP_ENDUSER_SCOPE = 'enduser.scope';\nconst TMP_THREAD_ID = 'thread.id';\nconst TMP_THREAD_NAME = 'thread.name';\nconst TMP_CODE_FUNCTION = 'code.function';\nconst TMP_CODE_NAMESPACE = 'code.namespace';\nconst TMP_CODE_FILEPATH = 'code.filepath';\nconst TMP_CODE_LINENO = 'code.lineno';\nconst TMP_HTTP_METHOD = 'http.method';\nconst TMP_HTTP_URL = 'http.url';\nconst TMP_HTTP_TARGET = 'http.target';\nconst TMP_HTTP_HOST = 'http.host';\nconst TMP_HTTP_SCHEME = 'http.scheme';\nconst TMP_HTTP_STATUS_CODE = 'http.status_code';\nconst TMP_HTTP_FLAVOR = 'http.flavor';\nconst TMP_HTTP_USER_AGENT = 'http.user_agent';\nconst TMP_HTTP_REQUEST_CONTENT_LENGTH = 'http.request_content_length';\nconst TMP_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED =\n  'http.request_content_length_uncompressed';\nconst TMP_HTTP_RESPONSE_CONTENT_LENGTH = 'http.response_content_length';\nconst TMP_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED =\n  'http.response_content_length_uncompressed';\nconst TMP_HTTP_SERVER_NAME = 'http.server_name';\nconst TMP_HTTP_ROUTE = 'http.route';\nconst TMP_HTTP_CLIENT_IP = 'http.client_ip';\nconst TMP_AWS_DYNAMODB_TABLE_NAMES = 'aws.dynamodb.table_names';\nconst TMP_AWS_DYNAMODB_CONSUMED_CAPACITY = 'aws.dynamodb.consumed_capacity';\nconst TMP_AWS_DYNAMODB_ITEM_COLLECTION_METRICS =\n  'aws.dynamodb.item_collection_metrics';\nconst TMP_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY =\n  'aws.dynamodb.provisioned_read_capacity';\nconst TMP_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY =\n  'aws.dynamodb.provisioned_write_capacity';\nconst TMP_AWS_DYNAMODB_CONSISTENT_READ = 'aws.dynamodb.consistent_read';\nconst TMP_AWS_DYNAMODB_PROJECTION = 'aws.dynamodb.projection';\nconst TMP_AWS_DYNAMODB_LIMIT = 'aws.dynamodb.limit';\nconst TMP_AWS_DYNAMODB_ATTRIBUTES_TO_GET = 'aws.dynamodb.attributes_to_get';\nconst TMP_AWS_DYNAMODB_INDEX_NAME = 'aws.dynamodb.index_name';\nconst TMP_AWS_DYNAMODB_SELECT = 'aws.dynamodb.select';\nconst TMP_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES =\n  'aws.dynamodb.global_secondary_indexes';\nconst TMP_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES =\n  'aws.dynamodb.local_secondary_indexes';\nconst TMP_AWS_DYNAMODB_EXCLUSIVE_START_TABLE =\n  'aws.dynamodb.exclusive_start_table';\nconst TMP_AWS_DYNAMODB_TABLE_COUNT = 'aws.dynamodb.table_count';\nconst TMP_AWS_DYNAMODB_SCAN_FORWARD = 'aws.dynamodb.scan_forward';\nconst TMP_AWS_DYNAMODB_SEGMENT = 'aws.dynamodb.segment';\nconst TMP_AWS_DYNAMODB_TOTAL_SEGMENTS = 'aws.dynamodb.total_segments';\nconst TMP_AWS_DYNAMODB_COUNT = 'aws.dynamodb.count';\nconst TMP_AWS_DYNAMODB_SCANNED_COUNT = 'aws.dynamodb.scanned_count';\nconst TMP_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS =\n  'aws.dynamodb.attribute_definitions';\nconst TMP_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES =\n  'aws.dynamodb.global_secondary_index_updates';\nconst TMP_MESSAGING_SYSTEM = 'messaging.system';\nconst TMP_MESSAGING_DESTINATION = 'messaging.destination';\nconst TMP_MESSAGING_DESTINATION_KIND = 'messaging.destination_kind';\nconst TMP_MESSAGING_TEMP_DESTINATION = 'messaging.temp_destination';\nconst TMP_MESSAGING_PROTOCOL = 'messaging.protocol';\nconst TMP_MESSAGING_PROTOCOL_VERSION = 'messaging.protocol_version';\nconst TMP_MESSAGING_URL = 'messaging.url';\nconst TMP_MESSAGING_MESSAGE_ID = 'messaging.message_id';\nconst TMP_MESSAGING_CONVERSATION_ID = 'messaging.conversation_id';\nconst TMP_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES =\n  'messaging.message_payload_size_bytes';\nconst TMP_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES =\n  'messaging.message_payload_compressed_size_bytes';\nconst TMP_MESSAGING_OPERATION = 'messaging.operation';\nconst TMP_MESSAGING_CONSUMER_ID = 'messaging.consumer_id';\nconst TMP_MESSAGING_RABBITMQ_ROUTING_KEY = 'messaging.rabbitmq.routing_key';\nconst TMP_MESSAGING_KAFKA_MESSAGE_KEY = 'messaging.kafka.message_key';\nconst TMP_MESSAGING_KAFKA_CONSUMER_GROUP = 'messaging.kafka.consumer_group';\nconst TMP_MESSAGING_KAFKA_CLIENT_ID = 'messaging.kafka.client_id';\nconst TMP_MESSAGING_KAFKA_PARTITION = 'messaging.kafka.partition';\nconst TMP_MESSAGING_KAFKA_TOMBSTONE = 'messaging.kafka.tombstone';\nconst TMP_RPC_SYSTEM = 'rpc.system';\nconst TMP_RPC_SERVICE = 'rpc.service';\nconst TMP_RPC_METHOD = 'rpc.method';\nconst TMP_RPC_GRPC_STATUS_CODE = 'rpc.grpc.status_code';\nconst TMP_RPC_JSONRPC_VERSION = 'rpc.jsonrpc.version';\nconst TMP_RPC_JSONRPC_REQUEST_ID = 'rpc.jsonrpc.request_id';\nconst TMP_RPC_JSONRPC_ERROR_CODE = 'rpc.jsonrpc.error_code';\nconst TMP_RPC_JSONRPC_ERROR_MESSAGE = 'rpc.jsonrpc.error_message';\nconst TMP_MESSAGE_TYPE = 'message.type';\nconst TMP_MESSAGE_ID = 'message.id';\nconst TMP_MESSAGE_COMPRESSED_SIZE = 'message.compressed_size';\nconst TMP_MESSAGE_UNCOMPRESSED_SIZE = 'message.uncompressed_size';\n\n/**\n * The full invoked ARN as provided on the `Context` passed to the function (`Lambda-Runtime-Invoked-Function-Arn` header on the `/runtime/invocation/next` applicable).\n *\n * Note: This may be different from `faas.id` if an alias is involved.\n *\n * @deprecated Use ATTR_AWS_LAMBDA_INVOKED_ARN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_LAMBDA_INVOKED_ARN = TMP_AWS_LAMBDA_INVOKED_ARN;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use ATTR_DB_SYSTEM in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_DB_SYSTEM = TMP_DB_SYSTEM;\n\n/**\n * The connection string used to connect to the database. It is recommended to remove embedded credentials.\n *\n * @deprecated Use ATTR_DB_CONNECTION_STRING in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_DB_CONNECTION_STRING = TMP_DB_CONNECTION_STRING;\n\n/**\n * Username for accessing the database.\n *\n * @deprecated Use ATTR_DB_USER in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_DB_USER = TMP_DB_USER;\n\n/**\n * The fully-qualified class name of the [Java Database Connectivity (JDBC)](https://docs.oracle.com/javase/8/docs/technotes/guides/jdbc/) driver used to connect.\n *\n * @deprecated Use ATTR_DB_JDBC_DRIVER_CLASSNAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_DB_JDBC_DRIVER_CLASSNAME = TMP_DB_JDBC_DRIVER_CLASSNAME;\n\n/**\n * If no [tech-specific attribute](#call-level-attributes-for-specific-technologies) is defined, this attribute is used to report the name of the database being accessed. For commands that switch the database, this should be set to the target database (even if the command fails).\n *\n * Note: In some SQL databases, the database name to be used is called &#34;schema name&#34;.\n *\n * @deprecated Use ATTR_DB_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_DB_NAME = TMP_DB_NAME;\n\n/**\n * The database statement being executed.\n *\n * Note: The value may be sanitized to exclude sensitive information.\n *\n * @deprecated Use ATTR_DB_STATEMENT in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_DB_STATEMENT = TMP_DB_STATEMENT;\n\n/**\n * The name of the operation being executed, e.g. the [MongoDB command name](https://docs.mongodb.com/manual/reference/command/#database-operations) such as `findAndModify`, or the SQL keyword.\n *\n * Note: When setting this to an SQL keyword, it is not recommended to attempt any client-side parsing of `db.statement` just to get this property, but it should be set if the operation name is provided by the library being instrumented. If the SQL statement has an ambiguous operation, or performs more than one operation, this value may be omitted.\n *\n * @deprecated Use ATTR_DB_OPERATION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_DB_OPERATION = TMP_DB_OPERATION;\n\n/**\n * The Microsoft SQL Server [instance name](https://docs.microsoft.com/en-us/sql/connect/jdbc/building-the-connection-url?view=sql-server-ver15) connecting to. This name is used to determine the port of a named instance.\n *\n * Note: If setting a `db.mssql.instance_name`, `net.peer.port` is no longer required (but still recommended if non-standard).\n *\n * @deprecated Use ATTR_DB_MSSQL_INSTANCE_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_DB_MSSQL_INSTANCE_NAME = TMP_DB_MSSQL_INSTANCE_NAME;\n\n/**\n * The name of the keyspace being accessed. To be used instead of the generic `db.name` attribute.\n *\n * @deprecated Use ATTR_DB_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_DB_CASSANDRA_KEYSPACE = TMP_DB_CASSANDRA_KEYSPACE;\n\n/**\n * The fetch size used for paging, i.e. how many rows will be returned at once.\n *\n * @deprecated Use ATTR_DB_CASSANDRA_PAGE_SIZE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_DB_CASSANDRA_PAGE_SIZE = TMP_DB_CASSANDRA_PAGE_SIZE;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n *\n * @deprecated Use ATTR_DB_CASSANDRA_CONSISTENCY_LEVEL in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_DB_CASSANDRA_CONSISTENCY_LEVEL =\n  TMP_DB_CASSANDRA_CONSISTENCY_LEVEL;\n\n/**\n * The name of the primary table that the operation is acting upon, including the schema name (if applicable).\n *\n * Note: This mirrors the db.sql.table attribute but references cassandra rather than sql. It is not recommended to attempt any client-side parsing of `db.statement` just to get this property, but it should be set if it is provided by the library being instrumented. If the operation is acting upon an anonymous table, or more than one table, this value MUST NOT be set.\n *\n * @deprecated Use ATTR_DB_CASSANDRA_TABLE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_DB_CASSANDRA_TABLE = TMP_DB_CASSANDRA_TABLE;\n\n/**\n * Whether or not the query is idempotent.\n *\n * @deprecated Use ATTR_DB_CASSANDRA_IDEMPOTENCE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_DB_CASSANDRA_IDEMPOTENCE = TMP_DB_CASSANDRA_IDEMPOTENCE;\n\n/**\n * The number of times a query was speculatively executed. Not set or `0` if the query was not executed speculatively.\n *\n * @deprecated Use ATTR_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT =\n  TMP_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT;\n\n/**\n * The ID of the coordinating node for a query.\n *\n * @deprecated Use ATTR_DB_CASSANDRA_COORDINATOR_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_DB_CASSANDRA_COORDINATOR_ID =\n  TMP_DB_CASSANDRA_COORDINATOR_ID;\n\n/**\n * The data center of the coordinating node for a query.\n *\n * @deprecated Use ATTR_DB_CASSANDRA_COORDINATOR_DC in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_DB_CASSANDRA_COORDINATOR_DC =\n  TMP_DB_CASSANDRA_COORDINATOR_DC;\n\n/**\n * The [HBase namespace](https://hbase.apache.org/book.html#_namespace) being accessed. To be used instead of the generic `db.name` attribute.\n *\n * @deprecated Use ATTR_DB_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_DB_HBASE_NAMESPACE = TMP_DB_HBASE_NAMESPACE;\n\n/**\n * The index of the database being accessed as used in the [`SELECT` command](https://redis.io/commands/select), provided as an integer. To be used instead of the generic `db.name` attribute.\n *\n * @deprecated Use ATTR_DB_REDIS_DATABASE_INDEX in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_DB_REDIS_DATABASE_INDEX = TMP_DB_REDIS_DATABASE_INDEX;\n\n/**\n * The collection being accessed within the database stated in `db.name`.\n *\n * @deprecated Use ATTR_DB_MONGODB_COLLECTION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_DB_MONGODB_COLLECTION = TMP_DB_MONGODB_COLLECTION;\n\n/**\n * The name of the primary table that the operation is acting upon, including the schema name (if applicable).\n *\n * Note: It is not recommended to attempt any client-side parsing of `db.statement` just to get this property, but it should be set if it is provided by the library being instrumented. If the operation is acting upon an anonymous table, or more than one table, this value MUST NOT be set.\n *\n * @deprecated Use ATTR_DB_SQL_TABLE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_DB_SQL_TABLE = TMP_DB_SQL_TABLE;\n\n/**\n * The type of the exception (its fully-qualified class name, if applicable). The dynamic type of the exception should be preferred over the static type in languages that support it.\n *\n * @deprecated Use ATTR_EXCEPTION_TYPE.\n */\nexport const SEMATTRS_EXCEPTION_TYPE = TMP_EXCEPTION_TYPE;\n\n/**\n * The exception message.\n *\n * @deprecated Use ATTR_EXCEPTION_MESSAGE.\n */\nexport const SEMATTRS_EXCEPTION_MESSAGE = TMP_EXCEPTION_MESSAGE;\n\n/**\n * A stacktrace as a string in the natural representation for the language runtime. The representation is to be determined and documented by each language SIG.\n *\n * @deprecated Use ATTR_EXCEPTION_STACKTRACE.\n */\nexport const SEMATTRS_EXCEPTION_STACKTRACE = TMP_EXCEPTION_STACKTRACE;\n\n/**\n* SHOULD be set to true if the exception event is recorded at a point where it is known that the exception is escaping the scope of the span.\n*\n* Note: An exception is considered to have escaped (or left) the scope of a span,\nif that span is ended while the exception is still logically &#34;in flight&#34;.\nThis may be actually &#34;in flight&#34; in some languages (e.g. if the exception\nis passed to a Context manager&#39;s `__exit__` method in Python) but will\nusually be caught at the point of recording the exception in most languages.\n\nIt is usually not possible to determine at the point where an exception is thrown\nwhether it will escape the scope of a span.\nHowever, it is trivial to know that an exception\nwill escape, if one checks for an active exception just before ending the span,\nas done in the [example above](#exception-end-example).\n\nIt follows that an exception may still escape the scope of the span\neven if the `exception.escaped` attribute was not set or set to false,\nsince the event might have been recorded at a time where it was not\nclear whether the exception will escape.\n*\n* @deprecated Use ATTR_EXCEPTION_ESCAPED.\n*/\nexport const SEMATTRS_EXCEPTION_ESCAPED = TMP_EXCEPTION_ESCAPED;\n\n/**\n * Type of the trigger on which the function is executed.\n *\n * @deprecated Use ATTR_FAAS_TRIGGER in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_FAAS_TRIGGER = TMP_FAAS_TRIGGER;\n\n/**\n * The execution ID of the current function execution.\n *\n * @deprecated Use ATTR_FAAS_INVOCATION_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_FAAS_EXECUTION = TMP_FAAS_EXECUTION;\n\n/**\n * The name of the source on which the triggering operation was performed. For example, in Cloud Storage or S3 corresponds to the bucket name, and in Cosmos DB to the database name.\n *\n * @deprecated Use ATTR_FAAS_DOCUMENT_COLLECTION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_FAAS_DOCUMENT_COLLECTION = TMP_FAAS_DOCUMENT_COLLECTION;\n\n/**\n * Describes the type of the operation that was performed on the data.\n *\n * @deprecated Use ATTR_FAAS_DOCUMENT_OPERATION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_FAAS_DOCUMENT_OPERATION = TMP_FAAS_DOCUMENT_OPERATION;\n\n/**\n * A string containing the time when the data was accessed in the [ISO 8601](https://www.iso.org/iso-8601-date-and-time-format.html) format expressed in [UTC](https://www.w3.org/TR/NOTE-datetime).\n *\n * @deprecated Use ATTR_FAAS_DOCUMENT_TIME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_FAAS_DOCUMENT_TIME = TMP_FAAS_DOCUMENT_TIME;\n\n/**\n * The document name/table subjected to the operation. For example, in Cloud Storage or S3 is the name of the file, and in Cosmos DB the table name.\n *\n * @deprecated Use ATTR_FAAS_DOCUMENT_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_FAAS_DOCUMENT_NAME = TMP_FAAS_DOCUMENT_NAME;\n\n/**\n * A string containing the function invocation time in the [ISO 8601](https://www.iso.org/iso-8601-date-and-time-format.html) format expressed in [UTC](https://www.w3.org/TR/NOTE-datetime).\n *\n * @deprecated Use ATTR_FAAS_TIME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_FAAS_TIME = TMP_FAAS_TIME;\n\n/**\n * A string containing the schedule period as [Cron Expression](https://docs.oracle.com/cd/E12058_01/doc/doc.1014/e12030/cron_expressions.htm).\n *\n * @deprecated Use ATTR_FAAS_CRON in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_FAAS_CRON = TMP_FAAS_CRON;\n\n/**\n * A boolean that is true if the serverless function is executed for the first time (aka cold-start).\n *\n * @deprecated Use ATTR_FAAS_COLDSTART in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_FAAS_COLDSTART = TMP_FAAS_COLDSTART;\n\n/**\n * The name of the invoked function.\n *\n * Note: SHOULD be equal to the `faas.name` resource attribute of the invoked function.\n *\n * @deprecated Use ATTR_FAAS_INVOKED_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_FAAS_INVOKED_NAME = TMP_FAAS_INVOKED_NAME;\n\n/**\n * The cloud provider of the invoked function.\n *\n * Note: SHOULD be equal to the `cloud.provider` resource attribute of the invoked function.\n *\n * @deprecated Use ATTR_FAAS_INVOKED_PROVIDER in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_FAAS_INVOKED_PROVIDER = TMP_FAAS_INVOKED_PROVIDER;\n\n/**\n * The cloud region of the invoked function.\n *\n * Note: SHOULD be equal to the `cloud.region` resource attribute of the invoked function.\n *\n * @deprecated Use ATTR_FAAS_INVOKED_REGION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_FAAS_INVOKED_REGION = TMP_FAAS_INVOKED_REGION;\n\n/**\n * Transport protocol used. See note below.\n *\n * @deprecated Use ATTR_NET_TRANSPORT in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_NET_TRANSPORT = TMP_NET_TRANSPORT;\n\n/**\n * Remote address of the peer (dotted decimal for IPv4 or [RFC5952](https://tools.ietf.org/html/rfc5952) for IPv6).\n *\n * @deprecated Use ATTR_NET_PEER_IP in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_NET_PEER_IP = TMP_NET_PEER_IP;\n\n/**\n * Remote port number.\n *\n * @deprecated Use ATTR_NET_PEER_PORT in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_NET_PEER_PORT = TMP_NET_PEER_PORT;\n\n/**\n * Remote hostname or similar, see note below.\n *\n * @deprecated Use ATTR_NET_PEER_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_NET_PEER_NAME = TMP_NET_PEER_NAME;\n\n/**\n * Like `net.peer.ip` but for the host IP. Useful in case of a multi-IP host.\n *\n * @deprecated Use ATTR_NET_HOST_IP in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_NET_HOST_IP = TMP_NET_HOST_IP;\n\n/**\n * Like `net.peer.port` but for the host port.\n *\n * @deprecated Use ATTR_NET_HOST_PORT in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_NET_HOST_PORT = TMP_NET_HOST_PORT;\n\n/**\n * Local hostname or similar, see note below.\n *\n * @deprecated Use ATTR_NET_HOST_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_NET_HOST_NAME = TMP_NET_HOST_NAME;\n\n/**\n * The internet connection type currently being used by the host.\n *\n * @deprecated Use ATTR_NETWORK_CONNECTION_TYPE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_NET_HOST_CONNECTION_TYPE = TMP_NET_HOST_CONNECTION_TYPE;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @deprecated Use ATTR_NETWORK_CONNECTION_SUBTYPE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_NET_HOST_CONNECTION_SUBTYPE =\n  TMP_NET_HOST_CONNECTION_SUBTYPE;\n\n/**\n * The name of the mobile carrier.\n *\n * @deprecated Use ATTR_NETWORK_CARRIER_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_NET_HOST_CARRIER_NAME = TMP_NET_HOST_CARRIER_NAME;\n\n/**\n * The mobile carrier country code.\n *\n * @deprecated Use ATTR_NETWORK_CARRIER_MCC in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_NET_HOST_CARRIER_MCC = TMP_NET_HOST_CARRIER_MCC;\n\n/**\n * The mobile carrier network code.\n *\n * @deprecated Use ATTR_NETWORK_CARRIER_MNC in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_NET_HOST_CARRIER_MNC = TMP_NET_HOST_CARRIER_MNC;\n\n/**\n * The ISO 3166-1 alpha-2 2-character country code associated with the mobile carrier network.\n *\n * @deprecated Use ATTR_NETWORK_CARRIER_ICC in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_NET_HOST_CARRIER_ICC = TMP_NET_HOST_CARRIER_ICC;\n\n/**\n * The [`service.name`](../../resource/semantic_conventions/README.md#service) of the remote service. SHOULD be equal to the actual `service.name` resource attribute of the remote service if any.\n *\n * @deprecated Use ATTR_PEER_SERVICE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_PEER_SERVICE = TMP_PEER_SERVICE;\n\n/**\n * Username or client_id extracted from the access token or [Authorization](https://tools.ietf.org/html/rfc7235#section-4.2) header in the inbound request from outside the system.\n *\n * @deprecated Use ATTR_ENDUSER_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_ENDUSER_ID = TMP_ENDUSER_ID;\n\n/**\n * Actual/assumed role the client is making the request under extracted from token or application security context.\n *\n * @deprecated Use ATTR_ENDUSER_ROLE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_ENDUSER_ROLE = TMP_ENDUSER_ROLE;\n\n/**\n * Scopes or granted authorities the client currently possesses extracted from token or application security context. The value would come from the scope associated with an [OAuth 2.0 Access Token](https://tools.ietf.org/html/rfc6749#section-3.3) or an attribute value in a [SAML 2.0 Assertion](http://docs.oasis-open.org/security/saml/Post2.0/sstc-saml-tech-overview-2.0.html).\n *\n * @deprecated Use ATTR_ENDUSER_SCOPE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_ENDUSER_SCOPE = TMP_ENDUSER_SCOPE;\n\n/**\n * Current &#34;managed&#34; thread ID (as opposed to OS thread ID).\n *\n * @deprecated Use ATTR_THREAD_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_THREAD_ID = TMP_THREAD_ID;\n\n/**\n * Current thread name.\n *\n * @deprecated Use ATTR_THREAD_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_THREAD_NAME = TMP_THREAD_NAME;\n\n/**\n * The method or function name, or equivalent (usually rightmost part of the code unit&#39;s name).\n *\n * @deprecated Use ATTR_CODE_FUNCTION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_CODE_FUNCTION = TMP_CODE_FUNCTION;\n\n/**\n * The &#34;namespace&#34; within which `code.function` is defined. Usually the qualified class or module name, such that `code.namespace` + some separator + `code.function` form a unique identifier for the code unit.\n *\n * @deprecated Use ATTR_CODE_NAMESPACE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_CODE_NAMESPACE = TMP_CODE_NAMESPACE;\n\n/**\n * The source code file name that identifies the code unit as uniquely as possible (preferably an absolute file path).\n *\n * @deprecated Use ATTR_CODE_FILEPATH in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_CODE_FILEPATH = TMP_CODE_FILEPATH;\n\n/**\n * The line number in `code.filepath` best representing the operation. It SHOULD point within the code unit named in `code.function`.\n *\n * @deprecated Use ATTR_CODE_LINENO in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_CODE_LINENO = TMP_CODE_LINENO;\n\n/**\n * HTTP request method.\n *\n * @deprecated Use ATTR_HTTP_METHOD in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_HTTP_METHOD = TMP_HTTP_METHOD;\n\n/**\n * Full HTTP request URL in the form `scheme://host[:port]/path?query[#fragment]`. Usually the fragment is not transmitted over HTTP, but if it is known, it should be included nevertheless.\n *\n * Note: `http.url` MUST NOT contain credentials passed via URL in form of `https://username:<EMAIL>/`. In such case the attribute&#39;s value should be `https://www.example.com/`.\n *\n * @deprecated Use ATTR_HTTP_URL in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_HTTP_URL = TMP_HTTP_URL;\n\n/**\n * The full request target as passed in a HTTP request line or equivalent.\n *\n * @deprecated Use ATTR_HTTP_TARGET in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_HTTP_TARGET = TMP_HTTP_TARGET;\n\n/**\n * The value of the [HTTP host header](https://tools.ietf.org/html/rfc7230#section-5.4). An empty Host header should also be reported, see note.\n *\n * Note: When the header is present but empty the attribute SHOULD be set to the empty string. Note that this is a valid situation that is expected in certain cases, according the aforementioned [section of RFC 7230](https://tools.ietf.org/html/rfc7230#section-5.4). When the header is not set the attribute MUST NOT be set.\n *\n * @deprecated Use ATTR_HTTP_HOST in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_HTTP_HOST = TMP_HTTP_HOST;\n\n/**\n * The URI scheme identifying the used protocol.\n *\n * @deprecated Use ATTR_HTTP_SCHEME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_HTTP_SCHEME = TMP_HTTP_SCHEME;\n\n/**\n * [HTTP response status code](https://tools.ietf.org/html/rfc7231#section-6).\n *\n * @deprecated Use ATTR_HTTP_STATUS_CODE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_HTTP_STATUS_CODE = TMP_HTTP_STATUS_CODE;\n\n/**\n * Kind of HTTP protocol used.\n *\n * Note: If `net.transport` is not specified, it can be assumed to be `IP.TCP` except if `http.flavor` is `QUIC`, in which case `IP.UDP` is assumed.\n *\n * @deprecated Use ATTR_HTTP_FLAVOR in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_HTTP_FLAVOR = TMP_HTTP_FLAVOR;\n\n/**\n * Value of the [HTTP User-Agent](https://tools.ietf.org/html/rfc7231#section-5.5.3) header sent by the client.\n *\n * @deprecated Use ATTR_HTTP_USER_AGENT in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_HTTP_USER_AGENT = TMP_HTTP_USER_AGENT;\n\n/**\n * The size of the request payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://tools.ietf.org/html/rfc7230#section-3.3.2) header. For requests using transport encoding, this should be the compressed size.\n *\n * @deprecated Use ATTR_HTTP_REQUEST_CONTENT_LENGTH in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH =\n  TMP_HTTP_REQUEST_CONTENT_LENGTH;\n\n/**\n * The size of the uncompressed request payload body after transport decoding. Not set if transport encoding not used.\n *\n * @deprecated Use ATTR_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED =\n  TMP_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED;\n\n/**\n * The size of the response payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://tools.ietf.org/html/rfc7230#section-3.3.2) header. For requests using transport encoding, this should be the compressed size.\n *\n * @deprecated Use ATTR_HTTP_RESPONSE_CONTENT_LENGTH in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH =\n  TMP_HTTP_RESPONSE_CONTENT_LENGTH;\n\n/**\n * The size of the uncompressed response payload body after transport decoding. Not set if transport encoding not used.\n *\n * @deprecated Use ATTR_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED =\n  TMP_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED;\n\n/**\n * The primary server name of the matched virtual host. This should be obtained via configuration. If no such configuration can be obtained, this attribute MUST NOT be set ( `net.host.name` should be used instead).\n *\n * Note: `http.url` is usually not readily available on the server side but would have to be assembled in a cumbersome and sometimes lossy process from other information (see e.g. open-telemetry/opentelemetry-python/pull/148). It is thus preferred to supply the raw data that is available.\n *\n * @deprecated Use ATTR_HTTP_SERVER_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_HTTP_SERVER_NAME = TMP_HTTP_SERVER_NAME;\n\n/**\n * The matched route (path template).\n *\n * @deprecated Use ATTR_HTTP_ROUTE.\n */\nexport const SEMATTRS_HTTP_ROUTE = TMP_HTTP_ROUTE;\n\n/**\n* The IP address of the original client behind all proxies, if known (e.g. from [X-Forwarded-For](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Forwarded-For)).\n*\n* Note: This is not necessarily the same as `net.peer.ip`, which would\nidentify the network-level peer, which may be a proxy.\n\nThis attribute should be set when a source of information different\nfrom the one used for `net.peer.ip`, is available even if that other\nsource just confirms the same value as `net.peer.ip`.\nRationale: For `net.peer.ip`, one typically does not know if it\ncomes from a proxy, reverse proxy, or the actual client. Setting\n`http.client_ip` when it&#39;s the same as `net.peer.ip` means that\none is at least somewhat confident that the address is not that of\nthe closest proxy.\n*\n* @deprecated Use ATTR_HTTP_CLIENT_IP in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n*/\nexport const SEMATTRS_HTTP_CLIENT_IP = TMP_HTTP_CLIENT_IP;\n\n/**\n * The keys in the `RequestItems` object field.\n *\n * @deprecated Use ATTR_AWS_DYNAMODB_TABLE_NAMES in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_DYNAMODB_TABLE_NAMES = TMP_AWS_DYNAMODB_TABLE_NAMES;\n\n/**\n * The JSON-serialized value of each item in the `ConsumedCapacity` response field.\n *\n * @deprecated Use ATTR_AWS_DYNAMODB_CONSUMED_CAPACITY in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_DYNAMODB_CONSUMED_CAPACITY =\n  TMP_AWS_DYNAMODB_CONSUMED_CAPACITY;\n\n/**\n * The JSON-serialized value of the `ItemCollectionMetrics` response field.\n *\n * @deprecated Use ATTR_AWS_DYNAMODB_ITEM_COLLECTION_METRICS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_DYNAMODB_ITEM_COLLECTION_METRICS =\n  TMP_AWS_DYNAMODB_ITEM_COLLECTION_METRICS;\n\n/**\n * The value of the `ProvisionedThroughput.ReadCapacityUnits` request parameter.\n *\n * @deprecated Use ATTR_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY =\n  TMP_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY;\n\n/**\n * The value of the `ProvisionedThroughput.WriteCapacityUnits` request parameter.\n *\n * @deprecated Use ATTR_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY =\n  TMP_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY;\n\n/**\n * The value of the `ConsistentRead` request parameter.\n *\n * @deprecated Use ATTR_AWS_DYNAMODB_CONSISTENT_READ in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_DYNAMODB_CONSISTENT_READ =\n  TMP_AWS_DYNAMODB_CONSISTENT_READ;\n\n/**\n * The value of the `ProjectionExpression` request parameter.\n *\n * @deprecated Use ATTR_AWS_DYNAMODB_PROJECTION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_DYNAMODB_PROJECTION = TMP_AWS_DYNAMODB_PROJECTION;\n\n/**\n * The value of the `Limit` request parameter.\n *\n * @deprecated Use ATTR_AWS_DYNAMODB_LIMIT in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_DYNAMODB_LIMIT = TMP_AWS_DYNAMODB_LIMIT;\n\n/**\n * The value of the `AttributesToGet` request parameter.\n *\n * @deprecated Use ATTR_AWS_DYNAMODB_ATTRIBUTES_TO_GET in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_DYNAMODB_ATTRIBUTES_TO_GET =\n  TMP_AWS_DYNAMODB_ATTRIBUTES_TO_GET;\n\n/**\n * The value of the `IndexName` request parameter.\n *\n * @deprecated Use ATTR_AWS_DYNAMODB_INDEX_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_DYNAMODB_INDEX_NAME = TMP_AWS_DYNAMODB_INDEX_NAME;\n\n/**\n * The value of the `Select` request parameter.\n *\n * @deprecated Use ATTR_AWS_DYNAMODB_SELECT in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_DYNAMODB_SELECT = TMP_AWS_DYNAMODB_SELECT;\n\n/**\n * The JSON-serialized value of each item of the `GlobalSecondaryIndexes` request field.\n *\n * @deprecated Use ATTR_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES =\n  TMP_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES;\n\n/**\n * The JSON-serialized value of each item of the `LocalSecondaryIndexes` request field.\n *\n * @deprecated Use ATTR_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES =\n  TMP_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES;\n\n/**\n * The value of the `ExclusiveStartTableName` request parameter.\n *\n * @deprecated Use ATTR_AWS_DYNAMODB_EXCLUSIVE_START_TABLE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_DYNAMODB_EXCLUSIVE_START_TABLE =\n  TMP_AWS_DYNAMODB_EXCLUSIVE_START_TABLE;\n\n/**\n * The the number of items in the `TableNames` response parameter.\n *\n * @deprecated Use ATTR_AWS_DYNAMODB_TABLE_COUNT in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_DYNAMODB_TABLE_COUNT = TMP_AWS_DYNAMODB_TABLE_COUNT;\n\n/**\n * The value of the `ScanIndexForward` request parameter.\n *\n * @deprecated Use ATTR_AWS_DYNAMODB_SCAN_FORWARD in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_DYNAMODB_SCAN_FORWARD = TMP_AWS_DYNAMODB_SCAN_FORWARD;\n\n/**\n * The value of the `Segment` request parameter.\n *\n * @deprecated Use ATTR_AWS_DYNAMODB_SEGMENT in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_DYNAMODB_SEGMENT = TMP_AWS_DYNAMODB_SEGMENT;\n\n/**\n * The value of the `TotalSegments` request parameter.\n *\n * @deprecated Use ATTR_AWS_DYNAMODB_TOTAL_SEGMENTS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_DYNAMODB_TOTAL_SEGMENTS =\n  TMP_AWS_DYNAMODB_TOTAL_SEGMENTS;\n\n/**\n * The value of the `Count` response parameter.\n *\n * @deprecated Use ATTR_AWS_DYNAMODB_COUNT in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_DYNAMODB_COUNT = TMP_AWS_DYNAMODB_COUNT;\n\n/**\n * The value of the `ScannedCount` response parameter.\n *\n * @deprecated Use ATTR_AWS_DYNAMODB_SCANNED_COUNT in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_DYNAMODB_SCANNED_COUNT =\n  TMP_AWS_DYNAMODB_SCANNED_COUNT;\n\n/**\n * The JSON-serialized value of each item in the `AttributeDefinitions` request field.\n *\n * @deprecated Use ATTR_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS =\n  TMP_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS;\n\n/**\n * The JSON-serialized value of each item in the the `GlobalSecondaryIndexUpdates` request field.\n *\n * @deprecated Use ATTR_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES =\n  TMP_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES;\n\n/**\n * A string identifying the messaging system.\n *\n * @deprecated Use ATTR_MESSAGING_SYSTEM in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_MESSAGING_SYSTEM = TMP_MESSAGING_SYSTEM;\n\n/**\n * The message destination name. This might be equal to the span name but is required nevertheless.\n *\n * @deprecated Use ATTR_MESSAGING_DESTINATION_NAME in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_MESSAGING_DESTINATION = TMP_MESSAGING_DESTINATION;\n\n/**\n * The kind of message destination.\n *\n * @deprecated Removed in semconv v1.20.0.\n */\nexport const SEMATTRS_MESSAGING_DESTINATION_KIND =\n  TMP_MESSAGING_DESTINATION_KIND;\n\n/**\n * A boolean that is true if the message destination is temporary.\n *\n * @deprecated Use ATTR_MESSAGING_DESTINATION_TEMPORARY in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_MESSAGING_TEMP_DESTINATION =\n  TMP_MESSAGING_TEMP_DESTINATION;\n\n/**\n * The name of the transport protocol.\n *\n * @deprecated Use ATTR_NETWORK_PROTOCOL_NAME.\n */\nexport const SEMATTRS_MESSAGING_PROTOCOL = TMP_MESSAGING_PROTOCOL;\n\n/**\n * The version of the transport protocol.\n *\n * @deprecated Use ATTR_NETWORK_PROTOCOL_VERSION.\n */\nexport const SEMATTRS_MESSAGING_PROTOCOL_VERSION =\n  TMP_MESSAGING_PROTOCOL_VERSION;\n\n/**\n * Connection string.\n *\n * @deprecated Removed in semconv v1.17.0.\n */\nexport const SEMATTRS_MESSAGING_URL = TMP_MESSAGING_URL;\n\n/**\n * A value used by the messaging system as an identifier for the message, represented as a string.\n *\n * @deprecated Use ATTR_MESSAGING_MESSAGE_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_MESSAGING_MESSAGE_ID = TMP_MESSAGING_MESSAGE_ID;\n\n/**\n * The [conversation ID](#conversations) identifying the conversation to which the message belongs, represented as a string. Sometimes called &#34;Correlation ID&#34;.\n *\n * @deprecated Use ATTR_MESSAGING_MESSAGE_CONVERSATION_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_MESSAGING_CONVERSATION_ID = TMP_MESSAGING_CONVERSATION_ID;\n\n/**\n * The (uncompressed) size of the message payload in bytes. Also use this attribute if it is unknown whether the compressed or uncompressed payload size is reported.\n *\n * @deprecated Use ATTR_MESSAGING_MESSAGE_BODY_SIZE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES =\n  TMP_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES;\n\n/**\n * The compressed size of the message payload in bytes.\n *\n * @deprecated Removed in semconv v1.22.0.\n */\nexport const SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES =\n  TMP_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES;\n\n/**\n * A string identifying the kind of message consumption as defined in the [Operation names](#operation-names) section above. If the operation is &#34;send&#34;, this attribute MUST NOT be set, since the operation can be inferred from the span kind in that case.\n *\n * @deprecated Use ATTR_MESSAGING_OPERATION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_MESSAGING_OPERATION = TMP_MESSAGING_OPERATION;\n\n/**\n * The identifier for the consumer receiving a message. For Kafka, set it to `{messaging.kafka.consumer_group} - {messaging.kafka.client_id}`, if both are present, or only `messaging.kafka.consumer_group`. For brokers, such as RabbitMQ and Artemis, set it to the `client_id` of the client consuming the message.\n *\n * @deprecated Removed in semconv v1.21.0.\n */\nexport const SEMATTRS_MESSAGING_CONSUMER_ID = TMP_MESSAGING_CONSUMER_ID;\n\n/**\n * RabbitMQ message routing key.\n *\n * @deprecated Use ATTR_MESSAGING_RABBITMQ_DESTINATION_ROUTING_KEY in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY =\n  TMP_MESSAGING_RABBITMQ_ROUTING_KEY;\n\n/**\n * Message keys in Kafka are used for grouping alike messages to ensure they&#39;re processed on the same partition. They differ from `messaging.message_id` in that they&#39;re not unique. If the key is `null`, the attribute MUST NOT be set.\n *\n * Note: If the key type is not string, it&#39;s string representation has to be supplied for the attribute. If the key has no unambiguous, canonical string form, don&#39;t include its value.\n *\n * @deprecated Use ATTR_MESSAGING_KAFKA_MESSAGE_KEY in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_MESSAGING_KAFKA_MESSAGE_KEY =\n  TMP_MESSAGING_KAFKA_MESSAGE_KEY;\n\n/**\n * Name of the Kafka Consumer Group that is handling the message. Only applies to consumers, not producers.\n *\n * @deprecated Use ATTR_MESSAGING_KAFKA_CONSUMER_GROUP in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_MESSAGING_KAFKA_CONSUMER_GROUP =\n  TMP_MESSAGING_KAFKA_CONSUMER_GROUP;\n\n/**\n * Client Id for the Consumer or Producer that is handling the message.\n *\n * @deprecated Use ATTR_MESSAGING_CLIENT_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_MESSAGING_KAFKA_CLIENT_ID = TMP_MESSAGING_KAFKA_CLIENT_ID;\n\n/**\n * Partition the message is sent to.\n *\n * @deprecated Use ATTR_MESSAGING_KAFKA_DESTINATION_PARTITION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_MESSAGING_KAFKA_PARTITION = TMP_MESSAGING_KAFKA_PARTITION;\n\n/**\n * A boolean that is true if the message is a tombstone.\n *\n * @deprecated Use ATTR_MESSAGING_KAFKA_MESSAGE_TOMBSTONE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_MESSAGING_KAFKA_TOMBSTONE = TMP_MESSAGING_KAFKA_TOMBSTONE;\n\n/**\n * A string identifying the remoting system.\n *\n * @deprecated Use ATTR_RPC_SYSTEM in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_RPC_SYSTEM = TMP_RPC_SYSTEM;\n\n/**\n * The full (logical) name of the service being called, including its package name, if applicable.\n *\n * Note: This is the logical name of the service from the RPC interface perspective, which can be different from the name of any implementing class. The `code.namespace` attribute may be used to store the latter (despite the attribute name, it may include a class name; e.g., class with method actually executing the call on the server side, RPC client stub class on the client side).\n *\n * @deprecated Use ATTR_RPC_SERVICE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_RPC_SERVICE = TMP_RPC_SERVICE;\n\n/**\n * The name of the (logical) method being called, must be equal to the $method part in the span name.\n *\n * Note: This is the logical name of the method from the RPC interface perspective, which can be different from the name of any implementing method/function. The `code.function` attribute may be used to store the latter (e.g., method actually executing the call on the server side, RPC client stub method on the client side).\n *\n * @deprecated Use ATTR_RPC_METHOD in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_RPC_METHOD = TMP_RPC_METHOD;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n *\n * @deprecated Use ATTR_RPC_GRPC_STATUS_CODE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_RPC_GRPC_STATUS_CODE = TMP_RPC_GRPC_STATUS_CODE;\n\n/**\n * Protocol version as in `jsonrpc` property of request/response. Since JSON-RPC 1.0 does not specify this, the value can be omitted.\n *\n * @deprecated Use ATTR_RPC_JSONRPC_VERSION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_RPC_JSONRPC_VERSION = TMP_RPC_JSONRPC_VERSION;\n\n/**\n * `id` property of request or response. Since protocol allows id to be int, string, `null` or missing (for notifications), value is expected to be cast to string for simplicity. Use empty string in case of `null` value. Omit entirely if this is a notification.\n *\n * @deprecated Use ATTR_RPC_JSONRPC_REQUEST_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_RPC_JSONRPC_REQUEST_ID = TMP_RPC_JSONRPC_REQUEST_ID;\n\n/**\n * `error.code` property of response if it is an error response.\n *\n * @deprecated Use ATTR_RPC_JSONRPC_ERROR_CODE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_RPC_JSONRPC_ERROR_CODE = TMP_RPC_JSONRPC_ERROR_CODE;\n\n/**\n * `error.message` property of response if it is an error response.\n *\n * @deprecated Use ATTR_RPC_JSONRPC_ERROR_MESSAGE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_RPC_JSONRPC_ERROR_MESSAGE = TMP_RPC_JSONRPC_ERROR_MESSAGE;\n\n/**\n * Whether this is a received or sent message.\n *\n * @deprecated Use ATTR_MESSAGE_TYPE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_MESSAGE_TYPE = TMP_MESSAGE_TYPE;\n\n/**\n * MUST be calculated as two different counters starting from `1` one for sent messages and one for received message.\n *\n * Note: This way we guarantee that the values will be consistent between different implementations.\n *\n * @deprecated Use ATTR_MESSAGE_ID in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_MESSAGE_ID = TMP_MESSAGE_ID;\n\n/**\n * Compressed size of the message in bytes.\n *\n * @deprecated Use ATTR_MESSAGE_COMPRESSED_SIZE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_MESSAGE_COMPRESSED_SIZE = TMP_MESSAGE_COMPRESSED_SIZE;\n\n/**\n * Uncompressed size of the message in bytes.\n *\n * @deprecated Use ATTR_MESSAGE_UNCOMPRESSED_SIZE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const SEMATTRS_MESSAGE_UNCOMPRESSED_SIZE = TMP_MESSAGE_UNCOMPRESSED_SIZE;\n\n/**\n * Definition of available values for SemanticAttributes\n * This type is used for backward compatibility, you should use the individual exported\n * constants SemanticAttributes_XXXXX rather than the exported constant map. As any single reference\n * to a constant map value will result in all strings being included into your bundle.\n * @deprecated Use the SEMATTRS_XXXXX constants rather than the SemanticAttributes.XXXXX for bundle minification.\n */\nexport type SemanticAttributes = {\n  /**\n   * The full invoked ARN as provided on the `Context` passed to the function (`Lambda-Runtime-Invoked-Function-Arn` header on the `/runtime/invocation/next` applicable).\n   *\n   * Note: This may be different from `faas.id` if an alias is involved.\n   */\n  AWS_LAMBDA_INVOKED_ARN: 'aws.lambda.invoked_arn';\n\n  /**\n   * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n   */\n  DB_SYSTEM: 'db.system';\n\n  /**\n   * The connection string used to connect to the database. It is recommended to remove embedded credentials.\n   */\n  DB_CONNECTION_STRING: 'db.connection_string';\n\n  /**\n   * Username for accessing the database.\n   */\n  DB_USER: 'db.user';\n\n  /**\n   * The fully-qualified class name of the [Java Database Connectivity (JDBC)](https://docs.oracle.com/javase/8/docs/technotes/guides/jdbc/) driver used to connect.\n   */\n  DB_JDBC_DRIVER_CLASSNAME: 'db.jdbc.driver_classname';\n\n  /**\n   * If no [tech-specific attribute](#call-level-attributes-for-specific-technologies) is defined, this attribute is used to report the name of the database being accessed. For commands that switch the database, this should be set to the target database (even if the command fails).\n   *\n   * Note: In some SQL databases, the database name to be used is called &#34;schema name&#34;.\n   */\n  DB_NAME: 'db.name';\n\n  /**\n   * The database statement being executed.\n   *\n   * Note: The value may be sanitized to exclude sensitive information.\n   */\n  DB_STATEMENT: 'db.statement';\n\n  /**\n   * The name of the operation being executed, e.g. the [MongoDB command name](https://docs.mongodb.com/manual/reference/command/#database-operations) such as `findAndModify`, or the SQL keyword.\n   *\n   * Note: When setting this to an SQL keyword, it is not recommended to attempt any client-side parsing of `db.statement` just to get this property, but it should be set if the operation name is provided by the library being instrumented. If the SQL statement has an ambiguous operation, or performs more than one operation, this value may be omitted.\n   */\n  DB_OPERATION: 'db.operation';\n\n  /**\n   * The Microsoft SQL Server [instance name](https://docs.microsoft.com/en-us/sql/connect/jdbc/building-the-connection-url?view=sql-server-ver15) connecting to. This name is used to determine the port of a named instance.\n   *\n   * Note: If setting a `db.mssql.instance_name`, `net.peer.port` is no longer required (but still recommended if non-standard).\n   */\n  DB_MSSQL_INSTANCE_NAME: 'db.mssql.instance_name';\n\n  /**\n   * The name of the keyspace being accessed. To be used instead of the generic `db.name` attribute.\n   */\n  DB_CASSANDRA_KEYSPACE: 'db.cassandra.keyspace';\n\n  /**\n   * The fetch size used for paging, i.e. how many rows will be returned at once.\n   */\n  DB_CASSANDRA_PAGE_SIZE: 'db.cassandra.page_size';\n\n  /**\n   * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n   */\n  DB_CASSANDRA_CONSISTENCY_LEVEL: 'db.cassandra.consistency_level';\n\n  /**\n   * The name of the primary table that the operation is acting upon, including the schema name (if applicable).\n   *\n   * Note: This mirrors the db.sql.table attribute but references cassandra rather than sql. It is not recommended to attempt any client-side parsing of `db.statement` just to get this property, but it should be set if it is provided by the library being instrumented. If the operation is acting upon an anonymous table, or more than one table, this value MUST NOT be set.\n   */\n  DB_CASSANDRA_TABLE: 'db.cassandra.table';\n\n  /**\n   * Whether or not the query is idempotent.\n   */\n  DB_CASSANDRA_IDEMPOTENCE: 'db.cassandra.idempotence';\n\n  /**\n   * The number of times a query was speculatively executed. Not set or `0` if the query was not executed speculatively.\n   */\n  DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT: 'db.cassandra.speculative_execution_count';\n\n  /**\n   * The ID of the coordinating node for a query.\n   */\n  DB_CASSANDRA_COORDINATOR_ID: 'db.cassandra.coordinator.id';\n\n  /**\n   * The data center of the coordinating node for a query.\n   */\n  DB_CASSANDRA_COORDINATOR_DC: 'db.cassandra.coordinator.dc';\n\n  /**\n   * The [HBase namespace](https://hbase.apache.org/book.html#_namespace) being accessed. To be used instead of the generic `db.name` attribute.\n   */\n  DB_HBASE_NAMESPACE: 'db.hbase.namespace';\n\n  /**\n   * The index of the database being accessed as used in the [`SELECT` command](https://redis.io/commands/select), provided as an integer. To be used instead of the generic `db.name` attribute.\n   */\n  DB_REDIS_DATABASE_INDEX: 'db.redis.database_index';\n\n  /**\n   * The collection being accessed within the database stated in `db.name`.\n   */\n  DB_MONGODB_COLLECTION: 'db.mongodb.collection';\n\n  /**\n   * The name of the primary table that the operation is acting upon, including the schema name (if applicable).\n   *\n   * Note: It is not recommended to attempt any client-side parsing of `db.statement` just to get this property, but it should be set if it is provided by the library being instrumented. If the operation is acting upon an anonymous table, or more than one table, this value MUST NOT be set.\n   */\n  DB_SQL_TABLE: 'db.sql.table';\n\n  /**\n   * The type of the exception (its fully-qualified class name, if applicable). The dynamic type of the exception should be preferred over the static type in languages that support it.\n   */\n  EXCEPTION_TYPE: 'exception.type';\n\n  /**\n   * The exception message.\n   */\n  EXCEPTION_MESSAGE: 'exception.message';\n\n  /**\n   * A stacktrace as a string in the natural representation for the language runtime. The representation is to be determined and documented by each language SIG.\n   */\n  EXCEPTION_STACKTRACE: 'exception.stacktrace';\n\n  /**\n  * SHOULD be set to true if the exception event is recorded at a point where it is known that the exception is escaping the scope of the span.\n  *\n  * Note: An exception is considered to have escaped (or left) the scope of a span,\nif that span is ended while the exception is still logically &#34;in flight&#34;.\nThis may be actually &#34;in flight&#34; in some languages (e.g. if the exception\nis passed to a Context manager&#39;s `__exit__` method in Python) but will\nusually be caught at the point of recording the exception in most languages.\n\nIt is usually not possible to determine at the point where an exception is thrown\nwhether it will escape the scope of a span.\nHowever, it is trivial to know that an exception\nwill escape, if one checks for an active exception just before ending the span,\nas done in the [example above](#exception-end-example).\n\nIt follows that an exception may still escape the scope of the span\neven if the `exception.escaped` attribute was not set or set to false,\nsince the event might have been recorded at a time where it was not\nclear whether the exception will escape.\n  */\n  EXCEPTION_ESCAPED: 'exception.escaped';\n\n  /**\n   * Type of the trigger on which the function is executed.\n   */\n  FAAS_TRIGGER: 'faas.trigger';\n\n  /**\n   * The execution ID of the current function execution.\n   */\n  FAAS_EXECUTION: 'faas.execution';\n\n  /**\n   * The name of the source on which the triggering operation was performed. For example, in Cloud Storage or S3 corresponds to the bucket name, and in Cosmos DB to the database name.\n   */\n  FAAS_DOCUMENT_COLLECTION: 'faas.document.collection';\n\n  /**\n   * Describes the type of the operation that was performed on the data.\n   */\n  FAAS_DOCUMENT_OPERATION: 'faas.document.operation';\n\n  /**\n   * A string containing the time when the data was accessed in the [ISO 8601](https://www.iso.org/iso-8601-date-and-time-format.html) format expressed in [UTC](https://www.w3.org/TR/NOTE-datetime).\n   */\n  FAAS_DOCUMENT_TIME: 'faas.document.time';\n\n  /**\n   * The document name/table subjected to the operation. For example, in Cloud Storage or S3 is the name of the file, and in Cosmos DB the table name.\n   */\n  FAAS_DOCUMENT_NAME: 'faas.document.name';\n\n  /**\n   * A string containing the function invocation time in the [ISO 8601](https://www.iso.org/iso-8601-date-and-time-format.html) format expressed in [UTC](https://www.w3.org/TR/NOTE-datetime).\n   */\n  FAAS_TIME: 'faas.time';\n\n  /**\n   * A string containing the schedule period as [Cron Expression](https://docs.oracle.com/cd/E12058_01/doc/doc.1014/e12030/cron_expressions.htm).\n   */\n  FAAS_CRON: 'faas.cron';\n\n  /**\n   * A boolean that is true if the serverless function is executed for the first time (aka cold-start).\n   */\n  FAAS_COLDSTART: 'faas.coldstart';\n\n  /**\n   * The name of the invoked function.\n   *\n   * Note: SHOULD be equal to the `faas.name` resource attribute of the invoked function.\n   */\n  FAAS_INVOKED_NAME: 'faas.invoked_name';\n\n  /**\n   * The cloud provider of the invoked function.\n   *\n   * Note: SHOULD be equal to the `cloud.provider` resource attribute of the invoked function.\n   */\n  FAAS_INVOKED_PROVIDER: 'faas.invoked_provider';\n\n  /**\n   * The cloud region of the invoked function.\n   *\n   * Note: SHOULD be equal to the `cloud.region` resource attribute of the invoked function.\n   */\n  FAAS_INVOKED_REGION: 'faas.invoked_region';\n\n  /**\n   * Transport protocol used. See note below.\n   */\n  NET_TRANSPORT: 'net.transport';\n\n  /**\n   * Remote address of the peer (dotted decimal for IPv4 or [RFC5952](https://tools.ietf.org/html/rfc5952) for IPv6).\n   */\n  NET_PEER_IP: 'net.peer.ip';\n\n  /**\n   * Remote port number.\n   */\n  NET_PEER_PORT: 'net.peer.port';\n\n  /**\n   * Remote hostname or similar, see note below.\n   */\n  NET_PEER_NAME: 'net.peer.name';\n\n  /**\n   * Like `net.peer.ip` but for the host IP. Useful in case of a multi-IP host.\n   */\n  NET_HOST_IP: 'net.host.ip';\n\n  /**\n   * Like `net.peer.port` but for the host port.\n   */\n  NET_HOST_PORT: 'net.host.port';\n\n  /**\n   * Local hostname or similar, see note below.\n   */\n  NET_HOST_NAME: 'net.host.name';\n\n  /**\n   * The internet connection type currently being used by the host.\n   */\n  NET_HOST_CONNECTION_TYPE: 'net.host.connection.type';\n\n  /**\n   * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n   */\n  NET_HOST_CONNECTION_SUBTYPE: 'net.host.connection.subtype';\n\n  /**\n   * The name of the mobile carrier.\n   */\n  NET_HOST_CARRIER_NAME: 'net.host.carrier.name';\n\n  /**\n   * The mobile carrier country code.\n   */\n  NET_HOST_CARRIER_MCC: 'net.host.carrier.mcc';\n\n  /**\n   * The mobile carrier network code.\n   */\n  NET_HOST_CARRIER_MNC: 'net.host.carrier.mnc';\n\n  /**\n   * The ISO 3166-1 alpha-2 2-character country code associated with the mobile carrier network.\n   */\n  NET_HOST_CARRIER_ICC: 'net.host.carrier.icc';\n\n  /**\n   * The [`service.name`](../../resource/semantic_conventions/README.md#service) of the remote service. SHOULD be equal to the actual `service.name` resource attribute of the remote service if any.\n   */\n  PEER_SERVICE: 'peer.service';\n\n  /**\n   * Username or client_id extracted from the access token or [Authorization](https://tools.ietf.org/html/rfc7235#section-4.2) header in the inbound request from outside the system.\n   */\n  ENDUSER_ID: 'enduser.id';\n\n  /**\n   * Actual/assumed role the client is making the request under extracted from token or application security context.\n   */\n  ENDUSER_ROLE: 'enduser.role';\n\n  /**\n   * Scopes or granted authorities the client currently possesses extracted from token or application security context. The value would come from the scope associated with an [OAuth 2.0 Access Token](https://tools.ietf.org/html/rfc6749#section-3.3) or an attribute value in a [SAML 2.0 Assertion](http://docs.oasis-open.org/security/saml/Post2.0/sstc-saml-tech-overview-2.0.html).\n   */\n  ENDUSER_SCOPE: 'enduser.scope';\n\n  /**\n   * Current &#34;managed&#34; thread ID (as opposed to OS thread ID).\n   */\n  THREAD_ID: 'thread.id';\n\n  /**\n   * Current thread name.\n   */\n  THREAD_NAME: 'thread.name';\n\n  /**\n   * The method or function name, or equivalent (usually rightmost part of the code unit&#39;s name).\n   */\n  CODE_FUNCTION: 'code.function';\n\n  /**\n   * The &#34;namespace&#34; within which `code.function` is defined. Usually the qualified class or module name, such that `code.namespace` + some separator + `code.function` form a unique identifier for the code unit.\n   */\n  CODE_NAMESPACE: 'code.namespace';\n\n  /**\n   * The source code file name that identifies the code unit as uniquely as possible (preferably an absolute file path).\n   */\n  CODE_FILEPATH: 'code.filepath';\n\n  /**\n   * The line number in `code.filepath` best representing the operation. It SHOULD point within the code unit named in `code.function`.\n   */\n  CODE_LINENO: 'code.lineno';\n\n  /**\n   * HTTP request method.\n   */\n  HTTP_METHOD: 'http.method';\n\n  /**\n   * Full HTTP request URL in the form `scheme://host[:port]/path?query[#fragment]`. Usually the fragment is not transmitted over HTTP, but if it is known, it should be included nevertheless.\n   *\n   * Note: `http.url` MUST NOT contain credentials passed via URL in form of `https://username:<EMAIL>/`. In such case the attribute&#39;s value should be `https://www.example.com/`.\n   */\n  HTTP_URL: 'http.url';\n\n  /**\n   * The full request target as passed in a HTTP request line or equivalent.\n   */\n  HTTP_TARGET: 'http.target';\n\n  /**\n   * The value of the [HTTP host header](https://tools.ietf.org/html/rfc7230#section-5.4). An empty Host header should also be reported, see note.\n   *\n   * Note: When the header is present but empty the attribute SHOULD be set to the empty string. Note that this is a valid situation that is expected in certain cases, according the aforementioned [section of RFC 7230](https://tools.ietf.org/html/rfc7230#section-5.4). When the header is not set the attribute MUST NOT be set.\n   */\n  HTTP_HOST: 'http.host';\n\n  /**\n   * The URI scheme identifying the used protocol.\n   */\n  HTTP_SCHEME: 'http.scheme';\n\n  /**\n   * [HTTP response status code](https://tools.ietf.org/html/rfc7231#section-6).\n   */\n  HTTP_STATUS_CODE: 'http.status_code';\n\n  /**\n   * Kind of HTTP protocol used.\n   *\n   * Note: If `net.transport` is not specified, it can be assumed to be `IP.TCP` except if `http.flavor` is `QUIC`, in which case `IP.UDP` is assumed.\n   */\n  HTTP_FLAVOR: 'http.flavor';\n\n  /**\n   * Value of the [HTTP User-Agent](https://tools.ietf.org/html/rfc7231#section-5.5.3) header sent by the client.\n   */\n  HTTP_USER_AGENT: 'http.user_agent';\n\n  /**\n   * The size of the request payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://tools.ietf.org/html/rfc7230#section-3.3.2) header. For requests using transport encoding, this should be the compressed size.\n   */\n  HTTP_REQUEST_CONTENT_LENGTH: 'http.request_content_length';\n\n  /**\n   * The size of the uncompressed request payload body after transport decoding. Not set if transport encoding not used.\n   */\n  HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED: 'http.request_content_length_uncompressed';\n\n  /**\n   * The size of the response payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://tools.ietf.org/html/rfc7230#section-3.3.2) header. For requests using transport encoding, this should be the compressed size.\n   */\n  HTTP_RESPONSE_CONTENT_LENGTH: 'http.response_content_length';\n\n  /**\n   * The size of the uncompressed response payload body after transport decoding. Not set if transport encoding not used.\n   */\n  HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED: 'http.response_content_length_uncompressed';\n\n  /**\n   * The primary server name of the matched virtual host. This should be obtained via configuration. If no such configuration can be obtained, this attribute MUST NOT be set ( `net.host.name` should be used instead).\n   *\n   * Note: `http.url` is usually not readily available on the server side but would have to be assembled in a cumbersome and sometimes lossy process from other information (see e.g. open-telemetry/opentelemetry-python/pull/148). It is thus preferred to supply the raw data that is available.\n   */\n  HTTP_SERVER_NAME: 'http.server_name';\n\n  /**\n   * The matched route (path template).\n   */\n  HTTP_ROUTE: 'http.route';\n\n  /**\n  * The IP address of the original client behind all proxies, if known (e.g. from [X-Forwarded-For](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Forwarded-For)).\n  *\n  * Note: This is not necessarily the same as `net.peer.ip`, which would\nidentify the network-level peer, which may be a proxy.\n\nThis attribute should be set when a source of information different\nfrom the one used for `net.peer.ip`, is available even if that other\nsource just confirms the same value as `net.peer.ip`.\nRationale: For `net.peer.ip`, one typically does not know if it\ncomes from a proxy, reverse proxy, or the actual client. Setting\n`http.client_ip` when it&#39;s the same as `net.peer.ip` means that\none is at least somewhat confident that the address is not that of\nthe closest proxy.\n  */\n  HTTP_CLIENT_IP: 'http.client_ip';\n\n  /**\n   * The keys in the `RequestItems` object field.\n   */\n  AWS_DYNAMODB_TABLE_NAMES: 'aws.dynamodb.table_names';\n\n  /**\n   * The JSON-serialized value of each item in the `ConsumedCapacity` response field.\n   */\n  AWS_DYNAMODB_CONSUMED_CAPACITY: 'aws.dynamodb.consumed_capacity';\n\n  /**\n   * The JSON-serialized value of the `ItemCollectionMetrics` response field.\n   */\n  AWS_DYNAMODB_ITEM_COLLECTION_METRICS: 'aws.dynamodb.item_collection_metrics';\n\n  /**\n   * The value of the `ProvisionedThroughput.ReadCapacityUnits` request parameter.\n   */\n  AWS_DYNAMODB_PROVISIONED_READ_CAPACITY: 'aws.dynamodb.provisioned_read_capacity';\n\n  /**\n   * The value of the `ProvisionedThroughput.WriteCapacityUnits` request parameter.\n   */\n  AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY: 'aws.dynamodb.provisioned_write_capacity';\n\n  /**\n   * The value of the `ConsistentRead` request parameter.\n   */\n  AWS_DYNAMODB_CONSISTENT_READ: 'aws.dynamodb.consistent_read';\n\n  /**\n   * The value of the `ProjectionExpression` request parameter.\n   */\n  AWS_DYNAMODB_PROJECTION: 'aws.dynamodb.projection';\n\n  /**\n   * The value of the `Limit` request parameter.\n   */\n  AWS_DYNAMODB_LIMIT: 'aws.dynamodb.limit';\n\n  /**\n   * The value of the `AttributesToGet` request parameter.\n   */\n  AWS_DYNAMODB_ATTRIBUTES_TO_GET: 'aws.dynamodb.attributes_to_get';\n\n  /**\n   * The value of the `IndexName` request parameter.\n   */\n  AWS_DYNAMODB_INDEX_NAME: 'aws.dynamodb.index_name';\n\n  /**\n   * The value of the `Select` request parameter.\n   */\n  AWS_DYNAMODB_SELECT: 'aws.dynamodb.select';\n\n  /**\n   * The JSON-serialized value of each item of the `GlobalSecondaryIndexes` request field.\n   */\n  AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES: 'aws.dynamodb.global_secondary_indexes';\n\n  /**\n   * The JSON-serialized value of each item of the `LocalSecondaryIndexes` request field.\n   */\n  AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES: 'aws.dynamodb.local_secondary_indexes';\n\n  /**\n   * The value of the `ExclusiveStartTableName` request parameter.\n   */\n  AWS_DYNAMODB_EXCLUSIVE_START_TABLE: 'aws.dynamodb.exclusive_start_table';\n\n  /**\n   * The the number of items in the `TableNames` response parameter.\n   */\n  AWS_DYNAMODB_TABLE_COUNT: 'aws.dynamodb.table_count';\n\n  /**\n   * The value of the `ScanIndexForward` request parameter.\n   */\n  AWS_DYNAMODB_SCAN_FORWARD: 'aws.dynamodb.scan_forward';\n\n  /**\n   * The value of the `Segment` request parameter.\n   */\n  AWS_DYNAMODB_SEGMENT: 'aws.dynamodb.segment';\n\n  /**\n   * The value of the `TotalSegments` request parameter.\n   */\n  AWS_DYNAMODB_TOTAL_SEGMENTS: 'aws.dynamodb.total_segments';\n\n  /**\n   * The value of the `Count` response parameter.\n   */\n  AWS_DYNAMODB_COUNT: 'aws.dynamodb.count';\n\n  /**\n   * The value of the `ScannedCount` response parameter.\n   */\n  AWS_DYNAMODB_SCANNED_COUNT: 'aws.dynamodb.scanned_count';\n\n  /**\n   * The JSON-serialized value of each item in the `AttributeDefinitions` request field.\n   */\n  AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS: 'aws.dynamodb.attribute_definitions';\n\n  /**\n   * The JSON-serialized value of each item in the the `GlobalSecondaryIndexUpdates` request field.\n   */\n  AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES: 'aws.dynamodb.global_secondary_index_updates';\n\n  /**\n   * A string identifying the messaging system.\n   */\n  MESSAGING_SYSTEM: 'messaging.system';\n\n  /**\n   * The message destination name. This might be equal to the span name but is required nevertheless.\n   */\n  MESSAGING_DESTINATION: 'messaging.destination';\n\n  /**\n   * The kind of message destination.\n   */\n  MESSAGING_DESTINATION_KIND: 'messaging.destination_kind';\n\n  /**\n   * A boolean that is true if the message destination is temporary.\n   */\n  MESSAGING_TEMP_DESTINATION: 'messaging.temp_destination';\n\n  /**\n   * The name of the transport protocol.\n   */\n  MESSAGING_PROTOCOL: 'messaging.protocol';\n\n  /**\n   * The version of the transport protocol.\n   */\n  MESSAGING_PROTOCOL_VERSION: 'messaging.protocol_version';\n\n  /**\n   * Connection string.\n   */\n  MESSAGING_URL: 'messaging.url';\n\n  /**\n   * A value used by the messaging system as an identifier for the message, represented as a string.\n   */\n  MESSAGING_MESSAGE_ID: 'messaging.message_id';\n\n  /**\n   * The [conversation ID](#conversations) identifying the conversation to which the message belongs, represented as a string. Sometimes called &#34;Correlation ID&#34;.\n   */\n  MESSAGING_CONVERSATION_ID: 'messaging.conversation_id';\n\n  /**\n   * The (uncompressed) size of the message payload in bytes. Also use this attribute if it is unknown whether the compressed or uncompressed payload size is reported.\n   */\n  MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES: 'messaging.message_payload_size_bytes';\n\n  /**\n   * The compressed size of the message payload in bytes.\n   */\n  MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES: 'messaging.message_payload_compressed_size_bytes';\n\n  /**\n   * A string identifying the kind of message consumption as defined in the [Operation names](#operation-names) section above. If the operation is &#34;send&#34;, this attribute MUST NOT be set, since the operation can be inferred from the span kind in that case.\n   */\n  MESSAGING_OPERATION: 'messaging.operation';\n\n  /**\n   * The identifier for the consumer receiving a message. For Kafka, set it to `{messaging.kafka.consumer_group} - {messaging.kafka.client_id}`, if both are present, or only `messaging.kafka.consumer_group`. For brokers, such as RabbitMQ and Artemis, set it to the `client_id` of the client consuming the message.\n   */\n  MESSAGING_CONSUMER_ID: 'messaging.consumer_id';\n\n  /**\n   * RabbitMQ message routing key.\n   */\n  MESSAGING_RABBITMQ_ROUTING_KEY: 'messaging.rabbitmq.routing_key';\n\n  /**\n   * Message keys in Kafka are used for grouping alike messages to ensure they&#39;re processed on the same partition. They differ from `messaging.message_id` in that they&#39;re not unique. If the key is `null`, the attribute MUST NOT be set.\n   *\n   * Note: If the key type is not string, it&#39;s string representation has to be supplied for the attribute. If the key has no unambiguous, canonical string form, don&#39;t include its value.\n   */\n  MESSAGING_KAFKA_MESSAGE_KEY: 'messaging.kafka.message_key';\n\n  /**\n   * Name of the Kafka Consumer Group that is handling the message. Only applies to consumers, not producers.\n   */\n  MESSAGING_KAFKA_CONSUMER_GROUP: 'messaging.kafka.consumer_group';\n\n  /**\n   * Client Id for the Consumer or Producer that is handling the message.\n   */\n  MESSAGING_KAFKA_CLIENT_ID: 'messaging.kafka.client_id';\n\n  /**\n   * Partition the message is sent to.\n   */\n  MESSAGING_KAFKA_PARTITION: 'messaging.kafka.partition';\n\n  /**\n   * A boolean that is true if the message is a tombstone.\n   */\n  MESSAGING_KAFKA_TOMBSTONE: 'messaging.kafka.tombstone';\n\n  /**\n   * A string identifying the remoting system.\n   */\n  RPC_SYSTEM: 'rpc.system';\n\n  /**\n   * The full (logical) name of the service being called, including its package name, if applicable.\n   *\n   * Note: This is the logical name of the service from the RPC interface perspective, which can be different from the name of any implementing class. The `code.namespace` attribute may be used to store the latter (despite the attribute name, it may include a class name; e.g., class with method actually executing the call on the server side, RPC client stub class on the client side).\n   */\n  RPC_SERVICE: 'rpc.service';\n\n  /**\n   * The name of the (logical) method being called, must be equal to the $method part in the span name.\n   *\n   * Note: This is the logical name of the method from the RPC interface perspective, which can be different from the name of any implementing method/function. The `code.function` attribute may be used to store the latter (e.g., method actually executing the call on the server side, RPC client stub method on the client side).\n   */\n  RPC_METHOD: 'rpc.method';\n\n  /**\n   * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n   */\n  RPC_GRPC_STATUS_CODE: 'rpc.grpc.status_code';\n\n  /**\n   * Protocol version as in `jsonrpc` property of request/response. Since JSON-RPC 1.0 does not specify this, the value can be omitted.\n   */\n  RPC_JSONRPC_VERSION: 'rpc.jsonrpc.version';\n\n  /**\n   * `id` property of request or response. Since protocol allows id to be int, string, `null` or missing (for notifications), value is expected to be cast to string for simplicity. Use empty string in case of `null` value. Omit entirely if this is a notification.\n   */\n  RPC_JSONRPC_REQUEST_ID: 'rpc.jsonrpc.request_id';\n\n  /**\n   * `error.code` property of response if it is an error response.\n   */\n  RPC_JSONRPC_ERROR_CODE: 'rpc.jsonrpc.error_code';\n\n  /**\n   * `error.message` property of response if it is an error response.\n   */\n  RPC_JSONRPC_ERROR_MESSAGE: 'rpc.jsonrpc.error_message';\n\n  /**\n   * Whether this is a received or sent message.\n   */\n  MESSAGE_TYPE: 'message.type';\n\n  /**\n   * MUST be calculated as two different counters starting from `1` one for sent messages and one for received message.\n   *\n   * Note: This way we guarantee that the values will be consistent between different implementations.\n   */\n  MESSAGE_ID: 'message.id';\n\n  /**\n   * Compressed size of the message in bytes.\n   */\n  MESSAGE_COMPRESSED_SIZE: 'message.compressed_size';\n\n  /**\n   * Uncompressed size of the message in bytes.\n   */\n  MESSAGE_UNCOMPRESSED_SIZE: 'message.uncompressed_size';\n};\n\n/**\n * Create exported Value Map for SemanticAttributes values\n * @deprecated Use the SEMATTRS_XXXXX constants rather than the SemanticAttributes.XXXXX for bundle minification\n */\nexport const SemanticAttributes: SemanticAttributes =\n  /*#__PURE__*/ createConstMap<SemanticAttributes>([\n    TMP_AWS_LAMBDA_INVOKED_ARN,\n    TMP_DB_SYSTEM,\n    TMP_DB_CONNECTION_STRING,\n    TMP_DB_USER,\n    TMP_DB_JDBC_DRIVER_CLASSNAME,\n    TMP_DB_NAME,\n    TMP_DB_STATEMENT,\n    TMP_DB_OPERATION,\n    TMP_DB_MSSQL_INSTANCE_NAME,\n    TMP_DB_CASSANDRA_KEYSPACE,\n    TMP_DB_CASSANDRA_PAGE_SIZE,\n    TMP_DB_CASSANDRA_CONSISTENCY_LEVEL,\n    TMP_DB_CASSANDRA_TABLE,\n    TMP_DB_CASSANDRA_IDEMPOTENCE,\n    TMP_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT,\n    TMP_DB_CASSANDRA_COORDINATOR_ID,\n    TMP_DB_CASSANDRA_COORDINATOR_DC,\n    TMP_DB_HBASE_NAMESPACE,\n    TMP_DB_REDIS_DATABASE_INDEX,\n    TMP_DB_MONGODB_COLLECTION,\n    TMP_DB_SQL_TABLE,\n    TMP_EXCEPTION_TYPE,\n    TMP_EXCEPTION_MESSAGE,\n    TMP_EXCEPTION_STACKTRACE,\n    TMP_EXCEPTION_ESCAPED,\n    TMP_FAAS_TRIGGER,\n    TMP_FAAS_EXECUTION,\n    TMP_FAAS_DOCUMENT_COLLECTION,\n    TMP_FAAS_DOCUMENT_OPERATION,\n    TMP_FAAS_DOCUMENT_TIME,\n    TMP_FAAS_DOCUMENT_NAME,\n    TMP_FAAS_TIME,\n    TMP_FAAS_CRON,\n    TMP_FAAS_COLDSTART,\n    TMP_FAAS_INVOKED_NAME,\n    TMP_FAAS_INVOKED_PROVIDER,\n    TMP_FAAS_INVOKED_REGION,\n    TMP_NET_TRANSPORT,\n    TMP_NET_PEER_IP,\n    TMP_NET_PEER_PORT,\n    TMP_NET_PEER_NAME,\n    TMP_NET_HOST_IP,\n    TMP_NET_HOST_PORT,\n    TMP_NET_HOST_NAME,\n    TMP_NET_HOST_CONNECTION_TYPE,\n    TMP_NET_HOST_CONNECTION_SUBTYPE,\n    TMP_NET_HOST_CARRIER_NAME,\n    TMP_NET_HOST_CARRIER_MCC,\n    TMP_NET_HOST_CARRIER_MNC,\n    TMP_NET_HOST_CARRIER_ICC,\n    TMP_PEER_SERVICE,\n    TMP_ENDUSER_ID,\n    TMP_ENDUSER_ROLE,\n    TMP_ENDUSER_SCOPE,\n    TMP_THREAD_ID,\n    TMP_THREAD_NAME,\n    TMP_CODE_FUNCTION,\n    TMP_CODE_NAMESPACE,\n    TMP_CODE_FILEPATH,\n    TMP_CODE_LINENO,\n    TMP_HTTP_METHOD,\n    TMP_HTTP_URL,\n    TMP_HTTP_TARGET,\n    TMP_HTTP_HOST,\n    TMP_HTTP_SCHEME,\n    TMP_HTTP_STATUS_CODE,\n    TMP_HTTP_FLAVOR,\n    TMP_HTTP_USER_AGENT,\n    TMP_HTTP_REQUEST_CONTENT_LENGTH,\n    TMP_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED,\n    TMP_HTTP_RESPONSE_CONTENT_LENGTH,\n    TMP_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED,\n    TMP_HTTP_SERVER_NAME,\n    TMP_HTTP_ROUTE,\n    TMP_HTTP_CLIENT_IP,\n    TMP_AWS_DYNAMODB_TABLE_NAMES,\n    TMP_AWS_DYNAMODB_CONSUMED_CAPACITY,\n    TMP_AWS_DYNAMODB_ITEM_COLLECTION_METRICS,\n    TMP_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY,\n    TMP_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY,\n    TMP_AWS_DYNAMODB_CONSISTENT_READ,\n    TMP_AWS_DYNAMODB_PROJECTION,\n    TMP_AWS_DYNAMODB_LIMIT,\n    TMP_AWS_DYNAMODB_ATTRIBUTES_TO_GET,\n    TMP_AWS_DYNAMODB_INDEX_NAME,\n    TMP_AWS_DYNAMODB_SELECT,\n    TMP_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES,\n    TMP_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES,\n    TMP_AWS_DYNAMODB_EXCLUSIVE_START_TABLE,\n    TMP_AWS_DYNAMODB_TABLE_COUNT,\n    TMP_AWS_DYNAMODB_SCAN_FORWARD,\n    TMP_AWS_DYNAMODB_SEGMENT,\n    TMP_AWS_DYNAMODB_TOTAL_SEGMENTS,\n    TMP_AWS_DYNAMODB_COUNT,\n    TMP_AWS_DYNAMODB_SCANNED_COUNT,\n    TMP_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS,\n    TMP_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES,\n    TMP_MESSAGING_SYSTEM,\n    TMP_MESSAGING_DESTINATION,\n    TMP_MESSAGING_DESTINATION_KIND,\n    TMP_MESSAGING_TEMP_DESTINATION,\n    TMP_MESSAGING_PROTOCOL,\n    TMP_MESSAGING_PROTOCOL_VERSION,\n    TMP_MESSAGING_URL,\n    TMP_MESSAGING_MESSAGE_ID,\n    TMP_MESSAGING_CONVERSATION_ID,\n    TMP_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES,\n    TMP_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES,\n    TMP_MESSAGING_OPERATION,\n    TMP_MESSAGING_CONSUMER_ID,\n    TMP_MESSAGING_RABBITMQ_ROUTING_KEY,\n    TMP_MESSAGING_KAFKA_MESSAGE_KEY,\n    TMP_MESSAGING_KAFKA_CONSUMER_GROUP,\n    TMP_MESSAGING_KAFKA_CLIENT_ID,\n    TMP_MESSAGING_KAFKA_PARTITION,\n    TMP_MESSAGING_KAFKA_TOMBSTONE,\n    TMP_RPC_SYSTEM,\n    TMP_RPC_SERVICE,\n    TMP_RPC_METHOD,\n    TMP_RPC_GRPC_STATUS_CODE,\n    TMP_RPC_JSONRPC_VERSION,\n    TMP_RPC_JSONRPC_REQUEST_ID,\n    TMP_RPC_JSONRPC_ERROR_CODE,\n    TMP_RPC_JSONRPC_ERROR_MESSAGE,\n    TMP_MESSAGE_TYPE,\n    TMP_MESSAGE_ID,\n    TMP_MESSAGE_COMPRESSED_SIZE,\n    TMP_MESSAGE_UNCOMPRESSED_SIZE,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for DbSystemValues enum definition\n *\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_DBSYSTEMVALUES_OTHER_SQL = 'other_sql';\nconst TMP_DBSYSTEMVALUES_MSSQL = 'mssql';\nconst TMP_DBSYSTEMVALUES_MYSQL = 'mysql';\nconst TMP_DBSYSTEMVALUES_ORACLE = 'oracle';\nconst TMP_DBSYSTEMVALUES_DB2 = 'db2';\nconst TMP_DBSYSTEMVALUES_POSTGRESQL = 'postgresql';\nconst TMP_DBSYSTEMVALUES_REDSHIFT = 'redshift';\nconst TMP_DBSYSTEMVALUES_HIVE = 'hive';\nconst TMP_DBSYSTEMVALUES_CLOUDSCAPE = 'cloudscape';\nconst TMP_DBSYSTEMVALUES_HSQLDB = 'hsqldb';\nconst TMP_DBSYSTEMVALUES_PROGRESS = 'progress';\nconst TMP_DBSYSTEMVALUES_MAXDB = 'maxdb';\nconst TMP_DBSYSTEMVALUES_HANADB = 'hanadb';\nconst TMP_DBSYSTEMVALUES_INGRES = 'ingres';\nconst TMP_DBSYSTEMVALUES_FIRSTSQL = 'firstsql';\nconst TMP_DBSYSTEMVALUES_EDB = 'edb';\nconst TMP_DBSYSTEMVALUES_CACHE = 'cache';\nconst TMP_DBSYSTEMVALUES_ADABAS = 'adabas';\nconst TMP_DBSYSTEMVALUES_FIREBIRD = 'firebird';\nconst TMP_DBSYSTEMVALUES_DERBY = 'derby';\nconst TMP_DBSYSTEMVALUES_FILEMAKER = 'filemaker';\nconst TMP_DBSYSTEMVALUES_INFORMIX = 'informix';\nconst TMP_DBSYSTEMVALUES_INSTANTDB = 'instantdb';\nconst TMP_DBSYSTEMVALUES_INTERBASE = 'interbase';\nconst TMP_DBSYSTEMVALUES_MARIADB = 'mariadb';\nconst TMP_DBSYSTEMVALUES_NETEZZA = 'netezza';\nconst TMP_DBSYSTEMVALUES_PERVASIVE = 'pervasive';\nconst TMP_DBSYSTEMVALUES_POINTBASE = 'pointbase';\nconst TMP_DBSYSTEMVALUES_SQLITE = 'sqlite';\nconst TMP_DBSYSTEMVALUES_SYBASE = 'sybase';\nconst TMP_DBSYSTEMVALUES_TERADATA = 'teradata';\nconst TMP_DBSYSTEMVALUES_VERTICA = 'vertica';\nconst TMP_DBSYSTEMVALUES_H2 = 'h2';\nconst TMP_DBSYSTEMVALUES_COLDFUSION = 'coldfusion';\nconst TMP_DBSYSTEMVALUES_CASSANDRA = 'cassandra';\nconst TMP_DBSYSTEMVALUES_HBASE = 'hbase';\nconst TMP_DBSYSTEMVALUES_MONGODB = 'mongodb';\nconst TMP_DBSYSTEMVALUES_REDIS = 'redis';\nconst TMP_DBSYSTEMVALUES_COUCHBASE = 'couchbase';\nconst TMP_DBSYSTEMVALUES_COUCHDB = 'couchdb';\nconst TMP_DBSYSTEMVALUES_COSMOSDB = 'cosmosdb';\nconst TMP_DBSYSTEMVALUES_DYNAMODB = 'dynamodb';\nconst TMP_DBSYSTEMVALUES_NEO4J = 'neo4j';\nconst TMP_DBSYSTEMVALUES_GEODE = 'geode';\nconst TMP_DBSYSTEMVALUES_ELASTICSEARCH = 'elasticsearch';\nconst TMP_DBSYSTEMVALUES_MEMCACHED = 'memcached';\nconst TMP_DBSYSTEMVALUES_COCKROACHDB = 'cockroachdb';\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_OTHER_SQL in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_OTHER_SQL = TMP_DBSYSTEMVALUES_OTHER_SQL;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_MSSQL in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_MSSQL = TMP_DBSYSTEMVALUES_MSSQL;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_MYSQL in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_MYSQL = TMP_DBSYSTEMVALUES_MYSQL;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_ORACLE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_ORACLE = TMP_DBSYSTEMVALUES_ORACLE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_DB2 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_DB2 = TMP_DBSYSTEMVALUES_DB2;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_POSTGRESQL in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_POSTGRESQL = TMP_DBSYSTEMVALUES_POSTGRESQL;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_REDSHIFT in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_REDSHIFT = TMP_DBSYSTEMVALUES_REDSHIFT;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_HIVE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_HIVE = TMP_DBSYSTEMVALUES_HIVE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_CLOUDSCAPE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_CLOUDSCAPE = TMP_DBSYSTEMVALUES_CLOUDSCAPE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_HSQLDB in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_HSQLDB = TMP_DBSYSTEMVALUES_HSQLDB;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_PROGRESS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_PROGRESS = TMP_DBSYSTEMVALUES_PROGRESS;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_MAXDB in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_MAXDB = TMP_DBSYSTEMVALUES_MAXDB;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_HANADB in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_HANADB = TMP_DBSYSTEMVALUES_HANADB;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_INGRES in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_INGRES = TMP_DBSYSTEMVALUES_INGRES;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_FIRSTSQL in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_FIRSTSQL = TMP_DBSYSTEMVALUES_FIRSTSQL;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_EDB in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_EDB = TMP_DBSYSTEMVALUES_EDB;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_CACHE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_CACHE = TMP_DBSYSTEMVALUES_CACHE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_ADABAS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_ADABAS = TMP_DBSYSTEMVALUES_ADABAS;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_FIREBIRD in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_FIREBIRD = TMP_DBSYSTEMVALUES_FIREBIRD;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_DERBY in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_DERBY = TMP_DBSYSTEMVALUES_DERBY;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_FILEMAKER in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_FILEMAKER = TMP_DBSYSTEMVALUES_FILEMAKER;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_INFORMIX in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_INFORMIX = TMP_DBSYSTEMVALUES_INFORMIX;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_INSTANTDB in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_INSTANTDB = TMP_DBSYSTEMVALUES_INSTANTDB;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_INTERBASE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_INTERBASE = TMP_DBSYSTEMVALUES_INTERBASE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_MARIADB in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_MARIADB = TMP_DBSYSTEMVALUES_MARIADB;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_NETEZZA in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_NETEZZA = TMP_DBSYSTEMVALUES_NETEZZA;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_PERVASIVE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_PERVASIVE = TMP_DBSYSTEMVALUES_PERVASIVE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_POINTBASE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_POINTBASE = TMP_DBSYSTEMVALUES_POINTBASE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_SQLITE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_SQLITE = TMP_DBSYSTEMVALUES_SQLITE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_SYBASE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_SYBASE = TMP_DBSYSTEMVALUES_SYBASE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_TERADATA in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_TERADATA = TMP_DBSYSTEMVALUES_TERADATA;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_VERTICA in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_VERTICA = TMP_DBSYSTEMVALUES_VERTICA;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_H2 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_H2 = TMP_DBSYSTEMVALUES_H2;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_COLDFUSION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_COLDFUSION = TMP_DBSYSTEMVALUES_COLDFUSION;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_CASSANDRA in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_CASSANDRA = TMP_DBSYSTEMVALUES_CASSANDRA;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_HBASE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_HBASE = TMP_DBSYSTEMVALUES_HBASE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_MONGODB in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_MONGODB = TMP_DBSYSTEMVALUES_MONGODB;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_REDIS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_REDIS = TMP_DBSYSTEMVALUES_REDIS;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_COUCHBASE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_COUCHBASE = TMP_DBSYSTEMVALUES_COUCHBASE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_COUCHDB in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_COUCHDB = TMP_DBSYSTEMVALUES_COUCHDB;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_COSMOSDB in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_COSMOSDB = TMP_DBSYSTEMVALUES_COSMOSDB;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_DYNAMODB in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_DYNAMODB = TMP_DBSYSTEMVALUES_DYNAMODB;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_NEO4J in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_NEO4J = TMP_DBSYSTEMVALUES_NEO4J;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_GEODE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_GEODE = TMP_DBSYSTEMVALUES_GEODE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_ELASTICSEARCH in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_ELASTICSEARCH = TMP_DBSYSTEMVALUES_ELASTICSEARCH;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_MEMCACHED in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_MEMCACHED = TMP_DBSYSTEMVALUES_MEMCACHED;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n *\n * @deprecated Use DB_SYSTEM_VALUE_COCKROACHDB in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBSYSTEMVALUES_COCKROACHDB = TMP_DBSYSTEMVALUES_COCKROACHDB;\n\n/**\n * Identifies the Values for DbSystemValues enum definition\n *\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n * @deprecated Use the DBSYSTEMVALUES_XXXXX constants rather than the DbSystemValues.XXXXX for bundle minification.\n */\nexport type DbSystemValues = {\n  /** Some other SQL database. Fallback only. See notes. */\n  OTHER_SQL: 'other_sql';\n\n  /** Microsoft SQL Server. */\n  MSSQL: 'mssql';\n\n  /** MySQL. */\n  MYSQL: 'mysql';\n\n  /** Oracle Database. */\n  ORACLE: 'oracle';\n\n  /** IBM Db2. */\n  DB2: 'db2';\n\n  /** PostgreSQL. */\n  POSTGRESQL: 'postgresql';\n\n  /** Amazon Redshift. */\n  REDSHIFT: 'redshift';\n\n  /** Apache Hive. */\n  HIVE: 'hive';\n\n  /** Cloudscape. */\n  CLOUDSCAPE: 'cloudscape';\n\n  /** HyperSQL DataBase. */\n  HSQLDB: 'hsqldb';\n\n  /** Progress Database. */\n  PROGRESS: 'progress';\n\n  /** SAP MaxDB. */\n  MAXDB: 'maxdb';\n\n  /** SAP HANA. */\n  HANADB: 'hanadb';\n\n  /** Ingres. */\n  INGRES: 'ingres';\n\n  /** FirstSQL. */\n  FIRSTSQL: 'firstsql';\n\n  /** EnterpriseDB. */\n  EDB: 'edb';\n\n  /** InterSystems Caché. */\n  CACHE: 'cache';\n\n  /** Adabas (Adaptable Database System). */\n  ADABAS: 'adabas';\n\n  /** Firebird. */\n  FIREBIRD: 'firebird';\n\n  /** Apache Derby. */\n  DERBY: 'derby';\n\n  /** FileMaker. */\n  FILEMAKER: 'filemaker';\n\n  /** Informix. */\n  INFORMIX: 'informix';\n\n  /** InstantDB. */\n  INSTANTDB: 'instantdb';\n\n  /** InterBase. */\n  INTERBASE: 'interbase';\n\n  /** MariaDB. */\n  MARIADB: 'mariadb';\n\n  /** Netezza. */\n  NETEZZA: 'netezza';\n\n  /** Pervasive PSQL. */\n  PERVASIVE: 'pervasive';\n\n  /** PointBase. */\n  POINTBASE: 'pointbase';\n\n  /** SQLite. */\n  SQLITE: 'sqlite';\n\n  /** Sybase. */\n  SYBASE: 'sybase';\n\n  /** Teradata. */\n  TERADATA: 'teradata';\n\n  /** Vertica. */\n  VERTICA: 'vertica';\n\n  /** H2. */\n  H2: 'h2';\n\n  /** ColdFusion IMQ. */\n  COLDFUSION: 'coldfusion';\n\n  /** Apache Cassandra. */\n  CASSANDRA: 'cassandra';\n\n  /** Apache HBase. */\n  HBASE: 'hbase';\n\n  /** MongoDB. */\n  MONGODB: 'mongodb';\n\n  /** Redis. */\n  REDIS: 'redis';\n\n  /** Couchbase. */\n  COUCHBASE: 'couchbase';\n\n  /** CouchDB. */\n  COUCHDB: 'couchdb';\n\n  /** Microsoft Azure Cosmos DB. */\n  COSMOSDB: 'cosmosdb';\n\n  /** Amazon DynamoDB. */\n  DYNAMODB: 'dynamodb';\n\n  /** Neo4j. */\n  NEO4J: 'neo4j';\n\n  /** Apache Geode. */\n  GEODE: 'geode';\n\n  /** Elasticsearch. */\n  ELASTICSEARCH: 'elasticsearch';\n\n  /** Memcached. */\n  MEMCACHED: 'memcached';\n\n  /** CockroachDB. */\n  COCKROACHDB: 'cockroachdb';\n};\n\n/**\n * The constant map of values for DbSystemValues.\n * @deprecated Use the DBSYSTEMVALUES_XXXXX constants rather than the DbSystemValues.XXXXX for bundle minification.\n */\nexport const DbSystemValues: DbSystemValues =\n  /*#__PURE__*/ createConstMap<DbSystemValues>([\n    TMP_DBSYSTEMVALUES_OTHER_SQL,\n    TMP_DBSYSTEMVALUES_MSSQL,\n    TMP_DBSYSTEMVALUES_MYSQL,\n    TMP_DBSYSTEMVALUES_ORACLE,\n    TMP_DBSYSTEMVALUES_DB2,\n    TMP_DBSYSTEMVALUES_POSTGRESQL,\n    TMP_DBSYSTEMVALUES_REDSHIFT,\n    TMP_DBSYSTEMVALUES_HIVE,\n    TMP_DBSYSTEMVALUES_CLOUDSCAPE,\n    TMP_DBSYSTEMVALUES_HSQLDB,\n    TMP_DBSYSTEMVALUES_PROGRESS,\n    TMP_DBSYSTEMVALUES_MAXDB,\n    TMP_DBSYSTEMVALUES_HANADB,\n    TMP_DBSYSTEMVALUES_INGRES,\n    TMP_DBSYSTEMVALUES_FIRSTSQL,\n    TMP_DBSYSTEMVALUES_EDB,\n    TMP_DBSYSTEMVALUES_CACHE,\n    TMP_DBSYSTEMVALUES_ADABAS,\n    TMP_DBSYSTEMVALUES_FIREBIRD,\n    TMP_DBSYSTEMVALUES_DERBY,\n    TMP_DBSYSTEMVALUES_FILEMAKER,\n    TMP_DBSYSTEMVALUES_INFORMIX,\n    TMP_DBSYSTEMVALUES_INSTANTDB,\n    TMP_DBSYSTEMVALUES_INTERBASE,\n    TMP_DBSYSTEMVALUES_MARIADB,\n    TMP_DBSYSTEMVALUES_NETEZZA,\n    TMP_DBSYSTEMVALUES_PERVASIVE,\n    TMP_DBSYSTEMVALUES_POINTBASE,\n    TMP_DBSYSTEMVALUES_SQLITE,\n    TMP_DBSYSTEMVALUES_SYBASE,\n    TMP_DBSYSTEMVALUES_TERADATA,\n    TMP_DBSYSTEMVALUES_VERTICA,\n    TMP_DBSYSTEMVALUES_H2,\n    TMP_DBSYSTEMVALUES_COLDFUSION,\n    TMP_DBSYSTEMVALUES_CASSANDRA,\n    TMP_DBSYSTEMVALUES_HBASE,\n    TMP_DBSYSTEMVALUES_MONGODB,\n    TMP_DBSYSTEMVALUES_REDIS,\n    TMP_DBSYSTEMVALUES_COUCHBASE,\n    TMP_DBSYSTEMVALUES_COUCHDB,\n    TMP_DBSYSTEMVALUES_COSMOSDB,\n    TMP_DBSYSTEMVALUES_DYNAMODB,\n    TMP_DBSYSTEMVALUES_NEO4J,\n    TMP_DBSYSTEMVALUES_GEODE,\n    TMP_DBSYSTEMVALUES_ELASTICSEARCH,\n    TMP_DBSYSTEMVALUES_MEMCACHED,\n    TMP_DBSYSTEMVALUES_COCKROACHDB,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for DbCassandraConsistencyLevelValues enum definition\n *\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ALL = 'all';\nconst TMP_DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM = 'each_quorum';\nconst TMP_DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM = 'quorum';\nconst TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM = 'local_quorum';\nconst TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ONE = 'one';\nconst TMP_DBCASSANDRACONSISTENCYLEVELVALUES_TWO = 'two';\nconst TMP_DBCASSANDRACONSISTENCYLEVELVALUES_THREE = 'three';\nconst TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE = 'local_one';\nconst TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ANY = 'any';\nconst TMP_DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL = 'serial';\nconst TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL = 'local_serial';\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n *\n * @deprecated Use DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_ALL in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBCASSANDRACONSISTENCYLEVELVALUES_ALL =\n  TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ALL;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n *\n * @deprecated Use DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_EACH_QUORUM in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM =\n  TMP_DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n *\n * @deprecated Use DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_QUORUM in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM =\n  TMP_DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n *\n * @deprecated Use DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_QUORUM in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM =\n  TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n *\n * @deprecated Use DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_ONE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBCASSANDRACONSISTENCYLEVELVALUES_ONE =\n  TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ONE;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n *\n * @deprecated Use DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_TWO in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBCASSANDRACONSISTENCYLEVELVALUES_TWO =\n  TMP_DBCASSANDRACONSISTENCYLEVELVALUES_TWO;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n *\n * @deprecated Use DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_THREE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBCASSANDRACONSISTENCYLEVELVALUES_THREE =\n  TMP_DBCASSANDRACONSISTENCYLEVELVALUES_THREE;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n *\n * @deprecated Use DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_ONE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE =\n  TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n *\n * @deprecated Use DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_ANY in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBCASSANDRACONSISTENCYLEVELVALUES_ANY =\n  TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ANY;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n *\n * @deprecated Use DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_SERIAL in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL =\n  TMP_DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n *\n * @deprecated Use DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_SERIAL in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL =\n  TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL;\n\n/**\n * Identifies the Values for DbCassandraConsistencyLevelValues enum definition\n *\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n * @deprecated Use the DBCASSANDRACONSISTENCYLEVELVALUES_XXXXX constants rather than the DbCassandraConsistencyLevelValues.XXXXX for bundle minification.\n */\nexport type DbCassandraConsistencyLevelValues = {\n  /** all. */\n  ALL: 'all';\n\n  /** each_quorum. */\n  EACH_QUORUM: 'each_quorum';\n\n  /** quorum. */\n  QUORUM: 'quorum';\n\n  /** local_quorum. */\n  LOCAL_QUORUM: 'local_quorum';\n\n  /** one. */\n  ONE: 'one';\n\n  /** two. */\n  TWO: 'two';\n\n  /** three. */\n  THREE: 'three';\n\n  /** local_one. */\n  LOCAL_ONE: 'local_one';\n\n  /** any. */\n  ANY: 'any';\n\n  /** serial. */\n  SERIAL: 'serial';\n\n  /** local_serial. */\n  LOCAL_SERIAL: 'local_serial';\n};\n\n/**\n * The constant map of values for DbCassandraConsistencyLevelValues.\n * @deprecated Use the DBCASSANDRACONSISTENCYLEVELVALUES_XXXXX constants rather than the DbCassandraConsistencyLevelValues.XXXXX for bundle minification.\n */\nexport const DbCassandraConsistencyLevelValues: DbCassandraConsistencyLevelValues =\n  /*#__PURE__*/ createConstMap<DbCassandraConsistencyLevelValues>([\n    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ALL,\n    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM,\n    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM,\n    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM,\n    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ONE,\n    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_TWO,\n    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_THREE,\n    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE,\n    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ANY,\n    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL,\n    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for FaasTriggerValues enum definition\n *\n * Type of the trigger on which the function is executed.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_FAASTRIGGERVALUES_DATASOURCE = 'datasource';\nconst TMP_FAASTRIGGERVALUES_HTTP = 'http';\nconst TMP_FAASTRIGGERVALUES_PUBSUB = 'pubsub';\nconst TMP_FAASTRIGGERVALUES_TIMER = 'timer';\nconst TMP_FAASTRIGGERVALUES_OTHER = 'other';\n\n/**\n * Type of the trigger on which the function is executed.\n *\n * @deprecated Use FAAS_TRIGGER_VALUE_DATASOURCE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const FAASTRIGGERVALUES_DATASOURCE = TMP_FAASTRIGGERVALUES_DATASOURCE;\n\n/**\n * Type of the trigger on which the function is executed.\n *\n * @deprecated Use FAAS_TRIGGER_VALUE_HTTP in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const FAASTRIGGERVALUES_HTTP = TMP_FAASTRIGGERVALUES_HTTP;\n\n/**\n * Type of the trigger on which the function is executed.\n *\n * @deprecated Use FAAS_TRIGGER_VALUE_PUBSUB in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const FAASTRIGGERVALUES_PUBSUB = TMP_FAASTRIGGERVALUES_PUBSUB;\n\n/**\n * Type of the trigger on which the function is executed.\n *\n * @deprecated Use FAAS_TRIGGER_VALUE_TIMER in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const FAASTRIGGERVALUES_TIMER = TMP_FAASTRIGGERVALUES_TIMER;\n\n/**\n * Type of the trigger on which the function is executed.\n *\n * @deprecated Use FAAS_TRIGGER_VALUE_OTHER in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const FAASTRIGGERVALUES_OTHER = TMP_FAASTRIGGERVALUES_OTHER;\n\n/**\n * Identifies the Values for FaasTriggerValues enum definition\n *\n * Type of the trigger on which the function is executed.\n * @deprecated Use the FAASTRIGGERVALUES_XXXXX constants rather than the FaasTriggerValues.XXXXX for bundle minification.\n */\nexport type FaasTriggerValues = {\n  /** A response to some data source operation such as a database or filesystem read/write. */\n  DATASOURCE: 'datasource';\n\n  /** To provide an answer to an inbound HTTP request. */\n  HTTP: 'http';\n\n  /** A function is set to be executed when messages are sent to a messaging system. */\n  PUBSUB: 'pubsub';\n\n  /** A function is scheduled to be executed regularly. */\n  TIMER: 'timer';\n\n  /** If none of the others apply. */\n  OTHER: 'other';\n};\n\n/**\n * The constant map of values for FaasTriggerValues.\n * @deprecated Use the FAASTRIGGERVALUES_XXXXX constants rather than the FaasTriggerValues.XXXXX for bundle minification.\n */\nexport const FaasTriggerValues: FaasTriggerValues =\n  /*#__PURE__*/ createConstMap<FaasTriggerValues>([\n    TMP_FAASTRIGGERVALUES_DATASOURCE,\n    TMP_FAASTRIGGERVALUES_HTTP,\n    TMP_FAASTRIGGERVALUES_PUBSUB,\n    TMP_FAASTRIGGERVALUES_TIMER,\n    TMP_FAASTRIGGERVALUES_OTHER,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for FaasDocumentOperationValues enum definition\n *\n * Describes the type of the operation that was performed on the data.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_FAASDOCUMENTOPERATIONVALUES_INSERT = 'insert';\nconst TMP_FAASDOCUMENTOPERATIONVALUES_EDIT = 'edit';\nconst TMP_FAASDOCUMENTOPERATIONVALUES_DELETE = 'delete';\n\n/**\n * Describes the type of the operation that was performed on the data.\n *\n * @deprecated Use FAAS_DOCUMENT_OPERATION_VALUE_INSERT in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const FAASDOCUMENTOPERATIONVALUES_INSERT =\n  TMP_FAASDOCUMENTOPERATIONVALUES_INSERT;\n\n/**\n * Describes the type of the operation that was performed on the data.\n *\n * @deprecated Use FAAS_DOCUMENT_OPERATION_VALUE_EDIT in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const FAASDOCUMENTOPERATIONVALUES_EDIT =\n  TMP_FAASDOCUMENTOPERATIONVALUES_EDIT;\n\n/**\n * Describes the type of the operation that was performed on the data.\n *\n * @deprecated Use FAAS_DOCUMENT_OPERATION_VALUE_DELETE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const FAASDOCUMENTOPERATIONVALUES_DELETE =\n  TMP_FAASDOCUMENTOPERATIONVALUES_DELETE;\n\n/**\n * Identifies the Values for FaasDocumentOperationValues enum definition\n *\n * Describes the type of the operation that was performed on the data.\n * @deprecated Use the FAASDOCUMENTOPERATIONVALUES_XXXXX constants rather than the FaasDocumentOperationValues.XXXXX for bundle minification.\n */\nexport type FaasDocumentOperationValues = {\n  /** When a new object is created. */\n  INSERT: 'insert';\n\n  /** When an object is modified. */\n  EDIT: 'edit';\n\n  /** When an object is deleted. */\n  DELETE: 'delete';\n};\n\n/**\n * The constant map of values for FaasDocumentOperationValues.\n * @deprecated Use the FAASDOCUMENTOPERATIONVALUES_XXXXX constants rather than the FaasDocumentOperationValues.XXXXX for bundle minification.\n */\nexport const FaasDocumentOperationValues: FaasDocumentOperationValues =\n  /*#__PURE__*/ createConstMap<FaasDocumentOperationValues>([\n    TMP_FAASDOCUMENTOPERATIONVALUES_INSERT,\n    TMP_FAASDOCUMENTOPERATIONVALUES_EDIT,\n    TMP_FAASDOCUMENTOPERATIONVALUES_DELETE,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for FaasInvokedProviderValues enum definition\n *\n * The cloud provider of the invoked function.\n *\n * Note: SHOULD be equal to the `cloud.provider` resource attribute of the invoked function.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD = 'alibaba_cloud';\nconst TMP_FAASINVOKEDPROVIDERVALUES_AWS = 'aws';\nconst TMP_FAASINVOKEDPROVIDERVALUES_AZURE = 'azure';\nconst TMP_FAASINVOKEDPROVIDERVALUES_GCP = 'gcp';\n\n/**\n * The cloud provider of the invoked function.\n *\n * Note: SHOULD be equal to the `cloud.provider` resource attribute of the invoked function.\n *\n * @deprecated Use FAAS_INVOKED_PROVIDER_VALUE_ALIBABA_CLOUD in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD =\n  TMP_FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD;\n\n/**\n * The cloud provider of the invoked function.\n *\n * Note: SHOULD be equal to the `cloud.provider` resource attribute of the invoked function.\n *\n * @deprecated Use FAAS_INVOKED_PROVIDER_VALUE_AWS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const FAASINVOKEDPROVIDERVALUES_AWS = TMP_FAASINVOKEDPROVIDERVALUES_AWS;\n\n/**\n * The cloud provider of the invoked function.\n *\n * Note: SHOULD be equal to the `cloud.provider` resource attribute of the invoked function.\n *\n * @deprecated Use FAAS_INVOKED_PROVIDER_VALUE_AZURE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const FAASINVOKEDPROVIDERVALUES_AZURE =\n  TMP_FAASINVOKEDPROVIDERVALUES_AZURE;\n\n/**\n * The cloud provider of the invoked function.\n *\n * Note: SHOULD be equal to the `cloud.provider` resource attribute of the invoked function.\n *\n * @deprecated Use FAAS_INVOKED_PROVIDER_VALUE_GCP in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const FAASINVOKEDPROVIDERVALUES_GCP = TMP_FAASINVOKEDPROVIDERVALUES_GCP;\n\n/**\n * Identifies the Values for FaasInvokedProviderValues enum definition\n *\n * The cloud provider of the invoked function.\n *\n * Note: SHOULD be equal to the `cloud.provider` resource attribute of the invoked function.\n * @deprecated Use the FAASINVOKEDPROVIDERVALUES_XXXXX constants rather than the FaasInvokedProviderValues.XXXXX for bundle minification.\n */\nexport type FaasInvokedProviderValues = {\n  /** Alibaba Cloud. */\n  ALIBABA_CLOUD: 'alibaba_cloud';\n\n  /** Amazon Web Services. */\n  AWS: 'aws';\n\n  /** Microsoft Azure. */\n  AZURE: 'azure';\n\n  /** Google Cloud Platform. */\n  GCP: 'gcp';\n};\n\n/**\n * The constant map of values for FaasInvokedProviderValues.\n * @deprecated Use the FAASINVOKEDPROVIDERVALUES_XXXXX constants rather than the FaasInvokedProviderValues.XXXXX for bundle minification.\n */\nexport const FaasInvokedProviderValues: FaasInvokedProviderValues =\n  /*#__PURE__*/ createConstMap<FaasInvokedProviderValues>([\n    TMP_FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD,\n    TMP_FAASINVOKEDPROVIDERVALUES_AWS,\n    TMP_FAASINVOKEDPROVIDERVALUES_AZURE,\n    TMP_FAASINVOKEDPROVIDERVALUES_GCP,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for NetTransportValues enum definition\n *\n * Transport protocol used. See note below.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_NETTRANSPORTVALUES_IP_TCP = 'ip_tcp';\nconst TMP_NETTRANSPORTVALUES_IP_UDP = 'ip_udp';\nconst TMP_NETTRANSPORTVALUES_IP = 'ip';\nconst TMP_NETTRANSPORTVALUES_UNIX = 'unix';\nconst TMP_NETTRANSPORTVALUES_PIPE = 'pipe';\nconst TMP_NETTRANSPORTVALUES_INPROC = 'inproc';\nconst TMP_NETTRANSPORTVALUES_OTHER = 'other';\n\n/**\n * Transport protocol used. See note below.\n *\n * @deprecated Use NET_TRANSPORT_VALUE_IP_TCP in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETTRANSPORTVALUES_IP_TCP = TMP_NETTRANSPORTVALUES_IP_TCP;\n\n/**\n * Transport protocol used. See note below.\n *\n * @deprecated Use NET_TRANSPORT_VALUE_IP_UDP in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETTRANSPORTVALUES_IP_UDP = TMP_NETTRANSPORTVALUES_IP_UDP;\n\n/**\n * Transport protocol used. See note below.\n *\n * @deprecated Removed in v1.21.0.\n */\nexport const NETTRANSPORTVALUES_IP = TMP_NETTRANSPORTVALUES_IP;\n\n/**\n * Transport protocol used. See note below.\n *\n * @deprecated Removed in v1.21.0.\n */\nexport const NETTRANSPORTVALUES_UNIX = TMP_NETTRANSPORTVALUES_UNIX;\n\n/**\n * Transport protocol used. See note below.\n *\n * @deprecated Use NET_TRANSPORT_VALUE_PIPE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETTRANSPORTVALUES_PIPE = TMP_NETTRANSPORTVALUES_PIPE;\n\n/**\n * Transport protocol used. See note below.\n *\n * @deprecated Use NET_TRANSPORT_VALUE_INPROC in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETTRANSPORTVALUES_INPROC = TMP_NETTRANSPORTVALUES_INPROC;\n\n/**\n * Transport protocol used. See note below.\n *\n * @deprecated Use NET_TRANSPORT_VALUE_OTHER in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETTRANSPORTVALUES_OTHER = TMP_NETTRANSPORTVALUES_OTHER;\n\n/**\n * Identifies the Values for NetTransportValues enum definition\n *\n * Transport protocol used. See note below.\n * @deprecated Use the NETTRANSPORTVALUES_XXXXX constants rather than the NetTransportValues.XXXXX for bundle minification.\n */\nexport type NetTransportValues = {\n  /** ip_tcp. */\n  IP_TCP: 'ip_tcp';\n\n  /** ip_udp. */\n  IP_UDP: 'ip_udp';\n\n  /** Another IP-based protocol. */\n  IP: 'ip';\n\n  /** Unix Domain socket. See below. */\n  UNIX: 'unix';\n\n  /** Named or anonymous pipe. See note below. */\n  PIPE: 'pipe';\n\n  /** In-process communication. */\n  INPROC: 'inproc';\n\n  /** Something else (non IP-based). */\n  OTHER: 'other';\n};\n\n/**\n * The constant map of values for NetTransportValues.\n * @deprecated Use the NETTRANSPORTVALUES_XXXXX constants rather than the NetTransportValues.XXXXX for bundle minification.\n */\nexport const NetTransportValues: NetTransportValues =\n  /*#__PURE__*/ createConstMap<NetTransportValues>([\n    TMP_NETTRANSPORTVALUES_IP_TCP,\n    TMP_NETTRANSPORTVALUES_IP_UDP,\n    TMP_NETTRANSPORTVALUES_IP,\n    TMP_NETTRANSPORTVALUES_UNIX,\n    TMP_NETTRANSPORTVALUES_PIPE,\n    TMP_NETTRANSPORTVALUES_INPROC,\n    TMP_NETTRANSPORTVALUES_OTHER,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for NetHostConnectionTypeValues enum definition\n *\n * The internet connection type currently being used by the host.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_NETHOSTCONNECTIONTYPEVALUES_WIFI = 'wifi';\nconst TMP_NETHOSTCONNECTIONTYPEVALUES_WIRED = 'wired';\nconst TMP_NETHOSTCONNECTIONTYPEVALUES_CELL = 'cell';\nconst TMP_NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE = 'unavailable';\nconst TMP_NETHOSTCONNECTIONTYPEVALUES_UNKNOWN = 'unknown';\n\n/**\n * The internet connection type currently being used by the host.\n *\n * @deprecated Use NETWORK_CONNECTION_TYPE_VALUE_WIFI in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONTYPEVALUES_WIFI =\n  TMP_NETHOSTCONNECTIONTYPEVALUES_WIFI;\n\n/**\n * The internet connection type currently being used by the host.\n *\n * @deprecated Use NETWORK_CONNECTION_TYPE_VALUE_WIRED in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONTYPEVALUES_WIRED =\n  TMP_NETHOSTCONNECTIONTYPEVALUES_WIRED;\n\n/**\n * The internet connection type currently being used by the host.\n *\n * @deprecated Use NETWORK_CONNECTION_TYPE_VALUE_CELL in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONTYPEVALUES_CELL =\n  TMP_NETHOSTCONNECTIONTYPEVALUES_CELL;\n\n/**\n * The internet connection type currently being used by the host.\n *\n * @deprecated Use NETWORK_CONNECTION_TYPE_VALUE_UNAVAILABLE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE =\n  TMP_NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE;\n\n/**\n * The internet connection type currently being used by the host.\n *\n * @deprecated Use NETWORK_CONNECTION_TYPE_VALUE_UNKNOWN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONTYPEVALUES_UNKNOWN =\n  TMP_NETHOSTCONNECTIONTYPEVALUES_UNKNOWN;\n\n/**\n * Identifies the Values for NetHostConnectionTypeValues enum definition\n *\n * The internet connection type currently being used by the host.\n * @deprecated Use the NETHOSTCONNECTIONTYPEVALUES_XXXXX constants rather than the NetHostConnectionTypeValues.XXXXX for bundle minification.\n */\nexport type NetHostConnectionTypeValues = {\n  /** wifi. */\n  WIFI: 'wifi';\n\n  /** wired. */\n  WIRED: 'wired';\n\n  /** cell. */\n  CELL: 'cell';\n\n  /** unavailable. */\n  UNAVAILABLE: 'unavailable';\n\n  /** unknown. */\n  UNKNOWN: 'unknown';\n};\n\n/**\n * The constant map of values for NetHostConnectionTypeValues.\n * @deprecated Use the NETHOSTCONNECTIONTYPEVALUES_XXXXX constants rather than the NetHostConnectionTypeValues.XXXXX for bundle minification.\n */\nexport const NetHostConnectionTypeValues: NetHostConnectionTypeValues =\n  /*#__PURE__*/ createConstMap<NetHostConnectionTypeValues>([\n    TMP_NETHOSTCONNECTIONTYPEVALUES_WIFI,\n    TMP_NETHOSTCONNECTIONTYPEVALUES_WIRED,\n    TMP_NETHOSTCONNECTIONTYPEVALUES_CELL,\n    TMP_NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE,\n    TMP_NETHOSTCONNECTIONTYPEVALUES_UNKNOWN,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for NetHostConnectionSubtypeValues enum definition\n *\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_GPRS = 'gprs';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EDGE = 'edge';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_UMTS = 'umts';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_CDMA = 'cdma';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0 = 'evdo_0';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A = 'evdo_a';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT = 'cdma2000_1xrtt';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA = 'hsdpa';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA = 'hsupa';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSPA = 'hspa';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_IDEN = 'iden';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B = 'evdo_b';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_LTE = 'lte';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD = 'ehrpd';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP = 'hspap';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_GSM = 'gsm';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA = 'td_scdma';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN = 'iwlan';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_NR = 'nr';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA = 'nrnsa';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA = 'lte_ca';\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @deprecated Use NETWORK_CONNECTION_SUBTYPE_VALUE_GPRS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_GPRS =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_GPRS;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @deprecated Use NETWORK_CONNECTION_SUBTYPE_VALUE_EDGE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_EDGE =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EDGE;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @deprecated Use NETWORK_CONNECTION_SUBTYPE_VALUE_UMTS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_UMTS =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_UMTS;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @deprecated Use NETWORK_CONNECTION_SUBTYPE_VALUE_CDMA in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_CDMA =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_CDMA;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @deprecated Use NETWORK_CONNECTION_SUBTYPE_VALUE_EVDO_0 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0 =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @deprecated Use NETWORK_CONNECTION_SUBTYPE_VALUE_EVDO_A in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @deprecated Use NETWORK_CONNECTION_SUBTYPE_VALUE_CDMA2000_1XRTT in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @deprecated Use NETWORK_CONNECTION_SUBTYPE_VALUE_HSDPA in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @deprecated Use NETWORK_CONNECTION_SUBTYPE_VALUE_HSUPA in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @deprecated Use NETWORK_CONNECTION_SUBTYPE_VALUE_HSPA in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_HSPA =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSPA;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @deprecated Use NETWORK_CONNECTION_SUBTYPE_VALUE_IDEN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_IDEN =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_IDEN;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @deprecated Use NETWORK_CONNECTION_SUBTYPE_VALUE_EVDO_B in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @deprecated Use NETWORK_CONNECTION_SUBTYPE_VALUE_LTE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_LTE =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_LTE;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @deprecated Use NETWORK_CONNECTION_SUBTYPE_VALUE_EHRPD in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @deprecated Use NETWORK_CONNECTION_SUBTYPE_VALUE_HSPAP in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @deprecated Use NETWORK_CONNECTION_SUBTYPE_VALUE_GSM in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_GSM =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_GSM;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @deprecated Use NETWORK_CONNECTION_SUBTYPE_VALUE_TD_SCDMA in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @deprecated Use NETWORK_CONNECTION_SUBTYPE_VALUE_IWLAN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @deprecated Use NETWORK_CONNECTION_SUBTYPE_VALUE_NR in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_NR =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_NR;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @deprecated Use NETWORK_CONNECTION_SUBTYPE_VALUE_NRNSA in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @deprecated Use NETWORK_CONNECTION_SUBTYPE_VALUE_LTE_CA in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA;\n\n/**\n * Identifies the Values for NetHostConnectionSubtypeValues enum definition\n *\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n * @deprecated Use the NETHOSTCONNECTIONSUBTYPEVALUES_XXXXX constants rather than the NetHostConnectionSubtypeValues.XXXXX for bundle minification.\n */\nexport type NetHostConnectionSubtypeValues = {\n  /** GPRS. */\n  GPRS: 'gprs';\n\n  /** EDGE. */\n  EDGE: 'edge';\n\n  /** UMTS. */\n  UMTS: 'umts';\n\n  /** CDMA. */\n  CDMA: 'cdma';\n\n  /** EVDO Rel. 0. */\n  EVDO_0: 'evdo_0';\n\n  /** EVDO Rev. A. */\n  EVDO_A: 'evdo_a';\n\n  /** CDMA2000 1XRTT. */\n  CDMA2000_1XRTT: 'cdma2000_1xrtt';\n\n  /** HSDPA. */\n  HSDPA: 'hsdpa';\n\n  /** HSUPA. */\n  HSUPA: 'hsupa';\n\n  /** HSPA. */\n  HSPA: 'hspa';\n\n  /** IDEN. */\n  IDEN: 'iden';\n\n  /** EVDO Rev. B. */\n  EVDO_B: 'evdo_b';\n\n  /** LTE. */\n  LTE: 'lte';\n\n  /** EHRPD. */\n  EHRPD: 'ehrpd';\n\n  /** HSPAP. */\n  HSPAP: 'hspap';\n\n  /** GSM. */\n  GSM: 'gsm';\n\n  /** TD-SCDMA. */\n  TD_SCDMA: 'td_scdma';\n\n  /** IWLAN. */\n  IWLAN: 'iwlan';\n\n  /** 5G NR (New Radio). */\n  NR: 'nr';\n\n  /** 5G NRNSA (New Radio Non-Standalone). */\n  NRNSA: 'nrnsa';\n\n  /** LTE CA. */\n  LTE_CA: 'lte_ca';\n};\n\n/**\n * The constant map of values for NetHostConnectionSubtypeValues.\n * @deprecated Use the NETHOSTCONNECTIONSUBTYPEVALUES_XXXXX constants rather than the NetHostConnectionSubtypeValues.XXXXX for bundle minification.\n */\nexport const NetHostConnectionSubtypeValues: NetHostConnectionSubtypeValues =\n  /*#__PURE__*/ createConstMap<NetHostConnectionSubtypeValues>([\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_GPRS,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EDGE,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_UMTS,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_CDMA,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSPA,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_IDEN,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_LTE,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_GSM,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_NR,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for HttpFlavorValues enum definition\n *\n * Kind of HTTP protocol used.\n *\n * Note: If `net.transport` is not specified, it can be assumed to be `IP.TCP` except if `http.flavor` is `QUIC`, in which case `IP.UDP` is assumed.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_HTTPFLAVORVALUES_HTTP_1_0 = '1.0';\nconst TMP_HTTPFLAVORVALUES_HTTP_1_1 = '1.1';\nconst TMP_HTTPFLAVORVALUES_HTTP_2_0 = '2.0';\nconst TMP_HTTPFLAVORVALUES_SPDY = 'SPDY';\nconst TMP_HTTPFLAVORVALUES_QUIC = 'QUIC';\n\n/**\n * Kind of HTTP protocol used.\n *\n * Note: If `net.transport` is not specified, it can be assumed to be `IP.TCP` except if `http.flavor` is `QUIC`, in which case `IP.UDP` is assumed.\n *\n * @deprecated Use HTTP_FLAVOR_VALUE_HTTP_1_0 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HTTPFLAVORVALUES_HTTP_1_0 = TMP_HTTPFLAVORVALUES_HTTP_1_0;\n\n/**\n * Kind of HTTP protocol used.\n *\n * Note: If `net.transport` is not specified, it can be assumed to be `IP.TCP` except if `http.flavor` is `QUIC`, in which case `IP.UDP` is assumed.\n *\n * @deprecated Use HTTP_FLAVOR_VALUE_HTTP_1_1 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HTTPFLAVORVALUES_HTTP_1_1 = TMP_HTTPFLAVORVALUES_HTTP_1_1;\n\n/**\n * Kind of HTTP protocol used.\n *\n * Note: If `net.transport` is not specified, it can be assumed to be `IP.TCP` except if `http.flavor` is `QUIC`, in which case `IP.UDP` is assumed.\n *\n * @deprecated Use HTTP_FLAVOR_VALUE_HTTP_2_0 in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HTTPFLAVORVALUES_HTTP_2_0 = TMP_HTTPFLAVORVALUES_HTTP_2_0;\n\n/**\n * Kind of HTTP protocol used.\n *\n * Note: If `net.transport` is not specified, it can be assumed to be `IP.TCP` except if `http.flavor` is `QUIC`, in which case `IP.UDP` is assumed.\n *\n * @deprecated Use HTTP_FLAVOR_VALUE_SPDY in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HTTPFLAVORVALUES_SPDY = TMP_HTTPFLAVORVALUES_SPDY;\n\n/**\n * Kind of HTTP protocol used.\n *\n * Note: If `net.transport` is not specified, it can be assumed to be `IP.TCP` except if `http.flavor` is `QUIC`, in which case `IP.UDP` is assumed.\n *\n * @deprecated Use HTTP_FLAVOR_VALUE_QUIC in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const HTTPFLAVORVALUES_QUIC = TMP_HTTPFLAVORVALUES_QUIC;\n\n/**\n * Identifies the Values for HttpFlavorValues enum definition\n *\n * Kind of HTTP protocol used.\n *\n * Note: If `net.transport` is not specified, it can be assumed to be `IP.TCP` except if `http.flavor` is `QUIC`, in which case `IP.UDP` is assumed.\n * @deprecated Use the HTTPFLAVORVALUES_XXXXX constants rather than the HttpFlavorValues.XXXXX for bundle minification.\n */\nexport type HttpFlavorValues = {\n  /** HTTP 1.0. */\n  HTTP_1_0: '1.0';\n\n  /** HTTP 1.1. */\n  HTTP_1_1: '1.1';\n\n  /** HTTP 2. */\n  HTTP_2_0: '2.0';\n\n  /** SPDY protocol. */\n  SPDY: 'SPDY';\n\n  /** QUIC protocol. */\n  QUIC: 'QUIC';\n};\n\n/**\n * The constant map of values for HttpFlavorValues.\n * @deprecated Use the HTTPFLAVORVALUES_XXXXX constants rather than the HttpFlavorValues.XXXXX for bundle minification.\n */\nexport const HttpFlavorValues: HttpFlavorValues = {\n  HTTP_1_0: TMP_HTTPFLAVORVALUES_HTTP_1_0,\n  HTTP_1_1: TMP_HTTPFLAVORVALUES_HTTP_1_1,\n  HTTP_2_0: TMP_HTTPFLAVORVALUES_HTTP_2_0,\n  SPDY: TMP_HTTPFLAVORVALUES_SPDY,\n  QUIC: TMP_HTTPFLAVORVALUES_QUIC,\n};\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for MessagingDestinationKindValues enum definition\n *\n * The kind of message destination.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_MESSAGINGDESTINATIONKINDVALUES_QUEUE = 'queue';\nconst TMP_MESSAGINGDESTINATIONKINDVALUES_TOPIC = 'topic';\n\n/**\n * The kind of message destination.\n *\n * @deprecated Removed in semconv v1.20.0.\n */\nexport const MESSAGINGDESTINATIONKINDVALUES_QUEUE =\n  TMP_MESSAGINGDESTINATIONKINDVALUES_QUEUE;\n\n/**\n * The kind of message destination.\n *\n * @deprecated Removed in semconv v1.20.0.\n */\nexport const MESSAGINGDESTINATIONKINDVALUES_TOPIC =\n  TMP_MESSAGINGDESTINATIONKINDVALUES_TOPIC;\n\n/**\n * Identifies the Values for MessagingDestinationKindValues enum definition\n *\n * The kind of message destination.\n * @deprecated Use the MESSAGINGDESTINATIONKINDVALUES_XXXXX constants rather than the MessagingDestinationKindValues.XXXXX for bundle minification.\n */\nexport type MessagingDestinationKindValues = {\n  /** A message sent to a queue. */\n  QUEUE: 'queue';\n\n  /** A message sent to a topic. */\n  TOPIC: 'topic';\n};\n\n/**\n * The constant map of values for MessagingDestinationKindValues.\n * @deprecated Use the MESSAGINGDESTINATIONKINDVALUES_XXXXX constants rather than the MessagingDestinationKindValues.XXXXX for bundle minification.\n */\nexport const MessagingDestinationKindValues: MessagingDestinationKindValues =\n  /*#__PURE__*/ createConstMap<MessagingDestinationKindValues>([\n    TMP_MESSAGINGDESTINATIONKINDVALUES_QUEUE,\n    TMP_MESSAGINGDESTINATIONKINDVALUES_TOPIC,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for MessagingOperationValues enum definition\n *\n * A string identifying the kind of message consumption as defined in the [Operation names](#operation-names) section above. If the operation is &#34;send&#34;, this attribute MUST NOT be set, since the operation can be inferred from the span kind in that case.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_MESSAGINGOPERATIONVALUES_RECEIVE = 'receive';\nconst TMP_MESSAGINGOPERATIONVALUES_PROCESS = 'process';\n\n/**\n * A string identifying the kind of message consumption as defined in the [Operation names](#operation-names) section above. If the operation is &#34;send&#34;, this attribute MUST NOT be set, since the operation can be inferred from the span kind in that case.\n *\n * @deprecated Use MESSAGING_OPERATION_TYPE_VALUE_RECEIVE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const MESSAGINGOPERATIONVALUES_RECEIVE =\n  TMP_MESSAGINGOPERATIONVALUES_RECEIVE;\n\n/**\n * A string identifying the kind of message consumption as defined in the [Operation names](#operation-names) section above. If the operation is &#34;send&#34;, this attribute MUST NOT be set, since the operation can be inferred from the span kind in that case.\n *\n * @deprecated Use MESSAGING_OPERATION_TYPE_VALUE_PROCESS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const MESSAGINGOPERATIONVALUES_PROCESS =\n  TMP_MESSAGINGOPERATIONVALUES_PROCESS;\n\n/**\n * Identifies the Values for MessagingOperationValues enum definition\n *\n * A string identifying the kind of message consumption as defined in the [Operation names](#operation-names) section above. If the operation is &#34;send&#34;, this attribute MUST NOT be set, since the operation can be inferred from the span kind in that case.\n * @deprecated Use the MESSAGINGOPERATIONVALUES_XXXXX constants rather than the MessagingOperationValues.XXXXX for bundle minification.\n */\nexport type MessagingOperationValues = {\n  /** receive. */\n  RECEIVE: 'receive';\n\n  /** process. */\n  PROCESS: 'process';\n};\n\n/**\n * The constant map of values for MessagingOperationValues.\n * @deprecated Use the MESSAGINGOPERATIONVALUES_XXXXX constants rather than the MessagingOperationValues.XXXXX for bundle minification.\n */\nexport const MessagingOperationValues: MessagingOperationValues =\n  /*#__PURE__*/ createConstMap<MessagingOperationValues>([\n    TMP_MESSAGINGOPERATIONVALUES_RECEIVE,\n    TMP_MESSAGINGOPERATIONVALUES_PROCESS,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for RpcGrpcStatusCodeValues enum definition\n *\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_RPCGRPCSTATUSCODEVALUES_OK = 0;\nconst TMP_RPCGRPCSTATUSCODEVALUES_CANCELLED = 1;\nconst TMP_RPCGRPCSTATUSCODEVALUES_UNKNOWN = 2;\nconst TMP_RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT = 3;\nconst TMP_RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED = 4;\nconst TMP_RPCGRPCSTATUSCODEVALUES_NOT_FOUND = 5;\nconst TMP_RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS = 6;\nconst TMP_RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED = 7;\nconst TMP_RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED = 8;\nconst TMP_RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION = 9;\nconst TMP_RPCGRPCSTATUSCODEVALUES_ABORTED = 10;\nconst TMP_RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE = 11;\nconst TMP_RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED = 12;\nconst TMP_RPCGRPCSTATUSCODEVALUES_INTERNAL = 13;\nconst TMP_RPCGRPCSTATUSCODEVALUES_UNAVAILABLE = 14;\nconst TMP_RPCGRPCSTATUSCODEVALUES_DATA_LOSS = 15;\nconst TMP_RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED = 16;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n *\n * @deprecated Use RPC_GRPC_STATUS_CODE_VALUE_OK in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const RPCGRPCSTATUSCODEVALUES_OK = TMP_RPCGRPCSTATUSCODEVALUES_OK;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n *\n * @deprecated Use RPC_GRPC_STATUS_CODE_VALUE_CANCELLED in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const RPCGRPCSTATUSCODEVALUES_CANCELLED =\n  TMP_RPCGRPCSTATUSCODEVALUES_CANCELLED;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n *\n * @deprecated Use RPC_GRPC_STATUS_CODE_VALUE_UNKNOWN in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const RPCGRPCSTATUSCODEVALUES_UNKNOWN =\n  TMP_RPCGRPCSTATUSCODEVALUES_UNKNOWN;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n *\n * @deprecated Use RPC_GRPC_STATUS_CODE_VALUE_INVALID_ARGUMENT in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT =\n  TMP_RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n *\n * @deprecated Use RPC_GRPC_STATUS_CODE_VALUE_DEADLINE_EXCEEDED in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED =\n  TMP_RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n *\n * @deprecated Use RPC_GRPC_STATUS_CODE_VALUE_NOT_FOUND in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const RPCGRPCSTATUSCODEVALUES_NOT_FOUND =\n  TMP_RPCGRPCSTATUSCODEVALUES_NOT_FOUND;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n *\n * @deprecated Use RPC_GRPC_STATUS_CODE_VALUE_ALREADY_EXISTS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS =\n  TMP_RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n *\n * @deprecated Use RPC_GRPC_STATUS_CODE_VALUE_PERMISSION_DENIED in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED =\n  TMP_RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n *\n * @deprecated Use RPC_GRPC_STATUS_CODE_VALUE_RESOURCE_EXHAUSTED in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED =\n  TMP_RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n *\n * @deprecated Use RPC_GRPC_STATUS_CODE_VALUE_FAILED_PRECONDITION in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION =\n  TMP_RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n *\n * @deprecated Use RPC_GRPC_STATUS_CODE_VALUE_ABORTED in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const RPCGRPCSTATUSCODEVALUES_ABORTED =\n  TMP_RPCGRPCSTATUSCODEVALUES_ABORTED;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n *\n * @deprecated Use RPC_GRPC_STATUS_CODE_VALUE_OUT_OF_RANGE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE =\n  TMP_RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n *\n * @deprecated Use RPC_GRPC_STATUS_CODE_VALUE_UNIMPLEMENTED in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED =\n  TMP_RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n *\n * @deprecated Use RPC_GRPC_STATUS_CODE_VALUE_INTERNAL in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const RPCGRPCSTATUSCODEVALUES_INTERNAL =\n  TMP_RPCGRPCSTATUSCODEVALUES_INTERNAL;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n *\n * @deprecated Use RPC_GRPC_STATUS_CODE_VALUE_UNAVAILABLE in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const RPCGRPCSTATUSCODEVALUES_UNAVAILABLE =\n  TMP_RPCGRPCSTATUSCODEVALUES_UNAVAILABLE;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n *\n * @deprecated Use RPC_GRPC_STATUS_CODE_VALUE_DATA_LOSS in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const RPCGRPCSTATUSCODEVALUES_DATA_LOSS =\n  TMP_RPCGRPCSTATUSCODEVALUES_DATA_LOSS;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n *\n * @deprecated Use RPC_GRPC_STATUS_CODE_VALUE_UNAUTHENTICATED in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED =\n  TMP_RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED;\n\n/**\n * Identifies the Values for RpcGrpcStatusCodeValues enum definition\n *\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n * @deprecated Use the RPCGRPCSTATUSCODEVALUES_XXXXX constants rather than the RpcGrpcStatusCodeValues.XXXXX for bundle minification.\n */\nexport type RpcGrpcStatusCodeValues = {\n  /** OK. */\n  OK: 0;\n\n  /** CANCELLED. */\n  CANCELLED: 1;\n\n  /** UNKNOWN. */\n  UNKNOWN: 2;\n\n  /** INVALID_ARGUMENT. */\n  INVALID_ARGUMENT: 3;\n\n  /** DEADLINE_EXCEEDED. */\n  DEADLINE_EXCEEDED: 4;\n\n  /** NOT_FOUND. */\n  NOT_FOUND: 5;\n\n  /** ALREADY_EXISTS. */\n  ALREADY_EXISTS: 6;\n\n  /** PERMISSION_DENIED. */\n  PERMISSION_DENIED: 7;\n\n  /** RESOURCE_EXHAUSTED. */\n  RESOURCE_EXHAUSTED: 8;\n\n  /** FAILED_PRECONDITION. */\n  FAILED_PRECONDITION: 9;\n\n  /** ABORTED. */\n  ABORTED: 10;\n\n  /** OUT_OF_RANGE. */\n  OUT_OF_RANGE: 11;\n\n  /** UNIMPLEMENTED. */\n  UNIMPLEMENTED: 12;\n\n  /** INTERNAL. */\n  INTERNAL: 13;\n\n  /** UNAVAILABLE. */\n  UNAVAILABLE: 14;\n\n  /** DATA_LOSS. */\n  DATA_LOSS: 15;\n\n  /** UNAUTHENTICATED. */\n  UNAUTHENTICATED: 16;\n};\n\n/**\n * The constant map of values for RpcGrpcStatusCodeValues.\n * @deprecated Use the RPCGRPCSTATUSCODEVALUES_XXXXX constants rather than the RpcGrpcStatusCodeValues.XXXXX for bundle minification.\n */\nexport const RpcGrpcStatusCodeValues: RpcGrpcStatusCodeValues = {\n  OK: TMP_RPCGRPCSTATUSCODEVALUES_OK,\n  CANCELLED: TMP_RPCGRPCSTATUSCODEVALUES_CANCELLED,\n  UNKNOWN: TMP_RPCGRPCSTATUSCODEVALUES_UNKNOWN,\n  INVALID_ARGUMENT: TMP_RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT,\n  DEADLINE_EXCEEDED: TMP_RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED,\n  NOT_FOUND: TMP_RPCGRPCSTATUSCODEVALUES_NOT_FOUND,\n  ALREADY_EXISTS: TMP_RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS,\n  PERMISSION_DENIED: TMP_RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED,\n  RESOURCE_EXHAUSTED: TMP_RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED,\n  FAILED_PRECONDITION: TMP_RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION,\n  ABORTED: TMP_RPCGRPCSTATUSCODEVALUES_ABORTED,\n  OUT_OF_RANGE: TMP_RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE,\n  UNIMPLEMENTED: TMP_RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED,\n  INTERNAL: TMP_RPCGRPCSTATUSCODEVALUES_INTERNAL,\n  UNAVAILABLE: TMP_RPCGRPCSTATUSCODEVALUES_UNAVAILABLE,\n  DATA_LOSS: TMP_RPCGRPCSTATUSCODEVALUES_DATA_LOSS,\n  UNAUTHENTICATED: TMP_RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED,\n};\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for MessageTypeValues enum definition\n *\n * Whether this is a received or sent message.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifiable export names for some package types\nconst TMP_MESSAGETYPEVALUES_SENT = 'SENT';\nconst TMP_MESSAGETYPEVALUES_RECEIVED = 'RECEIVED';\n\n/**\n * Whether this is a received or sent message.\n *\n * @deprecated Use MESSAGE_TYPE_VALUE_SENT in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const MESSAGETYPEVALUES_SENT = TMP_MESSAGETYPEVALUES_SENT;\n\n/**\n * Whether this is a received or sent message.\n *\n * @deprecated Use MESSAGE_TYPE_VALUE_RECEIVED in [incubating entry-point]({@link https://github.com/open-telemetry/opentelemetry-js/blob/main/semantic-conventions/README.md#unstable-semconv}).\n */\nexport const MESSAGETYPEVALUES_RECEIVED = TMP_MESSAGETYPEVALUES_RECEIVED;\n\n/**\n * Identifies the Values for MessageTypeValues enum definition\n *\n * Whether this is a received or sent message.\n * @deprecated Use the MESSAGETYPEVALUES_XXXXX constants rather than the MessageTypeValues.XXXXX for bundle minification.\n */\nexport type MessageTypeValues = {\n  /** sent. */\n  SENT: 'SENT';\n\n  /** received. */\n  RECEIVED: 'RECEIVED';\n};\n\n/**\n * The constant map of values for MessageTypeValues.\n * @deprecated Use the MESSAGETYPEVALUES_XXXXX constants rather than the MessageTypeValues.XXXXX for bundle minification.\n */\nexport const MessageTypeValues: MessageTypeValues =\n  /*#__PURE__*/ createConstMap<MessageTypeValues>([\n    TMP_MESSAGETYPEVALUES_SENT,\n    TMP_MESSAGETYPEVALUES_RECEIVED,\n  ]);\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;;AAEnD,4GAA4G;AAC5G,iHAAiH;AACjH,4GAA4G;AAE5G,4GAA4G;AAC5G,yCAAyC;AACzC,4GAA4G;AAE5G,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,0BAA0B,GAAG,wBAAwB,CAAC;AAC5D,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,WAAW,GAAG,SAAS,CAAC;AAC9B,IAAM,4BAA4B,GAAG,0BAA0B,CAAC;AAChE,IAAM,WAAW,GAAG,SAAS,CAAC;AAC9B,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,0BAA0B,GAAG,wBAAwB,CAAC;AAC5D,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAC1D,IAAM,0BAA0B,GAAG,wBAAwB,CAAC;AAC5D,IAAM,kCAAkC,GAAG,gCAAgC,CAAC;AAC5E,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,4BAA4B,GAAG,0BAA0B,CAAC;AAChE,IAAM,4CAA4C,GAChD,0CAA0C,CAAC;AAC7C,IAAM,+BAA+B,GAAG,6BAA6B,CAAC;AACtE,IAAM,+BAA+B,GAAG,6BAA6B,CAAC;AACtE,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAC1D,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAClD,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAClD,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,4BAA4B,GAAG,0BAA0B,CAAC;AAChE,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAClD,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAC1D,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,4BAA4B,GAAG,0BAA0B,CAAC;AAChE,IAAM,+BAA+B,GAAG,6BAA6B,CAAC;AACtE,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAC1D,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,cAAc,GAAG,YAAY,CAAC;AACpC,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,YAAY,GAAG,UAAU,CAAC;AAChC,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,oBAAoB,GAAG,kBAAkB,CAAC;AAChD,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAC9C,IAAM,+BAA+B,GAAG,6BAA6B,CAAC;AACtE,IAAM,4CAA4C,GAChD,0CAA0C,CAAC;AAC7C,IAAM,gCAAgC,GAAG,8BAA8B,CAAC;AACxE,IAAM,6CAA6C,GACjD,2CAA2C,CAAC;AAC9C,IAAM,oBAAoB,GAAG,kBAAkB,CAAC;AAChD,IAAM,cAAc,GAAG,YAAY,CAAC;AACpC,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,4BAA4B,GAAG,0BAA0B,CAAC;AAChE,IAAM,kCAAkC,GAAG,gCAAgC,CAAC;AAC5E,IAAM,wCAAwC,GAC5C,sCAAsC,CAAC;AACzC,IAAM,0CAA0C,GAC9C,wCAAwC,CAAC;AAC3C,IAAM,2CAA2C,GAC/C,yCAAyC,CAAC;AAC5C,IAAM,gCAAgC,GAAG,8BAA8B,CAAC;AACxE,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,kCAAkC,GAAG,gCAAgC,CAAC;AAC5E,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,yCAAyC,GAC7C,uCAAuC,CAAC;AAC1C,IAAM,wCAAwC,GAC5C,sCAAsC,CAAC;AACzC,IAAM,sCAAsC,GAC1C,oCAAoC,CAAC;AACvC,IAAM,4BAA4B,GAAG,0BAA0B,CAAC;AAChE,IAAM,6BAA6B,GAAG,2BAA2B,CAAC;AAClE,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,+BAA+B,GAAG,6BAA6B,CAAC;AACtE,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,8BAA8B,GAAG,4BAA4B,CAAC;AACpE,IAAM,sCAAsC,GAC1C,oCAAoC,CAAC;AACvC,IAAM,+CAA+C,GACnD,6CAA6C,CAAC;AAChD,IAAM,oBAAoB,GAAG,kBAAkB,CAAC;AAChD,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAC1D,IAAM,8BAA8B,GAAG,4BAA4B,CAAC;AACpE,IAAM,8BAA8B,GAAG,4BAA4B,CAAC;AACpE,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,8BAA8B,GAAG,4BAA4B,CAAC;AACpE,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,6BAA6B,GAAG,2BAA2B,CAAC;AAClE,IAAM,wCAAwC,GAC5C,sCAAsC,CAAC;AACzC,IAAM,mDAAmD,GACvD,iDAAiD,CAAC;AACpD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAC1D,IAAM,kCAAkC,GAAG,gCAAgC,CAAC;AAC5E,IAAM,+BAA+B,GAAG,6BAA6B,CAAC;AACtE,IAAM,kCAAkC,GAAG,gCAAgC,CAAC;AAC5E,IAAM,6BAA6B,GAAG,2BAA2B,CAAC;AAClE,IAAM,6BAA6B,GAAG,2BAA2B,CAAC;AAClE,IAAM,6BAA6B,GAAG,2BAA2B,CAAC;AAClE,IAAM,cAAc,GAAG,YAAY,CAAC;AACpC,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,cAAc,GAAG,YAAY,CAAC;AACpC,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,0BAA0B,GAAG,wBAAwB,CAAC;AAC5D,IAAM,0BAA0B,GAAG,wBAAwB,CAAC;AAC5D,IAAM,6BAA6B,GAAG,2BAA2B,CAAC;AAClE,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,cAAc,GAAG,YAAY,CAAC;AACpC,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,6BAA6B,GAAG,2BAA2B,CAAC;AAS3D,IAAM,+BAA+B,GAAG,0BAA0B,CAAC;AAOnE,IAAM,kBAAkB,GAAG,aAAa,CAAC;AAOzC,IAAM,6BAA6B,GAAG,wBAAwB,CAAC;AAO/D,IAAM,gBAAgB,GAAG,WAAW,CAAC;AAOrC,IAAM,iCAAiC,GAAG,4BAA4B,CAAC;AASvE,IAAM,gBAAgB,GAAG,WAAW,CAAC;AASrC,IAAM,qBAAqB,GAAG,gBAAgB,CAAC;AAS/C,IAAM,qBAAqB,GAAG,gBAAgB,CAAC;AAS/C,IAAM,+BAA+B,GAAG,0BAA0B,CAAC;AAOnE,IAAM,8BAA8B,GAAG,yBAAyB,CAAC;AAOjE,IAAM,+BAA+B,GAAG,0BAA0B,CAAC;AAOnE,IAAM,uCAAuC,GAClD,kCAAkC,CAAC;AAS9B,IAAM,2BAA2B,GAAG,sBAAsB,CAAC;AAO3D,IAAM,iCAAiC,GAAG,4BAA4B,CAAC;AAOvE,IAAM,iDAAiD,GAC5D,4CAA4C,CAAC;AAOxC,IAAM,oCAAoC,GAC/C,+BAA+B,CAAC;AAO3B,IAAM,oCAAoC,GAC/C,+BAA+B,CAAC;AAO3B,IAAM,2BAA2B,GAAG,sBAAsB,CAAC;AAO3D,IAAM,gCAAgC,GAAG,2BAA2B,CAAC;AAOrE,IAAM,8BAA8B,GAAG,yBAAyB,CAAC;AASjE,IAAM,qBAAqB,GAAG,gBAAgB,CAAC;AAO/C,IAAM,uBAAuB,GAAG,kBAAkB,CAAC;AAOnD,IAAM,0BAA0B,GAAG,qBAAqB,CAAC;AAOzD,IAAM,6BAA6B,GAAG,wBAAwB,CAAC;AAwB/D,IAAM,0BAA0B,GAAG,qBAAqB,CAAC;AAOzD,IAAM,qBAAqB,GAAG,gBAAgB,CAAC;AAO/C,IAAM,uBAAuB,GAAG,kBAAkB,CAAC;AAOnD,IAAM,iCAAiC,GAAG,4BAA4B,CAAC;AAOvE,IAAM,gCAAgC,GAAG,2BAA2B,CAAC;AAOrE,IAAM,2BAA2B,GAAG,sBAAsB,CAAC;AAO3D,IAAM,2BAA2B,GAAG,sBAAsB,CAAC;AAO3D,IAAM,kBAAkB,GAAG,aAAa,CAAC;AAOzC,IAAM,kBAAkB,GAAG,aAAa,CAAC;AAOzC,IAAM,uBAAuB,GAAG,kBAAkB,CAAC;AASnD,IAAM,0BAA0B,GAAG,qBAAqB,CAAC;AASzD,IAAM,8BAA8B,GAAG,yBAAyB,CAAC;AASjE,IAAM,4BAA4B,GAAG,uBAAuB,CAAC;AAO7D,IAAM,sBAAsB,GAAG,iBAAiB,CAAC;AAOjD,IAAM,oBAAoB,GAAG,eAAe,CAAC;AAO7C,IAAM,sBAAsB,GAAG,iBAAiB,CAAC;AAOjD,IAAM,sBAAsB,GAAG,iBAAiB,CAAC;AAOjD,IAAM,oBAAoB,GAAG,eAAe,CAAC;AAO7C,IAAM,sBAAsB,GAAG,iBAAiB,CAAC;AAOjD,IAAM,sBAAsB,GAAG,iBAAiB,CAAC;AAOjD,IAAM,iCAAiC,GAAG,4BAA4B,CAAC;AAOvE,IAAM,oCAAoC,GAC/C,+BAA+B,CAAC;AAO3B,IAAM,8BAA8B,GAAG,yBAAyB,CAAC;AAOjE,IAAM,6BAA6B,GAAG,wBAAwB,CAAC;AAO/D,IAAM,6BAA6B,GAAG,wBAAwB,CAAC;AAO/D,IAAM,6BAA6B,GAAG,wBAAwB,CAAC;AAO/D,IAAM,qBAAqB,GAAG,gBAAgB,CAAC;AAO/C,IAAM,mBAAmB,GAAG,cAAc,CAAC;AAO3C,IAAM,qBAAqB,GAAG,gBAAgB,CAAC;AAO/C,IAAM,sBAAsB,GAAG,iBAAiB,CAAC;AAOjD,IAAM,kBAAkB,GAAG,aAAa,CAAC;AAOzC,IAAM,oBAAoB,GAAG,eAAe,CAAC;AAO7C,IAAM,sBAAsB,GAAG,iBAAiB,CAAC;AAOjD,IAAM,uBAAuB,GAAG,kBAAkB,CAAC;AAOnD,IAAM,sBAAsB,GAAG,iBAAiB,CAAC;AAOjD,IAAM,oBAAoB,GAAG,eAAe,CAAC;AAO7C,IAAM,oBAAoB,GAAG,eAAe,CAAC;AAS7C,IAAM,iBAAiB,GAAG,YAAY,CAAC;AAOvC,IAAM,oBAAoB,GAAG,eAAe,CAAC;AAS7C,IAAM,kBAAkB,GAAG,aAAa,CAAC;AAOzC,IAAM,oBAAoB,GAAG,eAAe,CAAC;AAO7C,IAAM,yBAAyB,GAAG,oBAAoB,CAAC;AASvD,IAAM,oBAAoB,GAAG,eAAe,CAAC;AAO7C,IAAM,wBAAwB,GAAG,mBAAmB,CAAC;AAOrD,IAAM,oCAAoC,GAC/C,+BAA+B,CAAC;AAO3B,IAAM,iDAAiD,GAC5D,4CAA4C,CAAC;AAOxC,IAAM,qCAAqC,GAChD,gCAAgC,CAAC;AAO5B,IAAM,kDAAkD,GAC7D,6CAA6C,CAAC;AASzC,IAAM,yBAAyB,GAAG,oBAAoB,CAAC;AAOvD,IAAM,mBAAmB,GAAG,cAAc,CAAC;AAmB3C,IAAM,uBAAuB,GAAG,kBAAkB,CAAC;AAOnD,IAAM,iCAAiC,GAAG,4BAA4B,CAAC;AAOvE,IAAM,uCAAuC,GAClD,kCAAkC,CAAC;AAO9B,IAAM,6CAA6C,GACxD,wCAAwC,CAAC;AAOpC,IAAM,+CAA+C,GAC1D,0CAA0C,CAAC;AAOtC,IAAM,gDAAgD,GAC3D,2CAA2C,CAAC;AAOvC,IAAM,qCAAqC,GAChD,gCAAgC,CAAC;AAO5B,IAAM,gCAAgC,GAAG,2BAA2B,CAAC;AAOrE,IAAM,2BAA2B,GAAG,sBAAsB,CAAC;AAO3D,IAAM,uCAAuC,GAClD,kCAAkC,CAAC;AAO9B,IAAM,gCAAgC,GAAG,2BAA2B,CAAC;AAOrE,IAAM,4BAA4B,GAAG,uBAAuB,CAAC;AAO7D,IAAM,8CAA8C,GACzD,yCAAyC,CAAC;AAOrC,IAAM,6CAA6C,GACxD,wCAAwC,CAAC;AAOpC,IAAM,2CAA2C,GACtD,sCAAsC,CAAC;AAOlC,IAAM,iCAAiC,GAAG,4BAA4B,CAAC;AAOvE,IAAM,kCAAkC,GAAG,6BAA6B,CAAC;AAOzE,IAAM,6BAA6B,GAAG,wBAAwB,CAAC;AAO/D,IAAM,oCAAoC,GAC/C,+BAA+B,CAAC;AAO3B,IAAM,2BAA2B,GAAG,sBAAsB,CAAC;AAO3D,IAAM,mCAAmC,GAC9C,8BAA8B,CAAC;AAO1B,IAAM,2CAA2C,GACtD,sCAAsC,CAAC;AAOlC,IAAM,oDAAoD,GAC/D,+CAA+C,CAAC;AAO3C,IAAM,yBAAyB,GAAG,oBAAoB,CAAC;AAOvD,IAAM,8BAA8B,GAAG,yBAAyB,CAAC;AAOjE,IAAM,mCAAmC,GAC9C,8BAA8B,CAAC;AAO1B,IAAM,mCAAmC,GAC9C,8BAA8B,CAAC;AAO1B,IAAM,2BAA2B,GAAG,sBAAsB,CAAC;AAO3D,IAAM,mCAAmC,GAC9C,8BAA8B,CAAC;AAO1B,IAAM,sBAAsB,GAAG,iBAAiB,CAAC;AAOjD,IAAM,6BAA6B,GAAG,wBAAwB,CAAC;AAO/D,IAAM,kCAAkC,GAAG,6BAA6B,CAAC;AAOzE,IAAM,6CAA6C,GACxD,wCAAwC,CAAC;AAOpC,IAAM,wDAAwD,GACnE,mDAAmD,CAAC;AAO/C,IAAM,4BAA4B,GAAG,uBAAuB,CAAC;AAO7D,IAAM,8BAA8B,GAAG,yBAAyB,CAAC;AAOjE,IAAM,uCAAuC,GAClD,kCAAkC,CAAC;AAS9B,IAAM,oCAAoC,GAC/C,+BAA+B,CAAC;AAO3B,IAAM,uCAAuC,GAClD,kCAAkC,CAAC;AAO9B,IAAM,kCAAkC,GAAG,6BAA6B,CAAC;AAOzE,IAAM,kCAAkC,GAAG,6BAA6B,CAAC;AAOzE,IAAM,kCAAkC,GAAG,6BAA6B,CAAC;AAOzE,IAAM,mBAAmB,GAAG,cAAc,CAAC;AAS3C,IAAM,oBAAoB,GAAG,eAAe,CAAC;AAS7C,IAAM,mBAAmB,GAAG,cAAc,CAAC;AAO3C,IAAM,6BAA6B,GAAG,wBAAwB,CAAC;AAO/D,IAAM,4BAA4B,GAAG,uBAAuB,CAAC;AAO7D,IAAM,+BAA+B,GAAG,0BAA0B,CAAC;AAOnE,IAAM,+BAA+B,GAAG,0BAA0B,CAAC;AAOnE,IAAM,kCAAkC,GAAG,6BAA6B,CAAC;AAOzE,IAAM,qBAAqB,GAAG,gBAAgB,CAAC;AAS/C,IAAM,mBAAmB,GAAG,cAAc,CAAC;AAO3C,IAAM,gCAAgC,GAAG,2BAA2B,CAAC;AAOrE,IAAM,kCAAkC,GAAG,6BAA6B,CAAC;AAgtBzE,IAAM,kBAAkB,GAC7B,WAAA,EAAa,mSAAC,kBAAA,AAAc,EAAqB;IAC/C,0BAA0B;IAC1B,aAAa;IACb,wBAAwB;IACxB,WAAW;IACX,4BAA4B;IAC5B,WAAW;IACX,gBAAgB;IAChB,gBAAgB;IAChB,0BAA0B;IAC1B,yBAAyB;IACzB,0BAA0B;IAC1B,kCAAkC;IAClC,sBAAsB;IACtB,4BAA4B;IAC5B,4CAA4C;IAC5C,+BAA+B;IAC/B,+BAA+B;IAC/B,sBAAsB;IACtB,2BAA2B;IAC3B,yBAAyB;IACzB,gBAAgB;IAChB,kBAAkB;IAClB,qBAAqB;IACrB,wBAAwB;IACxB,qBAAqB;IACrB,gBAAgB;IAChB,kBAAkB;IAClB,4BAA4B;IAC5B,2BAA2B;IAC3B,sBAAsB;IACtB,sBAAsB;IACtB,aAAa;IACb,aAAa;IACb,kBAAkB;IAClB,qBAAqB;IACrB,yBAAyB;IACzB,uBAAuB;IACvB,iBAAiB;IACjB,eAAe;IACf,iBAAiB;IACjB,iBAAiB;IACjB,eAAe;IACf,iBAAiB;IACjB,iBAAiB;IACjB,4BAA4B;IAC5B,+BAA+B;IAC/B,yBAAyB;IACzB,wBAAwB;IACxB,wBAAwB;IACxB,wBAAwB;IACxB,gBAAgB;IAChB,cAAc;IACd,gBAAgB;IAChB,iBAAiB;IACjB,aAAa;IACb,eAAe;IACf,iBAAiB;IACjB,kBAAkB;IAClB,iBAAiB;IACjB,eAAe;IACf,eAAe;IACf,YAAY;IACZ,eAAe;IACf,aAAa;IACb,eAAe;IACf,oBAAoB;IACpB,eAAe;IACf,mBAAmB;IACnB,+BAA+B;IAC/B,4CAA4C;IAC5C,gCAAgC;IAChC,6CAA6C;IAC7C,oBAAoB;IACpB,cAAc;IACd,kBAAkB;IAClB,4BAA4B;IAC5B,kCAAkC;IAClC,wCAAwC;IACxC,0CAA0C;IAC1C,2CAA2C;IAC3C,gCAAgC;IAChC,2BAA2B;IAC3B,sBAAsB;IACtB,kCAAkC;IAClC,2BAA2B;IAC3B,uBAAuB;IACvB,yCAAyC;IACzC,wCAAwC;IACxC,sCAAsC;IACtC,4BAA4B;IAC5B,6BAA6B;IAC7B,wBAAwB;IACxB,+BAA+B;IAC/B,sBAAsB;IACtB,8BAA8B;IAC9B,sCAAsC;IACtC,+CAA+C;IAC/C,oBAAoB;IACpB,yBAAyB;IACzB,8BAA8B;IAC9B,8BAA8B;IAC9B,sBAAsB;IACtB,8BAA8B;IAC9B,iBAAiB;IACjB,wBAAwB;IACxB,6BAA6B;IAC7B,wCAAwC;IACxC,mDAAmD;IACnD,uBAAuB;IACvB,yBAAyB;IACzB,kCAAkC;IAClC,+BAA+B;IAC/B,kCAAkC;IAClC,6BAA6B;IAC7B,6BAA6B;IAC7B,6BAA6B;IAC7B,cAAc;IACd,eAAe;IACf,cAAc;IACd,wBAAwB;IACxB,uBAAuB;IACvB,0BAA0B;IAC1B,0BAA0B;IAC1B,6BAA6B;IAC7B,gBAAgB;IAChB,cAAc;IACd,2BAA2B;IAC3B,6BAA6B;CAC9B,CAAC,CAAC;AAEL;;;;gHAIgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,4BAA4B,GAAG,WAAW,CAAC;AACjD,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,yBAAyB,GAAG,QAAQ,CAAC;AAC3C,IAAM,sBAAsB,GAAG,KAAK,CAAC;AACrC,IAAM,6BAA6B,GAAG,YAAY,CAAC;AACnD,IAAM,2BAA2B,GAAG,UAAU,CAAC;AAC/C,IAAM,uBAAuB,GAAG,MAAM,CAAC;AACvC,IAAM,6BAA6B,GAAG,YAAY,CAAC;AACnD,IAAM,yBAAyB,GAAG,QAAQ,CAAC;AAC3C,IAAM,2BAA2B,GAAG,UAAU,CAAC;AAC/C,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,yBAAyB,GAAG,QAAQ,CAAC;AAC3C,IAAM,yBAAyB,GAAG,QAAQ,CAAC;AAC3C,IAAM,2BAA2B,GAAG,UAAU,CAAC;AAC/C,IAAM,sBAAsB,GAAG,KAAK,CAAC;AACrC,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,yBAAyB,GAAG,QAAQ,CAAC;AAC3C,IAAM,2BAA2B,GAAG,UAAU,CAAC;AAC/C,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,4BAA4B,GAAG,WAAW,CAAC;AACjD,IAAM,2BAA2B,GAAG,UAAU,CAAC;AAC/C,IAAM,4BAA4B,GAAG,WAAW,CAAC;AACjD,IAAM,4BAA4B,GAAG,WAAW,CAAC;AACjD,IAAM,0BAA0B,GAAG,SAAS,CAAC;AAC7C,IAAM,0BAA0B,GAAG,SAAS,CAAC;AAC7C,IAAM,4BAA4B,GAAG,WAAW,CAAC;AACjD,IAAM,4BAA4B,GAAG,WAAW,CAAC;AACjD,IAAM,yBAAyB,GAAG,QAAQ,CAAC;AAC3C,IAAM,yBAAyB,GAAG,QAAQ,CAAC;AAC3C,IAAM,2BAA2B,GAAG,UAAU,CAAC;AAC/C,IAAM,0BAA0B,GAAG,SAAS,CAAC;AAC7C,IAAM,qBAAqB,GAAG,IAAI,CAAC;AACnC,IAAM,6BAA6B,GAAG,YAAY,CAAC;AACnD,IAAM,4BAA4B,GAAG,WAAW,CAAC;AACjD,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,0BAA0B,GAAG,SAAS,CAAC;AAC7C,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,4BAA4B,GAAG,WAAW,CAAC;AACjD,IAAM,0BAA0B,GAAG,SAAS,CAAC;AAC7C,IAAM,2BAA2B,GAAG,UAAU,CAAC;AAC/C,IAAM,2BAA2B,GAAG,UAAU,CAAC;AAC/C,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,gCAAgC,GAAG,eAAe,CAAC;AACzD,IAAM,4BAA4B,GAAG,WAAW,CAAC;AACjD,IAAM,8BAA8B,GAAG,aAAa,CAAC;AAO9C,IAAM,wBAAwB,GAAG,4BAA4B,CAAC;AAO9D,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,qBAAqB,GAAG,yBAAyB,CAAC;AAOxD,IAAM,kBAAkB,GAAG,sBAAsB,CAAC;AAOlD,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAOhE,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAO5D,IAAM,mBAAmB,GAAG,uBAAuB,CAAC;AAOpD,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAOhE,IAAM,qBAAqB,GAAG,yBAAyB,CAAC;AAOxD,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAO5D,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,qBAAqB,GAAG,yBAAyB,CAAC;AAOxD,IAAM,qBAAqB,GAAG,yBAAyB,CAAC;AAOxD,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAO5D,IAAM,kBAAkB,GAAG,sBAAsB,CAAC;AAOlD,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,qBAAqB,GAAG,yBAAyB,CAAC;AAOxD,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAO5D,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,wBAAwB,GAAG,4BAA4B,CAAC;AAO9D,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAO5D,IAAM,wBAAwB,GAAG,4BAA4B,CAAC;AAO9D,IAAM,wBAAwB,GAAG,4BAA4B,CAAC;AAO9D,IAAM,sBAAsB,GAAG,0BAA0B,CAAC;AAO1D,IAAM,sBAAsB,GAAG,0BAA0B,CAAC;AAO1D,IAAM,wBAAwB,GAAG,4BAA4B,CAAC;AAO9D,IAAM,wBAAwB,GAAG,4BAA4B,CAAC;AAO9D,IAAM,qBAAqB,GAAG,yBAAyB,CAAC;AAOxD,IAAM,qBAAqB,GAAG,yBAAyB,CAAC;AAOxD,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAO5D,IAAM,sBAAsB,GAAG,0BAA0B,CAAC;AAO1D,IAAM,iBAAiB,GAAG,qBAAqB,CAAC;AAOhD,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAOhE,IAAM,wBAAwB,GAAG,4BAA4B,CAAC;AAO9D,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,sBAAsB,GAAG,0BAA0B,CAAC;AAO1D,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,wBAAwB,GAAG,4BAA4B,CAAC;AAO9D,IAAM,sBAAsB,GAAG,0BAA0B,CAAC;AAO1D,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAO5D,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAO5D,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD,IAAM,4BAA4B,GAAG,gCAAgC,CAAC;AAOtE,IAAM,wBAAwB,GAAG,4BAA4B,CAAC;AAO9D,IAAM,0BAA0B,GAAG,8BAA8B,CAAC;AA2JlE,IAAM,cAAc,GACzB,WAAA,EAAa,oSAAC,iBAAA,AAAc,EAAiB;IAC3C,4BAA4B;IAC5B,wBAAwB;IACxB,wBAAwB;IACxB,yBAAyB;IACzB,sBAAsB;IACtB,6BAA6B;IAC7B,2BAA2B;IAC3B,uBAAuB;IACvB,6BAA6B;IAC7B,yBAAyB;IACzB,2BAA2B;IAC3B,wBAAwB;IACxB,yBAAyB;IACzB,yBAAyB;IACzB,2BAA2B;IAC3B,sBAAsB;IACtB,wBAAwB;IACxB,yBAAyB;IACzB,2BAA2B;IAC3B,wBAAwB;IACxB,4BAA4B;IAC5B,2BAA2B;IAC3B,4BAA4B;IAC5B,4BAA4B;IAC5B,0BAA0B;IAC1B,0BAA0B;IAC1B,4BAA4B;IAC5B,4BAA4B;IAC5B,yBAAyB;IACzB,yBAAyB;IACzB,2BAA2B;IAC3B,0BAA0B;IAC1B,qBAAqB;IACrB,6BAA6B;IAC7B,4BAA4B;IAC5B,wBAAwB;IACxB,0BAA0B;IAC1B,wBAAwB;IACxB,4BAA4B;IAC5B,0BAA0B;IAC1B,2BAA2B;IAC3B,2BAA2B;IAC3B,wBAAwB;IACxB,wBAAwB;IACxB,gCAAgC;IAChC,4BAA4B;IAC5B,8BAA8B;CAC/B,CAAC,CAAC;AAEL;;;;gHAIgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,yCAAyC,GAAG,KAAK,CAAC;AACxD,IAAM,iDAAiD,GAAG,aAAa,CAAC;AACxE,IAAM,4CAA4C,GAAG,QAAQ,CAAC;AAC9D,IAAM,kDAAkD,GAAG,cAAc,CAAC;AAC1E,IAAM,yCAAyC,GAAG,KAAK,CAAC;AACxD,IAAM,yCAAyC,GAAG,KAAK,CAAC;AACxD,IAAM,2CAA2C,GAAG,OAAO,CAAC;AAC5D,IAAM,+CAA+C,GAAG,WAAW,CAAC;AACpE,IAAM,yCAAyC,GAAG,KAAK,CAAC;AACxD,IAAM,4CAA4C,GAAG,QAAQ,CAAC;AAC9D,IAAM,kDAAkD,GAAG,cAAc,CAAC;AAOnE,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AAOrC,IAAM,6CAA6C,GACxD,iDAAiD,CAAC;AAO7C,IAAM,wCAAwC,GACnD,4CAA4C,CAAC;AAOxC,IAAM,8CAA8C,GACzD,kDAAkD,CAAC;AAO9C,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AAOrC,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AAOrC,IAAM,uCAAuC,GAClD,2CAA2C,CAAC;AAOvC,IAAM,2CAA2C,GACtD,+CAA+C,CAAC;AAO3C,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AAOrC,IAAM,wCAAwC,GACnD,4CAA4C,CAAC;AAOxC,IAAM,8CAA8C,GACzD,kDAAkD,CAAC;AA+C9C,IAAM,iCAAiC,GAC5C,WAAA,EAAa,KAAC,gTAAA,AAAc,EAAoC;IAC9D,yCAAyC;IACzC,iDAAiD;IACjD,4CAA4C;IAC5C,kDAAkD;IAClD,yCAAyC;IACzC,yCAAyC;IACzC,2CAA2C;IAC3C,+CAA+C;IAC/C,yCAAyC;IACzC,4CAA4C;IAC5C,kDAAkD;CACnD,CAAC,CAAC;AAEL;;;;gHAIgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,gCAAgC,GAAG,YAAY,CAAC;AACtD,IAAM,0BAA0B,GAAG,MAAM,CAAC;AAC1C,IAAM,4BAA4B,GAAG,QAAQ,CAAC;AAC9C,IAAM,2BAA2B,GAAG,OAAO,CAAC;AAC5C,IAAM,2BAA2B,GAAG,OAAO,CAAC;AAOrC,IAAM,4BAA4B,GAAG,gCAAgC,CAAC;AAOtE,IAAM,sBAAsB,GAAG,0BAA0B,CAAC;AAO1D,IAAM,wBAAwB,GAAG,4BAA4B,CAAC;AAO9D,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAO5D,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AA6B5D,IAAM,iBAAiB,GAC5B,WAAA,EAAa,oSAAC,iBAAA,AAAc,EAAoB;IAC9C,gCAAgC;IAChC,0BAA0B;IAC1B,4BAA4B;IAC5B,2BAA2B;IAC3B,2BAA2B;CAC5B,CAAC,CAAC;AAEL;;;;gHAIgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,sCAAsC,GAAG,QAAQ,CAAC;AACxD,IAAM,oCAAoC,GAAG,MAAM,CAAC;AACpD,IAAM,sCAAsC,GAAG,QAAQ,CAAC;AAOjD,IAAM,kCAAkC,GAC7C,sCAAsC,CAAC;AAOlC,IAAM,gCAAgC,GAC3C,oCAAoC,CAAC;AAOhC,IAAM,kCAAkC,GAC7C,sCAAsC,CAAC;AAuBlC,IAAM,2BAA2B,GACtC,WAAA,EAAa,KAAC,gTAAA,AAAc,EAA8B;IACxD,sCAAsC;IACtC,oCAAoC;IACpC,sCAAsC;CACvC,CAAC,CAAC;AAEL;;;;;;gHAMgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,2CAA2C,GAAG,eAAe,CAAC;AACpE,IAAM,iCAAiC,GAAG,KAAK,CAAC;AAChD,IAAM,mCAAmC,GAAG,OAAO,CAAC;AACpD,IAAM,iCAAiC,GAAG,KAAK,CAAC;AASzC,IAAM,uCAAuC,GAClD,2CAA2C,CAAC;AASvC,IAAM,6BAA6B,GAAG,iCAAiC,CAAC;AASxE,IAAM,+BAA+B,GAC1C,mCAAmC,CAAC;AAS/B,IAAM,6BAA6B,GAAG,iCAAiC,CAAC;AA4BxE,IAAM,yBAAyB,GACpC,WAAA,EAAa,oSAAC,iBAAA,AAAc,EAA4B;IACtD,2CAA2C;IAC3C,iCAAiC;IACjC,mCAAmC;IACnC,iCAAiC;CAClC,CAAC,CAAC;AAEL;;;;gHAIgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,6BAA6B,GAAG,QAAQ,CAAC;AAC/C,IAAM,6BAA6B,GAAG,QAAQ,CAAC;AAC/C,IAAM,yBAAyB,GAAG,IAAI,CAAC;AACvC,IAAM,2BAA2B,GAAG,MAAM,CAAC;AAC3C,IAAM,2BAA2B,GAAG,MAAM,CAAC;AAC3C,IAAM,6BAA6B,GAAG,QAAQ,CAAC;AAC/C,IAAM,4BAA4B,GAAG,OAAO,CAAC;AAOtC,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAOhE,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAOhE,IAAM,qBAAqB,GAAG,yBAAyB,CAAC;AAOxD,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAO5D,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAO5D,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAOhE,IAAM,wBAAwB,GAAG,4BAA4B,CAAC;AAmC9D,IAAM,kBAAkB,GAC7B,WAAA,EAAa,oSAAC,iBAAA,AAAc,EAAqB;IAC/C,6BAA6B;IAC7B,6BAA6B;IAC7B,yBAAyB;IACzB,2BAA2B;IAC3B,2BAA2B;IAC3B,6BAA6B;IAC7B,4BAA4B;CAC7B,CAAC,CAAC;AAEL;;;;gHAIgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,oCAAoC,GAAG,MAAM,CAAC;AACpD,IAAM,qCAAqC,GAAG,OAAO,CAAC;AACtD,IAAM,oCAAoC,GAAG,MAAM,CAAC;AACpD,IAAM,2CAA2C,GAAG,aAAa,CAAC;AAClE,IAAM,uCAAuC,GAAG,SAAS,CAAC;AAOnD,IAAM,gCAAgC,GAC3C,oCAAoC,CAAC;AAOhC,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAOjC,IAAM,gCAAgC,GAC3C,oCAAoC,CAAC;AAOhC,IAAM,uCAAuC,GAClD,2CAA2C,CAAC;AAOvC,IAAM,mCAAmC,GAC9C,uCAAuC,CAAC;AA6BnC,IAAM,2BAA2B,GACtC,WAAA,EAAa,oSAAC,iBAAA,AAAc,EAA8B;IACxD,oCAAoC;IACpC,qCAAqC;IACrC,oCAAoC;IACpC,2CAA2C;IAC3C,uCAAuC;CACxC,CAAC,CAAC;AAEL;;;;gHAIgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,uCAAuC,GAAG,MAAM,CAAC;AACvD,IAAM,uCAAuC,GAAG,MAAM,CAAC;AACvD,IAAM,uCAAuC,GAAG,MAAM,CAAC;AACvD,IAAM,uCAAuC,GAAG,MAAM,CAAC;AACvD,IAAM,yCAAyC,GAAG,QAAQ,CAAC;AAC3D,IAAM,yCAAyC,GAAG,QAAQ,CAAC;AAC3D,IAAM,iDAAiD,GAAG,gBAAgB,CAAC;AAC3E,IAAM,wCAAwC,GAAG,OAAO,CAAC;AACzD,IAAM,wCAAwC,GAAG,OAAO,CAAC;AACzD,IAAM,uCAAuC,GAAG,MAAM,CAAC;AACvD,IAAM,uCAAuC,GAAG,MAAM,CAAC;AACvD,IAAM,yCAAyC,GAAG,QAAQ,CAAC;AAC3D,IAAM,sCAAsC,GAAG,KAAK,CAAC;AACrD,IAAM,wCAAwC,GAAG,OAAO,CAAC;AACzD,IAAM,wCAAwC,GAAG,OAAO,CAAC;AACzD,IAAM,sCAAsC,GAAG,KAAK,CAAC;AACrD,IAAM,2CAA2C,GAAG,UAAU,CAAC;AAC/D,IAAM,wCAAwC,GAAG,OAAO,CAAC;AACzD,IAAM,qCAAqC,GAAG,IAAI,CAAC;AACnD,IAAM,wCAAwC,GAAG,OAAO,CAAC;AACzD,IAAM,yCAAyC,GAAG,QAAQ,CAAC;AAOpD,IAAM,mCAAmC,GAC9C,uCAAuC,CAAC;AAOnC,IAAM,mCAAmC,GAC9C,uCAAuC,CAAC;AAOnC,IAAM,mCAAmC,GAC9C,uCAAuC,CAAC;AAOnC,IAAM,mCAAmC,GAC9C,uCAAuC,CAAC;AAOnC,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AAOrC,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AAOrC,IAAM,6CAA6C,GACxD,iDAAiD,CAAC;AAO7C,IAAM,oCAAoC,GAC/C,wCAAwC,CAAC;AAOpC,IAAM,oCAAoC,GAC/C,wCAAwC,CAAC;AAOpC,IAAM,mCAAmC,GAC9C,uCAAuC,CAAC;AAOnC,IAAM,mCAAmC,GAC9C,uCAAuC,CAAC;AAOnC,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AAOrC,IAAM,kCAAkC,GAC7C,sCAAsC,CAAC;AAOlC,IAAM,oCAAoC,GAC/C,wCAAwC,CAAC;AAOpC,IAAM,oCAAoC,GAC/C,wCAAwC,CAAC;AAOpC,IAAM,kCAAkC,GAC7C,sCAAsC,CAAC;AAOlC,IAAM,uCAAuC,GAClD,2CAA2C,CAAC;AAOvC,IAAM,oCAAoC,GAC/C,wCAAwC,CAAC;AAOpC,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAOjC,IAAM,oCAAoC,GAC/C,wCAAwC,CAAC;AAOpC,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AA6ErC,IAAM,8BAA8B,GACzC,WAAA,EAAa,KAAC,gTAAA,AAAc,EAAiC;IAC3D,uCAAuC;IACvC,uCAAuC;IACvC,uCAAuC;IACvC,uCAAuC;IACvC,yCAAyC;IACzC,yCAAyC;IACzC,iDAAiD;IACjD,wCAAwC;IACxC,wCAAwC;IACxC,uCAAuC;IACvC,uCAAuC;IACvC,yCAAyC;IACzC,sCAAsC;IACtC,wCAAwC;IACxC,wCAAwC;IACxC,sCAAsC;IACtC,2CAA2C;IAC3C,wCAAwC;IACxC,qCAAqC;IACrC,wCAAwC;IACxC,yCAAyC;CAC1C,CAAC,CAAC;AAEL;;;;;;gHAMgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,6BAA6B,GAAG,KAAK,CAAC;AAC5C,IAAM,6BAA6B,GAAG,KAAK,CAAC;AAC5C,IAAM,6BAA6B,GAAG,KAAK,CAAC;AAC5C,IAAM,yBAAyB,GAAG,MAAM,CAAC;AACzC,IAAM,yBAAyB,GAAG,MAAM,CAAC;AASlC,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAShE,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAShE,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAShE,IAAM,qBAAqB,GAAG,yBAAyB,CAAC;AASxD,IAAM,qBAAqB,GAAG,yBAAyB,CAAC;AA+BxD,IAAM,gBAAgB,GAAqB;IAChD,QAAQ,EAAE,6BAA6B;IACvC,QAAQ,EAAE,6BAA6B;IACvC,QAAQ,EAAE,6BAA6B;IACvC,IAAI,EAAE,yBAAyB;IAC/B,IAAI,EAAE,yBAAyB;CAChC,CAAC;AAEF;;;;gHAIgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,wCAAwC,GAAG,OAAO,CAAC;AACzD,IAAM,wCAAwC,GAAG,OAAO,CAAC;AAOlD,IAAM,oCAAoC,GAC/C,wCAAwC,CAAC;AAOpC,IAAM,oCAAoC,GAC/C,wCAAwC,CAAC;AAoBpC,IAAM,8BAA8B,GACzC,WAAA,EAAa,oSAAC,iBAAA,AAAc,EAAiC;IAC3D,wCAAwC;IACxC,wCAAwC;CACzC,CAAC,CAAC;AAEL;;;;gHAIgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,oCAAoC,GAAG,SAAS,CAAC;AACvD,IAAM,oCAAoC,GAAG,SAAS,CAAC;AAOhD,IAAM,gCAAgC,GAC3C,oCAAoC,CAAC;AAOhC,IAAM,gCAAgC,GAC3C,oCAAoC,CAAC;AAoBhC,IAAM,wBAAwB,GACnC,WAAA,EAAa,oSAAC,iBAAA,AAAc,EAA2B;IACrD,oCAAoC;IACpC,oCAAoC;CACrC,CAAC,CAAC;AAEL;;;;gHAIgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,8BAA8B,GAAG,CAAC,CAAC;AACzC,IAAM,qCAAqC,GAAG,CAAC,CAAC;AAChD,IAAM,mCAAmC,GAAG,CAAC,CAAC;AAC9C,IAAM,4CAA4C,GAAG,CAAC,CAAC;AACvD,IAAM,6CAA6C,GAAG,CAAC,CAAC;AACxD,IAAM,qCAAqC,GAAG,CAAC,CAAC;AAChD,IAAM,0CAA0C,GAAG,CAAC,CAAC;AACrD,IAAM,6CAA6C,GAAG,CAAC,CAAC;AACxD,IAAM,8CAA8C,GAAG,CAAC,CAAC;AACzD,IAAM,+CAA+C,GAAG,CAAC,CAAC;AAC1D,IAAM,mCAAmC,GAAG,EAAE,CAAC;AAC/C,IAAM,wCAAwC,GAAG,EAAE,CAAC;AACpD,IAAM,yCAAyC,GAAG,EAAE,CAAC;AACrD,IAAM,oCAAoC,GAAG,EAAE,CAAC;AAChD,IAAM,uCAAuC,GAAG,EAAE,CAAC;AACnD,IAAM,qCAAqC,GAAG,EAAE,CAAC;AACjD,IAAM,2CAA2C,GAAG,EAAE,CAAC;AAOhD,IAAM,0BAA0B,GAAG,8BAA8B,CAAC;AAOlE,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAOjC,IAAM,+BAA+B,GAC1C,mCAAmC,CAAC;AAO/B,IAAM,wCAAwC,GACnD,4CAA4C,CAAC;AAOxC,IAAM,yCAAyC,GACpD,6CAA6C,CAAC;AAOzC,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAOjC,IAAM,sCAAsC,GACjD,0CAA0C,CAAC;AAOtC,IAAM,yCAAyC,GACpD,6CAA6C,CAAC;AAOzC,IAAM,0CAA0C,GACrD,8CAA8C,CAAC;AAO1C,IAAM,2CAA2C,GACtD,+CAA+C,CAAC;AAO3C,IAAM,+BAA+B,GAC1C,mCAAmC,CAAC;AAO/B,IAAM,oCAAoC,GAC/C,wCAAwC,CAAC;AAOpC,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AAOrC,IAAM,gCAAgC,GAC3C,oCAAoC,CAAC;AAOhC,IAAM,mCAAmC,GAC9C,uCAAuC,CAAC;AAOnC,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAOjC,IAAM,uCAAuC,GAClD,2CAA2C,CAAC;AAiEvC,IAAM,uBAAuB,GAA4B;IAC9D,EAAE,EAAE,8BAA8B;IAClC,SAAS,EAAE,qCAAqC;IAChD,OAAO,EAAE,mCAAmC;IAC5C,gBAAgB,EAAE,4CAA4C;IAC9D,iBAAiB,EAAE,6CAA6C;IAChE,SAAS,EAAE,qCAAqC;IAChD,cAAc,EAAE,0CAA0C;IAC1D,iBAAiB,EAAE,6CAA6C;IAChE,kBAAkB,EAAE,8CAA8C;IAClE,mBAAmB,EAAE,+CAA+C;IACpE,OAAO,EAAE,mCAAmC;IAC5C,YAAY,EAAE,wCAAwC;IACtD,aAAa,EAAE,yCAAyC;IACxD,QAAQ,EAAE,oCAAoC;IAC9C,WAAW,EAAE,uCAAuC;IACpD,SAAS,EAAE,qCAAqC;IAChD,eAAe,EAAE,2CAA2C;CAC7D,CAAC;AAEF;;;;gHAIgH,CAEhH,2FAA2F;AAC3F,qGAAqG;AACrG,IAAM,0BAA0B,GAAG,MAAM,CAAC;AAC1C,IAAM,8BAA8B,GAAG,UAAU,CAAC;AAO3C,IAAM,sBAAsB,GAAG,0BAA0B,CAAC;AAO1D,IAAM,0BAA0B,GAAG,8BAA8B,CAAC;AAoBlE,IAAM,iBAAiB,GAC5B,WAAA,EAAa,oSAAC,iBAAA,AAAc,EAAoB;IAC9C,0BAA0B;IAC1B,8BAA8B;CAC/B,CAAC,CAAC", "ignoreList": [0], "debugId": null}}]}