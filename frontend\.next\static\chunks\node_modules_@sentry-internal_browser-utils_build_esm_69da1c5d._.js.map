{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "types.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/types.ts"], "sourcesContent": ["import type {\n  FetchBreadcrumbHint,\n  <PERSON>lerDataF<PERSON>ch,\n  SentryWrappedXMLHttpRequest,\n  XhrBreadcrumbHint,\n} from '@sentry/core';\nimport { GLOBAL_OBJ } from '@sentry/core';\n\nexport const WINDOW = GLOBAL_OBJ as typeof GLOBAL_OBJ &\n  // document is not available in all browser environments (webworkers). We make it optional so you have to explicitly check for it\n  Omit<Window, 'document'> &\n  Partial<Pick<Window, 'document'>>;\n\nexport type NetworkMetaWarning =\n  | 'MAYBE_JSON_TRUNCATED'\n  | 'TEXT_TRUNCATED'\n  | 'URL_SKIPPED'\n  | 'BODY_PARSE_ERROR'\n  | 'BODY_PARSE_TIMEOUT'\n  | 'UNPARSEABLE_BODY_TYPE';\n\ntype RequestBody = null | Blob | BufferSource | FormData | URLSearchParams | string;\n\nexport type XhrHint = XhrBreadcrumbHint & {\n  xhr: XMLHttpRequest & SentryWrappedXMLHttpRequest;\n  input?: RequestBody;\n};\nexport type FetchHint = FetchBreadcrumbHint & {\n  input: HandlerDataFetch['args'];\n  response: Response;\n};\n"], "names": [], "mappings": ";;;;;AAQO,MAAM,MAAO,sLAAE,aAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "file": "dom.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/instrument/dom.ts"], "sourcesContent": ["import type { HandlerDataDom } from '@sentry/core';\nimport { addHandler, addNonEnumerableProperty, fill, maybeInstrument, triggerHandlers, uuid4 } from '@sentry/core';\nimport { WINDOW } from '../types';\n\ntype SentryWrappedTarget = HTMLElement & { _sentryId?: string };\n\ntype AddEventListener = (\n  type: string,\n  listener: EventListenerOrEventListenerObject,\n  options?: boolean | AddEventListenerOptions,\n) => void;\ntype RemoveEventListener = (\n  type: string,\n  listener: EventListenerOrEventListenerObject,\n  options?: boolean | EventListenerOptions,\n) => void;\n\ntype InstrumentedElement = Element & {\n  __sentry_instrumentation_handlers__?: {\n    [key in 'click' | 'keypress']?: {\n      handler?: unknown;\n      /** The number of custom listeners attached to this element */\n      refCount: number;\n    };\n  };\n};\n\nconst DEBOUNCE_DURATION = 1000;\n\nlet debounceTimerID: number | undefined;\nlet lastCapturedEventType: string | undefined;\nlet lastCapturedEventTargetId: string | undefined;\n\n/**\n * Add an instrumentation handler for when a click or a keypress happens.\n *\n * Use at your own risk, this might break without changelog notice, only used internally.\n * @hidden\n */\nexport function addClickKeypressInstrumentationHandler(handler: (data: HandlerDataDom) => void): void {\n  const type = 'dom';\n  addHandler(type, handler);\n  maybeInstrument(type, instrumentDOM);\n}\n\n/** Exported for tests only. */\nexport function instrumentDOM(): void {\n  if (!WINDOW.document) {\n    return;\n  }\n\n  // Make it so that any click or keypress that is unhandled / bubbled up all the way to the document triggers our dom\n  // handlers. (Normally we have only one, which captures a breadcrumb for each click or keypress.) Do this before\n  // we instrument `addEventListener` so that we don't end up attaching this handler twice.\n  const triggerDOMHandler = triggerHandlers.bind(null, 'dom');\n  const globalDOMEventHandler = makeDOMEventHandler(triggerDOMHandler, true);\n  WINDOW.document.addEventListener('click', globalDOMEventHandler, false);\n  WINDOW.document.addEventListener('keypress', globalDOMEventHandler, false);\n\n  // After hooking into click and keypress events bubbled up to `document`, we also hook into user-handled\n  // clicks & keypresses, by adding an event listener of our own to any element to which they add a listener. That\n  // way, whenever one of their handlers is triggered, ours will be, too. (This is needed because their handler\n  // could potentially prevent the event from bubbling up to our global listeners. This way, our handler are still\n  // guaranteed to fire at least once.)\n  ['EventTarget', 'Node'].forEach((target: string) => {\n    const globalObject = WINDOW as unknown as Record<string, { prototype?: object }>;\n    const proto = globalObject[target]?.prototype;\n\n    // eslint-disable-next-line no-prototype-builtins\n    if (!proto?.hasOwnProperty?.('addEventListener')) {\n      return;\n    }\n\n    fill(proto, 'addEventListener', function (originalAddEventListener: AddEventListener): AddEventListener {\n      return function (this: InstrumentedElement, type, listener, options): AddEventListener {\n        if (type === 'click' || type == 'keypress') {\n          try {\n            const handlers = (this.__sentry_instrumentation_handlers__ =\n              this.__sentry_instrumentation_handlers__ || {});\n            const handlerForType = (handlers[type] = handlers[type] || { refCount: 0 });\n\n            if (!handlerForType.handler) {\n              const handler = makeDOMEventHandler(triggerDOMHandler);\n              handlerForType.handler = handler;\n              originalAddEventListener.call(this, type, handler, options);\n            }\n\n            handlerForType.refCount++;\n          } catch (e) {\n            // Accessing dom properties is always fragile.\n            // Also allows us to skip `addEventListeners` calls with no proper `this` context.\n          }\n        }\n\n        return originalAddEventListener.call(this, type, listener, options);\n      };\n    });\n\n    fill(\n      proto,\n      'removeEventListener',\n      function (originalRemoveEventListener: RemoveEventListener): RemoveEventListener {\n        return function (this: InstrumentedElement, type, listener, options): () => void {\n          if (type === 'click' || type == 'keypress') {\n            try {\n              const handlers = this.__sentry_instrumentation_handlers__ || {};\n              const handlerForType = handlers[type];\n\n              if (handlerForType) {\n                handlerForType.refCount--;\n                // If there are no longer any custom handlers of the current type on this element, we can remove ours, too.\n                if (handlerForType.refCount <= 0) {\n                  originalRemoveEventListener.call(this, type, handlerForType.handler, options);\n                  handlerForType.handler = undefined;\n                  delete handlers[type]; // eslint-disable-line @typescript-eslint/no-dynamic-delete\n                }\n\n                // If there are no longer any custom handlers of any type on this element, cleanup everything.\n                if (Object.keys(handlers).length === 0) {\n                  delete this.__sentry_instrumentation_handlers__;\n                }\n              }\n            } catch (e) {\n              // Accessing dom properties is always fragile.\n              // Also allows us to skip `addEventListeners` calls with no proper `this` context.\n            }\n          }\n\n          return originalRemoveEventListener.call(this, type, listener, options);\n        };\n      },\n    );\n  });\n}\n\n/**\n * Check whether the event is similar to the last captured one. For example, two click events on the same button.\n */\nfunction isSimilarToLastCapturedEvent(event: Event): boolean {\n  // If both events have different type, then user definitely performed two separate actions. e.g. click + keypress.\n  if (event.type !== lastCapturedEventType) {\n    return false;\n  }\n\n  try {\n    // If both events have the same type, it's still possible that actions were performed on different targets.\n    // e.g. 2 clicks on different buttons.\n    if (!event.target || (event.target as SentryWrappedTarget)._sentryId !== lastCapturedEventTargetId) {\n      return false;\n    }\n  } catch (e) {\n    // just accessing `target` property can throw an exception in some rare circumstances\n    // see: https://github.com/getsentry/sentry-javascript/issues/838\n  }\n\n  // If both events have the same type _and_ same `target` (an element which triggered an event, _not necessarily_\n  // to which an event listener was attached), we treat them as the same action, as we want to capture\n  // only one breadcrumb. e.g. multiple clicks on the same button, or typing inside a user input box.\n  return true;\n}\n\n/**\n * Decide whether an event should be captured.\n * @param event event to be captured\n */\nfunction shouldSkipDOMEvent(eventType: string, target: SentryWrappedTarget | null): boolean {\n  // We are only interested in filtering `keypress` events for now.\n  if (eventType !== 'keypress') {\n    return false;\n  }\n\n  if (!target?.tagName) {\n    return true;\n  }\n\n  // Only consider keypress events on actual input elements. This will disregard keypresses targeting body\n  // e.g.tabbing through elements, hotkeys, etc.\n  if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {\n    return false;\n  }\n\n  return true;\n}\n\n/**\n * Wraps addEventListener to capture UI breadcrumbs\n */\nfunction makeDOMEventHandler(\n  handler: (data: HandlerDataDom) => void,\n  globalListener: boolean = false,\n): (event: Event) => void {\n  return (event: Event & { _sentryCaptured?: true }): void => {\n    // It's possible this handler might trigger multiple times for the same\n    // event (e.g. event propagation through node ancestors).\n    // Ignore if we've already captured that event.\n    if (!event || event['_sentryCaptured']) {\n      return;\n    }\n\n    const target = getEventTarget(event);\n\n    // We always want to skip _some_ events.\n    if (shouldSkipDOMEvent(event.type, target)) {\n      return;\n    }\n\n    // Mark event as \"seen\"\n    addNonEnumerableProperty(event, '_sentryCaptured', true);\n\n    if (target && !target._sentryId) {\n      // Add UUID to event target so we can identify if\n      addNonEnumerableProperty(target, '_sentryId', uuid4());\n    }\n\n    const name = event.type === 'keypress' ? 'input' : event.type;\n\n    // If there is no last captured event, it means that we can safely capture the new event and store it for future comparisons.\n    // If there is a last captured event, see if the new event is different enough to treat it as a unique one.\n    // If that's the case, emit the previous event and store locally the newly-captured DOM event.\n    if (!isSimilarToLastCapturedEvent(event)) {\n      const handlerData: HandlerDataDom = { event, name, global: globalListener };\n      handler(handlerData);\n      lastCapturedEventType = event.type;\n      lastCapturedEventTargetId = target ? target._sentryId : undefined;\n    }\n\n    // Start a new debounce timer that will prevent us from capturing multiple events that should be grouped together.\n    clearTimeout(debounceTimerID);\n    debounceTimerID = WINDOW.setTimeout(() => {\n      lastCapturedEventTargetId = undefined;\n      lastCapturedEventType = undefined;\n    }, DEBOUNCE_DURATION);\n  };\n}\n\nfunction getEventTarget(event: Event): SentryWrappedTarget | null {\n  try {\n    return event.target as SentryWrappedTarget | null;\n  } catch (e) {\n    // just accessing `target` property can throw an exception in some rare circumstances\n    // see: https://github.com/getsentry/sentry-javascript/issues/838\n    return null;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AA2BA,MAAM,iBAAA,GAAoB,IAAI;AAE9B,IAAI,eAAe;AACnB,IAAI,qBAAqB;AACzB,IAAI,yBAAyB;AAE7B;;;;;CAKA,GACO,SAAS,sCAAsC,CAAC,OAAO,EAAwC;IACpG,MAAM,IAAK,GAAE,KAAK;wMAClB,aAAA,AAAU,EAAC,IAAI,EAAE,OAAO,CAAC;wMACzB,kBAAA,AAAe,EAAC,IAAI,EAAE,aAAa,CAAC;AACtC;AAEA,6BAAA,GACO,SAAS,aAAa,GAAS;IACpC,IAAI,sLAAC,SAAM,CAAC,QAAQ,EAAE;QACpB;IACJ;IAEA,oHAAA;IACA,gHAAA;IACA,yFAAA;IACE,MAAM,iBAAkB,mMAAE,kBAAe,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC;IAC3D,MAAM,wBAAwB,mBAAmB,CAAC,iBAAiB,EAAE,IAAI,CAAC;yLAC1E,SAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,qBAAqB,EAAE,KAAK,CAAC;yLACvE,SAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,UAAU,EAAE,qBAAqB,EAAE,KAAK,CAAC;IAE5E,wGAAA;IACA,gHAAA;IACA,6GAAA;IACA,gHAAA;IACA,qCAAA;IACE;QAAC,aAAa;QAAE,MAAM;KAAC,CAAC,OAAO,CAAC,CAAC,MAAM,KAAa;QAClD,MAAM,YAAa,wLAAE,SAAO;QAC5B,MAAM,QAAQ,YAAY,CAAC,MAAM,CAAC,EAAE,SAAS;QAEjD,iDAAA;QACI,IAAI,CAAC,KAAK,EAAE,cAAc,GAAG,kBAAkB,CAAC,EAAE;YAChD;QACN;4LAEI,OAAA,AAAI,EAAC,KAAK,EAAE,kBAAkB,EAAE,SAAU,wBAAwB,EAAsC;YACtG,OAAO,SAAqC,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAoB;gBACrF,IAAI,IAAK,KAAI,WAAW,IAAA,IAAQ,UAAU,EAAE;oBAC1C,IAAI;wBACF,MAAM,QAAS,GAAG,IAAI,CAAC,mCAAoC,GACzD,IAAI,CAAC,mCAAA,IAAuC,CAAA,CAAE,CAAC;wBACjD,MAAM,cAAe,GAAG,QAAQ,CAAC,IAAI,CAAE,GAAE,QAAQ,CAAC,IAAI,CAAA,IAAK;4BAAE,QAAQ,EAAE,CAAA;wBAAA,CAAG,CAAC;wBAE3E,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;4BAC3B,MAAM,OAAQ,GAAE,mBAAmB,CAAC,iBAAiB,CAAC;4BACtD,cAAc,CAAC,OAAQ,GAAE,OAAO;4BAChC,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC;wBACzE;wBAEY,cAAc,CAAC,QAAQ,EAAE;oBACrC,CAAY,CAAA,OAAO,CAAC,EAAE;oBACtB,8CAAA;oBACA,kFAAA;oBACA;gBACA;gBAEQ,OAAO,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC;YAC3E,CAAO;QACP,CAAK,CAAC;YAEF,uLAAA,AAAI,EACF,KAAK,EACL,qBAAqB,EACrB,SAAU,2BAA2B,EAA4C;YAC/E,OAAO,SAAqC,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAc;gBAC/E,IAAI,IAAK,KAAI,WAAW,IAAA,IAAQ,UAAU,EAAE;oBAC1C,IAAI;wBACF,MAAM,WAAW,IAAI,CAAC,mCAAoC,IAAG,CAAA,CAAE;wBAC/D,MAAM,cAAe,GAAE,QAAQ,CAAC,IAAI,CAAC;wBAErC,IAAI,cAAc,EAAE;4BAClB,cAAc,CAAC,QAAQ,EAAE;4BACzC,2GAAA;4BACgB,IAAI,cAAc,CAAC,QAAS,IAAG,CAAC,EAAE;gCAChC,2BAA2B,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC;gCAC7E,cAAc,CAAC,OAAQ,GAAE,SAAS;gCAClC,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAA,CAAA,2DAAA;4BACvC;4BAEA,8FAAA;4BACgB,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAA,KAAW,CAAC,EAAE;gCACtC,OAAO,IAAI,CAAC,mCAAmC;4BACjE;wBACA;oBACA,CAAc,CAAA,OAAO,CAAC,EAAE;oBACxB,8CAAA;oBACA,kFAAA;oBACA;gBACA;gBAEU,OAAO,2BAA2B,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC;YAChF,CAAS;QACT,CAAO;IAEP,CAAG,CAAC;AACJ;AAEA;;CAEA,GACA,SAAS,4BAA4B,CAAC,KAAK,EAAkB;IAC7D,kHAAA;IACE,IAAI,KAAK,CAAC,IAAK,KAAI,qBAAqB,EAAE;QACxC,OAAO,KAAK;IAChB;IAEE,IAAI;QACN,2GAAA;QACA,sCAAA;QACI,IAAI,CAAC,KAAK,CAAC,MAAA,IAAU,AAAC,KAAK,CAAC,MAAA,CAA+B,SAAU,KAAI,yBAAyB,EAAE;YAClG,OAAO,KAAK;QAClB;IACA,CAAI,CAAA,OAAO,CAAC,EAAE;IACd,qFAAA;IACA,iEAAA;IACA;IAEA,gHAAA;IACA,oGAAA;IACA,mGAAA;IACE,OAAO,IAAI;AACb;AAEA;;;CAGA,GACA,SAAS,kBAAkB,CAAC,SAAS,EAAU,MAAM,EAAuC;IAC5F,iEAAA;IACE,IAAI,SAAU,KAAI,UAAU,EAAE;QAC5B,OAAO,KAAK;IAChB;IAEE,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE;QACpB,OAAO,IAAI;IACf;IAEA,wGAAA;IACA,8CAAA;IACE,IAAI,MAAM,CAAC,OAAA,KAAY,OAAQ,IAAG,MAAM,CAAC,OAAA,KAAY,UAAA,IAAc,MAAM,CAAC,iBAAiB,EAAE;QAC3F,OAAO,KAAK;IAChB;IAEE,OAAO,IAAI;AACb;AAEA;;CAEA,GACA,SAAS,mBAAmB,CAC1B,OAAO,EACP,cAAc,GAAY,KAAK;IAE/B,OAAO,CAAC,KAAK,KAA+C;QAC9D,uEAAA;QACA,yDAAA;QACA,+CAAA;QACI,IAAI,CAAC,KAAA,IAAS,KAAK,CAAC,iBAAiB,CAAC,EAAE;YACtC;QACN;QAEI,MAAM,MAAO,GAAE,cAAc,CAAC,KAAK,CAAC;QAExC,wCAAA;QACI,IAAI,kBAAkB,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE;YAC1C;QACN;QAEA,uBAAA;QACI,+MAAA,AAAwB,EAAC,KAAK,EAAE,iBAAiB,EAAE,IAAI,CAAC;QAExD,IAAI,MAAO,IAAG,CAAC,MAAM,CAAC,SAAS,EAAE;YACrC,iDAAA;aACM,8MAAA,AAAwB,EAAC,MAAM,EAAE,WAAW,oLAAE,QAAA,AAAK,EAAE,CAAC;QAC5D;QAEI,MAAM,IAAA,GAAO,KAAK,CAAC,IAAA,KAAS,UAAA,GAAa,OAAA,GAAU,KAAK,CAAC,IAAI;QAEjE,6HAAA;QACA,2GAAA;QACA,8FAAA;QACI,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,EAAE;YACxC,MAAM,WAAW,GAAmB;gBAAE,KAAK;gBAAE,IAAI;gBAAE,MAAM,EAAE,cAAA;YAAA,CAAgB;YAC3E,OAAO,CAAC,WAAW,CAAC;YACpB,qBAAsB,GAAE,KAAK,CAAC,IAAI;YAClC,yBAAA,GAA4B,MAAO,GAAE,MAAM,CAAC,SAAA,GAAY,SAAS;QACvE;QAEA,kHAAA;QACI,YAAY,CAAC,eAAe,CAAC;QAC7B,eAAA,wLAAkB,SAAM,CAAC,UAAU,CAAC,MAAM;YACxC,yBAAA,GAA4B,SAAS;YACrC,qBAAA,GAAwB,SAAS;QACvC,CAAK,EAAE,iBAAiB,CAAC;IACzB,CAAG;AACH;AAEA,SAAS,cAAc,CAAC,KAAK,EAAqC;IAChE,IAAI;QACF,OAAO,KAAK,CAAC,MAAO;IACxB,CAAI,CAAA,OAAO,CAAC,EAAE;QACd,qFAAA;QACA,iEAAA;QACI,OAAO,IAAI;IACf;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "file": "xhr.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/instrument/xhr.ts"], "sourcesContent": ["import type { HandlerDataXhr, SentryWrappedXMLHttpRequest } from '@sentry/core';\nimport { addHandler, isString, maybeInstrument, timestampInSeconds, triggerHandlers } from '@sentry/core';\nimport { WINDOW } from '../types';\n\nexport const SENTRY_XHR_DATA_KEY = '__sentry_xhr_v3__';\n\ntype WindowWithXhr = Window & { XMLHttpRequest?: typeof XMLHttpRequest };\n\n/**\n * Add an instrumentation handler for when an XHR request happens.\n * The handler function is called once when the request starts and once when it ends,\n * which can be identified by checking if it has an `endTimestamp`.\n *\n * Use at your own risk, this might break without changelog notice, only used internally.\n * @hidden\n */\nexport function addXhrInstrumentationHandler(handler: (data: HandlerDataXhr) => void): void {\n  const type = 'xhr';\n  addHandler(type, handler);\n  maybeInstrument(type, instrumentXHR);\n}\n\n/** Exported only for tests. */\nexport function instrumentXHR(): void {\n  if (!(WINDOW as WindowWithXhr).XMLHttpRequest) {\n    return;\n  }\n\n  const xhrproto = XMLHttpRequest.prototype;\n\n  // eslint-disable-next-line @typescript-eslint/unbound-method\n  xhrproto.open = new Proxy(xhrproto.open, {\n    apply(\n      originalOpen,\n      xhrOpenThisArg: XMLHttpRequest & SentryWrappedXMLHttpRequest,\n      xhrOpenArgArray:\n        | [method: string, url: string | URL]\n        | [method: string, url: string | URL, async: boolean, username?: string | null, password?: string | null],\n    ) {\n      // NOTE: If you are a Sentry user, and you are seeing this stack frame,\n      //       it means the error, that was caused by your XHR call did not\n      //       have a stack trace. If you are using HttpClient integration,\n      //       this is the expected behavior, as we are using this virtual error to capture\n      //       the location of your XHR call, and group your HttpClient events accordingly.\n      const virtualError = new Error();\n\n      const startTimestamp = timestampInSeconds() * 1000;\n\n      // open() should always be called with two or more arguments\n      // But to be on the safe side, we actually validate this and bail out if we don't have a method & url\n      const method = isString(xhrOpenArgArray[0]) ? xhrOpenArgArray[0].toUpperCase() : undefined;\n      const url = parseXhrUrlArg(xhrOpenArgArray[1]);\n\n      if (!method || !url) {\n        return originalOpen.apply(xhrOpenThisArg, xhrOpenArgArray);\n      }\n\n      xhrOpenThisArg[SENTRY_XHR_DATA_KEY] = {\n        method,\n        url,\n        request_headers: {},\n      };\n\n      // if Sentry key appears in URL, don't capture it as a request\n      if (method === 'POST' && url.match(/sentry_key/)) {\n        xhrOpenThisArg.__sentry_own_request__ = true;\n      }\n\n      const onreadystatechangeHandler: () => void = () => {\n        // For whatever reason, this is not the same instance here as from the outer method\n        const xhrInfo = xhrOpenThisArg[SENTRY_XHR_DATA_KEY];\n\n        if (!xhrInfo) {\n          return;\n        }\n\n        if (xhrOpenThisArg.readyState === 4) {\n          try {\n            // touching statusCode in some platforms throws\n            // an exception\n            xhrInfo.status_code = xhrOpenThisArg.status;\n          } catch (e) {\n            /* do nothing */\n          }\n\n          const handlerData: HandlerDataXhr = {\n            endTimestamp: timestampInSeconds() * 1000,\n            startTimestamp,\n            xhr: xhrOpenThisArg,\n            virtualError,\n          };\n          triggerHandlers('xhr', handlerData);\n        }\n      };\n\n      if ('onreadystatechange' in xhrOpenThisArg && typeof xhrOpenThisArg.onreadystatechange === 'function') {\n        xhrOpenThisArg.onreadystatechange = new Proxy(xhrOpenThisArg.onreadystatechange, {\n          apply(originalOnreadystatechange, onreadystatechangeThisArg, onreadystatechangeArgArray: unknown[]) {\n            onreadystatechangeHandler();\n            return originalOnreadystatechange.apply(onreadystatechangeThisArg, onreadystatechangeArgArray);\n          },\n        });\n      } else {\n        xhrOpenThisArg.addEventListener('readystatechange', onreadystatechangeHandler);\n      }\n\n      // Intercepting `setRequestHeader` to access the request headers of XHR instance.\n      // This will only work for user/library defined headers, not for the default/browser-assigned headers.\n      // Request cookies are also unavailable for XHR, as `Cookie` header can't be defined by `setRequestHeader`.\n      xhrOpenThisArg.setRequestHeader = new Proxy(xhrOpenThisArg.setRequestHeader, {\n        apply(\n          originalSetRequestHeader,\n          setRequestHeaderThisArg: SentryWrappedXMLHttpRequest,\n          setRequestHeaderArgArray: unknown[],\n        ) {\n          const [header, value] = setRequestHeaderArgArray;\n\n          const xhrInfo = setRequestHeaderThisArg[SENTRY_XHR_DATA_KEY];\n\n          if (xhrInfo && isString(header) && isString(value)) {\n            xhrInfo.request_headers[header.toLowerCase()] = value;\n          }\n\n          return originalSetRequestHeader.apply(setRequestHeaderThisArg, setRequestHeaderArgArray);\n        },\n      });\n\n      return originalOpen.apply(xhrOpenThisArg, xhrOpenArgArray);\n    },\n  });\n\n  // eslint-disable-next-line @typescript-eslint/unbound-method\n  xhrproto.send = new Proxy(xhrproto.send, {\n    apply(originalSend, sendThisArg: XMLHttpRequest & SentryWrappedXMLHttpRequest, sendArgArray: unknown[]) {\n      const sentryXhrData = sendThisArg[SENTRY_XHR_DATA_KEY];\n\n      if (!sentryXhrData) {\n        return originalSend.apply(sendThisArg, sendArgArray);\n      }\n\n      if (sendArgArray[0] !== undefined) {\n        sentryXhrData.body = sendArgArray[0];\n      }\n\n      const handlerData: HandlerDataXhr = {\n        startTimestamp: timestampInSeconds() * 1000,\n        xhr: sendThisArg,\n      };\n      triggerHandlers('xhr', handlerData);\n\n      return originalSend.apply(sendThisArg, sendArgArray);\n    },\n  });\n}\n\n/**\n * Parses the URL argument of a XHR method to a string.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/open#url\n * url: A string or any other object with a stringifier — including a URL object — that provides the URL of the resource to send the request to.\n *\n * @param url - The URL argument of an XHR method\n * @returns The parsed URL string or undefined if the URL is invalid\n */\nfunction parseXhrUrlArg(url: unknown): string | undefined {\n  if (isString(url)) {\n    return url;\n  }\n\n  try {\n    // If the passed in argument is not a string, it should have a `toString` method as a stringifier.\n    // If that fails, we just return undefined (like in IE11 where URL is not available)\n    return (url as URL).toString();\n  } catch {} // eslint-disable-line no-empty\n\n  return undefined;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIO,MAAM,mBAAoB,GAAE;AAInC;;;;;;;CAOA,GACO,SAAS,4BAA4B,CAAC,OAAO,EAAwC;IAC1F,MAAM,IAAK,GAAE,KAAK;wMAClB,aAAA,AAAU,EAAC,IAAI,EAAE,OAAO,CAAC;uMACzB,mBAAA,AAAe,EAAC,IAAI,EAAE,aAAa,CAAC;AACtC;AAEA,6BAAA,GACO,SAAS,aAAa,GAAS;IACpC,IAAI,CAAC,qLAAC,SAAA,CAAyB,cAAc,EAAE;QAC7C;IACJ;IAEE,MAAM,QAAA,GAAW,cAAc,CAAC,SAAS;IAE3C,6DAAA;IACE,QAAQ,CAAC,IAAA,GAAO,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE;QACvC,KAAK,EACH,YAAY,EACZ,cAAc,EACd,eAAA;YAIN,uEAAA;YACA,qEAAA;YACA,qEAAA;YACA,qFAAA;YACA,qFAAA;YACM,MAAM,YAAa,GAAE,IAAI,KAAK,EAAE;YAEhC,MAAM,cAAe,qLAAE,qBAAA,AAAkB,EAAC,IAAI,IAAI;YAExD,4DAAA;YACA,qGAAA;YACM,MAAM,UAAS,0LAAA,AAAQ,EAAC,eAAe,CAAC,CAAC,CAAC,CAAA,GAAI,eAAe,CAAC,CAAC,CAAC,CAAC,WAAW,EAAC,GAAI,SAAS;YAC1F,MAAM,MAAM,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAE9C,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE;gBACnB,OAAO,YAAY,CAAC,KAAK,CAAC,cAAc,EAAE,eAAe,CAAC;YAClE;YAEM,cAAc,CAAC,mBAAmB,CAAA,GAAI;gBACpC,MAAM;gBACN,GAAG;gBACH,eAAe,EAAE,CAAA,CAAE;YAC3B,CAAO;YAEP,8DAAA;YACM,IAAI,MAAO,KAAI,MAAO,IAAG,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;gBAChD,cAAc,CAAC,sBAAuB,GAAE,IAAI;YACpD;YAEM,MAAM,yBAAyB,GAAe,MAAM;gBAC1D,mFAAA;gBACQ,MAAM,OAAQ,GAAE,cAAc,CAAC,mBAAmB,CAAC;gBAEnD,IAAI,CAAC,OAAO,EAAE;oBACZ;gBACV;gBAEQ,IAAI,cAAc,CAAC,UAAW,KAAI,CAAC,EAAE;oBACnC,IAAI;wBACd,+CAAA;wBACA,eAAA;wBACY,OAAO,CAAC,WAAA,GAAc,cAAc,CAAC,MAAM;oBACvD,CAAY,CAAA,OAAO,CAAC,EAAE;oBACtB,cAAA,GACA;oBAEU,MAAM,WAAW,GAAmB;wBAClC,YAAY,oLAAE,qBAAA,AAAkB,EAAC,IAAI,IAAI;wBACzC,cAAc;wBACd,GAAG,EAAE,cAAc;wBACnB,YAAY;oBACxB,CAAW;wNACD,kBAAA,AAAe,EAAC,KAAK,EAAE,WAAW,CAAC;gBAC7C;YACA,CAAO;YAED,IAAI,oBAAqB,IAAG,cAAe,IAAG,OAAO,cAAc,CAAC,kBAAA,KAAuB,UAAU,EAAE;gBACrG,cAAc,CAAC,kBAAA,GAAqB,IAAI,KAAK,CAAC,cAAc,CAAC,kBAAkB,EAAE;oBAC/E,KAAK,EAAC,0BAA0B,EAAE,yBAAyB,EAAE,0BAA0B,EAAa;wBAClG,yBAAyB,EAAE;wBAC3B,OAAO,0BAA0B,CAAC,KAAK,CAAC,yBAAyB,EAAE,0BAA0B,CAAC;oBAC1G,CAAW;gBACX,CAAS,CAAC;YACV,OAAa;gBACL,cAAc,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,yBAAyB,CAAC;YACtF;YAEA,iFAAA;YACA,sGAAA;YACA,2GAAA;YACM,cAAc,CAAC,gBAAA,GAAmB,IAAI,KAAK,CAAC,cAAc,CAAC,gBAAgB,EAAE;gBAC3E,KAAK,EACH,wBAAwB,EACxB,uBAAuB,EACvB,wBAAwB;oBAExB,MAAM,CAAC,MAAM,EAAE,KAAK,CAAA,GAAI,wBAAwB;oBAEhD,MAAM,OAAQ,GAAE,uBAAuB,CAAC,mBAAmB,CAAC;oBAE5D,IAAI,OAAQ,oLAAG,WAAA,AAAQ,EAAC,MAAM,CAAA,oLAAK,WAAA,AAAQ,EAAC,KAAK,CAAC,EAAE;wBAClD,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,WAAW,EAAE,CAAE,GAAE,KAAK;oBACjE;oBAEU,OAAO,wBAAwB,CAAC,KAAK,CAAC,uBAAuB,EAAE,wBAAwB,CAAC;gBAClG,CAAS;YACT,CAAO,CAAC;YAEF,OAAO,YAAY,CAAC,KAAK,CAAC,cAAc,EAAE,eAAe,CAAC;QAChE,CAAK;IACL,CAAG,CAAC;IAEJ,6DAAA;IACE,QAAQ,CAAC,IAAA,GAAO,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE;QACvC,KAAK,EAAC,YAAY,EAAE,WAAW,EAAgD,YAAY,EAAa;YACtG,MAAM,aAAc,GAAE,WAAW,CAAC,mBAAmB,CAAC;YAEtD,IAAI,CAAC,aAAa,EAAE;gBAClB,OAAO,YAAY,CAAC,KAAK,CAAC,WAAW,EAAE,YAAY,CAAC;YAC5D;YAEM,IAAI,YAAY,CAAC,CAAC,CAAE,KAAI,SAAS,EAAE;gBACjC,aAAa,CAAC,IAAA,GAAO,YAAY,CAAC,CAAC,CAAC;YAC5C;YAEM,MAAM,WAAW,GAAmB;gBAClC,cAAc,EAAE,uMAAA,AAAkB,EAAC,IAAI,IAAI;gBAC3C,GAAG,EAAE,WAAW;YACxB,CAAO;gNACD,kBAAA,AAAe,EAAC,KAAK,EAAE,WAAW,CAAC;YAEnC,OAAO,YAAY,CAAC,KAAK,CAAC,WAAW,EAAE,YAAY,CAAC;QAC1D,CAAK;IACL,CAAG,CAAC;AACJ;AAEA;;;;;;;;CAQA,GACA,SAAS,cAAc,CAAC,GAAG,EAA+B;IACxD,oLAAI,WAAA,AAAQ,EAAC,GAAG,CAAC,EAAE;QACjB,OAAO,GAAG;IACd;IAEE,IAAI;QACN,kGAAA;QACA,oFAAA;QACI,OAAO,AAAC,GAAA,CAAY,QAAQ,EAAE;IAClC,CAAE,CAAE,OAAM,CAAA,CAAC,CAAA,+BAAA;IAET,OAAO,SAAS;AAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "file": "history.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/instrument/history.ts"], "sourcesContent": ["import type { HandlerDataHistory } from '@sentry/core';\nimport { addHandler, fill, maybeInstrument, supportsHistory, triggerHandlers } from '@sentry/core';\nimport { WINDOW } from '../types';\n\nlet lastHref: string | undefined;\n\n/**\n * Add an instrumentation handler for when a fetch request happens.\n * The handler function is called once when the request starts and once when it ends,\n * which can be identified by checking if it has an `endTimestamp`.\n *\n * Use at your own risk, this might break without changelog notice, only used internally.\n * @hidden\n */\nexport function addHistoryInstrumentationHandler(handler: (data: HandlerDataHistory) => void): void {\n  const type = 'history';\n  addHandler(type, handler);\n  maybeInstrument(type, instrumentHistory);\n}\n\n/**\n * Exported just for testing\n */\nexport function instrumentHistory(): void {\n  // The `popstate` event may also be triggered on `pushState`, but it may not always reliably be emitted by the browser\n  // Which is why we also monkey-patch methods below, in addition to this\n  WINDOW.addEventListener('popstate', () => {\n    const to = WINDOW.location.href;\n    // keep track of the current URL state, as we always receive only the updated state\n    const from = lastHref;\n    lastHref = to;\n\n    if (from === to) {\n      return;\n    }\n\n    const handlerData = { from, to } satisfies HandlerDataHistory;\n    triggerHandlers('history', handlerData);\n  });\n\n  // Just guard against this not being available, in weird environments\n  if (!supportsHistory()) {\n    return;\n  }\n\n  function historyReplacementFunction(originalHistoryFunction: () => void): () => void {\n    return function (this: History, ...args: unknown[]): void {\n      const url = args.length > 2 ? args[2] : undefined;\n      if (url) {\n        const from = lastHref;\n\n        // Ensure the URL is absolute\n        // this can be either a path, then it is relative to the current origin\n        // or a full URL of the current origin - other origins are not allowed\n        // See: https://developer.mozilla.org/en-US/docs/Web/API/History/pushState#url\n        // coerce to string (this is what pushState does)\n        const to = getAbsoluteUrl(String(url));\n\n        // keep track of the current URL state, as we always receive only the updated state\n        lastHref = to;\n\n        if (from === to) {\n          return originalHistoryFunction.apply(this, args);\n        }\n\n        const handlerData = { from, to } satisfies HandlerDataHistory;\n        triggerHandlers('history', handlerData);\n      }\n      return originalHistoryFunction.apply(this, args);\n    };\n  }\n\n  fill(WINDOW.history, 'pushState', historyReplacementFunction);\n  fill(WINDOW.history, 'replaceState', historyReplacementFunction);\n}\n\nfunction getAbsoluteUrl(urlOrPath: string): string {\n  try {\n    const url = new URL(urlOrPath, WINDOW.location.origin);\n    return url.toString();\n  } catch {\n    // fallback, just do nothing\n    return urlOrPath;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAIA,IAAI,QAAQ;AAEZ;;;;;;;CAOA,GACO,SAAS,gCAAgC,CAAC,OAAO,EAA4C;IAClG,MAAM,IAAK,GAAE,SAAS;wMACtB,aAAA,AAAU,EAAC,IAAI,EAAE,OAAO,CAAC;QACzB,kNAAA,AAAe,EAAC,IAAI,EAAE,iBAAiB,CAAC;AAC1C;AAEA;;CAEA,GACO,SAAS,iBAAiB,GAAS;IAC1C,sHAAA;IACA,uEAAA;yLACE,SAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,MAAM;QACxC,MAAM,EAAG,wLAAE,SAAM,CAAC,QAAQ,CAAC,IAAI;QACnC,mFAAA;QACI,MAAM,IAAK,GAAE,QAAQ;QACrB,QAAA,GAAW,EAAE;QAEb,IAAI,IAAK,KAAI,EAAE,EAAE;YACf;QACN;QAEI,MAAM,WAAY,GAAE;YAAE,IAAI;YAAE;QAAA,CAAK;QACjC,sNAAA,AAAe,EAAC,SAAS,EAAE,WAAW,CAAC;IAC3C,CAAG,CAAC;IAEJ,qEAAA;IACE,IAAI,uLAAC,kBAAA,AAAe,EAAE,GAAE;QACtB;IACJ;IAEE,SAAS,0BAA0B,CAAC,uBAAuB,EAA0B;QACnF,OAAO,SAAyB,GAAG,IAAI,EAAmB;YACxD,MAAM,GAAA,GAAM,IAAI,CAAC,MAAO,GAAE,CAAE,GAAE,IAAI,CAAC,CAAC,CAAA,GAAI,SAAS;YACjD,IAAI,GAAG,EAAE;gBACP,MAAM,IAAK,GAAE,QAAQ;gBAE7B,6BAAA;gBACA,uEAAA;gBACA,sEAAA;gBACA,8EAAA;gBACA,iDAAA;gBACQ,MAAM,KAAK,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAE9C,mFAAA;gBACQ,QAAA,GAAW,EAAE;gBAEb,IAAI,IAAK,KAAI,EAAE,EAAE;oBACf,OAAO,uBAAuB,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;gBAC1D;gBAEQ,MAAM,WAAY,GAAE;oBAAE,IAAI;oBAAE;gBAAA,CAAK;iBACjC,qNAAA,AAAe,EAAC,SAAS,EAAE,WAAW,CAAC;YAC/C;YACM,OAAO,uBAAuB,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;QACtD,CAAK;IACL;wLAEE,OAAA,AAAI,uLAAC,SAAM,CAAC,OAAO,EAAE,WAAW,EAAE,0BAA0B,CAAC;wLAC7D,OAAA,AAAI,uLAAC,SAAM,CAAC,OAAO,EAAE,cAAc,EAAE,0BAA0B,CAAC;AAClE;AAEA,SAAS,cAAc,CAAC,SAAS,EAAkB;IACjD,IAAI;QACF,MAAM,GAAA,GAAM,IAAI,GAAG,CAAC,SAAS,uLAAE,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;QACtD,OAAO,GAAG,CAAC,QAAQ,EAAE;IACzB,EAAI,OAAM;QACV,4BAAA;QACI,OAAO,SAAS;IACpB;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 462, "column": 0}, "map": {"version": 3, "file": "debug-build.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/debug-build.ts"], "sourcesContent": ["declare const __DEBUG_BUILD__: boolean;\n\n/**\n * This serves as a build time flag that will be true by default, but false in non-debug builds or if users replace `__SENTRY_DEBUG__` in their generated code.\n *\n * ATTENTION: This constant must never cross package boundaries (i.e. be exported) to guarantee that it can be used for tree shaking.\n */\nexport const DEBUG_BUILD = __DEBUG_BUILD__;\n"], "names": [], "mappings": "AAEA;;;;CAIA;;;AACO,MAAM,WAAY,GAAiB,OAAA,gBAAA,KAAA,WAAA,IAAA,gBAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 478, "column": 0}, "map": {"version": 3, "file": "getNativeImplementation.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/getNativeImplementation.ts"], "sourcesContent": ["import { isNativeFunction, logger } from '@sentry/core';\nimport { DEBUG_BUILD } from './debug-build';\nimport { WINDOW } from './types';\n\n/**\n * We generally want to use window.fetch / window.setTimeout.\n * However, in some cases this may be wrapped (e.g. by Zone.js for Angular),\n * so we try to get an unpatched version of this from a sandboxed iframe.\n */\n\ninterface CacheableImplementations {\n  setTimeout: typeof WINDOW.setTimeout;\n  fetch: typeof WINDOW.fetch;\n}\n\nconst cachedImplementations: Partial<CacheableImplementations> = {};\n\n/**\n * Get the native implementation of a browser function.\n *\n * This can be used to ensure we get an unwrapped version of a function, in cases where a wrapped function can lead to problems.\n *\n * The following methods can be retrieved:\n * - `setTimeout`: This can be wrapped by e.g. Angular, causing change detection to be triggered.\n * - `fetch`: This can be wrapped by e.g. ad-blockers, causing an infinite loop when a request is blocked.\n */\nexport function getNativeImplementation<T extends keyof CacheableImplementations>(\n  name: T,\n): CacheableImplementations[T] {\n  const cached = cachedImplementations[name];\n  if (cached) {\n    return cached;\n  }\n\n  let impl = WINDOW[name] as CacheableImplementations[T];\n\n  // Fast path to avoid DOM I/O\n  if (isNativeFunction(impl)) {\n    return (cachedImplementations[name] = impl.bind(WINDOW) as CacheableImplementations[T]);\n  }\n\n  const document = WINDOW.document;\n  // eslint-disable-next-line deprecation/deprecation\n  if (document && typeof document.createElement === 'function') {\n    try {\n      const sandbox = document.createElement('iframe');\n      sandbox.hidden = true;\n      document.head.appendChild(sandbox);\n      const contentWindow = sandbox.contentWindow;\n      if (contentWindow?.[name]) {\n        impl = contentWindow[name] as CacheableImplementations[T];\n      }\n      document.head.removeChild(sandbox);\n    } catch (e) {\n      // Could not create sandbox iframe, just use window.xxx\n      DEBUG_BUILD && logger.warn(`Could not create sandbox iframe for ${name} check, bailing to window.${name}: `, e);\n    }\n  }\n\n  // Sanity check: This _should_ not happen, but if it does, we just skip caching...\n  // This can happen e.g. in tests where fetch may not be available in the env, or similar.\n  if (!impl) {\n    return impl;\n  }\n\n  return (cachedImplementations[name] = impl.bind(WINDOW) as CacheableImplementations[T]);\n}\n\n/** Clear a cached implementation. */\nexport function clearCachedImplementation(name: keyof CacheableImplementations): void {\n  cachedImplementations[name] = undefined;\n}\n\n/**\n * A special usecase for incorrectly wrapped Fetch APIs in conjunction with ad-blockers.\n * Whenever someone wraps the Fetch API and returns the wrong promise chain,\n * this chain becomes orphaned and there is no possible way to capture it's rejections\n * other than allowing it bubble up to this very handler. eg.\n *\n * const f = window.fetch;\n * window.fetch = function () {\n *   const p = f.apply(this, arguments);\n *\n *   p.then(function() {\n *     console.log('hi.');\n *   });\n *\n *   return p;\n * }\n *\n * `p.then(function () { ... })` is producing a completely separate promise chain,\n * however, what's returned is `p` - the result of original `fetch` call.\n *\n * This mean, that whenever we use the Fetch API to send our own requests, _and_\n * some ad-blocker blocks it, this orphaned chain will _always_ reject,\n * effectively causing another event to be captured.\n * This makes a whole process become an infinite loop, which we need to somehow\n * deal with, and break it in one way or another.\n *\n * To deal with this issue, we are making sure that we _always_ use the real\n * browser Fetch API, instead of relying on what `window.fetch` exposes.\n * The only downside to this would be missing our own requests as breadcrumbs,\n * but because we are already not doing this, it should be just fine.\n *\n * Possible failed fetch error messages per-browser:\n *\n * Chrome:  Failed to fetch\n * Edge:    Failed to Fetch\n * Firefox: NetworkError when attempting to fetch resource\n * Safari:  resource blocked by content blocker\n */\nexport function fetch(...rest: Parameters<typeof WINDOW.fetch>): ReturnType<typeof WINDOW.fetch> {\n  return getNativeImplementation('fetch')(...rest);\n}\n\n/**\n * Get an unwrapped `setTimeout` method.\n * This ensures that even if e.g. Angular wraps `setTimeout`, we get the native implementation,\n * avoiding triggering change detection.\n */\nexport function setTimeout(...rest: Parameters<typeof WINDOW.setTimeout>): ReturnType<typeof WINDOW.setTimeout> {\n  return getNativeImplementation('setTimeout')(...rest);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAIA;;;;CAIA,GAOA,MAAM,qBAAqB,GAAsC,CAAA,CAAE;AAEnE;;;;;;;;CAQA,GACO,SAAS,uBAAuB,CACrC,IAAI;IAEJ,MAAM,MAAO,GAAE,qBAAqB,CAAC,IAAI,CAAC;IAC1C,IAAI,MAAM,EAAE;QACV,OAAO,MAAM;IACjB;IAEE,IAAI,IAAK,wLAAE,SAAM,CAAC,IAAI,CAAE;IAE1B,6BAAA;IACE,0LAAI,mBAAA,AAAgB,EAAC,IAAI,CAAC,EAAE;QAC1B,OAAQ,qBAAqB,CAAC,IAAI,CAAA,GAAI,IAAI,CAAC,IAAI,sLAAC,SAAM;IAC1D;IAEE,MAAM,QAAA,GAAW,8LAAM,CAAC,QAAQ;IAClC,mDAAA;IACE,IAAI,QAAA,IAAY,OAAO,QAAQ,CAAC,aAAA,KAAkB,UAAU,EAAE;QAC5D,IAAI;YACF,MAAM,UAAU,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC;YAChD,OAAO,CAAC,MAAO,GAAE,IAAI;YACrB,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;YAClC,MAAM,aAAA,GAAgB,OAAO,CAAC,aAAa;YAC3C,IAAI,aAAa,EAAA,CAAG,IAAI,CAAC,EAAE;gBACzB,IAAK,GAAE,aAAa,CAAC,IAAI,CAAE;YACnC;YACM,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;QACxC,CAAM,CAAA,OAAO,CAAC,EAAE;YAChB,uDAAA;0MACM,cAAA,oLAAe,SAAM,CAAC,IAAI,CAAC,CAAC,oCAAoC,EAAE,IAAI,CAAC,0BAA0B,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACrH;IACA;IAEA,kFAAA;IACA,yFAAA;IACE,IAAI,CAAC,IAAI,EAAE;QACT,OAAO,IAAI;IACf;IAEE,OAAQ,qBAAqB,CAAC,IAAI,CAAA,GAAI,IAAI,CAAC,IAAI,sLAAC,SAAM;AACxD;AAEA,mCAAA,GACO,SAAS,yBAAyB,CAAC,IAAI,EAAwC;IACpF,qBAAqB,CAAC,IAAI,CAAA,GAAI,SAAS;AACzC;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAqCA,GACO,SAAS,KAAK,CAAC,GAAG,IAAI,EAAoE;IAC/F,OAAO,uBAAuB,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;AAClD;AAEA;;;;CAIA,GACO,SAAS,UAAU,CAAC,GAAG,IAAI,EAA8E;IAC9G,OAAO,uBAAuB,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 596, "column": 0}, "map": {"version": 3, "file": "bindReporter.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/web-vitals/lib/bindReporter.ts"], "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { MetricRatingThresholds, MetricType } from '../types';\n\nconst getRating = (value: number, thresholds: MetricRatingThresholds): MetricType['rating'] => {\n  if (value > thresholds[1]) {\n    return 'poor';\n  }\n  if (value > thresholds[0]) {\n    return 'needs-improvement';\n  }\n  return 'good';\n};\n\nexport const bindReporter = <MetricName extends MetricType['name']>(\n  callback: (metric: Extract<MetricType, { name: MetricName }>) => void,\n  metric: Extract<MetricType, { name: MetricN<PERSON> }>,\n  thresholds: MetricRatingThresholds,\n  reportAllChanges?: boolean,\n) => {\n  let prevValue: number;\n  let delta: number;\n  return (forceReport?: boolean) => {\n    if (metric.value >= 0) {\n      if (forceReport || reportAllChanges) {\n        delta = metric.value - (prevValue ?? 0);\n\n        // Report the metric if there's a non-zero delta or if no previous\n        // value exists (which can happen in the case of the document becoming\n        // hidden when the metric value is 0).\n        // See: https://github.com/GoogleChrome/web-vitals/issues/14\n        if (delta || prevValue === undefined) {\n          prevValue = metric.value;\n          metric.delta = delta;\n          metric.rating = getRating(metric.value, thresholds);\n          callback(metric);\n        }\n      }\n    }\n  };\n};\n"], "names": [], "mappings": ";;;AAkBA,MAAM,YAAY,CAAC,KAAK,EAAU,UAAU,KAAmD;IAC7F,IAAI,KAAM,GAAE,UAAU,CAAC,CAAC,CAAC,EAAE;QACzB,OAAO,MAAM;IACjB;IACE,IAAI,KAAM,GAAE,UAAU,CAAC,CAAC,CAAC,EAAE;QACzB,OAAO,mBAAmB;IAC9B;IACE,OAAO,MAAM;AACf,CAAC;AAEM,MAAM,eAAe,CAC1B,QAAQ,EACR,MAAM,EACN,UAAU,EACV,gBAAgB;IAEhB,IAAI,SAAS;IACb,IAAI,KAAK;IACT,OAAO,CAAC,WAAW,KAAe;QAChC,IAAI,MAAM,CAAC,KAAM,IAAG,CAAC,EAAE;YACrB,IAAI,WAAY,IAAG,gBAAgB,EAAE;gBACnC,KAAA,GAAQ,MAAM,CAAC,KAAA,GAAA,CAAS,SAAA,IAAa,CAAC,CAAC;gBAE/C,kEAAA;gBACA,sEAAA;gBACA,sCAAA;gBACA,4DAAA;gBACQ,IAAI,KAAA,IAAS,SAAU,KAAI,SAAS,EAAE;oBACpC,SAAU,GAAE,MAAM,CAAC,KAAK;oBACxB,MAAM,CAAC,KAAM,GAAE,KAAK;oBACpB,MAAM,CAAC,MAAA,GAAS,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC;oBACnD,QAAQ,CAAC,MAAM,CAAC;gBAC1B;YACA;QACA;IACA,CAAG;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 637, "column": 0}, "map": {"version": 3, "file": "generateUniqueID.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/web-vitals/lib/generateUniqueID.ts"], "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Performantly generate a unique, 30-char string by combining a version\n * number, the current timestamp with a 13-digit number integer.\n * @return {string}\n */\nexport const generateUniqueID = () => {\n  return `v5-${Date.now()}-${Math.floor(Math.random() * (9e12 - 1)) + 1e12}`;\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcA,GAEA;;;;CAIA;;;AACa,MAAA,gBAAA,GAAmB,MAAM;IACpC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAC,GAAA,CAAK,IAAA,GAAO,CAAC,CAAC,CAAA,GAAI,IAAI,CAAC,CAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 669, "column": 0}, "map": {"version": 3, "file": "getNavigationEntry.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/web-vitals/lib/getNavigationEntry.ts"], "sourcesContent": ["/*\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { WINDOW } from '../../../types';\n\n// sentry-specific change:\n// add optional param to not check for responseStart (see comment below)\nexport const getNavigationEntry = (checkResponseStart = true): PerformanceNavigationTiming | void => {\n  const navigationEntry = WINDOW.performance?.getEntriesByType?.('navigation')[0];\n  // Check to ensure the `responseStart` property is present and valid.\n  // In some cases a zero value is reported by the browser (for\n  // privacy/security reasons), and in other cases (bugs) the value is\n  // negative or is larger than the current page time. Ignore these cases:\n  // - https://github.com/GoogleChrome/web-vitals/issues/137\n  // - https://github.com/GoogleChrome/web-vitals/issues/162\n  // - https://github.com/GoogleChrome/web-vitals/issues/275\n  if (\n    // sentry-specific change:\n    // We don't want to check for responseStart for our own use of `getNavigationEntry`\n    !checkResponseStart ||\n    (navigationEntry && navigationEntry.responseStart > 0 && navigationEntry.responseStart < performance.now())\n  ) {\n    return navigationEntry;\n  }\n};\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;;;;;CAcA,GAIA,0BAAA;AACA,wEAAA;MACa,kBAAmB,GAAE,CAAC,kBAAmB,GAAE,IAAI,KAAyC;IACnG,MAAM,eAAA,wLAAkB,SAAM,CAAC,WAAW,EAAE,gBAAgB,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;IACjF,qEAAA;IACA,6DAAA;IACA,oEAAA;IACA,wEAAA;IACA,0DAAA;IACA,0DAAA;IACA,0DAAA;IACE,IACF,0BAAA;IACA,mFAAA;IACI,CAAC,kBAAmB,IACnB,eAAgB,IAAG,eAAe,CAAC,aAAA,GAAgB,CAAA,IAAK,eAAe,CAAC,aAAc,GAAE,WAAW,CAAC,GAAG,EAAE,EAC1G;QACA,OAAO,eAAe;IAC1B;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 713, "column": 0}, "map": {"version": 3, "file": "getActivationStart.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/web-vitals/lib/getActivationStart.ts"], "sourcesContent": ["/*\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getNavigationEntry } from './getNavigationEntry';\n\nexport const getActivationStart = (): number => {\n  const navEntry = getNavigationEntry();\n  return navEntry?.activationStart ?? 0;\n};\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;;;;;CAcA,GAIa,MAAA,kBAAA,GAAqB,MAAc;IAC9C,MAAM,QAAA,4OAAW,qBAAA,AAAkB,EAAE;IACrC,OAAO,QAAQ,EAAE,eAAA,IAAmB,CAAC;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 744, "column": 0}, "map": {"version": 3, "file": "initMetric.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/web-vitals/lib/initMetric.ts"], "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { WINDOW } from '../../../types';\nimport type { MetricType } from '../types';\nimport { generateUniqueID } from './generateUniqueID';\nimport { getActivationStart } from './getActivationStart';\nimport { getNavigationEntry } from './getNavigationEntry';\n\nexport const initMetric = <MetricName extends MetricType['name']>(name: MetricName, value: number = -1) => {\n  const navEntry = getNavigationEntry();\n  let navigationType: MetricType['navigationType'] = 'navigate';\n\n  if (navEntry) {\n    if (WINDOW.document?.prerendering || getActivationStart() > 0) {\n      navigationType = 'prerender';\n    } else if (WINDOW.document?.wasDiscarded) {\n      navigationType = 'restore';\n    } else if (navEntry.type) {\n      navigationType = navEntry.type.replace(/_/g, '-') as MetricType['navigationType'];\n    }\n  }\n\n  // Use `entries` type specific for the metric.\n  const entries: Extract<MetricType, { name: MetricName }>['entries'] = [];\n\n  return {\n    name,\n    value,\n    rating: 'good' as const, // If needed, will be updated when reported. `const` to keep the type from widening to `string`.\n    delta: 0,\n    entries,\n    id: generateUniqueID(),\n    navigationType,\n  };\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;;;;;CAcA,GAQa,MAAA,UAAA,GAAa,CAAwC,IAAI,EAAc,KAAK,GAAW,CAAA,CAAE,KAAK;IACzG,MAAM,QAAA,4OAAW,qBAAA,AAAkB,EAAE;IACrC,IAAI,cAAc,GAAiC,UAAU;IAE7D,IAAI,QAAQ,EAAE;QACZ,IAAI,8LAAM,CAAC,QAAQ,EAAE,YAAa,6OAAG,qBAAA,AAAkB,EAAG,IAAE,CAAC,EAAE;YAC7D,cAAA,GAAiB,WAAW;QAClC,CAAI,MAAO,yLAAI,SAAM,CAAC,QAAQ,EAAE,YAAY,EAAE;YACxC,cAAA,GAAiB,SAAS;QAChC,OAAW,IAAI,QAAQ,CAAC,IAAI,EAAE;YACxB,cAAA,GAAiB,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAE;QACxD;IACA;IAEA,8CAAA;IACE,MAAM,OAAO,GAAyD,EAAE;IAExE,OAAO;QACL,IAAI;QACJ,KAAK;QACL,MAAM,EAAE,MAAO;QACf,KAAK,EAAE,CAAC;QACR,OAAO;QACP,EAAE,yOAAE,mBAAA,AAAgB,EAAE;QACtB,cAAc;IAClB,CAAG;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 801, "column": 0}, "map": {"version": 3, "file": "initUnique.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/web-vitals/lib/initUnique.ts"], "sourcesContent": ["/*\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst instanceMap: WeakMap<object, unknown> = new WeakMap();\n\n/**\n * A function that accepts and identity object and a class object and returns\n * either a new instance of that class or an existing instance, if the\n * identity object was previously used.\n */\nexport function initUnique<T>(identityObj: object, ClassObj: new () => T): T {\n  if (!instanceMap.get(identityObj)) {\n    instanceMap.set(identityObj, new ClassObj());\n  }\n  return instanceMap.get(identityObj)! as T;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcA;;;AAEA,MAAM,WAAW,GAA6B,IAAI,OAAO,EAAE;AAE3D;;;;CAIA,GACO,SAAS,UAAU,CAAI,WAAW,EAAU,QAAQ,EAAkB;IAC3E,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;QACjC,WAAW,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,QAAQ,EAAE,CAAC;IAChD;IACE,OAAO,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 837, "column": 0}, "map": {"version": 3, "file": "LayoutShiftManager.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/web-vitals/lib/LayoutShiftManager.ts"], "sourcesContent": ["/* eslint-disable jsdoc/require-jsdoc */\n/*\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport class LayoutShiftManager {\n  // eslint-disable-next-line @typescript-eslint/explicit-member-accessibility\n  _onAfterProcessingUnexpectedShift?: (entry: LayoutShift) => void;\n\n  // eslint-disable-next-line @sentry-internal/sdk/no-class-field-initializers, @typescript-eslint/explicit-member-accessibility\n  _sessionValue = 0;\n  // eslint-disable-next-line @sentry-internal/sdk/no-class-field-initializers, @typescript-eslint/explicit-member-accessibility\n  _sessionEntries: LayoutShift[] = [];\n\n  // eslint-disable-next-line @typescript-eslint/explicit-member-accessibility\n  _processEntry(entry: LayoutShift) {\n    // Only count layout shifts without recent user input.\n    if (entry.hadRecentInput) return;\n\n    const firstSessionEntry = this._sessionEntries[0];\n    const lastSessionEntry = this._sessionEntries.at(-1);\n\n    // If the entry occurred less than 1 second after the previous entry\n    // and less than 5 seconds after the first entry in the session,\n    // include the entry in the current session. Otherwise, start a new\n    // session.\n    if (\n      this._sessionValue &&\n      firstSessionEntry &&\n      lastSessionEntry &&\n      entry.startTime - lastSessionEntry.startTime < 1000 &&\n      entry.startTime - firstSessionEntry.startTime < 5000\n    ) {\n      this._sessionValue += entry.value;\n      this._sessionEntries.push(entry);\n    } else {\n      this._sessionValue = entry.value;\n      this._sessionEntries = [entry];\n    }\n\n    this._onAfterProcessingUnexpectedShift?.(entry);\n  }\n}\n"], "names": [], "mappings": "AAAA,sCAAA,GACA;;;;;;;;;;;;;;CAcA;;;AAEO,MAAM,kBAAmB,CAAA;IAAA,WAAA,EAAA;QAAA,kBAAA,CAAA,SAAA,CAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA;QAAA,kBAAA,CAAA,SAAA,CAAA,OAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;IAAA;IAChC,4EAAA;IAGA,8HAAA;IACE,MAAA,GAAA;QAAA,IAAA,CAAA,aAAA,GAAgB;IAAC;IACnB,8HAAA;IACA,OAAA,GAAA;QAAA,IAAA,CAAE,eAAe,GAAkB,EAAA;IAAE;IAErC,4EAAA;IACE,aAAa,CAAC,KAAK,EAAe;QACpC,sDAAA;QACI,IAAI,KAAK,CAAC,cAAc,EAAE;QAE1B,MAAM,oBAAoB,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;QACjD,MAAM,gBAAiB,GAAE,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAA,CAAE,CAAC;QAExD,oEAAA;QACA,gEAAA;QACA,mEAAA;QACA,WAAA;QACI,IACE,IAAI,CAAC,aAAc,IACnB,iBAAkB,IAClB,gBAAiB,IACjB,KAAK,CAAC,SAAU,GAAE,gBAAgB,CAAC,SAAA,GAAY,IAAK,IACpD,KAAK,CAAC,SAAA,GAAY,iBAAiB,CAAC,SAAA,GAAY,MAChD;YACA,IAAI,CAAC,aAAA,IAAiB,KAAK,CAAC,KAAK;YACjC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC;QACtC,OAAW;YACL,IAAI,CAAC,aAAA,GAAgB,KAAK,CAAC,KAAK;YAChC,IAAI,CAAC,eAAA,GAAkB;gBAAC,KAAK;aAAC;QACpC;QAEI,IAAI,CAAC,iCAAiC,GAAG,KAAK,CAAC;IACnD;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 898, "column": 0}, "map": {"version": 3, "file": "observe.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/web-vitals/lib/observe.ts"], "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\ninterface PerformanceEntryMap {\n  event: PerformanceEventTiming[];\n  'first-input': PerformanceEventTiming[];\n  'layout-shift': LayoutShift[];\n  'largest-contentful-paint': LargestContentfulPaint[];\n  'long-animation-frame': PerformanceLongAnimationFrameTiming[];\n  paint: PerformancePaintTiming[];\n  navigation: PerformanceNavigationTiming[];\n  resource: PerformanceResourceTiming[];\n  // Sentry-specific change:\n  // We add longtask as a supported entry type as we use this in\n  // our `instrumentPerformanceObserver` function also observes 'longtask'\n  // entries.\n  longtask: PerformanceEntry[];\n}\n\n/**\n * Takes a performance entry type and a callback function, and creates a\n * `PerformanceObserver` instance that will observe the specified entry type\n * with buffering enabled and call the callback _for each entry_.\n *\n * This function also feature-detects entry support and wraps the logic in a\n * try/catch to avoid errors in unsupporting browsers.\n */\nexport const observe = <K extends keyof PerformanceEntryMap>(\n  type: K,\n  callback: (entries: PerformanceEntryMap[K]) => void,\n  opts: PerformanceObserverInit = {},\n): PerformanceObserver | undefined => {\n  try {\n    if (PerformanceObserver.supportedEntryTypes.includes(type)) {\n      const po = new PerformanceObserver(list => {\n        // Delay by a microtask to workaround a bug in Safari where the\n        // callback is invoked immediately, rather than in a separate task.\n        // See: https://github.com/GoogleChrome/web-vitals/issues/277\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        Promise.resolve().then(() => {\n          callback(list.getEntries() as PerformanceEntryMap[K]);\n        });\n      });\n      po.observe({ type, buffered: true, ...opts });\n      return po;\n    }\n  } catch {\n    // Do nothing.\n  }\n  return;\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcA,GAkBA;;;;;;;CAOA;;;AACO,MAAM,UAAU,CACrB,IAAI,EACJ,QAAQ,EACR,IAAI,GAA4B,CAAA,CAAE;IAElC,IAAI;QACF,IAAI,mBAAmB,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YAC1D,MAAM,EAAG,GAAE,IAAI,mBAAmB,EAAC,QAAQ;gBACjD,+DAAA;gBACA,mEAAA;gBACA,6DAAA;gBACA,mEAAA;gBACQ,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM;oBAC3B,QAAQ,CAAC,IAAI,CAAC,UAAU,IAA6B;gBAC/D,CAAS,CAAC;YACV,CAAO,CAAC;YACF,EAAE,CAAC,OAAO,CAAC;gBAAE,IAAI;gBAAE,QAAQ,EAAE,IAAI;gBAAE,GAAG,IAAA;YAAA,CAAM,CAAC;YAC7C,OAAO,EAAE;QACf;IACA,EAAI,OAAM;IACV,cAAA;IACA;IACE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 954, "column": 0}, "map": {"version": 3, "file": "runOnce.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/web-vitals/lib/runOnce.ts"], "sourcesContent": ["/*\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const runOnce = (cb: () => void) => {\n  let called = false;\n  return () => {\n    if (!called) {\n      cb();\n      called = true;\n    }\n  };\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcA;;;MAEa,OAAQ,GAAE,CAAC,EAAE,KAAiB;IACzC,IAAI,MAAO,GAAE,KAAK;IAClB,OAAO,MAAM;QACX,IAAI,CAAC,MAAM,EAAE;YACX,EAAE,EAAE;YACJ,MAAA,GAAS,IAAI;QACnB;IACA,CAAG;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 988, "column": 0}, "map": {"version": 3, "file": "getVisibilityWatcher.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/web-vitals/lib/getVisibilityWatcher.ts"], "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { WINDOW } from '../../../types';\nimport { getActivationStart } from './getActivationStart';\n\nlet firstHiddenTime = -1;\n\nconst initHiddenTime = () => {\n  // If the document is hidden when this code runs, assume it was always\n  // hidden and the page was loaded in the background, with the one exception\n  // that visibility state is always 'hidden' during prerendering, so we have\n  // to ignore that case until prerendering finishes (see: `prerenderingchange`\n  // event logic below).\n  return WINDOW.document?.visibilityState === 'hidden' && !WINDOW.document?.prerendering ? 0 : Infinity;\n};\n\nconst onVisibilityUpdate = (event: Event) => {\n  // If the document is 'hidden' and no previous hidden timestamp has been\n  // set, update it based on the current event data.\n  if (WINDOW.document!.visibilityState === 'hidden' && firstHiddenTime > -1) {\n    // If the event is a 'visibilitychange' event, it means the page was\n    // visible prior to this change, so the event timestamp is the first\n    // hidden time.\n    // However, if the event is not a 'visibilitychange' event, then it must\n    // be a 'prerenderingchange' event, and the fact that the document is\n    // still 'hidden' from the above check means the tab was activated\n    // in a background state and so has always been hidden.\n    firstHiddenTime = event.type === 'visibilitychange' ? event.timeStamp : 0;\n\n    // Remove all listeners now that a `firstHiddenTime` value has been set.\n    removeChangeListeners();\n  }\n};\n\nconst addChangeListeners = () => {\n  addEventListener('visibilitychange', onVisibilityUpdate, true);\n  // IMPORTANT: when a page is prerendering, its `visibilityState` is\n  // 'hidden', so in order to account for cases where this module checks for\n  // visibility during prerendering, an additional check after prerendering\n  // completes is also required.\n  addEventListener('prerenderingchange', onVisibilityUpdate, true);\n};\n\nconst removeChangeListeners = () => {\n  removeEventListener('visibilitychange', onVisibilityUpdate, true);\n  removeEventListener('prerenderingchange', onVisibilityUpdate, true);\n};\n\nexport const getVisibilityWatcher = () => {\n  if (WINDOW.document && firstHiddenTime < 0) {\n    // Check if we have a previous hidden `visibility-state` performance entry.\n    const activationStart = getActivationStart();\n    const firstVisibilityStateHiddenTime = !WINDOW.document.prerendering\n      ? globalThis.performance\n          .getEntriesByType('visibility-state')\n          .filter(e => e.name === 'hidden' && e.startTime > activationStart)[0]?.startTime\n      : undefined;\n\n    // Prefer that, but if it's not available and the document is hidden when\n    // this code runs, assume it was hidden since navigation start. This isn't\n    // a perfect heuristic, but it's the best we can do until the\n    // `visibility-state` performance entry becomes available in all browsers.\n    firstHiddenTime = firstVisibilityStateHiddenTime ?? initHiddenTime();\n    // We're still going to listen to for changes so we can handle things like\n    // bfcache restores and/or prerender without having to examine individual\n    // timestamps in detail.\n    addChangeListeners();\n  }\n  return {\n    get firstHiddenTime() {\n      return firstHiddenTime;\n    },\n  };\n};\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;;;;;CAcA,GAKA,IAAI,eAAA,GAAkB,CAAA,CAAE;AAExB,MAAM,cAAe,GAAE,MAAM;IAC7B,sEAAA;IACA,2EAAA;IACA,2EAAA;IACA,6EAAA;IACA,sBAAA;IACE,4LAAO,SAAM,CAAC,QAAQ,EAAE,eAAA,KAAoB,QAAS,IAAG,qLAAC,UAAM,CAAC,QAAQ,EAAE,eAAe,CAAA,GAAI,QAAQ;AACvG,CAAC;AAED,MAAM,kBAAmB,GAAE,CAAC,KAAK,KAAY;IAC7C,wEAAA;IACA,kDAAA;IACE,yLAAI,SAAM,CAAC,QAAQ,CAAE,eAAA,KAAoB,QAAA,IAAY,eAAA,GAAkB,CAAA,CAAE,EAAE;QAC7E,oEAAA;QACA,oEAAA;QACA,eAAA;QACA,wEAAA;QACA,qEAAA;QACA,kEAAA;QACA,uDAAA;QACI,eAAgB,GAAE,KAAK,CAAC,IAAK,KAAI,kBAAmB,GAAE,KAAK,CAAC,SAAU,GAAE,CAAC;QAE7E,wEAAA;QACI,qBAAqB,EAAE;IAC3B;AACA,CAAC;AAED,MAAM,kBAAmB,GAAE,MAAM;IAC/B,gBAAgB,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,IAAI,CAAC;IAChE,mEAAA;IACA,0EAAA;IACA,yEAAA;IACA,8BAAA;IACE,gBAAgB,CAAC,oBAAoB,EAAE,kBAAkB,EAAE,IAAI,CAAC;AAClE,CAAC;AAED,MAAM,qBAAsB,GAAE,MAAM;IAClC,mBAAmB,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,IAAI,CAAC;IACjE,mBAAmB,CAAC,oBAAoB,EAAE,kBAAkB,EAAE,IAAI,CAAC;AACrE,CAAC;AAEY,MAAA,oBAAA,GAAuB,MAAM;IACxC,yLAAI,SAAM,CAAC,QAAA,IAAY,eAAA,GAAkB,CAAC,EAAE;QAC9C,2EAAA;QACI,MAAM,eAAA,IAAkB,6PAAA,AAAkB,EAAE;QAC5C,MAAM,8BAA+B,GAAE,sLAAC,SAAM,CAAC,QAAQ,CAAC,YAAA,GACpD,UAAU,CAAC,WAAA,CACR,gBAAgB,CAAC,kBAAkB,EACnC,MAAM,CAAC,CAAA,IAAK,CAAC,CAAC,IAAA,KAAS,QAAA,IAAY,CAAC,CAAC,SAAA,GAAY,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE,YACzE,SAAS;QAEjB,yEAAA;QACA,0EAAA;QACA,6DAAA;QACA,0EAAA;QACI,kBAAkB,8BAAA,IAAkC,cAAc,EAAE;QACxE,0EAAA;QACA,yEAAA;QACA,wBAAA;QACI,kBAAkB,EAAE;IACxB;IACE,OAAO;QACL,IAAI,eAAe,IAAG;YACpB,OAAO,eAAe;QAC5B,CAAK;IACL,CAAG;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1075, "column": 0}, "map": {"version": 3, "file": "whenActivated.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/web-vitals/lib/whenActivated.ts"], "sourcesContent": ["/*\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { WINDOW } from '../../../types';\n\nexport const whenActivated = (callback: () => void) => {\n  if (WINDOW.document?.prerendering) {\n    addEventListener('prerenderingchange', () => callback(), true);\n  } else {\n    callback();\n  }\n};\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;;;;;CAcA,SAIa,aAAc,GAAE,CAAC,QAAQ,KAAiB;IACrD,yLAAI,SAAM,CAAC,QAAQ,EAAE,YAAY,EAAE;QACjC,gBAAgB,CAAC,oBAAoB,EAAE,IAAM,QAAQ,EAAE,EAAE,IAAI,CAAC;IAClE,OAAS;QACL,QAAQ,EAAE;IACd;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1109, "column": 0}, "map": {"version": 3, "file": "onFCP.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/web-vitals/onFCP.ts"], "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bindReporter } from './lib/bindReporter';\nimport { getActivationStart } from './lib/getActivationStart';\nimport { getVisibilityWatcher } from './lib/getVisibilityWatcher';\nimport { initMetric } from './lib/initMetric';\nimport { observe } from './lib/observe';\nimport { whenActivated } from './lib/whenActivated';\nimport type { FCPMetric, MetricRatingThresholds, ReportOpts } from './types';\n\n/** Thresholds for FCP. See https://web.dev/articles/fcp#what_is_a_good_fcp_score */\nexport const FCPThresholds: MetricRatingThresholds = [1800, 3000];\n\n/**\n * Calculates the [FCP](https://web.dev/articles/fcp) value for the current page and\n * calls the `callback` function once the value is ready, along with the\n * relevant `paint` performance entry used to determine the value. The reported\n * value is a `DOMHighResTimeStamp`.\n */\nexport const onFCP = (onReport: (metric: FCPMetric) => void, opts: ReportOpts = {}) => {\n  whenActivated(() => {\n    const visibilityWatcher = getVisibilityWatcher();\n    const metric = initMetric('FCP');\n    let report: ReturnType<typeof bindReporter>;\n\n    const handleEntries = (entries: FCPMetric['entries']) => {\n      for (const entry of entries) {\n        if (entry.name === 'first-contentful-paint') {\n          po!.disconnect();\n\n          // Only report if the page wasn't hidden prior to the first paint.\n          if (entry.startTime < visibilityWatcher.firstHiddenTime) {\n            // The activationStart reference is used because FCP should be\n            // relative to page activation rather than navigation start if the\n            // page was prerendered. But in cases where `activationStart` occurs\n            // after the FCP, this time should be clamped at 0.\n            metric.value = Math.max(entry.startTime - getActivationStart(), 0);\n            metric.entries.push(entry);\n            report(true);\n          }\n        }\n      }\n    };\n\n    const po = observe('paint', handleEntries);\n\n    if (po) {\n      report = bindReporter(onReport, metric, FCPThresholds, opts.reportAllChanges);\n    }\n  });\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;CAcA,GAUA,kFAAA,GACO,MAAM,aAAa,GAA2B;IAAC,IAAI;IAAE,IAAI;CAAA;AAEhE;;;;;CAKA,GACa,MAAA,KAAA,GAAQ,CAAC,QAAQ,EAA+B,IAAI,GAAe,CAAA,CAAE,KAAK;wOACrF,gBAAA,AAAa,EAAC,MAAM;QAClB,MAAM,iBAAA,8OAAoB,uBAAA,AAAoB,EAAE;QAChD,MAAM,MAAO,IAAE,6OAAA,AAAU,EAAC,KAAK,CAAC;QAChC,IAAI,MAAM;QAEV,MAAM,aAAc,GAAE,CAAC,OAAO,KAA2B;YACvD,KAAK,MAAM,KAAM,IAAG,OAAO,CAAE;gBAC3B,IAAI,KAAK,CAAC,IAAK,KAAI,wBAAwB,EAAE;oBAC3C,EAAE,CAAE,UAAU,EAAE;oBAE1B,kEAAA;oBACU,IAAI,KAAK,CAAC,SAAA,GAAY,iBAAiB,CAAC,eAAe,EAAE;wBACnE,8DAAA;wBACA,kEAAA;wBACA,oEAAA;wBACA,mDAAA;wBACY,MAAM,CAAC,KAAA,GAAQ,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,SAAA,IAAY,6PAAA,AAAkB,EAAE,GAAE,CAAC,CAAC;wBAClE,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;wBAC1B,MAAM,CAAC,IAAI,CAAC;oBACxB;gBACA;YACA;QACA,CAAK;QAED,MAAM,mOAAK,UAAA,AAAO,EAAC,OAAO,EAAE,aAAa,CAAC;QAE1C,IAAI,EAAE,EAAE;YACN,MAAO,sOAAE,eAAA,AAAY,EAAC,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC;QACnF;IACA,CAAG,CAAC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1184, "column": 0}, "map": {"version": 3, "file": "getCLS.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/web-vitals/getCLS.ts"], "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { WINDOW } from '../../types';\nimport { bindReporter } from './lib/bindReporter';\nimport { initMetric } from './lib/initMetric';\nimport { initUnique } from './lib/initUnique';\nimport { LayoutShiftManager } from './lib/LayoutShiftManager';\nimport { observe } from './lib/observe';\nimport { runOnce } from './lib/runOnce';\nimport { onFCP } from './onFCP';\nimport type { CLSMetric, MetricRatingThresholds, ReportOpts } from './types';\n\n/** Thresholds for CLS. See https://web.dev/articles/cls#what_is_a_good_cls_score */\nexport const CLSThresholds: MetricRatingThresholds = [0.1, 0.25];\n\n/**\n * Calculates the [CLS](https://web.dev/articles/cls) value for the current page and\n * calls the `callback` function once the value is ready to be reported, along\n * with all `layout-shift` performance entries that were used in the metric\n * value calculation. The reported value is a `double` (corresponding to a\n * [layout shift score](https://web.dev/articles/cls#layout_shift_score)).\n *\n * If the `reportAllChanges` configuration option is set to `true`, the\n * `callback` function will be called as soon as the value is initially\n * determined as well as any time the value changes throughout the page\n * lifespan.\n *\n * _**Important:** CLS should be continually monitored for changes throughout\n * the entire lifespan of a page—including if the user returns to the page after\n * it's been hidden/backgrounded. However, since browsers often [will not fire\n * additional callbacks once the user has backgrounded a\n * page](https://developer.chrome.com/blog/page-lifecycle-api/#advice-hidden),\n * `callback` is always called when the page's visibility state changes to\n * hidden. As a result, the `callback` function might be called multiple times\n * during the same page load._\n */\nexport const onCLS = (onReport: (metric: CLSMetric) => void, opts: ReportOpts = {}) => {\n  // Start monitoring FCP so we can only report CLS if FCP is also reported.\n  // Note: this is done to match the current behavior of CrUX.\n  onFCP(\n    runOnce(() => {\n      const metric = initMetric('CLS', 0);\n      let report: ReturnType<typeof bindReporter>;\n\n      const layoutShiftManager = initUnique(opts, LayoutShiftManager);\n\n      const handleEntries = (entries: LayoutShift[]) => {\n        for (const entry of entries) {\n          layoutShiftManager._processEntry(entry);\n        }\n\n        // If the current session value is larger than the current CLS value,\n        // update CLS and the entries contributing to it.\n        if (layoutShiftManager._sessionValue > metric.value) {\n          metric.value = layoutShiftManager._sessionValue;\n          metric.entries = layoutShiftManager._sessionEntries;\n          report();\n        }\n      };\n\n      const po = observe('layout-shift', handleEntries);\n      if (po) {\n        report = bindReporter(onReport, metric, CLSThresholds, opts!.reportAllChanges);\n\n        WINDOW.document?.addEventListener('visibilitychange', () => {\n          if (WINDOW.document?.visibilityState === 'hidden') {\n            handleEntries(po.takeRecords() as CLSMetric['entries']);\n            report(true);\n          }\n        });\n\n        // Queue a task to report (if nothing else triggers a report first).\n        // This allows CLS to be reported as soon as FCP fires when\n        // `reportAllChanges` is true.\n        WINDOW?.setTimeout?.(report);\n      }\n    }),\n  );\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;CAcA,GAYA,kFAAA,GACO,MAAM,aAAa,GAA2B;IAAC,GAAG;IAAE,IAAI;CAAA;AAE/D;;;;;;;;;;;;;;;;;;;;CAoBA,GACa,MAAA,KAAA,GAAQ,CAAC,QAAQ,EAA+B,IAAI,GAAe,CAAA,CAAE,KAAK;IACvF,0EAAA;IACA,4DAAA;yNACE,QAAA,AAAK,gOACH,UAAA,AAAO,EAAC,MAAM;QACZ,MAAM,UAAS,6OAAA,AAAU,EAAC,KAAK,EAAE,CAAC,CAAC;QACnC,IAAI,MAAM;QAEV,MAAM,sPAAqB,aAAA,AAAU,EAAC,IAAI,uOAAE,qBAAkB,CAAC;QAE/D,MAAM,aAAc,GAAE,CAAC,OAAO,KAAoB;YAChD,KAAK,MAAM,KAAM,IAAG,OAAO,CAAE;gBAC3B,kBAAkB,CAAC,aAAa,CAAC,KAAK,CAAC;YACjD;YAEA,qEAAA;YACA,iDAAA;YACQ,IAAI,kBAAkB,CAAC,aAAA,GAAgB,MAAM,CAAC,KAAK,EAAE;gBACnD,MAAM,CAAC,KAAA,GAAQ,kBAAkB,CAAC,aAAa;gBAC/C,MAAM,CAAC,OAAA,GAAU,kBAAkB,CAAC,eAAe;gBACnD,MAAM,EAAE;YAClB;QACA,CAAO;QAED,MAAM,mOAAK,UAAA,AAAO,EAAC,cAAc,EAAE,aAAa,CAAC;QACjD,IAAI,EAAE,EAAE;YACN,MAAO,sOAAE,eAAA,AAAY,EAAC,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,CAAE,gBAAgB,CAAC;gMAE9E,UAAM,CAAC,QAAQ,EAAE,gBAAgB,CAAC,kBAAkB,EAAE,MAAM;gBAC1D,yLAAI,SAAM,CAAC,QAAQ,EAAE,eAAA,KAAoB,QAAQ,EAAE;oBACjD,aAAa,CAAC,EAAE,CAAC,WAAW,IAA2B;oBACvD,MAAM,CAAC,IAAI,CAAC;gBACxB;YACA,CAAS,CAAC;YAEV,oEAAA;YACA,2DAAA;YACA,8BAAA;iMACQ,SAAM,EAAE,UAAU,GAAG,MAAM,CAAC;QACpC;IACA,CAAK,CAAC;AAEN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1285, "column": 0}, "map": {"version": 3, "file": "onHidden.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/web-vitals/lib/onHidden.ts"], "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { WINDOW } from '../../../types';\n\nexport interface OnHiddenCallback {\n  (event: Event): void;\n}\n\n// Sentry-specific change:\n// This function's logic was NOT updated to web-vitals 4.2.4 or 5.x but we continue\n// to use the web-vitals 3.5.2 versiondue to us having stricter browser support.\n// PR with context that made the changes: https://github.com/GoogleChrome/web-vitals/pull/442/files#r1530492402\n// The PR removed listening to the `pagehide` event, in favour of only listening to `visibilitychange` event.\n// This is \"more correct\" but some browsers we still support (<PERSON><PERSON> <14.4) don't fully support `visibilitychange`\n// or have known bugs w.r.t the `visibilitychange` event.\n// TODO (v10): If we decide to drop support for Safari 14.4, we can use the logic from web-vitals 4.2.4\n// In this case, we also need to update the integration tests that currently trigger the `pagehide` event to\n// simulate the page being hidden.\nexport const onHidden = (cb: OnHiddenCallback) => {\n  const onHiddenOrPageHide = (event: Event) => {\n    if (event.type === 'pagehide' || WINDOW.document?.visibilityState === 'hidden') {\n      cb(event);\n    }\n  };\n\n  if (WINDOW.document) {\n    addEventListener('visibilitychange', onHiddenOrPageHide, true);\n    // Some browsers have buggy implementations of visibilitychange,\n    // so we use pagehide in addition, just to be safe.\n    addEventListener('pagehide', onHiddenOrPageHide, true);\n  }\n};\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;;;;;CAcA,GAQA,0BAAA;AACA,mFAAA;AACA,gFAAA;AACA,+GAAA;AACA,6GAAA;AACA,kHAAA;AACA,yDAAA;AACA,uGAAA;AACA,4GAAA;AACA,kCAAA;MACa,QAAS,GAAE,CAAC,EAAE,KAAuB;IAChD,MAAM,kBAAmB,GAAE,CAAC,KAAK,KAAY;QAC3C,IAAI,KAAK,CAAC,IAAA,KAAS,UAAA,yLAAc,SAAM,CAAC,QAAQ,EAAE,eAAgB,KAAI,QAAQ,EAAE;YAC9E,EAAE,CAAC,KAAK,CAAC;QACf;IACA,CAAG;IAED,yLAAI,SAAM,CAAC,QAAQ,EAAE;QACnB,gBAAgB,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,IAAI,CAAC;QAClE,gEAAA;QACA,mDAAA;QACI,gBAAgB,CAAC,UAAU,EAAE,kBAAkB,EAAE,IAAI,CAAC;IAC1D;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1335, "column": 0}, "map": {"version": 3, "file": "getFID.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/web-vitals/getFID.ts"], "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * // Sentry: web-vitals removed FID reporting from v5. We're keeping it around\n * for the time being.\n * // TODO(v10): Remove FID reporting!\n */\n\nimport { bindReporter } from './lib/bindReporter';\nimport { getVisibilityWatcher } from './lib/getVisibilityWatcher';\nimport { initMetric } from './lib/initMetric';\nimport { observe } from './lib/observe';\nimport { onHidden } from './lib/onHidden';\nimport { runOnce } from './lib/runOnce';\nimport { whenActivated } from './lib/whenActivated';\nimport type { FIDMetric, MetricRatingThresholds, ReportOpts } from './types';\n\n/** Thresholds for FID. See https://web.dev/articles/fid#what_is_a_good_fid_score */\nexport const FIDThresholds: MetricRatingThresholds = [100, 300];\n\n/**\n * Calculates the [FID](https://web.dev/articles/fid) value for the current page and\n * calls the `callback` function once the value is ready, along with the\n * relevant `first-input` performance entry used to determine the value. The\n * reported value is a `DOMHighResTimeStamp`.\n *\n * _**Important:** since FID is only reported after the user interacts with the\n * page, it's possible that it will not be reported for some page loads._\n */\nexport const onFID = (onReport: (metric: FIDMetric) => void, opts: ReportOpts = {}) => {\n  whenActivated(() => {\n    const visibilityWatcher = getVisibilityWatcher();\n    const metric = initMetric('FID');\n    // eslint-disable-next-line prefer-const\n    let report: ReturnType<typeof bindReporter>;\n\n    const handleEntry = (entry: PerformanceEventTiming): void => {\n      // Only report if the page wasn't hidden prior to the first input.\n      if (entry.startTime < visibilityWatcher.firstHiddenTime) {\n        metric.value = entry.processingStart - entry.startTime;\n        metric.entries.push(entry);\n        report(true);\n      }\n    };\n\n    const handleEntries = (entries: FIDMetric['entries']) => {\n      (entries as PerformanceEventTiming[]).forEach(handleEntry);\n    };\n\n    const po = observe('first-input', handleEntries);\n\n    report = bindReporter(onReport, metric, FIDThresholds, opts.reportAllChanges);\n\n    if (po) {\n      // sentry: TODO: Figure out if we can use new whinIdleOrHidden insteard of onHidden\n      onHidden(\n        runOnce(() => {\n          handleEntries(po.takeRecords() as FIDMetric['entries']);\n          po.disconnect();\n        }),\n      );\n    }\n  });\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;;;CAkBA,GAWA,kFAAA,GACO,MAAM,aAAa,GAA2B;IAAC,GAAG;IAAE,GAAG;CAAA;AAE9D;;;;;;;;CAQA,GACa,MAAA,KAAA,GAAQ,CAAC,QAAQ,EAA+B,IAAI,GAAe,CAAA,CAAE,KAAK;wOACrF,gBAAA,AAAa,EAAC,MAAM;QAClB,MAAM,iBAAA,8OAAoB,uBAAA,AAAoB,EAAE;QAChD,MAAM,MAAO,GAAE,8OAAA,AAAU,EAAC,KAAK,CAAC;QACpC,wCAAA;QACI,IAAI,MAAM;QAEV,MAAM,WAAA,GAAc,CAAC,KAAK,KAAmC;YACjE,kEAAA;YACM,IAAI,KAAK,CAAC,SAAA,GAAY,iBAAiB,CAAC,eAAe,EAAE;gBACvD,MAAM,CAAC,KAAA,GAAQ,KAAK,CAAC,eAAgB,GAAE,KAAK,CAAC,SAAS;gBACtD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;gBAC1B,MAAM,CAAC,IAAI,CAAC;YACpB;QACA,CAAK;QAED,MAAM,aAAc,GAAE,CAAC,OAAO,KAA2B;YACtD,OAAQ,CAA6B,OAAO,CAAC,WAAW,CAAC;QAChE,CAAK;QAED,MAAM,MAAK,uOAAA,AAAO,EAAC,aAAa,EAAE,aAAa,CAAC;QAEhD,MAAO,sOAAE,eAAA,AAAY,EAAC,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC;QAE7E,IAAI,EAAE,EAAE;YACZ,mFAAA;2OACM,WAAA,AAAQ,gOACN,UAAA,AAAO,EAAC,MAAM;gBACZ,aAAa,CAAC,EAAE,CAAC,WAAW,IAA2B;gBACvD,EAAE,CAAC,UAAU,EAAE;YACzB,CAAS,CAAC;QAEV;IACA,CAAG,CAAC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1419, "column": 0}, "map": {"version": 3, "file": "interactionCountPolyfill.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/web-vitals/lib/polyfills/interactionCountPolyfill.ts"], "sourcesContent": ["/*\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { observe } from '../observe';\n\ndeclare global {\n  interface Performance {\n    interactionCount: number;\n  }\n}\n\nlet interactionCountEstimate = 0;\nlet minKnownInteractionId = Infinity;\nlet maxKnownInteractionId = 0;\n\nconst updateEstimate = (entries: PerformanceEventTiming[]) => {\n  entries.forEach(e => {\n    if (e.interactionId) {\n      minKnownInteractionId = Math.min(minKnownInteractionId, e.interactionId);\n      maxKnownInteractionId = Math.max(maxKnownInteractionId, e.interactionId);\n\n      interactionCountEstimate = maxKnownInteractionId ? (maxKnownInteractionId - minKnownInteractionId) / 7 + 1 : 0;\n    }\n  });\n};\n\nlet po: PerformanceObserver | undefined;\n\n/**\n * Returns the `interactionCount` value using the native API (if available)\n * or the polyfill estimate in this module.\n */\nexport const getInteractionCount = (): number => {\n  return po ? interactionCountEstimate : performance.interactionCount || 0;\n};\n\n/**\n * Feature detects native support or initializes the polyfill if needed.\n */\nexport const initInteractionCountPolyfill = (): void => {\n  if ('interactionCount' in performance || po) return;\n\n  po = observe('event', updateEstimate, {\n    type: 'event',\n    buffered: true,\n    durationThreshold: 0,\n  } as PerformanceObserverInit);\n};\n"], "names": [], "mappings": ";;;;;;AAAA;;;;;;;;;;;;;;CAcA,GAUA,IAAI,wBAAA,GAA2B,CAAC;AAChC,IAAI,qBAAA,GAAwB,QAAQ;AACpC,IAAI,qBAAA,GAAwB,CAAC;AAE7B,MAAM,cAAe,GAAE,CAAC,OAAO,KAA+B;IAC5D,OAAO,CAAC,OAAO,EAAC,KAAK;QACnB,IAAI,CAAC,CAAC,aAAa,EAAE;YACnB,qBAAA,GAAwB,IAAI,CAAC,GAAG,CAAC,qBAAqB,EAAE,CAAC,CAAC,aAAa,CAAC;YACxE,qBAAA,GAAwB,IAAI,CAAC,GAAG,CAAC,qBAAqB,EAAE,CAAC,CAAC,aAAa,CAAC;YAExE,wBAAyB,GAAE,qBAAsB,GAAE,CAAC,qBAAsB,GAAE,qBAAqB,IAAI,CAAA,GAAI,CAAA,GAAI,CAAC;QACpH;IACA,CAAG,CAAC;AACJ,CAAC;AAED,IAAI,EAAE;AAEN;;;CAGA,GACa,MAAA,mBAAA,GAAsB,MAAc;IAC/C,OAAO,KAAK,wBAAA,GAA2B,WAAW,CAAC,gBAAiB,IAAG,CAAC;AAC1E;AAEA;;CAEA,GACa,MAAA,4BAAA,GAA+B,MAAY;IACtD,IAAI,kBAAmB,IAAG,eAAe,EAAE,EAAE;IAE7C,mOAAK,UAAA,AAAO,EAAC,OAAO,EAAE,cAAc,EAAE;QACpC,IAAI,EAAE,OAAO;QACb,QAAQ,EAAE,IAAI;QACd,iBAAiB,EAAE,CAAC;IACxB,GAA+B;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1476, "column": 0}, "map": {"version": 3, "file": "InteractionManager.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/web-vitals/lib/InteractionManager.ts"], "sourcesContent": ["/*\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInteractionCount } from './polyfills/interactionCountPolyfill.js';\n\nexport interface Interaction {\n  _latency: number;\n  // While the `id` and `entries` properties are also internal and could be\n  // mangled by prefixing with an underscore, since they correspond to public\n  // symbols there is no need to mangle them as the library will compress\n  // better if we reuse the existing names.\n  id: number;\n  entries: PerformanceEventTiming[];\n}\n\n// To prevent unnecessary memory usage on pages with lots of interactions,\n// store at most 10 of the longest interactions to consider as INP candidates.\nconst MAX_INTERACTIONS_TO_CONSIDER = 10;\n\n// Used to store the interaction count after a bfcache restore, since p98\n// interaction latencies should only consider the current navigation.\nlet prevInteractionCount = 0;\n\n/**\n * Returns the interaction count since the last bfcache restore (or for the\n * full page lifecycle if there were no bfcache restores).\n */\nconst getInteractionCountForNavigation = () => {\n  return getInteractionCount() - prevInteractionCount;\n};\n\n/**\n *\n */\nexport class InteractionManager {\n  /**\n   * A list of longest interactions on the page (by latency) sorted so the\n   * longest one is first. The list is at most MAX_INTERACTIONS_TO_CONSIDER\n   * long.\n   */\n  // eslint-disable-next-line @sentry-internal/sdk/no-class-field-initializers, @typescript-eslint/explicit-member-accessibility\n  _longestInteractionList: Interaction[] = [];\n\n  /**\n   * A mapping of longest interactions by their interaction ID.\n   * This is used for faster lookup.\n   */\n  // eslint-disable-next-line @sentry-internal/sdk/no-class-field-initializers, @typescript-eslint/explicit-member-accessibility\n  _longestInteractionMap: Map<number, Interaction> = new Map();\n\n  // eslint-disable-next-line @typescript-eslint/explicit-member-accessibility\n  _onBeforeProcessingEntry?: (entry: PerformanceEventTiming) => void;\n\n  // eslint-disable-next-line @typescript-eslint/explicit-member-accessibility\n  _onAfterProcessingINPCandidate?: (interaction: Interaction) => void;\n\n  // eslint-disable-next-line @typescript-eslint/explicit-member-accessibility, jsdoc/require-jsdoc\n  _resetInteractions() {\n    prevInteractionCount = getInteractionCount();\n    this._longestInteractionList.length = 0;\n    this._longestInteractionMap.clear();\n  }\n\n  /**\n   * Returns the estimated p98 longest interaction based on the stored\n   * interaction candidates and the interaction count for the current page.\n   */\n  // eslint-disable-next-line @typescript-eslint/explicit-member-accessibility\n  _estimateP98LongestInteraction() {\n    const candidateInteractionIndex = Math.min(\n      this._longestInteractionList.length - 1,\n      Math.floor(getInteractionCountForNavigation() / 50),\n    );\n\n    return this._longestInteractionList[candidateInteractionIndex];\n  }\n\n  /**\n   * Takes a performance entry and adds it to the list of worst interactions\n   * if its duration is long enough to make it among the worst. If the\n   * entry is part of an existing interaction, it is merged and the latency\n   * and entries list is updated as needed.\n   */\n  // eslint-disable-next-line @typescript-eslint/explicit-member-accessibility\n  _processEntry(entry: PerformanceEventTiming) {\n    this._onBeforeProcessingEntry?.(entry);\n\n    // Skip further processing for entries that cannot be INP candidates.\n    if (!(entry.interactionId || entry.entryType === 'first-input')) return;\n\n    // The least-long of the 10 longest interactions.\n    const minLongestInteraction = this._longestInteractionList.at(-1);\n\n    let interaction = this._longestInteractionMap.get(entry.interactionId!);\n\n    // Only process the entry if it's possibly one of the ten longest,\n    // or if it's part of an existing interaction.\n    if (\n      interaction ||\n      this._longestInteractionList.length < MAX_INTERACTIONS_TO_CONSIDER ||\n      // If the above conditions are false, `minLongestInteraction` will be set.\n      entry.duration > minLongestInteraction!._latency\n    ) {\n      // If the interaction already exists, update it. Otherwise create one.\n      if (interaction) {\n        // If the new entry has a longer duration, replace the old entries,\n        // otherwise add to the array.\n        if (entry.duration > interaction._latency) {\n          interaction.entries = [entry];\n          interaction._latency = entry.duration;\n        } else if (entry.duration === interaction._latency && entry.startTime === interaction.entries[0]!.startTime) {\n          interaction.entries.push(entry);\n        }\n      } else {\n        interaction = {\n          id: entry.interactionId!,\n          entries: [entry],\n          _latency: entry.duration,\n        };\n        this._longestInteractionMap.set(interaction.id, interaction);\n        this._longestInteractionList.push(interaction);\n      }\n\n      // Sort the entries by latency (descending) and keep only the top ten.\n      this._longestInteractionList.sort((a, b) => b._latency - a._latency);\n      if (this._longestInteractionList.length > MAX_INTERACTIONS_TO_CONSIDER) {\n        const removedInteractions = this._longestInteractionList.splice(MAX_INTERACTIONS_TO_CONSIDER);\n\n        for (const interaction of removedInteractions) {\n          this._longestInteractionMap.delete(interaction.id);\n        }\n      }\n\n      // Call any post-processing on the interaction\n      this._onAfterProcessingINPCandidate?.(interaction);\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;;;;;CAcA,GAcA,0EAAA;AACA,8EAAA;AACA,MAAM,4BAAA,GAA+B,EAAE;AAEvC,yEAAA;AACA,qEAAA;AACA,IAAI,oBAAA,GAAuB,CAAC;AAE5B;;;CAGA,GACA,MAAM,gCAAiC,GAAE,MAAM;IAC7C,mQAAO,sBAAA,AAAmB,EAAC,IAAI,oBAAoB;AACrD,CAAC;AAED;;CAEA,GACO,MAAM,kBAAmB,CAAA;IAAA,WAAA,EAAA;QAAA,kBAAA,CAAA,SAAA,CAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA;QAAA,kBAAA,CAAA,SAAA,CAAA,OAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;IAAA;IAChC;;;;GAIA,GACA,8HAAA;IACA,MAAA,GAAA;QAAA,IAAA,CAAE,uBAAuB,GAAkB,EAAA;IAAE;IAE7C;;;GAGA,GACA,8HAAA;IACA,OAAA,GAAA;QAAA,IAAA,CAAE,sBAAsB,GAA6B,IAAI,GAAG;IAAE;IAE9D,4EAAA;IAGA,4EAAA;IAGA,iGAAA;IACE,kBAAkB,GAAG;QACnB,oBAAqB,+PAAE,sBAAA,AAAmB,EAAE;QAC5C,IAAI,CAAC,uBAAuB,CAAC,MAAA,GAAS,CAAC;QACvC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE;IACvC;IAEA;;;GAGA,GACA,4EAAA;IACE,8BAA8B,GAAG;QAC/B,MAAM,yBAAA,GAA4B,IAAI,CAAC,GAAG,CACxC,IAAI,CAAC,uBAAuB,CAAC,MAAA,GAAS,CAAC,EACvC,IAAI,CAAC,KAAK,CAAC,gCAAgC,EAAC,GAAI,EAAE,CAAC;QAGrD,OAAO,IAAI,CAAC,uBAAuB,CAAC,yBAAyB,CAAC;IAClE;IAEA;;;;;GAKA,GACA,4EAAA;IACE,aAAa,CAAC,KAAK,EAA0B;QAC3C,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC;QAE1C,qEAAA;QACI,IAAI,CAAA,CAAE,KAAK,CAAC,aAAA,IAAiB,KAAK,CAAC,SAAU,KAAI,aAAa,CAAC,EAAE;QAErE,iDAAA;QACI,MAAM,qBAAsB,GAAE,IAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC,CAAA,CAAE,CAAC;QAEjE,IAAI,WAAA,GAAc,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,KAAK,CAAC,aAAa,CAAE;QAE3E,kEAAA;QACA,8CAAA;QACI,IACE,WAAY,IACZ,IAAI,CAAC,uBAAuB,CAAC,MAAA,GAAS,4BAA6B,IACzE,0EAAA;QACM,KAAK,CAAC,QAAS,GAAE,qBAAqB,CAAE,QAAA,EACxC;YACN,sEAAA;YACM,IAAI,WAAW,EAAE;gBACvB,mEAAA;gBACA,8BAAA;gBACQ,IAAI,KAAK,CAAC,QAAA,GAAW,WAAW,CAAC,QAAQ,EAAE;oBACzC,WAAW,CAAC,OAAA,GAAU;wBAAC,KAAK;qBAAC;oBAC7B,WAAW,CAAC,QAAA,GAAW,KAAK,CAAC,QAAQ;gBAC/C,CAAQ,MAAO,IAAI,KAAK,CAAC,QAAS,KAAI,WAAW,CAAC,QAAS,IAAG,KAAK,CAAC,SAAA,KAAc,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAE,SAAS,EAAE;oBAC3G,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;gBACzC;YACA,OAAa;gBACL,cAAc;oBACZ,EAAE,EAAE,KAAK,CAAC,aAAa;oBACvB,OAAO,EAAE;wBAAC,KAAK;qBAAC;oBAChB,QAAQ,EAAE,KAAK,CAAC,QAAQ;gBAClC,CAAS;gBACD,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,EAAE,WAAW,CAAC;gBAC5D,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,WAAW,CAAC;YACtD;YAEA,sEAAA;YACM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAK,CAAC,CAAC,QAAA,GAAW,CAAC,CAAC,QAAQ,CAAC;YACpE,IAAI,IAAI,CAAC,uBAAuB,CAAC,MAAA,GAAS,4BAA4B,EAAE;gBACtE,MAAM,mBAAoB,GAAE,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,4BAA4B,CAAC;gBAE7F,KAAK,MAAM,WAAY,IAAG,mBAAmB,CAAE;oBAC7C,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC5D;YACA;YAEA,8CAAA;YACM,IAAI,CAAC,8BAA8B,GAAG,WAAW,CAAC;QACxD;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1606, "column": 0}, "map": {"version": 3, "file": "whenIdleOrHidden.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/web-vitals/lib/whenIdleOrHidden.ts"], "sourcesContent": ["/*\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { WINDOW } from '../../../types.js';\nimport { onHidden } from './onHidden.js';\nimport { runOnce } from './runOnce.js';\n\n/**\n * Runs the passed callback during the next idle period, or immediately\n * if the browser's visibility state is (or becomes) hidden.\n */\nexport const whenIdleOrHidden = (cb: () => void) => {\n  const rIC = WINDOW.requestIdleCallback || WINDOW.setTimeout;\n\n  // If the document is hidden, run the callback immediately, otherwise\n  // race an idle callback with the next `visibilitychange` event.\n  if (WINDOW.document?.visibilityState === 'hidden') {\n    cb();\n  } else {\n    // eslint-disable-next-line no-param-reassign\n    cb = runOnce(cb);\n    rIC(cb);\n    // sentry: we use onHidden instead of directly listening to visibilitychange\n    // because some browsers we still support (Safari <14.4) don't fully support\n    // `visibilitychange` or have known bugs w.r.t the `visibilitychange` event.\n    onHidden(cb);\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;;;;;CAcA,GAMA;;;CAGA,SACa,gBAAiB,GAAE,CAAC,EAAE,KAAiB;IAClD,MAAM,2LAAM,SAAM,CAAC,mBAAoB,wLAAG,UAAM,CAAC,UAAU;IAE7D,qEAAA;IACA,gEAAA;IACE,yLAAI,SAAM,CAAC,QAAQ,EAAE,eAAA,KAAoB,QAAQ,EAAE;QACjD,EAAE,EAAE;IACR,OAAS;QACT,6CAAA;QACI,EAAG,iOAAE,UAAA,AAAO,EAAC,EAAE,CAAC;QAChB,GAAG,CAAC,EAAE,CAAC;QACX,4EAAA;QACA,4EAAA;QACA,4EAAA;uOACI,WAAA,AAAQ,EAAC,EAAE,CAAC;IAChB;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1656, "column": 0}, "map": {"version": 3, "file": "getINP.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/web-vitals/getINP.ts"], "sourcesContent": ["/*\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bindReporter } from './lib/bindReporter';\nimport { initMetric } from './lib/initMetric';\nimport { initUnique } from './lib/initUnique';\nimport { InteractionManager } from './lib/InteractionManager';\nimport { observe } from './lib/observe';\nimport { onHidden } from './lib/onHidden';\nimport { initInteractionCountPolyfill } from './lib/polyfills/interactionCountPolyfill';\nimport { whenActivated } from './lib/whenActivated';\nimport { whenIdleOrHidden } from './lib/whenIdleOrHidden';\nimport type { INPMetric, INPReportOpts, MetricRatingThresholds } from './types';\n\n/** Thresholds for INP. See https://web.dev/articles/inp#what_is_a_good_inp_score */\nexport const INPThresholds: MetricRatingThresholds = [200, 500];\n\n// The default `durationThreshold` used across this library for observing\n// `event` entries via PerformanceObserver.\nconst DEFAULT_DURATION_THRESHOLD = 40;\n\n/**\n * Calculates the [INP](https://web.dev/articles/inp) value for the current\n * page and calls the `callback` function once the value is ready, along with\n * the `event` performance entries reported for that interaction. The reported\n * value is a `DOMHighResTimeStamp`.\n *\n * A custom `durationThreshold` configuration option can optionally be passed\n * to control what `event-timing` entries are considered for INP reporting. The\n * default threshold is `40`, which means INP scores of less than 40 will not\n * be reported. To avoid reporting no interactions in these cases, the library\n * will fall back to the input delay of the first interaction. Note that this\n * will not affect your 75th percentile INP value unless that value is also\n * less than 40 (well below the recommended\n * [good](https://web.dev/articles/inp#what_is_a_good_inp_score) threshold).\n *\n * If the `reportAllChanges` configuration option is set to `true`, the\n * `callback` function will be called as soon as the value is initially\n * determined as well as any time the value changes throughout the page\n * lifespan.\n *\n * _**Important:** INP should be continually monitored for changes throughout\n * the entire lifespan of a page—including if the user returns to the page after\n * it's been hidden/backgrounded. However, since browsers often [will not fire\n * additional callbacks once the user has backgrounded a\n * page](https://developer.chrome.com/blog/page-lifecycle-api/#advice-hidden),\n * `callback` is always called when the page's visibility state changes to\n * hidden. As a result, the `callback` function might be called multiple times\n * during the same page load._\n */\nexport const onINP = (onReport: (metric: INPMetric) => void, opts: INPReportOpts = {}) => {\n  // Return if the browser doesn't support all APIs needed to measure INP.\n  if (!(globalThis.PerformanceEventTiming && 'interactionId' in PerformanceEventTiming.prototype)) {\n    return;\n  }\n\n  whenActivated(() => {\n    // TODO(philipwalton): remove once the polyfill is no longer needed.\n    initInteractionCountPolyfill();\n\n    const metric = initMetric('INP');\n    // eslint-disable-next-line prefer-const\n    let report: ReturnType<typeof bindReporter>;\n\n    const interactionManager = initUnique(opts, InteractionManager);\n\n    const handleEntries = (entries: INPMetric['entries']) => {\n      // Queue the `handleEntries()` callback in the next idle task.\n      // This is needed to increase the chances that all event entries that\n      // occurred between the user interaction and the next paint\n      // have been dispatched. Note: there is currently an experiment\n      // running in Chrome (EventTimingKeypressAndCompositionInteractionId)\n      // 123+ that if rolled out fully may make this no longer necessary.\n      whenIdleOrHidden(() => {\n        for (const entry of entries) {\n          interactionManager._processEntry(entry);\n        }\n\n        const inp = interactionManager._estimateP98LongestInteraction();\n\n        if (inp && inp._latency !== metric.value) {\n          metric.value = inp._latency;\n          metric.entries = inp.entries;\n          report();\n        }\n      });\n    };\n\n    const po = observe('event', handleEntries, {\n      // Event Timing entries have their durations rounded to the nearest 8ms,\n      // so a duration of 40ms would be any event that spans 2.5 or more frames\n      // at 60Hz. This threshold is chosen to strike a balance between usefulness\n      // and performance. Running this callback for any interaction that spans\n      // just one or two frames is likely not worth the insight that could be\n      // gained.\n      durationThreshold: opts.durationThreshold ?? DEFAULT_DURATION_THRESHOLD,\n    });\n\n    report = bindReporter(onReport, metric, INPThresholds, opts.reportAllChanges);\n\n    if (po) {\n      // Also observe entries of type `first-input`. This is useful in cases\n      // where the first interaction is less than the `durationThreshold`.\n      po.observe({ type: 'first-input', buffered: true });\n\n      // sentry: we use onHidden instead of directly listening to visibilitychange\n      // because some browsers we still support (Safari <14.4) don't fully support\n      // `visibilitychange` or have known bugs w.r.t the `visibilitychange` event.\n      onHidden(() => {\n        handleEntries(po.takeRecords() as INPMetric['entries']);\n        report(true);\n      });\n    }\n  });\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;CAcA,GAaA,kFAAA,GACO,MAAM,aAAa,GAA2B;IAAC,GAAG;IAAE,GAAG;CAAA;AAE9D,yEAAA;AACA,2CAAA;AACA,MAAM,0BAAA,GAA6B,EAAE;AAErC;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4BA,GACa,MAAA,KAAA,GAAQ,CAAC,QAAQ,EAA+B,IAAI,GAAkB,CAAA,CAAE,KAAK;IAC1F,wEAAA;IACE,IAAI,CAAA,CAAE,UAAU,CAAC,sBAAA,IAA0B,eAAA,IAAmB,sBAAsB,CAAC,SAAS,CAAC,EAAE;QAC/F;IACJ;wOAEE,gBAAA,AAAa,EAAC,MAAM;QACtB,oEAAA;oQACI,+BAAA,AAA4B,EAAE;QAE9B,MAAM,MAAO,IAAE,6OAAA,AAAU,EAAC,KAAK,CAAC;QACpC,wCAAA;QACI,IAAI,MAAM;QAEV,MAAM,sPAAqB,aAAA,AAAU,EAAC,IAAI,uOAAE,qBAAkB,CAAC;QAE/D,MAAM,aAAc,GAAE,CAAC,OAAO,KAA2B;YAC7D,8DAAA;YACA,qEAAA;YACA,2DAAA;YACA,+DAAA;YACA,qEAAA;YACA,mEAAA;mPACM,mBAAA,AAAgB,EAAC,MAAM;gBACrB,KAAK,MAAM,KAAM,IAAG,OAAO,CAAE;oBAC3B,kBAAkB,CAAC,aAAa,CAAC,KAAK,CAAC;gBACjD;gBAEQ,MAAM,GAAI,GAAE,kBAAkB,CAAC,8BAA8B,EAAE;gBAE/D,IAAI,GAAA,IAAO,GAAG,CAAC,QAAA,KAAa,MAAM,CAAC,KAAK,EAAE;oBACxC,MAAM,CAAC,KAAA,GAAQ,GAAG,CAAC,QAAQ;oBAC3B,MAAM,CAAC,OAAA,GAAU,GAAG,CAAC,OAAO;oBAC5B,MAAM,EAAE;gBAClB;YACA,CAAO,CAAC;QACR,CAAK;QAED,MAAM,MAAK,uOAAA,AAAO,EAAC,OAAO,EAAE,aAAa,EAAE;YAC/C,wEAAA;YACA,yEAAA;YACA,2EAAA;YACA,wEAAA;YACA,uEAAA;YACA,UAAA;YACM,iBAAiB,EAAE,IAAI,CAAC,iBAAA,IAAqB,0BAA0B;QAC7E,CAAK,CAAC;QAEF,MAAO,IAAE,iPAAA,AAAY,EAAC,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC;QAE7E,IAAI,EAAE,EAAE;YACZ,sEAAA;YACA,oEAAA;YACM,EAAE,CAAC,OAAO,CAAC;gBAAE,IAAI,EAAE,aAAa;gBAAE,QAAQ,EAAE,IAAA;YAAA,CAAM,CAAC;YAEzD,4EAAA;YACA,4EAAA;YACA,4EAAA;2OACM,WAAA,AAAQ,EAAC,MAAM;gBACb,aAAa,CAAC,EAAE,CAAC,WAAW,IAA2B;gBACvD,MAAM,CAAC,IAAI,CAAC;YACpB,CAAO,CAAC;QACR;IACA,CAAG,CAAC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1793, "column": 0}, "map": {"version": 3, "file": "LCPEntryManager.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/web-vitals/lib/LCPEntryManager.ts"], "sourcesContent": ["/*\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// eslint-disable-next-line jsdoc/require-jsdoc\nexport class LCPEntryManager {\n  // eslint-disable-next-line @typescript-eslint/explicit-member-accessibility\n  _onBeforeProcessingEntry?: (entry: LargestContentfulPaint) => void;\n\n  // eslint-disable-next-line @typescript-eslint/explicit-member-accessibility, jsdoc/require-jsdoc\n  _processEntry(entry: LargestContentfulPaint) {\n    this._onBeforeProcessingEntry?.(entry);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcA,GAEA,+CAAA;;;;AACO,MAAM,eAAgB,CAAA;IAC7B,4EAAA;IAGA,iGAAA;IACE,aAAa,CAAC,KAAK,EAA0B;QAC3C,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC;IAC1C;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1826, "column": 0}, "map": {"version": 3, "file": "getLCP.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/web-vitals/getLCP.ts"], "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { WINDOW } from '../../types';\nimport { bindReporter } from './lib/bindReporter';\nimport { getActivationStart } from './lib/getActivationStart';\nimport { getVisibilityWatcher } from './lib/getVisibilityWatcher';\nimport { initMetric } from './lib/initMetric';\nimport { initUnique } from './lib/initUnique';\nimport { LCPEntryManager } from './lib/LCPEntryManager';\nimport { observe } from './lib/observe';\nimport { runOnce } from './lib/runOnce';\nimport { whenActivated } from './lib/whenActivated';\nimport { whenIdleOrHidden } from './lib/whenIdleOrHidden';\nimport type { LCPMetric, MetricRatingThresholds, ReportOpts } from './types';\n\n/** Thresholds for LCP. See https://web.dev/articles/lcp#what_is_a_good_lcp_score */\nexport const LCPThresholds: MetricRatingThresholds = [2500, 4000];\n\n/**\n * Calculates the [LCP](https://web.dev/articles/lcp) value for the current page and\n * calls the `callback` function once the value is ready (along with the\n * relevant `largest-contentful-paint` performance entry used to determine the\n * value). The reported value is a `DOMHighResTimeStamp`.\n *\n * If the `reportAllChanges` configuration option is set to `true`, the\n * `callback` function will be called any time a new `largest-contentful-paint`\n * performance entry is dispatched, or once the final value of the metric has\n * been determined.\n */\nexport const onLCP = (onReport: (metric: LCPMetric) => void, opts: ReportOpts = {}) => {\n  whenActivated(() => {\n    const visibilityWatcher = getVisibilityWatcher();\n    const metric = initMetric('LCP');\n    let report: ReturnType<typeof bindReporter>;\n\n    const lcpEntryManager = initUnique(opts, LCPEntryManager);\n\n    const handleEntries = (entries: LCPMetric['entries']) => {\n      // If reportAllChanges is set then call this function for each entry,\n      // otherwise only consider the last one.\n      if (!opts!.reportAllChanges) {\n        // eslint-disable-next-line no-param-reassign\n        entries = entries.slice(-1);\n      }\n\n      for (const entry of entries) {\n        lcpEntryManager._processEntry(entry);\n\n        // Only report if the page wasn't hidden prior to LCP.\n        if (entry.startTime < visibilityWatcher.firstHiddenTime) {\n          // The startTime attribute returns the value of the renderTime if it is\n          // not 0, and the value of the loadTime otherwise. The activationStart\n          // reference is used because LCP should be relative to page activation\n          // rather than navigation start if the page was prerendered. But in cases\n          // where `activationStart` occurs after the LCP, this time should be\n          // clamped at 0.\n          metric.value = Math.max(entry.startTime - getActivationStart(), 0);\n          metric.entries = [entry];\n          report();\n        }\n      }\n    };\n\n    const po = observe('largest-contentful-paint', handleEntries);\n\n    if (po) {\n      report = bindReporter(onReport, metric, LCPThresholds, opts.reportAllChanges);\n\n      // Ensure this logic only runs once, since it can be triggered from\n      // any of three different event listeners below.\n      const stopListening = runOnce(() => {\n        handleEntries(po.takeRecords() as LCPMetric['entries']);\n        po.disconnect();\n        report(true);\n      });\n\n      // Stop listening after input or visibilitychange.\n      // Note: while scrolling is an input that stops LCP observation, it's\n      // unreliable since it can be programmatically generated.\n      // See: https://github.com/GoogleChrome/web-vitals/issues/75\n      for (const type of ['keydown', 'click', 'visibilitychange']) {\n        // Wrap the listener in an idle callback so it's run in a separate\n        // task to reduce potential INP impact.\n        // https://github.com/GoogleChrome/web-vitals/issues/383\n        if (WINDOW.document) {\n          addEventListener(type, () => whenIdleOrHidden(stopListening), {\n            capture: true,\n            once: true,\n          });\n        }\n      }\n    }\n  });\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;CAcA,GAeA,kFAAA,GACO,MAAM,aAAa,GAA2B;IAAC,IAAI;IAAE,IAAI;CAAA;AAEhE;;;;;;;;;;CAUA,GACa,MAAA,KAAA,GAAQ,CAAC,QAAQ,EAA+B,IAAI,GAAe,CAAA,CAAE,KAAK;wOACrF,gBAAA,AAAa,EAAC,MAAM;QAClB,MAAM,iBAAA,8OAAoB,uBAAA,AAAoB,EAAE;QAChD,MAAM,MAAO,GAAE,8OAAA,AAAU,EAAC,KAAK,CAAC;QAChC,IAAI,MAAM;QAEV,MAAM,sBAAkB,0OAAA,AAAU,EAAC,IAAI,oOAAE,kBAAe,CAAC;QAEzD,MAAM,aAAc,GAAE,CAAC,OAAO,KAA2B;YAC7D,qEAAA;YACA,wCAAA;YACM,IAAI,CAAC,IAAI,CAAE,gBAAgB,EAAE;gBACnC,6CAAA;gBACQ,OAAA,GAAU,OAAO,CAAC,KAAK,CAAC,CAAA,CAAE,CAAC;YACnC;YAEM,KAAK,MAAM,KAAM,IAAG,OAAO,CAAE;gBAC3B,eAAe,CAAC,aAAa,CAAC,KAAK,CAAC;gBAE5C,sDAAA;gBACQ,IAAI,KAAK,CAAC,SAAA,GAAY,iBAAiB,CAAC,eAAe,EAAE;oBACjE,uEAAA;oBACA,sEAAA;oBACA,sEAAA;oBACA,yEAAA;oBACA,oEAAA;oBACA,gBAAA;oBACU,MAAM,CAAC,KAAA,GAAQ,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,SAAA,4OAAY,qBAAA,AAAkB,EAAE,GAAE,CAAC,CAAC;oBAClE,MAAM,CAAC,OAAA,GAAU;wBAAC,KAAK;qBAAC;oBACxB,MAAM,EAAE;gBAClB;YACA;QACA,CAAK;QAED,MAAM,mOAAK,UAAA,AAAO,EAAC,0BAA0B,EAAE,aAAa,CAAC;QAE7D,IAAI,EAAE,EAAE;YACN,MAAO,IAAE,iPAAA,AAAY,EAAC,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC;YAEnF,mEAAA;YACA,gDAAA;YACM,MAAM,aAAc,GAAE,wOAAA,AAAO,EAAC,MAAM;gBAClC,aAAa,CAAC,EAAE,CAAC,WAAW,IAA2B;gBACvD,EAAE,CAAC,UAAU,EAAE;gBACf,MAAM,CAAC,IAAI,CAAC;YACpB,CAAO,CAAC;YAER,kDAAA;YACA,qEAAA;YACA,yDAAA;YACA,4DAAA;YACM,KAAK,MAAM,IAAA,IAAQ;gBAAC,SAAS;gBAAE,OAAO;gBAAE,kBAAkB;aAAC,CAAE;gBACnE,kEAAA;gBACA,uCAAA;gBACA,wDAAA;gBACQ,yLAAI,SAAM,CAAC,QAAQ,EAAE;oBACnB,gBAAgB,CAAC,IAAI,EAAE,2OAAM,mBAAA,AAAgB,EAAC,aAAa,CAAC,EAAE;wBAC5D,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE,IAAI;oBACtB,CAAW,CAAC;gBACZ;YACA;QACA;IACA,CAAG,CAAC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1951, "column": 0}, "map": {"version": 3, "file": "onTTFB.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/web-vitals/onTTFB.ts"], "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { WINDOW } from '../../types';\nimport { bindReporter } from './lib/bindReporter';\nimport { getActivationStart } from './lib/getActivationStart';\nimport { getNavigationEntry } from './lib/getNavigationEntry';\nimport { initMetric } from './lib/initMetric';\nimport { whenActivated } from './lib/whenActivated';\nimport type { MetricRatingThresholds, ReportOpts, TTFBMetric } from './types';\n\n/** Thresholds for TTFB. See https://web.dev/articles/ttfb#what_is_a_good_ttfb_score */\nexport const TTFBThresholds: MetricRatingThresholds = [800, 1800];\n\n/**\n * Runs in the next task after the page is done loading and/or prerendering.\n * @param callback\n */\nconst whenReady = (callback: () => void) => {\n  if (WINDOW.document?.prerendering) {\n    whenActivated(() => whenReady(callback));\n  } else if (WINDOW.document?.readyState !== 'complete') {\n    addEventListener('load', () => whenReady(callback), true);\n  } else {\n    // Queue a task so the callback runs after `loadEventEnd`.\n    setTimeout(callback);\n  }\n};\n\n/**\n * Calculates the [TTFB](https://web.dev/articles/ttfb) value for the\n * current page and calls the `callback` function once the page has loaded,\n * along with the relevant `navigation` performance entry used to determine the\n * value. The reported value is a `DOMHighResTimeStamp`.\n *\n * Note, this function waits until after the page is loaded to call `callback`\n * in order to ensure all properties of the `navigation` entry are populated.\n * This is useful if you want to report on other metrics exposed by the\n * [Navigation Timing API](https://w3c.github.io/navigation-timing/). For\n * example, the TTFB metric starts from the page's [time\n * origin](https://www.w3.org/TR/hr-time-2/#sec-time-origin), which means it\n * includes time spent on DNS lookup, connection negotiation, network latency,\n * and server processing time.\n */\nexport const onTTFB = (onReport: (metric: TTFBMetric) => void, opts: ReportOpts = {}) => {\n  const metric = initMetric('TTFB');\n  const report = bindReporter(onReport, metric, TTFBThresholds, opts.reportAllChanges);\n\n  whenReady(() => {\n    const navigationEntry = getNavigationEntry();\n\n    if (navigationEntry) {\n      // The activationStart reference is used because TTFB should be\n      // relative to page activation rather than navigation start if the\n      // page was prerendered. But in cases where `activationStart` occurs\n      // after the first byte is received, this time should be clamped at 0.\n      metric.value = Math.max(navigationEntry.responseStart - getActivationStart(), 0);\n\n      metric.entries = [navigationEntry];\n      report(true);\n    }\n  });\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;CAcA,GAUA,qFAAA,GACO,MAAM,cAAc,GAA2B;IAAC,GAAG;IAAE,IAAI;CAAA;AAEhE;;;CAGA,GACA,MAAM,SAAU,GAAE,CAAC,QAAQ,KAAiB;IAC1C,wLAAI,UAAM,CAAC,QAAQ,EAAE,YAAY,EAAE;4OACjC,gBAAA,AAAa,EAAC,IAAM,SAAS,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAE,MAAO,wLAAI,UAAM,CAAC,QAAQ,EAAE,UAAA,KAAe,UAAU,EAAE;QACrD,gBAAgB,CAAC,MAAM,EAAE,IAAM,SAAS,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC;IAC7D,OAAS;QACT,0DAAA;QACI,UAAU,CAAC,QAAQ,CAAC;IACxB;AACA,CAAC;AAED;;;;;;;;;;;;;;CAcA,GACa,MAAA,MAAA,GAAS,CAAC,QAAQ,EAAgC,IAAI,GAAe,CAAA,CAAE,KAAK;IACvF,MAAM,MAAO,oOAAE,aAAA,AAAU,EAAC,MAAM,CAAC;IACjC,MAAM,MAAA,sOAAS,eAAA,AAAY,EAAC,QAAQ,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,CAAC,gBAAgB,CAAC;IAEpF,SAAS,CAAC,MAAM;QACd,MAAM,eAAA,4OAAkB,qBAAA,AAAkB,EAAE;QAE5C,IAAI,eAAe,EAAE;YACzB,+DAAA;YACA,kEAAA;YACA,oEAAA;YACA,sEAAA;YACM,MAAM,CAAC,KAAA,GAAQ,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,aAAA,4OAAgB,qBAAA,AAAkB,EAAE,GAAE,CAAC,CAAC;YAEhF,MAAM,CAAC,OAAA,GAAU;gBAAC,eAAe;aAAC;YAClC,MAAM,CAAC,IAAI,CAAC;QAClB;IACA,CAAG,CAAC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2038, "column": 0}, "map": {"version": 3, "file": "instrument.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/instrument.ts"], "sourcesContent": ["import { getFunctionName, logger } from '@sentry/core';\nimport { DEBUG_BUILD } from '../debug-build';\nimport { onCLS } from './web-vitals/getCLS';\nimport { onFID } from './web-vitals/getFID';\nimport { onINP } from './web-vitals/getINP';\nimport { onLCP } from './web-vitals/getLCP';\nimport { observe } from './web-vitals/lib/observe';\nimport { onTTFB } from './web-vitals/onTTFB';\n\ntype InstrumentHandlerTypePerformanceObserver =\n  | 'longtask'\n  | 'event'\n  | 'navigation'\n  | 'paint'\n  | 'resource'\n  | 'first-input';\n\ntype InstrumentHandlerTypeMetric = 'cls' | 'lcp' | 'fid' | 'ttfb' | 'inp';\n\n// We provide this here manually instead of relying on a global, as this is not available in non-browser environements\n// And we do not want to expose such types\ninterface PerformanceEntry {\n  readonly duration: number;\n  readonly entryType: string;\n  readonly name: string;\n  readonly startTime: number;\n  toJSON(): Record<string, unknown>;\n}\ninterface PerformanceEventTiming extends PerformanceEntry {\n  processingStart: number;\n  processingEnd: number;\n  duration: number;\n  cancelable?: boolean;\n  target?: unknown | null;\n  interactionId?: number;\n}\n\ninterface PerformanceScriptTiming extends PerformanceEntry {\n  sourceURL: string;\n  sourceFunctionName: string;\n  sourceCharPosition: number;\n  invoker: string;\n  invokerType: string;\n}\nexport interface PerformanceLongAnimationFrameTiming extends PerformanceEntry {\n  scripts: PerformanceScriptTiming[];\n}\n\ninterface Metric {\n  /**\n   * The name of the metric (in acronym form).\n   */\n  name: 'CLS' | 'FCP' | 'FID' | 'INP' | 'LCP' | 'TTFB';\n\n  /**\n   * The current value of the metric.\n   */\n  value: number;\n\n  /**\n   * The rating as to whether the metric value is within the \"good\",\n   * \"needs improvement\", or \"poor\" thresholds of the metric.\n   */\n  rating: 'good' | 'needs-improvement' | 'poor';\n\n  /**\n   * The delta between the current value and the last-reported value.\n   * On the first report, `delta` and `value` will always be the same.\n   */\n  delta: number;\n\n  /**\n   * A unique ID representing this particular metric instance. This ID can\n   * be used by an analytics tool to dedupe multiple values sent for the same\n   * metric instance, or to group multiple deltas together and calculate a\n   * total. It can also be used to differentiate multiple different metric\n   * instances sent from the same page, which can happen if the page is\n   * restored from the back/forward cache (in that case new metrics object\n   * get created).\n   */\n  id: string;\n\n  /**\n   * Any performance entries relevant to the metric value calculation.\n   * The array may also be empty if the metric value was not based on any\n   * entries (e.g. a CLS value of 0 given no layout shifts).\n   */\n  entries: PerformanceEntry[];\n\n  /**\n   * The type of navigation\n   *\n   * Navigation Timing API (or `undefined` if the browser doesn't\n   * support that API). For pages that are restored from the bfcache, this\n   * value will be 'back-forward-cache'.\n   */\n  navigationType: 'navigate' | 'reload' | 'back-forward' | 'back-forward-cache' | 'prerender' | 'restore';\n}\n\ntype InstrumentHandlerType = InstrumentHandlerTypeMetric | InstrumentHandlerTypePerformanceObserver;\n\ntype StopListening = undefined | void | (() => void);\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype InstrumentHandlerCallback = (data: any) => void;\n\ntype CleanupHandlerCallback = () => void;\n\nconst handlers: { [key in InstrumentHandlerType]?: InstrumentHandlerCallback[] } = {};\nconst instrumented: { [key in InstrumentHandlerType]?: boolean } = {};\n\nlet _previousCls: Metric | undefined;\nlet _previousFid: Metric | undefined;\nlet _previousLcp: Metric | undefined;\nlet _previousTtfb: Metric | undefined;\nlet _previousInp: Metric | undefined;\n\n/**\n * Add a callback that will be triggered when a CLS metric is available.\n * Returns a cleanup callback which can be called to remove the instrumentation handler.\n *\n * Pass `stopOnCallback = true` to stop listening for CLS when the cleanup callback is called.\n * This will lead to the CLS being finalized and frozen.\n */\nexport function addClsInstrumentationHandler(\n  callback: (data: { metric: Metric }) => void,\n  stopOnCallback = false,\n): CleanupHandlerCallback {\n  return addMetricObserver('cls', callback, instrumentCls, _previousCls, stopOnCallback);\n}\n\n/**\n * Add a callback that will be triggered when a LCP metric is available.\n * Returns a cleanup callback which can be called to remove the instrumentation handler.\n *\n * Pass `stopOnCallback = true` to stop listening for LCP when the cleanup callback is called.\n * This will lead to the LCP being finalized and frozen.\n */\nexport function addLcpInstrumentationHandler(\n  callback: (data: { metric: Metric }) => void,\n  stopOnCallback = false,\n): CleanupHandlerCallback {\n  return addMetricObserver('lcp', callback, instrumentLcp, _previousLcp, stopOnCallback);\n}\n\n/**\n * Add a callback that will be triggered when a FID metric is available.\n * Returns a cleanup callback which can be called to remove the instrumentation handler.\n */\nexport function addFidInstrumentationHandler(callback: (data: { metric: Metric }) => void): CleanupHandlerCallback {\n  return addMetricObserver('fid', callback, instrumentFid, _previousFid);\n}\n\n/**\n * Add a callback that will be triggered when a FID metric is available.\n */\nexport function addTtfbInstrumentationHandler(callback: (data: { metric: Metric }) => void): CleanupHandlerCallback {\n  return addMetricObserver('ttfb', callback, instrumentTtfb, _previousTtfb);\n}\n\nexport type InstrumentationHandlerCallback = (data: {\n  metric: Omit<Metric, 'entries'> & {\n    entries: PerformanceEventTiming[];\n  };\n}) => void;\n\n/**\n * Add a callback that will be triggered when a INP metric is available.\n * Returns a cleanup callback which can be called to remove the instrumentation handler.\n */\nexport function addInpInstrumentationHandler(callback: InstrumentationHandlerCallback): CleanupHandlerCallback {\n  return addMetricObserver('inp', callback, instrumentInp, _previousInp);\n}\n\nexport function addPerformanceInstrumentationHandler(\n  type: 'event',\n  callback: (data: { entries: ((PerformanceEntry & { target?: unknown | null }) | PerformanceEventTiming)[] }) => void,\n): CleanupHandlerCallback;\nexport function addPerformanceInstrumentationHandler(\n  type: InstrumentHandlerTypePerformanceObserver,\n  callback: (data: { entries: PerformanceEntry[] }) => void,\n): CleanupHandlerCallback;\n\n/**\n * Add a callback that will be triggered when a performance observer is triggered,\n * and receives the entries of the observer.\n * Returns a cleanup callback which can be called to remove the instrumentation handler.\n */\nexport function addPerformanceInstrumentationHandler(\n  type: InstrumentHandlerTypePerformanceObserver,\n  callback: (data: { entries: PerformanceEntry[] }) => void,\n): CleanupHandlerCallback {\n  addHandler(type, callback);\n\n  if (!instrumented[type]) {\n    instrumentPerformanceObserver(type);\n    instrumented[type] = true;\n  }\n\n  return getCleanupCallback(type, callback);\n}\n\n/** Trigger all handlers of a given type. */\nfunction triggerHandlers(type: InstrumentHandlerType, data: unknown): void {\n  const typeHandlers = handlers[type];\n\n  if (!typeHandlers?.length) {\n    return;\n  }\n\n  for (const handler of typeHandlers) {\n    try {\n      handler(data);\n    } catch (e) {\n      DEBUG_BUILD &&\n        logger.error(\n          `Error while triggering instrumentation handler.\\nType: ${type}\\nName: ${getFunctionName(handler)}\\nError:`,\n          e,\n        );\n    }\n  }\n}\n\nfunction instrumentCls(): StopListening {\n  return onCLS(\n    metric => {\n      triggerHandlers('cls', {\n        metric,\n      });\n      _previousCls = metric;\n    },\n    // We want the callback to be called whenever the CLS value updates.\n    // By default, the callback is only called when the tab goes to the background.\n    { reportAllChanges: true },\n  );\n}\n\nfunction instrumentFid(): void {\n  return onFID(metric => {\n    triggerHandlers('fid', {\n      metric,\n    });\n    _previousFid = metric;\n  });\n}\n\nfunction instrumentLcp(): StopListening {\n  return onLCP(\n    metric => {\n      triggerHandlers('lcp', {\n        metric,\n      });\n      _previousLcp = metric;\n    },\n    // We want the callback to be called whenever the LCP value updates.\n    // By default, the callback is only called when the tab goes to the background.\n    { reportAllChanges: true },\n  );\n}\n\nfunction instrumentTtfb(): StopListening {\n  return onTTFB(metric => {\n    triggerHandlers('ttfb', {\n      metric,\n    });\n    _previousTtfb = metric;\n  });\n}\n\nfunction instrumentInp(): void {\n  return onINP(metric => {\n    triggerHandlers('inp', {\n      metric,\n    });\n    _previousInp = metric;\n  });\n}\n\nfunction addMetricObserver(\n  type: InstrumentHandlerTypeMetric,\n  callback: InstrumentHandlerCallback,\n  instrumentFn: () => StopListening,\n  previousValue: Metric | undefined,\n  stopOnCallback = false,\n): CleanupHandlerCallback {\n  addHandler(type, callback);\n\n  let stopListening: StopListening | undefined;\n\n  if (!instrumented[type]) {\n    stopListening = instrumentFn();\n    instrumented[type] = true;\n  }\n\n  if (previousValue) {\n    callback({ metric: previousValue });\n  }\n\n  return getCleanupCallback(type, callback, stopOnCallback ? stopListening : undefined);\n}\n\nfunction instrumentPerformanceObserver(type: InstrumentHandlerTypePerformanceObserver): void {\n  const options: PerformanceObserverInit = {};\n\n  // Special per-type options we want to use\n  if (type === 'event') {\n    options.durationThreshold = 0;\n  }\n\n  observe(\n    type,\n    entries => {\n      triggerHandlers(type, { entries });\n    },\n    options,\n  );\n}\n\nfunction addHandler(type: InstrumentHandlerType, handler: InstrumentHandlerCallback): void {\n  handlers[type] = handlers[type] || [];\n  (handlers[type] as InstrumentHandlerCallback[]).push(handler);\n}\n\n// Get a callback which can be called to remove the instrumentation handler\nfunction getCleanupCallback(\n  type: InstrumentHandlerType,\n  callback: InstrumentHandlerCallback,\n  stopListening: StopListening,\n): CleanupHandlerCallback {\n  return () => {\n    if (stopListening) {\n      stopListening();\n    }\n\n    const typeHandlers = handlers[type];\n\n    if (!typeHandlers) {\n      return;\n    }\n\n    const index = typeHandlers.indexOf(callback);\n    if (index !== -1) {\n      typeHandlers.splice(index, 1);\n    }\n  };\n}\n\n/**\n * Check if a PerformanceEntry is a PerformanceEventTiming by checking for the `duration` property.\n */\nexport function isPerformanceEventTiming(entry: PerformanceEntry): entry is PerformanceEventTiming {\n  return 'duration' in entry;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AA4GA,MAAM,QAAQ,GAAqE,CAAA,CAAE;AACrF,MAAM,YAAY,GAAiD,CAAA,CAAE;AAErE,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,YAAY;AAEhB;;;;;;CAMA,GACO,SAAS,4BAA4B,CAC1C,QAAQ,EACR,cAAA,GAAiB,KAAK;IAEtB,OAAO,iBAAiB,CAAC,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,YAAY,EAAE,cAAc,CAAC;AACxF;AAEA;;;;;;CAMA,GACO,SAAS,4BAA4B,CAC1C,QAAQ,EACR,cAAA,GAAiB,KAAK;IAEtB,OAAO,iBAAiB,CAAC,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,YAAY,EAAE,cAAc,CAAC;AACxF;AAEA;;;CAGA,GACO,SAAS,4BAA4B,CAAC,QAAQ,EAA8D;IACjH,OAAO,iBAAiB,CAAC,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,YAAY,CAAC;AACxE;AAEA;;CAEA,GACO,SAAS,6BAA6B,CAAC,QAAQ,EAA8D;IAClH,OAAO,iBAAiB,CAAC,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,aAAa,CAAC;AAC3E;AAQA;;;CAGA,GACO,SAAS,4BAA4B,CAAC,QAAQ,EAA0D;IAC7G,OAAO,iBAAiB,CAAC,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,YAAY,CAAC;AACxE;AAWA;;;;CAIA,GACO,SAAS,oCAAoC,CAClD,IAAI,EACJ,QAAQ;IAER,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC;IAE1B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;QACvB,6BAA6B,CAAC,IAAI,CAAC;QACnC,YAAY,CAAC,IAAI,CAAA,GAAI,IAAI;IAC7B;IAEE,OAAO,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC;AAC3C;AAEA,0CAAA,GACA,SAAS,eAAe,CAAC,IAAI,EAAyB,IAAI,EAAiB;IACzE,MAAM,YAAa,GAAE,QAAQ,CAAC,IAAI,CAAC;IAEnC,IAAI,CAAC,YAAY,EAAE,MAAM,EAAE;QACzB;IACJ;IAEE,KAAK,MAAM,OAAQ,IAAG,YAAY,CAAE;QAClC,IAAI;YACF,OAAO,CAAC,IAAI,CAAC;QACnB,CAAM,CAAA,OAAO,CAAC,EAAE;0MACV,cAAY,oLACV,SAAM,CAAC,KAAK,CACV,CAAC,uDAAuD,EAAE,IAAI,CAAC,QAAQ,MAAE,sMAAA,AAAe,EAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,EAC3G,CAAC;QAEX;IACA;AACA;AAEA,SAAS,aAAa,GAAkB;IACtC,WAAO,0NAAA,AAAK,GACV,UAAU;QACR,eAAe,CAAC,KAAK,EAAE;YACrB,MAAM;QACd,CAAO,CAAC;QACF,YAAA,GAAe,MAAM;IAC3B,CAAK,EACL,oEAAA;IACA,+EAAA;IACI;QAAE,gBAAgB,EAAE,IAAA;IAAA,CAAM;AAE9B;AAEA,SAAS,aAAa,GAAS;IAC7B,6NAAO,QAAA,AAAK,GAAC,MAAA,IAAU;QACrB,eAAe,CAAC,KAAK,EAAE;YACrB,MAAM;QACZ,CAAK,CAAC;QACF,YAAA,GAAe,MAAM;IACzB,CAAG,CAAC;AACJ;AAEA,SAAS,aAAa,GAAkB;IACtC,6NAAO,QAAA,AAAK,GACV,UAAU;QACR,eAAe,CAAC,KAAK,EAAE;YACrB,MAAM;QACd,CAAO,CAAC;QACF,YAAA,GAAe,MAAM;IAC3B,CAAK,EACL,oEAAA;IACA,+EAAA;IACI;QAAE,gBAAgB,EAAE,IAAA;IAAA,CAAM;AAE9B;AAEA,SAAS,cAAc,GAAkB;IACvC,6NAAO,SAAA,AAAM,GAAC,MAAA,IAAU;QACtB,eAAe,CAAC,MAAM,EAAE;YACtB,MAAM;QACZ,CAAK,CAAC;QACF,aAAA,GAAgB,MAAM;IAC1B,CAAG,CAAC;AACJ;AAEA,SAAS,aAAa,GAAS;IAC7B,WAAO,0NAAA,AAAK,GAAC,MAAA,IAAU;QACrB,eAAe,CAAC,KAAK,EAAE;YACrB,MAAM;QACZ,CAAK,CAAC;QACF,YAAA,GAAe,MAAM;IACzB,CAAG,CAAC;AACJ;AAEA,SAAS,iBAAiB,CACxB,IAAI,EACJ,QAAQ,EACR,YAAY,EACZ,aAAa,EACb,cAAA,GAAiB,KAAK;IAEtB,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC;IAE1B,IAAI,aAAa;IAEjB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;QACvB,aAAc,GAAE,YAAY,EAAE;QAC9B,YAAY,CAAC,IAAI,CAAA,GAAI,IAAI;IAC7B;IAEE,IAAI,aAAa,EAAE;QACjB,QAAQ,CAAC;YAAE,MAAM,EAAE,aAAA;QAAA,CAAe,CAAC;IACvC;IAEE,OAAO,kBAAkB,CAAC,IAAI,EAAE,QAAQ,EAAE,cAAA,GAAiB,aAAA,GAAgB,SAAS,CAAC;AACvF;AAEA,SAAS,6BAA6B,CAAC,IAAI,EAAkD;IAC3F,MAAM,OAAO,GAA4B,CAAA,CAAE;IAE7C,0CAAA;IACE,IAAI,IAAK,KAAI,OAAO,EAAE;QACpB,OAAO,CAAC,iBAAkB,GAAE,CAAC;IACjC;KAEE,uOAAA,AAAO,EACL,IAAI,GACJ,WAAW;QACT,eAAe,CAAC,IAAI,EAAE;YAAE,OAAA;QAAA,CAAS,CAAC;IACxC,CAAK,EACD,OAAO;AAEX;AAEA,SAAS,UAAU,CAAC,IAAI,EAAyB,OAAO,EAAmC;IACzF,QAAQ,CAAC,IAAI,CAAE,GAAE,QAAQ,CAAC,IAAI,CAAA,IAAK,EAAE;IACpC,QAAQ,CAAC,IAAI,CAAA,CAAkC,IAAI,CAAC,OAAO,CAAC;AAC/D;AAEA,2EAAA;AACA,SAAS,kBAAkB,CACzB,IAAI,EACJ,QAAQ,EACR,aAAa;IAEb,OAAO,MAAM;QACX,IAAI,aAAa,EAAE;YACjB,aAAa,EAAE;QACrB;QAEI,MAAM,YAAa,GAAE,QAAQ,CAAC,IAAI,CAAC;QAEnC,IAAI,CAAC,YAAY,EAAE;YACjB;QACN;QAEI,MAAM,QAAQ,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC;QAC5C,IAAI,KAAA,KAAU,CAAA,CAAE,EAAE;YAChB,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;QACnC;IACA,CAAG;AACH;AAEA;;CAEA,GACO,SAAS,wBAAwB,CAAC,KAAK,EAAqD;IACjG,OAAO,UAAW,IAAG,KAAK;AAC5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2238, "column": 0}, "map": {"version": 3, "file": "utils.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/utils.ts"], "sourcesContent": ["import type { Integration, SentrySpan, Span, SpanAttributes, SpanTimeInput, StartSpanOptions } from '@sentry/core';\nimport { getClient, getCurrentScope, spanToJSON, startInactiveSpan, withActiveSpan } from '@sentry/core';\nimport { WINDOW } from '../types';\n\n/**\n * Checks if a given value is a valid measurement value.\n */\nexport function isMeasurementValue(value: unknown): value is number {\n  return typeof value === 'number' && isFinite(value);\n}\n\n/**\n * Helper function to start child on transactions. This function will make sure that the transaction will\n * use the start timestamp of the created child span if it is earlier than the transactions actual\n * start timestamp.\n */\nexport function startAndEndSpan(\n  parentSpan: Span,\n  startTimeInSeconds: number,\n  endTime: SpanTimeInput,\n  { ...ctx }: StartSpanOptions,\n): Span | undefined {\n  const parentStartTime = spanToJSON(parentSpan).start_timestamp;\n  if (parentStartTime && parentStartTime > startTimeInSeconds) {\n    // We can only do this for SentrySpans...\n    if (typeof (parentSpan as Partial<SentrySpan>).updateStartTime === 'function') {\n      (parentSpan as SentrySpan).updateStartTime(startTimeInSeconds);\n    }\n  }\n\n  // The return value only exists for tests\n  return withActiveSpan(parentSpan, () => {\n    const span = startInactiveSpan({\n      startTime: startTimeInSeconds,\n      ...ctx,\n    });\n\n    if (span) {\n      span.end(endTime);\n    }\n\n    return span;\n  });\n}\n\ninterface StandaloneWebVitalSpanOptions {\n  name: string;\n  transaction?: string;\n  attributes: SpanAttributes;\n  startTime: number;\n}\n\n/**\n * Starts an inactive, standalone span used to send web vital values to Sentry.\n * DO NOT use this for arbitrary spans, as these spans require special handling\n * during ingestion to extract metrics.\n *\n * This function adds a bunch of attributes and data to the span that's shared\n * by all web vital standalone spans. However, you need to take care of adding\n * the actual web vital value as an event to the span. Also, you need to assign\n * a transaction name and some other values that are specific to the web vital.\n *\n * Ultimately, you also need to take care of ending the span to send it off.\n *\n * @param options\n *\n * @returns an inactive, standalone and NOT YET ended span\n */\nexport function startStandaloneWebVitalSpan(options: StandaloneWebVitalSpanOptions): Span | undefined {\n  const client = getClient();\n  if (!client) {\n    return;\n  }\n\n  const { name, transaction, attributes: passedAttributes, startTime } = options;\n\n  const { release, environment, sendDefaultPii } = client.getOptions();\n  // We need to get the replay, user, and activeTransaction from the current scope\n  // so that we can associate replay id, profile id, and a user display to the span\n  const replay = client.getIntegrationByName<Integration & { getReplayId: () => string }>('Replay');\n  const replayId = replay?.getReplayId();\n\n  const scope = getCurrentScope();\n\n  const user = scope.getUser();\n  const userDisplay = user !== undefined ? user.email || user.id || user.ip_address : undefined;\n\n  let profileId: string | undefined;\n  try {\n    // @ts-expect-error skip optional chaining to save bundle size with try catch\n    profileId = scope.getScopeData().contexts.profile.profile_id;\n  } catch {\n    // do nothing\n  }\n\n  const attributes: SpanAttributes = {\n    release,\n    environment,\n\n    user: userDisplay || undefined,\n    profile_id: profileId || undefined,\n    replay_id: replayId || undefined,\n\n    transaction,\n\n    // Web vital score calculation relies on the user agent to account for different\n    // browsers setting different thresholds for what is considered a good/meh/bad value.\n    // For example: Chrome vs. Chrome Mobile\n    'user_agent.original': WINDOW.navigator?.userAgent,\n\n    // This tells Sentry to infer the IP address from the request\n    'client.address': sendDefaultPii ? '{{auto}}' : undefined,\n\n    ...passedAttributes,\n  };\n\n  return startInactiveSpan({\n    name,\n    attributes,\n    startTime,\n    experimental: {\n      standalone: true,\n    },\n  });\n}\n\n/** Get the browser performance API. */\nexport function getBrowserPerformanceAPI(): Performance | undefined {\n  // @ts-expect-error we want to make sure all of these are available, even if TS is sure they are\n  return WINDOW.addEventListener && WINDOW.performance;\n}\n\n/**\n * Converts from milliseconds to seconds\n * @param time time in ms\n */\nexport function msToSec(time: number): number {\n  return time / 1000;\n}\n\n/**\n * Converts ALPN protocol ids to name and version.\n *\n * (https://www.iana.org/assignments/tls-extensiontype-values/tls-extensiontype-values.xhtml#alpn-protocol-ids)\n * @param nextHopProtocol PerformanceResourceTiming.nextHopProtocol\n */\nexport function extractNetworkProtocol(nextHopProtocol: string): { name: string; version: string } {\n  let name = 'unknown';\n  let version = 'unknown';\n  let _name = '';\n  for (const char of nextHopProtocol) {\n    // http/1.1 etc.\n    if (char === '/') {\n      [name, version] = nextHopProtocol.split('/') as [string, string];\n      break;\n    }\n    // h2, h3 etc.\n    if (!isNaN(Number(char))) {\n      name = _name === 'h' ? 'http' : _name;\n      version = nextHopProtocol.split(_name)[1] as string;\n      break;\n    }\n    _name += char;\n  }\n  if (_name === nextHopProtocol) {\n    // webrtc, ftp, etc.\n    name = _name;\n  }\n  return { name, version };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAIA;;CAEA,GACO,SAAS,kBAAkB,CAAC,KAAK,EAA4B;IAClE,OAAO,OAAO,KAAM,KAAI,YAAY,QAAQ,CAAC,KAAK,CAAC;AACrD;AAEA;;;;CAIA,GACO,SAAS,eAAe,CAC7B,UAAU,EACV,kBAAkB,EAClB,OAAO,EACP,EAAE,GAAG,GAAA,EAAK;IAEV,MAAM,mBAAkB,0LAAA,AAAU,EAAC,UAAU,CAAC,CAAC,eAAe;IAC9D,IAAI,eAAA,IAAmB,eAAgB,GAAE,kBAAkB,EAAE;QAC/D,yCAAA;QACI,IAAI,OAAO,AAAC,UAAA,CAAmC,eAAA,KAAoB,UAAU,EAAE;YAC5E,UAAW,CAAe,eAAe,CAAC,kBAAkB,CAAC;QACpE;IACA;IAEA,yCAAA;IACE,mLAAO,iBAAA,AAAc,EAAC,UAAU,EAAE,MAAM;QACtC,MAAM,IAAA,+KAAO,oBAAA,AAAiB,EAAC;YAC7B,SAAS,EAAE,kBAAkB;YAC7B,GAAG,GAAG;QACZ,CAAK,CAAC;QAEF,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;QACvB;QAEI,OAAO,IAAI;IACf,CAAG,CAAC;AACJ;AASA;;;;;;;;;;;;;;;CAeA,GACO,SAAS,2BAA2B,CAAC,OAAO,EAAmD;IACpG,MAAM,MAAA,4KAAS,YAAA,AAAS,EAAE;IAC1B,IAAI,CAAC,MAAM,EAAE;QACX;IACJ;IAEE,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,UAAU,EAAE,gBAAgB,EAAE,SAAU,EAAA,GAAI,OAAO;IAE9E,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,cAAe,EAAA,GAAI,MAAM,CAAC,UAAU,EAAE;IACtE,gFAAA;IACA,iFAAA;IACE,MAAM,SAAS,MAAM,CAAC,oBAAoB,CAA8C,QAAQ,CAAC;IACjG,MAAM,QAAS,GAAE,MAAM,EAAE,WAAW,EAAE;IAEtC,MAAM,KAAA,IAAQ,0LAAA,AAAe,EAAE;IAE/B,MAAM,IAAK,GAAE,KAAK,CAAC,OAAO,EAAE;IAC5B,MAAM,WAAY,GAAE,SAAS,SAAA,GAAY,IAAI,CAAC,KAAA,IAAS,IAAI,CAAC,EAAG,IAAG,IAAI,CAAC,UAAA,GAAa,SAAS;IAE7F,IAAI,SAAS;IACb,IAAI;QACN,6EAAA;QACI,SAAU,GAAE,KAAK,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU;IAChE,EAAI,OAAM;IACV,aAAA;IACA;IAEE,MAAM,UAAU,GAAmB;QACjC,OAAO;QACP,WAAW;QAEX,IAAI,EAAE,WAAY,IAAG,SAAS;QAC9B,UAAU,EAAE,SAAU,IAAG,SAAS;QAClC,SAAS,EAAE,QAAS,IAAG,SAAS;QAEhC,WAAW;QAEf,gFAAA;QACA,qFAAA;QACA,wCAAA;QACI,qBAAqB,uLAAE,SAAM,CAAC,SAAS,EAAE,SAAS;QAEtD,6DAAA;QACI,gBAAgB,EAAE,cAAA,GAAiB,UAAA,GAAa,SAAS;QAEzD,GAAG,gBAAgB;IACvB,CAAG;IAED,QAAO,+LAAA,AAAiB,EAAC;QACvB,IAAI;QACJ,UAAU;QACV,SAAS;QACT,YAAY,EAAE;YACZ,UAAU,EAAE,IAAI;QACtB,CAAK;IACL,CAAG,CAAC;AACJ;AAEA,qCAAA,GACO,SAAS,wBAAwB,GAA4B;IACpE,gGAAA;IACE,4LAAO,SAAM,CAAC,gBAAA,yLAAoB,SAAM,CAAC,WAAW;AACtD;AAEA;;;CAGA,GACO,SAAS,OAAO,CAAC,IAAI,EAAkB;IAC5C,OAAO,IAAK,GAAE,IAAI;AACpB;AAEA;;;;;CAKA,GACO,SAAS,sBAAsB,CAAC,eAAe,EAA6C;IACjG,IAAI,IAAK,GAAE,SAAS;IACpB,IAAI,OAAQ,GAAE,SAAS;IACvB,IAAI,KAAM,GAAE,EAAE;IACd,KAAK,MAAM,IAAK,IAAG,eAAe,CAAE;QACtC,gBAAA;QACI,IAAI,IAAK,KAAI,GAAG,EAAE;YAChB,CAAC,IAAI,EAAE,OAAO,CAAA,GAAI,eAAe,CAAC,KAAK,CAAC,GAAG,CAAE;YAC7C;QACN;QACA,cAAA;QACI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;YACxB,IAAA,GAAO,KAAM,KAAI,MAAM,MAAA,GAAS,KAAK;YACrC,OAAA,GAAU,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAE;YAC1C;QACN;QACI,KAAA,IAAS,IAAI;IACjB;IACE,IAAI,KAAM,KAAI,eAAe,EAAE;QACjC,oBAAA;QACI,IAAA,GAAO,KAAK;IAChB;IACE,OAAO;QAAE,IAAI;QAAE;IAAA,CAAS;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2391, "column": 0}, "map": {"version": 3, "file": "inp.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/inp.ts"], "sourcesContent": ["import type { Span, SpanAttributes } from '@sentry/core';\nimport {\n  browserPerformanceTimeOrigin,\n  getActiveSpan,\n  getCurrentScope,\n  getRootSpan,\n  htmlTreeAsString,\n  SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME,\n  SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT,\n  SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  spanToJSON,\n} from '@sentry/core';\nimport type { InstrumentationHandlerCallback } from './instrument';\nimport {\n  addInpInstrumentationHandler,\n  addPerformanceInstrumentationHandler,\n  isPerformanceEventTiming,\n} from './instrument';\nimport { getBrowserPerformanceAPI, msToSec, startStandaloneWebVitalSpan } from './utils';\n\nconst LAST_INTERACTIONS: number[] = [];\nconst INTERACTIONS_SPAN_MAP = new Map<number, Span>();\n\n/**\n * 60 seconds is the maximum for a plausible INP value\n * (source: Me)\n */\nconst MAX_PLAUSIBLE_INP_DURATION = 60;\n/**\n * Start tracking INP webvital events.\n */\nexport function startTrackingINP(): () => void {\n  const performance = getBrowserPerformanceAPI();\n  if (performance && browserPerformanceTimeOrigin()) {\n    const inpCallback = _trackINP();\n\n    return (): void => {\n      inpCallback();\n    };\n  }\n\n  return () => undefined;\n}\n\nconst INP_ENTRY_MAP: Record<string, 'click' | 'hover' | 'drag' | 'press'> = {\n  click: 'click',\n  pointerdown: 'click',\n  pointerup: 'click',\n  mousedown: 'click',\n  mouseup: 'click',\n  touchstart: 'click',\n  touchend: 'click',\n  mouseover: 'hover',\n  mouseout: 'hover',\n  mouseenter: 'hover',\n  mouseleave: 'hover',\n  pointerover: 'hover',\n  pointerout: 'hover',\n  pointerenter: 'hover',\n  pointerleave: 'hover',\n  dragstart: 'drag',\n  dragend: 'drag',\n  drag: 'drag',\n  dragenter: 'drag',\n  dragleave: 'drag',\n  dragover: 'drag',\n  drop: 'drag',\n  keydown: 'press',\n  keyup: 'press',\n  keypress: 'press',\n  input: 'press',\n};\n\n/** Starts tracking the Interaction to Next Paint on the current page. #\n * exported only for testing\n */\nexport function _trackINP(): () => void {\n  return addInpInstrumentationHandler(_onInp);\n}\n\n/**\n * exported only for testing\n */\nexport const _onInp: InstrumentationHandlerCallback = ({ metric }) => {\n  if (metric.value == undefined) {\n    return;\n  }\n\n  const duration = msToSec(metric.value);\n\n  // We received occasional reports of hour-long INP values.\n  // Therefore, we add a sanity check to avoid creating spans for\n  // unrealistically long INP durations.\n  if (duration > MAX_PLAUSIBLE_INP_DURATION) {\n    return;\n  }\n\n  const entry = metric.entries.find(entry => entry.duration === metric.value && INP_ENTRY_MAP[entry.name]);\n\n  if (!entry) {\n    return;\n  }\n\n  const { interactionId } = entry;\n  const interactionType = INP_ENTRY_MAP[entry.name];\n\n  /** Build the INP span, create an envelope from the span, and then send the envelope */\n  const startTime = msToSec((browserPerformanceTimeOrigin() as number) + entry.startTime);\n  const activeSpan = getActiveSpan();\n  const rootSpan = activeSpan ? getRootSpan(activeSpan) : undefined;\n\n  // We first try to lookup the span from our INTERACTIONS_SPAN_MAP,\n  // where we cache the route per interactionId\n  const cachedSpan = interactionId != null ? INTERACTIONS_SPAN_MAP.get(interactionId) : undefined;\n\n  const spanToUse = cachedSpan || rootSpan;\n\n  // Else, we try to use the active span.\n  // Finally, we fall back to look at the transactionName on the scope\n  const routeName = spanToUse ? spanToJSON(spanToUse).description : getCurrentScope().getScopeData().transactionName;\n\n  const name = htmlTreeAsString(entry.target);\n  const attributes: SpanAttributes = {\n    [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.http.browser.inp',\n    [SEMANTIC_ATTRIBUTE_SENTRY_OP]: `ui.interaction.${interactionType}`,\n    [SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME]: entry.duration,\n  };\n\n  const span = startStandaloneWebVitalSpan({\n    name,\n    transaction: routeName,\n    attributes,\n    startTime,\n  });\n\n  if (span) {\n    span.addEvent('inp', {\n      [SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT]: 'millisecond',\n      [SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE]: metric.value,\n    });\n\n    span.end(startTime + duration);\n  }\n};\n\n/**\n * Register a listener to cache route information for INP interactions.\n */\nexport function registerInpInteractionListener(): void {\n  const handleEntries = ({ entries }: { entries: PerformanceEntry[] }): void => {\n    const activeSpan = getActiveSpan();\n    const activeRootSpan = activeSpan && getRootSpan(activeSpan);\n\n    entries.forEach(entry => {\n      if (!isPerformanceEventTiming(entry) || !activeRootSpan) {\n        return;\n      }\n\n      const interactionId = entry.interactionId;\n      if (interactionId == null) {\n        return;\n      }\n\n      // If the interaction was already recorded before, nothing more to do\n      if (INTERACTIONS_SPAN_MAP.has(interactionId)) {\n        return;\n      }\n\n      // We keep max. 10 interactions in the list, then remove the oldest one & clean up\n      if (LAST_INTERACTIONS.length > 10) {\n        const last = LAST_INTERACTIONS.shift() as number;\n        INTERACTIONS_SPAN_MAP.delete(last);\n      }\n\n      // We add the interaction to the list of recorded interactions\n      // and store the span for this interaction\n      LAST_INTERACTIONS.push(interactionId);\n      INTERACTIONS_SPAN_MAP.set(interactionId, activeRootSpan);\n    });\n  };\n\n  addPerformanceInstrumentationHandler('event', handleEntries);\n  addPerformanceInstrumentationHandler('first-input', handleEntries);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAsBA,MAAM,iBAAiB,GAAa,EAAE;AACtC,MAAM,qBAAsB,GAAE,IAAI,GAAG,EAAgB;AAErD;;;CAGA,GACA,MAAM,0BAAA,GAA6B,EAAE;AACrC;;CAEA,GACO,SAAS,gBAAgB,GAAe;IAC7C,MAAM,WAAA,uMAAc,2BAAA,AAAwB,EAAE;IAC9C,IAAI,WAAA,sLAAe,+BAAA,AAA4B,EAAE,GAAE;QACjD,MAAM,WAAA,GAAc,SAAS,EAAE;QAE/B,OAAO,MAAY;YACjB,WAAW,EAAE;QACnB,CAAK;IACL;IAEE,OAAO,IAAM,SAAS;AACxB;AAEA,MAAM,aAAa,GAAyD;IAC1E,KAAK,EAAE,OAAO;IACd,WAAW,EAAE,OAAO;IACpB,SAAS,EAAE,OAAO;IAClB,SAAS,EAAE,OAAO;IAClB,OAAO,EAAE,OAAO;IAChB,UAAU,EAAE,OAAO;IACnB,QAAQ,EAAE,OAAO;IACjB,SAAS,EAAE,OAAO;IAClB,QAAQ,EAAE,OAAO;IACjB,UAAU,EAAE,OAAO;IACnB,UAAU,EAAE,OAAO;IACnB,WAAW,EAAE,OAAO;IACpB,UAAU,EAAE,OAAO;IACnB,YAAY,EAAE,OAAO;IACrB,YAAY,EAAE,OAAO;IACrB,SAAS,EAAE,MAAM;IACjB,OAAO,EAAE,MAAM;IACf,IAAI,EAAE,MAAM;IACZ,SAAS,EAAE,MAAM;IACjB,SAAS,EAAE,MAAM;IACjB,QAAQ,EAAE,MAAM;IAChB,IAAI,EAAE,MAAM;IACZ,OAAO,EAAE,OAAO;IAChB,KAAK,EAAE,OAAO;IACd,QAAQ,EAAE,OAAO;IACjB,KAAK,EAAE,OAAO;AAChB,CAAC;AAED;;CAEA,GACO,SAAS,SAAS,GAAe;IACtC,gNAAO,+BAAA,AAA4B,EAAC,MAAM,CAAC;AAC7C;AAEA;;CAEA,GACO,MAAM,MAAM,GAAmC,CAAC,EAAE,MAAA,EAAQ,KAAK;IACpE,IAAI,MAAM,CAAC,KAAM,IAAG,SAAS,EAAE;QAC7B;IACJ;IAEE,MAAM,+MAAW,UAAA,AAAO,EAAC,MAAM,CAAC,KAAK,CAAC;IAExC,0DAAA;IACA,+DAAA;IACA,sCAAA;IACE,IAAI,QAAS,GAAE,0BAA0B,EAAE;QACzC;IACJ;IAEE,MAAM,KAAM,GAAE,MAAM,CAAC,OAAO,CAAC,IAAI,EAAC,KAAA,GAAS,KAAK,CAAC,QAAS,KAAI,MAAM,CAAC,KAAM,IAAG,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAExG,IAAI,CAAC,KAAK,EAAE;QACV;IACJ;IAEE,MAAM,EAAE,aAAc,EAAA,GAAI,KAAK;IAC/B,MAAM,kBAAkB,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC;IAEnD,qFAAA,GACE,MAAM,SAAA,uMAAY,UAAA,AAAO,EAAC,kLAAC,+BAAA,AAA4B,EAAC,IAAe,KAAK,CAAC,SAAS,CAAC;IACvF,MAAM,UAAA,IAAa,6LAAA,AAAa,EAAE;IAClC,MAAM,QAAS,GAAE,UAAW,iLAAE,cAAA,AAAW,EAAC,UAAU,CAAE,GAAE,SAAS;IAEnE,kEAAA;IACA,6CAAA;IACE,MAAM,UAAA,GAAa,aAAA,IAAiB,IAAK,GAAE,qBAAqB,CAAC,GAAG,CAAC,aAAa,CAAA,GAAI,SAAS;IAE/F,MAAM,SAAA,GAAY,UAAA,IAAc,QAAQ;IAE1C,uCAAA;IACA,oEAAA;IACE,MAAM,YAAY,SAAA,iLAAY,aAAA,AAAU,EAAC,SAAS,CAAC,CAAC,WAAA,4KAAc,kBAAA,AAAe,EAAE,EAAC,YAAY,EAAE,CAAC,eAAe;IAElH,MAAM,4LAAO,mBAAA,AAAgB,EAAC,KAAK,CAAC,MAAM,CAAC;IAC3C,MAAM,UAAU,GAAmB;QACjC,2KAAC,mCAAgC,CAAA,EAAG,uBAAuB;QAC3D,2KAAC,+BAA4B,CAAA,EAAG,CAAC,eAAe,EAAE,eAAe,CAAC,CAAA;QACA,2KAAA,oCAAA,CAAA,EAAA,KAAA,CAAA,QAAA;IACA,CAAA;IAEA,MAAA,IAAA,uMAAA,8BAAA,EAAA;QACA,IAAA;QACA,WAAA,EAAA,SAAA;QACA,UAAA;QACA,SAAA;IACA,CAAA,CAAA;IAEA,IAAA,IAAA,EAAA;QACA,IAAA,CAAA,QAAA,CAAA,KAAA,EAAA;YACA,2KAAA,6CAAA,CAAA,EAAA,aAAA;YACA,2KAAA,8CAAA,CAAA,EAAA,MAAA,CAAA,KAAA;QACA,CAAA,CAAA;QAEA,IAAA,CAAA,GAAA,CAAA,SAAA,GAAA,QAAA,CAAA;IACA;AACA;AAEA;;CAEA,GACA,SAAA,8BAAA,GAAA;IACA,MAAA,aAAA,GAAA,CAAA,EAAA,OAAA,EAAA,KAAA;QACA,MAAA,UAAA,iLAAA,gBAAA,EAAA;QACA,MAAA,cAAA,GAAA,UAAA,KAAA,2LAAA,EAAA,UAAA,CAAA;QAEA,OAAA,CAAA,OAAA,EAAA,KAAA,IAAA;YACA,IAAA,0MAAA,2BAAA,EAAA,KAAA,CAAA,IAAA,CAAA,cAAA,EAAA;gBACA;YACA;YAEA,MAAA,aAAA,GAAA,KAAA,CAAA,aAAA;YACA,IAAA,aAAA,IAAA,IAAA,EAAA;gBACA;YACA;YAEA,qEAAA;YACA,IAAA,qBAAA,CAAA,GAAA,CAAA,aAAA,CAAA,EAAA;gBACA;YACA;YAEA,kFAAA;YACA,IAAA,iBAAA,CAAA,MAAA,GAAA,EAAA,EAAA;gBACA,MAAA,IAAA,GAAA,iBAAA,CAAA,KAAA,EAAA;gBACA,qBAAA,CAAA,MAAA,CAAA,IAAA,CAAA;YACA;YAEA,8DAAA;YACA,0CAAA;YACA,iBAAA,CAAA,IAAA,CAAA,aAAA,CAAA;YACA,qBAAA,CAAA,GAAA,CAAA,aAAA,EAAA,cAAA,CAAA;QACA,CAAA,CAAA;IACA,CAAA;6MAEA,uCAAA,EAAA,OAAA,EAAA,aAAA,CAAA;6MACA,uCAAA,EAAA,aAAA,EAAA,aAAA,CAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2547, "column": 0}, "map": {"version": 3, "file": "cls.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/cls.ts"], "sourcesContent": ["import type { SpanAttributes } from '@sentry/core';\nimport {\n  browserPerformanceTimeOrigin,\n  getActiveSpan,\n  getClient,\n  getCurrentScope,\n  getRootSpan,\n  htmlTreeAsString,\n  logger,\n  SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME,\n  SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT,\n  SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  spanToJSON,\n} from '@sentry/core';\nimport { DEBUG_BUILD } from '../debug-build';\nimport { addClsInstrumentationHandler } from './instrument';\nimport { msToSec, startStandaloneWebVitalSpan } from './utils';\nimport { onHidden } from './web-vitals/lib/onHidden';\n\n/**\n * Starts tracking the Cumulative Layout Shift on the current page and collects the value once\n *\n * - the page visibility is hidden\n * - a navigation span is started (to stop CLS measurement for SPA soft navigations)\n *\n * Once either of these events triggers, the CLS value is sent as a standalone span and we stop\n * measuring CLS.\n */\nexport function trackClsAsStandaloneSpan(): void {\n  let standaloneCLsValue = 0;\n  let standaloneClsEntry: LayoutShift | undefined;\n  let pageloadSpanId: string | undefined;\n\n  if (!supportsLayoutShift()) {\n    return;\n  }\n\n  let sentSpan = false;\n  function _collectClsOnce() {\n    if (sentSpan) {\n      return;\n    }\n    sentSpan = true;\n    if (pageloadSpanId) {\n      sendStandaloneClsSpan(standaloneCLsValue, standaloneClsEntry, pageloadSpanId);\n    }\n    cleanupClsHandler();\n  }\n\n  const cleanupClsHandler = addClsInstrumentationHandler(({ metric }) => {\n    const entry = metric.entries[metric.entries.length - 1] as LayoutShift | undefined;\n    if (!entry) {\n      return;\n    }\n    standaloneCLsValue = metric.value;\n    standaloneClsEntry = entry;\n  }, true);\n\n  // TODO: Figure out if we can switch to using whenIdleOrHidden instead of onHidden\n  // use pagehide event from web-vitals\n  onHidden(() => {\n    _collectClsOnce();\n  });\n\n  // Since the call chain of this function is synchronous and evaluates before the SDK client is created,\n  // we need to wait with subscribing to a client hook until the client is created. Therefore, we defer\n  // to the next tick after the SDK setup.\n  setTimeout(() => {\n    const client = getClient();\n\n    if (!client) {\n      return;\n    }\n\n    const unsubscribeStartNavigation = client.on('startNavigationSpan', () => {\n      _collectClsOnce();\n      unsubscribeStartNavigation?.();\n    });\n\n    const activeSpan = getActiveSpan();\n    if (activeSpan) {\n      const rootSpan = getRootSpan(activeSpan);\n      const spanJSON = spanToJSON(rootSpan);\n      if (spanJSON.op === 'pageload') {\n        pageloadSpanId = rootSpan.spanContext().spanId;\n      }\n    }\n  }, 0);\n}\n\nfunction sendStandaloneClsSpan(clsValue: number, entry: LayoutShift | undefined, pageloadSpanId: string) {\n  DEBUG_BUILD && logger.log(`Sending CLS span (${clsValue})`);\n\n  const startTime = msToSec((browserPerformanceTimeOrigin() || 0) + (entry?.startTime || 0));\n  const routeName = getCurrentScope().getScopeData().transactionName;\n\n  const name = entry ? htmlTreeAsString(entry.sources[0]?.node) : 'Layout shift';\n\n  const attributes: SpanAttributes = {\n    [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.http.browser.cls',\n    [SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'ui.webvital.cls',\n    [SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME]: entry?.duration || 0,\n    // attach the pageload span id to the CLS span so that we can link them in the UI\n    'sentry.pageload.span_id': pageloadSpanId,\n  };\n\n  const span = startStandaloneWebVitalSpan({\n    name,\n    transaction: routeName,\n    attributes,\n    startTime,\n  });\n\n  if (span) {\n    span.addEvent('cls', {\n      [SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT]: '',\n      [SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE]: clsValue,\n    });\n\n    // LayoutShift performance entries always have a duration of 0, so we don't need to add `entry.duration` here\n    // see: https://developer.mozilla.org/en-US/docs/Web/API/PerformanceEntry/duration\n    span.end(startTime);\n  }\n}\n\nfunction supportsLayoutShift(): boolean {\n  try {\n    return PerformanceObserver.supportedEntryTypes.includes('layout-shift');\n  } catch {\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAqBA;;;;;;;;CAQA,GACO,SAAS,wBAAwB,GAAS;IAC/C,IAAI,kBAAmB,GAAE,CAAC;IAC1B,IAAI,kBAAkB;IACtB,IAAI,cAAc;IAElB,IAAI,CAAC,mBAAmB,EAAE,EAAE;QAC1B;IACJ;IAEE,IAAI,QAAS,GAAE,KAAK;IACpB,SAAS,eAAe,GAAG;QACzB,IAAI,QAAQ,EAAE;YACZ;QACN;QACI,QAAA,GAAW,IAAI;QACf,IAAI,cAAc,EAAE;YAClB,qBAAqB,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,cAAc,CAAC;QACnF;QACI,iBAAiB,EAAE;IACvB;IAEE,MAAM,iBAAkB,4MAAE,+BAAA,AAA4B,EAAC,CAAC,EAAE,MAAA,EAAQ,KAAK;QACrE,MAAM,KAAA,GAAQ,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,MAAO,GAAE,CAAC,CAAE;QACxD,IAAI,CAAC,KAAK,EAAE;YACV;QACN;QACI,kBAAmB,GAAE,MAAM,CAAC,KAAK;QACjC,kBAAA,GAAqB,KAAK;IAC9B,CAAG,EAAE,IAAI,CAAC;IAEV,kFAAA;IACA,qCAAA;mOACE,WAAA,AAAQ,EAAC,MAAM;QACb,eAAe,EAAE;IACrB,CAAG,CAAC;IAEJ,uGAAA;IACA,qGAAA;IACA,wCAAA;IACE,UAAU,CAAC,MAAM;QACf,MAAM,MAAA,IAAS,oLAAA,AAAS,EAAE;QAE1B,IAAI,CAAC,MAAM,EAAE;YACX;QACN;QAEI,MAAM,0BAA2B,GAAE,MAAM,CAAC,EAAE,CAAC,qBAAqB,EAAE,MAAM;YACxE,eAAe,EAAE;YACjB,0BAA0B,IAAI;QACpC,CAAK,CAAC;QAEF,MAAM,UAAA,GAAa,8LAAA,AAAa,EAAE;QAClC,IAAI,UAAU,EAAE;YACd,MAAM,QAAS,iLAAE,cAAA,AAAW,EAAC,UAAU,CAAC;YACxC,MAAM,QAAS,GAAE,2LAAA,AAAU,EAAC,QAAQ,CAAC;YACrC,IAAI,QAAQ,CAAC,EAAG,KAAI,UAAU,EAAE;gBAC9B,cAAA,GAAiB,QAAQ,CAAC,WAAW,EAAE,CAAC,MAAM;YACtD;QACA;IACA,CAAG,EAAE,CAAC,CAAC;AACP;AAEA,SAAS,qBAAqB,CAAC,QAAQ,EAAU,KAAK,EAA2B,cAAc,EAAU;kMACvG,cAAY,oLAAG,SAAM,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;IAE3D,MAAM,gNAAY,UAAA,AAAO,EAAC,mLAAC,+BAAA,AAA4B,EAAG,KAAG,CAAC,IAAA,CAAK,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC;IAC1F,MAAM,SAAU,4KAAE,kBAAA,AAAe,EAAE,EAAC,YAAY,EAAE,CAAC,eAAe;IAElE,MAAM,IAAK,GAAE,KAAM,IAAE,uMAAA,AAAgB,EAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAA,GAAI,cAAc;IAE9E,MAAM,UAAU,GAAmB;QACjC,0KAAC,oCAAgC,CAAA,EAAG,uBAAuB;QAC3D,2KAAC,+BAA4B,CAAA,EAAG,iBAAiB;QACjD,2KAAC,oCAAiC,CAAA,EAAG,KAAK,EAAE,QAAA,IAAY,CAAC;QAC7D,iFAAA;QACI,yBAAyB,EAAE,cAAc;IAC7C,CAAG;IAED,MAAM,IAAA,uMAAO,8BAAA,AAA2B,EAAC;QACvC,IAAI;QACJ,WAAW,EAAE,SAAS;QACtB,UAAU;QACV,SAAS;IACb,CAAG,CAAC;IAEF,IAAI,IAAI,EAAE;QACR,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;YACnB,2KAAC,6CAA0C,CAAA,EAAG,EAAE;YAChD,2KAAC,8CAA2C,CAAA,EAAG,QAAQ;QAC7D,CAAK,CAAC;QAEN,6GAAA;QACA,kFAAA;QACI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;IACvB;AACA;AAEA,SAAS,mBAAmB,GAAY;IACtC,IAAI;QACF,OAAO,mBAAmB,CAAC,mBAAmB,CAAC,QAAQ,CAAC,cAAc,CAAC;IAC3E,EAAI,OAAM;QACN,OAAO,KAAK;IAChB;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2669, "column": 0}, "map": {"version": 3, "file": "browserMetrics.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/metrics/browserMetrics.ts"], "sourcesContent": ["/* eslint-disable max-lines */\nimport type { Measurements, Span, SpanAttributes, SpanAttributeValue, StartSpanOptions } from '@sentry/core';\nimport {\n  browserPerformanceTimeOrigin,\n  getActiveSpan,\n  getComponentName,\n  htmlTreeAsString,\n  isPrimitive,\n  parseUrl,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  setMeasurement,\n  spanToJSON,\n  stringMatchesSomePattern,\n} from '@sentry/core';\nimport { WINDOW } from '../types';\nimport { trackClsAsStandaloneSpan } from './cls';\nimport {\n  type PerformanceLongAnimationFrameTiming,\n  addClsInstrumentationHandler,\n  addFidInstrumentationHandler,\n  addLcpInstrumentationHandler,\n  addPerformanceInstrumentationHandler,\n  addTtfbInstrumentationHandler,\n} from './instrument';\nimport {\n  extractNetworkProtocol,\n  getBrowserPerformanceAPI,\n  isMeasurementValue,\n  msToSec,\n  startAndEndSpan,\n} from './utils';\nimport { getActivationStart } from './web-vitals/lib/getActivationStart';\nimport { getNavigationEntry } from './web-vitals/lib/getNavigationEntry';\nimport { getVisibilityWatcher } from './web-vitals/lib/getVisibilityWatcher';\n\ninterface NavigatorNetworkInformation {\n  readonly connection?: NetworkInformation;\n}\n\n// http://wicg.github.io/netinfo/#connection-types\ntype ConnectionType = 'bluetooth' | 'cellular' | 'ethernet' | 'mixed' | 'none' | 'other' | 'unknown' | 'wifi' | 'wimax';\n\n// http://wicg.github.io/netinfo/#effectiveconnectiontype-enum\ntype EffectiveConnectionType = '2g' | '3g' | '4g' | 'slow-2g';\n\n// http://wicg.github.io/netinfo/#dom-megabit\ntype Megabit = number;\n// http://wicg.github.io/netinfo/#dom-millisecond\ntype Millisecond = number;\n\n// http://wicg.github.io/netinfo/#networkinformation-interface\ninterface NetworkInformation extends EventTarget {\n  // http://wicg.github.io/netinfo/#type-attribute\n  readonly type?: ConnectionType;\n  // http://wicg.github.io/netinfo/#effectivetype-attribute\n  readonly effectiveType?: EffectiveConnectionType;\n  // http://wicg.github.io/netinfo/#downlinkmax-attribute\n  readonly downlinkMax?: Megabit;\n  // http://wicg.github.io/netinfo/#downlink-attribute\n  readonly downlink?: Megabit;\n  // http://wicg.github.io/netinfo/#rtt-attribute\n  readonly rtt?: Millisecond;\n  // http://wicg.github.io/netinfo/#savedata-attribute\n  readonly saveData?: boolean;\n  // http://wicg.github.io/netinfo/#handling-changes-to-the-underlying-connection\n  onchange?: EventListener;\n}\n\n// https://w3c.github.io/device-memory/#sec-device-memory-js-api\ninterface NavigatorDeviceMemory {\n  readonly deviceMemory?: number;\n}\n\nconst MAX_INT_AS_BYTES = 2147483647;\n\nlet _performanceCursor: number = 0;\n\nlet _measurements: Measurements = {};\nlet _lcpEntry: LargestContentfulPaint | undefined;\nlet _clsEntry: LayoutShift | undefined;\n\ninterface StartTrackingWebVitalsOptions {\n  recordClsStandaloneSpans: boolean;\n}\n\n/**\n * Start tracking web vitals.\n * The callback returned by this function can be used to stop tracking & ensure all measurements are final & captured.\n *\n * @returns A function that forces web vitals collection\n */\nexport function startTrackingWebVitals({ recordClsStandaloneSpans }: StartTrackingWebVitalsOptions): () => void {\n  const performance = getBrowserPerformanceAPI();\n  if (performance && browserPerformanceTimeOrigin()) {\n    // @ts-expect-error we want to make sure all of these are available, even if TS is sure they are\n    if (performance.mark) {\n      WINDOW.performance.mark('sentry-tracing-init');\n    }\n    const fidCleanupCallback = _trackFID();\n    const lcpCleanupCallback = _trackLCP();\n    const ttfbCleanupCallback = _trackTtfb();\n    const clsCleanupCallback = recordClsStandaloneSpans ? trackClsAsStandaloneSpan() : _trackCLS();\n\n    return (): void => {\n      fidCleanupCallback();\n      lcpCleanupCallback();\n      ttfbCleanupCallback();\n      clsCleanupCallback?.();\n    };\n  }\n\n  return () => undefined;\n}\n\n/**\n * Start tracking long tasks.\n */\nexport function startTrackingLongTasks(): void {\n  addPerformanceInstrumentationHandler('longtask', ({ entries }) => {\n    const parent = getActiveSpan();\n    if (!parent) {\n      return;\n    }\n\n    const { op: parentOp, start_timestamp: parentStartTimestamp } = spanToJSON(parent);\n\n    for (const entry of entries) {\n      const startTime = msToSec((browserPerformanceTimeOrigin() as number) + entry.startTime);\n      const duration = msToSec(entry.duration);\n\n      if (parentOp === 'navigation' && parentStartTimestamp && startTime < parentStartTimestamp) {\n        // Skip adding a span if the long task started before the navigation started.\n        // `startAndEndSpan` will otherwise adjust the parent's start time to the span's start\n        // time, potentially skewing the duration of the actual navigation as reported via our\n        // routing instrumentations\n        continue;\n      }\n\n      startAndEndSpan(parent, startTime, startTime + duration, {\n        name: 'Main UI thread blocked',\n        op: 'ui.long-task',\n        attributes: {\n          [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.ui.browser.metrics',\n        },\n      });\n    }\n  });\n}\n\n/**\n * Start tracking long animation frames.\n */\nexport function startTrackingLongAnimationFrames(): void {\n  // NOTE: the current web-vitals version (3.5.2) does not support long-animation-frame, so\n  // we directly observe `long-animation-frame` events instead of through the web-vitals\n  // `observe` helper function.\n  const observer = new PerformanceObserver(list => {\n    const parent = getActiveSpan();\n    if (!parent) {\n      return;\n    }\n    for (const entry of list.getEntries() as PerformanceLongAnimationFrameTiming[]) {\n      if (!entry.scripts[0]) {\n        continue;\n      }\n\n      const startTime = msToSec((browserPerformanceTimeOrigin() as number) + entry.startTime);\n\n      const { start_timestamp: parentStartTimestamp, op: parentOp } = spanToJSON(parent);\n\n      if (parentOp === 'navigation' && parentStartTimestamp && startTime < parentStartTimestamp) {\n        // Skip adding the span if the long animation frame started before the navigation started.\n        // `startAndEndSpan` will otherwise adjust the parent's start time to the span's start\n        // time, potentially skewing the duration of the actual navigation as reported via our\n        // routing instrumentations\n        continue;\n      }\n      const duration = msToSec(entry.duration);\n\n      const attributes: SpanAttributes = {\n        [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.ui.browser.metrics',\n      };\n\n      const initialScript = entry.scripts[0];\n      const { invoker, invokerType, sourceURL, sourceFunctionName, sourceCharPosition } = initialScript;\n      attributes['browser.script.invoker'] = invoker;\n      attributes['browser.script.invoker_type'] = invokerType;\n      if (sourceURL) {\n        attributes['code.filepath'] = sourceURL;\n      }\n      if (sourceFunctionName) {\n        attributes['code.function'] = sourceFunctionName;\n      }\n      if (sourceCharPosition !== -1) {\n        attributes['browser.script.source_char_position'] = sourceCharPosition;\n      }\n\n      startAndEndSpan(parent, startTime, startTime + duration, {\n        name: 'Main UI thread blocked',\n        op: 'ui.long-animation-frame',\n        attributes,\n      });\n    }\n  });\n\n  observer.observe({ type: 'long-animation-frame', buffered: true });\n}\n\n/**\n * Start tracking interaction events.\n */\nexport function startTrackingInteractions(): void {\n  addPerformanceInstrumentationHandler('event', ({ entries }) => {\n    const parent = getActiveSpan();\n    if (!parent) {\n      return;\n    }\n    for (const entry of entries) {\n      if (entry.name === 'click') {\n        const startTime = msToSec((browserPerformanceTimeOrigin() as number) + entry.startTime);\n        const duration = msToSec(entry.duration);\n\n        const spanOptions: StartSpanOptions & Required<Pick<StartSpanOptions, 'attributes'>> = {\n          name: htmlTreeAsString(entry.target),\n          op: `ui.interaction.${entry.name}`,\n          startTime: startTime,\n          attributes: {\n            [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.ui.browser.metrics',\n          },\n        };\n\n        const componentName = getComponentName(entry.target);\n        if (componentName) {\n          spanOptions.attributes['ui.component_name'] = componentName;\n        }\n\n        startAndEndSpan(parent, startTime, startTime + duration, spanOptions);\n      }\n    }\n  });\n}\n\nexport { registerInpInteractionListener, startTrackingINP } from './inp';\n\n/**\n * Starts tracking the Cumulative Layout Shift on the current page and collects the value and last entry\n * to the `_measurements` object which ultimately is applied to the pageload span's measurements.\n */\nfunction _trackCLS(): () => void {\n  return addClsInstrumentationHandler(({ metric }) => {\n    const entry = metric.entries[metric.entries.length - 1] as LayoutShift | undefined;\n    if (!entry) {\n      return;\n    }\n    _measurements['cls'] = { value: metric.value, unit: '' };\n    _clsEntry = entry;\n  }, true);\n}\n\n/** Starts tracking the Largest Contentful Paint on the current page. */\nfunction _trackLCP(): () => void {\n  return addLcpInstrumentationHandler(({ metric }) => {\n    const entry = metric.entries[metric.entries.length - 1];\n    if (!entry) {\n      return;\n    }\n\n    _measurements['lcp'] = { value: metric.value, unit: 'millisecond' };\n    _lcpEntry = entry as LargestContentfulPaint;\n  }, true);\n}\n\n/** Starts tracking the First Input Delay on the current page. */\nfunction _trackFID(): () => void {\n  return addFidInstrumentationHandler(({ metric }) => {\n    const entry = metric.entries[metric.entries.length - 1];\n    if (!entry) {\n      return;\n    }\n\n    const timeOrigin = msToSec(browserPerformanceTimeOrigin() as number);\n    const startTime = msToSec(entry.startTime);\n    _measurements['fid'] = { value: metric.value, unit: 'millisecond' };\n    _measurements['mark.fid'] = { value: timeOrigin + startTime, unit: 'second' };\n  });\n}\n\nfunction _trackTtfb(): () => void {\n  return addTtfbInstrumentationHandler(({ metric }) => {\n    const entry = metric.entries[metric.entries.length - 1];\n    if (!entry) {\n      return;\n    }\n\n    _measurements['ttfb'] = { value: metric.value, unit: 'millisecond' };\n  });\n}\n\ninterface AddPerformanceEntriesOptions {\n  /**\n   * Flag to determine if CLS should be recorded as a measurement on the span or\n   * sent as a standalone span instead.\n   */\n  recordClsOnPageloadSpan: boolean;\n\n  /**\n   * Resource spans with `op`s matching strings in the array will not be emitted.\n   *\n   * Default: []\n   */\n  ignoreResourceSpans: Array<'resouce.script' | 'resource.css' | 'resource.img' | 'resource.other' | string>;\n\n  /**\n   * Performance spans created from browser Performance APIs,\n   * `performance.mark(...)` nand `performance.measure(...)`\n   * with `name`s matching strings in the array will not be emitted.\n   *\n   * Default: []\n   */\n  ignorePerformanceApiSpans: Array<string | RegExp>;\n}\n\n/** Add performance related spans to a transaction */\nexport function addPerformanceEntries(span: Span, options: AddPerformanceEntriesOptions): void {\n  const performance = getBrowserPerformanceAPI();\n  const origin = browserPerformanceTimeOrigin();\n  if (!performance?.getEntries || !origin) {\n    // Gatekeeper if performance API not available\n    return;\n  }\n\n  const timeOrigin = msToSec(origin);\n\n  const performanceEntries = performance.getEntries();\n\n  const { op, start_timestamp: transactionStartTime } = spanToJSON(span);\n\n  performanceEntries.slice(_performanceCursor).forEach(entry => {\n    const startTime = msToSec(entry.startTime);\n    const duration = msToSec(\n      // Inexplicably, Chrome sometimes emits a negative duration. We need to work around this.\n      // There is a SO post attempting to explain this, but it leaves one with open questions: https://stackoverflow.com/questions/23191918/peformance-getentries-and-negative-duration-display\n      // The way we clamp the value is probably not accurate, since we have observed this happen for things that may take a while to load, like for example the replay worker.\n      // TODO: Investigate why this happens and how to properly mitigate. For now, this is a workaround to prevent transactions being dropped due to negative duration spans.\n      Math.max(0, entry.duration),\n    );\n\n    if (op === 'navigation' && transactionStartTime && timeOrigin + startTime < transactionStartTime) {\n      return;\n    }\n\n    switch (entry.entryType) {\n      case 'navigation': {\n        _addNavigationSpans(span, entry as PerformanceNavigationTiming, timeOrigin);\n        break;\n      }\n      case 'mark':\n      case 'paint':\n      case 'measure': {\n        _addMeasureSpans(span, entry, startTime, duration, timeOrigin, options.ignorePerformanceApiSpans);\n\n        // capture web vitals\n        const firstHidden = getVisibilityWatcher();\n        // Only report if the page wasn't hidden prior to the web vital.\n        const shouldRecord = entry.startTime < firstHidden.firstHiddenTime;\n\n        if (entry.name === 'first-paint' && shouldRecord) {\n          _measurements['fp'] = { value: entry.startTime, unit: 'millisecond' };\n        }\n        if (entry.name === 'first-contentful-paint' && shouldRecord) {\n          _measurements['fcp'] = { value: entry.startTime, unit: 'millisecond' };\n        }\n        break;\n      }\n      case 'resource': {\n        _addResourceSpans(\n          span,\n          entry as PerformanceResourceTiming,\n          entry.name,\n          startTime,\n          duration,\n          timeOrigin,\n          options.ignoreResourceSpans,\n        );\n        break;\n      }\n      // Ignore other entry types.\n    }\n  });\n\n  _performanceCursor = Math.max(performanceEntries.length - 1, 0);\n\n  _trackNavigator(span);\n\n  // Measurements are only available for pageload transactions\n  if (op === 'pageload') {\n    _addTtfbRequestTimeToMeasurements(_measurements);\n\n    const fidMark = _measurements['mark.fid'];\n    if (fidMark && _measurements['fid']) {\n      // create span for FID\n      startAndEndSpan(span, fidMark.value, fidMark.value + msToSec(_measurements['fid'].value), {\n        name: 'first input delay',\n        op: 'ui.action',\n        attributes: {\n          [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.ui.browser.metrics',\n        },\n      });\n\n      // Delete mark.fid as we don't want it to be part of final payload\n      delete _measurements['mark.fid'];\n    }\n\n    // If FCP is not recorded we should not record the cls value\n    // according to the new definition of CLS.\n    // TODO: Check if the first condition is still necessary: `onCLS` already only fires once `onFCP` was called.\n    if (!('fcp' in _measurements) || !options.recordClsOnPageloadSpan) {\n      delete _measurements.cls;\n    }\n\n    Object.entries(_measurements).forEach(([measurementName, measurement]) => {\n      setMeasurement(measurementName, measurement.value, measurement.unit);\n    });\n\n    // Set timeOrigin which denotes the timestamp which to base the LCP/FCP/FP/TTFB measurements on\n    span.setAttribute('performance.timeOrigin', timeOrigin);\n\n    // In prerendering scenarios, where a page might be prefetched and pre-rendered before the user clicks the link,\n    // the navigation starts earlier than when the user clicks it. Web Vitals should always be based on the\n    // user-perceived time, so they are not reported from the actual start of the navigation, but rather from the\n    // time where the user actively started the navigation, for example by clicking a link.\n    // This is user action is called \"activation\" and the time between navigation and activation is stored in\n    // the `activationStart` attribute of the \"navigation\" PerformanceEntry.\n    span.setAttribute('performance.activationStart', getActivationStart());\n\n    _setWebVitalAttributes(span);\n  }\n\n  _lcpEntry = undefined;\n  _clsEntry = undefined;\n  _measurements = {};\n}\n\n/**\n * Create measure related spans.\n * Exported only for tests.\n */\nexport function _addMeasureSpans(\n  span: Span,\n  entry: PerformanceEntry,\n  startTime: number,\n  duration: number,\n  timeOrigin: number,\n  ignorePerformanceApiSpans: AddPerformanceEntriesOptions['ignorePerformanceApiSpans'],\n): void {\n  if (\n    ['mark', 'measure'].includes(entry.entryType) &&\n    stringMatchesSomePattern(entry.name, ignorePerformanceApiSpans)\n  ) {\n    return;\n  }\n\n  const navEntry = getNavigationEntry(false);\n  const requestTime = msToSec(navEntry ? navEntry.requestStart : 0);\n  // Because performance.measure accepts arbitrary timestamps it can produce\n  // spans that happen before the browser even makes a request for the page.\n  //\n  // An example of this is the automatically generated Next.js-before-hydration\n  // spans created by the Next.js framework.\n  //\n  // To prevent this we will pin the start timestamp to the request start time\n  // This does make duration inaccurate, so if this does happen, we will add\n  // an attribute to the span\n  const measureStartTimestamp = timeOrigin + Math.max(startTime, requestTime);\n  const startTimeStamp = timeOrigin + startTime;\n  const measureEndTimestamp = startTimeStamp + duration;\n\n  const attributes: SpanAttributes = {\n    [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.resource.browser.metrics',\n  };\n\n  if (measureStartTimestamp !== startTimeStamp) {\n    attributes['sentry.browser.measure_happened_before_request'] = true;\n    attributes['sentry.browser.measure_start_time'] = measureStartTimestamp;\n  }\n\n  _addDetailToSpanAttributes(attributes, entry as PerformanceMeasure);\n\n  // Measurements from third parties can be off, which would create invalid spans, dropping transactions in the process.\n  if (measureStartTimestamp <= measureEndTimestamp) {\n    startAndEndSpan(span, measureStartTimestamp, measureEndTimestamp, {\n      name: entry.name as string,\n      op: entry.entryType as string,\n      attributes,\n    });\n  }\n}\n\nfunction _addDetailToSpanAttributes(attributes: SpanAttributes, performanceMeasure: PerformanceMeasure): void {\n  try {\n    // Accessing detail might throw in some browsers (e.g., Firefox) due to security restrictions\n    const detail = performanceMeasure.detail;\n\n    if (!detail) {\n      return;\n    }\n\n    // Process detail based on its type\n    if (typeof detail === 'object') {\n      // Handle object details\n      for (const [key, value] of Object.entries(detail)) {\n        if (value && isPrimitive(value)) {\n          attributes[`sentry.browser.measure.detail.${key}`] = value as SpanAttributeValue;\n        } else if (value !== undefined) {\n          try {\n            // This is user defined so we can't guarantee it's serializable\n            attributes[`sentry.browser.measure.detail.${key}`] = JSON.stringify(value);\n          } catch {\n            // Skip values that can't be stringified\n          }\n        }\n      }\n      return;\n    }\n\n    if (isPrimitive(detail)) {\n      // Handle primitive details\n      attributes['sentry.browser.measure.detail'] = detail as SpanAttributeValue;\n      return;\n    }\n\n    try {\n      attributes['sentry.browser.measure.detail'] = JSON.stringify(detail);\n    } catch {\n      // Skip if stringification fails\n    }\n  } catch {\n    // Silently ignore any errors when accessing detail\n    // This handles the Firefox \"Permission denied to access object\" error\n  }\n}\n\n/**\n * Instrument navigation entries\n * exported only for tests\n */\nexport function _addNavigationSpans(span: Span, entry: PerformanceNavigationTiming, timeOrigin: number): void {\n  (['unloadEvent', 'redirect', 'domContentLoadedEvent', 'loadEvent', 'connect'] as const).forEach(event => {\n    _addPerformanceNavigationTiming(span, entry, event, timeOrigin);\n  });\n  _addPerformanceNavigationTiming(span, entry, 'secureConnection', timeOrigin, 'TLS/SSL');\n  _addPerformanceNavigationTiming(span, entry, 'fetch', timeOrigin, 'cache');\n  _addPerformanceNavigationTiming(span, entry, 'domainLookup', timeOrigin, 'DNS');\n\n  _addRequest(span, entry, timeOrigin);\n}\n\ntype StartEventName =\n  | 'secureConnection'\n  | 'fetch'\n  | 'domainLookup'\n  | 'unloadEvent'\n  | 'redirect'\n  | 'connect'\n  | 'domContentLoadedEvent'\n  | 'loadEvent';\n\ntype EndEventName =\n  | 'connectEnd'\n  | 'domainLookupStart'\n  | 'domainLookupEnd'\n  | 'unloadEventEnd'\n  | 'redirectEnd'\n  | 'connectEnd'\n  | 'domContentLoadedEventEnd'\n  | 'loadEventEnd';\n\n/** Create performance navigation related spans */\nfunction _addPerformanceNavigationTiming(\n  span: Span,\n  entry: PerformanceNavigationTiming,\n  event: StartEventName,\n  timeOrigin: number,\n  name: string = event,\n): void {\n  const eventEnd = _getEndPropertyNameForNavigationTiming(event) satisfies keyof PerformanceNavigationTiming;\n  const end = entry[eventEnd];\n  const start = entry[`${event}Start`];\n  if (!start || !end) {\n    return;\n  }\n  startAndEndSpan(span, timeOrigin + msToSec(start), timeOrigin + msToSec(end), {\n    op: `browser.${name}`,\n    name: entry.name,\n    attributes: {\n      [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.ui.browser.metrics',\n      ...(event === 'redirect' && entry.redirectCount != null ? { 'http.redirect_count': entry.redirectCount } : {}),\n    },\n  });\n}\n\nfunction _getEndPropertyNameForNavigationTiming(event: StartEventName): EndEventName {\n  if (event === 'secureConnection') {\n    return 'connectEnd';\n  }\n  if (event === 'fetch') {\n    return 'domainLookupStart';\n  }\n  return `${event}End`;\n}\n\n/** Create request and response related spans */\nfunction _addRequest(span: Span, entry: PerformanceNavigationTiming, timeOrigin: number): void {\n  const requestStartTimestamp = timeOrigin + msToSec(entry.requestStart as number);\n  const responseEndTimestamp = timeOrigin + msToSec(entry.responseEnd as number);\n  const responseStartTimestamp = timeOrigin + msToSec(entry.responseStart as number);\n  if (entry.responseEnd) {\n    // It is possible that we are collecting these metrics when the page hasn't finished loading yet, for example when the HTML slowly streams in.\n    // In this case, ie. when the document request hasn't finished yet, `entry.responseEnd` will be 0.\n    // In order not to produce faulty spans, where the end timestamp is before the start timestamp, we will only collect\n    // these spans when the responseEnd value is available. The backend (Relay) would drop the entire span if it contained faulty spans.\n    startAndEndSpan(span, requestStartTimestamp, responseEndTimestamp, {\n      op: 'browser.request',\n      name: entry.name,\n      attributes: {\n        [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.ui.browser.metrics',\n      },\n    });\n\n    startAndEndSpan(span, responseStartTimestamp, responseEndTimestamp, {\n      op: 'browser.response',\n      name: entry.name,\n      attributes: {\n        [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.ui.browser.metrics',\n      },\n    });\n  }\n}\n\n/**\n * Create resource-related spans.\n * Exported only for tests.\n */\nexport function _addResourceSpans(\n  span: Span,\n  entry: PerformanceResourceTiming,\n  resourceUrl: string,\n  startTime: number,\n  duration: number,\n  timeOrigin: number,\n  ignoreResourceSpans?: Array<string>,\n): void {\n  // we already instrument based on fetch and xhr, so we don't need to\n  // duplicate spans here.\n  if (entry.initiatorType === 'xmlhttprequest' || entry.initiatorType === 'fetch') {\n    return;\n  }\n\n  const op = entry.initiatorType ? `resource.${entry.initiatorType}` : 'resource.other';\n  if (ignoreResourceSpans?.includes(op)) {\n    return;\n  }\n\n  const parsedUrl = parseUrl(resourceUrl);\n\n  const attributes: SpanAttributes = {\n    [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.resource.browser.metrics',\n  };\n  setResourceEntrySizeData(attributes, entry, 'transferSize', 'http.response_transfer_size');\n  setResourceEntrySizeData(attributes, entry, 'encodedBodySize', 'http.response_content_length');\n  setResourceEntrySizeData(attributes, entry, 'decodedBodySize', 'http.decoded_response_content_length');\n\n  // `deliveryType` is experimental and does not exist everywhere\n  const deliveryType = (entry as { deliveryType?: 'cache' | 'navigational-prefetch' | '' }).deliveryType;\n  if (deliveryType != null) {\n    attributes['http.response_delivery_type'] = deliveryType;\n  }\n\n  // Types do not reflect this property yet\n  const renderBlockingStatus = (entry as { renderBlockingStatus?: 'render-blocking' | 'non-render-blocking' })\n    .renderBlockingStatus;\n  if (renderBlockingStatus) {\n    attributes['resource.render_blocking_status'] = renderBlockingStatus;\n  }\n\n  if (parsedUrl.protocol) {\n    attributes['url.scheme'] = parsedUrl.protocol.split(':').pop(); // the protocol returned by parseUrl includes a :, but OTEL spec does not, so we remove it.\n  }\n\n  if (parsedUrl.host) {\n    attributes['server.address'] = parsedUrl.host;\n  }\n\n  attributes['url.same_origin'] = resourceUrl.includes(WINDOW.location.origin);\n\n  const { name, version } = extractNetworkProtocol(entry.nextHopProtocol);\n  attributes['network.protocol.name'] = name;\n  attributes['network.protocol.version'] = version;\n\n  const startTimestamp = timeOrigin + startTime;\n  const endTimestamp = startTimestamp + duration;\n\n  startAndEndSpan(span, startTimestamp, endTimestamp, {\n    name: resourceUrl.replace(WINDOW.location.origin, ''),\n    op,\n    attributes,\n  });\n}\n\n/**\n * Capture the information of the user agent.\n */\nfunction _trackNavigator(span: Span): void {\n  const navigator = WINDOW.navigator as null | (Navigator & NavigatorNetworkInformation & NavigatorDeviceMemory);\n  if (!navigator) {\n    return;\n  }\n\n  // track network connectivity\n  const connection = navigator.connection;\n  if (connection) {\n    if (connection.effectiveType) {\n      span.setAttribute('effectiveConnectionType', connection.effectiveType);\n    }\n\n    if (connection.type) {\n      span.setAttribute('connectionType', connection.type);\n    }\n\n    if (isMeasurementValue(connection.rtt)) {\n      _measurements['connection.rtt'] = { value: connection.rtt, unit: 'millisecond' };\n    }\n  }\n\n  if (isMeasurementValue(navigator.deviceMemory)) {\n    span.setAttribute('deviceMemory', `${navigator.deviceMemory} GB`);\n  }\n\n  if (isMeasurementValue(navigator.hardwareConcurrency)) {\n    span.setAttribute('hardwareConcurrency', String(navigator.hardwareConcurrency));\n  }\n}\n\n/** Add LCP / CLS data to span to allow debugging */\nfunction _setWebVitalAttributes(span: Span): void {\n  if (_lcpEntry) {\n    // Capture Properties of the LCP element that contributes to the LCP.\n\n    if (_lcpEntry.element) {\n      span.setAttribute('lcp.element', htmlTreeAsString(_lcpEntry.element));\n    }\n\n    if (_lcpEntry.id) {\n      span.setAttribute('lcp.id', _lcpEntry.id);\n    }\n\n    if (_lcpEntry.url) {\n      // Trim URL to the first 200 characters.\n      span.setAttribute('lcp.url', _lcpEntry.url.trim().slice(0, 200));\n    }\n\n    if (_lcpEntry.loadTime != null) {\n      // loadTime is the time of LCP that's related to receiving the LCP element response..\n      span.setAttribute('lcp.loadTime', _lcpEntry.loadTime);\n    }\n\n    if (_lcpEntry.renderTime != null) {\n      // renderTime is loadTime + rendering time\n      // it's 0 if the LCP element is loaded from a 3rd party origin that doesn't send the\n      // `Timing-Allow-Origin` header.\n      span.setAttribute('lcp.renderTime', _lcpEntry.renderTime);\n    }\n\n    span.setAttribute('lcp.size', _lcpEntry.size);\n  }\n\n  // See: https://developer.mozilla.org/en-US/docs/Web/API/LayoutShift\n  if (_clsEntry?.sources) {\n    _clsEntry.sources.forEach((source, index) =>\n      span.setAttribute(`cls.source.${index + 1}`, htmlTreeAsString(source.node)),\n    );\n  }\n}\n\nfunction setResourceEntrySizeData(\n  attributes: SpanAttributes,\n  entry: PerformanceResourceTiming,\n  key: keyof Pick<PerformanceResourceTiming, 'transferSize' | 'encodedBodySize' | 'decodedBodySize'>,\n  dataKey: 'http.response_transfer_size' | 'http.response_content_length' | 'http.decoded_response_content_length',\n): void {\n  const entryVal = entry[key];\n  if (entryVal != null && entryVal < MAX_INT_AS_BYTES) {\n    attributes[dataKey] = entryVal;\n  }\n}\n\n/**\n * Add ttfb request time information to measurements.\n *\n * ttfb information is added via vendored web vitals library.\n */\nfunction _addTtfbRequestTimeToMeasurements(_measurements: Measurements): void {\n  const navEntry = getNavigationEntry(false);\n  if (!navEntry) {\n    return;\n  }\n\n  const { responseStart, requestStart } = navEntry;\n\n  if (requestStart <= responseStart) {\n    _measurements['ttfb.requestTime'] = {\n      value: responseStart - requestStart,\n      unit: 'millisecond',\n    };\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEA,MAAM,gBAAA,GAAmB,UAAU;AAEnC,IAAI,kBAAkB,GAAW,CAAC;AAElC,IAAI,aAAa,GAAiB,CAAA,CAAE;AACpC,IAAI,SAAS;AACb,IAAI,SAAS;AAMb;;;;;CAKA,GACO,SAAS,sBAAsB,CAAC,EAAE,wBAAyB,EAAC,EAA6C;IAC9G,MAAM,WAAA,uMAAc,2BAAA,AAAwB,EAAE;IAC9C,IAAI,WAAA,sLAAe,+BAAA,AAA4B,EAAE,GAAE;QACrD,gGAAA;QACI,IAAI,WAAW,CAAC,IAAI,EAAE;iMACpB,SAAM,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC;QACpD;QACI,MAAM,kBAAA,GAAqB,SAAS,EAAE;QACtC,MAAM,kBAAA,GAAqB,SAAS,EAAE;QACtC,MAAM,mBAAA,GAAsB,UAAU,EAAE;QACxC,MAAM,kBAAmB,GAAE,wBAAyB,IAAE,4NAAA,AAAwB,EAAG,IAAE,SAAS,EAAE;QAE9F,OAAO,MAAY;YACjB,kBAAkB,EAAE;YACpB,kBAAkB,EAAE;YACpB,mBAAmB,EAAE;YACrB,kBAAkB,IAAI;QAC5B,CAAK;IACL;IAEE,OAAO,IAAM,SAAS;AACxB;AAEA;;CAEA,GACO,SAAS,sBAAsB,GAAS;6MAC7C,uCAAA,AAAoC,EAAC,UAAU,EAAE,CAAC,EAAE,OAAA,EAAS,KAAK;QAChE,MAAM,MAAA,iLAAS,gBAAA,AAAa,EAAE;QAC9B,IAAI,CAAC,MAAM,EAAE;YACX;QACN;QAEI,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,eAAe,EAAE,oBAAA,EAAuB,iLAAE,aAAA,AAAU,EAAC,MAAM,CAAC;QAElF,KAAK,MAAM,KAAM,IAAG,OAAO,CAAE;YAC3B,MAAM,SAAA,uMAAY,UAAA,AAAO,EAAC,kLAAC,+BAAA,AAA4B,EAAC,IAAe,KAAK,CAAC,SAAS,CAAC;YACvF,MAAM,+MAAW,UAAA,AAAO,EAAC,KAAK,CAAC,QAAQ,CAAC;YAExC,IAAI,QAAA,KAAa,YAAA,IAAgB,oBAAA,IAAwB,SAAA,GAAY,oBAAoB,EAAE;gBAKzF;YACR;aAEM,qNAAA,AAAe,EAAC,MAAM,EAAE,SAAS,EAAE,SAAA,GAAY,QAAQ,EAAE;gBACvD,IAAI,EAAE,wBAAwB;gBAC9B,EAAE,EAAE,cAAc;gBAClB,UAAU,EAAE;oBACV,2KAAC,mCAAgC,CAAA,EAAG,yBAAyB;gBACvE,CAAS;YACT,CAAO,CAAC;QACR;IACA,CAAG,CAAC;AACJ;AAEA;;CAEA,GACO,SAAS,gCAAgC,GAAS;IACzD,yFAAA;IACA,sFAAA;IACA,6BAAA;IACE,MAAM,QAAS,GAAE,IAAI,mBAAmB,EAAC,QAAQ;QAC/C,MAAM,MAAA,iLAAS,gBAAA,AAAa,EAAE;QAC9B,IAAI,CAAC,MAAM,EAAE;YACX;QACN;QACI,KAAK,MAAM,KAAM,IAAG,IAAI,CAAC,UAAU,EAAC,CAA4C;YAC9E,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;gBACrB;YACR;YAEM,MAAM,SAAA,OAAY,0MAAA,AAAO,EAAC,kLAAC,+BAAA,AAA4B,EAAC,IAAe,KAAK,CAAC,SAAS,CAAC;YAEvF,MAAM,EAAE,eAAe,EAAE,oBAAoB,EAAE,EAAE,EAAE,QAAA,EAAW,IAAE,0LAAA,AAAU,EAAC,MAAM,CAAC;YAElF,IAAI,QAAA,KAAa,YAAA,IAAgB,oBAAA,IAAwB,SAAA,GAAY,oBAAoB,EAAE;gBAKzF;YACR;YACM,MAAM,+MAAW,UAAA,AAAO,EAAC,KAAK,CAAC,QAAQ,CAAC;YAExC,MAAM,UAAU,GAAmB;gBACjC,2KAAC,mCAAgC,CAAA,EAAG,yBAAyB;YACrE,CAAO;YAED,MAAM,gBAAgB,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YACtC,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,kBAAkB,EAAE,kBAAmB,EAAA,GAAI,aAAa;YACjG,UAAU,CAAC,wBAAwB,CAAA,GAAI,OAAO;YAC9C,UAAU,CAAC,6BAA6B,CAAA,GAAI,WAAW;YACvD,IAAI,SAAS,EAAE;gBACb,UAAU,CAAC,eAAe,CAAA,GAAI,SAAS;YAC/C;YACM,IAAI,kBAAkB,EAAE;gBACtB,UAAU,CAAC,eAAe,CAAA,GAAI,kBAAkB;YACxD;YACM,IAAI,kBAAA,KAAuB,CAAA,CAAE,EAAE;gBAC7B,UAAU,CAAC,qCAAqC,CAAA,GAAI,kBAAkB;YAC9E;YAEM,sNAAA,AAAe,EAAC,MAAM,EAAE,SAAS,EAAE,SAAA,GAAY,QAAQ,EAAE;gBACvD,IAAI,EAAE,wBAAwB;gBAC9B,EAAE,EAAE,yBAAyB;gBAC7B,UAAU;YAClB,CAAO,CAAC;QACR;IACA,CAAG,CAAC;IAEF,QAAQ,CAAC,OAAO,CAAC;QAAE,IAAI,EAAE,sBAAsB;QAAE,QAAQ,EAAE,IAAA;IAAA,CAAM,CAAC;AACpE;AAEA;;CAEA,GACO,SAAS,yBAAyB,GAAS;IAChD,gPAAA,AAAoC,EAAC,OAAO,EAAE,CAAC,EAAE,OAAA,EAAS,KAAK;QAC7D,MAAM,MAAA,iLAAS,gBAAA,AAAa,EAAE;QAC9B,IAAI,CAAC,MAAM,EAAE;YACX;QACN;QACI,KAAK,MAAM,KAAM,IAAG,OAAO,CAAE;YAC3B,IAAI,KAAK,CAAC,IAAK,KAAI,OAAO,EAAE;gBAC1B,MAAM,SAAA,uMAAY,UAAA,AAAO,EAAC,kLAAC,+BAAA,AAA4B,EAAC,IAAe,KAAK,CAAC,SAAS,CAAC;gBACvF,MAAM,+MAAW,UAAA,AAAO,EAAC,KAAK,CAAC,QAAQ,CAAC;gBAExC,MAAM,WAAW,GAAsE;oBACrF,IAAI,uLAAE,mBAAA,AAAgB,EAAC,KAAK,CAAC,MAAM,CAAC;oBACpC,EAAE,EAAE,CAAC,eAAe,EAAE,KAAK,CAAC,IAAI,CAAC,CAAA;oBACA,SAAA,EAAA,SAAA;oBACA,UAAA,EAAA;wBACA,2KAAA,mCAAA,CAAA,EAAA,yBAAA;oBACA,CAAA;gBACA,CAAA;gBAEA,MAAA,aAAA,wLAAA,mBAAA,EAAA,KAAA,CAAA,MAAA,CAAA;gBACA,IAAA,aAAA,EAAA;oBACA,WAAA,CAAA,UAAA,CAAA,mBAAA,CAAA,GAAA,aAAA;gBACA;oBAEA,kNAAA,EAAA,MAAA,EAAA,SAAA,EAAA,SAAA,GAAA,QAAA,EAAA,WAAA,CAAA;YACA;QACA;IACA,CAAA,CAAA;AACA;AAIA;;;CAGA,GACA,SAAA,SAAA,GAAA;IACA,OAAA,wOAAA,EAAA,CAAA,EAAA,MAAA,EAAA,KAAA;QACA,MAAA,KAAA,GAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,OAAA,CAAA,MAAA,GAAA,CAAA,CAAA;QACA,IAAA,CAAA,KAAA,EAAA;YACA;QACA;QACA,aAAA,CAAA,KAAA,CAAA,GAAA;YAAA,KAAA,EAAA,MAAA,CAAA,KAAA;YAAA,IAAA,EAAA,EAAA;QAAA,CAAA;QACA,SAAA,GAAA,KAAA;IACA,CAAA,EAAA,IAAA,CAAA;AACA;AAEA,sEAAA,GACA,SAAA,SAAA,GAAA;IACA,gNAAA,+BAAA,EAAA,CAAA,EAAA,MAAA,EAAA,KAAA;QACA,MAAA,KAAA,GAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,OAAA,CAAA,MAAA,GAAA,CAAA,CAAA;QACA,IAAA,CAAA,KAAA,EAAA;YACA;QACA;QAEA,aAAA,CAAA,KAAA,CAAA,GAAA;YAAA,KAAA,EAAA,MAAA,CAAA,KAAA;YAAA,IAAA,EAAA,aAAA;QAAA,CAAA;QACA,SAAA,GAAA,KAAA;IACA,CAAA,EAAA,IAAA,CAAA;AACA;AAEA,+DAAA,GACA,SAAA,SAAA,GAAA;IACA,WAAA,oOAAA,EAAA,CAAA,EAAA,MAAA,EAAA,KAAA;QACA,MAAA,KAAA,GAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,OAAA,CAAA,MAAA,GAAA,CAAA,CAAA;QACA,IAAA,CAAA,KAAA,EAAA;YACA;QACA;QAEA,MAAA,UAAA,sMAAA,WAAA,oLAAA,+BAAA,EAAA,EAAA;QACA,MAAA,SAAA,uMAAA,UAAA,EAAA,KAAA,CAAA,SAAA,CAAA;QACA,aAAA,CAAA,KAAA,CAAA,GAAA;YAAA,KAAA,EAAA,MAAA,CAAA,KAAA;YAAA,IAAA,EAAA,aAAA;QAAA,CAAA;QACA,aAAA,CAAA,UAAA,CAAA,GAAA;YAAA,KAAA,EAAA,UAAA,GAAA,SAAA;YAAA,IAAA,EAAA,QAAA;QAAA,CAAA;IACA,CAAA,CAAA;AACA;AAEA,SAAA,UAAA,GAAA;IACA,gNAAA,gCAAA,EAAA,CAAA,EAAA,MAAA,EAAA,KAAA;QACA,MAAA,KAAA,GAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,OAAA,CAAA,MAAA,GAAA,CAAA,CAAA;QACA,IAAA,CAAA,KAAA,EAAA;YACA;QACA;QAEA,aAAA,CAAA,MAAA,CAAA,GAAA;YAAA,KAAA,EAAA,MAAA,CAAA,KAAA;YAAA,IAAA,EAAA,aAAA;QAAA,CAAA;IACA,CAAA,CAAA;AACA;AA0BA,mDAAA,GACA,SAAA,qBAAA,CAAA,IAAA,EAAA,OAAA,EAAA;IACA,MAAA,WAAA,GAAA,+NAAA,EAAA;IACA,MAAA,MAAA,qLAAA,+BAAA,EAAA;IACA,IAAA,CAAA,WAAA,EAAA,UAAA,IAAA,CAAA,MAAA,EAAA;QACA,8CAAA;QACA;IACA;IAEA,MAAA,UAAA,uMAAA,UAAA,EAAA,MAAA,CAAA;IAEA,MAAA,kBAAA,GAAA,WAAA,CAAA,UAAA,EAAA;IAEA,MAAA,EAAA,EAAA,EAAA,eAAA,EAAA,oBAAA,EAAA,iLAAA,aAAA,EAAA,IAAA,CAAA;IAEA,kBAAA,CAAA,KAAA,CAAA,kBAAA,CAAA,CAAA,OAAA,EAAA,KAAA,IAAA;QACA,MAAA,SAAA,uMAAA,UAAA,EAAA,KAAA,CAAA,SAAA,CAAA;QACA,MAAA,QAAA,uMAAA,UAAA,EACA,yFAAA;QACA,yLAAA;QACA,wKAAA;QACA,uKAAA;QACA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,KAAA,CAAA,QAAA,CAAA;QAGA,IAAA,EAAA,KAAA,YAAA,IAAA,oBAAA,IAAA,UAAA,GAAA,SAAA,GAAA,oBAAA,EAAA;YACA;QACA;QAEA,OAAA,KAAA,CAAA,SAAA;YACA,KAAA,YAAA;gBAAA;oBACA,mBAAA,CAAA,IAAA,EAAA,KAAA,EAAA,UAAA,CAAA;oBACA;gBACA;YACA,KAAA,MAAA;YACA,KAAA,OAAA;YACA,KAAA,SAAA;gBAAA;oBACA,gBAAA,CAAA,IAAA,EAAA,KAAA,EAAA,SAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,CAAA,yBAAA,CAAA;oBAEA,qBAAA;oBACA,MAAA,WAAA,8OAAA,uBAAA,EAAA;oBACA,gEAAA;oBACA,MAAA,YAAA,GAAA,KAAA,CAAA,SAAA,GAAA,WAAA,CAAA,eAAA;oBAEA,IAAA,KAAA,CAAA,IAAA,KAAA,aAAA,IAAA,YAAA,EAAA;wBACA,aAAA,CAAA,IAAA,CAAA,GAAA;4BAAA,KAAA,EAAA,KAAA,CAAA,SAAA;4BAAA,IAAA,EAAA,aAAA;wBAAA,CAAA;oBACA;oBACA,IAAA,KAAA,CAAA,IAAA,KAAA,wBAAA,IAAA,YAAA,EAAA;wBACA,aAAA,CAAA,KAAA,CAAA,GAAA;4BAAA,KAAA,EAAA,KAAA,CAAA,SAAA;4BAAA,IAAA,EAAA,aAAA;wBAAA,CAAA;oBACA;oBACA;gBACA;YACA,KAAA,UAAA;gBAAA;oBACA,iBAAA,CACA,IAAA,EACA,KAAA,EACA,KAAA,CAAA,IAAA,EACA,SAAA,EACA,QAAA,EACA,UAAA,EACA,OAAA,CAAA,mBAAA;oBAEA;gBACA;QAEA;IACA,CAAA,CAAA;IAEA,kBAAA,GAAA,IAAA,CAAA,GAAA,CAAA,kBAAA,CAAA,MAAA,GAAA,CAAA,EAAA,CAAA,CAAA;IAEA,eAAA,CAAA,IAAA,CAAA;IAEA,4DAAA;IACA,IAAA,EAAA,KAAA,UAAA,EAAA;QACA,iCAAA,CAAA,aAAA,CAAA;QAEA,MAAA,OAAA,GAAA,aAAA,CAAA,UAAA,CAAA;QACA,IAAA,OAAA,IAAA,aAAA,CAAA,KAAA,CAAA,EAAA;YACA,sBAAA;gNACA,kBAAA,EAAA,IAAA,EAAA,OAAA,CAAA,KAAA,EAAA,OAAA,CAAA,KAAA,IAAA,6MAAA,EAAA,aAAA,CAAA,KAAA,CAAA,CAAA,KAAA,CAAA,EAAA;gBACA,IAAA,EAAA,mBAAA;gBACA,EAAA,EAAA,WAAA;gBACA,UAAA,EAAA;oBACA,2KAAA,mCAAA,CAAA,EAAA,yBAAA;gBACA,CAAA;YACA,CAAA,CAAA;YAEA,kEAAA;YACA,OAAA,aAAA,CAAA,UAAA,CAAA;QACA;QAEA,4DAAA;QACA,0CAAA;QACA,6GAAA;QACA,IAAA,CAAA,CAAA,KAAA,IAAA,aAAA,CAAA,IAAA,CAAA,OAAA,CAAA,uBAAA,EAAA;YACA,OAAA,aAAA,CAAA,GAAA;QACA;QAEA,MAAA,CAAA,OAAA,CAAA,aAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,eAAA,EAAA,WAAA,CAAA,KAAA;aACA,kMAAA,EAAA,eAAA,EAAA,WAAA,CAAA,KAAA,EAAA,WAAA,CAAA,IAAA,CAAA;QACA,CAAA,CAAA;QAEA,+FAAA;QACA,IAAA,CAAA,YAAA,CAAA,wBAAA,EAAA,UAAA,CAAA;QAEA,gHAAA;QACA,uGAAA;QACA,6GAAA;QACA,uFAAA;QACA,yGAAA;QACA,wEAAA;QACA,IAAA,CAAA,YAAA,CAAA,6BAAA,2OAAA,qBAAA,EAAA,CAAA;QAEA,sBAAA,CAAA,IAAA,CAAA;IACA;IAEA,SAAA,GAAA,SAAA;IACA,SAAA,GAAA,SAAA;IACA,aAAA,GAAA,CAAA,CAAA;AACA;AAEA;;;CAGA,GACA,SAAA,gBAAA,CACA,IAAA,EACA,KAAA,EACA,SAAA,EACA,QAAA,EACA,UAAA,EACA,yBAAA;IAEA,IACA;QAAA,MAAA;QAAA,SAAA;KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,SAAA,CAAA,wLACA,2BAAA,EAAA,KAAA,CAAA,IAAA,EAAA,yBAAA,GACA;QACA;IACA;IAEA,MAAA,QAAA,4OAAA,qBAAA,EAAA,KAAA,CAAA;IACA,MAAA,WAAA,IAAA,6MAAA,EAAA,QAAA,GAAA,QAAA,CAAA,YAAA,GAAA,CAAA,CAAA;IACA,0EAAA;IACA,0EAAA;IACA,EAAA;IACA,6EAAA;IACA,0CAAA;IACA,EAAA;IACA,4EAAA;IACA,0EAAA;IACA,2BAAA;IACA,MAAA,qBAAA,GAAA,UAAA,GAAA,IAAA,CAAA,GAAA,CAAA,SAAA,EAAA,WAAA,CAAA;IACA,MAAA,cAAA,GAAA,UAAA,GAAA,SAAA;IACA,MAAA,mBAAA,GAAA,cAAA,GAAA,QAAA;IAEA,MAAA,UAAA,GAAA;QACA,2KAAA,mCAAA,CAAA,EAAA,+BAAA;IACA,CAAA;IAEA,IAAA,qBAAA,KAAA,cAAA,EAAA;QACA,UAAA,CAAA,gDAAA,CAAA,GAAA,IAAA;QACA,UAAA,CAAA,mCAAA,CAAA,GAAA,qBAAA;IACA;IAEA,0BAAA,CAAA,UAAA,EAAA,KAAA,EAAA;IAEA,sHAAA;IACA,IAAA,qBAAA,IAAA,mBAAA,EAAA;YACA,kNAAA,EAAA,IAAA,EAAA,qBAAA,EAAA,mBAAA,EAAA;YACA,IAAA,EAAA,KAAA,CAAA,IAAA;YACA,EAAA,EAAA,KAAA,CAAA,SAAA;YACA,UAAA;QACA,CAAA,CAAA;IACA;AACA;AAEA,SAAA,0BAAA,CAAA,UAAA,EAAA,kBAAA,EAAA;IACA,IAAA;QACA,6FAAA;QACA,MAAA,MAAA,GAAA,kBAAA,CAAA,MAAA;QAEA,IAAA,CAAA,MAAA,EAAA;YACA;QACA;QAEA,mCAAA;QACA,IAAA,OAAA,MAAA,KAAA,QAAA,EAAA;YACA,wBAAA;YACA,KAAA,MAAA,CAAA,GAAA,EAAA,KAAA,CAAA,IAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA;gBACA,IAAA,KAAA,oLAAA,cAAA,EAAA,KAAA,CAAA,EAAA;oBACA,UAAA,CAAA,CAAA,8BAAA,EAAA,GAAA,CAAA,CAAA,CAAA,GAAA,KAAA;gBACA,CAAA,MAAA,IAAA,KAAA,KAAA,SAAA,EAAA;oBACA,IAAA;wBACA,+DAAA;wBACA,UAAA,CAAA,CAAA,8BAAA,EAAA,GAAA,CAAA,CAAA,CAAA,GAAA,IAAA,CAAA,SAAA,CAAA,KAAA,CAAA;oBACA,CAAA,CAAA,OAAA;oBACA,wCAAA;oBACA;gBACA;YACA;YACA;QACA;QAEA,oLAAA,cAAA,EAAA,MAAA,CAAA,EAAA;YACA,2BAAA;YACA,UAAA,CAAA,+BAAA,CAAA,GAAA,MAAA;YACA;QACA;QAEA,IAAA;YACA,UAAA,CAAA,+BAAA,CAAA,GAAA,IAAA,CAAA,SAAA,CAAA,MAAA,CAAA;QACA,CAAA,CAAA,OAAA;QACA,gCAAA;QACA;IACA,CAAA,CAAA,OAAA;IACA,mDAAA;IACA,sEAAA;IACA;AACA;AAEA;;;CAGA,GACA,SAAA,mBAAA,CAAA,IAAA,EAAA,KAAA,EAAA,UAAA,EAAA;IACA;QAAA,aAAA;QAAA,UAAA;QAAA,uBAAA;QAAA,WAAA;QAAA,SAAA;KAAA,CAAA,OAAA,EAAA,KAAA,IAAA;QACA,+BAAA,CAAA,IAAA,EAAA,KAAA,EAAA,KAAA,EAAA,UAAA,CAAA;IACA,CAAA,CAAA;IACA,+BAAA,CAAA,IAAA,EAAA,KAAA,EAAA,kBAAA,EAAA,UAAA,EAAA,SAAA,CAAA;IACA,+BAAA,CAAA,IAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,OAAA,CAAA;IACA,+BAAA,CAAA,IAAA,EAAA,KAAA,EAAA,cAAA,EAAA,UAAA,EAAA,KAAA,CAAA;IAEA,WAAA,CAAA,IAAA,EAAA,KAAA,EAAA,UAAA,CAAA;AACA;AAsBA,gDAAA,GACA,SAAA,+BAAA,CACA,IAAA,EACA,KAAA,EACA,KAAA,EACA,UAAA,EACA,IAAA,GAAA,KAAA;IAEA,MAAA,QAAA,GAAA,sCAAA,CAAA,KAAA,CAAA;IACA,MAAA,GAAA,GAAA,KAAA,CAAA,QAAA,CAAA;IACA,MAAA,KAAA,GAAA,KAAA,CAAA,CAAA,EAAA,KAAA,CAAA,KAAA,CAAA,CAAA;IACA,IAAA,CAAA,KAAA,IAAA,CAAA,GAAA,EAAA;QACA;IACA;IACA,sNAAA,EAAA,IAAA,EAAA,UAAA,uMAAA,UAAA,EAAA,KAAA,CAAA,EAAA,UAAA,OAAA,0MAAA,EAAA,GAAA,CAAA,EAAA;QACA,EAAA,EAAA,CAAA,QAAA,EAAA,IAAA,CAAA,CAAA;QACA,IAAA,EAAA,KAAA,CAAA,IAAA;QACA,UAAA,EAAA;YACA,0KAAA,oCAAA,CAAA,EAAA,yBAAA;YACA,GAAA,KAAA,KAAA,UAAA,IAAA,KAAA,CAAA,aAAA,IAAA,IAAA,GAAA;gBAAA,qBAAA,EAAA,KAAA,CAAA,aAAA;YAAA,CAAA,GAAA,CAAA,CAAA,CAAA;QACA,CAAA;IACA,CAAA,CAAA;AACA;AAEA,SAAA,sCAAA,CAAA,KAAA,EAAA;IACA,IAAA,KAAA,KAAA,kBAAA,EAAA;QACA,OAAA,YAAA;IACA;IACA,IAAA,KAAA,KAAA,OAAA,EAAA;QACA,OAAA,mBAAA;IACA;IACA,OAAA,CAAA,EAAA,KAAA,CAAA,GAAA,CAAA;AACA;AAEA,8CAAA,GACA,SAAA,WAAA,CAAA,IAAA,EAAA,KAAA,EAAA,UAAA,EAAA;IACA,MAAA,qBAAA,GAAA,UAAA,uMAAA,UAAA,EAAA,KAAA,CAAA,YAAA,EAAA;IACA,MAAA,oBAAA,GAAA,UAAA,uMAAA,UAAA,EAAA,KAAA,CAAA,WAAA,EAAA;IACA,MAAA,sBAAA,GAAA,UAAA,sMAAA,WAAA,EAAA,KAAA,CAAA,aAAA,EAAA;IACA,IAAA,KAAA,CAAA,WAAA,EAAA;QACA,8IAAA;QACA,kGAAA;QACA,oHAAA;QACA,oIAAA;QACA,sNAAA,EAAA,IAAA,EAAA,qBAAA,EAAA,oBAAA,EAAA;YACA,EAAA,EAAA,iBAAA;YACA,IAAA,EAAA,KAAA,CAAA,IAAA;YACA,UAAA,EAAA;gBACA,2KAAA,mCAAA,CAAA,EAAA,yBAAA;YACA,CAAA;QACA,CAAA,CAAA;4MAEA,kBAAA,EAAA,IAAA,EAAA,sBAAA,EAAA,oBAAA,EAAA;YACA,EAAA,EAAA,kBAAA;YACA,IAAA,EAAA,KAAA,CAAA,IAAA;YACA,UAAA,EAAA;gBACA,2KAAA,mCAAA,CAAA,EAAA,yBAAA;YACA,CAAA;QACA,CAAA,CAAA;IACA;AACA;AAEA;;;CAGA,GACA,SAAA,iBAAA,CACA,IAAA,EACA,KAAA,EACA,WAAA,EACA,SAAA,EACA,QAAA,EACA,UAAA,EACA,mBAAA;IAEA,oEAAA;IACA,wBAAA;IACA,IAAA,KAAA,CAAA,aAAA,KAAA,gBAAA,IAAA,KAAA,CAAA,aAAA,KAAA,OAAA,EAAA;QACA;IACA;IAEA,MAAA,EAAA,GAAA,KAAA,CAAA,aAAA,GAAA,CAAA,SAAA,EAAA,KAAA,CAAA,aAAA,CAAA,CAAA,GAAA,gBAAA;IACA,IAAA,mBAAA,EAAA,QAAA,CAAA,EAAA,CAAA,EAAA;QACA;IACA;IAEA,MAAA,SAAA,oLAAA,WAAA,EAAA,WAAA,CAAA;IAEA,MAAA,UAAA,GAAA;QACA,2KAAA,mCAAA,CAAA,EAAA,+BAAA;IACA,CAAA;IACA,wBAAA,CAAA,UAAA,EAAA,KAAA,EAAA,cAAA,EAAA,6BAAA,CAAA;IACA,wBAAA,CAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,8BAAA,CAAA;IACA,wBAAA,CAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,sCAAA,CAAA;IAEA,+DAAA;IACA,MAAA,YAAA,GAAA,KAAA,CAAA,YAAA;IACA,IAAA,YAAA,IAAA,IAAA,EAAA;QACA,UAAA,CAAA,6BAAA,CAAA,GAAA,YAAA;IACA;IAEA,yCAAA;IACA,MAAA,oBAAA,GAAA,KAAA,CACA,oBAAA;IACA,IAAA,oBAAA,EAAA;QACA,UAAA,CAAA,iCAAA,CAAA,GAAA,oBAAA;IACA;IAEA,IAAA,SAAA,CAAA,QAAA,EAAA;QACA,UAAA,CAAA,YAAA,CAAA,GAAA,SAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,EAAA,CAAA,CAAA,2FAAA;IACA;IAEA,IAAA,SAAA,CAAA,IAAA,EAAA;QACA,UAAA,CAAA,gBAAA,CAAA,GAAA,SAAA,CAAA,IAAA;IACA;IAEA,UAAA,CAAA,iBAAA,CAAA,GAAA,WAAA,CAAA,QAAA,sLAAA,SAAA,CAAA,QAAA,CAAA,MAAA,CAAA;IAEA,MAAA,EAAA,IAAA,EAAA,OAAA,EAAA,uMAAA,yBAAA,EAAA,KAAA,CAAA,eAAA,CAAA;IACA,UAAA,CAAA,uBAAA,CAAA,GAAA,IAAA;IACA,UAAA,CAAA,0BAAA,CAAA,GAAA,OAAA;IAEA,MAAA,cAAA,GAAA,UAAA,GAAA,SAAA;IACA,MAAA,YAAA,GAAA,cAAA,GAAA,QAAA;IAEA,sNAAA,EAAA,IAAA,EAAA,cAAA,EAAA,YAAA,EAAA;QACA,IAAA,EAAA,WAAA,CAAA,OAAA,sLAAA,SAAA,CAAA,QAAA,CAAA,MAAA,EAAA,EAAA,CAAA;QACA,EAAA;QACA,UAAA;IACA,CAAA,CAAA;AACA;AAEA;;CAEA,GACA,SAAA,eAAA,CAAA,IAAA,EAAA;IACA,MAAA,SAAA,wLAAA,SAAA,CAAA,SAAA;IACA,IAAA,CAAA,SAAA,EAAA;QACA;IACA;IAEA,6BAAA;IACA,MAAA,UAAA,GAAA,SAAA,CAAA,UAAA;IACA,IAAA,UAAA,EAAA;QACA,IAAA,UAAA,CAAA,aAAA,EAAA;YACA,IAAA,CAAA,YAAA,CAAA,yBAAA,EAAA,UAAA,CAAA,aAAA,CAAA;QACA;QAEA,IAAA,UAAA,CAAA,IAAA,EAAA;YACA,IAAA,CAAA,YAAA,CAAA,gBAAA,EAAA,UAAA,CAAA,IAAA,CAAA;QACA;QAEA,wMAAA,qBAAA,EAAA,UAAA,CAAA,GAAA,CAAA,EAAA;YACA,aAAA,CAAA,gBAAA,CAAA,GAAA;gBAAA,KAAA,EAAA,UAAA,CAAA,GAAA;gBAAA,IAAA,EAAA,aAAA;YAAA,CAAA;QACA;IACA;IAEA,wMAAA,qBAAA,EAAA,SAAA,CAAA,YAAA,CAAA,EAAA;QACA,IAAA,CAAA,YAAA,CAAA,cAAA,EAAA,CAAA,EAAA,SAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA;IACA;IAEA,wMAAA,qBAAA,EAAA,SAAA,CAAA,mBAAA,CAAA,EAAA;QACA,IAAA,CAAA,YAAA,CAAA,qBAAA,EAAA,MAAA,CAAA,SAAA,CAAA,mBAAA,CAAA,CAAA;IACA;AACA;AAEA,kDAAA,GACA,SAAA,sBAAA,CAAA,IAAA,EAAA;IACA,IAAA,SAAA,EAAA;QACA,qEAAA;QAEA,IAAA,SAAA,CAAA,OAAA,EAAA;YACA,IAAA,CAAA,YAAA,CAAA,aAAA,GAAA,uMAAA,EAAA,SAAA,CAAA,OAAA,CAAA,CAAA;QACA;QAEA,IAAA,SAAA,CAAA,EAAA,EAAA;YACA,IAAA,CAAA,YAAA,CAAA,QAAA,EAAA,SAAA,CAAA,EAAA,CAAA;QACA;QAEA,IAAA,SAAA,CAAA,GAAA,EAAA;YACA,wCAAA;YACA,IAAA,CAAA,YAAA,CAAA,SAAA,EAAA,SAAA,CAAA,GAAA,CAAA,IAAA,EAAA,CAAA,KAAA,CAAA,CAAA,EAAA,GAAA,CAAA,CAAA;QACA;QAEA,IAAA,SAAA,CAAA,QAAA,IAAA,IAAA,EAAA;YACA,qFAAA;YACA,IAAA,CAAA,YAAA,CAAA,cAAA,EAAA,SAAA,CAAA,QAAA,CAAA;QACA;QAEA,IAAA,SAAA,CAAA,UAAA,IAAA,IAAA,EAAA;YACA,0CAAA;YACA,oFAAA;YACA,gCAAA;YACA,IAAA,CAAA,YAAA,CAAA,gBAAA,EAAA,SAAA,CAAA,UAAA,CAAA;QACA;QAEA,IAAA,CAAA,YAAA,CAAA,UAAA,EAAA,SAAA,CAAA,IAAA,CAAA;IACA;IAEA,oEAAA;IACA,IAAA,SAAA,EAAA,OAAA,EAAA;QACA,SAAA,CAAA,OAAA,CAAA,OAAA,CAAA,CAAA,MAAA,EAAA,KAAA,GACA,IAAA,CAAA,YAAA,CAAA,CAAA,WAAA,EAAA,KAAA,GAAA,CAAA,CAAA,CAAA,EAAA,wMAAA,EAAA,MAAA,CAAA,IAAA,CAAA,CAAA;IAEA;AACA;AAEA,SAAA,wBAAA,CACA,UAAA,EACA,KAAA,EACA,GAAA,EACA,OAAA;IAEA,MAAA,QAAA,GAAA,KAAA,CAAA,GAAA,CAAA;IACA,IAAA,QAAA,IAAA,IAAA,IAAA,QAAA,GAAA,gBAAA,EAAA;QACA,UAAA,CAAA,OAAA,CAAA,GAAA,QAAA;IACA;AACA;AAEA;;;;CAIA,GACA,SAAA,iCAAA,CAAA,aAAA,EAAA;IACA,MAAA,QAAA,IAAA,6PAAA,EAAA,KAAA,CAAA;IACA,IAAA,CAAA,QAAA,EAAA;QACA;IACA;IAEA,MAAA,EAAA,aAAA,EAAA,YAAA,EAAA,GAAA,QAAA;IAEA,IAAA,YAAA,IAAA,aAAA,EAAA;QACA,aAAA,CAAA,kBAAA,CAAA,GAAA;YACA,KAAA,EAAA,aAAA,GAAA,YAAA;YACA,IAAA,EAAA,aAAA;QACA,CAAA;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3281, "column": 0}, "map": {"version": 3, "file": "networkUtils.js", "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40sentry-internal/browser-utils/src/networkUtils.ts"], "sourcesContent": ["import type { Logger } from '@sentry/core';\nimport { logger } from '@sentry/core';\nimport { DEBUG_BUILD } from './debug-build';\nimport type { NetworkMetaWarning } from './types';\n\n/**\n * Serializes FormData.\n *\n * This is a bit simplified, but gives us a decent estimate.\n * This converts e.g. { name: '<PERSON>', age: 13 } to 'name=<PERSON>&age=13'.\n *\n */\nexport function serializeFormData(formData: FormData): string {\n  // @ts-expect-error passing FormData to URLSearchParams actually works\n  return new URLSearchParams(formData).toString();\n}\n\n/** Get the string representation of a body. */\nexport function getBodyString(body: unknown, _logger: Logger = logger): [string | undefined, NetworkMetaWarning?] {\n  try {\n    if (typeof body === 'string') {\n      return [body];\n    }\n\n    if (body instanceof URLSearchParams) {\n      return [body.toString()];\n    }\n\n    if (body instanceof FormData) {\n      return [serializeFormData(body)];\n    }\n\n    if (!body) {\n      return [undefined];\n    }\n  } catch (error) {\n    DEBUG_BUILD && _logger.error(error, 'Failed to serialize body', body);\n    return [undefined, 'BODY_PARSE_ERROR'];\n  }\n\n  DEBUG_BUILD && _logger.info('Skipping network body because of body type', body);\n\n  return [undefined, 'UNPARSEABLE_BODY_TYPE'];\n}\n\n/**\n * Parses the fetch arguments to extract the request payload.\n *\n * We only support getting the body from the fetch options.\n */\nexport function getFetchRequestArgBody(fetchArgs: unknown[] = []): RequestInit['body'] | undefined {\n  if (fetchArgs.length !== 2 || typeof fetchArgs[1] !== 'object') {\n    return undefined;\n  }\n\n  return (fetchArgs[1] as RequestInit).body;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAKA;;;;;;CAMA,GACO,SAAS,iBAAiB,CAAC,QAAQ,EAAoB;IAC9D,sEAAA;IACE,OAAO,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE;AACjD;AAEA,6CAAA,GACO,SAAS,aAAa,CAAC,IAAI,EAAW,OAAO,mLAAW,SAAM,EAA6C;IAChH,IAAI;QACF,IAAI,OAAO,IAAK,KAAI,QAAQ,EAAE;YAC5B,OAAO;gBAAC,IAAI;aAAC;QACnB;QAEI,IAAI,IAAK,YAAW,eAAe,EAAE;YACnC,OAAO;gBAAC,IAAI,CAAC,QAAQ,EAAE;aAAC;QAC9B;QAEI,IAAI,IAAK,YAAW,QAAQ,EAAE;YAC5B,OAAO;gBAAC,iBAAiB,CAAC,IAAI,CAAC;aAAC;QACtC;QAEI,IAAI,CAAC,IAAI,EAAE;YACT,OAAO;gBAAC,SAAS;aAAC;QACxB;IACA,CAAI,CAAA,OAAO,KAAK,EAAE;sMACd,cAAA,IAAe,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,0BAA0B,EAAE,IAAI,CAAC;QACrE,OAAO;YAAC,SAAS;YAAE,kBAAkB;SAAC;IAC1C;kMAEE,cAAA,IAAe,OAAO,CAAC,IAAI,CAAC,4CAA4C,EAAE,IAAI,CAAC;IAE/E,OAAO;QAAC,SAAS;QAAE,uBAAuB;KAAC;AAC7C;AAEA;;;;CAIA,GACO,SAAS,sBAAsB,CAAC,SAAS,GAAc,EAAE,EAAmC;IACjG,IAAI,SAAS,CAAC,MAAA,KAAW,CAAA,IAAK,OAAO,SAAS,CAAC,CAAC,CAAE,KAAI,QAAQ,EAAE;QAC9D,OAAO,SAAS;IACpB;IAEE,OAAO,AAAC,SAAS,CAAC,CAAC,CAAE,CAAgB,IAAI;AAC3C", "ignoreList": [0], "debugId": null}}]}