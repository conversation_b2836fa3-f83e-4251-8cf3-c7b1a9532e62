import { randomBytes as g } from "node:crypto";
import { resolve as u } from "import-meta-resolve";
import { setupSidecar as d } from "@spotlightjs/sidecar";
const S = "@spotlightjs/spotlight", v = 7;
function p({
  server: t,
  module: r = S
} = {}) {
  const e = u(r, import.meta.url).slice(v).split("?", 1)[0];
  return t && t.config.server.fs.allow.push(e), e;
}
const y = /* @__PURE__ */ new Set(["importPath", "integrationNames", "port"]);
function O(t) {
  const r = Object.fromEntries(
    Object.entries(t).filter(([i]) => !i.startsWith("_") && !y.has(i))
  );
  let e = JSON.stringify({
    ...r,
    showTriggerButton: t.showTriggerButton !== !1,
    injectImmediately: t.injectImmediately !== !1
  });
  const n = JSON.stringify({ openLastError: !0 });
  return e = `{integrations: [${(t.integrationNames || ["sentry"]).map((i) => `Spotlight.${i}(${n})`).join(", ")}], ${e.slice(1)}`, [
    `import * as Spotlight from ${JSON.stringify(`/@fs${t.importPath || p()}`)};`,
    `Spotlight.init(${e});`,
    "window.createErrorOverlay=function createErrorOverlay(err) { Spotlight.openSpotlight(); };"
  ].join(`
`);
}
async function w(t, r = "http://localhost:8969/stream") {
  var c;
  if (!t.errors) {
    console.log(t);
    return;
  }
  const e = t.errors[0], n = (c = t.pluginCode) == null ? void 0 : c.split(`
`), o = e.location.lineText, i = n == null ? void 0 : n.indexOf(o), s = g(16).toString("hex"), a = /* @__PURE__ */ new Date(), m = new URL(r);
  let l = r;
  m.pathname.endsWith("/stream") || (l = new URL("/stream", r).href);
  const f = [
    { event_id: s, sent_at: a.toISOString() },
    { type: "event" },
    {
      event_id: s,
      level: "error",
      platform: "javascript",
      environment: "development",
      tags: { runtime: "vite" },
      timestamp: a.getTime(),
      exception: {
        values: [
          {
            type: "Error",
            mechanism: {
              type: "instrument",
              handled: !1
            },
            value: e.text,
            stacktrace: {
              frames: [
                e ? {
                  filename: e.location.file,
                  lineno: e.location.line,
                  colno: e.location.column,
                  context_line: o,
                  pre_context: n == null ? void 0 : n.slice(0, i),
                  post_context: i != null && i > -1 ? n == null ? void 0 : n.slice(i + 1) : void 0
                } : {
                  filename: t.id
                }
              ]
            }
          }
        ]
      }
    }
  ].map((h) => JSON.stringify(h)).join(`
`);
  return await fetch(l, {
    method: "POST",
    body: f,
    headers: { "Content-Type": "application/x-sentry-envelope" }
  });
}
function _(t = {}) {
  let r;
  return {
    name: "spotlight",
    apply: "serve",
    transform(e, n) {
      if (n.endsWith("vite/dist/client/client.mjs"))
        return `${O({ ...t, importPath: r })}${e}`;
    },
    configureServer(e) {
      return d({ port: t.port }), r = p({ server: e }), () => e.middlewares.use(async function(o, i, s, a) {
        if (await w(o, t.sidecarUrl), s.headersSent)
          return a(o);
      });
    }
  };
}
export {
  O as buildClientInit,
  _ as default,
  p as getSpotlightClientModulePath
};
