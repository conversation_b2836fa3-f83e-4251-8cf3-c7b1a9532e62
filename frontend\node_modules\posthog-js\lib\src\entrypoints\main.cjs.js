"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.posthog = exports.PostHog = void 0;
require("./external-scripts-loader");
var posthog_core_1 = require("../posthog-core");
var posthog_core_2 = require("../posthog-core");
Object.defineProperty(exports, "PostHog", { enumerable: true, get: function () { return posthog_core_2.PostHog; } });
__exportStar(require("../types"), exports);
__exportStar(require("../posthog-surveys-types"), exports);
exports.posthog = (0, posthog_core_1.init_as_module)();
exports.default = exports.posthog;
//# sourceMappingURL=main.cjs.js.map