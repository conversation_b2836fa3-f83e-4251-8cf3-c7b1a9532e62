{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/Sponsors.tsx"], "sourcesContent": ["/* eslint-disable react-dom/no-unsafe-target-blank */\r\nimport Image from 'next/image';\r\n\r\nexport const Sponsors = () => (\r\n  <table className=\"border-collapse\">\r\n    <tbody>\r\n      <tr className=\"h-56\">\r\n        <td className=\"border-2 border-gray-300 p-3\">\r\n          <a\r\n            href=\"https://clerk.com?utm_source=github&utm_medium=sponsorship&utm_campaign=nextjs-boilerplate\"\r\n            target=\"_blank\"\r\n            rel=\"noopener\"\r\n          >\r\n            <Image\r\n              src=\"/assets/images/clerk-logo-dark.png\"\r\n              alt=\"Clerk – Authentication & User Management for Next.js\"\r\n              width={260}\r\n              height={224}\r\n            />\r\n          </a>\r\n        </td>\r\n        <td className=\"border-2 border-gray-300 p-3\">\r\n          <a href=\"https://www.coderabbit.ai?utm_source=next_js_starter&utm_medium=github&utm_campaign=next_js_starter_oss_2025\" target=\"_blank\" rel=\"noopener\">\r\n            <Image\r\n              src=\"/assets/images/coderabbit-logo-light.svg\"\r\n              alt=\"CodeRabbit\"\r\n              width={260}\r\n              height={224}\r\n            />\r\n          </a>\r\n        </td>\r\n        <td className=\"border-2 border-gray-300 p-3\">\r\n          <a\r\n            href=\"https://sentry.io/for/nextjs/?utm_source=github&utm_medium=paid-community&utm_campaign=general-fy25q1-nextjs&utm_content=github-banner-nextjsboilerplate-logo\"\r\n            target=\"_blank\"\r\n            rel=\"noopener\"\r\n          >\r\n            <Image\r\n              src=\"/assets/images/sentry-dark.png\"\r\n              alt=\"Sentry\"\r\n              width={260}\r\n              height={224}\r\n            />\r\n          </a>\r\n        </td>\r\n      </tr>\r\n      <tr className=\"h-56\">\r\n        <td className=\"border-2 border-gray-300 p-3\">\r\n          <a href=\"https://launch.arcjet.com/Q6eLbRE\">\r\n            <Image\r\n              src=\"/assets/images/arcjet-light.svg\"\r\n              alt=\"Arcjet\"\r\n              width={260}\r\n              height={224}\r\n            />\r\n          </a>\r\n        </td>\r\n        <td className=\"border-2 border-gray-300 p-3\">\r\n          <a href=\"https://sevalla.com/\">\r\n            <Image\r\n              src=\"/assets/images/sevalla-light.png\"\r\n              alt=\"Sevalla\"\r\n              width={260}\r\n              height={224}\r\n            />\r\n          </a>\r\n        </td>\r\n        <td className=\"border-2 border-gray-300 p-3\">\r\n          <a href=\"https://l.crowdin.com/next-js\" target=\"_blank\" rel=\"noopener\">\r\n            <Image\r\n              src=\"/assets/images/crowdin-dark.png\"\r\n              alt=\"Crowdin\"\r\n              width={260}\r\n              height={224}\r\n            />\r\n          </a>\r\n        </td>\r\n      </tr>\r\n      <tr className=\"h-56\">\r\n        <td className=\"border-2 border-gray-300 p-3\">\r\n          <a\r\n            href=\"https://posthog.com/?utm_source=github&utm_medium=sponsorship&utm_campaign=next-js-boilerplate\"\r\n            target=\"_blank\"\r\n            rel=\"noopener\"\r\n          >\r\n            <Image\r\n              src=\"https://posthog.com/brand/posthog-logo.svg\"\r\n              alt=\"PostHog\"\r\n              width={260}\r\n              height={224}\r\n            />\r\n          </a>\r\n        </td>\r\n        <td className=\"border-2 border-gray-300 p-3\">\r\n          <a\r\n            href=\"https://betterstack.com/?utm_source=github&utm_medium=sponsorship&utm_campaign=next-js-boilerplate\"\r\n            target=\"_blank\"\r\n            rel=\"noopener\"\r\n          >\r\n            <Image\r\n              src=\"/assets/images/better-stack-dark.png\"\r\n              alt=\"Better Stack\"\r\n              width={260}\r\n              height={224}\r\n            />\r\n          </a>\r\n        </td>\r\n        <td className=\"border-2 border-gray-300 p-3\">\r\n          <a\r\n            href=\"https://www.checklyhq.com/?utm_source=github&utm_medium=sponsorship&utm_campaign=next-js-boilerplate\"\r\n            target=\"_blank\"\r\n            rel=\"noopener\"\r\n          >\r\n            <Image\r\n              src=\"/assets/images/checkly-logo-light.png\"\r\n              alt=\"Checkly\"\r\n              width={260}\r\n              height={224}\r\n            />\r\n          </a>\r\n        </td>\r\n      </tr>\r\n      <tr className=\"h-56\">\r\n        <td className=\"border-2 border-gray-300 p-3\">\r\n          <a href=\"https://nextjs-boilerplate.com/pro-saas-starter-kit\">\r\n            <Image\r\n              src=\"/assets/images/nextjs-boilerplate-saas.png\"\r\n              alt=\"Next.js SaaS Boilerplate\"\r\n              width={260}\r\n              height={224}\r\n            />\r\n          </a>\r\n        </td>\r\n      </tr>\r\n    </tbody>\r\n  </table>\r\n);\r\n"], "names": [], "mappings": "AAAA,mDAAmD;;;;AACnD;;;AAEO,MAAM,WAAW,kBACtB,8OAAC;QAAM,WAAU;kBACf,cAAA,8OAAC;;8BACC,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCACC,MAAK;gCACL,QAAO;gCACP,KAAI;0CAEJ,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;;;;;;;;;;;;;;;;sCAId,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAE,MAAK;gCAA+G,QAAO;gCAAS,KAAI;0CACzI,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;;;;;;;;;;;;;;;;sCAId,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCACC,MAAK;gCACL,QAAO;gCACP,KAAI;0CAEJ,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;;;;;;;;;;;;;;;;;;;;;;8BAKhB,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAE,MAAK;0CACN,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;;;;;;;;;;;;;;;;sCAId,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAE,MAAK;0CACN,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;;;;;;;;;;;;;;;;sCAId,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAE,MAAK;gCAAgC,QAAO;gCAAS,KAAI;0CAC1D,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;;;;;;;;;;;;;;;;;;;;;;8BAKhB,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCACC,MAAK;gCACL,QAAO;gCACP,KAAI;0CAEJ,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;;;;;;;;;;;;;;;;sCAId,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCACC,MAAK;gCACL,QAAO;gCACP,KAAI;0CAEJ,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;;;;;;;;;;;;;;;;sCAId,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCACC,MAAK;gCACL,QAAO;gCACP,KAAI;0CAEJ,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;;;;;;;;;;;;;;;;;;;;;;8BAKhB,8OAAC;oBAAG,WAAU;8BACZ,cAAA,8OAAC;wBAAG,WAAU;kCACZ,cAAA,8OAAC;4BAAE,MAAK;sCACN,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ", "debugId": null}}, {"offset": {"line": 346, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/app/%5Blocale%5D/%28marketing%29/page.tsx"], "sourcesContent": ["import { getTranslations, setRequestLocale } from 'next-intl/server';\r\nimport { Sponsors } from '@/components/Sponsors';\r\n\r\ntype IIndexProps = {\r\n  params: Promise<{ locale: string }>;\r\n};\r\n\r\nexport async function generateMetadata(props: IIndexProps) {\r\n  const { locale } = await props.params;\r\n  const t = await getTranslations({\r\n    locale,\r\n    namespace: 'Index',\r\n  });\r\n\r\n  return {\r\n    title: t('meta_title'),\r\n    description: t('meta_description'),\r\n  };\r\n}\r\n\r\nexport default async function Index(props: IIndexProps) {\r\n  const { locale } = await props.params;\r\n  setRequestLocale(locale);\r\n  const t = await getTranslations({\r\n    locale,\r\n    namespace: 'Index',\r\n  });\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-white\">\r\n      {/* Hero Section */}\r\n      <section className=\"relative py-20 px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <div className=\"text-center\">\r\n            <h1 className=\"text-hero text-gradient mb-6\">\r\n              {t('hero_title')}\r\n            </h1>\r\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto mb-12\">\r\n              {t('hero_description')}\r\n            </p>\r\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-16\">\r\n              <button className=\"btn-primary text-lg px-8 py-4\">\r\n                {t('get_started_free')}\r\n              </button>\r\n              <button className=\"btn-outline text-lg px-8 py-4\">\r\n                {t('watch_demo')}\r\n              </button>\r\n            </div>\r\n\r\n            {/* Hero Image/Video Placeholder */}\r\n            <div className=\"relative max-w-5xl mx-auto\">\r\n              <div className=\"aspect-w-16 aspect-h-9 rounded-2xl overflow-hidden shadow-2xl\">\r\n                <div className=\"w-full h-96 bg-gradient-to-br from-primary-400 to-accent-500 flex items-center justify-center\">\r\n                  <div className=\"text-center text-white\">\r\n                    <svg className=\"w-24 h-24 mx-auto mb-4 opacity-80\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                      <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\" clipRule=\"evenodd\" />\r\n                    </svg>\r\n                    <p className=\"text-lg font-medium\">{t('demo_video_placeholder')}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Features Overview */}\r\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8 bg-white\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <div className=\"text-center mb-16\">\r\n            <h2 className=\"text-section text-gray-900 mb-6\">\r\n              {t('features_title')}\r\n            </h2>\r\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\r\n              {t('features_description')}\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\r\n            <div className=\"text-center\">\r\n              <div className=\"w-16 h-16 bg-primary-100 rounded-2xl flex items-center justify-center mx-auto mb-6\">\r\n                <svg className=\"w-8 h-8 text-primary-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">\r\n                {t('feature_1_title')}\r\n              </h3>\r\n              <p className=\"text-gray-600\">\r\n                {t('feature_1_description')}\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"text-center\">\r\n              <div className=\"w-16 h-16 bg-accent-100 rounded-2xl flex items-center justify-center mx-auto mb-6\">\r\n                <svg className=\"w-8 h-8 text-accent-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">\r\n                {t('feature_2_title')}\r\n              </h3>\r\n              <p className=\"text-gray-600\">\r\n                {t('feature_2_description')}\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"text-center\">\r\n              <div className=\"w-16 h-16 bg-success-100 rounded-2xl flex items-center justify-center mx-auto mb-6\">\r\n                <svg className=\"w-8 h-8 text-success-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\" />\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">\r\n                {t('feature_3_title')}\r\n              </h3>\r\n              <p className=\"text-gray-600\">\r\n                {t('feature_3_description')}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Social Proof */}\r\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8 bg-gray-50\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <div className=\"text-center mb-16\">\r\n            <h2 className=\"text-section text-gray-900 mb-6\">\r\n              {t('trusted_by_title')}\r\n            </h2>\r\n            <p className=\"text-xl text-gray-600\">\r\n              {t('trusted_by_description')}\r\n            </p>\r\n          </div>\r\n          <Sponsors />\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8 bg-primary-600\">\r\n        <div className=\"max-w-4xl mx-auto text-center\">\r\n          <h2 className=\"text-section text-white mb-6\">\r\n            {t('cta_title')}\r\n          </h2>\r\n          <p className=\"text-xl text-primary-100 mb-8\">\r\n            {t('cta_description')}\r\n          </p>\r\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n            <button className=\"bg-white text-primary-600 hover:bg-gray-50 font-medium px-8 py-4 rounded-lg transition-colors duration-200\">\r\n              {t('start_free_trial')}\r\n            </button>\r\n            <button className=\"border-2 border-white text-white hover:bg-white hover:text-primary-600 font-medium px-8 py-4 rounded-lg transition-all duration-200\">\r\n              {t('contact_sales')}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AACA;;;;AAMO,eAAe,iBAAiB,KAAkB;IACvD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,MAAM;IACrC,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAC9B;QACA,WAAW;IACb;IAEA,OAAO;QACL,OAAO,EAAE;QACT,aAAa,EAAE;IACjB;AACF;AAEe,eAAe,MAAM,KAAkB;IACpD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,MAAM;IACrC,CAAA,GAAA,2QAAA,CAAA,mBAAgB,AAAD,EAAE;IACjB,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAC9B;QACA,WAAW;IACb;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;0CAEL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDACf,EAAE;;;;;;kDAEL,8OAAC;wCAAO,WAAU;kDACf,EAAE;;;;;;;;;;;;0CAKP,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAAoC,MAAK;oDAAe,SAAQ;8DAC7E,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAA0G,UAAS;;;;;;;;;;;8DAEhJ,8OAAC;oDAAE,WAAU;8DAAuB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpD,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAEL,8OAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;sCAIP,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAA2B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAClF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDACX,EAAE;;;;;;sDAEL,8OAAC;4CAAE,WAAU;sDACV,EAAE;;;;;;;;;;;;8CAIP,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDACX,EAAE;;;;;;sDAEL,8OAAC;4CAAE,WAAU;sDACV,EAAE;;;;;;;;;;;;8CAIP,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAA2B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAClF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDACX,EAAE;;;;;;sDAEL,8OAAC;4CAAE,WAAU;sDACV,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAEL,8OAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;sCAGP,8OAAC,8HAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;0BAKb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,8OAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;sCAEL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;8CACf,EAAE;;;;;;8CAEL,8OAAC;oCAAO,WAAU;8CACf,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}]}