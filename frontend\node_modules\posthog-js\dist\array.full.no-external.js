!function(){"use strict";function e(e,t,r,i,n,s,o){try{var a=e[s](o),l=a.value}catch(e){return void r(e)}a.done?t(l):Promise.resolve(l).then(i,n)}function t(t){return function(){var r=this,i=arguments;return new Promise((function(n,s){var o=t.apply(r,i);function a(t){e(o,n,s,a,l,"next",t)}function l(t){e(o,n,s,a,l,"throw",t)}a(void 0)}))}}function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var i in r)({}).hasOwnProperty.call(r,i)&&(e[i]=r[i])}return e},r.apply(null,arguments)}function i(e,t){if(null==e)return{};var r={};for(var i in e)if({}.hasOwnProperty.call(e,i)){if(-1!==t.indexOf(i))continue;r[i]=e[i]}return r}var n,s=["type"],o=Object.defineProperty,a=(e,t,r)=>((e,t,r)=>t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r)(e,"symbol"!=typeof t?t+"":t,r),l=Object.defineProperty,u=(e,t,r)=>((e,t,r)=>t in e?l(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r)(e,"symbol"!=typeof t?t+"":t,r),c=(e=>(e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment",e))(c||{}),d={Node:["childNodes","parentNode","parentElement","textContent"],ShadowRoot:["host","styleSheets"],Element:["shadowRoot","querySelector","querySelectorAll"],MutationObserver:[]},h={Node:["contains","getRootNode"],ShadowRoot:["getSelection"],Element:[],MutationObserver:["constructor"]};var p={};function _(e){if(p[e])return p[e];var t=function(e){var t,r=null==globalThis||null==(t=globalThis.Zone)||null==t.__symbol__?void 0:t.__symbol__(e);return r&&globalThis[r]?globalThis[r]:void 0}(e)||globalThis[e],r=t.prototype,i=e in d?d[e]:void 0,n=Boolean(i&&i.every((e=>{var t,i;return Boolean(null==(i=null==(t=Object.getOwnPropertyDescriptor(r,e))?void 0:t.get)?void 0:i.toString().includes("[native code]"))}))),s=e in h?h[e]:void 0,o=Boolean(s&&s.every((e=>{var t;return"function"==typeof r[e]&&(null==(t=r[e])?void 0:t.toString().includes("[native code]"))})));if(n&&o)return p[e]=t.prototype,t.prototype;try{var a=document.createElement("iframe");document.body.appendChild(a);var l=a.contentWindow;if(!l)return t.prototype;var u=l[e].prototype;return document.body.removeChild(a),u?p[e]=u:t.prototype}catch(e){return t.prototype}}var g={};function f(e,t,r){var i,n=e+"."+String(r);if(g[n])return g[n].call(t);var s=_(e),o=null==(i=Object.getOwnPropertyDescriptor(s,r))?void 0:i.get;return o?(g[n]=o,o.call(t)):t[r]}var v={};function m(e,t,r){var i=e+"."+String(r);if(v[i])return v[i].bind(t);var n=_(e)[r];return"function"!=typeof n?t[r]:(v[i]=n,n.bind(t))}var y={childNodes:function(e){return f("Node",e,"childNodes")},parentNode:function(e){return f("Node",e,"parentNode")},parentElement:function(e){return f("Node",e,"parentElement")},textContent:function(e){return f("Node",e,"textContent")},contains:function(e,t){return m("Node",e,"contains")(t)},getRootNode:function(e){return m("Node",e,"getRootNode")()},host:function(e){return e&&"host"in e?f("ShadowRoot",e,"host"):null},styleSheets:function(e){return e.styleSheets},shadowRoot:function(e){return e&&"shadowRoot"in e?f("Element",e,"shadowRoot"):null},querySelector:function(e,t){return f("Element",e,"querySelector")(t)},querySelectorAll:function(e,t){return f("Element",e,"querySelectorAll")(t)},mutationObserver:function(){return _("MutationObserver").constructor}};function b(e){return e.nodeType===e.ELEMENT_NODE}function S(e){var t=e&&"host"in e&&"mode"in e&&y.host(e)||null;return Boolean(t&&"shadowRoot"in t&&y.shadowRoot(t)===e)}function w(e){return"[object ShadowRoot]"===Object.prototype.toString.call(e)}function C(e){try{var t=e.rules||e.cssRules;if(!t)return null;var r=Array.from(t,(t=>I(t,e.href))).join("");return(i=r).includes(" background-clip: text;")&&!i.includes(" -webkit-background-clip: text;")&&(i=i.replace(/\sbackground-clip:\s*text;/g," -webkit-background-clip: text; background-clip: text;")),i}catch(e){return null}var i}function I(e,t){if(function(e){return"styleSheet"in e}(e)){var r;try{r=C(e.styleSheet)||function(e){var{cssText:t}=e;if(t.split('"').length<3)return t;var r=["@import","url("+JSON.stringify(e.href)+")"];return""===e.layerName?r.push("layer"):e.layerName&&r.push("layer("+e.layerName+")"),e.supportsText&&r.push("supports("+e.supportsText+")"),e.media.length&&r.push(e.media.mediaText),r.join(" ")+";"}(e)}catch(t){r=e.cssText}return e.styleSheet.href?L(r,e.styleSheet.href):r}var i,n=e.cssText;return function(e){return"selectorText"in e}(e)&&e.selectorText.includes(":")&&(i=/(\[(?:[\w-]+)[^\\])(:(?:[\w-]+)\])/gm,n=n.replace(i,"$1\\$2")),t?L(n,t):n}function k(e,t){return Array.from(e.styleSheets).find((e=>e.href===t))}let E=class{constructor(){u(this,"idNodeMap",new Map),u(this,"nodeMetaMap",new WeakMap)}getId(e){var t;if(!e)return-1;var r=null==(t=this.getMeta(e))?void 0:t.id;return null!=r?r:-1}getNode(e){return this.idNodeMap.get(e)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(e){return this.nodeMetaMap.get(e)||null}removeNodeFromMap(e){var t=this.getId(e);this.idNodeMap.delete(t),e.childNodes&&e.childNodes.forEach((e=>this.removeNodeFromMap(e)))}has(e){return this.idNodeMap.has(e)}hasNode(e){return this.nodeMetaMap.has(e)}add(e,t){var r=t.id;this.idNodeMap.set(r,e),this.nodeMetaMap.set(e,t)}replace(e,t){var r=this.getNode(e);if(r){var i=this.nodeMetaMap.get(r);i&&this.nodeMetaMap.set(t,i)}this.idNodeMap.set(e,t)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}};function x(e){var{element:t,maskInputOptions:r,tagName:i,type:n,value:s,maskInputFn:o}=e,a=s||"",l=n&&T(n);return(r[i.toLowerCase()]||l&&r[l])&&(a=o?o(a,t):"*".repeat(a.length)),a}function T(e){return e.toLowerCase()}var M="__rrweb_original__";function R(e){var t=e.type;return e.hasAttribute("data-rr-is-password")?"password":t?T(t):null}function A(e,t){var r,i;try{i=new URL(e,null!=t?t:window.location.href)}catch(e){return null}var n=i.pathname.match(/\.([0-9a-z]+)(?:$)/i);return null!==(r=null==n?void 0:n[1])&&void 0!==r?r:null}var F=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,N=/^(?:[a-z+]+:)?\/\//i,P=/^www\..*/i,O=/^(data:)([^,]*),(.*)/i;function L(e,t){return(e||"").replace(F,((e,r,i,n,s,o)=>{var a,l=i||s||o,u=r||n||"";if(!l)return e;if(N.test(l)||P.test(l))return"url("+u+l+u+")";if(O.test(l))return"url("+u+l+u+")";if("/"===l[0])return"url("+u+(((a=t).indexOf("//")>-1?a.split("/").slice(0,3).join("/"):a.split("/")[0]).split("?")[0]+l)+u+")";var c=t.split("/"),d=l.split("/");for(var h of(c.pop(),d))"."!==h&&(".."===h?c.pop():c.push(h));return"url("+u+c.join("/")+u+")"}))}var D,B,q=1,H=new RegExp("[^a-z0-9-_:]"),$=-2;function V(){return q++}var W=/^[^ \t\n\r\u000c]+/,U=/^[, \t\n\r\u000c]+/;var Z=new WeakMap;function z(e,t){return t&&""!==t.trim()?j(e,t):t}function G(e){return Boolean("svg"===e.tagName||e.ownerSVGElement)}function j(e,t){var r=Z.get(e);if(r||(r=e.createElement("a"),Z.set(e,r)),t){if(t.startsWith("blob:")||t.startsWith("data:"))return t}else t="";return r.setAttribute("href",t),r.href}function Y(e,t,r,i){return i?"src"===r||"href"===r&&("use"!==t||"#"!==i[0])||"xlink:href"===r&&"#"!==i[0]?z(e,i):"background"!==r||"table"!==t&&"td"!==t&&"th"!==t?"srcset"===r?function(e,t){if(""===t.trim())return t;var r=0;function i(e){var i,n=e.exec(t.substring(r));return n?(i=n[0],r+=i.length,i):""}for(var n=[];i(U),!(r>=t.length);){var s=i(W);if(","===s.slice(-1))s=z(e,s.substring(0,s.length-1)),n.push(s);else{var o="";s=z(e,s);for(var a=!1;;){var l=t.charAt(r);if(""===l){n.push((s+o).trim());break}if(a)")"===l&&(a=!1);else{if(","===l){r+=1,n.push((s+o).trim());break}"("===l&&(a=!0)}o+=l,r+=1}}}return n.join(", ")}(e,i):"style"===r?L(i,j(e)):"object"===t&&"data"===r?z(e,i):i:z(e,i):i}function X(e,t,r){return("video"===e||"audio"===e)&&"autoplay"===t}function J(e,t,r){if(!e)return!1;if(e.nodeType!==e.ELEMENT_NODE)return!!r&&J(y.parentNode(e),t,r);for(var i=e.classList.length;i--;){var n=e.classList[i];if(t.test(n))return!0}return!!r&&J(y.parentNode(e),t,r)}function K(e,t,r,i){var n;if(b(e)){if(n=e,!y.childNodes(n).length)return!1}else{if(null===y.parentElement(e))return!1;n=y.parentElement(e)}try{if("string"==typeof t){if(i){if(n.closest("."+t))return!0}else if(n.classList.contains(t))return!0}else if(J(n,t,i))return!0;if(r)if(i){if(n.closest(r))return!0}else if(n.matches(r))return!0}catch(e){}return!1}function Q(e,t){var{doc:r,mirror:i,blockClass:n,blockSelector:s,needsMask:o,inlineStylesheet:a,maskInputOptions:l={},maskTextFn:u,maskInputFn:d,dataURLOptions:h={},inlineImages:p,recordCanvas:_,keepIframeSrcFn:g,newlyAddedElement:f=!1}=t,v=function(e,t){if(!t.hasNode(e))return;var r=t.getId(e);return 1===r?void 0:r}(r,i);switch(e.nodeType){case e.DOCUMENT_NODE:return"CSS1Compat"!==e.compatMode?{type:c.Document,childNodes:[],compatMode:e.compatMode}:{type:c.Document,childNodes:[]};case e.DOCUMENT_TYPE_NODE:return{type:c.DocumentType,name:e.name,publicId:e.publicId,systemId:e.systemId,rootId:v};case e.ELEMENT_NODE:return function(e,t){for(var r,{doc:i,blockClass:n,blockSelector:s,inlineStylesheet:o,maskInputOptions:a={},maskInputFn:l,dataURLOptions:u={},inlineImages:d,recordCanvas:h,keepIframeSrcFn:p,newlyAddedElement:_=!1,rootId:g}=t,f=function(e,t,r){try{if("string"==typeof t){if(e.classList.contains(t))return!0}else for(var i=e.classList.length;i--;){var n=e.classList[i];if(t.test(n))return!0}if(r)return e.matches(r)}catch(e){}return!1}(e,n,s),v=function(e){if(e instanceof HTMLFormElement)return"form";var t=T(e.tagName);return H.test(t)?"div":t}(e),m={},b=e.attributes.length,S=0;S<b;S++){var w=e.attributes[S];X(v,w.name,w.value)||(m[w.name]=Y(i,v,T(w.name),w.value))}if("link"===v&&o){var I=function(e){return null==e?void 0:e.href}(e);if(I){var E=k(i,I);if(!E&&I.includes(".css"))E=k(i,window.location.origin+"/"+I.replace(window.location.href,""));var A=null;E&&(A=C(E)),A&&(delete m.rel,delete m.href,m._cssText=A)}}if("style"===v&&e.sheet&&!(e.innerText||y.textContent(e)||"").trim().length){var F=C(e.sheet);F&&(m._cssText=F)}if("input"===v||"textarea"===v||"select"===v){var N=e.value,P=e.checked;"radio"!==m.type&&"checkbox"!==m.type&&"submit"!==m.type&&"button"!==m.type&&N?m.value=x({element:e,type:R(e),tagName:v,value:N,maskInputOptions:a,maskInputFn:l}):P&&(m.checked=P)}"option"===v&&(e.selected&&!a.select?m.selected=!0:delete m.selected);if("dialog"===v&&e.open)try{m.rr_open_mode=e.matches("dialog:modal")?"modal":"non-modal"}catch(e){m.rr_open_mode="modal",m.ph_rr_could_not_detect_modal=!0}if("canvas"===v&&h)if("2d"===e.__context)(function(e){var t=e.getContext("2d");if(!t)return!0;for(var r=0;r<e.width;r+=50)for(var i=0;i<e.height;i+=50){var n=t.getImageData,s=M in n?n[M]:n;if(new Uint32Array(s.call(t,r,i,Math.min(50,e.width-r),Math.min(50,e.height-i)).data.buffer).some((e=>0!==e)))return!1}return!0})(e)||(m.rr_dataURL=e.toDataURL(u.type,u.quality));else if(!("__context"in e)){var O=e.toDataURL(u.type,u.quality),L=i.createElement("canvas");L.width=e.width,L.height=e.height,O!==L.toDataURL(u.type,u.quality)&&(m.rr_dataURL=O)}if("img"===v&&d){D||(D=i.createElement("canvas"),B=D.getContext("2d"));var q=e,$=q.currentSrc||q.getAttribute("src")||"<unknown-src>",V=q.crossOrigin,W=()=>{q.removeEventListener("load",W);try{D.width=q.naturalWidth,D.height=q.naturalHeight,B.drawImage(q,0,0),m.rr_dataURL=D.toDataURL(u.type,u.quality)}catch(e){if("anonymous"!==q.crossOrigin)return q.crossOrigin="anonymous",void(q.complete&&0!==q.naturalWidth?W():q.addEventListener("load",W));console.warn("Cannot inline img src="+$+"! Error: "+e)}"anonymous"===q.crossOrigin&&(V?m.crossOrigin=V:q.removeAttribute("crossorigin"))};q.complete&&0!==q.naturalWidth?W():q.addEventListener("load",W)}if("audio"===v||"video"===v){var U=m;U.rr_mediaState=e.paused?"paused":"played",U.rr_mediaCurrentTime=e.currentTime,U.rr_mediaPlaybackRate=e.playbackRate,U.rr_mediaMuted=e.muted,U.rr_mediaLoop=e.loop,U.rr_mediaVolume=e.volume}_||(e.scrollLeft&&(m.rr_scrollLeft=e.scrollLeft),e.scrollTop&&(m.rr_scrollTop=e.scrollTop));if(f){var{width:Z,height:z}=e.getBoundingClientRect();m={class:m.class,rr_width:Z+"px",rr_height:z+"px"}}"iframe"!==v||p(m.src)||(e.contentDocument||(m.rr_src=m.src),delete m.src);try{customElements.get(v)&&(r=!0)}catch(e){}return{type:c.Element,tagName:v,attributes:m,childNodes:[],isSVG:G(e)||void 0,needBlock:f,rootId:g,isCustom:r}}(e,{doc:r,blockClass:n,blockSelector:s,inlineStylesheet:a,maskInputOptions:l,maskInputFn:d,dataURLOptions:h,inlineImages:p,recordCanvas:_,keepIframeSrcFn:g,newlyAddedElement:f,rootId:v});case e.TEXT_NODE:return function(e,t){var r,{needsMask:i,maskTextFn:n,rootId:s}=t,o=y.parentNode(e),a=o&&o.tagName,l=y.textContent(e),u="STYLE"===a||void 0,d="SCRIPT"===a||void 0;if(u&&l){try{e.nextSibling||e.previousSibling||(null==(r=o.sheet)?void 0:r.cssRules)&&(l=C(o.sheet))}catch(t){console.warn("Cannot get CSS styles from text's parentNode. Error: "+t,e)}l=L(l,j(t.doc))}d&&(l="SCRIPT_PLACEHOLDER");!u&&!d&&l&&i&&(l=n?n(l,y.parentElement(e)):l.replace(/[\S]/g,"*"));return{type:c.Text,textContent:l||"",isStyle:u,rootId:s}}(e,{doc:r,needsMask:o,maskTextFn:u,rootId:v});case e.CDATA_SECTION_NODE:return{type:c.CDATA,textContent:"",rootId:v};case e.COMMENT_NODE:return{type:c.Comment,textContent:y.textContent(e)||"",rootId:v};default:return!1}}function ee(e){return null==e?"":e.toLowerCase()}function te(e,t){var{doc:r,mirror:i,blockClass:n,blockSelector:s,maskTextClass:o,maskTextSelector:a,skipChild:l=!1,inlineStylesheet:u=!0,maskInputOptions:d={},maskTextFn:h,maskInputFn:p,slimDOMOptions:_,dataURLOptions:g={},inlineImages:f=!1,recordCanvas:v=!1,onSerialize:m,onIframeLoad:C,iframeLoadTimeout:I=5e3,onStylesheetLoad:k,stylesheetLoadTimeout:E=5e3,keepIframeSrcFn:x=(()=>!1),newlyAddedElement:T=!1}=t,{needsMask:M}=t,{preserveWhiteSpace:R=!0}=t;M||(M=K(e,o,a,void 0===M));var F,N=Q(e,{doc:r,mirror:i,blockClass:n,blockSelector:s,needsMask:M,inlineStylesheet:u,maskInputOptions:d,maskTextFn:h,maskInputFn:p,dataURLOptions:g,inlineImages:f,recordCanvas:v,keepIframeSrcFn:x,newlyAddedElement:T});if(!N)return console.warn(e,"not serialized"),null;F=i.hasNode(e)?i.getId(e):!function(e,t){if(t.comment&&e.type===c.Comment)return!0;if(e.type===c.Element){if(t.script&&("script"===e.tagName||"link"===e.tagName&&("preload"===e.attributes.rel||"modulepreload"===e.attributes.rel)&&"script"===e.attributes.as||"link"===e.tagName&&"prefetch"===e.attributes.rel&&"string"==typeof e.attributes.href&&"js"===A(e.attributes.href)))return!0;if(t.headFavicon&&("link"===e.tagName&&"shortcut icon"===e.attributes.rel||"meta"===e.tagName&&(ee(e.attributes.name).match(/^msapplication-tile(image|color)$/)||"application-name"===ee(e.attributes.name)||"icon"===ee(e.attributes.rel)||"apple-touch-icon"===ee(e.attributes.rel)||"shortcut icon"===ee(e.attributes.rel))))return!0;if("meta"===e.tagName){if(t.headMetaDescKeywords&&ee(e.attributes.name).match(/^description|keywords$/))return!0;if(t.headMetaSocial&&(ee(e.attributes.property).match(/^(og|twitter|fb):/)||ee(e.attributes.name).match(/^(og|twitter):/)||"pinterest"===ee(e.attributes.name)))return!0;if(t.headMetaRobots&&("robots"===ee(e.attributes.name)||"googlebot"===ee(e.attributes.name)||"bingbot"===ee(e.attributes.name)))return!0;if(t.headMetaHttpEquiv&&void 0!==e.attributes["http-equiv"])return!0;if(t.headMetaAuthorship&&("author"===ee(e.attributes.name)||"generator"===ee(e.attributes.name)||"framework"===ee(e.attributes.name)||"publisher"===ee(e.attributes.name)||"progid"===ee(e.attributes.name)||ee(e.attributes.property).match(/^article:/)||ee(e.attributes.property).match(/^product:/)))return!0;if(t.headMetaVerification&&("google-site-verification"===ee(e.attributes.name)||"yandex-verification"===ee(e.attributes.name)||"csrf-token"===ee(e.attributes.name)||"p:domain_verify"===ee(e.attributes.name)||"verify-v1"===ee(e.attributes.name)||"verification"===ee(e.attributes.name)||"shopify-checkout-api-token"===ee(e.attributes.name)))return!0}}return!1}(N,_)&&(R||N.type!==c.Text||N.isStyle||N.textContent.replace(/^\s+|\s+$/gm,"").length)?V():$;var P=Object.assign(N,{id:F});if(i.add(e,P),F===$)return null;m&&m(e);var O=!l;if(P.type===c.Element){O=O&&!P.needBlock,delete P.needBlock;var L=y.shadowRoot(e);L&&w(L)&&(P.isShadowHost=!0)}if((P.type===c.Document||P.type===c.Element)&&O){_.headWhitespace&&P.type===c.Element&&"head"===P.tagName&&(R=!1);var D={doc:r,mirror:i,blockClass:n,blockSelector:s,needsMask:M,maskTextClass:o,maskTextSelector:a,skipChild:l,inlineStylesheet:u,maskInputOptions:d,maskTextFn:h,maskInputFn:p,slimDOMOptions:_,dataURLOptions:g,inlineImages:f,recordCanvas:v,preserveWhiteSpace:R,onSerialize:m,onIframeLoad:C,iframeLoadTimeout:I,onStylesheetLoad:k,stylesheetLoadTimeout:E,keepIframeSrcFn:x};if(P.type===c.Element&&"textarea"===P.tagName&&void 0!==P.attributes.value);else for(var B of Array.from(y.childNodes(e))){var q=te(B,D);q&&P.childNodes.push(q)}var H=null;if(b(e)&&(H=y.shadowRoot(e)))for(var W of Array.from(y.childNodes(H))){var U=te(W,D);U&&(w(H)&&(U.isShadow=!0),P.childNodes.push(U))}}var Z=y.parentNode(e);return Z&&S(Z)&&w(Z)&&(P.isShadow=!0),P.type===c.Element&&"iframe"===P.tagName&&function(e,t,r){var i=e.contentWindow;if(i){var n,s=!1;try{n=i.document.readyState}catch(e){return}if("complete"===n){var o="about:blank";if(i.location.href!==o||e.src===o||""===e.src)return setTimeout(t,0),e.addEventListener("load",t);e.addEventListener("load",t)}else{var a=setTimeout((()=>{s||(t(),s=!0)}),r);e.addEventListener("load",(()=>{clearTimeout(a),s=!0,t()}))}}}(e,(()=>{var t=e.contentDocument;if(t&&C){var r=te(t,{doc:t,mirror:i,blockClass:n,blockSelector:s,needsMask:M,maskTextClass:o,maskTextSelector:a,skipChild:!1,inlineStylesheet:u,maskInputOptions:d,maskTextFn:h,maskInputFn:p,slimDOMOptions:_,dataURLOptions:g,inlineImages:f,recordCanvas:v,preserveWhiteSpace:R,onSerialize:m,onIframeLoad:C,iframeLoadTimeout:I,onStylesheetLoad:k,stylesheetLoadTimeout:E,keepIframeSrcFn:x});r&&C(e,r)}}),I),P.type===c.Element&&"link"===P.tagName&&"string"==typeof P.attributes.rel&&("stylesheet"===P.attributes.rel||"preload"===P.attributes.rel&&"string"==typeof P.attributes.href&&"css"===A(P.attributes.href))&&function(e,t,r){var i,n=!1;try{i=e.sheet}catch(e){return}if(!i){var s=setTimeout((()=>{n||(t(),n=!0)}),r);e.addEventListener("load",(()=>{clearTimeout(s),n=!0,t()}))}}(e,(()=>{if(k){var t=te(e,{doc:r,mirror:i,blockClass:n,blockSelector:s,needsMask:M,maskTextClass:o,maskTextSelector:a,skipChild:!1,inlineStylesheet:u,maskInputOptions:d,maskTextFn:h,maskInputFn:p,slimDOMOptions:_,dataURLOptions:g,inlineImages:f,recordCanvas:v,preserveWhiteSpace:R,onSerialize:m,onIframeLoad:C,iframeLoadTimeout:I,onStylesheetLoad:k,stylesheetLoadTimeout:E,keepIframeSrcFn:x});t&&k(e,t)}}),E),P}let re=class e{constructor(){__publicField2(this,"parentElement",null),__publicField2(this,"parentNode",null),__publicField2(this,"ownerDocument"),__publicField2(this,"firstChild",null),__publicField2(this,"lastChild",null),__publicField2(this,"previousSibling",null),__publicField2(this,"nextSibling",null),__publicField2(this,"ELEMENT_NODE",1),__publicField2(this,"TEXT_NODE",3),__publicField2(this,"nodeType"),__publicField2(this,"nodeName"),__publicField2(this,"RRNodeType")}get childNodes(){for(var e=[],t=this.firstChild;t;)e.push(t),t=t.nextSibling;return e}contains(t){if(!(t instanceof e))return!1;if(t.ownerDocument!==this.ownerDocument)return!1;if(t===this)return!0;for(;t.parentNode;){if(t.parentNode===this)return!0;t=t.parentNode}return!1}appendChild(e){throw new Error("RRDomException: Failed to execute 'appendChild' on 'RRNode': This RRNode type does not support this method.")}insertBefore(e,t){throw new Error("RRDomException: Failed to execute 'insertBefore' on 'RRNode': This RRNode type does not support this method.")}removeChild(e){throw new Error("RRDomException: Failed to execute 'removeChild' on 'RRNode': This RRNode type does not support this method.")}toString(){return"RRNode"}};var ie={Node:["childNodes","parentNode","parentElement","textContent"],ShadowRoot:["host","styleSheets"],Element:["shadowRoot","querySelector","querySelectorAll"],MutationObserver:[]},ne={Node:["contains","getRootNode"],ShadowRoot:["getSelection"],Element:[],MutationObserver:["constructor"]};var se={};function oe(e){if(se[e])return se[e];var t=function(e){var t,r=null==globalThis||null==(t=globalThis.Zone)||null==t.__symbol__?void 0:t.__symbol__(e);return r&&globalThis[r]?globalThis[r]:void 0}(e)||globalThis[e],r=t.prototype,i=e in ie?ie[e]:void 0,n=Boolean(i&&i.every((e=>{var t,i;return Boolean(null==(i=null==(t=Object.getOwnPropertyDescriptor(r,e))?void 0:t.get)?void 0:i.toString().includes("[native code]"))}))),s=e in ne?ne[e]:void 0,o=Boolean(s&&s.every((e=>{var t;return"function"==typeof r[e]&&(null==(t=r[e])?void 0:t.toString().includes("[native code]"))})));if(n&&o)return se[e]=t.prototype,t.prototype;try{var a=document.createElement("iframe");document.body.appendChild(a);var l=a.contentWindow;if(!l)return t.prototype;var u=l[e].prototype;return document.body.removeChild(a),u?se[e]=u:r}catch(e){return r}}var ae={};function le(e,t,r){var i,n=e+"."+String(r);if(ae[n])return ae[n].call(t);var s=oe(e),o=null==(i=Object.getOwnPropertyDescriptor(s,r))?void 0:i.get;return o?(ae[n]=o,o.call(t)):t[r]}var ue={};function ce(e,t,r){var i=e+"."+String(r);if(ue[i])return ue[i].bind(t);var n=oe(e)[r];return"function"!=typeof n?t[r]:(ue[i]=n,n.bind(t))}function de(){return oe("MutationObserver").constructor}var he={childNodes:function(e){return le("Node",e,"childNodes")},parentNode:function(e){return le("Node",e,"parentNode")},parentElement:function(e){return le("Node",e,"parentElement")},textContent:function(e){return le("Node",e,"textContent")},contains:function(e,t){return ce("Node",e,"contains")(t)},getRootNode:function(e){return ce("Node",e,"getRootNode")()},host:function(e){return e&&"host"in e?le("ShadowRoot",e,"host"):null},styleSheets:function(e){return e.styleSheets},shadowRoot:function(e){return e&&"shadowRoot"in e?le("Element",e,"shadowRoot"):null},querySelector:function(e,t){return le("Element",e,"querySelector")(t)},querySelectorAll:function(e,t){return le("Element",e,"querySelectorAll")(t)},mutationObserver:de};function pe(e,t,r){void 0===r&&(r=document);var i={capture:!0,passive:!0};return r.addEventListener(e,t,i),()=>r.removeEventListener(e,t,i)}var _e="Please stop import mirror directly. Instead of that,\r\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\r\nor you can use record.mirror to access the mirror instance during recording.",ge={map:{},getId:()=>(console.error(_e),-1),getNode:()=>(console.error(_e),null),removeNodeFromMap(){console.error(_e)},has:()=>(console.error(_e),!1),reset(){console.error(_e)}};function fe(e,t,r){void 0===r&&(r={});var i=null,n=0;return function(){for(var s=arguments.length,o=new Array(s),a=0;a<s;a++)o[a]=arguments[a];var l=Date.now();n||!1!==r.leading||(n=l);var u=t-(l-n),c=this;u<=0||u>t?(i&&(clearTimeout(i),i=null),n=l,e.apply(c,o)):i||!1===r.trailing||(i=setTimeout((()=>{n=!1===r.leading?0:Date.now(),i=null,e.apply(c,o)}),u))}}function ve(e,t,r,i,n){void 0===n&&(n=window);var s=n.Object.getOwnPropertyDescriptor(e,t);return n.Object.defineProperty(e,t,i?r:{set(e){setTimeout((()=>{r.set.call(this,e)}),0),s&&s.set&&s.set.call(this,e)}}),()=>ve(e,t,s||{},!0)}function me(e,t,r){try{if(!(t in e))return()=>{};var i=e[t],n=r(i);return"function"==typeof n&&(n.prototype=n.prototype||{},Object.defineProperties(n,{__rrweb_original__:{enumerable:!1,value:i}})),e[t]=n,()=>{e[t]=i}}catch(e){return()=>{}}}"undefined"!=typeof window&&window.Proxy&&window.Reflect&&(ge=new Proxy(ge,{get:(e,t,r)=>("map"===t&&console.error(_e),Reflect.get(e,t,r))}));var ye=Date.now;function be(e){var t,r,i,n,s=e.document;return{left:s.scrollingElement?s.scrollingElement.scrollLeft:void 0!==e.pageXOffset?e.pageXOffset:s.documentElement.scrollLeft||(null==s?void 0:s.body)&&(null==(t=he.parentElement(s.body))?void 0:t.scrollLeft)||(null==(r=null==s?void 0:s.body)?void 0:r.scrollLeft)||0,top:s.scrollingElement?s.scrollingElement.scrollTop:void 0!==e.pageYOffset?e.pageYOffset:(null==s?void 0:s.documentElement.scrollTop)||(null==s?void 0:s.body)&&(null==(i=he.parentElement(s.body))?void 0:i.scrollTop)||(null==(n=null==s?void 0:s.body)?void 0:n.scrollTop)||0}}function Se(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight}function we(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth}function Ce(e){return e?e.nodeType===e.ELEMENT_NODE?e:he.parentElement(e):null}function Ie(e,t,r,i){if(!e)return!1;var n=Ce(e);if(!n)return!1;try{if("string"==typeof t){if(n.classList.contains(t))return!0;if(i&&null!==n.closest("."+t))return!0}else if(J(n,t,i))return!0}catch(e){}if(r){if(n.matches(r))return!0;if(i&&null!==n.closest(r))return!0}return!1}function ke(e,t,r){return!("TITLE"!==e.tagName||!r.headTitleMutations)||t.getId(e)===$}function Ee(e,t){if(S(e))return!1;var r=t.getId(e);if(!t.has(r))return!0;var i=he.parentNode(e);return(!i||i.nodeType!==e.DOCUMENT_NODE)&&(!i||Ee(i,t))}function xe(e){return Boolean(e.changedTouches)}function Te(e,t){return Boolean("IFRAME"===e.nodeName&&t.getMeta(e))}function Me(e,t){return Boolean("LINK"===e.nodeName&&e.nodeType===e.ELEMENT_NODE&&e.getAttribute&&"stylesheet"===e.getAttribute("rel")&&t.getMeta(e))}function Re(e){return!!e&&(e instanceof re&&"shadowRoot"in e?Boolean(e.shadowRoot):Boolean(he.shadowRoot(e)))}/[1-9][0-9]{12}/.test(Date.now().toString())||(ye=()=>(new Date).getTime());let Ae=class{constructor(){a(this,"id",1),a(this,"styleIDMap",new WeakMap),a(this,"idStyleMap",new Map)}getId(e){var t;return null!==(t=this.styleIDMap.get(e))&&void 0!==t?t:-1}has(e){return this.styleIDMap.has(e)}add(e,t){return this.has(e)?this.getId(e):(r=void 0===t?this.id++:t,this.styleIDMap.set(e,r),this.idStyleMap.set(r,e),r);var r}getStyle(e){return this.idStyleMap.get(e)||null}reset(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}generateId(){return this.id++}};function Fe(e){var t,r=null;return"getRootNode"in e&&(null==(t=he.getRootNode(e))?void 0:t.nodeType)===Node.DOCUMENT_FRAGMENT_NODE&&he.host(he.getRootNode(e))&&(r=he.host(he.getRootNode(e))),r}function Ne(e){var t=e.ownerDocument;if(!t)return!1;var r=function(e){for(var t,r=e;t=Fe(r);)r=t;return r}(e);return he.contains(t,r)}function Pe(e){var t=e.ownerDocument;return!!t&&(he.contains(t,e)||Ne(e))}var Oe=(e=>(e[e.DomContentLoaded=0]="DomContentLoaded",e[e.Load=1]="Load",e[e.FullSnapshot=2]="FullSnapshot",e[e.IncrementalSnapshot=3]="IncrementalSnapshot",e[e.Meta=4]="Meta",e[e.Custom=5]="Custom",e[e.Plugin=6]="Plugin",e))(Oe||{}),Le=(e=>(e[e.Mutation=0]="Mutation",e[e.MouseMove=1]="MouseMove",e[e.MouseInteraction=2]="MouseInteraction",e[e.Scroll=3]="Scroll",e[e.ViewportResize=4]="ViewportResize",e[e.Input=5]="Input",e[e.TouchMove=6]="TouchMove",e[e.MediaInteraction=7]="MediaInteraction",e[e.StyleSheetRule=8]="StyleSheetRule",e[e.CanvasMutation=9]="CanvasMutation",e[e.Font=10]="Font",e[e.Log=11]="Log",e[e.Drag=12]="Drag",e[e.StyleDeclaration=13]="StyleDeclaration",e[e.Selection=14]="Selection",e[e.AdoptedStyleSheet=15]="AdoptedStyleSheet",e[e.CustomElement=16]="CustomElement",e))(Le||{}),De=(e=>(e[e.MouseUp=0]="MouseUp",e[e.MouseDown=1]="MouseDown",e[e.Click=2]="Click",e[e.ContextMenu=3]="ContextMenu",e[e.DblClick=4]="DblClick",e[e.Focus=5]="Focus",e[e.Blur=6]="Blur",e[e.TouchStart=7]="TouchStart",e[e.TouchMove_Departed=8]="TouchMove_Departed",e[e.TouchEnd=9]="TouchEnd",e[e.TouchCancel=10]="TouchCancel",e))(De||{}),Be=(e=>(e[e.Mouse=0]="Mouse",e[e.Pen=1]="Pen",e[e.Touch=2]="Touch",e))(Be||{}),qe=(e=>(e[e["2D"]=0]="2D",e[e.WebGL=1]="WebGL",e[e.WebGL2=2]="WebGL2",e))(qe||{}),He=(e=>(e[e.Play=0]="Play",e[e.Pause=1]="Pause",e[e.Seeked=2]="Seeked",e[e.VolumeChange=3]="VolumeChange",e[e.RateChange=4]="RateChange",e))(He||{});function $e(e){return"__ln"in e}class Ve{constructor(){a(this,"length",0),a(this,"head",null),a(this,"tail",null)}get(e){if(e>=this.length)throw new Error("Position outside of list range");for(var t=this.head,r=0;r<e;r++)t=(null==t?void 0:t.next)||null;return t}addNode(e){var t={value:e,previous:null,next:null};if(e.__ln=t,e.previousSibling&&$e(e.previousSibling)){var r=e.previousSibling.__ln.next;t.next=r,t.previous=e.previousSibling.__ln,e.previousSibling.__ln.next=t,r&&(r.previous=t)}else if(e.nextSibling&&$e(e.nextSibling)&&e.nextSibling.__ln.previous){var i=e.nextSibling.__ln.previous;t.previous=i,t.next=e.nextSibling.__ln,e.nextSibling.__ln.previous=t,i&&(i.next=t)}else this.head&&(this.head.previous=t),t.next=this.head,this.head=t;null===t.next&&(this.tail=t),this.length++}removeNode(e){var t=e.__ln;this.head&&(t.previous?(t.previous.next=t.next,t.next?t.next.previous=t.previous:this.tail=t.previous):(this.head=t.next,this.head?this.head.previous=null:this.tail=null),e.__ln&&delete e.__ln,this.length--)}}var We,Ue=(e,t)=>e+"@"+t;class Ze{constructor(){a(this,"frozen",!1),a(this,"locked",!1),a(this,"texts",[]),a(this,"attributes",[]),a(this,"attributeMap",new WeakMap),a(this,"removes",[]),a(this,"mapRemoves",[]),a(this,"movedMap",{}),a(this,"addedSet",new Set),a(this,"movedSet",new Set),a(this,"droppedSet",new Set),a(this,"mutationCb"),a(this,"blockClass"),a(this,"blockSelector"),a(this,"maskTextClass"),a(this,"maskTextSelector"),a(this,"inlineStylesheet"),a(this,"maskInputOptions"),a(this,"maskTextFn"),a(this,"maskInputFn"),a(this,"keepIframeSrcFn"),a(this,"recordCanvas"),a(this,"inlineImages"),a(this,"slimDOMOptions"),a(this,"dataURLOptions"),a(this,"doc"),a(this,"mirror"),a(this,"iframeManager"),a(this,"stylesheetManager"),a(this,"shadowDomManager"),a(this,"canvasManager"),a(this,"processedNodeManager"),a(this,"unattachedDoc"),a(this,"processMutations",(e=>{e.forEach(this.processMutation),this.emit()})),a(this,"emit",(()=>{if(!this.frozen&&!this.locked){for(var e=[],t=new Set,r=new Ve,i=e=>{for(var t=e,r=$;r===$;)r=(t=t&&t.nextSibling)&&this.mirror.getId(t);return r},n=n=>{var s=he.parentNode(n);if(s&&Pe(n)&&"TEXTAREA"!==s.tagName){var o=S(s)?this.mirror.getId(Fe(n)):this.mirror.getId(s),a=i(n);if(-1===o||-1===a)return r.addNode(n);var l=te(n,{doc:this.doc,mirror:this.mirror,blockClass:this.blockClass,blockSelector:this.blockSelector,maskTextClass:this.maskTextClass,maskTextSelector:this.maskTextSelector,skipChild:!0,newlyAddedElement:!0,inlineStylesheet:this.inlineStylesheet,maskInputOptions:this.maskInputOptions,maskTextFn:this.maskTextFn,maskInputFn:this.maskInputFn,slimDOMOptions:this.slimDOMOptions,dataURLOptions:this.dataURLOptions,recordCanvas:this.recordCanvas,inlineImages:this.inlineImages,onSerialize:e=>{Te(e,this.mirror)&&this.iframeManager.addIframe(e),Me(e,this.mirror)&&this.stylesheetManager.trackLinkElement(e),Re(n)&&this.shadowDomManager.addShadowRoot(he.shadowRoot(n),this.doc)},onIframeLoad:(e,t)=>{this.iframeManager.attachIframe(e,t),this.shadowDomManager.observeAttachShadow(e)},onStylesheetLoad:(e,t)=>{this.stylesheetManager.attachLinkElement(e,t)}});l&&(e.push({parentId:o,nextId:a,node:l}),t.add(l.id))}};this.mapRemoves.length;)this.mirror.removeNodeFromMap(this.mapRemoves.shift());for(var s of this.movedSet)Ge(this.removes,s,this.mirror)&&!this.movedSet.has(he.parentNode(s))||n(s);for(var o of this.addedSet)je(this.droppedSet,o)||Ge(this.removes,o,this.mirror)?je(this.movedSet,o)?n(o):this.droppedSet.add(o):n(o);for(var a=null;r.length;){var l=null;if(a){var u=this.mirror.getId(he.parentNode(a.value)),c=i(a.value);-1!==u&&-1!==c&&(l=a)}if(!l)for(var d=r.tail;d;){var h=d;if(d=d.previous,h){var p=this.mirror.getId(he.parentNode(h.value));if(-1===i(h.value))continue;if(-1!==p){l=h;break}var _=h.value,g=he.parentNode(_);if(g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE){var f=he.host(g);if(-1!==this.mirror.getId(f)){l=h;break}}}}if(!l){for(;r.head;)r.removeNode(r.head.value);break}a=l.previous,r.removeNode(l.value),n(l.value)}var v={texts:this.texts.map((e=>{var t=e.node,r=he.parentNode(t);return r&&"TEXTAREA"===r.tagName&&this.genTextAreaValueMutation(r),{id:this.mirror.getId(t),value:e.value}})).filter((e=>!t.has(e.id))).filter((e=>this.mirror.has(e.id))),attributes:this.attributes.map((e=>{var{attributes:t}=e;if("string"==typeof t.style){var r=JSON.stringify(e.styleDiff),i=JSON.stringify(e._unchangedStyles);r.length<t.style.length&&(r+i).split("var(").length===t.style.split("var(").length&&(t.style=e.styleDiff)}return{id:this.mirror.getId(e.node),attributes:t}})).filter((e=>!t.has(e.id))).filter((e=>this.mirror.has(e.id))),removes:this.removes,adds:e};(v.texts.length||v.attributes.length||v.removes.length||v.adds.length)&&(this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.movedMap={},this.mutationCb(v))}})),a(this,"genTextAreaValueMutation",(e=>{var t=this.attributeMap.get(e);t||(t={node:e,attributes:{},styleDiff:{},_unchangedStyles:{}},this.attributes.push(t),this.attributeMap.set(e,t)),t.attributes.value=Array.from(he.childNodes(e),(e=>he.textContent(e)||"")).join("")})),a(this,"processMutation",(e=>{if(!ke(e.target,this.mirror,this.slimDOMOptions))switch(e.type){case"characterData":var t=he.textContent(e.target);Ie(e.target,this.blockClass,this.blockSelector,!1)||t===e.oldValue||this.texts.push({value:K(e.target,this.maskTextClass,this.maskTextSelector,!0)&&t?this.maskTextFn?this.maskTextFn(t,Ce(e.target)):t.replace(/[\S]/g,"*"):t,node:e.target});break;case"attributes":var r=e.target,i=e.attributeName,n=e.target.getAttribute(i);if("value"===i){var s=R(r);n=x({element:r,maskInputOptions:this.maskInputOptions,tagName:r.tagName,type:s,value:n,maskInputFn:this.maskInputFn})}if(Ie(e.target,this.blockClass,this.blockSelector,!1)||n===e.oldValue)return;var o=this.attributeMap.get(e.target);if("IFRAME"===r.tagName&&"src"===i&&!this.keepIframeSrcFn(n)){if(r.contentDocument)return;i="rr_src"}if(o||(o={node:e.target,attributes:{},styleDiff:{},_unchangedStyles:{}},this.attributes.push(o),this.attributeMap.set(e.target,o)),"type"===i&&"INPUT"===r.tagName&&"password"===(e.oldValue||"").toLowerCase()&&r.setAttribute("data-rr-is-password","true"),!X(r.tagName,i))if(o.attributes[i]=Y(this.doc,T(r.tagName),T(i),n),"style"===i){if(!this.unattachedDoc)try{this.unattachedDoc=document.implementation.createHTMLDocument()}catch(e){this.unattachedDoc=this.doc}var a=this.unattachedDoc.createElement("span");for(var l of(e.oldValue&&a.setAttribute("style",e.oldValue),Array.from(r.style))){var u=r.style.getPropertyValue(l),c=r.style.getPropertyPriority(l);u!==a.style.getPropertyValue(l)||c!==a.style.getPropertyPriority(l)?o.styleDiff[l]=""===c?u:[u,c]:o._unchangedStyles[l]=[u,c]}for(var d of Array.from(a.style))""===r.style.getPropertyValue(d)&&(o.styleDiff[d]=!1)}else"open"===i&&"DIALOG"===r.tagName&&(r.matches("dialog:modal")?o.attributes.rr_open_mode="modal":o.attributes.rr_open_mode="non-modal");break;case"childList":if(Ie(e.target,this.blockClass,this.blockSelector,!0))return;if("TEXTAREA"===e.target.tagName)return void this.genTextAreaValueMutation(e.target);e.addedNodes.forEach((t=>this.genAdds(t,e.target))),e.removedNodes.forEach((t=>{var r=this.mirror.getId(t),i=S(e.target)?this.mirror.getId(he.host(e.target)):this.mirror.getId(e.target);Ie(e.target,this.blockClass,this.blockSelector,!1)||ke(t,this.mirror,this.slimDOMOptions)||!function(e,t){return-1!==t.getId(e)}(t,this.mirror)||(this.addedSet.has(t)?(ze(this.addedSet,t),this.droppedSet.add(t)):this.addedSet.has(e.target)&&-1===r||Ee(e.target,this.mirror)||(this.movedSet.has(t)&&this.movedMap[Ue(r,i)]?ze(this.movedSet,t):this.removes.push({parentId:i,id:r,isShadow:!(!S(e.target)||!w(e.target))||void 0})),this.mapRemoves.push(t))}))}})),a(this,"genAdds",((e,t)=>{if(!this.processedNodeManager.inOtherBuffer(e,this)&&!this.addedSet.has(e)&&!this.movedSet.has(e)){if(this.mirror.hasNode(e)){if(ke(e,this.mirror,this.slimDOMOptions))return;this.movedSet.add(e);var r=null;t&&this.mirror.hasNode(t)&&(r=this.mirror.getId(t)),r&&-1!==r&&(this.movedMap[Ue(this.mirror.getId(e),r)]=!0)}else this.addedSet.add(e),this.droppedSet.delete(e);Ie(e,this.blockClass,this.blockSelector,!1)||(he.childNodes(e).forEach((e=>this.genAdds(e))),Re(e)&&he.childNodes(he.shadowRoot(e)).forEach((t=>{this.processedNodeManager.add(t,this),this.genAdds(t,e)})))}}))}init(e){["mutationCb","blockClass","blockSelector","maskTextClass","maskTextSelector","inlineStylesheet","maskInputOptions","maskTextFn","maskInputFn","keepIframeSrcFn","recordCanvas","inlineImages","slimDOMOptions","dataURLOptions","doc","mirror","iframeManager","stylesheetManager","shadowDomManager","canvasManager","processedNodeManager"].forEach((t=>{this[t]=e[t]}))}freeze(){this.frozen=!0,this.canvasManager.freeze()}unfreeze(){this.frozen=!1,this.canvasManager.unfreeze(),this.emit()}isFrozen(){return this.frozen}lock(){this.locked=!0,this.canvasManager.lock()}unlock(){this.locked=!1,this.canvasManager.unlock(),this.emit()}reset(){this.shadowDomManager.reset(),this.canvasManager.reset()}}function ze(e,t){e.delete(t),he.childNodes(t).forEach((t=>ze(e,t)))}function Ge(e,t,r){return 0!==e.length&&function(e,t,r){var i,n=he.parentNode(t),s=function(){var t=r.getId(n);if(e.some((e=>e.id===t)))return{v:!0};n=he.parentNode(n)};for(;n;)if(i=s())return i.v;return!1}(e,t,r)}function je(e,t){return 0!==e.size&&Ye(e,t)}function Ye(e,t){var r=he.parentNode(t);return!!r&&(!!e.has(r)||Ye(e,r))}var Xe=e=>{if(!We)return e;return function(){try{return e(...arguments)}catch(e){if(We&&!0===We(e))return;throw e}}},Je=[];function Ke(e){try{if("composedPath"in e){var t=e.composedPath();if(t.length)return t[0]}else if("path"in e&&e.path.length)return e.path[0]}catch(e){}return e&&e.target}function Qe(e,t){var r=new Ze;Je.push(r),r.init(e);var i=new(de())(Xe(r.processMutations.bind(r)));return i.observe(t,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}),i}function et(e){var{mouseInteractionCb:t,doc:i,mirror:n,blockClass:s,blockSelector:o,sampling:a}=e;if(!1===a.mouseInteraction)return()=>{};var l=!0===a.mouseInteraction||void 0===a.mouseInteraction?{}:a.mouseInteraction,u=[],c=null;return Object.keys(De).filter((e=>Number.isNaN(Number(e))&&!e.endsWith("_Departed")&&!1!==l[e])).forEach((e=>{var a=T(e),l=(e=>i=>{var a=Ke(i);if(!Ie(a,s,o,!0)){var l=null,u=e;if("pointerType"in i){switch(i.pointerType){case"mouse":l=Be.Mouse;break;case"touch":l=Be.Touch;break;case"pen":l=Be.Pen}l===Be.Touch?De[e]===De.MouseDown?u="TouchStart":De[e]===De.MouseUp&&(u="TouchEnd"):Be.Pen}else xe(i)&&(l=Be.Touch);null!==l?(c=l,(u.startsWith("Touch")&&l===Be.Touch||u.startsWith("Mouse")&&l===Be.Mouse)&&(l=null)):De[e]===De.Click&&(l=c,c=null);var d=xe(i)?i.changedTouches[0]:i;if(d){var h=n.getId(a),{clientX:p,clientY:_}=d;Xe(t)(r({type:De[u],id:h,x:p,y:_},null!==l&&{pointerType:l}))}}})(e);if(window.PointerEvent)switch(De[e]){case De.MouseDown:case De.MouseUp:a=a.replace("mouse","pointer");break;case De.TouchStart:case De.TouchEnd:return}u.push(pe(a,l,i))})),Xe((()=>{u.forEach((e=>e()))}))}function tt(e){var{scrollCb:t,doc:r,mirror:i,blockClass:n,blockSelector:s,sampling:o}=e;return pe("scroll",Xe(fe(Xe((e=>{var o=Ke(e);if(o&&!Ie(o,n,s,!0)){var a=i.getId(o);if(o===r&&r.defaultView){var l=be(r.defaultView);t({id:a,x:l.left,y:l.top})}else t({id:a,x:o.scrollLeft,y:o.scrollTop})}})),o.scroll||100)),r)}var rt=["INPUT","TEXTAREA","SELECT"],it=new WeakMap;function nt(e){return function(e,t){if(lt("CSSGroupingRule")&&e.parentRule instanceof CSSGroupingRule||lt("CSSMediaRule")&&e.parentRule instanceof CSSMediaRule||lt("CSSSupportsRule")&&e.parentRule instanceof CSSSupportsRule||lt("CSSConditionRule")&&e.parentRule instanceof CSSConditionRule){var r=Array.from(e.parentRule.cssRules).indexOf(e);t.unshift(r)}else if(e.parentStyleSheet){var i=Array.from(e.parentStyleSheet.cssRules).indexOf(e);t.unshift(i)}return t}(e,[])}function st(e,t,r){var i,n;return e?(e.ownerNode?i=t.getId(e.ownerNode):n=r.getId(e),{styleId:n,id:i}):{}}function ot(e,t){var r,i,n,{mirror:s,stylesheetManager:o}=e,a=null;a="#document"===t.nodeName?s.getId(t):s.getId(he.host(t));var l="#document"===t.nodeName?null==(r=t.defaultView)?void 0:r.Document:null==(n=null==(i=t.ownerDocument)?void 0:i.defaultView)?void 0:n.ShadowRoot,u=(null==l?void 0:l.prototype)?Object.getOwnPropertyDescriptor(null==l?void 0:l.prototype,"adoptedStyleSheets"):void 0;return null!==a&&-1!==a&&l&&u?(Object.defineProperty(t,"adoptedStyleSheets",{configurable:u.configurable,enumerable:u.enumerable,get(){var e;return null==(e=u.get)?void 0:e.call(this)},set(e){var t,r=null==(t=u.set)?void 0:t.call(this,e);if(null!==a&&-1!==a)try{o.adoptStyleSheets(e,a)}catch(e){}return r}}),Xe((()=>{Object.defineProperty(t,"adoptedStyleSheets",{configurable:u.configurable,enumerable:u.enumerable,get:u.get,set:u.set})}))):()=>{}}function at(e,t){void 0===t&&(t={});var i,n=e.doc.defaultView;if(!n)return()=>{};!function(e,t){var{mutationCb:r,mousemoveCb:i,mouseInteractionCb:n,scrollCb:s,viewportResizeCb:o,inputCb:a,mediaInteractionCb:l,styleSheetRuleCb:u,styleDeclarationCb:c,canvasMutationCb:d,fontCb:h,selectionCb:p,customElementCb:_}=e;e.mutationCb=function(){t.mutation&&t.mutation(...arguments),r(...arguments)},e.mousemoveCb=function(){t.mousemove&&t.mousemove(...arguments),i(...arguments)},e.mouseInteractionCb=function(){t.mouseInteraction&&t.mouseInteraction(...arguments),n(...arguments)},e.scrollCb=function(){t.scroll&&t.scroll(...arguments),s(...arguments)},e.viewportResizeCb=function(){t.viewportResize&&t.viewportResize(...arguments),o(...arguments)},e.inputCb=function(){t.input&&t.input(...arguments),a(...arguments)},e.mediaInteractionCb=function(){t.mediaInteaction&&t.mediaInteaction(...arguments),l(...arguments)},e.styleSheetRuleCb=function(){t.styleSheetRule&&t.styleSheetRule(...arguments),u(...arguments)},e.styleDeclarationCb=function(){t.styleDeclaration&&t.styleDeclaration(...arguments),c(...arguments)},e.canvasMutationCb=function(){t.canvasMutation&&t.canvasMutation(...arguments),d(...arguments)},e.fontCb=function(){t.font&&t.font(...arguments),h(...arguments)},e.selectionCb=function(){t.selection&&t.selection(...arguments),p(...arguments)},e.customElementCb=function(){t.customElement&&t.customElement(...arguments),_(...arguments)}}(e,t),e.recordDOM&&(i=Qe(e,e.doc));var s=function(e){var{mousemoveCb:t,sampling:r,doc:i,mirror:n}=e;if(!1===r.mousemove)return()=>{};var s,o="number"==typeof r.mousemove?r.mousemove:50,a="number"==typeof r.mousemoveCallback?r.mousemoveCallback:500,l=[],u=fe(Xe((e=>{var r=Date.now()-s;t(l.map((e=>(e.timeOffset-=r,e))),e),l=[],s=null})),a),c=Xe(fe(Xe((e=>{var t=Ke(e),{clientX:r,clientY:i}=xe(e)?e.changedTouches[0]:e;s||(s=ye()),l.push({x:r,y:i,id:n.getId(t),timeOffset:ye()-s}),u("undefined"!=typeof DragEvent&&e instanceof DragEvent?Le.Drag:e instanceof MouseEvent?Le.MouseMove:Le.TouchMove)})),o,{trailing:!1})),d=[pe("mousemove",c,i),pe("touchmove",c,i),pe("drag",c,i)];return Xe((()=>{d.forEach((e=>e()))}))}(e),o=et(e),a=tt(e),l=function(e,t){var{viewportResizeCb:r}=e,{win:i}=t,n=-1,s=-1;return pe("resize",Xe(fe(Xe((()=>{var e=Se(),t=we();n===e&&s===t||(r({width:Number(t),height:Number(e)}),n=e,s=t)})),200)),i)}(e,{win:n}),u=function(e){var{inputCb:t,doc:i,mirror:n,blockClass:s,blockSelector:o,ignoreClass:a,ignoreSelector:l,maskInputOptions:u,maskInputFn:c,sampling:d,userTriggeredOnInput:h}=e;function p(e){var t=Ke(e),r=e.isTrusted,n=t&&t.tagName;if(t&&"OPTION"===n&&(t=he.parentElement(t)),t&&n&&!(rt.indexOf(n)<0)&&!Ie(t,s,o,!0)&&!(t.classList.contains(a)||l&&t.matches(l))){var d=t.value,p=!1,g=R(t)||"";"radio"===g||"checkbox"===g?p=t.checked:(u[n.toLowerCase()]||u[g])&&(d=x({element:t,maskInputOptions:u,tagName:n,type:g,value:d,maskInputFn:c})),_(t,h?{text:d,isChecked:p,userTriggered:r}:{text:d,isChecked:p});var f=t.name;"radio"===g&&f&&p&&i.querySelectorAll('input[type="radio"][name="'+f+'"]').forEach((e=>{if(e!==t){var r=e.value;_(e,h?{text:r,isChecked:!p,userTriggered:!1}:{text:r,isChecked:!p})}}))}}function _(e,i){var s=it.get(e);if(!s||s.text!==i.text||s.isChecked!==i.isChecked){it.set(e,i);var o=n.getId(e);Xe(t)(r({},i,{id:o}))}}var g=("last"===d.input?["change"]:["input","change"]).map((e=>pe(e,Xe(p),i))),f=i.defaultView;if(!f)return()=>{g.forEach((e=>e()))};var v=f.Object.getOwnPropertyDescriptor(f.HTMLInputElement.prototype,"value"),m=[[f.HTMLInputElement.prototype,"value"],[f.HTMLInputElement.prototype,"checked"],[f.HTMLSelectElement.prototype,"value"],[f.HTMLTextAreaElement.prototype,"value"],[f.HTMLSelectElement.prototype,"selectedIndex"],[f.HTMLOptionElement.prototype,"selected"]];return v&&v.set&&g.push(...m.map((e=>ve(e[0],e[1],{set(){Xe(p)({target:this,isTrusted:!1})}},!1,f)))),Xe((()=>{g.forEach((e=>e()))}))}(e),c=function(e){var{mediaInteractionCb:t,blockClass:r,blockSelector:i,mirror:n,sampling:s,doc:o}=e,a=Xe((e=>fe(Xe((s=>{var o=Ke(s);if(o&&!Ie(o,r,i,!0)){var{currentTime:a,volume:l,muted:u,playbackRate:c,loop:d}=o;t({type:e,id:n.getId(o),currentTime:a,volume:l,muted:u,playbackRate:c,loop:d})}})),s.media||500))),l=[pe("play",a(He.Play),o),pe("pause",a(He.Pause),o),pe("seeked",a(He.Seeked),o),pe("volumechange",a(He.VolumeChange),o),pe("ratechange",a(He.RateChange),o)];return Xe((()=>{l.forEach((e=>e()))}))}(e),d=()=>{},h=()=>{},p=()=>{},_=()=>{};e.recordDOM&&(d=function(e,t){var{styleSheetRuleCb:r,mirror:i,stylesheetManager:n}=e,{win:s}=t;if(!s.CSSStyleSheet||!s.CSSStyleSheet.prototype)return()=>{};var o=s.CSSStyleSheet.prototype.insertRule;s.CSSStyleSheet.prototype.insertRule=new Proxy(o,{apply:Xe(((e,t,s)=>{var[o,a]=s,{id:l,styleId:u}=st(t,i,n.styleMirror);return(l&&-1!==l||u&&-1!==u)&&r({id:l,styleId:u,adds:[{rule:o,index:a}]}),e.apply(t,s)}))}),s.CSSStyleSheet.prototype.addRule=function(e,t,r){void 0===r&&(r=this.cssRules.length);var i=e+" { "+t+" }";return s.CSSStyleSheet.prototype.insertRule.apply(this,[i,r])};var a,l,u=s.CSSStyleSheet.prototype.deleteRule;s.CSSStyleSheet.prototype.deleteRule=new Proxy(u,{apply:Xe(((e,t,s)=>{var[o]=s,{id:a,styleId:l}=st(t,i,n.styleMirror);return(a&&-1!==a||l&&-1!==l)&&r({id:a,styleId:l,removes:[{index:o}]}),e.apply(t,s)}))}),s.CSSStyleSheet.prototype.removeRule=function(e){return s.CSSStyleSheet.prototype.deleteRule.apply(this,[e])},s.CSSStyleSheet.prototype.replace&&(a=s.CSSStyleSheet.prototype.replace,s.CSSStyleSheet.prototype.replace=new Proxy(a,{apply:Xe(((e,t,s)=>{var[o]=s,{id:a,styleId:l}=st(t,i,n.styleMirror);return(a&&-1!==a||l&&-1!==l)&&r({id:a,styleId:l,replace:o}),e.apply(t,s)}))})),s.CSSStyleSheet.prototype.replaceSync&&(l=s.CSSStyleSheet.prototype.replaceSync,s.CSSStyleSheet.prototype.replaceSync=new Proxy(l,{apply:Xe(((e,t,s)=>{var[o]=s,{id:a,styleId:l}=st(t,i,n.styleMirror);return(a&&-1!==a||l&&-1!==l)&&r({id:a,styleId:l,replaceSync:o}),e.apply(t,s)}))}));var c={};ut("CSSGroupingRule")?c.CSSGroupingRule=s.CSSGroupingRule:(ut("CSSMediaRule")&&(c.CSSMediaRule=s.CSSMediaRule),ut("CSSConditionRule")&&(c.CSSConditionRule=s.CSSConditionRule),ut("CSSSupportsRule")&&(c.CSSSupportsRule=s.CSSSupportsRule));var d={};return Object.entries(c).forEach((e=>{var[t,s]=e;d[t]={insertRule:s.prototype.insertRule,deleteRule:s.prototype.deleteRule},s.prototype.insertRule=new Proxy(d[t].insertRule,{apply:Xe(((e,t,s)=>{var[o,a]=s,{id:l,styleId:u}=st(t.parentStyleSheet,i,n.styleMirror);return(l&&-1!==l||u&&-1!==u)&&r({id:l,styleId:u,adds:[{rule:o,index:[...nt(t),a||0]}]}),e.apply(t,s)}))}),s.prototype.deleteRule=new Proxy(d[t].deleteRule,{apply:Xe(((e,t,s)=>{var[o]=s,{id:a,styleId:l}=st(t.parentStyleSheet,i,n.styleMirror);return(a&&-1!==a||l&&-1!==l)&&r({id:a,styleId:l,removes:[{index:[...nt(t),o]}]}),e.apply(t,s)}))})})),Xe((()=>{s.CSSStyleSheet.prototype.insertRule=o,s.CSSStyleSheet.prototype.deleteRule=u,a&&(s.CSSStyleSheet.prototype.replace=a),l&&(s.CSSStyleSheet.prototype.replaceSync=l),Object.entries(c).forEach((e=>{var[t,r]=e;r.prototype.insertRule=d[t].insertRule,r.prototype.deleteRule=d[t].deleteRule}))}))}(e,{win:n}),h=ot(e,e.doc),p=function(e,t){var{styleDeclarationCb:r,mirror:i,ignoreCSSAttributes:n,stylesheetManager:s}=e,{win:o}=t,a=o.CSSStyleDeclaration.prototype.setProperty;o.CSSStyleDeclaration.prototype.setProperty=new Proxy(a,{apply:Xe(((e,t,o)=>{var l,[u,c,d]=o;if(n.has(u))return a.apply(t,[u,c,d]);var{id:h,styleId:p}=st(null==(l=t.parentRule)?void 0:l.parentStyleSheet,i,s.styleMirror);return(h&&-1!==h||p&&-1!==p)&&r({id:h,styleId:p,set:{property:u,value:c,priority:d},index:nt(t.parentRule)}),e.apply(t,o)}))});var l=o.CSSStyleDeclaration.prototype.removeProperty;return o.CSSStyleDeclaration.prototype.removeProperty=new Proxy(l,{apply:Xe(((e,t,o)=>{var a,[u]=o;if(n.has(u))return l.apply(t,[u]);var{id:c,styleId:d}=st(null==(a=t.parentRule)?void 0:a.parentStyleSheet,i,s.styleMirror);return(c&&-1!==c||d&&-1!==d)&&r({id:c,styleId:d,remove:{property:u},index:nt(t.parentRule)}),e.apply(t,o)}))}),Xe((()=>{o.CSSStyleDeclaration.prototype.setProperty=a,o.CSSStyleDeclaration.prototype.removeProperty=l}))}(e,{win:n}),e.collectFonts&&(_=function(e){var{fontCb:t,doc:r}=e,i=r.defaultView;if(!i)return()=>{};var n=[],s=new WeakMap,o=i.FontFace;i.FontFace=function(e,t,r){var i=new o(e,t,r);return s.set(i,{family:e,buffer:"string"!=typeof t,descriptors:r,fontSource:"string"==typeof t?t:JSON.stringify(Array.from(new Uint8Array(t)))}),i};var a=me(r.fonts,"add",(function(e){return function(r){return setTimeout(Xe((()=>{var e=s.get(r);e&&(t(e),s.delete(r))})),0),e.apply(this,[r])}}));return n.push((()=>{i.FontFace=o})),n.push(a),Xe((()=>{n.forEach((e=>e()))}))}(e)));var g=function(e){var{doc:t,mirror:r,blockClass:i,blockSelector:n,selectionCb:s}=e,o=!0,a=Xe((()=>{var e=t.getSelection();if(!(!e||o&&(null==e?void 0:e.isCollapsed))){o=e.isCollapsed||!1;for(var a=[],l=e.rangeCount||0,u=0;u<l;u++){var c=e.getRangeAt(u),{startContainer:d,startOffset:h,endContainer:p,endOffset:_}=c;Ie(d,i,n,!0)||Ie(p,i,n,!0)||a.push({start:r.getId(d),startOffset:h,end:r.getId(p),endOffset:_})}s({ranges:a})}}));return a(),pe("selectionchange",a)}(e),f=function(e){var{doc:t,customElementCb:r}=e,i=t.defaultView;if(!i||!i.customElements)return()=>{};var n=me(i.customElements,"define",(function(e){return function(t,i,n){try{r({define:{name:t}})}catch(e){console.warn("Custom element callback failed for "+t)}return e.apply(this,[t,i,n])}}));return n}(e),v=[];for(var m of e.plugins)v.push(m.observer(m.callback,n,m.options));return Xe((()=>{Je.forEach((e=>e.reset())),null==i||i.disconnect(),s(),o(),a(),l(),u(),c(),d(),h(),p(),_(),g(),f(),v.forEach((e=>e()))}))}function lt(e){return void 0!==window[e]}function ut(e){return Boolean(void 0!==window[e]&&window[e].prototype&&"insertRule"in window[e].prototype&&"deleteRule"in window[e].prototype)}class ct{constructor(e){a(this,"iframeIdToRemoteIdMap",new WeakMap),a(this,"iframeRemoteIdToIdMap",new WeakMap),this.generateIdFn=e}getId(e,t,r,i){var n=r||this.getIdToRemoteIdMap(e),s=i||this.getRemoteIdToIdMap(e),o=n.get(t);return o||(o=this.generateIdFn(),n.set(t,o),s.set(o,t)),o}getIds(e,t){var r=this.getIdToRemoteIdMap(e),i=this.getRemoteIdToIdMap(e);return t.map((t=>this.getId(e,t,r,i)))}getRemoteId(e,t,r){var i=r||this.getRemoteIdToIdMap(e);if("number"!=typeof t)return t;var n=i.get(t);return n||-1}getRemoteIds(e,t){var r=this.getRemoteIdToIdMap(e);return t.map((t=>this.getRemoteId(e,t,r)))}reset(e){if(!e)return this.iframeIdToRemoteIdMap=new WeakMap,void(this.iframeRemoteIdToIdMap=new WeakMap);this.iframeIdToRemoteIdMap.delete(e),this.iframeRemoteIdToIdMap.delete(e)}getIdToRemoteIdMap(e){var t=this.iframeIdToRemoteIdMap.get(e);return t||(t=new Map,this.iframeIdToRemoteIdMap.set(e,t)),t}getRemoteIdToIdMap(e){var t=this.iframeRemoteIdToIdMap.get(e);return t||(t=new Map,this.iframeRemoteIdToIdMap.set(e,t)),t}}class dt{constructor(e){a(this,"iframes",new WeakMap),a(this,"crossOriginIframeMap",new WeakMap),a(this,"crossOriginIframeMirror",new ct(V)),a(this,"crossOriginIframeStyleMirror"),a(this,"crossOriginIframeRootIdMap",new WeakMap),a(this,"mirror"),a(this,"mutationCb"),a(this,"wrappedEmit"),a(this,"loadListener"),a(this,"stylesheetManager"),a(this,"recordCrossOriginIframes"),this.mutationCb=e.mutationCb,this.wrappedEmit=e.wrappedEmit,this.stylesheetManager=e.stylesheetManager,this.recordCrossOriginIframes=e.recordCrossOriginIframes,this.crossOriginIframeStyleMirror=new ct(this.stylesheetManager.styleMirror.generateId.bind(this.stylesheetManager.styleMirror)),this.mirror=e.mirror,this.recordCrossOriginIframes&&window.addEventListener("message",this.handleMessage.bind(this))}addIframe(e){this.iframes.set(e,!0),e.contentWindow&&this.crossOriginIframeMap.set(e.contentWindow,e)}addLoadListener(e){this.loadListener=e}attachIframe(e,t){var r,i;this.mutationCb({adds:[{parentId:this.mirror.getId(e),nextId:null,node:t}],removes:[],texts:[],attributes:[],isAttachIframe:!0}),this.recordCrossOriginIframes&&(null==(r=e.contentWindow)||r.addEventListener("message",this.handleMessage.bind(this))),null==(i=this.loadListener)||i.call(this,e),e.contentDocument&&e.contentDocument.adoptedStyleSheets&&e.contentDocument.adoptedStyleSheets.length>0&&this.stylesheetManager.adoptStyleSheets(e.contentDocument.adoptedStyleSheets,this.mirror.getId(e.contentDocument))}handleMessage(e){var t=e;if("rrweb"===t.data.type&&t.origin===t.data.origin&&e.source){var r=this.crossOriginIframeMap.get(e.source);if(r){var i=this.transformCrossOriginEvent(r,t.data.event);i&&this.wrappedEmit(i,t.data.isCheckout)}}}transformCrossOriginEvent(e,t){var r;switch(t.type){case Oe.FullSnapshot:this.crossOriginIframeMirror.reset(e),this.crossOriginIframeStyleMirror.reset(e),this.replaceIdOnNode(t.data.node,e);var i=t.data.node.id;return this.crossOriginIframeRootIdMap.set(e,i),this.patchRootIdOnNode(t.data.node,i),{timestamp:t.timestamp,type:Oe.IncrementalSnapshot,data:{source:Le.Mutation,adds:[{parentId:this.mirror.getId(e),nextId:null,node:t.data.node}],removes:[],texts:[],attributes:[],isAttachIframe:!0}};case Oe.Meta:case Oe.Load:case Oe.DomContentLoaded:return!1;case Oe.Plugin:return t;case Oe.Custom:return this.replaceIds(t.data.payload,e,["id","parentId","previousId","nextId"]),t;case Oe.IncrementalSnapshot:switch(t.data.source){case Le.Mutation:return t.data.adds.forEach((t=>{this.replaceIds(t,e,["parentId","nextId","previousId"]),this.replaceIdOnNode(t.node,e);var r=this.crossOriginIframeRootIdMap.get(e);r&&this.patchRootIdOnNode(t.node,r)})),t.data.removes.forEach((t=>{this.replaceIds(t,e,["parentId","id"])})),t.data.attributes.forEach((t=>{this.replaceIds(t,e,["id"])})),t.data.texts.forEach((t=>{this.replaceIds(t,e,["id"])})),t;case Le.Drag:case Le.TouchMove:case Le.MouseMove:return t.data.positions.forEach((t=>{this.replaceIds(t,e,["id"])})),t;case Le.ViewportResize:return!1;case Le.MediaInteraction:case Le.MouseInteraction:case Le.Scroll:case Le.CanvasMutation:case Le.Input:return this.replaceIds(t.data,e,["id"]),t;case Le.StyleSheetRule:case Le.StyleDeclaration:return this.replaceIds(t.data,e,["id"]),this.replaceStyleIds(t.data,e,["styleId"]),t;case Le.Font:return t;case Le.Selection:return t.data.ranges.forEach((t=>{this.replaceIds(t,e,["start","end"])})),t;case Le.AdoptedStyleSheet:return this.replaceIds(t.data,e,["id"]),this.replaceStyleIds(t.data,e,["styleIds"]),null==(r=t.data.styles)||r.forEach((t=>{this.replaceStyleIds(t,e,["styleId"])})),t}}return!1}replace(e,t,r,i){for(var n of i)(Array.isArray(t[n])||"number"==typeof t[n])&&(Array.isArray(t[n])?t[n]=e.getIds(r,t[n]):t[n]=e.getId(r,t[n]));return t}replaceIds(e,t,r){return this.replace(this.crossOriginIframeMirror,e,t,r)}replaceStyleIds(e,t,r){return this.replace(this.crossOriginIframeStyleMirror,e,t,r)}replaceIdOnNode(e,t){this.replaceIds(e,t,["id","rootId"]),"childNodes"in e&&e.childNodes.forEach((e=>{this.replaceIdOnNode(e,t)}))}patchRootIdOnNode(e,t){e.type===c.Document||e.rootId||(e.rootId=t),"childNodes"in e&&e.childNodes.forEach((e=>{this.patchRootIdOnNode(e,t)}))}}class ht{constructor(e){a(this,"shadowDoms",new WeakSet),a(this,"mutationCb"),a(this,"scrollCb"),a(this,"bypassOptions"),a(this,"mirror"),a(this,"restoreHandlers",[]),this.mutationCb=e.mutationCb,this.scrollCb=e.scrollCb,this.bypassOptions=e.bypassOptions,this.mirror=e.mirror,this.init()}init(){this.reset(),this.patchAttachShadow(Element,document)}addShadowRoot(e,t){if(w(e)&&!this.shadowDoms.has(e)){this.shadowDoms.add(e);var i=Qe(r({},this.bypassOptions,{doc:t,mutationCb:this.mutationCb,mirror:this.mirror,shadowDomManager:this}),e);this.restoreHandlers.push((()=>i.disconnect())),this.restoreHandlers.push(tt(r({},this.bypassOptions,{scrollCb:this.scrollCb,doc:e,mirror:this.mirror}))),setTimeout((()=>{e.adoptedStyleSheets&&e.adoptedStyleSheets.length>0&&this.bypassOptions.stylesheetManager.adoptStyleSheets(e.adoptedStyleSheets,this.mirror.getId(he.host(e))),this.restoreHandlers.push(ot({mirror:this.mirror,stylesheetManager:this.bypassOptions.stylesheetManager},e))}),0)}}observeAttachShadow(e){e.contentWindow&&e.contentDocument&&this.patchAttachShadow(e.contentWindow.Element,e.contentDocument)}patchAttachShadow(e,t){var r=this;this.restoreHandlers.push(me(e.prototype,"attachShadow",(function(e){return function(i){var n=e.call(this,i),s=he.shadowRoot(this);return s&&Pe(this)&&r.addShadowRoot(s,t),n}})))}reset(){this.restoreHandlers.forEach((e=>{try{e()}catch(e){}})),this.restoreHandlers=[],this.shadowDoms=new WeakSet}}for(var pt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",_t="undefined"==typeof Uint8Array?[]:new Uint8Array(256),gt=0;gt<64;gt++)_t[pt.charCodeAt(gt)]=gt;var ft=new Map;var vt=(e,t,r)=>{if(e&&(bt(e,t)||"object"==typeof e)){var i=function(e,t){var r=ft.get(e);return r||(r=new Map,ft.set(e,r)),r.has(t)||r.set(t,[]),r.get(t)}(r,e.constructor.name),n=i.indexOf(e);return-1===n&&(n=i.length,i.push(e)),n}};function mt(e,t,r){if(e instanceof Array)return e.map((e=>mt(e,t,r)));if(null===e)return e;if(e instanceof Float32Array||e instanceof Float64Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Uint8Array||e instanceof Uint16Array||e instanceof Int16Array||e instanceof Int8Array||e instanceof Uint8ClampedArray)return{rr_type:e.constructor.name,args:[Object.values(e)]};if(e instanceof ArrayBuffer)return{rr_type:e.constructor.name,base64:function(e){var t,r=new Uint8Array(e),i=r.length,n="";for(t=0;t<i;t+=3)n+=pt[r[t]>>2],n+=pt[(3&r[t])<<4|r[t+1]>>4],n+=pt[(15&r[t+1])<<2|r[t+2]>>6],n+=pt[63&r[t+2]];return i%3==2?n=n.substring(0,n.length-1)+"=":i%3==1&&(n=n.substring(0,n.length-2)+"=="),n}(e)};if(e instanceof DataView)return{rr_type:e.constructor.name,args:[mt(e.buffer,t,r),e.byteOffset,e.byteLength]};if(e instanceof HTMLImageElement){var i=e.constructor.name,{src:n}=e;return{rr_type:i,src:n}}if(e instanceof HTMLCanvasElement){return{rr_type:"HTMLImageElement",src:e.toDataURL()}}return e instanceof ImageData?{rr_type:e.constructor.name,args:[mt(e.data,t,r),e.width,e.height]}:bt(e,t)||"object"==typeof e?{rr_type:e.constructor.name,index:vt(e,t,r)}:e}var yt=(e,t,r)=>e.map((e=>mt(e,t,r))),bt=(e,t)=>{var r=["WebGLActiveInfo","WebGLBuffer","WebGLFramebuffer","WebGLProgram","WebGLRenderbuffer","WebGLShader","WebGLShaderPrecisionFormat","WebGLTexture","WebGLUniformLocation","WebGLVertexArrayObject","WebGLVertexArrayObjectOES"].filter((e=>"function"==typeof t[e]));return Boolean(r.find((r=>e instanceof t[r])))};function St(e,t,r,i){var n=[];try{var s=me(e.HTMLCanvasElement.prototype,"getContext",(function(e){return function(n){for(var s=arguments.length,o=new Array(s>1?s-1:0),a=1;a<s;a++)o[a-1]=arguments[a];if(!Ie(this,t,r,!0)){var l=function(e){return"experimental-webgl"===e?"webgl":e}(n);if("__context"in this||(this.__context=l),i&&["webgl","webgl2"].includes(l))if(o[0]&&"object"==typeof o[0]){var u=o[0];u.preserveDrawingBuffer||(u.preserveDrawingBuffer=!0)}else o.splice(0,1,{preserveDrawingBuffer:!0})}return e.apply(this,[n,...o])}}));n.push(s)}catch(e){console.error("failed to patch HTMLCanvasElement.prototype.getContext")}return()=>{n.forEach((e=>e()))}}function wt(e,t,r,i,n,s){var o=[],a=Object.getOwnPropertyNames(e),l=function(a){if(["isContextLost","canvas","drawingBufferWidth","drawingBufferHeight"].includes(a))return 0;try{if("function"!=typeof e[a])return 0;var l=me(e,a,(function(e){return function(){for(var o=arguments.length,l=new Array(o),u=0;u<o;u++)l[u]=arguments[u];var c=e.apply(this,l);if(vt(c,s,this),"tagName"in this.canvas&&!Ie(this.canvas,i,n,!0)){var d=yt(l,s,this),h={type:t,property:a,args:d};r(this.canvas,h)}return c}}));o.push(l)}catch(i){var u=ve(e,a,{set(e){r(this.canvas,{type:t,property:a,args:[e],setter:!0})}});o.push(u)}};for(var u of a)l(u);return o}var Ct,It,kt,Et,xt="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",Tt="undefined"!=typeof window&&window.Blob&&new Blob([(Ct=xt,Uint8Array.from(atob(Ct),(e=>e.charCodeAt(0))))],{type:"text/javascript;charset=utf-8"});function Mt(e){var t;try{if(!(t=Tt&&(window.URL||window.webkitURL).createObjectURL(Tt)))throw"";var r=new Worker(t,{name:null==e?void 0:e.name});return r.addEventListener("error",(()=>{(window.URL||window.webkitURL).revokeObjectURL(t)})),r}catch(t){return new Worker("data:text/javascript;base64,"+xt,{name:null==e?void 0:e.name})}finally{t&&(window.URL||window.webkitURL).revokeObjectURL(t)}}class Rt{constructor(e){a(this,"pendingCanvasMutations",new Map),a(this,"rafStamps",{latestId:0,invokeId:null}),a(this,"mirror"),a(this,"mutationCb"),a(this,"resetObservers"),a(this,"frozen",!1),a(this,"locked",!1),a(this,"processMutation",((e,t)=>{!(this.rafStamps.invokeId&&this.rafStamps.latestId!==this.rafStamps.invokeId)&&this.rafStamps.invokeId||(this.rafStamps.invokeId=this.rafStamps.latestId),this.pendingCanvasMutations.has(e)||this.pendingCanvasMutations.set(e,[]),this.pendingCanvasMutations.get(e).push(t)}));var{sampling:t="all",win:r,blockClass:i,blockSelector:n,recordCanvas:s,dataURLOptions:o}=e;this.mutationCb=e.mutationCb,this.mirror=e.mirror,s&&"all"===t&&this.initCanvasMutationObserver(r,i,n),s&&"number"==typeof t&&this.initCanvasFPSObserver(t,r,i,n,{dataURLOptions:o})}reset(){this.pendingCanvasMutations.clear(),this.resetObservers&&this.resetObservers()}freeze(){this.frozen=!0}unfreeze(){this.frozen=!1}lock(){this.locked=!0}unlock(){this.locked=!1}initCanvasFPSObserver(e,r,i,n,s){var o=this,a=St(r,i,n,!0),l=new Map,u=new Mt;u.onmessage=e=>{var{id:t}=e.data;if(l.set(t,!1),"base64"in e.data){var{base64:r,type:i,width:n,height:s}=e.data;this.mutationCb({id:t,type:qe["2D"],commands:[{property:"clearRect",args:[0,0,n,s]},{property:"drawImage",args:[{rr_type:"ImageBitmap",args:[{rr_type:"Blob",data:[{rr_type:"ArrayBuffer",base64:r}],type:i}]},0,0]}]})}};var c,d=1e3/e,h=0,p=e=>{var a,_;h&&e-h<d?c=requestAnimationFrame(p):(h=e,(a=[],_=e=>{e.querySelectorAll("canvas").forEach((e=>{Ie(e,i,n,!0)||a.push(e)})),e.querySelectorAll("*").forEach((e=>{e.shadowRoot&&_(e.shadowRoot)}))},_(r.document),a).forEach(function(){var e=t((function*(e){var t,r=o.mirror.getId(e);if(!l.get(r)&&0!==e.width&&0!==e.height){if(l.set(r,!0),["webgl","webgl2"].includes(e.__context)){var i=e.getContext(e.__context);!1===(null==(t=null==i?void 0:i.getContextAttributes())?void 0:t.preserveDrawingBuffer)&&i.clear(i.COLOR_BUFFER_BIT)}var n=e.clientWidth||e.width,a=e.clientHeight||e.height,c=yield createImageBitmap(e,{resizeWidth:n,resizeHeight:a});u.postMessage({id:r,bitmap:c,width:n,height:a,dataURLOptions:s.dataURLOptions},[c])}}));return function(t){return e.apply(this,arguments)}}()),c=requestAnimationFrame(p))};c=requestAnimationFrame(p),this.resetObservers=()=>{a(),cancelAnimationFrame(c)}}initCanvasMutationObserver(e,t,r){this.startRAFTimestamping(),this.startPendingCanvasMutationFlusher();var i=St(e,t,r,!1),n=function(e,t,r,i){var n=[],s=Object.getOwnPropertyNames(t.CanvasRenderingContext2D.prototype),o=function(s){try{if("function"!=typeof t.CanvasRenderingContext2D.prototype[s])return 1;var o=me(t.CanvasRenderingContext2D.prototype,s,(function(n){return function(){for(var o=arguments.length,a=new Array(o),l=0;l<o;l++)a[l]=arguments[l];return Ie(this.canvas,r,i,!0)||setTimeout((()=>{var r=yt(a,t,this);e(this.canvas,{type:qe["2D"],property:s,args:r})}),0),n.apply(this,a)}}));n.push(o)}catch(r){var a=ve(t.CanvasRenderingContext2D.prototype,s,{set(t){e(this.canvas,{type:qe["2D"],property:s,args:[t],setter:!0})}});n.push(a)}};for(var a of s)o(a);return()=>{n.forEach((e=>e()))}}(this.processMutation.bind(this),e,t,r),s=function(e,t,r,i){var n=[];return n.push(...wt(t.WebGLRenderingContext.prototype,qe.WebGL,e,r,i,t)),void 0!==t.WebGL2RenderingContext&&n.push(...wt(t.WebGL2RenderingContext.prototype,qe.WebGL2,e,r,i,t)),()=>{n.forEach((e=>e()))}}(this.processMutation.bind(this),e,t,r);this.resetObservers=()=>{i(),n(),s()}}startPendingCanvasMutationFlusher(){requestAnimationFrame((()=>this.flushPendingCanvasMutations()))}startRAFTimestamping(){var e=t=>{this.rafStamps.latestId=t,requestAnimationFrame(e)};requestAnimationFrame(e)}flushPendingCanvasMutations(){this.pendingCanvasMutations.forEach(((e,t)=>{var r=this.mirror.getId(t);this.flushPendingCanvasMutationFor(t,r)})),requestAnimationFrame((()=>this.flushPendingCanvasMutations()))}flushPendingCanvasMutationFor(e,t){if(!this.frozen&&!this.locked){var r=this.pendingCanvasMutations.get(e);if(r&&-1!==t){var n=r.map((e=>i(e,s))),{type:o}=r[0];this.mutationCb({id:t,type:o,commands:n}),this.pendingCanvasMutations.delete(e)}}}}class At{constructor(e){a(this,"trackedLinkElements",new WeakSet),a(this,"mutationCb"),a(this,"adoptedStyleSheetCb"),a(this,"styleMirror",new Ae),this.mutationCb=e.mutationCb,this.adoptedStyleSheetCb=e.adoptedStyleSheetCb}attachLinkElement(e,t){"_cssText"in t.attributes&&this.mutationCb({adds:[],removes:[],texts:[],attributes:[{id:t.id,attributes:t.attributes}]}),this.trackLinkElement(e)}trackLinkElement(e){this.trackedLinkElements.has(e)||(this.trackedLinkElements.add(e),this.trackStylesheetInLinkElement(e))}adoptStyleSheets(e,t){var r=this;if(0!==e.length){var i={id:t,styleIds:[]},n=[],s=function(e){var t;r.styleMirror.has(e)?t=r.styleMirror.getId(e):(t=r.styleMirror.add(e),n.push({styleId:t,rules:Array.from(e.rules||CSSRule,((t,r)=>({rule:I(t,e.href),index:r})))})),i.styleIds.push(t)};for(var o of e)s(o);n.length>0&&(i.styles=n),this.adoptedStyleSheetCb(i)}}reset(){this.styleMirror.reset(),this.trackedLinkElements=new WeakSet}trackStylesheetInLinkElement(e){}}class Ft{constructor(){a(this,"nodeMap",new WeakMap),a(this,"active",!1)}inOtherBuffer(e,t){var r=this.nodeMap.get(e);return r&&Array.from(r).some((e=>e!==t))}add(e,t){this.active||(this.active=!0,requestAnimationFrame((()=>{this.nodeMap=new WeakMap,this.active=!1}))),this.nodeMap.set(e,(this.nodeMap.get(e)||new Set).add(t))}destroy(){}}var Nt=!1;try{if(2!==Array.from([1],(e=>2*e))[0]){var Pt=document.createElement("iframe");document.body.appendChild(Pt),Array.from=(null==(n=Pt.contentWindow)?void 0:n.Array.from)||Array.from,document.body.removeChild(Pt)}}catch(e){console.debug("Unable to override Array.from",e)}var Ot,Lt,Dt=new E;function Bt(e){void 0===e&&(e={});var{emit:t,checkoutEveryNms:i,checkoutEveryNth:n,blockClass:s="rr-block",blockSelector:o=null,ignoreClass:a="rr-ignore",ignoreSelector:l=null,maskTextClass:u="rr-mask",maskTextSelector:c=null,inlineStylesheet:d=!0,maskAllInputs:h,maskInputOptions:p,slimDOMOptions:_,maskInputFn:g,maskTextFn:f,hooks:v,packFn:m,sampling:y={},dataURLOptions:b={},mousemoveWait:S,recordDOM:w=!0,recordCanvas:C=!1,recordCrossOriginIframes:I=!1,recordAfter:k=("DOMContentLoaded"===e.recordAfter?e.recordAfter:"load"),userTriggeredOnInput:x=!1,collectFonts:T=!1,inlineImages:M=!1,plugins:R,keepIframeSrcFn:A=(()=>!1),ignoreCSSAttributes:F=new Set([]),errorHandler:N}=e;We=N;var P=!I||window.parent===window,O=!1;if(!P)try{window.parent.document&&(O=!1)}catch(e){O=!0}if(P&&!t)throw new Error("emit function is required");if(!P&&!O)return()=>{};void 0!==S&&void 0===y.mousemove&&(y.mousemove=S),Dt.reset();var L,D=!0===h?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,password:!0}:void 0!==p?p:{password:!0},B=!0===_||"all"===_?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaVerification:!0,headMetaAuthorship:"all"===_,headMetaDescKeywords:"all"===_,headTitleMutations:"all"===_}:_||{};!function(e){void 0===e&&(e=window),"NodeList"in e&&!e.NodeList.prototype.forEach&&(e.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in e&&!e.DOMTokenList.prototype.forEach&&(e.DOMTokenList.prototype.forEach=Array.prototype.forEach)}();var q=0,H=e=>{for(var t of R||[])t.eventProcessor&&(e=t.eventProcessor(e));return m&&!O&&(e=m(e)),e};It=(e,r)=>{var s,o=e;if(o.timestamp=ye(),!(null==(s=Je[0])?void 0:s.isFrozen())||o.type===Oe.FullSnapshot||o.type===Oe.IncrementalSnapshot&&o.data.source===Le.Mutation||Je.forEach((e=>e.unfreeze())),P)null==t||t(H(o),r);else if(O){var a={type:"rrweb",event:H(o),origin:window.location.origin,isCheckout:r};window.parent.postMessage(a,"*")}if(o.type===Oe.FullSnapshot)L=o,q=0;else if(o.type===Oe.IncrementalSnapshot){if(o.data.source===Le.Mutation&&o.data.isAttachIframe)return;q++;var l=n&&q>=n,u=i&&o.timestamp-L.timestamp>i;(l||u)&&kt(!0)}};var $=e=>{It({type:Oe.IncrementalSnapshot,data:r({source:Le.Mutation},e)})},V=e=>It({type:Oe.IncrementalSnapshot,data:r({source:Le.Scroll},e)}),W=e=>It({type:Oe.IncrementalSnapshot,data:r({source:Le.CanvasMutation},e)}),U=new At({mutationCb:$,adoptedStyleSheetCb:e=>It({type:Oe.IncrementalSnapshot,data:r({source:Le.AdoptedStyleSheet},e)})}),Z=new dt({mirror:Dt,mutationCb:$,stylesheetManager:U,recordCrossOriginIframes:I,wrappedEmit:It});for(var z of R||[])z.getMirror&&z.getMirror({nodeMirror:Dt,crossOriginIframeMirror:Z.crossOriginIframeMirror,crossOriginIframeStyleMirror:Z.crossOriginIframeStyleMirror});var G=new Ft;Et=new Rt({recordCanvas:C,mutationCb:W,win:window,blockClass:s,blockSelector:o,mirror:Dt,sampling:y.canvas,dataURLOptions:b});var j=new ht({mutationCb:$,scrollCb:V,bypassOptions:{blockClass:s,blockSelector:o,maskTextClass:u,maskTextSelector:c,inlineStylesheet:d,maskInputOptions:D,dataURLOptions:b,maskTextFn:f,maskInputFn:g,recordCanvas:C,inlineImages:M,sampling:y,slimDOMOptions:B,iframeManager:Z,stylesheetManager:U,canvasManager:Et,keepIframeSrcFn:A,processedNodeManager:G},mirror:Dt});kt=function(e){if(void 0===e&&(e=!1),w){It({type:Oe.Meta,data:{href:window.location.href,width:we(),height:Se()}},e),U.reset(),j.init(),Je.forEach((e=>e.lock()));var t=function(e,t){var{mirror:r=new E,blockClass:i="rr-block",blockSelector:n=null,maskTextClass:s="rr-mask",maskTextSelector:o=null,inlineStylesheet:a=!0,inlineImages:l=!1,recordCanvas:u=!1,maskAllInputs:c=!1,maskTextFn:d,maskInputFn:h,slimDOM:p=!1,dataURLOptions:_,preserveWhiteSpace:g,onSerialize:f,onIframeLoad:v,iframeLoadTimeout:m,onStylesheetLoad:y,stylesheetLoadTimeout:b,keepIframeSrcFn:S=(()=>!1)}=t||{};return te(e,{doc:e,mirror:r,blockClass:i,blockSelector:n,maskTextClass:s,maskTextSelector:o,skipChild:!1,inlineStylesheet:a,maskInputOptions:!0===c?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,password:!0}:!1===c?{password:!0}:c,maskTextFn:d,maskInputFn:h,slimDOMOptions:!0===p||"all"===p?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaDescKeywords:"all"===p,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaAuthorship:!0,headMetaVerification:!0}:!1===p?{}:p,dataURLOptions:_,inlineImages:l,recordCanvas:u,preserveWhiteSpace:g,onSerialize:f,onIframeLoad:v,iframeLoadTimeout:m,onStylesheetLoad:y,stylesheetLoadTimeout:b,keepIframeSrcFn:S,newlyAddedElement:!1})}(document,{mirror:Dt,blockClass:s,blockSelector:o,maskTextClass:u,maskTextSelector:c,inlineStylesheet:d,maskAllInputs:D,maskTextFn:f,maskInputFn:g,slimDOM:B,dataURLOptions:b,recordCanvas:C,inlineImages:M,onSerialize:e=>{Te(e,Dt)&&Z.addIframe(e),Me(e,Dt)&&U.trackLinkElement(e),Re(e)&&j.addShadowRoot(he.shadowRoot(e),document)},onIframeLoad:(e,t)=>{Z.attachIframe(e,t),j.observeAttachShadow(e)},onStylesheetLoad:(e,t)=>{U.attachLinkElement(e,t)},keepIframeSrcFn:A});if(!t)return console.warn("Failed to snapshot the document");It({type:Oe.FullSnapshot,data:{node:t,initialOffset:be(window)}},e),Je.forEach((e=>e.unlock())),document.adoptedStyleSheets&&document.adoptedStyleSheets.length>0&&U.adoptStyleSheets(document.adoptedStyleSheets,Dt.getId(document))}};try{var Y=[],X=e=>{var t;return Xe(at)({mutationCb:$,mousemoveCb:(e,t)=>It({type:Oe.IncrementalSnapshot,data:{source:t,positions:e}}),mouseInteractionCb:e=>It({type:Oe.IncrementalSnapshot,data:r({source:Le.MouseInteraction},e)}),scrollCb:V,viewportResizeCb:e=>It({type:Oe.IncrementalSnapshot,data:r({source:Le.ViewportResize},e)}),inputCb:e=>It({type:Oe.IncrementalSnapshot,data:r({source:Le.Input},e)}),mediaInteractionCb:e=>It({type:Oe.IncrementalSnapshot,data:r({source:Le.MediaInteraction},e)}),styleSheetRuleCb:e=>It({type:Oe.IncrementalSnapshot,data:r({source:Le.StyleSheetRule},e)}),styleDeclarationCb:e=>It({type:Oe.IncrementalSnapshot,data:r({source:Le.StyleDeclaration},e)}),canvasMutationCb:W,fontCb:e=>It({type:Oe.IncrementalSnapshot,data:r({source:Le.Font},e)}),selectionCb:e=>{It({type:Oe.IncrementalSnapshot,data:r({source:Le.Selection},e)})},customElementCb:e=>{It({type:Oe.IncrementalSnapshot,data:r({source:Le.CustomElement},e)})},blockClass:s,ignoreClass:a,ignoreSelector:l,maskTextClass:u,maskTextSelector:c,maskInputOptions:D,inlineStylesheet:d,sampling:y,recordDOM:w,recordCanvas:C,inlineImages:M,userTriggeredOnInput:x,collectFonts:T,doc:e,maskInputFn:g,maskTextFn:f,keepIframeSrcFn:A,blockSelector:o,slimDOMOptions:B,dataURLOptions:b,mirror:Dt,iframeManager:Z,stylesheetManager:U,shadowDomManager:j,processedNodeManager:G,canvasManager:Et,ignoreCSSAttributes:F,plugins:(null==(t=null==R?void 0:R.filter((e=>e.observer)))?void 0:t.map((e=>({observer:e.observer,options:e.options,callback:t=>It({type:Oe.Plugin,data:{plugin:e.name,payload:t}})}))))||[]},v)};Z.addLoadListener((e=>{try{Y.push(X(e.contentDocument))}catch(e){console.warn(e)}}));var J=()=>{kt(),Y.push(X(document)),Nt=!0};return"interactive"===document.readyState||"complete"===document.readyState?J():(Y.push(pe("DOMContentLoaded",(()=>{It({type:Oe.DomContentLoaded,data:{}}),"DOMContentLoaded"===k&&J()}))),Y.push(pe("load",(()=>{It({type:Oe.Load,data:{}}),"load"===k&&J()}),window))),()=>{Y.forEach((e=>e())),G.destroy(),Nt=!1,We=void 0}}catch(e){console.warn(e)}}Bt.addCustomEvent=(e,t)=>{if(!Nt)throw new Error("please add custom event after start recording");It({type:Oe.Custom,data:{tag:e,payload:t}})},Bt.freezePage=()=>{Je.forEach((e=>e.freeze()))},Bt.takeFullSnapshot=e=>{if(!Nt)throw new Error("please take full snapshot after start recording");kt(e)},Bt.mirror=Dt,(Lt=Ot||(Ot={}))[Lt.NotStarted=0]="NotStarted",Lt[Lt.Running=1]="Running",Lt[Lt.Stopped=2]="Stopped";var qt,Ht=Object.defineProperty,$t=(e,t,r)=>((e,t,r)=>t in e?Ht(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r)(e,"symbol"!=typeof t?t+"":t,r),Vt=Object.defineProperty,Wt=(e,t,r)=>((e,t,r)=>t in e?Vt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r)(e,"symbol"!=typeof t?t+"":t,r),Ut=Object.defineProperty,Zt=(e,t,r)=>((e,t,r)=>t in e?Ut(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r)(e,"symbol"!=typeof t?t+"":t,r),zt={Node:["childNodes","parentNode","parentElement","textContent"],ShadowRoot:["host","styleSheets"],Element:["shadowRoot","querySelector","querySelectorAll"],MutationObserver:[]},Gt={Node:["contains","getRootNode"],ShadowRoot:["getSelection"],Element:[],MutationObserver:["constructor"]},jt={};function Yt(e){if(jt[e])return jt[e];var t=globalThis[e],r=t.prototype,i=e in zt?zt[e]:void 0,n=Boolean(i&&i.every((e=>{var t,i;return Boolean(null==(i=null==(t=Object.getOwnPropertyDescriptor(r,e))?void 0:t.get)?void 0:i.toString().includes("[native code]"))}))),s=e in Gt?Gt[e]:void 0,o=Boolean(s&&s.every((e=>{var t;return"function"==typeof r[e]&&(null==(t=r[e])?void 0:t.toString().includes("[native code]"))})));if(n&&o)return jt[e]=t.prototype,t.prototype;try{var a=document.createElement("iframe");document.body.appendChild(a);var l=a.contentWindow;if(!l)return t.prototype;var u=l[e].prototype;return document.body.removeChild(a),u?jt[e]=u:r}catch(e){return r}}var Xt={};function Jt(e,t,r){var i,n=e+"."+String(r);if(Xt[n])return Xt[n].call(t);var s=Yt(e),o=null==(i=Object.getOwnPropertyDescriptor(s,r))?void 0:i.get;return o?(Xt[n]=o,o.call(t)):t[r]}var Kt={};function Qt(e,t,r){var i=e+"."+String(r);if(Kt[i])return Kt[i].bind(t);var n=Yt(e)[r];return"function"!=typeof n?t[r]:(Kt[i]=n,n.bind(t))}var er={childNodes:function(e){return Jt("Node",e,"childNodes")},parentNode:function(e){return Jt("Node",e,"parentNode")},parentElement:function(e){return Jt("Node",e,"parentElement")},textContent:function(e){return Jt("Node",e,"textContent")},contains:function(e,t){return Qt("Node",e,"contains")(t)},getRootNode:function(e){return Qt("Node",e,"getRootNode")()},host:function(e){return e&&"host"in e?Jt("ShadowRoot",e,"host"):null},styleSheets:function(e){return e.styleSheets},shadowRoot:function(e){return e&&"shadowRoot"in e?Jt("Element",e,"shadowRoot"):null},querySelector:function(e,t){return Jt("Element",e,"querySelector")(t)},querySelectorAll:function(e,t){return Jt("Element",e,"querySelectorAll")(t)},mutationObserver:function(){return Yt("MutationObserver").constructor}};class tr{constructor(){Zt(this,"idNodeMap",new Map),Zt(this,"nodeMetaMap",new WeakMap)}getId(e){var t;if(!e)return-1;var r=null==(t=this.getMeta(e))?void 0:t.id;return null!=r?r:-1}getNode(e){return this.idNodeMap.get(e)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(e){return this.nodeMetaMap.get(e)||null}removeNodeFromMap(e){var t=this.getId(e);this.idNodeMap.delete(t),e.childNodes&&e.childNodes.forEach((e=>this.removeNodeFromMap(e)))}has(e){return this.idNodeMap.has(e)}hasNode(e){return this.nodeMetaMap.has(e)}add(e,t){var r=t.id;this.idNodeMap.set(r,e),this.nodeMetaMap.set(e,t)}replace(e,t){var r=this.getNode(e);if(r){var i=this.nodeMetaMap.get(r);i&&this.nodeMetaMap.set(t,i)}this.idNodeMap.set(e,t)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}}function rr(e,t,r){if(!e)return!1;if(e.nodeType!==e.ELEMENT_NODE)return!!r&&rr(er.parentNode(e),t,r);for(var i=e.classList.length;i--;){var n=e.classList[i];if(t.test(n))return!0}return!!r&&rr(er.parentNode(e),t,r)}class ir{constructor(){__publicField22(this,"parentElement",null),__publicField22(this,"parentNode",null),__publicField22(this,"ownerDocument"),__publicField22(this,"firstChild",null),__publicField22(this,"lastChild",null),__publicField22(this,"previousSibling",null),__publicField22(this,"nextSibling",null),__publicField22(this,"ELEMENT_NODE",1),__publicField22(this,"TEXT_NODE",3),__publicField22(this,"nodeType"),__publicField22(this,"nodeName"),__publicField22(this,"RRNodeType")}get childNodes(){for(var e=[],t=this.firstChild;t;)e.push(t),t=t.nextSibling;return e}contains(e){if(!(e instanceof ir))return!1;if(e.ownerDocument!==this.ownerDocument)return!1;if(e===this)return!0;for(;e.parentNode;){if(e.parentNode===this)return!0;e=e.parentNode}return!1}appendChild(e){throw new Error("RRDomException: Failed to execute 'appendChild' on 'RRNode': This RRNode type does not support this method.")}insertBefore(e,t){throw new Error("RRDomException: Failed to execute 'insertBefore' on 'RRNode': This RRNode type does not support this method.")}removeChild(e){throw new Error("RRDomException: Failed to execute 'removeChild' on 'RRNode': This RRNode type does not support this method.")}toString(){return"RRNode"}}var nr={Node:["childNodes","parentNode","parentElement","textContent"],ShadowRoot:["host","styleSheets"],Element:["shadowRoot","querySelector","querySelectorAll"],MutationObserver:[]},sr={Node:["contains","getRootNode"],ShadowRoot:["getSelection"],Element:[],MutationObserver:["constructor"]},or={};function ar(e){if(or[e])return or[e];var t=globalThis[e],r=t.prototype,i=e in nr?nr[e]:void 0,n=Boolean(i&&i.every((e=>{var t,i;return Boolean(null==(i=null==(t=Object.getOwnPropertyDescriptor(r,e))?void 0:t.get)?void 0:i.toString().includes("[native code]"))}))),s=e in sr?sr[e]:void 0,o=Boolean(s&&s.every((e=>{var t;return"function"==typeof r[e]&&(null==(t=r[e])?void 0:t.toString().includes("[native code]"))})));if(n&&o)return or[e]=t.prototype,t.prototype;try{var a=document.createElement("iframe");document.body.appendChild(a);var l=a.contentWindow;if(!l)return t.prototype;var u=l[e].prototype;return document.body.removeChild(a),u?or[e]=u:r}catch(e){return r}}var lr={};function ur(e,t,r){var i,n=e+"."+String(r);if(lr[n])return lr[n].call(t);var s=ar(e),o=null==(i=Object.getOwnPropertyDescriptor(s,r))?void 0:i.get;return o?(lr[n]=o,o.call(t)):t[r]}var cr={};function dr(e,t,r){var i=e+"."+String(r);if(cr[i])return cr[i].bind(t);var n=ar(e)[r];return"function"!=typeof n?t[r]:(cr[i]=n,n.bind(t))}var hr={childNodes:function(e){return ur("Node",e,"childNodes")},parentNode:function(e){return ur("Node",e,"parentNode")},parentElement:function(e){return ur("Node",e,"parentElement")},textContent:function(e){return ur("Node",e,"textContent")},contains:function(e,t){return dr("Node",e,"contains")(t)},getRootNode:function(e){return dr("Node",e,"getRootNode")()},host:function(e){return e&&"host"in e?ur("ShadowRoot",e,"host"):null},styleSheets:function(e){return e.styleSheets},shadowRoot:function(e){return e&&"shadowRoot"in e?ur("Element",e,"shadowRoot"):null},querySelector:function(e,t){return ur("Element",e,"querySelector")(t)},querySelectorAll:function(e,t){return ur("Element",e,"querySelectorAll")(t)},mutationObserver:function(){return ar("MutationObserver").constructor}};var pr="Please stop import mirror directly. Instead of that,\r\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\r\nor you can use record.mirror to access the mirror instance during recording.",_r={map:{},getId:()=>(console.error(pr),-1),getNode:()=>(console.error(pr),null),removeNodeFromMap(){console.error(pr)},has:()=>(console.error(pr),!1),reset(){console.error(pr)}};"undefined"!=typeof window&&window.Proxy&&window.Reflect&&(_r=new Proxy(_r,{get:(e,t,r)=>("map"===t&&console.error(pr),Reflect.get(e,t,r))}));var gr=Date.now;function fr(e){return e?e.nodeType===e.ELEMENT_NODE?e:hr.parentElement(e):null}/[1-9][0-9]{12}/.test(Date.now().toString())||(gr=()=>(new Date).getTime());function vr(e){var t,r=null;return"getRootNode"in e&&(null==(t=hr.getRootNode(e))?void 0:t.nodeType)===Node.DOCUMENT_FRAGMENT_NODE&&hr.host(hr.getRootNode(e))&&(r=hr.host(hr.getRootNode(e))),r}function mr(e){for(var t,r=e;t=vr(r);)r=t;return r}function yr(e){var t=e.ownerDocument;if(!t)return!1;var r=mr(e);return hr.contains(t,r)}for(var br=Object.freeze(Object.defineProperty({__proto__:null,StyleSheetMirror:class{constructor(){Wt(this,"id",1),Wt(this,"styleIDMap",new WeakMap),Wt(this,"idStyleMap",new Map)}getId(e){var t;return null!==(t=this.styleIDMap.get(e))&&void 0!==t?t:-1}has(e){return this.styleIDMap.has(e)}add(e,t){return this.has(e)?this.getId(e):(r=void 0===t?this.id++:t,this.styleIDMap.set(e,r),this.idStyleMap.set(r,e),r);var r}getStyle(e){return this.idStyleMap.get(e)||null}reset(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}generateId(){return this.id++}},get _mirror(){return _r},closestElementOfNode:fr,getBaseDimension:function e(t,r){var i,n,s=null==(n=null==(i=t.ownerDocument)?void 0:i.defaultView)?void 0:n.frameElement;if(!s||s===r)return{x:0,y:0,relativeScale:1,absoluteScale:1};var o=s.getBoundingClientRect(),a=e(s,r),l=o.height/s.clientHeight;return{x:o.x*a.relativeScale+a.x,y:o.y*a.relativeScale+a.y,relativeScale:l,absoluteScale:a.absoluteScale*l}},getNestedRule:function e(t,r){var i=t[r[0]];return 1===r.length?i:e(i.cssRules[r[1]].cssRules,r.slice(2))},getPositionsAndIndex:function(e){var t=[...e],r=t.pop();return{positions:t,index:r}},getRootShadowHost:mr,getShadowHost:vr,getWindowHeight:function(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight},getWindowScroll:function(e){var t,r,i,n,s=e.document;return{left:s.scrollingElement?s.scrollingElement.scrollLeft:void 0!==e.pageXOffset?e.pageXOffset:s.documentElement.scrollLeft||(null==s?void 0:s.body)&&(null==(t=hr.parentElement(s.body))?void 0:t.scrollLeft)||(null==(r=null==s?void 0:s.body)?void 0:r.scrollLeft)||0,top:s.scrollingElement?s.scrollingElement.scrollTop:void 0!==e.pageYOffset?e.pageYOffset:(null==s?void 0:s.documentElement.scrollTop)||(null==s?void 0:s.body)&&(null==(i=hr.parentElement(s.body))?void 0:i.scrollTop)||(null==(n=null==s?void 0:s.body)?void 0:n.scrollTop)||0}},getWindowWidth:function(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth},hasShadowRoot:function(e){return!!e&&(e instanceof ir&&"shadowRoot"in e?Boolean(e.shadowRoot):Boolean(hr.shadowRoot(e)))},hookSetter:function e(t,r,i,n,s){void 0===s&&(s=window);var o=s.Object.getOwnPropertyDescriptor(t,r);return s.Object.defineProperty(t,r,n?i:{set(e){setTimeout((()=>{i.set.call(this,e)}),0),o&&o.set&&o.set.call(this,e)}}),()=>e(t,r,o||{},!0)},inDom:function(e){var t=e.ownerDocument;return!!t&&(hr.contains(t,e)||yr(e))},isAncestorRemoved:function e(t,r){if(n=(i=t)&&"host"in i&&"mode"in i&&er.host(i)||null,Boolean(n&&"shadowRoot"in n&&er.shadowRoot(n)===i))return!1;var i,n,s=r.getId(t);if(!r.has(s))return!0;var o=hr.parentNode(t);return(!o||o.nodeType!==t.DOCUMENT_NODE)&&(!o||e(o,r))},isBlocked:function(e,t,r,i){if(!e)return!1;var n=fr(e);if(!n)return!1;try{if("string"==typeof t){if(n.classList.contains(t))return!0;if(i&&null!==n.closest("."+t))return!0}else if(rr(n,t,i))return!0}catch(e){}if(r){if(n.matches(r))return!0;if(i&&null!==n.closest(r))return!0}return!1},isIgnored:function(e,t,r){return!("TITLE"!==e.tagName||!r.headTitleMutations)||-2===t.getId(e)},isSerialized:function(e,t){return-1!==t.getId(e)},isSerializedIframe:function(e,t){return Boolean("IFRAME"===e.nodeName&&t.getMeta(e))},isSerializedStylesheet:function(e,t){return Boolean("LINK"===e.nodeName&&e.nodeType===e.ELEMENT_NODE&&e.getAttribute&&"stylesheet"===e.getAttribute("rel")&&t.getMeta(e))},iterateResolveTree:function e(t,r){r(t.value);for(var i=t.children.length-1;i>=0;i--)e(t.children[i],r)},legacy_isTouchEvent:function(e){return Boolean(e.changedTouches)},get nowTimestamp(){return gr},on:function(e,t,r){void 0===r&&(r=document);var i={capture:!0,passive:!0};return r.addEventListener(e,t,i),()=>r.removeEventListener(e,t,i)},patch:function(e,t,r){try{if(!(t in e))return()=>{};var i=e[t],n=r(i);return"function"==typeof n&&(n.prototype=n.prototype||{},Object.defineProperties(n,{__rrweb_original__:{enumerable:!1,value:i}})),e[t]=n,()=>{e[t]=i}}catch(e){return()=>{}}},polyfill:function(e){void 0===e&&(e=window),"NodeList"in e&&!e.NodeList.prototype.forEach&&(e.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in e&&!e.DOMTokenList.prototype.forEach&&(e.DOMTokenList.prototype.forEach=Array.prototype.forEach)},queueToResolveTrees:function(e){var t={},r=(e,r)=>{var i={value:e,parent:r,children:[]};return t[e.node.id]=i,i},i=[];for(var n of e){var{nextId:s,parentId:o}=n;if(s&&s in t){var a=t[s];if(a.parent){var l=a.parent.children.indexOf(a);a.parent.children.splice(l,0,r(n,a.parent))}else{var u=i.indexOf(a);i.splice(u,0,r(n,null))}}else if(o in t){var c=t[o];c.children.push(r(n,c))}else i.push(r(n,null))}return i},shadowHostInDom:yr,throttle:function(e,t,r){void 0===r&&(r={});var i=null,n=0;return function(){for(var s=arguments.length,o=new Array(s),a=0;a<s;a++)o[a]=arguments[a];var l=Date.now();n||!1!==r.leading||(n=l);var u=t-(l-n),c=this;u<=0||u>t?(i&&(clearTimeout(i),i=null),n=l,e.apply(c,o)):i||!1===r.trailing||(i=setTimeout((()=>{n=!1===r.leading?0:Date.now(),i=null,e.apply(c,o)}),u))}},uniqueTextMutations:function(e){for(var t=new Set,r=[],i=e.length;i--;){var n=e[i];t.has(n.id)||(r.push(n),t.add(n.id))}return r}},Symbol.toStringTag,{value:"Module"})),Sr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",wr="undefined"==typeof Uint8Array?[]:new Uint8Array(256),Cr=0;Cr<64;Cr++)wr[Sr.charCodeAt(Cr)]=Cr;var Ir;"undefined"!=typeof window&&window.Blob&&new Blob([(e=>Uint8Array.from(atob(e),(e=>e.charCodeAt(0))))("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")],{type:"text/javascript;charset=utf-8"});try{if(2!==Array.from([1],(e=>2*e))[0]){var kr=document.createElement("iframe");document.body.appendChild(kr),Array.from=(null==(qt=kr.contentWindow)?void 0:qt.Array.from)||Array.from,document.body.removeChild(kr)}}catch(e){console.debug("Unable to override Array.from",e)}new tr,function(e){e[e.NotStarted=0]="NotStarted",e[e.Running=1]="Running",e[e.Stopped=2]="Stopped"}(Ir||(Ir={}));class Er{constructor(e){$t(this,"fileName"),$t(this,"functionName"),$t(this,"lineNumber"),$t(this,"columnNumber"),this.fileName=e.fileName||"",this.functionName=e.functionName||"",this.lineNumber=e.lineNumber,this.columnNumber=e.columnNumber}toString(){var e=this.lineNumber||"",t=this.columnNumber||"";return this.functionName?this.functionName+" ("+this.fileName+":"+e+":"+t+")":this.fileName+":"+e+":"+t}}var xr=/(^|@)\S+:\d+/,Tr=/^\s*at .*(\S+:\d+|\(native\))/m,Mr=/^(eval@)?(\[native code])?$/,Rr={parse:function(e){return e?void 0!==e.stacktrace||void 0!==e["opera#sourceloc"]?this.parseOpera(e):e.stack&&e.stack.match(Tr)?this.parseV8OrIE(e):e.stack?this.parseFFOrSafari(e):(console.warn("[console-record-plugin]: Failed to parse error object:",e),[]):[]},extractLocation:function(e){if(-1===e.indexOf(":"))return[e];var t=/(.+?)(?::(\d+))?(?::(\d+))?$/.exec(e.replace(/[()]/g,""));if(!t)throw new Error("Cannot parse given url: "+e);return[t[1],t[2]||void 0,t[3]||void 0]},parseV8OrIE:function(e){return e.stack.split("\n").filter((function(e){return!!e.match(Tr)}),this).map((function(e){e.indexOf("(eval ")>-1&&(e=e.replace(/eval code/g,"eval").replace(/(\(eval at [^()]*)|(\),.*$)/g,""));var t=e.replace(/^\s+/,"").replace(/\(eval code/g,"("),r=t.match(/ (\((.+):(\d+):(\d+)\)$)/),i=(t=r?t.replace(r[0],""):t).split(/\s+/).slice(1),n=this.extractLocation(r?r[1]:i.pop()),s=i.join(" ")||void 0,o=["eval","<anonymous>"].indexOf(n[0])>-1?void 0:n[0];return new Er({functionName:s,fileName:o,lineNumber:n[1],columnNumber:n[2]})}),this)},parseFFOrSafari:function(e){return e.stack.split("\n").filter((function(e){return!e.match(Mr)}),this).map((function(e){if(e.indexOf(" > eval")>-1&&(e=e.replace(/ line (\d+)(?: > eval line \d+)* > eval:\d+:\d+/g,":$1")),-1===e.indexOf("@")&&-1===e.indexOf(":"))return new Er({functionName:e});var t=/((.*".+"[^@]*)?[^@]*)(?:@)/,r=e.match(t),i=r&&r[1]?r[1]:void 0,n=this.extractLocation(e.replace(t,""));return new Er({functionName:i,fileName:n[0],lineNumber:n[1],columnNumber:n[2]})}),this)},parseOpera:function(e){return!e.stacktrace||e.message.indexOf("\n")>-1&&e.message.split("\n").length>e.stacktrace.split("\n").length?this.parseOpera9(e):e.stack?this.parseOpera11(e):this.parseOpera10(e)},parseOpera9:function(e){for(var t=/Line (\d+).*script (?:in )?(\S+)/i,r=e.message.split("\n"),i=[],n=2,s=r.length;n<s;n+=2){var o=t.exec(r[n]);o&&i.push(new Er({fileName:o[2],lineNumber:parseFloat(o[1])}))}return i},parseOpera10:function(e){for(var t=/Line (\d+).*script (?:in )?(\S+)(?:: In function (\S+))?$/i,r=e.stacktrace.split("\n"),i=[],n=0,s=r.length;n<s;n+=2){var o=t.exec(r[n]);o&&i.push(new Er({functionName:o[3]||void 0,fileName:o[2],lineNumber:parseFloat(o[1])}))}return i},parseOpera11:function(e){return e.stack.split("\n").filter((function(e){return!!e.match(xr)&&!e.match(/^Error created at/)}),this).map((function(e){var t=e.split("@"),r=this.extractLocation(t.pop()),i=(t.shift()||"").replace(/<anonymous function(: (\w+))?>/,"$2").replace(/\([^)]*\)/g,"")||void 0;return new Er({functionName:i,fileName:r[0],lineNumber:r[1],columnNumber:r[2]})}),this)}};function Ar(e){if(!e||!e.outerHTML)return"";for(var t="";e.parentElement;){var r=e.localName;if(!r)break;r=r.toLowerCase();var i=e.parentElement,n=[];if(i.children&&i.children.length>0)for(var s=0;s<i.children.length;s++){var o=i.children[s];o.localName&&o.localName.toLowerCase&&o.localName.toLowerCase()===r&&n.push(o)}n.length>1&&(r+=":eq("+n.indexOf(e)+")"),t=r+(t?">"+t:""),e=i}return t}function Fr(e){return"[object Object]"===Object.prototype.toString.call(e)}function Nr(e,t){if(0===t)return!0;var r=Object.keys(e);for(var i of r)if(Fr(e[i])&&Nr(e[i],t-1))return!0;return!1}function Pr(e,t){var r={numOfKeysLimit:50,depthOfLimit:4};Object.assign(r,t);var i=[],n=[];return JSON.stringify(e,(function(e,t){if(i.length>0){var s=i.indexOf(this);~s?i.splice(s+1):i.push(this),~s?n.splice(s,1/0,e):n.push(e),~i.indexOf(t)&&(t=i[0]===t?"[Circular ~]":"[Circular ~."+n.slice(0,i.indexOf(t)).join(".")+"]")}else i.push(t);if(null===t)return t;if(void 0===t)return"undefined";if(function(e){if(Fr(e)&&Object.keys(e).length>r.numOfKeysLimit)return!0;if("function"==typeof e)return!0;if(Fr(e)&&Nr(e,r.depthOfLimit))return!0;return!1}(t))return function(e){var t=e.toString();r.stringLengthLimit&&t.length>r.stringLengthLimit&&(t=t.slice(0,r.stringLengthLimit)+"...");return t}(t);if("bigint"==typeof t)return t.toString()+"n";if(t instanceof Event){var o={};for(var a in t){var l=t[a];Array.isArray(l)?o[a]=Ar(l.length?l[0]:null):o[a]=l}return o}return t instanceof Node?t instanceof HTMLElement?t?t.outerHTML:"":t.nodeName:t instanceof Error?t.stack?t.stack+"\nEnd of stack for Error object":t.name+": "+t.message:t}))}var Or={level:["assert","clear","count","countReset","debug","dir","dirxml","error","group","groupCollapsed","groupEnd","info","log","table","time","timeEnd","timeLog","trace","warn"],lengthThreshold:1e3,logger:"console"};function Lr(e,t,r){var i,n=r?Object.assign({},Or,r):Or,s=n.logger;if(!s)return()=>{};i="string"==typeof s?t[s]:s;var o=0,a=!1,l=[];if(n.level.includes("error")){var u=t=>{var r=t.message,i=t.error,s=Rr.parse(i).map((e=>e.toString())),o=[Pr(r,n.stringifyOptions)];e({level:"error",trace:s,payload:o})};t.addEventListener("error",u),l.push((()=>{t.removeEventListener("error",u)}));var c=t=>{var r,i;t.reason instanceof Error?i=[Pr("Uncaught (in promise) "+(r=t.reason).name+": "+r.message,n.stringifyOptions)]:(r=new Error,i=[Pr("Uncaught (in promise)",n.stringifyOptions),Pr(t.reason,n.stringifyOptions)]);var s=Rr.parse(r).map((e=>e.toString()));e({level:"error",trace:s,payload:i})};t.addEventListener("unhandledrejection",c),l.push((()=>{t.removeEventListener("unhandledrejection",c)}))}for(var d of n.level)l.push(h(i,d));return()=>{l.forEach((e=>e()))};function h(t,r){var i=this;return t[r]?br.patch(t,r,(t=>function(){for(var s=arguments.length,l=new Array(s),u=0;u<s;u++)l[u]=arguments[u];if(t.apply(i,l),!("assert"===r&&l[0]||a)){a=!0;try{var c=Rr.parse(new Error).map((e=>e.toString())).splice(1),d=("assert"===r?l.slice(1):l).map((e=>Pr(e,n.stringifyOptions)));++o<n.lengthThreshold?e({level:r,trace:c,payload:d}):o===n.lengthThreshold&&e({level:"warn",trace:[],payload:[Pr("The number of log records reached the threshold.")]})}catch(e){t("rrweb logger error:",e,...l)}finally{a=!1}}})):()=>{}}}var Dr=e=>({name:"rrweb/console@1",observer:Lr,options:e}),Br="undefined"!=typeof window?window:void 0,qr="undefined"!=typeof globalThis?globalThis:Br,Hr=Array.prototype,$r=Hr.forEach,Vr=Hr.indexOf,Wr=null==qr?void 0:qr.navigator,Ur=null==qr?void 0:qr.document,Zr=null==qr?void 0:qr.location,zr=null==qr?void 0:qr.fetch,Gr=null!=qr&&qr.XMLHttpRequest&&"withCredentials"in new qr.XMLHttpRequest?qr.XMLHttpRequest:void 0,jr=null==qr?void 0:qr.AbortController,Yr=null==Wr?void 0:Wr.userAgent,Xr=null!=Br?Br:{},Jr="$copy_autocapture",Kr=["$snapshot","$pageview","$pageleave","$set","survey dismissed","survey sent","survey shown","$identify","$groupidentify","$create_alias","$$client_ingestion_warning","$web_experiment_applied","$feature_enrollment_update","$feature_flag_called"],Qr=function(e){return e.GZipJS="gzip-js",e.Base64="base64",e}({}),ei=["fatal","error","warning","log","info","debug"];function ti(e,t){return-1!==e.indexOf(t)}var ri=function(e){return e.trim()},ii=function(e){return e.replace(/^\$/,"")};var ni=Array.isArray,si=Object.prototype,oi=si.hasOwnProperty,ai=si.toString,li=ni||function(e){return"[object Array]"===ai.call(e)},ui=e=>"function"==typeof e,ci=e=>ui(e)&&-1!==e.toString().indexOf("[native code]"),di=()=>!!Br.Zone,hi=e=>e===Object(e)&&!li(e),pi=e=>{if(hi(e)){for(var t in e)if(oi.call(e,t))return!1;return!0}return!1},_i=e=>void 0===e,gi=e=>"[object String]"==ai.call(e),fi=e=>gi(e)&&0===e.trim().length,vi=e=>null===e,mi=e=>_i(e)||vi(e),yi=e=>"[object Number]"==ai.call(e),bi=e=>"[object Boolean]"===ai.call(e),Si=e=>e instanceof Document,wi=e=>e instanceof FormData,Ci=e=>ti(Kr,e),Ii={DEBUG:!1,LIB_VERSION:"1.255.1"},ki=e=>{var t={_log:function(t){if(Br&&(Ii.DEBUG||Xr.POSTHOG_DEBUG)&&!_i(Br.console)&&Br.console){for(var r=("__rrweb_original__"in Br.console[t]?Br.console[t].__rrweb_original__:Br.console[t]),i=arguments.length,n=new Array(i>1?i-1:0),s=1;s<i;s++)n[s-1]=arguments[s];r(e,...n)}},info:function(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];t._log("log",...r)},warn:function(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];t._log("warn",...r)},error:function(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];t._log("error",...r)},critical:function(){for(var t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];console.error(e,...r)},uninitializedWarning:e=>{t.error("You must initialize PostHog before calling "+e)},createLogger:t=>ki(e+" "+t)};return t},Ei=ki("[PostHog.js]"),xi=Ei.createLogger,Ti={};function Mi(e,t,r){if(li(e))if($r&&e.forEach===$r)e.forEach(t,r);else if("length"in e&&e.length===+e.length)for(var i=0,n=e.length;i<n;i++)if(i in e&&t.call(r,e[i],i)===Ti)return}function Ri(e,t,r){if(!mi(e)){if(li(e))return Mi(e,t,r);if(wi(e)){for(var i of e.entries())if(t.call(r,i[1],i[0])===Ti)return}else for(var n in e)if(oi.call(e,n)&&t.call(r,e[n],n)===Ti)return}}var Ai=function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];return Mi(r,(function(t){for(var r in t)void 0!==t[r]&&(e[r]=t[r])})),e},Fi=function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];return Mi(r,(function(t){Mi(t,(function(t){e.push(t)}))})),e};function Ni(e){for(var t=Object.keys(e),r=t.length,i=new Array(r);r--;)i[r]=[t[r],e[t[r]]];return i}var Pi=function(e){try{return e()}catch(e){return}},Oi=function(e){return function(){try{for(var t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];return e.apply(this,r)}catch(e){Ei.critical("Implementation error. Please turn on debug mode and open a ticket on https://app.posthog.com/home#panel=support%3Asupport%3A."),Ei.critical(e)}}},Li=function(e){var t={};return Ri(e,(function(e,r){(gi(e)&&e.length>0||yi(e))&&(t[r]=e)})),t};function Di(e,t){return r=e,i=e=>gi(e)&&!vi(t)?e.slice(0,t):e,n=new Set,function e(t,r){return t!==Object(t)?i?i(t,r):t:n.has(t)?void 0:(n.add(t),li(t)?(s=[],Mi(t,(t=>{s.push(e(t))}))):(s={},Ri(t,((t,r)=>{n.has(t)||(s[r]=e(t,r))}))),s);var s}(r);var r,i,n}var Bi=["herokuapp.com","vercel.app","netlify.app"];function qi(e){var t=null==e?void 0:e.hostname;if(!gi(t))return!1;var r=t.split(".").slice(-2).join(".");for(var i of Bi)if(r===i)return!1;return!0}function Hi(e,t){for(var r=0;r<e.length;r++)if(t(e[r]))return e[r]}function $i(e,t,r,i){var{capture:n=!1,passive:s=!0}=null!=i?i:{};null==e||e.addEventListener(t,r,{capture:n,passive:s})}var Vi=["localhost","127.0.0.1"],Wi=e=>{var t=null==Ur?void 0:Ur.createElement("a");return _i(t)?null:(t.href=e,t)},Ui=function(e,t){var r,i;void 0===t&&(t="&");var n=[];return Ri(e,(function(e,t){_i(e)||_i(t)||"undefined"===t||(r=encodeURIComponent((e=>e instanceof File)(e)?e.name:e.toString()),i=encodeURIComponent(t),n[n.length]=i+"="+r)})),n.join(t)},Zi=function(e,t){for(var r,i=((e.split("#")[0]||"").split(/\?(.*)/)[1]||"").replace(/^\?+/g,"").split("&"),n=0;n<i.length;n++){var s=i[n].split("=");if(s[0]===t){r=s;break}}if(!li(r)||r.length<2)return"";var o=r[1];try{o=decodeURIComponent(o)}catch(e){Ei.error("Skipping decoding for malformed query param: "+o)}return o.replace(/\+/g," ")},zi=function(e,t,r){if(!e||!t||!t.length)return e;for(var i=e.split("#"),n=i[0]||"",s=i[1],o=n.split("?"),a=o[1],l=o[0],u=(a||"").split("&"),c=[],d=0;d<u.length;d++){var h=u[d].split("=");li(h)&&(t.includes(h[0])?c.push(h[0]+"="+r):c.push(u[d]))}var p=l;return null!=a&&(p+="?"+c.join("&")),null!=s&&(p+="#"+s),p},Gi=function(e,t){var r=e.match(new RegExp(t+"=([^&]*)"));return r?r[1]:null};function ji(e,t,r){try{if(!(t in e))return()=>{};var i=e[t],n=r(i);return ui(n)&&(n.prototype=n.prototype||{},Object.defineProperties(n,{__posthog_wrapped__:{enumerable:!1,value:!0}})),e[t]=n,()=>{e[t]=i}}catch(e){return()=>{}}}function Yi(e,t){var r,i=function(e){try{return"string"==typeof e?new URL(e).hostname:"url"in e?new URL(e.url).hostname:e.hostname}catch(e){return null}}(e),n={hostname:i,isHostDenied:!1};if(null==(r=t.payloadHostDenyList)||!r.length||null==i||!i.trim().length)return n;for(var s of t.payloadHostDenyList)if(i.endsWith(s))return{hostname:i,isHostDenied:!0};return n}var Xi="$people_distinct_id",Ji="__alias",Ki="__timers",Qi="$autocapture_disabled_server_side",en="$heatmaps_enabled_server_side",tn="$exception_capture_enabled_server_side",rn="$error_tracking_suppression_rules",nn="$web_vitals_enabled_server_side",sn="$dead_clicks_enabled_server_side",on="$web_vitals_allowed_metrics",an="$session_recording_enabled_server_side",ln="$console_log_recording_enabled_server_side",un="$session_recording_network_payload_capture",cn="$session_recording_masking",dn="$session_recording_canvas_recording",hn="$replay_sample_rate",pn="$replay_minimum_duration",_n="$replay_script_config",gn="$sesid",fn="$session_is_sampled",vn="$session_recording_url_trigger_activated_session",mn="$session_recording_event_trigger_activated_session",yn="$enabled_feature_flags",bn="$early_access_features",Sn="$feature_flag_details",wn="$stored_person_properties",Cn="$stored_group_properties",In="$surveys",kn="$surveys_activated",En="$flag_call_reported",xn="$user_state",Tn="$client_session_props",Mn="$capture_rate_limit",Rn="$initial_campaign_params",An="$initial_referrer_info",Fn="$initial_person_info",Nn="$epp",Pn="__POSTHOG_TOOLBAR__",On="$posthog_cookieless",Ln=[Xi,Ji,"__cmpns",Ki,an,en,gn,yn,rn,xn,bn,Sn,Cn,wn,In,En,Tn,Mn,Rn,An,Nn,Fn];function Dn(e){return e instanceof Element&&(e.id===Pn||!(null==e.closest||!e.closest(".toolbar-global-fade-container")))}function Bn(e){return!!e&&1===e.nodeType}function qn(e,t){return!!e&&!!e.tagName&&e.tagName.toLowerCase()===t.toLowerCase()}function Hn(e){return!!e&&3===e.nodeType}function $n(e){return!!e&&11===e.nodeType}function Vn(e){return e?ri(e).split(/\s+/):[]}function Wn(e){var t=null==Br?void 0:Br.location.href;return!!(t&&e&&e.some((e=>t.match(e))))}function Un(e){var t="";switch(typeof e.className){case"string":t=e.className;break;case"object":t=(e.className&&"baseVal"in e.className?e.className.baseVal:null)||e.getAttribute("class")||"";break;default:t=""}return Vn(t)}function Zn(e){return mi(e)?null:ri(e).split(/(\s+)/).filter((e=>ss(e))).join("").replace(/[\r\n]/g," ").replace(/[ ]+/g," ").substring(0,255)}function zn(e){var t="";return Jn(e)&&!Kn(e)&&e.childNodes&&e.childNodes.length&&Ri(e.childNodes,(function(e){var r;Hn(e)&&e.textContent&&(t+=null!==(r=Zn(e.textContent))&&void 0!==r?r:"")})),ri(t)}function Gn(e){return _i(e.target)?e.srcElement||null:null!=(t=e.target)&&t.shadowRoot?e.composedPath()[0]||null:e.target||null;var t}var jn=["a","button","form","input","select","textarea","label"];function Yn(e){var t=e.parentNode;return!(!t||!Bn(t))&&t}function Xn(e,t,r,i,n){var s,o,a;if(void 0===r&&(r=void 0),!Br||!e||qn(e,"html")||!Bn(e))return!1;if(null!=(s=r)&&s.url_allowlist&&!Wn(r.url_allowlist))return!1;if(null!=(o=r)&&o.url_ignorelist&&Wn(r.url_ignorelist))return!1;if(null!=(a=r)&&a.dom_event_allowlist){var l=r.dom_event_allowlist;if(l&&!l.some((e=>t.type===e)))return!1}for(var u=!1,c=[e],d=!0,h=e;h.parentNode&&!qn(h,"body");)if($n(h.parentNode))c.push(h.parentNode.host),h=h.parentNode.host;else{if(!(d=Yn(h)))break;if(i||jn.indexOf(d.tagName.toLowerCase())>-1)u=!0;else{var p=Br.getComputedStyle(d);p&&"pointer"===p.getPropertyValue("cursor")&&(u=!0)}c.push(d),h=d}if(!function(e,t){var r=null==t?void 0:t.element_allowlist;if(_i(r))return!0;var i,n=function(e){if(r.some((t=>e.tagName.toLowerCase()===t)))return{v:!0}};for(var s of e)if(i=n(s))return i.v;return!1}(c,r))return!1;if(!function(e,t){var r=null==t?void 0:t.css_selector_allowlist;if(_i(r))return!0;var i,n=function(e){if(r.some((t=>e.matches(t))))return{v:!0}};for(var s of e)if(i=n(s))return i.v;return!1}(c,r))return!1;var _=Br.getComputedStyle(e);if(_&&"pointer"===_.getPropertyValue("cursor")&&"click"===t.type)return!0;var g=e.tagName.toLowerCase();switch(g){case"html":return!1;case"form":return(n||["submit"]).indexOf(t.type)>=0;case"input":case"select":case"textarea":return(n||["change","click"]).indexOf(t.type)>=0;default:return u?(n||["click"]).indexOf(t.type)>=0:(n||["click"]).indexOf(t.type)>=0&&(jn.indexOf(g)>-1||"true"===e.getAttribute("contenteditable"))}}function Jn(e){for(var t=e;t.parentNode&&!qn(t,"body");t=t.parentNode){var r=Un(t);if(ti(r,"ph-sensitive")||ti(r,"ph-no-capture"))return!1}if(ti(Un(e),"ph-include"))return!0;var i=e.type||"";if(gi(i))switch(i.toLowerCase()){case"hidden":case"password":return!1}var n=e.name||e.id||"";if(gi(n)){if(/^cc|cardnum|ccnum|creditcard|csc|cvc|cvv|exp|pass|pwd|routing|seccode|securitycode|securitynum|socialsec|socsec|ssn/i.test(n.replace(/[^a-zA-Z0-9]/g,"")))return!1}return!0}function Kn(e){return!!(qn(e,"input")&&!["button","checkbox","submit","reset"].includes(e.type)||qn(e,"select")||qn(e,"textarea")||"true"===e.getAttribute("contenteditable"))}var Qn="(4[0-9]{12}(?:[0-9]{3})?)|(5[1-5][0-9]{14})|(6(?:011|5[0-9]{2})[0-9]{12})|(3[47][0-9]{13})|(3(?:0[0-5]|[68][0-9])[0-9]{11})|((?:2131|1800|35[0-9]{3})[0-9]{11})",es=new RegExp("^(?:"+Qn+")$"),ts=new RegExp(Qn),rs="\\d{3}-?\\d{2}-?\\d{4}",is=new RegExp("^("+rs+")$"),ns=new RegExp("("+rs+")");function ss(e,t){if(void 0===t&&(t=!0),mi(e))return!1;if(gi(e)){if(e=ri(e),(t?es:ts).test((e||"").replace(/[- ]/g,"")))return!1;if((t?is:ns).test(e))return!1}return!0}function os(e){var t=zn(e);return ss(t=(t+" "+as(e)).trim())?t:""}function as(e){var t="";return e&&e.childNodes&&e.childNodes.length&&Ri(e.childNodes,(function(e){var r;if(e&&"span"===(null==(r=e.tagName)?void 0:r.toLowerCase()))try{var i=zn(e);t=(t+" "+i).trim(),e.childNodes&&e.childNodes.length&&(t=(t+" "+as(e)).trim())}catch(e){Ei.error("[AutoCapture]",e)}})),t}function ls(e){return function(e){var t=e.map((e=>{var t,i,n="";if(e.tag_name&&(n+=e.tag_name),e.attr_class)for(var s of(e.attr_class.sort(),e.attr_class))n+="."+s.replace(/"/g,"");var o=r({},e.text?{text:e.text}:{},{"nth-child":null!==(t=e.nth_child)&&void 0!==t?t:0,"nth-of-type":null!==(i=e.nth_of_type)&&void 0!==i?i:0},e.href?{href:e.href}:{},e.attr_id?{attr_id:e.attr_id}:{},e.attributes),a={};return Ni(o).sort(((e,t)=>{var[r]=e,[i]=t;return r.localeCompare(i)})).forEach((e=>{var[t,r]=e;return a[us(t.toString())]=us(r.toString())})),n+=":",n+=Ni(a).map((e=>{var[t,r]=e;return t+'="'+r+'"'})).join("")}));return t.join(";")}(function(e){return e.map((e=>{var t,r,i={text:null==(t=e.$el_text)?void 0:t.slice(0,400),tag_name:e.tag_name,href:null==(r=e.attr__href)?void 0:r.slice(0,2048),attr_class:cs(e),attr_id:e.attr__id,nth_child:e.nth_child,nth_of_type:e.nth_of_type,attributes:{}};return Ni(e).filter((e=>{var[t]=e;return 0===t.indexOf("attr__")})).forEach((e=>{var[t,r]=e;return i.attributes[t]=r})),i}))}(e))}function us(e){return e.replace(/"|\\"/g,'\\"')}function cs(e){var t=e.attr__class;return t?li(t)?t:Vn(t):void 0}var ds="[SessionRecording]",hs="redacted",ps={initiatorTypes:["audio","beacon","body","css","early-hint","embed","fetch","frame","iframe","icon","image","img","input","link","navigation","object","ping","script","track","video","xmlhttprequest"],maskRequestFn:e=>e,recordHeaders:!1,recordBody:!1,recordInitialRequests:!1,recordPerformance:!1,performanceEntryTypeToObserve:["first-input","navigation","paint","resource"],payloadSizeLimitBytes:1e6,payloadHostDenyList:[".lr-ingest.io",".ingest.sentry.io",".clarity.ms","analytics.google.com","bam.nr-data.net"]},_s=["authorization","x-forwarded-for","authorization","cookie","set-cookie","x-api-key","x-real-ip","remote-addr","forwarded","proxy-authorization","x-csrf-token","x-csrftoken","x-xsrf-token"],gs=["password","secret","passwd","api_key","apikey","auth","credentials","mysql_pwd","privatekey","private_key","token"],fs=["/s/","/e/","/i/"];function vs(e,t,r,i){if(mi(e))return e;var n=(null==t?void 0:t["content-length"])||function(e){return new Blob([e]).size}(e);return gi(n)&&(n=parseInt(n)),n>r?ds+" "+i+" body too large to record ("+n+" bytes)":e}function ms(e,t){if(mi(e))return e;var r=e;return ss(r,!1)||(r=ds+" "+t+" body "+hs),Ri(gs,(e=>{var i,n;null!=(i=r)&&i.length&&-1!==(null==(n=r)?void 0:n.indexOf(e))&&(r=ds+" "+t+" body "+hs+" as might contain: "+e)})),r}var ys=(e,t)=>{var i,n,s,o={payloadSizeLimitBytes:ps.payloadSizeLimitBytes,performanceEntryTypeToObserve:[...ps.performanceEntryTypeToObserve],payloadHostDenyList:[...t.payloadHostDenyList||[],...ps.payloadHostDenyList]},a=!1!==e.session_recording.recordHeaders&&t.recordHeaders,l=!1!==e.session_recording.recordBody&&t.recordBody,u=!1!==e.capture_performance&&t.recordPerformance,c=(i=o,s=Math.min(1e6,null!==(n=i.payloadSizeLimitBytes)&&void 0!==n?n:1e6),e=>(null!=e&&e.requestBody&&(e.requestBody=vs(e.requestBody,e.requestHeaders,s,"Request")),null!=e&&e.responseBody&&(e.responseBody=vs(e.responseBody,e.responseHeaders,s,"Response")),e)),d=t=>{return c(((e,t)=>{var r,i=Wi(e.name),n=0===t.indexOf("http")?null==(r=Wi(t))?void 0:r.pathname:t;"/"===n&&(n="");var s=null==i?void 0:i.pathname.replace(n||"","");if(!(i&&s&&fs.some((e=>0===s.indexOf(e)))))return e})((i=(r=t).requestHeaders,mi(i)||Ri(Object.keys(null!=i?i:{}),(e=>{_s.includes(e.toLowerCase())&&(i[e]=hs)})),r),e.api_host));var r,i},h=ui(e.session_recording.maskNetworkRequestFn);return h&&ui(e.session_recording.maskCapturedNetworkRequestFn)&&Ei.warn("Both `maskNetworkRequestFn` and `maskCapturedNetworkRequestFn` are defined. `maskNetworkRequestFn` will be ignored."),h&&(e.session_recording.maskCapturedNetworkRequestFn=t=>{var i=e.session_recording.maskNetworkRequestFn({url:t.name});return r({},t,{name:null==i?void 0:i.url})}),o.maskRequestFn=ui(e.session_recording.maskCapturedNetworkRequestFn)?t=>{var r,i=d(t);return i&&null!==(r=null==e.session_recording.maskCapturedNetworkRequestFn?void 0:e.session_recording.maskCapturedNetworkRequestFn(i))&&void 0!==r?r:void 0}:e=>function(e){if(!_i(e))return e.requestBody=ms(e.requestBody,"Request"),e.responseBody=ms(e.responseBody,"Response"),e}(d(e)),r({},ps,o,{recordHeaders:a,recordBody:l,recordPerformance:u,recordInitialRequests:u})},bs=xi("[Recorder]"),Ss=e=>"navigation"===e.entryType,ws=e=>"resource"===e.entryType;function Cs(e,t,r){if(r.recordInitialRequests){var i=t.performance.getEntries().filter((e=>Ss(e)||ws(e)&&r.initiatorTypes.includes(e.initiatorType)));e({requests:i.flatMap((e=>Rs({entry:e,method:void 0,status:void 0,networkRequest:{},isInitial:!0}))),isInitial:!0})}var n=new t.PerformanceObserver((t=>{var i=t.getEntries().filter((e=>Ss(e)||ws(e)&&r.initiatorTypes.includes(e.initiatorType)&&(e=>!r.recordBody&&!r.recordHeaders||"xmlhttprequest"!==e.initiatorType&&"fetch"!==e.initiatorType)(e)));e({requests:i.flatMap((e=>Rs({entry:e,method:void 0,status:void 0,networkRequest:{}})))})})),s=PerformanceObserver.supportedEntryTypes.filter((e=>r.performanceEntryTypeToObserve.includes(e)));return n.observe({entryTypes:s}),()=>{n.disconnect()}}function Is(e,t){return!!t&&(bi(t)||t[e])}function ks(e){var{type:t,recordBody:r,headers:i,url:n}=e;function s(e){var t=Object.keys(i).find((e=>"content-type"===e.toLowerCase())),r=t&&i[t];return e.some((e=>null==r?void 0:r.includes(e)))}if(!r)return!1;if(function e(t){try{return"string"==typeof t?t.startsWith("blob:"):t instanceof URL?"blob:"===t.protocol:t instanceof Request&&e(t.url)}catch(e){return!1}}(n))return!1;if(bi(r))return!0;if(li(r))return s(r);var o=r[t];return bi(o)?o:s(o)}function Es(e,t,r,i,n,s){return xs.apply(this,arguments)}function xs(){return xs=t((function*(e,t,r,i,n,s){if(void 0===s&&(s=0),s>10)return bs.warn("Failed to get performance entry for request",{url:r,initiatorType:t}),null;var o=function(e,t){for(var r=e.length-1;r>=0;r-=1)if(t(e[r]))return e[r]}(e.performance.getEntriesByName(r),(e=>ws(e)&&e.initiatorType===t&&(_i(i)||e.startTime>=i)&&(_i(n)||e.startTime<=n)));return o||(yield new Promise((e=>setTimeout(e,50*s))),Es(e,t,r,i,n,s+1))})),xs.apply(this,arguments)}function Ts(e){var{body:t,options:r,url:i}=e;if(mi(t))return null;var{hostname:n,isHostDenied:s}=Yi(i,r);if(s)return n+" is in deny list";if(gi(t))return t;if(Si(t))return t.textContent;if(wi(t))return Ui(t);if(hi(t))try{return JSON.stringify(t)}catch(e){return"[SessionReplay] Failed to stringify response object"}return"[SessionReplay] Cannot read body of type "+toString.call(t)}var Ms=e=>!vi(e)&&("navigation"===e.entryType||"resource"===e.entryType);function Rs(e){var{entry:t,method:i,status:n,networkRequest:s,isInitial:o,start:a,end:l,url:u,initiatorType:c}=e;a=t?t.startTime:a,l=t?t.responseEnd:l;var d=Math.floor(Date.now()-performance.now()),h=Math.floor(d+(a||0)),p=[r({},t?t.toJSON():{name:u},{startTime:_i(a)?void 0:Math.round(a),endTime:_i(l)?void 0:Math.round(l),timeOrigin:d,timestamp:h,method:i,initiatorType:c||(t?t.initiatorType:void 0),status:n,requestHeaders:s.requestHeaders,requestBody:s.requestBody,responseHeaders:s.responseHeaders,responseBody:s.responseBody,isInitial:o})];if(Ms(t))for(var _ of t.serverTiming||[])p.push({timeOrigin:d,timestamp:h,startTime:Math.round(t.startTime),name:_.name,duration:_.duration,entryType:"serverTiming"});return p}var As=["video/","audio/"];function Fs(e){return new Promise(((t,r)=>{var i=setTimeout((()=>t("[SessionReplay] Timeout while trying to read body")),500);try{e.clone().text().then((e=>t(e)),(e=>r(e))).finally((()=>clearTimeout(i)))}catch(e){clearTimeout(i),t("[SessionReplay] Failed to read body")}}))}function Ns(){return Ns=t((function*(e){var{r:t,options:r,url:i}=e,{hostname:n,isHostDenied:s}=Yi(i,r);return s?Promise.resolve(n+" is in deny list"):Fs(t)})),Ns.apply(this,arguments)}function Ps(){return Ps=t((function*(e){var{r:t,options:r,url:i}=e,n=function(e){var t,{r:r,options:i,url:n}=e;if("chunked"===r.headers.get("Transfer-Encoding"))return"Chunked Transfer-Encoding is not supported";var s=null==(t=r.headers.get("Content-Type"))?void 0:t.toLowerCase(),o=As.some((e=>null==s?void 0:s.startsWith(e)));if(s&&o)return"Content-Type "+s+" is not supported";var{hostname:a,isHostDenied:l}=Yi(n,i);return l?a+" is in deny list":null}({r:t,options:r,url:i});return vi(n)?Fs(t):Promise.resolve(n)})),Ps.apply(this,arguments)}function Os(e,r,i){if(!i.initiatorTypes.includes("fetch"))return()=>{};var n=Is("request",i.recordHeaders),s=Is("response",i.recordHeaders),o=ji(r,"fetch",(o=>function(){var a=t((function*(t,a){var l,u,c,d=new Request(t,a),h={};try{var p={};d.headers.forEach(((e,t)=>{p[t]=e})),n&&(h.requestHeaders=p),ks({type:"request",headers:p,url:t,recordBody:i.recordBody})&&(h.requestBody=yield function(e){return Ns.apply(this,arguments)}({r:d,options:i,url:t})),u=r.performance.now(),l=yield o(d),c=r.performance.now();var _={};return l.headers.forEach(((e,t)=>{_[t]=e})),s&&(h.responseHeaders=_),ks({type:"response",headers:_,url:t,recordBody:i.recordBody})&&(h.responseBody=yield function(e){return Ps.apply(this,arguments)}({r:l,options:i,url:t})),l}finally{Es(r,"fetch",d.url,u,c).then((t=>{var r,i=Rs({entry:t,method:d.method,status:null==(r=l)?void 0:r.status,networkRequest:h,start:u,end:c,url:d.url,initiatorType:"fetch"});e({requests:i})})).catch((()=>{}))}}));return function(e,t){return a.apply(this,arguments)}}()));return()=>{o()}}var Ls=null;function Ds(e,t,i){if(!("performance"in t))return()=>{};if(Ls)return bs.warn("Network observer already initialised, doing nothing"),()=>{};var n=i?Object.assign({},ps,i):ps,s=t=>{var i=[];t.requests.forEach((e=>{var t=n.maskRequestFn(e);t&&i.push(t)})),i.length>0&&e(r({},t,{requests:i}))},o=Cs(s,t,n),a=()=>{},l=()=>{};return(n.recordHeaders||n.recordBody)&&(a=function(e,t,r){if(!r.initiatorTypes.includes("xmlhttprequest"))return()=>{};var i=Is("request",r.recordHeaders),n=Is("response",r.recordHeaders),s=ji(t.XMLHttpRequest.prototype,"open",(s=>function(o,a,l,u,c){void 0===l&&(l=!0);var d,h,p=this,_=new Request(a),g={},f={},v=p.setRequestHeader.bind(p);p.setRequestHeader=(e,t)=>(f[e]=t,v(e,t)),i&&(g.requestHeaders=f);var m=p.send.bind(p);p.send=e=>(ks({type:"request",headers:f,url:a,recordBody:r.recordBody})&&(g.requestBody=Ts({body:e,options:r,url:a})),d=t.performance.now(),m(e)),p.addEventListener("readystatechange",(()=>{if(p.readyState===p.DONE){h=t.performance.now();var i={};p.getAllResponseHeaders().trim().split(/[\r\n]+/).forEach((e=>{var t=e.split(": "),r=t.shift(),n=t.join(": ");r&&(i[r]=n)})),n&&(g.responseHeaders=i),ks({type:"response",headers:i,url:a,recordBody:r.recordBody})&&(g.responseBody=Ts({body:p.response,options:r,url:a})),Es(t,"xmlhttprequest",_.url,d,h).then((t=>{var r=Rs({entry:t,method:o,status:null==p?void 0:p.status,networkRequest:g,start:d,end:h,url:a.toString(),initiatorType:"xmlhttprequest"});e({requests:r})})).catch((()=>{}))}})),s.call(p,o,a,l,u,c)}));return()=>{s()}}(s,t,n),l=Os(s,t,n)),Ls=()=>{o(),a(),l()}}var Bs=e=>({name:"rrweb/network@1",observer:Ds,options:e});Xr.__PosthogExtensions__=Xr.__PosthogExtensions__||{},Xr.__PosthogExtensions__.rrwebPlugins={getRecordConsolePlugin:Dr,getRecordNetworkPlugin:Bs},Xr.__PosthogExtensions__.rrweb={record:Bt,version:"v2"},Xr.rrweb={record:Bt,version:"v2"},Xr.rrwebConsoleRecord={getRecordConsolePlugin:Dr},Xr.getRecordNetworkPlugin=Bs;var qs,Hs,$s,Vs,Ws,Us,Zs,zs,Gs={},js=[],Ys=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,Xs=Array.isArray;function Js(e,t){for(var r in t)e[r]=t[r];return e}function Ks(e){var t=e.parentNode;t&&t.removeChild(e)}function Qs(e,t,r){var i,n,s,o={};for(s in t)"key"==s?i=t[s]:"ref"==s?n=t[s]:o[s]=t[s];if(arguments.length>2&&(o.children=arguments.length>3?qs.call(arguments,2):r),"function"==typeof e&&null!=e.defaultProps)for(s in e.defaultProps)void 0===o[s]&&(o[s]=e.defaultProps[s]);return eo(e,o,i,n,null)}function eo(e,t,r,i,n){var s={type:e,props:t,key:r,ref:i,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==n?++$s:n,__i:-1,__u:0};return null==n&&null!=Hs.vnode&&Hs.vnode(s),s}function to(e){return e.children}function ro(e,t){this.props=e,this.context=t}function io(e,t){if(null==t)return e.__?io(e.__,e.__i+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?io(e):null}function no(e){var t,r;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e){e.__e=e.__c.base=r.__e;break}return no(e)}}function so(e){(!e.__d&&(e.__d=!0)&&Vs.push(e)&&!oo.__r++||Ws!==Hs.debounceRendering)&&((Ws=Hs.debounceRendering)||Us)(oo)}function oo(){var e,t,r,i,n,s,o,a,l;for(Vs.sort(Zs);e=Vs.shift();)e.__d&&(t=Vs.length,i=void 0,s=(n=(r=e).__v).__e,a=[],l=[],(o=r.__P)&&((i=Js({},n)).__v=n.__v+1,Hs.vnode&&Hs.vnode(i),go(o,i,n,r.__n,void 0!==o.ownerSVGElement,32&n.__u?[s]:null,a,null==s?io(n):s,!!(32&n.__u),l),i.__.__k[i.__i]=i,fo(a,i,l),i.__e!=s&&no(i)),Vs.length>t&&Vs.sort(Zs));oo.__r=0}function ao(e,t,r,i,n,s,o,a,l,u,c){var d,h,p,_,g,f=i&&i.__k||js,v=t.length;for(r.__d=l,function(e,t,r){var i,n,s,o,a,l=t.length,u=r.length,c=u,d=0;for(e.__k=[],i=0;i<l;i++)null!=(n=e.__k[i]=null==(n=t[i])||"boolean"==typeof n||"function"==typeof n?null:"string"==typeof n||"number"==typeof n||"bigint"==typeof n||n.constructor==String?eo(null,n,null,null,n):Xs(n)?eo(to,{children:n},null,null,null):void 0===n.constructor&&n.__b>0?eo(n.type,n.props,n.key,n.ref?n.ref:null,n.__v):n)?(n.__=e,n.__b=e.__b+1,a=uo(n,r,o=i+d,c),n.__i=a,s=null,-1!==a&&(c--,(s=r[a])&&(s.__u|=131072)),null==s||null===s.__v?(-1==a&&d--,"function"!=typeof n.type&&(n.__u|=65536)):a!==o&&(a===o+1?d++:a>o?c>l-o?d+=a-o:d--:d=a<o&&a==o-1?a-o:0,a!==i+d&&(n.__u|=65536))):(s=r[i])&&null==s.key&&s.__e&&(s.__e==e.__d&&(e.__d=io(s)),mo(s,s,!1),r[i]=null,c--);if(c)for(i=0;i<u;i++)null!=(s=r[i])&&0==(131072&s.__u)&&(s.__e==e.__d&&(e.__d=io(s)),mo(s,s))}(r,t,f),l=r.__d,d=0;d<v;d++)null!=(p=r.__k[d])&&"boolean"!=typeof p&&"function"!=typeof p&&(h=-1===p.__i?Gs:f[p.__i]||Gs,p.__i=d,go(e,p,h,n,s,o,a,l,u,c),_=p.__e,p.ref&&h.ref!=p.ref&&(h.ref&&vo(h.ref,null,p),c.push(p.ref,p.__c||_,p)),null==g&&null!=_&&(g=_),65536&p.__u||h.__k===p.__k?l=lo(p,l,e):"function"==typeof p.type&&void 0!==p.__d?l=p.__d:_&&(l=_.nextSibling),p.__d=void 0,p.__u&=-196609);r.__d=l,r.__e=g}function lo(e,t,r){var i,n;if("function"==typeof e.type){for(i=e.__k,n=0;i&&n<i.length;n++)i[n]&&(i[n].__=e,t=lo(i[n],t,r));return t}return e.__e!=t&&(r.insertBefore(e.__e,t||null),t=e.__e),t&&t.nextSibling}function uo(e,t,r,i){var n=e.key,s=e.type,o=r-1,a=r+1,l=t[r];if(null===l||l&&n==l.key&&s===l.type)return r;if(i>(null!=l&&0==(131072&l.__u)?1:0))for(;o>=0||a<t.length;){if(o>=0){if((l=t[o])&&0==(131072&l.__u)&&n==l.key&&s===l.type)return o;o--}if(a<t.length){if((l=t[a])&&0==(131072&l.__u)&&n==l.key&&s===l.type)return a;a++}}return-1}function co(e,t,r){"-"===t[0]?e.setProperty(t,null==r?"":r):e[t]=null==r?"":"number"!=typeof r||Ys.test(t)?r:r+"px"}function ho(e,t,r,i,n){var s;e:if("style"===t)if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof i&&(e.style.cssText=i=""),i)for(t in i)r&&t in r||co(e.style,t,"");if(r)for(t in r)i&&r[t]===i[t]||co(e.style,t,r[t])}else if("o"===t[0]&&"n"===t[1])s=t!==(t=t.replace(/(PointerCapture)$|Capture$/,"$1")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+s]=r,r?i?r.u=i.u:(r.u=Date.now(),e.addEventListener(t,s?_o:po,s)):e.removeEventListener(t,s?_o:po,s);else{if(n)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!==t&&"height"!==t&&"href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&"rowSpan"!==t&&"colSpan"!==t&&"role"!==t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,r))}}function po(e){var t=this.l[e.type+!1];if(e.t){if(e.t<=t.u)return}else e.t=Date.now();return t(Hs.event?Hs.event(e):e)}function _o(e){return this.l[e.type+!0](Hs.event?Hs.event(e):e)}function go(e,t,r,i,n,s,o,a,l,u){var c,d,h,p,_,g,f,v,m,y,b,S,w,C,I,k=t.type;if(void 0!==t.constructor)return null;128&r.__u&&(l=!!(32&r.__u),s=[a=t.__e=r.__e]),(c=Hs.__b)&&c(t);e:if("function"==typeof k)try{if(v=t.props,m=(c=k.contextType)&&i[c.__c],y=c?m?m.props.value:c.__:i,r.__c?f=(d=t.__c=r.__c).__=d.__E:("prototype"in k&&k.prototype.render?t.__c=d=new k(v,y):(t.__c=d=new ro(v,y),d.constructor=k,d.render=yo),m&&m.sub(d),d.props=v,d.state||(d.state={}),d.context=y,d.__n=i,h=d.__d=!0,d.__h=[],d._sb=[]),null==d.__s&&(d.__s=d.state),null!=k.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=Js({},d.__s)),Js(d.__s,k.getDerivedStateFromProps(v,d.__s))),p=d.props,_=d.state,d.__v=t,h)null==k.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(null==k.getDerivedStateFromProps&&v!==p&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(v,y),!d.__e&&(null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(v,d.__s,y)||t.__v===r.__v)){for(t.__v!==r.__v&&(d.props=v,d.state=d.__s,d.__d=!1),t.__e=r.__e,t.__k=r.__k,t.__k.forEach((function(e){e&&(e.__=t)})),b=0;b<d._sb.length;b++)d.__h.push(d._sb[b]);d._sb=[],d.__h.length&&o.push(d);break e}null!=d.componentWillUpdate&&d.componentWillUpdate(v,d.__s,y),null!=d.componentDidUpdate&&d.__h.push((function(){d.componentDidUpdate(p,_,g)}))}if(d.context=y,d.props=v,d.__P=e,d.__e=!1,S=Hs.__r,w=0,"prototype"in k&&k.prototype.render){for(d.state=d.__s,d.__d=!1,S&&S(t),c=d.render(d.props,d.state,d.context),C=0;C<d._sb.length;C++)d.__h.push(d._sb[C]);d._sb=[]}else do{d.__d=!1,S&&S(t),c=d.render(d.props,d.state,d.context),d.state=d.__s}while(d.__d&&++w<25);d.state=d.__s,null!=d.getChildContext&&(i=Js(Js({},i),d.getChildContext())),h||null==d.getSnapshotBeforeUpdate||(g=d.getSnapshotBeforeUpdate(p,_)),ao(e,Xs(I=null!=c&&c.type===to&&null==c.key?c.props.children:c)?I:[I],t,r,i,n,s,o,a,l,u),d.base=t.__e,t.__u&=-161,d.__h.length&&o.push(d),f&&(d.__E=d.__=null)}catch(e){t.__v=null,l||null!=s?(t.__e=a,t.__u|=l?160:32,s[s.indexOf(a)]=null):(t.__e=r.__e,t.__k=r.__k),Hs.__e(e,t,r)}else null==s&&t.__v===r.__v?(t.__k=r.__k,t.__e=r.__e):t.__e=function(e,t,r,i,n,s,o,a,l){var u,c,d,h,p,_,g,f=r.props,v=t.props,m=t.type;if("svg"===m&&(n=!0),null!=s)for(u=0;u<s.length;u++)if((p=s[u])&&"setAttribute"in p==!!m&&(m?p.localName===m:3===p.nodeType)){e=p,s[u]=null;break}if(null==e){if(null===m)return document.createTextNode(v);e=n?document.createElementNS("http://www.w3.org/2000/svg",m):document.createElement(m,v.is&&v),s=null,a=!1}if(null===m)f===v||a&&e.data===v||(e.data=v);else{if(s=s&&qs.call(e.childNodes),f=r.props||Gs,!a&&null!=s)for(f={},u=0;u<e.attributes.length;u++)f[(p=e.attributes[u]).name]=p.value;for(u in f)p=f[u],"children"==u||("dangerouslySetInnerHTML"==u?d=p:"key"===u||u in v||ho(e,u,null,p,n));for(u in v)p=v[u],"children"==u?h=p:"dangerouslySetInnerHTML"==u?c=p:"value"==u?_=p:"checked"==u?g=p:"key"===u||a&&"function"!=typeof p||f[u]===p||ho(e,u,p,f[u],n);if(c)a||d&&(c.__html===d.__html||c.__html===e.innerHTML)||(e.innerHTML=c.__html),t.__k=[];else if(d&&(e.innerHTML=""),ao(e,Xs(h)?h:[h],t,r,i,n&&"foreignObject"!==m,s,o,s?s[0]:r.__k&&io(r,0),a,l),null!=s)for(u=s.length;u--;)null!=s[u]&&Ks(s[u]);a||(u="value",void 0!==_&&(_!==e[u]||"progress"===m&&!_||"option"===m&&_!==f[u])&&ho(e,u,_,f[u],!1),u="checked",void 0!==g&&g!==e[u]&&ho(e,u,g,f[u],!1))}return e}(r.__e,t,r,i,n,s,o,l,u);(c=Hs.diffed)&&c(t)}function fo(e,t,r){t.__d=void 0;for(var i=0;i<r.length;i++)vo(r[i],r[++i],r[++i]);Hs.__c&&Hs.__c(t,e),e.some((function(t){try{e=t.__h,t.__h=[],e.some((function(e){e.call(t)}))}catch(e){Hs.__e(e,t.__v)}}))}function vo(e,t,r){try{"function"==typeof e?e(t):e.current=t}catch(e){Hs.__e(e,r)}}function mo(e,t,r){var i,n;if(Hs.unmount&&Hs.unmount(e),(i=e.ref)&&(i.current&&i.current!==e.__e||vo(i,null,t)),null!=(i=e.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(e){Hs.__e(e,t)}i.base=i.__P=null,e.__c=void 0}if(i=e.__k)for(n=0;n<i.length;n++)i[n]&&mo(i[n],t,r||"function"!=typeof e.type);r||null==e.__e||Ks(e.__e),e.__=e.__e=e.__d=void 0}function yo(e,t,r){return this.constructor(e,r)}function bo(e,t,r){var i,n,s,o;Hs.__&&Hs.__(e,t),n=(i="function"==typeof r)?null:t.__k,s=[],o=[],go(t,e=(!i&&r||t).__k=Qs(to,null,[e]),n||Gs,Gs,void 0!==t.ownerSVGElement,!i&&r?[r]:n?null:t.firstChild?qs.call(t.childNodes):null,s,!i&&r?r:n?n.__e:t.firstChild,i,o),fo(s,e,o)}function So(e,t,r){var i,n,s,o,a=Js({},e.props);for(s in e.type&&e.type.defaultProps&&(o=e.type.defaultProps),t)"key"==s?i=t[s]:"ref"==s?n=t[s]:a[s]=void 0===t[s]&&void 0!==o?o[s]:t[s];return arguments.length>2&&(a.children=arguments.length>3?qs.call(arguments,2):r),eo(e.type,a,i||e.key,n||e.ref,null)}qs=js.slice,Hs={__e:function(e,t,r,i){for(var n,s,o;t=t.__;)if((n=t.__c)&&!n.__)try{if((s=n.constructor)&&null!=s.getDerivedStateFromError&&(n.setState(s.getDerivedStateFromError(e)),o=n.__d),null!=n.componentDidCatch&&(n.componentDidCatch(e,i||{}),o=n.__d),o)return n.__E=n}catch(t){e=t}throw e}},$s=0,ro.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=Js({},this.state),"function"==typeof e&&(e=e(Js({},r),this.props)),e&&Js(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),so(this))},ro.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),so(this))},ro.prototype.render=to,Vs=[],Us="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,Zs=function(e,t){return e.__v.__b-t.__v.__b},oo.__r=0,zs=0;var wo,Co,Io,ko,Eo=0,xo=[],To=[],Mo=Hs.__b,Ro=Hs.__r,Ao=Hs.diffed,Fo=Hs.__c,No=Hs.unmount;function Po(e,t){Hs.__h&&Hs.__h(Co,e,Eo||t),Eo=0;var r=Co.__H||(Co.__H={__:[],__h:[]});return e>=r.__.length&&r.__.push({__V:To}),r.__[e]}function Oo(e){return Eo=1,function(e,t,r){var i=Po(wo++,2);if(i.t=e,!i.__c&&(i.__=[zo(void 0,t),function(e){var t=i.__N?i.__N[0]:i.__[0],r=i.t(t,e);t!==r&&(i.__N=[r,i.__[1]],i.__c.setState({}))}],i.__c=Co,!Co.u)){var n=function(e,t,r){if(!i.__c.__H)return!0;var n=i.__c.__H.__.filter((function(e){return e.__c}));if(n.every((function(e){return!e.__N})))return!s||s.call(this,e,t,r);var o=!1;return n.forEach((function(e){if(e.__N){var t=e.__[0];e.__=e.__N,e.__N=void 0,t!==e.__[0]&&(o=!0)}})),!(!o&&i.__c.props===e)&&(!s||s.call(this,e,t,r))};Co.u=!0;var s=Co.shouldComponentUpdate,o=Co.componentWillUpdate;Co.componentWillUpdate=function(e,t,r){if(this.__e){var i=s;s=void 0,n(e,t,r),s=i}o&&o.call(this,e,t,r)},Co.shouldComponentUpdate=n}return i.__N||i.__}(zo,e)}function Lo(e,t){var r=Po(wo++,3);!Hs.__s&&Zo(r.__H,t)&&(r.__=e,r.i=t,Co.__H.__h.push(r))}function Do(e){return Eo=5,Bo((function(){return{current:e}}),[])}function Bo(e,t){var r=Po(wo++,7);return Zo(r.__H,t)?(r.__V=e(),r.i=t,r.__h=e,r.__V):r.__}function qo(e){var t=Co.context[e.__c],r=Po(wo++,9);return r.c=e,t?(null==r.__&&(r.__=!0,t.sub(Co)),t.props.value):e.__}function Ho(){for(var e;e=xo.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(Wo),e.__H.__h.forEach(Uo),e.__H.__h=[]}catch(t){e.__H.__h=[],Hs.__e(t,e.__v)}}Hs.__b=function(e){Co=null,Mo&&Mo(e)},Hs.__r=function(e){Ro&&Ro(e),wo=0;var t=(Co=e.__c).__H;t&&(Io===Co?(t.__h=[],Co.__h=[],t.__.forEach((function(e){e.__N&&(e.__=e.__N),e.__V=To,e.__N=e.i=void 0}))):(t.__h.forEach(Wo),t.__h.forEach(Uo),t.__h=[],wo=0)),Io=Co},Hs.diffed=function(e){Ao&&Ao(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(1!==xo.push(t)&&ko===Hs.requestAnimationFrame||((ko=Hs.requestAnimationFrame)||Vo)(Ho)),t.__H.__.forEach((function(e){e.i&&(e.__H=e.i),e.__V!==To&&(e.__=e.__V),e.i=void 0,e.__V=To}))),Io=Co=null},Hs.__c=function(e,t){t.some((function(e){try{e.__h.forEach(Wo),e.__h=e.__h.filter((function(e){return!e.__||Uo(e)}))}catch(r){t.some((function(e){e.__h&&(e.__h=[])})),t=[],Hs.__e(r,e.__v)}})),Fo&&Fo(e,t)},Hs.unmount=function(e){No&&No(e);var t,r=e.__c;r&&r.__H&&(r.__H.__.forEach((function(e){try{Wo(e)}catch(e){t=e}})),r.__H=void 0,t&&Hs.__e(t,r.__v))};var $o="function"==typeof requestAnimationFrame;function Vo(e){var t,r=function(){clearTimeout(i),$o&&cancelAnimationFrame(t),setTimeout(e)},i=setTimeout(r,100);$o&&(t=requestAnimationFrame(r))}function Wo(e){var t=Co,r=e.__c;"function"==typeof r&&(e.__c=void 0,r()),Co=t}function Uo(e){var t=Co;e.__c=e.__(),Co=t}function Zo(e,t){return!e||e.length!==t.length||t.some((function(t,r){return t!==e[r]}))}function zo(e,t){return"function"==typeof t?t(e):t}var Go=function(e){return e.Button="button",e.Tab="tab",e.Selector="selector",e}({}),jo=function(e){return e.TopLeft="top_left",e.TopRight="top_right",e.TopCenter="top_center",e.MiddleLeft="middle_left",e.MiddleRight="middle_right",e.MiddleCenter="middle_center",e.Left="left",e.Center="center",e.Right="right",e.NextToTrigger="next_to_trigger",e}({}),Yo=function(e){return e.Popover="popover",e.API="api",e.Widget="widget",e}({}),Xo=function(e){return e.Open="open",e.MultipleChoice="multiple_choice",e.SingleChoice="single_choice",e.Rating="rating",e.Link="link",e}({}),Jo=function(e){return e.NextQuestion="next_question",e.End="end",e.ResponseBased="response_based",e.SpecificQuestion="specific_question",e}({}),Ko=function(e){return e.Once="once",e.Recurring="recurring",e.Always="always",e}({}),Qo=function(e){return e.SHOWN="survey shown",e.DISMISSED="survey dismissed",e.SENT="survey sent",e}({}),ea=function(e){return e.SURVEY_ID="$survey_id",e.SURVEY_NAME="$survey_name",e.SURVEY_RESPONSE="$survey_response",e.SURVEY_ITERATION="$survey_iteration",e.SURVEY_ITERATION_START_DATE="$survey_iteration_start_date",e.SURVEY_PARTIALLY_COMPLETED="$survey_partially_completed",e.SURVEY_SUBMISSION_ID="$survey_submission_id",e.SURVEY_QUESTIONS="$survey_questions",e.SURVEY_COMPLETED="$survey_completed",e}({}),ta=xi("[Surveys]");function ra(e){return!(!e.start_date||e.end_date)}function ia(e){var t;return!(null==(t=e.conditions)||null==(t=t.events)||null==(t=t.values)||!t.length)}function na(e){var t;return!(null==(t=e.conditions)||null==(t=t.actions)||null==(t=t.values)||!t.length)}var sa="seenSurvey_",oa="inProgressSurvey_",aa=(e,t)=>{var r="$survey_"+t+"/"+e.id;return e.current_iteration&&e.current_iteration>0&&(r="$survey_"+t+"/"+e.id+"/"+e.current_iteration),r};Math.trunc||(Math.trunc=function(e){return e<0?Math.ceil(e):Math.floor(e)}),Number.isInteger||(Number.isInteger=function(e){return yi(e)&&isFinite(e)&&Math.floor(e)===e});var la="0123456789abcdef";class ua{constructor(e){if(this.bytes=e,16!==e.length)throw new TypeError("not 128-bit length")}static fromFieldsV7(e,t,r,i){if(!Number.isInteger(e)||!Number.isInteger(t)||!Number.isInteger(r)||!Number.isInteger(i)||e<0||t<0||r<0||i<0||e>0xffffffffffff||t>4095||r>1073741823||i>4294967295)throw new RangeError("invalid field value");var n=new Uint8Array(16);return n[0]=e/Math.pow(2,40),n[1]=e/Math.pow(2,32),n[2]=e/Math.pow(2,24),n[3]=e/Math.pow(2,16),n[4]=e/Math.pow(2,8),n[5]=e,n[6]=112|t>>>8,n[7]=t,n[8]=128|r>>>24,n[9]=r>>>16,n[10]=r>>>8,n[11]=r,n[12]=i>>>24,n[13]=i>>>16,n[14]=i>>>8,n[15]=i,new ua(n)}toString(){for(var e="",t=0;t<this.bytes.length;t++)e=e+la.charAt(this.bytes[t]>>>4)+la.charAt(15&this.bytes[t]),3!==t&&5!==t&&7!==t&&9!==t||(e+="-");if(36!==e.length)throw new Error("Invalid UUIDv7 was generated");return e}clone(){return new ua(this.bytes.slice(0))}equals(e){return 0===this.compareTo(e)}compareTo(e){for(var t=0;t<16;t++){var r=this.bytes[t]-e.bytes[t];if(0!==r)return Math.sign(r)}return 0}}class ca{constructor(){this._timestamp=0,this._counter=0,this._random=new pa}generate(){var e=this.generateOrAbort();if(_i(e)){this._timestamp=0;var t=this.generateOrAbort();if(_i(t))throw new Error("Could not generate UUID after timestamp reset");return t}return e}generateOrAbort(){var e=Date.now();if(e>this._timestamp)this._timestamp=e,this._resetCounter();else{if(!(e+1e4>this._timestamp))return;this._counter++,this._counter>4398046511103&&(this._timestamp++,this._resetCounter())}return ua.fromFieldsV7(this._timestamp,Math.trunc(this._counter/Math.pow(2,30)),this._counter&Math.pow(2,30)-1,this._random.nextUint32())}_resetCounter(){this._counter=1024*this._random.nextUint32()+(1023&this._random.nextUint32())}}var da,ha=e=>{if("undefined"!=typeof UUIDV7_DENY_WEAK_RNG&&UUIDV7_DENY_WEAK_RNG)throw new Error("no cryptographically strong RNG available");for(var t=0;t<e.length;t++)e[t]=65536*Math.trunc(65536*Math.random())+Math.trunc(65536*Math.random());return e};Br&&!_i(Br.crypto)&&crypto.getRandomValues&&(ha=e=>crypto.getRandomValues(e));class pa{constructor(){this._buffer=new Uint32Array(8),this._cursor=1/0}nextUint32(){return this._cursor>=this._buffer.length&&(ha(this._buffer),this._cursor=0),this._buffer[this._cursor++]}}var _a=()=>ga().toString(),ga=()=>(da||(da=new ca)).generate(),fa="Mobile",va="iOS",ma="Android",ya="Tablet",ba=ma+" "+ya,Sa="iPad",wa="Apple",Ca=wa+" Watch",Ia="Safari",ka="BlackBerry",Ea="Samsung",xa=Ea+"Browser",Ta=Ea+" Internet",Ma="Chrome",Ra=Ma+" OS",Aa=Ma+" "+va,Fa="Internet Explorer",Na=Fa+" "+fa,Pa="Opera",Oa=Pa+" Mini",La="Edge",Da="Microsoft "+La,Ba="Firefox",qa=Ba+" "+va,Ha="Nintendo",$a="PlayStation",Va="Xbox",Wa=ma+" "+fa,Ua=fa+" "+Ia,Za="Windows",za=Za+" Phone",Ga="Nokia",ja="Ouya",Ya="Generic",Xa=Ya+" "+fa.toLowerCase(),Ja=Ya+" "+ya.toLowerCase(),Ka="Konqueror",Qa="(\\d+(\\.\\d+)?)",el=new RegExp("Version/"+Qa),tl=new RegExp(Va,"i"),rl=new RegExp($a+" \\w+","i"),il=new RegExp(Ha+" \\w+","i"),nl=new RegExp(ka+"|PlayBook|BB10","i"),sl={"NT3.51":"NT 3.11","NT4.0":"NT 4.0","5.0":"2000",5.1:"XP",5.2:"XP","6.0":"Vista",6.1:"7",6.2:"8",6.3:"8.1",6.4:"10","10.0":"10"};var ol=(e,t)=>t&&ti(t,wa)||function(e){return ti(e,Ia)&&!ti(e,Ma)&&!ti(e,ma)}(e),al=function(e,t){return t=t||"",ti(e," OPR/")&&ti(e,"Mini")?Oa:ti(e," OPR/")?Pa:nl.test(e)?ka:ti(e,"IE"+fa)||ti(e,"WPDesktop")?Na:ti(e,xa)?Ta:ti(e,La)||ti(e,"Edg/")?Da:ti(e,"FBIOS")?"Facebook "+fa:ti(e,"UCWEB")||ti(e,"UCBrowser")?"UC Browser":ti(e,"CriOS")?Aa:ti(e,"CrMo")||ti(e,Ma)?Ma:ti(e,ma)&&ti(e,Ia)?Wa:ti(e,"FxiOS")?qa:ti(e.toLowerCase(),Ka.toLowerCase())?Ka:ol(e,t)?ti(e,fa)?Ua:Ia:ti(e,Ba)?Ba:ti(e,"MSIE")||ti(e,"Trident/")?Fa:ti(e,"Gecko")?Ba:""},ll={[Na]:[new RegExp("rv:"+Qa)],[Da]:[new RegExp(La+"?\\/"+Qa)],[Ma]:[new RegExp("("+Ma+"|CrMo)\\/"+Qa)],[Aa]:[new RegExp("CriOS\\/"+Qa)],"UC Browser":[new RegExp("(UCBrowser|UCWEB)\\/"+Qa)],[Ia]:[el],[Ua]:[el],[Pa]:[new RegExp("(Opera|OPR)\\/"+Qa)],[Ba]:[new RegExp(Ba+"\\/"+Qa)],[qa]:[new RegExp("FxiOS\\/"+Qa)],[Ka]:[new RegExp("Konqueror[:/]?"+Qa,"i")],[ka]:[new RegExp(ka+" "+Qa),el],[Wa]:[new RegExp("android\\s"+Qa,"i")],[Ta]:[new RegExp(xa+"\\/"+Qa)],[Fa]:[new RegExp("(rv:|MSIE )"+Qa)],Mozilla:[new RegExp("rv:"+Qa)]},ul=function(e,t){var r=al(e,t),i=ll[r];if(_i(i))return null;for(var n=0;n<i.length;n++){var s=i[n],o=e.match(s);if(o)return parseFloat(o[o.length-2])}return null},cl=[[new RegExp(Va+"; "+Va+" (.*?)[);]","i"),e=>[Va,e&&e[1]||""]],[new RegExp(Ha,"i"),[Ha,""]],[new RegExp($a,"i"),[$a,""]],[nl,[ka,""]],[new RegExp(Za,"i"),(e,t)=>{if(/Phone/.test(t)||/WPDesktop/.test(t))return[za,""];if(new RegExp(fa).test(t)&&!/IEMobile\b/.test(t))return[Za+" "+fa,""];var r=/Windows NT ([0-9.]+)/i.exec(t);if(r&&r[1]){var i=r[1],n=sl[i]||"";return/arm/i.test(t)&&(n="RT"),[Za,n]}return[Za,""]}],[/((iPhone|iPad|iPod).*?OS (\d+)_(\d+)_?(\d+)?|iPhone)/,e=>{if(e&&e[3]){var t=[e[3],e[4],e[5]||"0"];return[va,t.join(".")]}return[va,""]}],[/(watch.*\/(\d+\.\d+\.\d+)|watch os,(\d+\.\d+),)/i,e=>{var t="";return e&&e.length>=3&&(t=_i(e[2])?e[3]:e[2]),["watchOS",t]}],[new RegExp("("+ma+" (\\d+)\\.(\\d+)\\.?(\\d+)?|"+ma+")","i"),e=>{if(e&&e[2]){var t=[e[2],e[3],e[4]||"0"];return[ma,t.join(".")]}return[ma,""]}],[/Mac OS X (\d+)[_.](\d+)[_.]?(\d+)?/i,e=>{var t=["Mac OS X",""];if(e&&e[1]){var r=[e[1],e[2],e[3]||"0"];t[1]=r.join(".")}return t}],[/Mac/i,["Mac OS X",""]],[/CrOS/,[Ra,""]],[/Linux|debian/i,["Linux",""]]],dl=function(e){return il.test(e)?Ha:rl.test(e)?$a:tl.test(e)?Va:new RegExp(ja,"i").test(e)?ja:new RegExp("("+za+"|WPDesktop)","i").test(e)?za:/iPad/.test(e)?Sa:/iPod/.test(e)?"iPod Touch":/iPhone/.test(e)?"iPhone":/(watch)(?: ?os[,/]|\d,\d\/)[\d.]+/i.test(e)?Ca:nl.test(e)?ka:/(kobo)\s(ereader|touch)/i.test(e)?"Kobo":new RegExp(Ga,"i").test(e)?Ga:/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i.test(e)||/(kf[a-z]+)( bui|\)).+silk\//i.test(e)?"Kindle Fire":/(Android|ZTE)/i.test(e)?!new RegExp(fa).test(e)||/(9138B|TB782B|Nexus [97]|pixel c|HUAWEISHT|BTV|noble nook|smart ultra 6)/i.test(e)?/pixel[\daxl ]{1,6}/i.test(e)&&!/pixel c/i.test(e)||/(huaweimed-al00|tah-|APA|SM-G92|i980|zte|U304AA)/i.test(e)||/lmy47v/i.test(e)&&!/QTAQZ3/i.test(e)?ma:ba:ma:new RegExp("(pda|"+fa+")","i").test(e)?Xa:new RegExp(ya,"i").test(e)&&!new RegExp(ya+" pc","i").test(e)?Ja:""},hl=function(e){var t=dl(e);return t===Sa||t===ba||"Kobo"===t||"Kindle Fire"===t||t===Ja?ya:t===Ha||t===Va||t===$a||t===ja?"Console":t===Ca?"Wearable":t?fa:"Desktop"},pl=Uint8Array,_l=Uint16Array,gl=Uint32Array,fl=new pl([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),vl=new pl([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),ml=new pl([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),yl=function(e,t){for(var r=new _l(31),i=0;i<31;++i)r[i]=t+=1<<e[i-1];var n=new gl(r[30]);for(i=1;i<30;++i)for(var s=r[i];s<r[i+1];++s)n[s]=s-r[i]<<5|i;return[r,n]},bl=yl(fl,2),Sl=bl[0],wl=bl[1];Sl[28]=258,wl[258]=28;for(var Cl=yl(vl,0)[1],Il=new _l(32768),kl=0;kl<32768;++kl){var El=(43690&kl)>>>1|(21845&kl)<<1;El=(61680&(El=(52428&El)>>>2|(13107&El)<<2))>>>4|(3855&El)<<4,Il[kl]=((65280&El)>>>8|(255&El)<<8)>>>1}var xl=function(e,t,r){for(var i=e.length,n=0,s=new _l(t);n<i;++n)++s[e[n]-1];var o,a=new _l(t);for(n=0;n<t;++n)a[n]=a[n-1]+s[n-1]<<1;if(r){o=new _l(1<<t);var l=15-t;for(n=0;n<i;++n)if(e[n])for(var u=n<<4|e[n],c=t-e[n],d=a[e[n]-1]++<<c,h=d|(1<<c)-1;d<=h;++d)o[Il[d]>>>l]=u}else for(o=new _l(i),n=0;n<i;++n)o[n]=Il[a[e[n]-1]++]>>>15-e[n];return o},Tl=new pl(288);for(kl=0;kl<144;++kl)Tl[kl]=8;for(kl=144;kl<256;++kl)Tl[kl]=9;for(kl=256;kl<280;++kl)Tl[kl]=7;for(kl=280;kl<288;++kl)Tl[kl]=8;var Ml=new pl(32);for(kl=0;kl<32;++kl)Ml[kl]=5;var Rl=xl(Tl,9,0),Al=xl(Ml,5,0),Fl=function(e){return(e/8>>0)+(7&e&&1)},Nl=function(e,t,r){(null==r||r>e.length)&&(r=e.length);var i=new(e instanceof _l?_l:e instanceof gl?gl:pl)(r-t);return i.set(e.subarray(t,r)),i},Pl=function(e,t,r){r<<=7&t;var i=t/8>>0;e[i]|=r,e[i+1]|=r>>>8},Ol=function(e,t,r){r<<=7&t;var i=t/8>>0;e[i]|=r,e[i+1]|=r>>>8,e[i+2]|=r>>>16},Ll=function(e,t){for(var r=[],i=0;i<e.length;++i)e[i]&&r.push({s:i,f:e[i]});var n=r.length,s=r.slice();if(!n)return[new pl(0),0];if(1==n){var o=new pl(r[0].s+1);return o[r[0].s]=1,[o,1]}r.sort((function(e,t){return e.f-t.f})),r.push({s:-1,f:25001});var a=r[0],l=r[1],u=0,c=1,d=2;for(r[0]={s:-1,f:a.f+l.f,l:a,r:l};c!=n-1;)a=r[r[u].f<r[d].f?u++:d++],l=r[u!=c&&r[u].f<r[d].f?u++:d++],r[c++]={s:-1,f:a.f+l.f,l:a,r:l};var h=s[0].s;for(i=1;i<n;++i)s[i].s>h&&(h=s[i].s);var p=new _l(h+1),_=Dl(r[c-1],p,0);if(_>t){i=0;var g=0,f=_-t,v=1<<f;for(s.sort((function(e,t){return p[t.s]-p[e.s]||e.f-t.f}));i<n;++i){var m=s[i].s;if(!(p[m]>t))break;g+=v-(1<<_-p[m]),p[m]=t}for(g>>>=f;g>0;){var y=s[i].s;p[y]<t?g-=1<<t-p[y]++-1:++i}for(;i>=0&&g;--i){var b=s[i].s;p[b]==t&&(--p[b],++g)}_=t}return[new pl(p),_]},Dl=function(e,t,r){return-1==e.s?Math.max(Dl(e.l,t,r+1),Dl(e.r,t,r+1)):t[e.s]=r},Bl=function(e){for(var t=e.length;t&&!e[--t];);for(var r=new _l(++t),i=0,n=e[0],s=1,o=function(e){r[i++]=e},a=1;a<=t;++a)if(e[a]==n&&a!=t)++s;else{if(!n&&s>2){for(;s>138;s-=138)o(32754);s>2&&(o(s>10?s-11<<5|28690:s-3<<5|12305),s=0)}else if(s>3){for(o(n),--s;s>6;s-=6)o(8304);s>2&&(o(s-3<<5|8208),s=0)}for(;s--;)o(n);s=1,n=e[a]}return[r.subarray(0,i),t]},ql=function(e,t){for(var r=0,i=0;i<t.length;++i)r+=e[i]*t[i];return r},Hl=function(e,t,r){var i=r.length,n=Fl(t+2);e[n]=255&i,e[n+1]=i>>>8,e[n+2]=255^e[n],e[n+3]=255^e[n+1];for(var s=0;s<i;++s)e[n+s+4]=r[s];return 8*(n+4+i)},$l=function(e,t,r,i,n,s,o,a,l,u,c){Pl(t,c++,r),++n[256];for(var d=Ll(n,15),h=d[0],p=d[1],_=Ll(s,15),g=_[0],f=_[1],v=Bl(h),m=v[0],y=v[1],b=Bl(g),S=b[0],w=b[1],C=new _l(19),I=0;I<m.length;++I)C[31&m[I]]++;for(I=0;I<S.length;++I)C[31&S[I]]++;for(var k=Ll(C,7),E=k[0],x=k[1],T=19;T>4&&!E[ml[T-1]];--T);var M,R,A,F,N=u+5<<3,P=ql(n,Tl)+ql(s,Ml)+o,O=ql(n,h)+ql(s,g)+o+14+3*T+ql(C,E)+(2*C[16]+3*C[17]+7*C[18]);if(N<=P&&N<=O)return Hl(t,c,e.subarray(l,l+u));if(Pl(t,c,1+(O<P)),c+=2,O<P){M=xl(h,p,0),R=h,A=xl(g,f,0),F=g;var L=xl(E,x,0);Pl(t,c,y-257),Pl(t,c+5,w-1),Pl(t,c+10,T-4),c+=14;for(I=0;I<T;++I)Pl(t,c+3*I,E[ml[I]]);c+=3*T;for(var D=[m,S],B=0;B<2;++B){var q=D[B];for(I=0;I<q.length;++I){var H=31&q[I];Pl(t,c,L[H]),c+=E[H],H>15&&(Pl(t,c,q[I]>>>5&127),c+=q[I]>>>12)}}}else M=Rl,R=Tl,A=Al,F=Ml;for(I=0;I<a;++I)if(i[I]>255){H=i[I]>>>18&31;Ol(t,c,M[H+257]),c+=R[H+257],H>7&&(Pl(t,c,i[I]>>>23&31),c+=fl[H]);var $=31&i[I];Ol(t,c,A[$]),c+=F[$],$>3&&(Ol(t,c,i[I]>>>5&8191),c+=vl[$])}else Ol(t,c,M[i[I]]),c+=R[i[I]];return Ol(t,c,M[256]),c+R[256]},Vl=new gl([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),Wl=function(){for(var e=new gl(256),t=0;t<256;++t){for(var r=t,i=9;--i;)r=(1&r&&3988292384)^r>>>1;e[t]=r}return e}(),Ul=function(){var e=4294967295;return{p:function(t){for(var r=e,i=0;i<t.length;++i)r=Wl[255&r^t[i]]^r>>>8;e=r},d:function(){return 4294967295^e}}},Zl=function(e,t,r,i,n){return function(e,t,r,i,n,s){var o=e.length,a=new pl(i+o+5*(1+Math.floor(o/7e3))+n),l=a.subarray(i,a.length-n),u=0;if(!t||o<8)for(var c=0;c<=o;c+=65535){var d=c+65535;d<o?u=Hl(l,u,e.subarray(c,d)):(l[c]=s,u=Hl(l,u,e.subarray(c,o)))}else{for(var h=Vl[t-1],p=h>>>13,_=8191&h,g=(1<<r)-1,f=new _l(32768),v=new _l(g+1),m=Math.ceil(r/3),y=2*m,b=function(t){return(e[t]^e[t+1]<<m^e[t+2]<<y)&g},S=new gl(25e3),w=new _l(288),C=new _l(32),I=0,k=0,E=(c=0,0),x=0,T=0;c<o;++c){var M=b(c),R=32767&c,A=v[M];if(f[R]=A,v[M]=R,x<=c){var F=o-c;if((I>7e3||E>24576)&&F>423){u=$l(e,l,0,S,w,C,k,E,T,c-T,u),E=I=k=0,T=c;for(var N=0;N<286;++N)w[N]=0;for(N=0;N<30;++N)C[N]=0}var P=2,O=0,L=_,D=R-A&32767;if(F>2&&M==b(c-D))for(var B=Math.min(p,F)-1,q=Math.min(32767,c),H=Math.min(258,F);D<=q&&--L&&R!=A;){if(e[c+P]==e[c+P-D]){for(var $=0;$<H&&e[c+$]==e[c+$-D];++$);if($>P){if(P=$,O=D,$>B)break;var V=Math.min(D,$-2),W=0;for(N=0;N<V;++N){var U=c-D+N+32768&32767,Z=U-f[U]+32768&32767;Z>W&&(W=Z,A=U)}}}D+=(R=A)-(A=f[R])+32768&32767}if(O){S[E++]=268435456|wl[P]<<18|Cl[O];var z=31&wl[P],G=31&Cl[O];k+=fl[z]+vl[G],++w[257+z],++C[G],x=c+P,++I}else S[E++]=e[c],++w[e[c]]}}u=$l(e,l,s,S,w,C,k,E,T,c-T,u)}return Nl(a,0,i+Fl(u)+n)}(e,null==t.level?6:t.level,null==t.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(e.length)))):12+t.mem,r,i,!n)},zl=function(e,t,r){for(;r;++t)e[t]=r,r>>>=8},Gl=function(e,t){var r=t.filename;if(e[0]=31,e[1]=139,e[2]=8,e[8]=t.level<2?4:9==t.level?2:0,e[9]=3,0!=t.mtime&&zl(e,4,Math.floor(new Date(t.mtime||Date.now())/1e3)),r){e[3]=8;for(var i=0;i<=r.length;++i)e[i+10]=r.charCodeAt(i)}},jl=function(e){return 10+(e.filename&&e.filename.length+1||0)};function Yl(e,t){void 0===t&&(t={});var r=Ul(),i=e.length;r.p(e);var n=Zl(e,t,jl(t),8),s=n.length;return Gl(n,t),zl(n,s-8,r.d()),zl(n,s-4,i),n}function Xl(e,t){var r=e.length;if("undefined"!=typeof TextEncoder)return(new TextEncoder).encode(e);for(var i=new pl(e.length+(e.length>>>1)),n=0,s=function(e){i[n++]=e},o=0;o<r;++o){if(n+5>i.length){var a=new pl(n+8+(r-o<<1));a.set(i),i=a}var l=e.charCodeAt(o);l<128||t?s(l):l<2048?(s(192|l>>>6),s(128|63&l)):l>55295&&l<57344?(s(240|(l=65536+(1047552&l)|1023&e.charCodeAt(++o))>>>18),s(128|l>>>12&63),s(128|l>>>6&63),s(128|63&l)):(s(224|l>>>12),s(128|l>>>6&63),s(128|63&l))}return Nl(i,0,n)}var Jl=function(e){var t,r,i,n,s="";for(t=r=0,i=(e=(e+"").replace(/\r\n/g,"\n").replace(/\r/g,"\n")).length,n=0;n<i;n++){var o=e.charCodeAt(n),a=null;o<128?r++:a=o>127&&o<2048?String.fromCharCode(o>>6|192,63&o|128):String.fromCharCode(o>>12|224,o>>6&63|128,63&o|128),vi(a)||(r>t&&(s+=e.substring(t,r)),s+=a,t=r=n+1)}return r>t&&(s+=e.substring(t,e.length)),s},Kl=!!Gr||!!zr,Ql="text/plain",eu=(e,t)=>{var[i,n]=e.split("?"),s=r({},t);null==n||n.split("&").forEach((e=>{var[t]=e.split("=");delete s[t]}));var o=Ui(s);return i+"?"+(o=o?(n?n+"&":"")+o:n)},tu=(e,t)=>JSON.stringify(e,((e,t)=>"bigint"==typeof t?t.toString():t),t),ru=e=>{var{data:t,compression:r}=e;if(t){if(r===Qr.GZipJS){var i=Yl(Xl(tu(t)),{mtime:0}),n=new Blob([i],{type:Ql});return{contentType:Ql,body:n,estimatedSize:n.size}}if(r===Qr.Base64){var s=function(e){var t,r,i,n,s,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",a=0,l=0,u="",c=[];if(!e)return e;e=Jl(e);do{t=(s=e.charCodeAt(a++)<<16|e.charCodeAt(a++)<<8|e.charCodeAt(a++))>>18&63,r=s>>12&63,i=s>>6&63,n=63&s,c[l++]=o.charAt(t)+o.charAt(r)+o.charAt(i)+o.charAt(n)}while(a<e.length);switch(u=c.join(""),e.length%3){case 1:u=u.slice(0,-2)+"==";break;case 2:u=u.slice(0,-1)+"="}return u}(tu(t)),o=(e=>"data="+encodeURIComponent("string"==typeof e?e:tu(e)))(s);return{contentType:"application/x-www-form-urlencoded",body:o,estimatedSize:new Blob([o]).size}}var a=tu(t);return{contentType:"application/json",body:a,estimatedSize:new Blob([a]).size}}},iu=[];zr&&iu.push({transport:"fetch",method:e=>{var t,i,{contentType:n,body:s,estimatedSize:o}=null!==(t=ru(e))&&void 0!==t?t:{},a=new Headers;Ri(e.headers,(function(e,t){a.append(t,e)})),n&&a.append("Content-Type",n);var l=e.url,u=null;if(jr){var c=new jr;u={signal:c.signal,timeout:setTimeout((()=>c.abort()),e.timeout)}}zr(l,r({method:(null==e?void 0:e.method)||"GET",headers:a,keepalive:"POST"===e.method&&(o||0)<52428.8,body:s,signal:null==(i=u)?void 0:i.signal},e.fetchOptions)).then((t=>t.text().then((r=>{var i={statusCode:t.status,text:r};if(200===t.status)try{i.json=JSON.parse(r)}catch(e){Ei.error(e)}null==e.callback||e.callback(i)})))).catch((t=>{Ei.error(t),null==e.callback||e.callback({statusCode:0,text:t})})).finally((()=>u?clearTimeout(u.timeout):null))}}),Gr&&iu.push({transport:"XHR",method:e=>{var t,r=new Gr;r.open(e.method||"GET",e.url,!0);var{contentType:i,body:n}=null!==(t=ru(e))&&void 0!==t?t:{};Ri(e.headers,(function(e,t){r.setRequestHeader(t,e)})),i&&r.setRequestHeader("Content-Type",i),e.timeout&&(r.timeout=e.timeout),r.withCredentials=!0,r.onreadystatechange=()=>{if(4===r.readyState){var t={statusCode:r.status,text:r.responseText};if(200===r.status)try{t.json=JSON.parse(r.responseText)}catch(e){}null==e.callback||e.callback(t)}},r.send(n)}}),null!=Wr&&Wr.sendBeacon&&iu.push({transport:"sendBeacon",method:e=>{var t=eu(e.url,{beacon:"1"});try{var r,{contentType:i,body:n}=null!==(r=ru(e))&&void 0!==r?r:{},s="string"==typeof n?new Blob([n],{type:i}):n;Wr.sendBeacon(t,s)}catch(e){}}});var nu=function(e,t){if(!function(e){try{new RegExp(e)}catch(e){return!1}return!0}(t))return!1;try{return new RegExp(t).test(e)}catch(e){return!1}};function su(e,t,r){return tu({distinct_id:e,userPropertiesToSet:t,userPropertiesToSetOnce:r})}var ou={exact:(e,t)=>t.some((t=>e.some((e=>t===e)))),is_not:(e,t)=>t.every((t=>e.every((e=>t!==e)))),regex:(e,t)=>t.some((t=>e.some((e=>nu(t,e))))),not_regex:(e,t)=>t.every((t=>e.every((e=>!nu(t,e))))),icontains:(e,t)=>t.map(au).some((t=>e.map(au).some((e=>t.includes(e))))),not_icontains:(e,t)=>t.map(au).every((t=>e.map(au).every((e=>!t.includes(e)))))},au=e=>e.toLowerCase(),lu=xi("[Stylesheet Loader]"),uu=Br,cu=Ur;function du(e){return"$survey_response_"+e}var hu="#020617",pu={fontFamily:"inherit",backgroundColor:"#eeeded",submitButtonColor:"black",submitButtonTextColor:"white",ratingButtonColor:"white",ratingButtonActiveColor:"black",borderColor:"#c9c6c6",placeholder:"Start typing...",whiteLabel:!1,displayThankYouMessage:!0,thankYouMessageHeader:"Thank you for your feedback!",position:jo.Right,widgetType:Go.Tab,widgetLabel:"Feedback",widgetColor:"black",zIndex:"2147483647",disabledButtonOpacity:"0.6",maxWidth:"300px",textSubtleColor:"#939393",boxPadding:"20px 24px",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)",borderRadius:"10px",shuffleQuestions:!1,surveyPopupDelaySeconds:void 0,outlineColor:"rgba(59, 130, 246, 0.8)",inputBackground:"white",inputTextColor:hu,scrollbarThumbColor:"var(--ph-survey-border-color)",scrollbarTrackColor:"var(--ph-survey-background-color)"};function _u(e){if("#"===e[0]){var t=e.replace(/^#/,"");return"rgb("+parseInt(t.slice(0,2),16)+","+parseInt(t.slice(2,4),16)+","+parseInt(t.slice(4,6),16)+")"}return"rgb(255, 255, 255)"}function gu(e){var t;void 0===e&&(e=pu.backgroundColor),"#"===e[0]&&(t=_u(e)),e.startsWith("rgb")&&(t=e);var r=function(e){return{aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4","indianred ":"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370d8",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#d87093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"}[e.toLowerCase()]}(e);if(r&&(t=_u(r)),!t)return hu;var i=t.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/);if(i){var n=parseInt(i[1]),s=parseInt(i[2]),o=parseInt(i[3]);return Math.sqrt(n*n*.299+s*s*.587+o*o*.114)>127.5?hu:"white"}return hu}function fu(e){var t=((e,t,r)=>{var i,n=e.createElement("style");return n.innerText=t,null!=r&&null!=(i=r.config)&&i.prepare_external_dependency_stylesheet&&(n=r.config.prepare_external_dependency_stylesheet(n)),n||(lu.error("prepare_external_dependency_stylesheet returned null"),null)})(cu,':host{--ph-survey-font-family:-apple-system,BlinkMacSystemFont,"Inter","Segoe UI","Roboto",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";--ph-survey-box-padding:20px 24px;--ph-survey-max-width:300px;--ph-survey-z-index:2147483647;--ph-survey-border-color:#dcdcdc;--ph-survey-border-bottom:1.5px solid var(--ph-survey-border-color);--ph-survey-border-radius:10px;--ph-survey-background-color:#eeeded;--ph-survey-box-shadow:0 4px 12px rgba(0,0,0,.15);--ph-survey-submit-button-color:#000;--ph-survey-submit-button-text-color:#fff;--ph-survey-rating-bg-color:#fff;--ph-survey-rating-text-color:#020617;--ph-survey-rating-active-bg-color:#000;--ph-survey-rating-active-text-color:#fff;--ph-survey-text-primary-color:#020617;--ph-survey-text-subtle-color:#939393;--ph-widget-color:#e0a045;--ph-widget-text-color:#fff;--ph-survey-scrollbar-thumb-color:var(--ph-survey-border-color);--ph-survey-scrollbar-track-color:var(--ph-survey-background-color);--ph-survey-outline-color:rgba(59,130,246,.8);--ph-survey-input-background:#fff;--ph-survey-input-text-color:#020617;--ph-survey-disabled-button-opacity:0.6}.ph-survey{bottom:0;height:fit-content;margin:0;max-width:85%;min-width:300px;position:fixed;width:var(--ph-survey-max-width);z-index:var(--ph-survey-z-index)}.ph-survey h3,.ph-survey p{margin:0}.ph-survey *{box-sizing:border-box;color:var(--ph-survey-text-primary-color);font-family:var(--ph-survey-font-family)}.ph-survey .multiple-choice-options label,.ph-survey input[type=text],.ph-survey textarea{background:var(--ph-survey-input-background);border:1.5px solid var(--ph-survey-border-color);border-radius:4px;color:var(--ph-survey-input-text-color);padding:10px;transition:border-color .2s ease-out,box-shadow .2s ease-out,transform .15s ease-out}.ph-survey input[type=text],.ph-survey textarea{transition:border-color .2s ease-out,box-shadow .2s ease-out,transform .15s ease-out}.ph-survey input{margin:0}.ph-survey .form-submit:focus,.ph-survey .form-submit:focus-visible,.ph-survey input[type=checkbox]:focus,.ph-survey input[type=checkbox]:focus-visible,.ph-survey input[type=radio]:focus,.ph-survey input[type=radio]:focus-visible,.ph-survey input[type=text]:focus,.ph-survey input[type=text]:focus-visible,.ph-survey textarea:focus,.ph-survey textarea:focus-visible{border-color:var(--ph-survey-rating-active-bg-color);outline:1.5px solid var(--ph-survey-outline-color);outline-offset:2px}.ph-survey button:focus:not(:focus-visible),.ph-survey input[type=checkbox]:focus:not(:focus-visible),.ph-survey input[type=radio]:focus:not(:focus-visible),.ph-survey input[type=text]:focus:not(:focus-visible),.ph-survey textarea:focus:not(:focus-visible){outline:none}.ph-survey input[type=text]:hover:not(:focus),.ph-survey textarea:hover:not(:focus){border-color:var(--ph-survey-rating-active-bg-color)}@media (max-width:768px){.ph-survey input[type=text],.ph-survey textarea{font-size:1rem}}.ph-survey .form-cancel,.ph-survey .multiple-choice-options label,.ph-survey .rating-options-number,.ph-survey input[type=checkbox],.ph-survey input[type=radio]{border:1.5px solid var(--ph-survey-border-color)}.ph-survey .footer-branding,.ph-survey .form-cancel,.ph-survey .form-submit,.ph-survey .ratings-emoji,.ph-survey .ratings-number,.ph-survey input[type=checkbox],.ph-survey input[type=radio],.ph-survey label{transition:all .2s ease-out}@media (prefers-reduced-motion:no-preference){.ph-survey button:active,.ph-survey input[type=checkbox]:active,.ph-survey input[type=radio]:active,.ph-survey label:active{transition-duration:.1s}}.ph-survey-widget-tab{background:var(--ph-widget-color);border:none;border-radius:3px 3px 0 0;color:var(--ph-widget-text-color);cursor:pointer;padding:10px 12px;position:fixed;right:0;text-align:center;top:50%;transform:rotate(-90deg) translateY(-100%);transform-origin:right top;transition:padding-bottom .2s ease-out;z-index:var(--ph-survey-z-index)}.ph-survey-widget-tab:hover{padding-bottom:16px}@keyframes ph-survey-fade-in{0%{opacity:0}to{opacity:1}}.survey-box{gap:16px}.bottom-section,.survey-box{display:flex;flex-direction:column}.bottom-section{gap:8px}.thank-you-message-header~.bottom-section{padding-top:16px}.question-container,.thank-you-message{display:flex;flex-direction:column;gap:8px}.survey-question{font-size:14px;font-weight:500}.survey-question-description{font-size:13px;opacity:.8;padding-top:4px}.question-textarea-wrapper{display:flex;flex-direction:column}.survey-form{animation:ph-survey-fade-in .3s ease-out forwards}.survey-form,.thank-you-message{background:var(--ph-survey-background-color);border:1.5px solid var(--ph-survey-border-color);border-bottom:var(--ph-survey-border-bottom);border-radius:var(--ph-survey-border-radius);box-shadow:var(--ph-survey-box-shadow);margin:0;padding:var(--ph-survey-box-padding);position:relative;text-align:left;width:100%;z-index:var(--ph-survey-z-index)}.survey-form input[type=text],.survey-form textarea{min-width:100%}:is(.survey-form textarea):focus,:is(.survey-form textarea):focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.08)}:is(.survey-form textarea):focus:not(:focus-visible){box-shadow:none}.survey-box:has(.survey-question:empty):not(:has(.survey-question-description)) .multiple-choice-options,.survey-box:has(.survey-question:empty):not(:has(.survey-question-description)) textarea{margin-top:0}.multiple-choice-options{border:none;display:flex;flex-direction:column;font-size:14px;gap:8px;margin:0;padding:1px 0}.multiple-choice-options label{align-items:center;cursor:pointer;display:flex;font-size:13px;gap:8px}:is(.multiple-choice-options label):hover:not(:has(input:checked)){border-color:var(--ph-survey-text-subtle-color);box-shadow:0 2px 8px rgba(0,0,0,.08)}:is(.multiple-choice-options label):has(input:checked){border-color:var(--ph-survey-rating-active-bg-color);box-shadow:0 1px 4px rgba(0,0,0,.05)}.choice-option-open:is(.multiple-choice-options label){flex-wrap:wrap}:is(.multiple-choice-options label) span{color:inherit}.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]{appearance:none;-webkit-appearance:none;-moz-appearance:none;background:var(--ph-survey-input-background);border-radius:3px;cursor:pointer;flex-shrink:0;height:1rem;position:relative;transition:all .2s cubic-bezier(.4,0,.2,1),transform .15s ease-out;width:1rem;z-index:1}:is(.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]):focus{transform:scale(1.05)}:is(.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]):hover{border-color:var(--ph-survey-rating-active-bg-color);transform:scale(1.05)}:is(.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]):active{transform:scale(.95)}:is(.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]):checked{background:var(--ph-survey-rating-active-bg-color);border-color:var(--ph-survey-rating-active-bg-color);transform:scale(1)}:is(.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]):checked:hover{transform:scale(1.05)}.multiple-choice-options input[type=checkbox]:checked:after{animation:ph-survey-checkmark-reveal .2s ease-out .1s forwards;border:solid var(--ph-survey-rating-active-text-color);border-width:0 2px 2px 0;height:8px;left:4px;transform:rotate(45deg) scale(0);width:4px}.multiple-choice-options input[type=radio]:checked:after{animation:ph-survey-radio-reveal .15s ease-out .05s forwards;background:var(--ph-survey-rating-active-text-color);border-radius:50%;height:6px;left:5px;top:5px;transform:scale(0);width:6px}.multiple-choice-options input[type=checkbox]:checked:after,.multiple-choice-options input[type=radio]:checked:after{box-sizing:content-box;content:"";position:absolute}.multiple-choice-options input[type=radio]{border-radius:50%}.multiple-choice-options input[type=radio]:checked{border:none}:is(.multiple-choice-options input[type=checkbox]:checked,.multiple-choice-options input[type=radio]:checked)~*{font-weight:700}:is(:is(.multiple-choice-options .choice-option-open) input[type=text])::placeholder{color:var(--ph-survey-text-subtle-color);font-weight:400}.rating-options-emoji{display:flex;justify-content:space-between}.ratings-emoji{background-color:transparent;border:none;font-size:16px;opacity:.5;padding:0}.ratings-emoji:hover{cursor:pointer;opacity:1;transform:scale(1.15)}.ratings-emoji.rating-active{opacity:1}.ratings-emoji svg{fill:var(--ph-survey-text-primary-color);transition:fill .2s ease-out}.rating-options-number{border-radius:6px;display:grid;grid-auto-columns:1fr;grid-auto-flow:column;overflow:hidden}.rating-options-number .ratings-number{border:none;border-right:1px solid var(--ph-survey-border-color);color:var(--ph-survey-rating-text-color);cursor:pointer;font-weight:700;text-align:center}:is(.rating-options-number .ratings-number):last-of-type{border-right:0}.rating-active:is(.rating-options-number .ratings-number){background:var(--ph-survey-rating-active-bg-color);color:var(--ph-survey-rating-active-text-color)}.ratings-number{background-color:var(--ph-survey-rating-bg-color);border:none;padding:8px 0}.ratings-number .rating-active{background-color:var(--ph-survey-rating-active-bg-color)}.ratings-number:hover{cursor:pointer}.rating-text{display:flex;flex-direction:row;font-size:11px;justify-content:space-between;opacity:.7}.form-submit{background:var(--ph-survey-submit-button-color);border:none;border-radius:6px;box-shadow:0 2px 0 rgba(0,0,0,.045);color:var(--ph-survey-submit-button-text-color);cursor:pointer;font-weight:700;min-width:100%;padding:12px;text-align:center;user-select:none}.form-submit:not([disabled]):hover{box-shadow:0 4px 8px rgba(0,0,0,.1);transform:scale(1.02)}.form-submit:not([disabled]):active{box-shadow:0 1px 2px rgba(0,0,0,.05);transform:scale(.98)}.form-submit[disabled]{cursor:not-allowed;opacity:var(--ph-survey-disabled-button-opacity)}.form-cancel{background:#fff;border-radius:100%;cursor:pointer;line-height:0;padding:12px;position:absolute;right:0;top:0;transform:translate(50%,-50%)}.form-cancel:hover{opacity:.7;transform:translate(50%,-50%) scale(1.1)}.footer-branding{align-items:center;display:flex;font-size:11px;font-weight:500;gap:4px;justify-content:center;opacity:.6;text-decoration:none}.footer-branding:hover{opacity:1}.footer-branding a{text-decoration:none}.thank-you-message{text-align:center}.thank-you-message-header{margin:10px 0 0}.thank-you-message-body{font-size:14px;opacity:.8}.limit-height{max-height:256px;overflow-x:hidden;overflow-y:auto;scrollbar-color:var(--ph-survey-scrollbar-thumb-color) var(--ph-survey-scrollbar-track-color);scrollbar-width:thin}.limit-height::-webkit-scrollbar{width:8px}.limit-height::-webkit-scrollbar-track{background:var(--ph-survey-scrollbar-track-color);border-radius:4px}.limit-height::-webkit-scrollbar-thumb{background-color:var(--ph-survey-scrollbar-thumb-color);border:2px solid var(--ph-survey-scrollbar-track-color);border-radius:4px}:is(.limit-height::-webkit-scrollbar-thumb):hover{background-color:var(--ph-survey-text-subtle-color)}.sr-only{clip:rect(0,0,0,0);border:0;height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;white-space:nowrap;width:1px}@media (prefers-reduced-motion:reduce){.ph-survey *{animation-duration:.01ms!important;animation-iteration-count:1!important;scroll-behavior:auto!important;transition-duration:.01ms!important}}@keyframes ph-survey-checkmark-reveal{0%{opacity:0;transform:rotate(45deg) scale(0)}50%{opacity:1;transform:rotate(45deg) scale(1.2)}to{opacity:1;transform:rotate(45deg) scale(1)}}@keyframes ph-survey-radio-reveal{0%{opacity:0;transform:scale(0)}50%{opacity:1;transform:scale(1.3)}to{opacity:1;transform:scale(1)}}',e);return null==t||t.setAttribute("data-ph-survey-style","true"),t}var vu=(e,t,i)=>{var n=Lu(e),s=cu.querySelector("."+n);if(s&&s.shadowRoot)return{shadow:s.shadowRoot,isNewlyCreated:!1};var o=cu.createElement("div");((e,t,i)=>{var n=r({},pu,i),s=e.style,o=![jo.Center,jo.Left,jo.Right].includes(n.position)||t===Yo.Widget&&(null==i?void 0:i.widgetType)===Go.Tab;s.setProperty("--ph-survey-font-family",function(e){if("inherit"===e)return"inherit";var t='BlinkMacSystemFont, "Inter", "Segoe UI", "Roboto", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"';return e?e+", "+t:"-apple-system, "+t}(n.fontFamily)),s.setProperty("--ph-survey-box-padding",n.boxPadding),s.setProperty("--ph-survey-max-width",n.maxWidth),s.setProperty("--ph-survey-z-index",n.zIndex),s.setProperty("--ph-survey-border-color",n.borderColor),o?(s.setProperty("--ph-survey-border-radius",n.borderRadius),s.setProperty("--ph-survey-border-bottom","1.5px solid var(--ph-survey-border-color)")):(s.setProperty("--ph-survey-border-bottom","none"),s.setProperty("--ph-survey-border-radius",n.borderRadius+" "+n.borderRadius+" 0 0")),s.setProperty("--ph-survey-background-color",n.backgroundColor),s.setProperty("--ph-survey-box-shadow",n.boxShadow),s.setProperty("--ph-survey-disabled-button-opacity",n.disabledButtonOpacity),s.setProperty("--ph-survey-submit-button-color",n.submitButtonColor),s.setProperty("--ph-survey-submit-button-text-color",(null==i?void 0:i.submitButtonTextColor)||gu(n.submitButtonColor)),s.setProperty("--ph-survey-rating-bg-color",n.ratingButtonColor),s.setProperty("--ph-survey-rating-text-color",gu(n.ratingButtonColor)),s.setProperty("--ph-survey-rating-active-bg-color",n.ratingButtonActiveColor),s.setProperty("--ph-survey-rating-active-text-color",gu(n.ratingButtonActiveColor)),s.setProperty("--ph-survey-text-primary-color",gu(n.backgroundColor)),s.setProperty("--ph-survey-text-subtle-color",n.textSubtleColor),s.setProperty("--ph-widget-color",n.widgetColor),s.setProperty("--ph-widget-text-color",gu(n.widgetColor)),s.setProperty("--ph-widget-z-index",n.zIndex),"white"===n.backgroundColor&&s.setProperty("--ph-survey-input-background","#f8f8f8"),s.setProperty("--ph-survey-input-background",n.inputBackground),s.setProperty("--ph-survey-input-text-color",gu(n.inputBackground)),s.setProperty("--ph-survey-scrollbar-thumb-color",n.scrollbarThumbColor),s.setProperty("--ph-survey-scrollbar-track-color",n.scrollbarTrackColor),s.setProperty("--ph-survey-outline-color",n.outlineColor)})(o,e.type,e.appearance),o.className=n;var a=o.attachShadow({mode:"open"}),l=fu(t);if(l){var u=a.querySelector("style");u&&a.removeChild(u),a.appendChild(l)}return cu.body.appendChild(o),{shadow:a,isNewlyCreated:!0}},mu=(e,t)=>{if(!t)return null;var r=e[du(t)];return li(r)?[...r]:r},yu=e=>{var{responses:t,survey:i,surveySubmissionId:n,posthog:s,isSurveyCompleted:o}=e;s?(s.capture(Qo.SENT,r({[ea.SURVEY_NAME]:i.name,[ea.SURVEY_ID]:i.id,[ea.SURVEY_ITERATION]:i.current_iteration,[ea.SURVEY_ITERATION_START_DATE]:i.current_iteration_start_date,[ea.SURVEY_QUESTIONS]:i.questions.map((e=>({id:e.id,question:e.question,response:mu(t,e.id)}))),[ea.SURVEY_SUBMISSION_ID]:n,[ea.SURVEY_COMPLETED]:o,sessionRecordingUrl:null==s.get_session_replay_url?void 0:s.get_session_replay_url()},t)),o&&(uu.dispatchEvent(new CustomEvent("PHSurveySent",{detail:{surveyId:i.id}})),Ou(i))):ta.error("[survey sent] event not captured, PostHog instance not found.")},bu=(e,t,i)=>{if(t){if(!i){var n=Nu(e);t.capture(Qo.DISMISSED,r({[ea.SURVEY_NAME]:e.name,[ea.SURVEY_ID]:e.id,[ea.SURVEY_ITERATION]:e.current_iteration,[ea.SURVEY_ITERATION_START_DATE]:e.current_iteration_start_date,[ea.SURVEY_PARTIALLY_COMPLETED]:Object.values((null==n?void 0:n.responses)||{}).filter((e=>!mi(e))).length>0,sessionRecordingUrl:null==t.get_session_replay_url?void 0:t.get_session_replay_url()},null==n?void 0:n.responses,{[ea.SURVEY_SUBMISSION_ID]:null==n?void 0:n.surveySubmissionId,[ea.SURVEY_QUESTIONS]:e.questions.map((e=>({id:e.id,question:e.question,response:mu((null==n?void 0:n.responses)||{},e.id)})))})),Ou(e),localStorage.setItem(ku(e),"true"),uu.dispatchEvent(new CustomEvent("PHSurveyClosed",{detail:{surveyId:e.id}}))}}else ta.error("[survey dismissed] event not captured, PostHog instance not found.")},Su=e=>e.map((e=>({sort:Math.floor(10*Math.random()),value:e}))).sort(((e,t)=>e.sort-t.sort)).map((e=>e.value)),wu=(e,t)=>e.length===t.length&&e.every(((e,r)=>e===t[r]))?t.reverse():t,Cu=e=>e.appearance&&e.appearance.shuffleQuestions&&!e.enable_partial_responses?wu(e.questions,Su(e.questions)):e.questions,Iu=e=>{var t;return!(null==(t=e.conditions)||null==(t=t.events)||!t.repeatedActivation||!(e=>{var t,r;return null!=(null==(t=e.conditions)||null==(t=t.events)||null==(t=t.values)?void 0:t.length)&&(null==(r=e.conditions)||null==(r=r.events)||null==(r=r.values)?void 0:r.length)>0})(e))||e.schedule===Ko.Always||Pu(e)},ku=e=>{var t=""+sa+e.id;return e.current_iteration&&e.current_iteration>0&&(t=""+sa+e.id+"_"+e.current_iteration),t},Eu=function(e,t){var r={__c:t="__cC"+zs++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var r,i;return this.getChildContext||(r=[],(i={})[t]=this,this.getChildContext=function(){return i},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&r.some((function(e){e.__e=!0,so(e)}))},this.sub=function(e){r.push(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){r.splice(r.indexOf(e),1),t&&t.call(e)}}),e.children}};return r.Provider.__=r.Consumer.contextType=r}({isPreviewMode:!1,previewPageIndex:0,onPopupSurveyDismissed:()=>{},isPopup:!0,onPreviewSubmit:()=>{},surveySubmissionId:""}),xu=()=>qo(Eu),Tu=e=>{var{component:t,children:r,renderAsHtml:i,style:n}=e;return So(t,i?{dangerouslySetInnerHTML:{__html:r},style:n}:{children:r,style:n})};function Mu(e){return null!=e?e:"icontains"}function Ru(e){var t,r,i;if(null==(t=e.conditions)||!t.url)return!0;var n=null==uu||null==(r=uu.location)?void 0:r.href;if(!n)return!1;var s=[e.conditions.url],o=Mu(null==(i=e.conditions)?void 0:i.urlMatchType);return ou[o](s,[n])}var Au=e=>{var t=""+oa+e.id;return e.current_iteration&&e.current_iteration>0&&(t=""+oa+e.id+"_"+e.current_iteration),t},Fu=(e,t)=>{try{localStorage.setItem(Au(e),JSON.stringify(t))}catch(e){ta.error("Error setting in-progress survey state in localStorage",e)}},Nu=e=>{try{var t=localStorage.getItem(Au(e));if(t)return JSON.parse(t)}catch(e){ta.error("Error getting in-progress survey state from localStorage",e)}return null},Pu=e=>{var t=Nu(e);return!mi(null==t?void 0:t.surveySubmissionId)},Ou=e=>{try{localStorage.removeItem(Au(e))}catch(e){ta.error("Error clearing in-progress survey state from localStorage",e)}};function Lu(e,t){void 0===t&&(t=!1);var r="PostHogSurvey-"+e.id;return t?"."+r:r}var Du=0;function Bu(e,t,r,i,n,s){var o,a,l={};for(a in t)"ref"==a?o=t[a]:l[a]=t[a];var u={type:e,props:l,key:r,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--Du,__i:-1,__u:0,__source:n,__self:s};if("function"==typeof e&&(o=e.defaultProps))for(a in o)void 0===l[a]&&(l[a]=o[a]);return Hs.vnode&&Hs.vnode(u),u}var qu=Bu("svg",{className:"emoji-svg",xmlns:"http://www.w3.org/2000/svg",height:"48",viewBox:"0 -960 960 960",width:"48",children:Bu("path",{d:"M626-533q22.5 0 38.25-15.75T680-587q0-22.5-15.75-38.25T626-641q-22.5 0-38.25 15.75T572-587q0 22.5 15.75 38.25T626-533Zm-292 0q22.5 0 38.25-15.75T388-587q0-22.5-15.75-38.25T334-641q-22.5 0-38.25 15.75T280-587q0 22.5 15.75 38.25T334-533Zm146 272q66 0 121.5-35.5T682-393h-52q-23 40-63 61.5T480.5-310q-46.5 0-87-21T331-393h-53q26 61 81 96.5T480-261Zm0 181q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 340q142.375 0 241.188-98.812Q820-337.625 820-480t-98.812-241.188Q622.375-820 480-820t-241.188 98.812Q140-622.375 140-480t98.812 241.188Q337.625-140 480-140Z"})}),Hu=Bu("svg",{className:"emoji-svg",xmlns:"http://www.w3.org/2000/svg",height:"48",viewBox:"0 -960 960 960",width:"48",children:Bu("path",{d:"M626-533q22.5 0 38.25-15.75T680-587q0-22.5-15.75-38.25T626-641q-22.5 0-38.25 15.75T572-587q0 22.5 15.75 38.25T626-533Zm-292 0q22.5 0 38.25-15.75T388-587q0-22.5-15.75-38.25T334-641q-22.5 0-38.25 15.75T280-587q0 22.5 15.75 38.25T334-533Zm20 194h253v-49H354v49ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 340q142.375 0 241.188-98.812Q820-337.625 820-480t-98.812-241.188Q622.375-820 480-820t-241.188 98.812Q140-622.375 140-480t98.812 241.188Q337.625-140 480-140Z"})}),$u=Bu("svg",{className:"emoji-svg",xmlns:"http://www.w3.org/2000/svg",height:"48",viewBox:"0 -960 960 960",width:"48",children:Bu("path",{d:"M626-533q22.5 0 38.25-15.75T680-587q0-22.5-15.75-38.25T626-641q-22.5 0-38.25 15.75T572-587q0 22.5 15.75 38.25T626-533Zm-292 0q22.5 0 38.25-15.75T388-587q0-22.5-15.75-38.25T334-641q-22.5 0-38.25 15.75T280-587q0 22.5 15.75 38.25T334-533Zm146.174 116Q413-417 358.5-379.5T278-280h53q22-42 62.173-65t87.5-23Q528-368 567.5-344.5T630-280h52q-25-63-79.826-100-54.826-37-122-37ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 340q142.375 0 241.188-98.812Q820-337.625 820-480t-98.812-241.188Q622.375-820 480-820t-241.188 98.812Q140-622.375 140-480t98.812 241.188Q337.625-140 480-140Z"})}),Vu=Bu("svg",{className:"emoji-svg",xmlns:"http://www.w3.org/2000/svg",height:"48",viewBox:"0 -960 960 960",width:"48",children:Bu("path",{d:"M480-417q-67 0-121.5 37.5T278-280h404q-25-63-80-100t-122-37Zm-183-72 50-45 45 45 31-36-45-45 45-45-31-36-45 45-50-45-31 36 45 45-45 45 31 36Zm272 0 44-45 51 45 31-36-45-45 45-45-31-36-51 45-44-45-31 36 44 45-44 45 31 36ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 340q142 0 241-99t99-241q0-142-99-241t-241-99q-142 0-241 99t-99 241q0 142 99 241t241 99Z"})}),Wu=Bu("svg",{className:"emoji-svg",xmlns:"http://www.w3.org/2000/svg",height:"48",viewBox:"0 -960 960 960",width:"48",children:Bu("path",{d:"M479.504-261Q537-261 585.5-287q48.5-26 78.5-72.4 6-11.6-.75-22.6-6.75-11-20.25-11H316.918Q303-393 296.5-382t-.5 22.6q30 46.4 78.5 72.4 48.5 26 105.004 26ZM347-578l27 27q7.636 8 17.818 8Q402-543 410-551q8-8 8-18t-8-18l-42-42q-8.8-9-20.9-9-12.1 0-21.1 9l-42 42q-8 7.636-8 17.818Q276-559 284-551q8 8 18 8t18-8l27-27Zm267 0 27 27q7.714 8 18 8t18-8q8-7.636 8-17.818Q685-579 677-587l-42-42q-8.8-9-20.9-9-12.1 0-21.1 9l-42 42q-8 7.714-8 18t8 18q7.636 8 17.818 8Q579-543 587-551l27-27ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 340q142.375 0 241.188-98.812Q820-337.625 820-480t-98.812-241.188Q622.375-820 480-820t-241.188 98.812Q140-622.375 140-480t98.812 241.188Q337.625-140 480-140Z"})}),Uu=Bu("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-labelledby":"close-survey-title",children:[Bu("title",{id:"close-survey-title",children:"Close survey"}),Bu("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M0.164752 0.164752C0.384422 -0.0549175 0.740578 -0.0549175 0.960248 0.164752L6 5.20451L11.0398 0.164752C11.2594 -0.0549175 11.6156 -0.0549175 11.8352 0.164752C12.0549 0.384422 12.0549 0.740578 11.8352 0.960248L6.79549 6L11.8352 11.0398C12.0549 11.2594 12.0549 11.6156 11.8352 11.8352C11.6156 12.0549 11.2594 12.0549 11.0398 11.8352L6 6.79549L0.960248 11.8352C0.740578 12.0549 0.384422 12.0549 0.164752 11.8352C-0.0549175 11.6156 -0.0549175 11.2594 0.164752 11.0398L5.20451 6L0.164752 0.960248C-0.0549175 0.740578 -0.0549175 0.384422 0.164752 0.164752Z",fill:"black"})]}),Zu=Bu("svg",{width:"77",height:"14",viewBox:"0 0 77 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[Bu("g",{"clip-path":"url(#clip0_2415_6911)",children:[Bu("mask",{id:"mask0_2415_6911",style:{maskType:"luminance"},maskUnits:"userSpaceOnUse",x:"0",y:"0",width:"77",height:"14",children:Bu("path",{d:"M0.5 0H76.5V14H0.5V0Z",fill:"white"})}),Bu("g",{mask:"url(#mask0_2415_6911)",children:[Bu("path",{d:"M5.77226 8.02931C5.59388 8.37329 5.08474 8.37329 4.90634 8.02931L4.4797 7.20672C4.41155 7.07535 4.41155 6.9207 4.4797 6.78933L4.90634 5.96669C5.08474 5.62276 5.59388 5.62276 5.77226 5.96669L6.19893 6.78933C6.26709 6.9207 6.26709 7.07535 6.19893 7.20672L5.77226 8.02931ZM5.77226 12.6946C5.59388 13.0386 5.08474 13.0386 4.90634 12.6946L4.4797 11.872C4.41155 11.7406 4.41155 11.586 4.4797 11.4546L4.90634 10.632C5.08474 10.288 5.59388 10.288 5.77226 10.632L6.19893 11.4546C6.26709 11.586 6.26709 11.7406 6.19893 11.872L5.77226 12.6946Z",fill:"#1D4AFF"}),Bu("path",{d:"M0.5 10.9238C0.5 10.508 1.02142 10.2998 1.32637 10.5938L3.54508 12.7327C3.85003 13.0267 3.63405 13.5294 3.20279 13.5294H0.984076C0.716728 13.5294 0.5 13.3205 0.5 13.0627V10.9238ZM0.5 8.67083C0.5 8.79459 0.551001 8.91331 0.641783 9.00081L5.19753 13.3927C5.28831 13.4802 5.41144 13.5294 5.53982 13.5294H8.0421C8.47337 13.5294 8.68936 13.0267 8.3844 12.7327L1.32637 5.92856C1.02142 5.63456 0.5 5.84278 0.5 6.25854V8.67083ZM0.5 4.00556C0.5 4.12932 0.551001 4.24802 0.641783 4.33554L10.0368 13.3927C10.1276 13.4802 10.2508 13.5294 10.3791 13.5294H12.8814C13.3127 13.5294 13.5287 13.0267 13.2237 12.7327L1.32637 1.26329C1.02142 0.969312 0.5 1.17752 0.5 1.59327V4.00556ZM5.33931 4.00556C5.33931 4.12932 5.39033 4.24802 5.4811 4.33554L14.1916 12.7327C14.4965 13.0267 15.0179 12.8185 15.0179 12.4028V9.99047C15.0179 9.86671 14.9669 9.74799 14.8762 9.66049L6.16568 1.26329C5.86071 0.969307 5.33931 1.17752 5.33931 1.59327V4.00556ZM11.005 1.26329C10.7 0.969307 10.1786 1.17752 10.1786 1.59327V4.00556C10.1786 4.12932 10.2296 4.24802 10.3204 4.33554L14.1916 8.06748C14.4965 8.36148 15.0179 8.15325 15.0179 7.7375V5.3252C15.0179 5.20144 14.9669 5.08272 14.8762 4.99522L11.005 1.26329Z",fill:"#F9BD2B"}),Bu("path",{d:"M21.0852 10.981L16.5288 6.58843C16.2238 6.29443 15.7024 6.50266 15.7024 6.91841V13.0627C15.7024 13.3205 15.9191 13.5294 16.1865 13.5294H23.2446C23.5119 13.5294 23.7287 13.3205 23.7287 13.0627V12.5032C23.7287 12.2455 23.511 12.0396 23.2459 12.0063C22.4323 11.9042 21.6713 11.546 21.0852 10.981ZM18.0252 12.0365C17.5978 12.0365 17.251 11.7021 17.251 11.2901C17.251 10.878 17.5978 10.5436 18.0252 10.5436C18.4527 10.5436 18.7996 10.878 18.7996 11.2901C18.7996 11.7021 18.4527 12.0365 18.0252 12.0365Z",fill:"currentColor"}),Bu("path",{d:"M0.5 13.0627C0.5 13.3205 0.716728 13.5294 0.984076 13.5294H3.20279C3.63405 13.5294 3.85003 13.0267 3.54508 12.7327L1.32637 10.5938C1.02142 10.2998 0.5 10.508 0.5 10.9238V13.0627ZM5.33931 5.13191L1.32637 1.26329C1.02142 0.969306 0.5 1.17752 0.5 1.59327V4.00556C0.5 4.12932 0.551001 4.24802 0.641783 4.33554L5.33931 8.86412V5.13191ZM1.32637 5.92855C1.02142 5.63455 0.5 5.84278 0.5 6.25853V8.67083C0.5 8.79459 0.551001 8.91331 0.641783 9.00081L5.33931 13.5294V9.79717L1.32637 5.92855Z",fill:"#1D4AFF"}),Bu("path",{d:"M10.1787 5.3252C10.1787 5.20144 10.1277 5.08272 10.0369 4.99522L6.16572 1.26329C5.8608 0.969306 5.33936 1.17752 5.33936 1.59327V4.00556C5.33936 4.12932 5.39037 4.24802 5.48114 4.33554L10.1787 8.86412V5.3252ZM5.33936 13.5294H8.04214C8.47341 13.5294 8.6894 13.0267 8.38443 12.7327L5.33936 9.79717V13.5294ZM5.33936 5.13191V8.67083C5.33936 8.79459 5.39037 8.91331 5.48114 9.00081L10.1787 13.5294V9.99047C10.1787 9.86671 10.1277 9.74803 10.0369 9.66049L5.33936 5.13191Z",fill:"#F54E00"}),Bu("path",{d:"M29.375 11.6667H31.3636V8.48772H33.0249C34.8499 8.48772 36.0204 7.4443 36.0204 5.83052C36.0204 4.21681 34.8499 3.17334 33.0249 3.17334H29.375V11.6667ZM31.3636 6.84972V4.81136H32.8236C33.5787 4.81136 34.0318 5.19958 34.0318 5.83052C34.0318 6.4615 33.5787 6.84972 32.8236 6.84972H31.3636ZM39.618 11.7637C41.5563 11.7637 42.9659 10.429 42.9659 8.60905C42.9659 6.78905 41.5563 5.45438 39.618 5.45438C37.6546 5.45438 36.2701 6.78905 36.2701 8.60905C36.2701 10.429 37.6546 11.7637 39.618 11.7637ZM38.1077 8.60905C38.1077 7.63838 38.7118 6.97105 39.618 6.97105C40.5116 6.97105 41.1157 7.63838 41.1157 8.60905C41.1157 9.57972 40.5116 10.2471 39.618 10.2471C38.7118 10.2471 38.1077 9.57972 38.1077 8.60905ZM46.1482 11.7637C47.6333 11.7637 48.6402 10.8658 48.6402 9.81025C48.6402 7.33505 45.2294 8.13585 45.2294 7.16518C45.2294 6.8983 45.5189 6.72843 45.9342 6.72843C46.3622 6.72843 46.8782 6.98318 47.0418 7.54132L48.527 6.94678C48.2375 6.06105 47.1677 5.45438 45.8713 5.45438C44.4743 5.45438 43.6058 6.25518 43.6058 7.21372C43.6058 9.53118 46.9663 8.88812 46.9663 9.84665C46.9663 10.1864 46.6391 10.417 46.1482 10.417C45.4434 10.417 44.9525 9.94376 44.8015 9.3735L43.3164 9.93158C43.6436 10.8537 44.6001 11.7637 46.1482 11.7637ZM53.4241 11.606L53.2982 10.0651C53.0843 10.1743 52.8074 10.2106 52.5808 10.2106C52.1278 10.2106 51.8257 9.89523 51.8257 9.34918V7.03172H53.3612V5.55145H51.8257V3.78001H49.9755V5.55145H48.9687V7.03172H49.9755V9.57972C49.9755 11.06 51.0202 11.7637 52.3921 11.7637C52.7696 11.7637 53.122 11.7031 53.4241 11.606ZM59.8749 3.17334V6.47358H56.376V3.17334H54.3874V11.6667H56.376V8.11158H59.8749V11.6667H61.8761V3.17334H59.8749ZM66.2899 11.7637C68.2281 11.7637 69.6378 10.429 69.6378 8.60905C69.6378 6.78905 68.2281 5.45438 66.2899 5.45438C64.3265 5.45438 62.942 6.78905 62.942 8.60905C62.942 10.429 64.3265 11.7637 66.2899 11.7637ZM64.7796 8.60905C64.7796 7.63838 65.3837 6.97105 66.2899 6.97105C67.1835 6.97105 67.7876 7.63838 67.7876 8.60905C67.7876 9.57972 67.1835 10.2471 66.2899 10.2471C65.3837 10.2471 64.7796 9.57972 64.7796 8.60905ZM73.2088 11.4725C73.901 11.4725 74.5177 11.242 74.845 10.8416V11.424C74.845 12.1034 74.2786 12.5767 73.4102 12.5767C72.7935 12.5767 72.2523 12.2854 72.1642 11.788L70.4776 12.0428C70.7042 13.1955 71.925 13.972 73.4102 13.972C75.361 13.972 76.6574 12.8679 76.6574 11.2298V5.55145H74.8324V6.07318C74.4926 5.69705 73.9136 5.45438 73.171 5.45438C71.409 5.45438 70.3014 6.61918 70.3014 8.46345C70.3014 10.3077 71.409 11.4725 73.2088 11.4725ZM72.1012 8.46345C72.1012 7.55345 72.655 6.97105 73.5109 6.97105C74.3793 6.97105 74.9331 7.55345 74.9331 8.46345C74.9331 9.37345 74.3793 9.95585 73.5109 9.95585C72.655 9.95585 72.1012 9.37345 72.1012 8.46345Z",fill:"currentColor"})]})]}),Bu("defs",{children:Bu("clipPath",{id:"clip0_2415_6911",children:Bu("rect",{width:"76",height:"14",fill:"white",transform:"translate(0.5)"})})})]});function zu(){return Bu("a",{href:"https://posthog.com/surveys",target:"_blank",rel:"noopener",className:"footer-branding",children:["Survey by ",Zu]})}function Gu(e){var{text:t,submitDisabled:r,appearance:i,onSubmit:n,link:s,onPreviewSubmit:o,skipSubmitButton:a}=e,{isPreviewMode:l}=qo(Eu);return Bu("div",{className:"bottom-section",children:[!a&&Bu("button",{className:"form-submit",disabled:r,"aria-label":"Submit survey",type:"button",onClick:()=>{s&&(null==Br||Br.open(s)),l?null==o||o():n()},children:t}),!i.whiteLabel&&Bu(zu,{})]})}function ju(e){var{question:t,forceDisableHtml:r,htmlFor:i}=e;return Bu("div",{class:"question-header",children:[Bu(t.type===Xo.Open?"label":"h3",{className:"survey-question",htmlFor:i,children:t.question}),t.description&&Tu({component:Qs("p",{className:"survey-question-description"}),children:t.description,renderAsHtml:!r&&"text"!==t.descriptionContentType})]})}function Yu(e){var{onClick:t}=e,{isPreviewMode:r}=qo(Eu);return Bu("button",{className:"form-cancel",onClick:t,disabled:r,"aria-label":"Close survey",type:"button",children:Uu})}Bu("svg",{width:"16",height:"12",viewBox:"0 0 16 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:Bu("path",{d:"M5.30769 10.6923L4.77736 11.2226C4.91801 11.3633 5.10878 11.4423 5.30769 11.4423C5.5066 11.4423 5.69737 11.3633 5.83802 11.2226L5.30769 10.6923ZM15.5303 1.53033C15.8232 1.23744 15.8232 0.762563 15.5303 0.46967C15.2374 0.176777 14.7626 0.176777 14.4697 0.46967L15.5303 1.53033ZM1.53033 5.85429C1.23744 5.56139 0.762563 5.56139 0.46967 5.85429C0.176777 6.14718 0.176777 6.62205 0.46967 6.91495L1.53033 5.85429ZM5.83802 11.2226L15.5303 1.53033L14.4697 0.46967L4.77736 10.162L5.83802 11.2226ZM0.46967 6.91495L4.77736 11.2226L5.83802 10.162L1.53033 5.85429L0.46967 6.91495Z",fill:"currentColor"})});var Xu=Br;function Ju(e){var{header:t,description:r,contentType:i,forceDisableHtml:n,appearance:s,onClose:o}=e,{isPopup:a}=qo(Eu);return Lo((()=>{var e=e=>{"Enter"!==e.key&&"Escape"!==e.key||(e.preventDefault(),o())};return $i(Xu,"keydown",e),()=>{Xu.removeEventListener("keydown",e)}}),[o]),Bu("div",{className:"thank-you-message",role:"status",tabIndex:0,"aria-atomic":"true",children:[a&&Bu(Yu,{onClick:()=>o()}),Bu("h3",{className:"thank-you-message-header",children:t}),r&&Tu({component:Qs("p",{className:"thank-you-message-body"}),children:r,renderAsHtml:!n&&"text"!==i}),a&&Bu(Gu,{text:s.thankYouMessageCloseButtonText||"Close",submitDisabled:!1,appearance:s,onSubmit:()=>o()})]})}var Ku=e=>li(e)&&e.every((e=>gi(e)));function Qu(e){var{question:t,forceDisableHtml:r,appearance:i,onSubmit:n,onPreviewSubmit:s,displayQuestionIndex:o,initialValue:a}=e,{isPreviewMode:l}=xu(),u=Do(null),[c,d]=Oo((()=>gi(a)?a:""));Lo((()=>{setTimeout((()=>{var e;l||(null==(e=u.current)||e.focus())}),100)}),[l]);var h="surveyQuestion"+o;return Bu(to,{children:[Bu("div",{className:"question-container",children:[Bu(ju,{question:t,forceDisableHtml:r,htmlFor:h}),Bu("textarea",{ref:u,id:h,rows:4,placeholder:null==i?void 0:i.placeholder,onInput:e=>{d(e.currentTarget.value),e.stopPropagation()},onKeyDown:e=>{e.stopPropagation()},value:c})]}),Bu(Gu,{text:t.buttonText||"Submit",submitDisabled:!c&&!t.optional,appearance:i,onSubmit:()=>n(c),onPreviewSubmit:()=>s(c)})]})}function ec(e){var{question:t,forceDisableHtml:r,appearance:i,onSubmit:n,onPreviewSubmit:s}=e;return Bu(to,{children:[Bu("div",{className:"question-container",children:Bu(ju,{question:t,forceDisableHtml:r})}),Bu(Gu,{text:t.buttonText||"Submit",submitDisabled:!1,link:t.link,appearance:i,onSubmit:()=>n("link clicked"),onPreviewSubmit:()=>s("link clicked")})]})}function tc(e){var{question:t,forceDisableHtml:r,displayQuestionIndex:i,appearance:n,onSubmit:s,onPreviewSubmit:o,initialValue:a}=e,l=t.scale,u=10===t.scale?0:1,[c,d]=Oo((()=>yi(a)?a:li(a)&&a.length>0&&yi(parseInt(a[0]))?parseInt(a[0]):gi(a)&&yi(parseInt(a))?parseInt(a):null)),{isPreviewMode:h}=xu(),p=e=>h?o(e):s(e);return Bu(to,{children:[Bu("div",{className:"question-container",children:[Bu(ju,{question:t,forceDisableHtml:r}),Bu("div",{className:"rating-section",children:[Bu("div",{className:"rating-options",children:["emoji"===t.display&&Bu("div",{className:"rating-options-emoji",children:(3===t.scale?nc:sc).map(((e,r)=>Bu("button",{"aria-label":"Rate "+(r+1),className:"ratings-emoji question-"+i+"-rating-"+r+" "+(r+1===c?"rating-active":""),value:r+1,type:"button",onClick:()=>{var e=r+1;d(e),t.skipSubmitButton&&p(e)},children:e},r)))}),"number"===t.display&&Bu("div",{className:"rating-options-number",style:{gridTemplateColumns:"repeat("+(l-u+1)+", minmax(0, 1fr))"},children:uc(t.scale).map(((e,r)=>Bu(rc,{displayQuestionIndex:i,active:c===e,appearance:n,num:e,setActiveNumber:e=>{d(e),t.skipSubmitButton&&p(e)}},r)))})]}),Bu("div",{className:"rating-text",children:[Bu("div",{children:t.lowerBoundLabel}),Bu("div",{children:t.upperBoundLabel})]})]})]}),Bu(Gu,{text:t.buttonText||(null==n?void 0:n.submitButtonText)||"Submit",submitDisabled:vi(c)&&!t.optional,appearance:n,onSubmit:()=>s(c),onPreviewSubmit:()=>o(c),skipSubmitButton:t.skipSubmitButton})]})}function rc(e){var{num:t,active:r,displayQuestionIndex:i,setActiveNumber:n}=e;return Bu("button",{"aria-label":"Rate "+t,className:"ratings-number question-"+i+"-rating-"+t+" "+(r?"rating-active":""),type:"button",onClick:()=>{n(t)},children:t})}function ic(e){var{question:t,forceDisableHtml:i,displayQuestionIndex:n,appearance:s,onSubmit:o,onPreviewSubmit:a,initialValue:l}=e,u=Do(null),c=Bo((()=>(e=>{if(!e.shuffleOptions)return e.choices;var t=e.choices,r="";e.hasOpenChoice&&(r=t.pop());var i=wu(t,Su(t));return e.hasOpenChoice&&(e.choices.push(r),i.push(r)),i})(t)),[t]),[d,h]=Oo((()=>((e,t)=>gi(e)||Ku(e)?e:t===Xo.SingleChoice?null:[])(l,t.type))),[p,_]=Oo((()=>((e,t)=>{if(gi(e)&&!t.includes(e))return{isSelected:!0,inputValue:e};if(Ku(e)){var r=e.find((e=>!t.includes(e)));if(r)return{isSelected:!0,inputValue:r}}return{isSelected:!1,inputValue:""}})(l,c))),{isPreviewMode:g}=xu(),f=t.type===Xo.SingleChoice,v=t.type===Xo.MultipleChoice,m=t.skipSubmitButton&&f&&!t.hasOpenChoice,y=(e,t)=>{if(t){var i=!p.isSelected;return _((e=>r({},e,{isSelected:i,inputValue:i?e.inputValue:""}))),f&&h(""),void(i&&setTimeout((()=>{var e;return null==(e=u.current)?void 0:e.focus()}),75))}if(f)return h(e),_((e=>r({},e,{isSelected:!1,inputValue:""}))),void(m&&(o(e),g&&a(e)));v&&li(d)&&(d.includes(e)?h(d.filter((t=>t!==e))):h([...d,e]))},b=e=>{e.stopPropagation();var t=e.currentTarget.value;_((e=>r({},e,{inputValue:t}))),f&&h(t)},S=e=>{e.stopPropagation(),"Enter"!==e.key||w()||(e.preventDefault(),C()),"Escape"===e.key&&(e.preventDefault(),_((e=>r({},e,{isSelected:!1,inputValue:""}))),f&&h(null))},w=()=>!t.optional&&(!!vi(d)||(!(!li(d)||p.isSelected||0!==d.length)||!(!p.isSelected||""!==p.inputValue.trim()))),C=()=>{p.isSelected&&v?li(d)&&(g?a([...d,p.inputValue]):o([...d,p.inputValue])):g?a(d):o(d)};return Bu(to,{children:[Bu("div",{className:"question-container",children:[Bu(ju,{question:t,forceDisableHtml:i}),Bu("fieldset",{className:"multiple-choice-options limit-height",children:[Bu("legend",{className:"sr-only",children:v?" Select all that apply":" Select one"}),c.map(((e,r)=>{var i=!!t.hasOpenChoice&&r===t.choices.length-1,s="surveyQuestion"+n+"Choice"+r,o=s+"Open",a=i?p.isSelected:f?d===e:li(d)&&d.includes(e);return Bu("label",{className:i?"choice-option-open":"",children:[Bu("input",{type:f?"radio":"checkbox",name:s,checked:a,onChange:()=>y(e,i),id:s,"aria-controls":o}),Bu("span",{children:i?e+":":e}),i&&Bu("input",{type:"text",ref:u,id:o,name:"question"+n+"Open",value:p.inputValue,onKeyDown:S,onInput:b,onClick:t=>{p.isSelected||y(e,!0),t.stopPropagation()},"aria-label":e+" - please specify"})]},r)}))]})]}),Bu(Gu,{text:t.buttonText||"Submit",submitDisabled:w(),appearance:s,onSubmit:C,onPreviewSubmit:C,skipSubmitButton:m})]})}var nc=[$u,Hu,qu],sc=[Vu,$u,Hu,qu,Wu],oc=[1,2,3,4,5],ac=[1,2,3,4,5,6,7],lc=[0,1,2,3,4,5,6,7,8,9,10];function uc(e){switch(e){case 5:default:return oc;case 7:return ac;case 10:return lc}}var cc=Br,dc=Ur,hc="ph:show_survey_widget",pc="PHWidgetSurveyClickListener";function _c(e,t,r){var i,n=e.questions[t],s=t+1;if(null==(i=n.branching)||!i.type)return t===e.questions.length-1?Jo.End:s;if(n.branching.type===Jo.End)return Jo.End;if(n.branching.type===Jo.SpecificQuestion){if(Number.isInteger(n.branching.index))return n.branching.index}else if(n.branching.type===Jo.ResponseBased){if(n.type===Xo.SingleChoice){var o,a=n.choices.indexOf(""+r);if(-1===a&&n.hasOpenChoice&&(a=n.choices.length-1),null!=(o=n.branching)&&null!=(o=o.responseValues)&&o.hasOwnProperty(a)){var l=n.branching.responseValues[a];return Number.isInteger(l)?l:l===Jo.End?Jo.End:s}}else if(n.type===Xo.Rating){var u;if("number"!=typeof r||!Number.isInteger(r))throw new Error("The response type must be an integer");var c=function(e,t){if(3===t){if(e<1||e>3)throw new Error("The response must be in range 1-3");return 1===e?"negative":2===e?"neutral":"positive"}if(5===t){if(e<1||e>5)throw new Error("The response must be in range 1-5");return e<=2?"negative":3===e?"neutral":"positive"}if(7===t){if(e<1||e>7)throw new Error("The response must be in range 1-7");return e<=3?"negative":4===e?"neutral":"positive"}if(10===t){if(e<0||e>10)throw new Error("The response must be in range 0-10");return e<=6?"detractors":e<=8?"passives":"promoters"}throw new Error("The scale must be one of: 3, 5, 7, 10")}(r,n.scale);if(null!=(u=n.branching)&&null!=(u=u.responseValues)&&u.hasOwnProperty(c)){var d=n.branching.responseValues[c];return Number.isInteger(d)?d:d===Jo.End?Jo.End:s}}return s}return ta.warn("Falling back to next question index due to unexpected branching type"),s}var gc=250,fc=20,vc=12;class mc{constructor(e){var t=this;this._surveyTimeouts=new Map,this._widgetSelectorListeners=new Map,this._handlePopoverSurvey=e=>{var t;this._clearSurveyTimeout(e.id),this._addSurveyToFocus(e);var i=(null==(t=e.appearance)?void 0:t.surveyPopupDelaySeconds)||0,{shadow:n}=vu(e,this._posthog);if(i<=0)return bo(Bu(wc,{posthog:this._posthog,survey:e,removeSurveyFromFocus:this._removeSurveyFromFocus}),n);var s=setTimeout((()=>{if(!Ru(e))return this._removeSurveyFromFocus(e);bo(Bu(wc,{posthog:this._posthog,survey:r({},e,{appearance:r({},e.appearance,{surveyPopupDelaySeconds:0})}),removeSurveyFromFocus:this._removeSurveyFromFocus}),n)}),1e3*i);this._surveyTimeouts.set(e.id,s)},this._handleWidget=e=>{var{shadow:t,isNewlyCreated:r}=vu(e,this._posthog);r&&bo(Bu(Ic,{posthog:this._posthog,survey:e},e.id),t)},this._removeWidgetSelectorListener=e=>{this._removeSurveyFromDom(e);var t=this._widgetSelectorListeners.get(e.id);t&&(t.element.removeEventListener("click",t.listener),t.element.removeAttribute(pc),this._widgetSelectorListeners.delete(e.id),ta.info("Removed click listener for survey "+e.id))},this._manageWidgetSelectorListener=(e,t)=>{var r=dc.querySelector(t),i=this._widgetSelectorListeners.get(e.id);if(r){if(this._handleWidget(e),i){if(r===i.element)return;ta.info("Selector element changed for survey "+e.id+". Re-attaching listener."),this._removeWidgetSelectorListener(e)}if(!r.hasAttribute(pc)){var n=t=>{var r,i;t.stopPropagation();var n=(null==(r=e.appearance)?void 0:r.position)===jo.NextToTrigger?function(e,t){try{var r=e.getBoundingClientRect(),i=cc.innerHeight,n=cc.innerWidth,s=gc,o=r.left+r.width/2-t/2;o+t>n-fc&&(o=n-t-fc),o<fc&&(o=fc);var a=vc,l=i-r.bottom,u=r.top,c=l<s&&u>l;return{position:"fixed",top:c?"auto":r.bottom+a+"px",left:o+"px",right:"auto",bottom:c?i-r.top+a+"px":"auto",zIndex:pu.zIndex}}catch(e){return ta.warn("Failed to calculate trigger position:",e),null}}(t.currentTarget,parseInt((null==(i=e.appearance)?void 0:i.maxWidth)||pu.maxWidth)):{};cc.dispatchEvent(new CustomEvent(hc,{detail:{surveyId:e.id,position:n}}))};$i(r,"click",n),r.setAttribute(pc,"true"),this._widgetSelectorListeners.set(e.id,{element:r,listener:n,survey:e}),ta.info("Attached click listener for feedback button survey "+e.id)}}else i&&this._removeWidgetSelectorListener(e)},this.renderSurvey=(e,t)=>{bo(Bu(wc,{posthog:this._posthog,survey:e,removeSurveyFromFocus:this._removeSurveyFromFocus,isPopup:!1}),t)},this.getActiveMatchingSurveys=function(e,r){var i;void 0===r&&(r=!1),null==(i=t._posthog)||i.surveys.getSurveys((r=>{var i=r.filter((e=>t.checkSurveyEligibility(e).eligible&&t._isSurveyConditionMatched(e)&&t._hasActionOrEventTriggeredSurvey(e)&&t._checkFlags(e)));e(i)}),r)},this.callSurveysAndEvaluateDisplayLogic=function(e){void 0===e&&(e=!1),t.getActiveMatchingSurveys((e=>{var r=e.filter((e=>e.type!==Yo.API)),i=t._sortSurveysByAppearanceDelay(r),n=new Set;i.forEach((e=>{if(e.type===Yo.Widget){var r,i,s,o;if((null==(r=e.appearance)?void 0:r.widgetType)===Go.Tab)return void t._handleWidget(e);if((null==(i=e.appearance)?void 0:i.widgetType)===Go.Selector&&null!=(s=e.appearance)&&s.widgetSelector)n.add(e.id),t._manageWidgetSelectorListener(e,null==(o=e.appearance)?void 0:o.widgetSelector)}vi(t._surveyInFocus)&&e.type===Yo.Popover&&t._handlePopoverSurvey(e)})),t._widgetSelectorListeners.forEach((e=>{var{survey:r}=e;n.has(r.id)||t._removeWidgetSelectorListener(r)}))}),e)},this._addSurveyToFocus=e=>{vi(this._surveyInFocus)||ta.error("Survey "+[...this._surveyInFocus]+" already in focus. Cannot add survey "+e.id+"."),this._surveyInFocus=e.id},this._removeSurveyFromFocus=e=>{this._surveyInFocus!==e.id&&ta.error("Survey "+e.id+" is not in focus. Cannot remove survey "+e.id+"."),this._clearSurveyTimeout(e.id),this._surveyInFocus=null,this._removeSurveyFromDom(e)},this._posthog=e,this._surveyInFocus=null}_clearSurveyTimeout(e){var t=this._surveyTimeouts.get(e);t&&(clearTimeout(t),this._surveyTimeouts.delete(e))}_sortSurveysByAppearanceDelay(e){return e.sort(((e,t)=>{var r,i,n=Pu(e),s=Pu(t);if(n&&!s)return-1;if(!n&&s)return 1;var o=e.schedule===Ko.Always,a=t.schedule===Ko.Always;return o&&!a?1:!o&&a?-1:((null==(r=e.appearance)?void 0:r.surveyPopupDelaySeconds)||0)-((null==(i=t.appearance)?void 0:i.surveyPopupDelaySeconds)||0)}))}_isSurveyFeatureFlagEnabled(e){return!e||!!this._posthog.featureFlags.isFeatureEnabled(e,{send_event:!e.startsWith("survey-targeting-")})}_isSurveyConditionMatched(e){return!e.conditions||Ru(e)&&function(e){var t,r,i;if(null==(t=e.conditions)||!t.deviceTypes||0===(null==(r=e.conditions)?void 0:r.deviceTypes.length))return!0;if(!Yr)return!1;var n=hl(Yr);return ou[Mu(null==(i=e.conditions)?void 0:i.deviceTypesMatchType)](e.conditions.deviceTypes,[n])}(e)&&function(e){var t;return null==(t=e.conditions)||!t.selector||!(null==cu||!cu.querySelector(e.conditions.selector))}(e)}_internalFlagCheckSatisfied(e){return Iu(e)||this._isSurveyFeatureFlagEnabled(e.internal_targeting_flag_key)||Pu(e)}checkSurveyEligibility(e){var t,r={eligible:!0,reason:void 0};return ra(e)?this._isSurveyFeatureFlagEnabled(e.linked_flag_key)?this._isSurveyFeatureFlagEnabled(e.targeting_flag_key)?this._internalFlagCheckSatisfied(e)?(e=>{var t=localStorage.getItem("lastSeenSurveyDate");if(!e||!t)return!0;var r=new Date,i=Math.abs(r.getTime()-new Date(t).getTime());return Math.ceil(i/864e5)>e})(null==(t=e.conditions)?void 0:t.seenSurveyWaitPeriodInDays)?(e=>!!localStorage.getItem(ku(e))&&!Iu(e))(e)?(r.eligible=!1,r.reason="Survey has already been seen and it can't be activated again",r):r:(r.eligible=!1,r.reason="Survey wait period has not passed",r):(r.eligible=!1,r.reason="Survey internal targeting flag is not enabled and survey cannot activate repeatedly and survey is not in progress",r):(r.eligible=!1,r.reason="Survey targeting feature flag is not enabled",r):(r.eligible=!1,r.reason="Survey linked feature flag is not enabled",r):(r.eligible=!1,r.reason="Survey is not running. It was completed on "+e.end_date,r)}_hasActionOrEventTriggeredSurvey(e){var t;if(!ia(e)&&!na(e))return!0;var r=null==(t=this._posthog.surveys._surveyEventReceiver)?void 0:t.getSurveys();return!(null==r||!r.includes(e.id))}_checkFlags(e){var t;return null==(t=e.feature_flag_keys)||!t.length||e.feature_flag_keys.every((e=>{var{key:t,value:r}=e;return!t||!r||this._isSurveyFeatureFlagEnabled(r)}))}_removeSurveyFromDom(e){try{var t=dc.querySelector(Lu(e,!0));null!=t&&t.shadowRoot&&bo(null,t.shadowRoot),null==t||t.remove()}catch(t){ta.warn("Failed to remove survey "+e.id+" from DOM:",t)}}getTestAPI(){return{addSurveyToFocus:this._addSurveyToFocus,removeSurveyFromFocus:this._removeSurveyFromFocus,surveyInFocus:this._surveyInFocus,surveyTimeouts:this._surveyTimeouts,handleWidget:this._handleWidget,handlePopoverSurvey:this._handlePopoverSurvey,manageWidgetSelectorListener:this._manageWidgetSelectorListener,sortSurveysByAppearanceDelay:this._sortSurveysByAppearanceDelay,checkFlags:this._checkFlags.bind(this),isSurveyFeatureFlagEnabled:this._isSurveyFeatureFlagEnabled.bind(this)}}}function yc(e){if(dc&&cc){var t=new mc(e);return e.config.disable_surveys_automatic_display?(ta.info("Surveys automatic display is disabled. Skipping call surveys and evaluate display logic."),t):(t.callSurveysAndEvaluateDisplayLogic(!0),setInterval((()=>{t.callSurveysAndEvaluateDisplayLogic(!1)}),1e3),t)}}function bc(e){var{survey:t,removeSurveyFromFocus:r=(()=>{}),setSurveyVisible:i,isPreviewMode:n=!1}=e;Lo((()=>{var e;if(!n&&null!=(e=t.conditions)&&e.url){var s=()=>{var e,n=t.type===Yo.Widget,s=Ru(t),o=(null==(e=t.appearance)?void 0:e.widgetType)===Go.Tab&&n;if(!s)return ta.info("Hiding survey "+t.id+" because URL does not match"),i(!1),r(t);o&&(ta.info("Showing survey "+t.id+" because it is a feedback button tab and URL matches"),i(!0))};$i(cc,"popstate",s),$i(cc,"hashchange",s);var o=cc.history.pushState,a=cc.history.replaceState;return cc.history.pushState=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];o.apply(this,t),s()},cc.history.replaceState=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];a.apply(this,t),s()},()=>{cc.removeEventListener("popstate",s),cc.removeEventListener("hashchange",s),cc.history.pushState=o,cc.history.replaceState=a}}}),[n,t,r,i])}function Sc(e,t,r){switch(void 0===t&&(t=jo.Right),t){case jo.TopLeft:return{top:"0",left:"0",transform:"translate(30px, 30px)"};case jo.TopRight:return{top:"0",right:"0",transform:"translate(-30px, 30px)"};case jo.TopCenter:return{top:"0",left:"50%",transform:"translate(-50%, 30px)"};case jo.MiddleLeft:return{top:"50%",left:"0",transform:"translate(30px, -50%)"};case jo.MiddleRight:return{top:"50%",right:"0",transform:"translate(-30px, -50%)"};case jo.MiddleCenter:return{top:"50%",left:"50%",transform:"translate(-50%, -50%)"};case jo.Left:return{left:"30px"};case jo.Center:return{left:"50%",transform:"translateX(-50%)"};default:case jo.Right:return{right:e===Yo.Widget&&r===Go.Tab?"60px":"30px"}}}function wc(e){var t,i,n,s,o,a,{survey:l,forceDisableHtml:u,posthog:c,style:d={},previewPageIndex:h,removeSurveyFromFocus:p=(()=>{}),isPopup:_=!0,onPreviewSubmit:g=(()=>{}),onPopupSurveyDismissed:f=(()=>{}),onCloseConfirmationMessage:v=(()=>{})}=e,m=Do(null),y=Number.isInteger(h),b=null!=(t=l.appearance)&&t.surveyPopupDelaySeconds?1e3*l.appearance.surveyPopupDelaySeconds:0,{isPopupVisible:S,isSurveySent:w,hidePopupWithViewTransition:C}=function(e,t,r,i,n,s){var[o,a]=Oo(i||0===r),[l,u]=Oo(!1),c=()=>{var t=()=>{e.type===Yo.Popover&&n(e),a(!1)};dc.startViewTransition?dc.startViewTransition((()=>{var e;null==s||null==(e=s.current)||e.remove()})).finished.then((()=>{setTimeout((()=>{t()}),100)})):t()},d=t=>{t.detail.surveyId===e.id&&c()};return Lo((()=>{if(t){if(!i){var n=t=>{var r,i;if(t.detail.surveyId===e.id){if(null==(r=e.appearance)||!r.displayThankYouMessage)return c();u(!0),null!=(i=e.appearance)&&i.autoDisappear&&setTimeout((()=>{c()}),5e3)}},s=()=>{Ru(e)&&(a(!0),cc.dispatchEvent(new Event("PHSurveyShown")),t.capture(Qo.SHOWN,{[ea.SURVEY_NAME]:e.name,[ea.SURVEY_ID]:e.id,[ea.SURVEY_ITERATION]:e.current_iteration,[ea.SURVEY_ITERATION_START_DATE]:e.current_iteration_start_date,sessionRecordingUrl:null==t.get_session_replay_url?void 0:t.get_session_replay_url()}),localStorage.setItem("lastSeenSurveyDate",(new Date).toISOString()))};if($i(cc,"PHSurveyClosed",d),$i(cc,"PHSurveySent",n),r>0){var o=setTimeout(s,r);return()=>{clearTimeout(o),cc.removeEventListener("PHSurveyClosed",d),cc.removeEventListener("PHSurveySent",n)}}return s(),()=>{cc.removeEventListener("PHSurveyClosed",d),cc.removeEventListener("PHSurveySent",n)}}}else ta.error("usePopupVisibility hook called without a PostHog instance.")}),[]),bc({survey:e,removeSurveyFromFocus:n,setSurveyVisible:a,isPreviewMode:i}),{isPopupVisible:o,isSurveySent:l,setIsPopupVisible:a,hidePopupWithViewTransition:c}}(l,c,b,y,p,m),I=w||h===l.questions.length,k=Bo((()=>{var e=Nu(l);return{isPreviewMode:y,previewPageIndex:h,onPopupSurveyDismissed:()=>{bu(l,c,y),f()},isPopup:_||!1,surveySubmissionId:(null==e?void 0:e.surveySubmissionId)||_a(),onPreviewSubmit:g,posthog:c}}),[y,h,_,c,l,f,g]);return S?Bu(Eu.Provider,{value:k,children:Bu("div",{className:"ph-survey",style:r({},Sc(l.type,null==(i=l.appearance)?void 0:i.position,null==(n=l.appearance)?void 0:n.widgetType),d),ref:m,children:I?Bu(Ju,{header:(null==(s=l.appearance)?void 0:s.thankYouMessageHeader)||"Thank you!",description:(null==(o=l.appearance)?void 0:o.thankYouMessageDescription)||"",forceDisableHtml:!!u,contentType:null==(a=l.appearance)?void 0:a.thankYouMessageDescriptionContentType,appearance:l.appearance||pu,onClose:()=>{C(),v()}}):Bu(Cc,{survey:l,forceDisableHtml:!!u,posthog:c})})}):null}function Cc(e){var{survey:t,forceDisableHtml:i,posthog:n}=e,[s,o]=Oo((()=>{var e=Nu(t);return null!=e&&e.responses&&ta.info("Survey is already in progress, filling in initial responses"),(null==e?void 0:e.responses)||{}})),{previewPageIndex:a,onPopupSurveyDismissed:l,isPopup:u,onPreviewSubmit:c,surveySubmissionId:d,isPreviewMode:h}=qo(Eu),[p,_]=Oo((()=>{var e=Nu(t);return a||(null==e?void 0:e.lastQuestionIndex)||0})),g=Bo((()=>Cu(t)),[t]);Lo((()=>{h&&!_i(a)&&_(a)}),[a,h]);var f=g.at(p);return f?Bu("form",{className:"survey-form",name:"surveyForm",children:[u&&Bu(Yu,{onClick:()=>{l()}}),Bu("div",{className:"survey-box",children:kc({question:f,forceDisableHtml:i,displayQuestionIndex:p,appearance:t.appearance||pu,onSubmit:e=>(e=>{var{res:i,displayQuestionIndex:a,questionId:l}=e;if(n)if(l){var u=du(l),c=r({},s,{[u]:i});o(c);var h=_c(t,a,i),p=h===Jo.End;p||(_(h),Fu(t,{surveySubmissionId:d,responses:c,lastQuestionIndex:h})),(t.enable_partial_responses||p)&&yu({responses:c,survey:t,surveySubmissionId:d,isSurveyCompleted:p,posthog:n})}else ta.error("onNextButtonClick called without a questionId.");else ta.error("onNextButtonClick called without a PostHog instance.")})({res:e,displayQuestionIndex:p,questionId:f.id}),onPreviewSubmit:c,initialValue:f.id?s[du(f.id)]:void 0})})]}):null}function Ic(e){var t,r,i,n,s,{survey:o,forceDisableHtml:a,posthog:l,readOnly:u}=e,[c,d]=Oo(!0),[h,p]=Oo(!1),[_,g]=Oo({}),f=()=>{p(!h)};if(Lo((()=>{var e;if(l){if(!u){"tab"===(null==(e=o.appearance)?void 0:e.widgetType)&&g({top:"50%",bottom:"auto"});var t=e=>{var t,r=e;(null==(t=r.detail)?void 0:t.surveyId)===o.id&&(ta.info("Received show event for feedback button survey "+o.id),g(r.detail.position||{}),f())};return $i(cc,hc,t),()=>{cc.removeEventListener(hc,t)}}}else ta.error("FeedbackWidget called without a PostHog instance.")}),[l,u,o.id,null==(t=o.appearance)?void 0:t.widgetType,null==(r=o.appearance)?void 0:r.widgetSelector,null==(i=o.appearance)?void 0:i.borderColor]),bc({survey:o,setSurveyVisible:d}),!c)return null;var v=()=>{o.schedule!==Ko.Always&&d(!1),setTimeout((()=>{p(!1)}),200)};return Bu(to,{children:["tab"===(null==(n=o.appearance)?void 0:n.widgetType)&&Bu("button",{className:"ph-survey-widget-tab",onClick:f,disabled:u,children:(null==(s=o.appearance)?void 0:s.widgetLabel)||""}),h&&Bu(wc,{posthog:l,survey:o,forceDisableHtml:a,style:_,onPopupSurveyDismissed:v,onCloseConfirmationMessage:v})]})}var kc=e=>{var{question:t,forceDisableHtml:i,displayQuestionIndex:n,appearance:s,onSubmit:o,onPreviewSubmit:a,initialValue:l}=e,u={forceDisableHtml:i,appearance:s,onPreviewSubmit:e=>{a(e)},onSubmit:e=>{o(e)},initialValue:l,displayQuestionIndex:n};switch(t.type){case Xo.Open:return Qs(Qu,r({},u,{question:t,key:t.id}));case Xo.Link:return Qs(ec,r({},u,{question:t,key:t.id}));case Xo.Rating:return Qs(tc,r({},u,{question:t,key:t.id}));case Xo.SingleChoice:case Xo.MultipleChoice:return Qs(ic,r({},u,{question:t,key:t.id}));default:return ta.error("Unsupported question type: "+t.type),null}};function Ec(e){return!_i(Event)&&xc(e,Event)}function xc(e,t){try{return e instanceof t}catch(e){return!1}}function Tc(e){return vi(e)||!hi(e)&&!ui(e)}function Mc(e){switch(Object.prototype.toString.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object DOMError]":return!0;default:return xc(e,Error)}}function Rc(e,t){return Object.prototype.toString.call(e)==="[object "+t+"]"}function Ac(e){return Rc(e,"DOMError")}Xr.__PosthogExtensions__=Xr.__PosthogExtensions__||{},Xr.__PosthogExtensions__.generateSurveys=yc,Xr.extendPostHogWithSurveys=yc;var Fc=/\(error: (.*)\)/,Nc=50,Pc="?";function Oc(e,t,r,i){var n={platform:"web:javascript",filename:e,function:"<anonymous>"===t?Pc:t,in_app:!0};return _i(r)||(n.lineno=r),_i(i)||(n.colno=i),n}var Lc=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,Dc=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,Bc=/\((\S*)(?::(\d+))(?::(\d+))\)/,qc=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,Hc=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,$c=function(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];var n=t.sort(((e,t)=>e[0]-t[0])).map((e=>e[1]));return function(e,t){void 0===t&&(t=0);for(var i=[],s=e.split("\n"),o=t;o<s.length;o++){var a=s[o];if(!(a.length>1024)){var l=Fc.test(a)?a.replace(Fc,"$1"):a;if(!l.match(/\S*Error: /)){for(var u of n){var c=u(l);if(c){i.push(c);break}}if(i.length>=Nc)break}}}return function(e){if(!e.length)return[];var t=Array.from(e);return t.reverse(),t.slice(0,Nc).map((e=>r({},e,{filename:e.filename||Vc(t).filename,function:e.function||Pc})))}(i)}}(...[[30,e=>{var t=Lc.exec(e);if(t){var[,r,i,n]=t;return Oc(r,Pc,+i,+n)}var s=Dc.exec(e);if(s){if(s[2]&&0===s[2].indexOf("eval")){var o=Bc.exec(s[2]);o&&(s[2]=o[1],s[3]=o[2],s[4]=o[3])}var[a,l]=zc(s[1]||Pc,s[2]);return Oc(l,a,s[3]?+s[3]:void 0,s[4]?+s[4]:void 0)}}],[50,e=>{var t=qc.exec(e);if(t){if(t[3]&&t[3].indexOf(" > eval")>-1){var r=Hc.exec(t[3]);r&&(t[1]=t[1]||"eval",t[3]=r[1],t[4]=r[2],t[5]="")}var i=t[3],n=t[1]||Pc;return[n,i]=zc(n,i),Oc(i,n,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}}]]);function Vc(e){return e[e.length-1]||{}}var Wc,Uc,Zc,zc=(e,t)=>{var r=-1!==e.indexOf("safari-extension"),i=-1!==e.indexOf("safari-web-extension");return r||i?[-1!==e.indexOf("@")?e.split("@")[0]:Pc,r?"safari-extension:"+t:"safari-web-extension:"+t]:[e,t]};var Gc=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i;function jc(e,t){void 0===t&&(t=0);var r=e.stacktrace||e.stack||"",i=function(e){if(e&&Yc.test(e.message))return 1;return 0}(e);try{var n=$c,s=function(e,t){var r=function(e){var t=globalThis._posthogChunkIds;if(!t)return{};var r=Object.keys(t);return Zc&&r.length===Uc||(Uc=r.length,Zc=r.reduce(((r,i)=>{Wc||(Wc={});var n=Wc[i];if(n)r[n[0]]=n[1];else for(var s=e(i),o=s.length-1;o>=0;o--){var a=s[o],l=null==a?void 0:a.filename,u=t[i];if(l&&u){r[l]=u,Wc[i]=[l,u];break}}return r}),{})),Zc}(t);return e.forEach((e=>{e.filename&&(e.chunk_id=r[e.filename])})),e}(n(r,i),n);return s.slice(0,s.length-t)}catch(e){}return[]}var Yc=/Minified React error #\d+;/i;function Xc(e,t){var r,i,n=jc(e),s=null===(r=null==t?void 0:t.handled)||void 0===r||r,o=null!==(i=null==t?void 0:t.synthetic)&&void 0!==i&&i;return{type:null!=t&&t.overrideExceptionType?t.overrideExceptionType:e.name,value:function(e){var t=e.message;if(t.error&&"string"==typeof t.error.message)return String(t.error.message);return String(t)}(e),stacktrace:{frames:n,type:"raw"},mechanism:{handled:s,synthetic:o}}}function Jc(e,t){var r=Xc(e,t);return e.cause&&Mc(e.cause)&&e.cause!==e?[r,...Jc(e.cause,{handled:null==t?void 0:t.handled,synthetic:null==t?void 0:t.synthetic})]:[r]}function Kc(e,t){return{$exception_list:Jc(e,t),$exception_level:"error"}}function Qc(e,t){var r,i,n,s=null===(r=null==t?void 0:t.handled)||void 0===r||r,o=null===(i=null==t?void 0:t.synthetic)||void 0===i||i,a={type:null!=t&&t.overrideExceptionType?t.overrideExceptionType:null!==(n=null==t?void 0:t.defaultExceptionType)&&void 0!==n?n:"Error",value:e||(null==t?void 0:t.defaultExceptionMessage),mechanism:{handled:s,synthetic:o}};if(null!=t&&t.syntheticException){var l=jc(t.syntheticException,1);l.length&&(a.stacktrace={frames:l,type:"raw"})}return{$exception_list:[a],$exception_level:"error"}}function ed(e){return gi(e)&&!fi(e)&&ei.indexOf(e)>=0}function td(e,t){var r,i,n=null===(r=null==t?void 0:t.handled)||void 0===r||r,s=null===(i=null==t?void 0:t.synthetic)||void 0===i||i,o=null!=t&&t.overrideExceptionType?t.overrideExceptionType:Ec(e)?e.constructor.name:"Error",a="Non-Error 'exception' captured with keys: "+function(e,t){void 0===t&&(t=40);var r=Object.keys(e);if(r.sort(),!r.length)return"[object has no keys]";for(var i=r.length;i>0;i--){var n=r.slice(0,i).join(", ");if(!(n.length>t))return i===r.length||n.length<=t?n:n.slice(0,t)+"..."}return""}(e),l={type:o,value:a,mechanism:{handled:n,synthetic:s}};if(null!=t&&t.syntheticException){var u=jc(null==t?void 0:t.syntheticException,1);u.length&&(l.stacktrace={frames:u,type:"raw"})}return{$exception_list:[l],$exception_level:ed(e.level)?e.level:"error"}}function rd(e,t){var{error:i,event:n}=e,s={$exception_list:[]},o=i||n;if(Ac(o)||function(e){return Rc(e,"DOMException")}(o)){var a=o;if(function(e){return"stack"in e}(o))s=Kc(o,t);else{var l=a.name||(Ac(a)?"DOMError":"DOMException"),u=a.message?l+": "+a.message:l;s=Qc(u,r({},t,{overrideExceptionType:Ac(a)?"DOMError":"DOMException",defaultExceptionMessage:u}))}return"code"in a&&(s.$exception_DOMException_code=""+a.code),s}if(function(e){return Rc(e,"ErrorEvent")}(o)&&o.error)return Kc(o.error,t);if(Mc(o))return Kc(o,t);if(function(e){return Rc(e,"Object")}(o)||Ec(o))return td(o,t);if(_i(i)&&gi(n)){var c="Error",d=n,h=n.match(Gc);return h&&(c=h[1],d=h[2]),Qc(d,r({},t,{overrideExceptionType:c,defaultExceptionMessage:d}))}return Qc(o,t)}function id(e){var[t]=e,r=function(e){if(Tc(e))return e;try{if("reason"in e)return e.reason;if("detail"in e&&"reason"in e.detail)return e.detail.reason}catch(e){}return e}(t);return Tc(r)?Qc("Non-Error promise rejection captured with value: "+String(r),{handled:!1,synthetic:!1,overrideExceptionType:"UnhandledRejection"}):rd({event:r},{handled:!1,overrideExceptionType:"UnhandledRejection",defaultExceptionMessage:String(r)})}var nd=xi("[ExceptionAutocapture]"),sd={wrapOnError:e=>{var t=Br;t||nd.info("window not available, cannot wrap onerror");var r=t.onerror;return t.onerror=function(){for(var t,i=arguments.length,n=new Array(i),s=0;s<i;s++)n[s]=arguments[s];var o=rd({event:n[0],error:n[4]});return e(o),null!==(t=null==r?void 0:r(...n))&&void 0!==t&&t},t.onerror.__POSTHOG_INSTRUMENTED__=!0,()=>{var e;null==(e=t.onerror)||delete e.__POSTHOG_INSTRUMENTED__,t.onerror=r}},wrapUnhandledRejection:e=>{var t=Br;t||nd.info("window not available, cannot wrap onUnhandledRejection");var r=t.onunhandledrejection;return t.onunhandledrejection=function(){for(var i,n=arguments.length,s=new Array(n),o=0;o<n;o++)s[o]=arguments[o];var a=id(s);return e(a),null!==(i=null==r?void 0:r.apply(t,s))&&void 0!==i&&i},t.onunhandledrejection.__POSTHOG_INSTRUMENTED__=!0,()=>{var e;null==(e=t.onunhandledrejection)||delete e.__POSTHOG_INSTRUMENTED__,t.onunhandledrejection=r}},wrapConsoleError:e=>{var t=console;t||nd.info("console not available, cannot wrap console.error");var r=t.error;return t.error=function(){for(var t=arguments.length,i=new Array(t),n=0;n<t;n++)i[n]=arguments[n];var s=i.join(" "),o=i.find((e=>e instanceof Error)),a=o?rd({event:s,error:o}):rd({event:s},{syntheticException:new Error("PostHog syntheticException")});return e(a),null==r?void 0:r(...i)},t.error.__POSTHOG_INSTRUMENTED__=!0,()=>{var e;null==(e=t.error)||delete e.__POSTHOG_INSTRUMENTED__,t.error=r}}};Xr.__PosthogExtensions__=Xr.__PosthogExtensions__||{},Xr.__PosthogExtensions__.errorWrappingFunctions=sd,Xr.posthogErrorWrappingFunctions=sd;var od=(e,t,r)=>{if(t){var{sessionId:i,windowId:n}=t.checkAndGetSessionAndWindowId(!0);r.headers.set("X-POSTHOG-SESSION-ID",i),r.headers.set("X-POSTHOG-WINDOW-ID",n)}r.headers.set("X-POSTHOG-DISTINCT-ID",e)};Xr.__PosthogExtensions__=Xr.__PosthogExtensions__||{};var ad={_patchFetch:(e,r)=>ji(Br,"fetch",(i=>function(){var n=t((function*(t,n){var s=new Request(t,n);return od(e,r,s),i(s)}));return function(e,t){return n.apply(this,arguments)}}())),_patchXHR:(e,t)=>ji(Br.XMLHttpRequest.prototype,"open",(r=>function(i,n,s,o,a){void 0===s&&(s=!0);var l=new Request(n);return od(e,t,l),r.call(this,i,l.url,s,o,a)}))};Xr.__PosthogExtensions__.tracingHeadersPatchFns=ad,Xr.postHogTracingHeadersPatchFns=ad;var ld,ud,cd=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},dd=function(e){if("loading"===document.readyState)return"loading";var t=cd();if(t){if(e<t.domInteractive)return"loading";if(0===t.domContentLoadedEventStart||e<t.domContentLoadedEventStart)return"dom-interactive";if(0===t.domComplete||e<t.domComplete)return"dom-content-loaded"}return"complete"},hd=function(e){var t=e.nodeName;return 1===e.nodeType?t.toLowerCase():t.toUpperCase().replace(/^#/,"")},pd=function(e,t){var r="";try{for(;e&&9!==e.nodeType;){var i=e,n=i.id?"#"+i.id:hd(i)+(i.classList&&i.classList.value&&i.classList.value.trim()&&i.classList.value.trim().length?"."+i.classList.value.trim().replace(/\s+/g,"."):"");if(r.length+n.length>(t||100)-1)return r||n;if(r=r?n+">"+r:n,i.id)break;e=i.parentNode}}catch(e){}return r},_d=-1,gd=function(){return _d},fd=function(e){addEventListener("pageshow",(function(t){t.persisted&&(_d=t.timeStamp,e(t))}),!0)},vd=function(){var e=cd();return e&&e.activationStart||0},md=function(e,t){var r=cd(),i="navigate";return gd()>=0?i="back-forward-cache":r&&(document.prerendering||vd()>0?i="prerender":document.wasDiscarded?i="restore":r.type&&(i=r.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:i}},yd=function(e,t,r){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var i=new PerformanceObserver((function(e){Promise.resolve().then((function(){t(e.getEntries())}))}));return i.observe(Object.assign({type:e,buffered:!0},r||{})),i}}catch(e){}},bd=function(e,t,r,i){var n,s;return function(o){t.value>=0&&(o||i)&&((s=t.value-(n||0))||void 0===n)&&(n=t.value,t.delta=s,t.rating=function(e,t){return e>t[1]?"poor":e>t[0]?"needs-improvement":"good"}(t.value,r),e(t))}},Sd=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},wd=function(e){document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&e()}))},Cd=function(e){var t=!1;return function(){t||(e(),t=!0)}},Id=-1,kd=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},Ed=function(e){"hidden"===document.visibilityState&&Id>-1&&(Id="visibilitychange"===e.type?e.timeStamp:0,Td())},xd=function(){addEventListener("visibilitychange",Ed,!0),addEventListener("prerenderingchange",Ed,!0)},Td=function(){removeEventListener("visibilitychange",Ed,!0),removeEventListener("prerenderingchange",Ed,!0)},Md=function(){return Id<0&&(Id=kd(),xd(),fd((function(){setTimeout((function(){Id=kd(),xd()}),0)}))),{get firstHiddenTime(){return Id}}},Rd=function(e){document.prerendering?addEventListener("prerenderingchange",(function(){return e()}),!0):e()},Ad=[1800,3e3],Fd=function(e,t){t=t||{},Rd((function(){var r,i=Md(),n=md("FCP"),s=yd("paint",(function(e){e.forEach((function(e){"first-contentful-paint"===e.name&&(s.disconnect(),e.startTime<i.firstHiddenTime&&(n.value=Math.max(e.startTime-vd(),0),n.entries.push(e),r(!0)))}))}));s&&(r=bd(e,n,Ad,t.reportAllChanges),fd((function(i){n=md("FCP"),r=bd(e,n,Ad,t.reportAllChanges),Sd((function(){n.value=performance.now()-i.timeStamp,r(!0)}))})))}))},Nd=[.1,.25],Pd=0,Od=1/0,Ld=0,Dd=function(e){e.forEach((function(e){e.interactionId&&(Od=Math.min(Od,e.interactionId),Ld=Math.max(Ld,e.interactionId),Pd=Ld?(Ld-Od)/7+1:0)}))},Bd=function(){return ld?Pd:performance.interactionCount||0},qd=function(){"interactionCount"in performance||ld||(ld=yd("event",Dd,{type:"event",buffered:!0,durationThreshold:0}))},Hd=[],$d=new Map,Vd=0,Wd=[],Ud=function(e){if(Wd.forEach((function(t){return t(e)})),e.interactionId||"first-input"===e.entryType){var t=Hd[Hd.length-1],r=$d.get(e.interactionId);if(r||Hd.length<10||e.duration>t.latency){if(r)e.duration>r.latency?(r.entries=[e],r.latency=e.duration):e.duration===r.latency&&e.startTime===r.entries[0].startTime&&r.entries.push(e);else{var i={id:e.interactionId,latency:e.duration,entries:[e]};$d.set(i.id,i),Hd.push(i)}Hd.sort((function(e,t){return t.latency-e.latency})),Hd.length>10&&Hd.splice(10).forEach((function(e){return $d.delete(e.id)}))}}},Zd=function(e){var t=self.requestIdleCallback||self.setTimeout,r=-1;return e=Cd(e),"hidden"===document.visibilityState?e():(r=t(e),wd(e)),r},zd=[200,500],Gd=function(e,t){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(t=t||{},Rd((function(){var r;qd();var i,n=md("INP"),s=function(e){Zd((function(){e.forEach(Ud);var t=function(){var e=Math.min(Hd.length-1,Math.floor((Bd()-Vd)/50));return Hd[e]}();t&&t.latency!==n.value&&(n.value=t.latency,n.entries=t.entries,i())}))},o=yd("event",s,{durationThreshold:null!==(r=t.durationThreshold)&&void 0!==r?r:40});i=bd(e,n,zd,t.reportAllChanges),o&&(o.observe({type:"first-input",buffered:!0}),wd((function(){s(o.takeRecords()),i(!0)})),fd((function(){Vd=Bd(),Hd.length=0,$d.clear(),n=md("INP"),i=bd(e,n,zd,t.reportAllChanges)})))})))},jd=[],Yd=[],Xd=0,Jd=new WeakMap,Kd=new Map,Qd=-1,eh=function(e){jd=jd.concat(e),th()},th=function(){Qd<0&&(Qd=Zd(rh))},rh=function(){Kd.size>10&&Kd.forEach((function(e,t){$d.has(t)||Kd.delete(t)}));var e=Hd.map((function(e){return Jd.get(e.entries[0])})),t=Yd.length-50;Yd=Yd.filter((function(r,i){return i>=t||e.includes(r)}));for(var r=new Set,i=0;i<Yd.length;i++){var n=Yd[i];ih(n.startTime,n.processingEnd).forEach((function(e){r.add(e)}))}var s=jd.length-1-50;jd=jd.filter((function(e,t){return e.startTime>Xd&&t>s||r.has(e)})),Qd=-1};Wd.push((function(e){e.interactionId&&e.target&&!Kd.has(e.interactionId)&&Kd.set(e.interactionId,e.target)}),(function(e){var t,r=e.startTime+e.duration;Xd=Math.max(Xd,e.processingEnd);for(var i=Yd.length-1;i>=0;i--){var n=Yd[i];if(Math.abs(r-n.renderTime)<=8){(t=n).startTime=Math.min(e.startTime,t.startTime),t.processingStart=Math.min(e.processingStart,t.processingStart),t.processingEnd=Math.max(e.processingEnd,t.processingEnd),t.entries.push(e);break}}t||(t={startTime:e.startTime,processingStart:e.processingStart,processingEnd:e.processingEnd,renderTime:r,entries:[e]},Yd.push(t)),(e.interactionId||"first-input"===e.entryType)&&Jd.set(e,t),th()}));var ih=function(e,t){for(var r,i=[],n=0;r=jd[n];n++)if(!(r.startTime+r.duration<e)){if(r.startTime>t)break;i.push(r)}return i},nh=[2500,4e3],sh={},oh={onLCP:function(e,t){!function(e,t){t=t||{},Rd((function(){var r,i=Md(),n=md("LCP"),s=function(e){t.reportAllChanges||(e=e.slice(-1)),e.forEach((function(e){e.startTime<i.firstHiddenTime&&(n.value=Math.max(e.startTime-vd(),0),n.entries=[e],r())}))},o=yd("largest-contentful-paint",s);if(o){r=bd(e,n,nh,t.reportAllChanges);var a=Cd((function(){sh[n.id]||(s(o.takeRecords()),o.disconnect(),sh[n.id]=!0,r(!0))}));["keydown","click"].forEach((function(e){addEventListener(e,(function(){return Zd(a)}),{once:!0,capture:!0})})),wd(a),fd((function(i){n=md("LCP"),r=bd(e,n,nh,t.reportAllChanges),Sd((function(){n.value=performance.now()-i.timeStamp,sh[n.id]=!0,r(!0)}))}))}}))}((function(t){var r=function(e){var t={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:e.value};if(e.entries.length){var r=cd();if(r){var i=r.activationStart||0,n=e.entries[e.entries.length-1],s=n.url&&performance.getEntriesByType("resource").filter((function(e){return e.name===n.url}))[0],o=Math.max(0,r.responseStart-i),a=Math.max(o,s?(s.requestStart||s.startTime)-i:0),l=Math.max(a,s?s.responseEnd-i:0),u=Math.max(l,n.startTime-i);t={element:pd(n.element),timeToFirstByte:o,resourceLoadDelay:a-o,resourceLoadDuration:l-a,elementRenderDelay:u-l,navigationEntry:r,lcpEntry:n},n.url&&(t.url=n.url),s&&(t.lcpResourceEntry=s)}}return Object.assign(e,{attribution:t})}(t);e(r)}),t)},onCLS:function(e,t){!function(e,t){t=t||{},Fd(Cd((function(){var r,i=md("CLS",0),n=0,s=[],o=function(e){e.forEach((function(e){if(!e.hadRecentInput){var t=s[0],r=s[s.length-1];n&&e.startTime-r.startTime<1e3&&e.startTime-t.startTime<5e3?(n+=e.value,s.push(e)):(n=e.value,s=[e])}})),n>i.value&&(i.value=n,i.entries=s,r())},a=yd("layout-shift",o);a&&(r=bd(e,i,Nd,t.reportAllChanges),wd((function(){o(a.takeRecords()),r(!0)})),fd((function(){n=0,i=md("CLS",0),r=bd(e,i,Nd,t.reportAllChanges),Sd((function(){return r()}))})),setTimeout(r,0))})))}((function(t){var r=function(e){var t,r={};if(e.entries.length){var i=e.entries.reduce((function(e,t){return e&&e.value>t.value?e:t}));if(i&&i.sources&&i.sources.length){var n=(t=i.sources).find((function(e){return e.node&&1===e.node.nodeType}))||t[0];n&&(r={largestShiftTarget:pd(n.node),largestShiftTime:i.startTime,largestShiftValue:i.value,largestShiftSource:n,largestShiftEntry:i,loadState:dd(i.startTime)})}}return Object.assign(e,{attribution:r})}(t);e(r)}),t)},onFCP:function(e,t){Fd((function(t){var r=function(e){var t={timeToFirstByte:0,firstByteToFCP:e.value,loadState:dd(gd())};if(e.entries.length){var r=cd(),i=e.entries[e.entries.length-1];if(r){var n=r.activationStart||0,s=Math.max(0,r.responseStart-n);t={timeToFirstByte:s,firstByteToFCP:e.value-s,loadState:dd(e.entries[0].startTime),navigationEntry:r,fcpEntry:i}}}return Object.assign(e,{attribution:t})}(t);e(r)}),t)},onINP:function(e,t){ud||(ud=yd("long-animation-frame",eh)),Gd((function(t){var r=function(e){var t=e.entries[0],r=Jd.get(t),i=t.processingStart,n=r.processingEnd,s=r.entries.sort((function(e,t){return e.processingStart-t.processingStart})),o=ih(t.startTime,n),a=e.entries.find((function(e){return e.target})),l=a&&a.target||Kd.get(t.interactionId),u=[t.startTime+t.duration,n].concat(o.map((function(e){return e.startTime+e.duration}))),c=Math.max.apply(Math,u),d={interactionTarget:pd(l),interactionTargetElement:l,interactionType:t.name.startsWith("key")?"keyboard":"pointer",interactionTime:t.startTime,nextPaintTime:c,processedEventEntries:s,longAnimationFrameEntries:o,inputDelay:i-t.startTime,processingDuration:n-i,presentationDelay:Math.max(c-n,0),loadState:dd(t.startTime)};return Object.assign(e,{attribution:d})}(t);e(r)}),t)}};Xr.__PosthogExtensions__=Xr.__PosthogExtensions__||{},Xr.__PosthogExtensions__.postHogWebVitalsCallbacks=oh,Xr.postHogWebVitalsCallbacks=oh;class ah{constructor(){this.clicks=[]}isRageClick(e,t,r){var i=this.clicks[this.clicks.length-1];if(i&&Math.abs(e-i.x)+Math.abs(t-i.y)<30&&r-i.timestamp<1e3){if(this.clicks.push({x:e,y:t,timestamp:r}),3===this.clicks.length)return!0}else this.clicks=[{x:e,y:t,timestamp:r}];return!1}}var lh=xi("[AutoCapture]");function uh(e,t){return t.length>e?t.slice(0,e)+"...":t}function ch(e){if(e.previousElementSibling)return e.previousElementSibling;var t=e;do{t=t.previousSibling}while(t&&!Bn(t));return t}function dh(e,t,r,i){var n=e.tagName.toLowerCase(),s={tag_name:n};jn.indexOf(n)>-1&&!r&&("a"===n.toLowerCase()||"button"===n.toLowerCase()?s.$el_text=uh(1024,os(e)):s.$el_text=uh(1024,zn(e)));var o=Un(e);o.length>0&&(s.classes=o.filter((function(e){return""!==e}))),Ri(e.attributes,(function(r){var n;if((!Kn(e)||-1!==["name","id","class","aria-label"].indexOf(r.name))&&((null==i||!i.includes(r.name))&&!t&&ss(r.value)&&(n=r.name,!gi(n)||"_ngcontent"!==n.substring(0,10)&&"_nghost"!==n.substring(0,7)))){var o=r.value;"class"===r.name&&(o=Vn(o).join(" ")),s["attr__"+r.name]=uh(1024,o)}}));for(var a=1,l=1,u=e;u=ch(u);)a++,u.tagName===e.tagName&&l++;return s.nth_child=a,s.nth_of_type=l,s}function hh(e,t){for(var r,i,{e:n,maskAllElementAttributes:s,maskAllText:o,elementAttributeIgnoreList:a,elementsChainAsString:l}=t,u=[e],c=e;c.parentNode&&!qn(c,"body");)$n(c.parentNode)?(u.push(c.parentNode.host),c=c.parentNode.host):(u.push(c.parentNode),c=c.parentNode);var d,h=[],p={},_=!1,g=!1;if(Ri(u,(e=>{var t=Jn(e);"a"===e.tagName.toLowerCase()&&(_=e.getAttribute("href"),_=t&&_&&ss(_)&&_),ti(Un(e),"ph-no-capture")&&(g=!0),h.push(dh(e,s,o,a));var r=function(e){if(!Jn(e))return{};var t={};return Ri(e.attributes,(function(e){if(e.name&&0===e.name.indexOf("data-ph-capture-attribute")){var r=e.name.replace("data-ph-capture-attribute-",""),i=e.value;r&&i&&ss(i)&&(t[r]=i)}})),t}(e);Ai(p,r)})),g)return{props:{},explicitNoCapture:g};if(o||("a"===e.tagName.toLowerCase()||"button"===e.tagName.toLowerCase()?h[0].$el_text=os(e):h[0].$el_text=zn(e)),_){var f,v;h[0].attr__href=_;var m=null==(f=Wi(_))?void 0:f.host,y=null==Br||null==(v=Br.location)?void 0:v.host;m&&y&&m!==y&&(d=_)}return{props:Ai({$event_type:n.type,$ce_version:1},l?{}:{$elements:h},{$elements_chain:ls(h)},null!=(r=h[0])&&r.$el_text?{$el_text:null==(i=h[0])?void 0:i.$el_text}:{},d&&"click"===n.type?{$external_click_url:d}:{},p)}}class ph{constructor(e){this._initialized=!1,this._isDisabledServerSide=null,this.rageclicks=new ah,this._elementsChainAsString=!1,this.instance=e,this._elementSelectors=null}get _config(){var e,t,r=hi(this.instance.config.autocapture)?this.instance.config.autocapture:{};return r.url_allowlist=null==(e=r.url_allowlist)?void 0:e.map((e=>new RegExp(e))),r.url_ignorelist=null==(t=r.url_ignorelist)?void 0:t.map((e=>new RegExp(e))),r}_addDomEventHandlers(){if(this.isBrowserSupported()){if(Br&&Ur){var e=e=>{e=e||(null==Br?void 0:Br.event);try{this._captureEvent(e)}catch(e){lh.error("Failed to capture event",e)}};if($i(Ur,"submit",e,{capture:!0}),$i(Ur,"change",e,{capture:!0}),$i(Ur,"click",e,{capture:!0}),this._config.capture_copied_text){var t=e=>{e=e||(null==Br?void 0:Br.event),this._captureEvent(e,Jr)};$i(Ur,"copy",t,{capture:!0}),$i(Ur,"cut",t,{capture:!0})}}}else lh.info("Disabling Automatic Event Collection because this browser is not supported")}startIfEnabled(){this.isEnabled&&!this._initialized&&(this._addDomEventHandlers(),this._initialized=!0)}onRemoteConfig(e){e.elementsChainAsString&&(this._elementsChainAsString=e.elementsChainAsString),this.instance.persistence&&this.instance.persistence.register({[Qi]:!!e.autocapture_opt_out}),this._isDisabledServerSide=!!e.autocapture_opt_out,this.startIfEnabled()}setElementSelectors(e){this._elementSelectors=e}getElementSelectors(e){var t,r=[];return null==(t=this._elementSelectors)||t.forEach((t=>{var i=null==Ur?void 0:Ur.querySelectorAll(t);null==i||i.forEach((i=>{e===i&&r.push(t)}))})),r}get isEnabled(){var e,t,r=null==(e=this.instance.persistence)?void 0:e.props[Qi],i=this._isDisabledServerSide;if(vi(i)&&!bi(r)&&!this.instance._shouldDisableFlags())return!1;var n=null!==(t=this._isDisabledServerSide)&&void 0!==t?t:!!r;return!!this.instance.config.autocapture&&!n}_captureEvent(e,t){if(void 0===t&&(t="$autocapture"),this.isEnabled){var r,i=Gn(e);if(Hn(i)&&(i=i.parentNode||null),"$autocapture"===t&&"click"===e.type&&e instanceof MouseEvent)this.instance.config.rageclick&&null!=(r=this.rageclicks)&&r.isRageClick(e.clientX,e.clientY,(new Date).getTime())&&this._captureEvent(e,"$rageclick");var n=t===Jr;if(i&&Xn(i,e,this._config,n,n?["copy","cut"]:void 0)){var{props:s,explicitNoCapture:o}=hh(i,{e:e,maskAllElementAttributes:this.instance.config.mask_all_element_attributes,maskAllText:this.instance.config.mask_all_text,elementAttributeIgnoreList:this._config.element_attribute_ignorelist,elementsChainAsString:this._elementsChainAsString});if(o)return!1;var a=this.getElementSelectors(i);if(a&&a.length>0&&(s.$element_selectors=a),t===Jr){var l,u=Zn(null==Br||null==(l=Br.getSelection())?void 0:l.toString()),c=e.type||"clipboard";if(!u)return!1;s.$selected_content=u,s.$copy_type=c}return this.instance.capture(t,s),!0}}}isBrowserSupported(){return ui(null==Ur?void 0:Ur.querySelectorAll)}}var _h={};function gh(e){return function(e,t){var r=_h[e];if(r)return r;var i=t[e];if(ci(i)&&!di())return _h[e]=i.bind(t);var n=t.document;if(n&&ui(n.createElement))try{var s=n.createElement("iframe");s.hidden=!0,n.head.appendChild(s);var o=s.contentWindow;o&&o[e]&&(i=o[e]),n.head.removeChild(s)}catch(t){Ei.warn("Could not create sandbox iframe for "+e+" check, bailing to assignableWindow."+e+": ",t)}return i&&ui(i)?_h[e]=i.bind(t):i}("MutationObserver",e)}function fh(e,t){return yi(e)&&e>=t}class vh{_asRequiredConfig(e){var t,r,i,n,s=this._defaultConfig((null==e?void 0:e.__onCapture)||this._captureDeadClick.bind(this));return{element_attribute_ignorelist:null!==(t=null==e?void 0:e.element_attribute_ignorelist)&&void 0!==t?t:s.element_attribute_ignorelist,scroll_threshold_ms:null!==(r=null==e?void 0:e.scroll_threshold_ms)&&void 0!==r?r:s.scroll_threshold_ms,selection_change_threshold_ms:null!==(i=null==e?void 0:e.selection_change_threshold_ms)&&void 0!==i?i:s.selection_change_threshold_ms,mutation_threshold_ms:null!==(n=null==e?void 0:e.mutation_threshold_ms)&&void 0!==n?n:s.mutation_threshold_ms,__onCapture:s.__onCapture}}constructor(e,t){this._clicks=[],this._defaultConfig=e=>({element_attribute_ignorelist:[],scroll_threshold_ms:100,selection_change_threshold_ms:100,mutation_threshold_ms:2500,__onCapture:e}),this._onClick=e=>{var t=function(e){var t=Gn(e);return t?{node:t,originalEvent:e,timestamp:Date.now()}:null}(e);vi(t)||this._ignoreClick(t)||this._clicks.push(t),this._clicks.length&&_i(this._checkClickTimer)&&(this._checkClickTimer=Xr.setTimeout((()=>{this._checkClicks()}),1e3))},this._onScroll=()=>{var e=Date.now();e%50==0&&this._clicks.forEach((t=>{_i(t.scrollDelayMs)&&(t.scrollDelayMs=e-t.timestamp)}))},this._onSelectionChange=()=>{this._lastSelectionChanged=Date.now()},this.instance=e,this._config=this._asRequiredConfig(t),this._onCapture=this._config.__onCapture}start(e){this._startClickObserver(),this._startScrollObserver(),this._startSelectionChangedObserver(),this._startMutationObserver(e)}_startMutationObserver(e){if(!this._mutationObserver){var t=gh(Xr);this._mutationObserver=new t((e=>{this._onMutation(e)})),this._mutationObserver.observe(e,{attributes:!0,characterData:!0,childList:!0,subtree:!0})}}stop(){var e;null==(e=this._mutationObserver)||e.disconnect(),this._mutationObserver=void 0,Xr.removeEventListener("click",this._onClick),Xr.removeEventListener("scroll",this._onScroll,{capture:!0}),Xr.removeEventListener("selectionchange",this._onSelectionChange)}_onMutation(e){this._lastMutation=Date.now()}_startClickObserver(){$i(Xr,"click",this._onClick)}_startScrollObserver(){$i(Xr,"scroll",this._onScroll,{capture:!0})}_startSelectionChangedObserver(){$i(Xr,"selectionchange",this._onSelectionChange)}_ignoreClick(e){if(!e)return!0;if(Dn(e.node))return!0;var t=this._clicks.some((t=>t.node===e.node&&Math.abs(t.timestamp-e.timestamp)<1e3));return!!t||!(!qn(e.node,"html")&&Bn(e.node)&&!jn.includes(e.node.tagName.toLowerCase()))}_checkClicks(){if(this._clicks.length){clearTimeout(this._checkClickTimer),this._checkClickTimer=void 0;var e=this._clicks;for(var t of(this._clicks=[],e)){var r;t.mutationDelayMs=null!==(r=t.mutationDelayMs)&&void 0!==r?r:this._lastMutation&&t.timestamp<=this._lastMutation?this._lastMutation-t.timestamp:void 0,t.absoluteDelayMs=Date.now()-t.timestamp,t.selectionChangedDelayMs=this._lastSelectionChanged&&t.timestamp<=this._lastSelectionChanged?this._lastSelectionChanged-t.timestamp:void 0;var i=fh(t.scrollDelayMs,this._config.scroll_threshold_ms),n=fh(t.selectionChangedDelayMs,this._config.selection_change_threshold_ms),s=fh(t.mutationDelayMs,this._config.mutation_threshold_ms),o=fh(t.absoluteDelayMs,1.1*this._config.mutation_threshold_ms),a=yi(t.scrollDelayMs)&&t.scrollDelayMs<this._config.scroll_threshold_ms,l=yi(t.mutationDelayMs)&&t.mutationDelayMs<this._config.mutation_threshold_ms,u=yi(t.selectionChangedDelayMs)&&t.selectionChangedDelayMs<this._config.selection_change_threshold_ms;a||l||u||(i||s||o||n?this._onCapture(t,{$dead_click_last_mutation_timestamp:this._lastMutation,$dead_click_event_timestamp:t.timestamp,$dead_click_scroll_timeout:i,$dead_click_mutation_timeout:s,$dead_click_absolute_timeout:o,$dead_click_selection_changed_timeout:n}):t.absoluteDelayMs<this._config.mutation_threshold_ms&&this._clicks.push(t))}this._clicks.length&&_i(this._checkClickTimer)&&(this._checkClickTimer=Xr.setTimeout((()=>{this._checkClicks()}),1e3))}}_captureDeadClick(e,t){this.instance.capture("$dead_click",r({},t,hh(e.node,{e:e.originalEvent,maskAllElementAttributes:this.instance.config.mask_all_element_attributes,maskAllText:this.instance.config.mask_all_text,elementAttributeIgnoreList:this._config.element_attribute_ignorelist,elementsChainAsString:!1}).props,{$dead_click_scroll_delay_ms:e.scrollDelayMs,$dead_click_mutation_delay_ms:e.mutationDelayMs,$dead_click_absolute_delay_ms:e.absoluteDelayMs,$dead_click_selection_changed_delay_ms:e.selectionChangedDelayMs}),{timestamp:new Date(e.timestamp)})}}Xr.__PosthogExtensions__=Xr.__PosthogExtensions__||{},Xr.__PosthogExtensions__.initDeadClicksAutocapture=(e,t)=>new vh(e,t);var mh="";var yh=/[a-z0-9][a-z0-9-]+\.[a-z]{2,}$/i;function bh(e,t){if(t){var r=function(e,t){if(void 0===t&&(t=Ur),mh)return mh;if(!t)return"";if(["localhost","127.0.0.1"].includes(e))return"";for(var r=e.split("."),i=Math.min(r.length,8),n="dmn_chk_"+_a();!mh&&i--;){var s=r.slice(i).join("."),o=n+"=1;domain=."+s+";path=/";t.cookie=o+";max-age=3",t.cookie.includes(n)&&(t.cookie=o+";max-age=0",mh=s)}return mh}(e);if(!r){var i=(e=>{var t=e.match(yh);return t?t[0]:""})(e);i!==r&&Ei.info("Warning: cookie subdomain discovery mismatch",i,r),r=i}return r?"; domain=."+r:""}return""}var Sh={_is_supported:()=>!!Ur,_error:function(e){Ei.error("cookieStore error: "+e)},_get:function(e){if(Ur){try{for(var t=e+"=",r=Ur.cookie.split(";").filter((e=>e.length)),i=0;i<r.length;i++){for(var n=r[i];" "==n.charAt(0);)n=n.substring(1,n.length);if(0===n.indexOf(t))return decodeURIComponent(n.substring(t.length,n.length))}}catch(e){}return null}},_parse:function(e){var t;try{t=JSON.parse(Sh._get(e))||{}}catch(e){}return t},_set:function(e,t,r,i,n){if(Ur)try{var s="",o="",a=bh(Ur.location.hostname,i);if(r){var l=new Date;l.setTime(l.getTime()+24*r*60*60*1e3),s="; expires="+l.toUTCString()}n&&(o="; secure");var u=e+"="+encodeURIComponent(JSON.stringify(t))+s+"; SameSite=Lax; path=/"+a+o;return u.length>3686.4&&Ei.warn("cookieStore warning: large cookie, len="+u.length),Ur.cookie=u,u}catch(e){return}},_remove:function(e,t){try{Sh._set(e,"",-1,t)}catch(e){return}}},wh=null,Ch={_is_supported:function(){if(!vi(wh))return wh;var e=!0;if(_i(Br))e=!1;else try{var t="__mplssupport__";Ch._set(t,"xyz"),'"xyz"'!==Ch._get(t)&&(e=!1),Ch._remove(t)}catch(t){e=!1}return e||Ei.error("localStorage unsupported; falling back to cookie store"),wh=e,e},_error:function(e){Ei.error("localStorage error: "+e)},_get:function(e){try{return null==Br?void 0:Br.localStorage.getItem(e)}catch(e){Ch._error(e)}return null},_parse:function(e){try{return JSON.parse(Ch._get(e))||{}}catch(e){}return null},_set:function(e,t){try{null==Br||Br.localStorage.setItem(e,JSON.stringify(t))}catch(e){Ch._error(e)}},_remove:function(e){try{null==Br||Br.localStorage.removeItem(e)}catch(e){Ch._error(e)}}},Ih=["distinct_id",gn,fn,Nn,Fn],kh=r({},Ch,{_parse:function(e){try{var t={};try{t=Sh._parse(e)||{}}catch(e){}var r=Ai(t,JSON.parse(Ch._get(e)||"{}"));return Ch._set(e,r),r}catch(e){}return null},_set:function(e,t,r,i,n,s){try{Ch._set(e,t,void 0,void 0,s);var o={};Ih.forEach((e=>{t[e]&&(o[e]=t[e])})),Object.keys(o).length&&Sh._set(e,o,r,i,n,s)}catch(e){Ch._error(e)}},_remove:function(e,t){try{null==Br||Br.localStorage.removeItem(e),Sh._remove(e,t)}catch(e){Ch._error(e)}}}),Eh={},xh={_is_supported:function(){return!0},_error:function(e){Ei.error("memoryStorage error: "+e)},_get:function(e){return Eh[e]||null},_parse:function(e){return Eh[e]||null},_set:function(e,t){Eh[e]=t},_remove:function(e){delete Eh[e]}},Th=null,Mh={_is_supported:function(){if(!vi(Th))return Th;if(Th=!0,_i(Br))Th=!1;else try{var e="__support__";Mh._set(e,"xyz"),'"xyz"'!==Mh._get(e)&&(Th=!1),Mh._remove(e)}catch(e){Th=!1}return Th},_error:function(e){Ei.error("sessionStorage error: ",e)},_get:function(e){try{return null==Br?void 0:Br.sessionStorage.getItem(e)}catch(e){Mh._error(e)}return null},_parse:function(e){try{return JSON.parse(Mh._get(e))||null}catch(e){}return null},_set:function(e,t){try{null==Br||Br.sessionStorage.setItem(e,JSON.stringify(t))}catch(e){Mh._error(e)}},_remove:function(e){try{null==Br||Br.sessionStorage.removeItem(e)}catch(e){Mh._error(e)}}},Rh=function(e){return e[e.PENDING=-1]="PENDING",e[e.DENIED=0]="DENIED",e[e.GRANTED=1]="GRANTED",e}({});class Ah{constructor(e){this._instance=e}get _config(){return this._instance.config}get consent(){return this._getDnt()?Rh.DENIED:this._storedConsent}isOptedOut(){return this.consent===Rh.DENIED||this.consent===Rh.PENDING&&this._config.opt_out_capturing_by_default}isOptedIn(){return!this.isOptedOut()}optInOut(e){this._storage._set(this._storageKey,e?1:0,this._config.cookie_expiration,this._config.cross_subdomain_cookie,this._config.secure_cookie)}reset(){this._storage._remove(this._storageKey,this._config.cross_subdomain_cookie)}get _storageKey(){var{token:e,opt_out_capturing_cookie_prefix:t}=this._instance.config;return(t||"__ph_opt_in_out_")+e}get _storedConsent(){var e=this._storage._get(this._storageKey);return"1"===e?Rh.GRANTED:"0"===e?Rh.DENIED:Rh.PENDING}get _storage(){if(!this._persistentStore){var e=this._config.opt_out_capturing_persistence_type;this._persistentStore="localStorage"===e?Ch:Sh;var t="localStorage"===e?Sh:Ch;t._get(this._storageKey)&&(this._persistentStore._get(this._storageKey)||this.optInOut("1"===t._get(this._storageKey)),t._remove(this._storageKey,this._config.cross_subdomain_cookie))}return this._persistentStore}_getDnt(){return!!this._config.respect_dnt&&!!Hi([null==Wr?void 0:Wr.doNotTrack,null==Wr?void 0:Wr.msDoNotTrack,Xr.doNotTrack],(e=>ti([!0,1,"1","yes"],e)))}}var Fh=xi("[Dead Clicks]"),Nh=()=>!0,Ph=e=>{var t,r=!(null==(t=e.instance.persistence)||!t.get_property(sn)),i=e.instance.config.capture_dead_clicks;return bi(i)?i:r};class Oh{get lazyLoadedDeadClicksAutocapture(){return this._lazyLoadedDeadClicksAutocapture}constructor(e,t,r){this.instance=e,this.isEnabled=t,this.onCapture=r,this.startIfEnabled()}onRemoteConfig(e){this.instance.persistence&&this.instance.persistence.register({[sn]:null==e?void 0:e.captureDeadClicks}),this.startIfEnabled()}startIfEnabled(){this.isEnabled(this)&&this._loadScript((()=>{this._start()}))}_loadScript(e){var t,r;null!=(t=Xr.__PosthogExtensions__)&&t.initDeadClicksAutocapture&&e(),null==(r=Xr.__PosthogExtensions__)||null==r.loadExternalDependency||r.loadExternalDependency(this.instance,"dead-clicks-autocapture",(t=>{t?Fh.error("failed to load script",t):e()}))}_start(){var e;if(Ur){if(!this._lazyLoadedDeadClicksAutocapture&&null!=(e=Xr.__PosthogExtensions__)&&e.initDeadClicksAutocapture){var t=hi(this.instance.config.capture_dead_clicks)?this.instance.config.capture_dead_clicks:{};t.__onCapture=this.onCapture,this._lazyLoadedDeadClicksAutocapture=Xr.__PosthogExtensions__.initDeadClicksAutocapture(this.instance,t),this._lazyLoadedDeadClicksAutocapture.start(Ur),Fh.info("starting...")}}else Fh.error("`document` not found. Cannot start.")}stop(){this._lazyLoadedDeadClicksAutocapture&&(this._lazyLoadedDeadClicksAutocapture.stop(),this._lazyLoadedDeadClicksAutocapture=void 0,Fh.info("stopping..."))}}function Lh(e,t,r,i,n){return t>r&&(Ei.warn("min cannot be greater than max."),t=r),yi(e)?e>r?(i&&Ei.warn(i+" cannot be  greater than max: "+r+". Using max value instead."),r):e<t?(i&&Ei.warn(i+" cannot be less than min: "+t+". Using min value instead."),t):e:(i&&Ei.warn(i+" must be a number. using max or fallback. max: "+r+", fallback: "+n),Lh(n||r,t,r,i))}class Dh{constructor(e){this._buckets={},this._refillBuckets=()=>{Object.keys(this._buckets).forEach((e=>{var t=this._getBucket(e)+this._refillRate;t>=this._bucketSize?delete this._buckets[e]:this._setBucket(e,t)}))},this._getBucket=e=>this._buckets[String(e)],this._setBucket=(e,t)=>{this._buckets[String(e)]=t},this.consumeRateLimit=e=>{var t,r=null!==(t=this._getBucket(e))&&void 0!==t?t:this._bucketSize;if(0===(r=Math.max(r-1,0)))return!0;this._setBucket(e,r);var i,n=0===r;n&&(null==(i=this._onBucketRateLimited)||i.call(this,e));return n},this._options=e,this._onBucketRateLimited=this._options._onBucketRateLimited,this._bucketSize=Lh(this._options.bucketSize,0,100,"rate limiter bucket size"),this._refillRate=Lh(this._options.refillRate,0,this._bucketSize,"rate limiter refill rate"),this._refillInterval=Lh(this._options.refillInterval,0,864e5,"rate limiter refill interval"),setInterval((()=>{this._refillBuckets()}),this._refillInterval)}}var Bh=xi("[ExceptionAutocapture]");class qh{constructor(e){var t,r,i;this._startCapturing=()=>{var e;if(Br&&this.isEnabled&&null!=(e=Xr.__PosthogExtensions__)&&e.errorWrappingFunctions){var t=Xr.__PosthogExtensions__.errorWrappingFunctions.wrapOnError,r=Xr.__PosthogExtensions__.errorWrappingFunctions.wrapUnhandledRejection,i=Xr.__PosthogExtensions__.errorWrappingFunctions.wrapConsoleError;try{!this._unwrapOnError&&this._config.capture_unhandled_errors&&(this._unwrapOnError=t(this.captureException.bind(this))),!this._unwrapUnhandledRejection&&this._config.capture_unhandled_rejections&&(this._unwrapUnhandledRejection=r(this.captureException.bind(this))),!this._unwrapConsoleError&&this._config.capture_console_errors&&(this._unwrapConsoleError=i(this.captureException.bind(this)))}catch(e){Bh.error("failed to start",e),this._stopCapturing()}}},this._instance=e,this._remoteEnabled=!(null==(t=this._instance.persistence)||!t.props[tn]),this._config=this._requiredConfig(),this._rateLimiter=new Dh({refillRate:null!==(r=this._instance.config.error_tracking.__exceptionRateLimiterRefillRate)&&void 0!==r?r:1,bucketSize:null!==(i=this._instance.config.error_tracking.__exceptionRateLimiterBucketSize)&&void 0!==i?i:10,refillInterval:1e4}),this.startIfEnabled()}_requiredConfig(){var e=this._instance.config.capture_exceptions,t={capture_unhandled_errors:!1,capture_unhandled_rejections:!1,capture_console_errors:!1};return hi(e)?t=r({},t,e):(_i(e)?this._remoteEnabled:e)&&(t=r({},t,{capture_unhandled_errors:!0,capture_unhandled_rejections:!0})),t}get isEnabled(){return this._config.capture_console_errors||this._config.capture_unhandled_errors||this._config.capture_unhandled_rejections}startIfEnabled(){this.isEnabled&&(Bh.info("enabled"),this._loadScript(this._startCapturing))}_loadScript(e){var t,r;null!=(t=Xr.__PosthogExtensions__)&&t.errorWrappingFunctions&&e(),null==(r=Xr.__PosthogExtensions__)||null==r.loadExternalDependency||r.loadExternalDependency(this._instance,"exception-autocapture",(t=>{if(t)return Bh.error("failed to load script",t);e()}))}_stopCapturing(){var e,t,r;null==(e=this._unwrapOnError)||e.call(this),this._unwrapOnError=void 0,null==(t=this._unwrapUnhandledRejection)||t.call(this),this._unwrapUnhandledRejection=void 0,null==(r=this._unwrapConsoleError)||r.call(this),this._unwrapConsoleError=void 0}onRemoteConfig(e){var t=e.autocaptureExceptions;this._remoteEnabled=!!t||!1,this._config=this._requiredConfig(),this._instance.persistence&&this._instance.persistence.register({[tn]:this._remoteEnabled}),this.startIfEnabled()}captureException(e){var t,r=this._instance.requestRouter.endpointFor("ui");e.$exception_personURL=r+"/project/"+this._instance.config.token+"/person/"+this._instance.get_distinct_id();var i=null!==(t=e.$exception_list[0].type)&&void 0!==t?t:"Exception";this._rateLimiter.consumeRateLimit(i)?Bh.info("Skipping exception capture because of client rate limiting.",{exception:e.$exception_list[0].type}):this._instance.exceptions.sendExceptionEvent(e)}}class Hh{constructor(e){var t;this._instance=e,this._lastPathname=(null==Br||null==(t=Br.location)?void 0:t.pathname)||""}get isEnabled(){return"history_change"===this._instance.config.capture_pageview}startIfEnabled(){this.isEnabled&&(Ei.info("History API monitoring enabled, starting..."),this.monitorHistoryChanges())}stop(){this._popstateListener&&this._popstateListener(),this._popstateListener=void 0,Ei.info("History API monitoring stopped")}monitorHistoryChanges(){var e,t;if(Br&&Br.history){var r=this;null!=(e=Br.history.pushState)&&e.__posthog_wrapped__||ji(Br.history,"pushState",(e=>function(t,i,n){e.call(this,t,i,n),r._capturePageview("pushState")})),null!=(t=Br.history.replaceState)&&t.__posthog_wrapped__||ji(Br.history,"replaceState",(e=>function(t,i,n){e.call(this,t,i,n),r._capturePageview("replaceState")})),this._setupPopstateListener()}}_capturePageview(e){try{var t,r=null==Br||null==(t=Br.location)?void 0:t.pathname;if(!r)return;r!==this._lastPathname&&this.isEnabled&&this._instance.capture("$pageview",{navigation_type:e}),this._lastPathname=r}catch(t){Ei.error("Error capturing "+e+" pageview",t)}}_setupPopstateListener(){if(!this._popstateListener){var e=()=>{this._capturePageview("popstate")};$i(Br,"popstate",e),this._popstateListener=()=>{Br&&Br.removeEventListener("popstate",e)}}}}function $h(e){var t,r;return(null==(t=JSON.stringify(e,(r=[],function(e,t){if(hi(t)){for(;r.length>0&&r[r.length-1]!==this;)r.pop();return r.includes(t)?"[Circular]":(r.push(t),t)}return t})))?void 0:t.length)||0}function Vh(e,t){if(void 0===t&&(t=6606028.8),e.size>=t&&e.data.length>1){var r=Math.floor(e.data.length/2),i=e.data.slice(0,r),n=e.data.slice(r);return[Vh({size:$h(i),data:i,sessionId:e.sessionId,windowId:e.windowId}),Vh({size:$h(n),data:n,sessionId:e.sessionId,windowId:e.windowId})].flatMap((e=>e))}return[e]}var Wh=(e=>(e[e.DomContentLoaded=0]="DomContentLoaded",e[e.Load=1]="Load",e[e.FullSnapshot=2]="FullSnapshot",e[e.IncrementalSnapshot=3]="IncrementalSnapshot",e[e.Meta=4]="Meta",e[e.Custom=5]="Custom",e[e.Plugin=6]="Plugin",e))(Wh||{}),Uh=(e=>(e[e.Mutation=0]="Mutation",e[e.MouseMove=1]="MouseMove",e[e.MouseInteraction=2]="MouseInteraction",e[e.Scroll=3]="Scroll",e[e.ViewportResize=4]="ViewportResize",e[e.Input=5]="Input",e[e.TouchMove=6]="TouchMove",e[e.MediaInteraction=7]="MediaInteraction",e[e.StyleSheetRule=8]="StyleSheetRule",e[e.CanvasMutation=9]="CanvasMutation",e[e.Font=10]="Font",e[e.Log=11]="Log",e[e.Drag=12]="Drag",e[e.StyleDeclaration=13]="StyleDeclaration",e[e.Selection=14]="Selection",e[e.AdoptedStyleSheet=15]="AdoptedStyleSheet",e[e.CustomElement=16]="CustomElement",e))(Uh||{});class Zh{constructor(e,t){var r,i;void 0===t&&(t={}),this._loggedTracker={},this._onNodeRateLimited=e=>{if(!this._loggedTracker[e]){var t,r;this._loggedTracker[e]=!0;var i=this._getNode(e);null==(t=(r=this._options).onBlockedNode)||t.call(r,e,i)}},this._getNodeOrRelevantParent=e=>{var t=this._getNode(e);if("svg"!==(null==t?void 0:t.nodeName)&&t instanceof Element){var r=t.closest("svg");if(r)return[this._rrweb.mirror.getId(r),r]}return[e,t]},this._getNode=e=>this._rrweb.mirror.getNode(e),this._numberOfChanges=e=>{var t,r,i,n,s,o,a,l;return(null!==(t=null==(r=e.removes)?void 0:r.length)&&void 0!==t?t:0)+(null!==(i=null==(n=e.attributes)?void 0:n.length)&&void 0!==i?i:0)+(null!==(s=null==(o=e.texts)?void 0:o.length)&&void 0!==s?s:0)+(null!==(a=null==(l=e.adds)?void 0:l.length)&&void 0!==a?a:0)},this.throttleMutations=e=>{if(3!==e.type||0!==e.data.source)return e;var t=e.data,r=this._numberOfChanges(t);t.attributes&&(t.attributes=t.attributes.filter((e=>{var[t]=this._getNodeOrRelevantParent(e.id);return!this._rateLimiter.consumeRateLimit(t)&&e})));var i=this._numberOfChanges(t);return 0!==i||r===i?e:void 0},this._rrweb=e,this._options=t,this._rateLimiter=new Dh({bucketSize:null!==(r=this._options.bucketSize)&&void 0!==r?r:100,refillRate:null!==(i=this._options.refillRate)&&void 0!==i?i:10,refillInterval:1e3,_onBucketRateLimited:this._onNodeRateLimited})}}function zh(e,t){return function(e){for(var t=0,r=0;r<e.length;r++)t=(t<<5)-t+e.charCodeAt(r),t|=0;return Math.abs(t)}(e)%100<Lh(100*t,0,100)}var Gh="disabled",jh="sampled",Yh="active",Xh="buffering",Jh="paused",Kh="trigger",Qh=Kh+"_activated",ep=Kh+"_pending",tp=Kh+"_"+Gh;function rp(e,t){return t.some((t=>"regex"===t.matching&&new RegExp(t.url).test(e)))}class ip{constructor(e){this._matchers=e}triggerStatus(e){var t=this._matchers.map((t=>t.triggerStatus(e)));return t.includes(Qh)?Qh:t.includes(ep)?ep:tp}stop(){this._matchers.forEach((e=>e.stop()))}}class np{constructor(e){this._matchers=e}triggerStatus(e){var t=new Set;for(var r of this._matchers)t.add(r.triggerStatus(e));switch(t.delete(tp),t.size){case 0:return tp;case 1:return Array.from(t)[0];default:return ep}}stop(){this._matchers.forEach((e=>e.stop()))}}class sp{triggerStatus(){return ep}stop(){}}class op{constructor(e){this._urlTriggers=[],this._urlBlocklist=[],this.urlBlocked=!1,this._instance=e}onRemoteConfig(e){var t,r;this._urlTriggers=(null==(t=e.sessionRecording)?void 0:t.urlTriggers)||[],this._urlBlocklist=(null==(r=e.sessionRecording)?void 0:r.urlBlocklist)||[]}_urlTriggerStatus(e){var t;return 0===this._urlTriggers.length?tp:(null==(t=this._instance)?void 0:t.get_property(vn))===e?Qh:ep}triggerStatus(e){var t=this._urlTriggerStatus(e),r=t===Qh?Qh:t===ep?ep:tp;return this._instance.register_for_session({$sdk_debug_replay_url_trigger_status:r}),r}checkUrlTriggerConditions(e,t,r){if(void 0!==Br&&Br.location.href){var i=Br.location.href,n=this.urlBlocked,s=rp(i,this._urlBlocklist);n&&s||(s&&!n?e():!s&&n&&t(),rp(i,this._urlTriggers)&&r("url"))}}stop(){}}class ap{constructor(e){this.linkedFlag=null,this.linkedFlagSeen=!1,this._flaglistenerCleanup=()=>{},this._instance=e}triggerStatus(){var e=ep;return mi(this.linkedFlag)&&(e=tp),this.linkedFlagSeen&&(e=Qh),this._instance.register_for_session({$sdk_debug_replay_linked_flag_trigger_status:e}),e}onRemoteConfig(e,t){var r;if(this.linkedFlag=(null==(r=e.sessionRecording)?void 0:r.linkedFlag)||null,!mi(this.linkedFlag)&&!this.linkedFlagSeen){var i=gi(this.linkedFlag)?this.linkedFlag:this.linkedFlag.flag,n=gi(this.linkedFlag)?null:this.linkedFlag.variant;this._flaglistenerCleanup=this._instance.onFeatureFlags(((e,r)=>{var s=!1;if(hi(r)&&i in r){var o=r[i];s=bi(o)?!0===o:n?o===n:!!o}this.linkedFlagSeen=s,s&&t(i,n)}))}}stop(){this._flaglistenerCleanup()}}class lp{constructor(e){this._eventTriggers=[],this._instance=e}onRemoteConfig(e){var t;this._eventTriggers=(null==(t=e.sessionRecording)?void 0:t.eventTriggers)||[]}_eventTriggerStatus(e){var t;return 0===this._eventTriggers.length?tp:(null==(t=this._instance)?void 0:t.get_property(mn))===e?Qh:ep}triggerStatus(e){var t=this._eventTriggerStatus(e),r=t===Qh?Qh:t===ep?ep:tp;return this._instance.register_for_session({$sdk_debug_replay_event_trigger_status:r}),r}stop(){}}function up(e){return e.isRecordingEnabled?Xh:Gh}function cp(e){if(!e.receivedFlags)return Xh;if(!e.isRecordingEnabled)return Gh;if(e.urlTriggerMatching.urlBlocked)return Jh;var t=!0===e.isSampled,r=new ip([e.eventTriggerMatching,e.urlTriggerMatching,e.linkedFlagMatching]).triggerStatus(e.sessionId);return t?jh:r===Qh?Yh:r===ep?Xh:!1===e.isSampled?Gh:Yh}function dp(e){if(!e.receivedFlags)return Xh;if(!e.isRecordingEnabled)return Gh;if(e.urlTriggerMatching.urlBlocked)return Jh;var t=new np([e.eventTriggerMatching,e.urlTriggerMatching,e.linkedFlagMatching]).triggerStatus(e.sessionId),r=t!==tp,i=bi(e.isSampled);return r&&t===ep?Xh:r&&t===tp||i&&!e.isSampled?Gh:!0===e.isSampled?jh:Yh}var hp="[SessionRecording]",pp=xi(hp);function _p(){var e;return null==Xr||null==(e=Xr.__PosthogExtensions__)||null==(e=e.rrweb)?void 0:e.record}var gp=3e5,fp=[Uh.MouseMove,Uh.MouseInteraction,Uh.Scroll,Uh.ViewportResize,Uh.Input,Uh.TouchMove,Uh.MediaInteraction,Uh.Drag],vp=e=>({rrwebMethod:e,enqueuedAt:Date.now(),attempt:1});function mp(e){return function(e,t){for(var r="",i=0;i<e.length;){var n=e[i++];n<128||t?r+=String.fromCharCode(n):n<224?r+=String.fromCharCode((31&n)<<6|63&e[i++]):n<240?r+=String.fromCharCode((15&n)<<12|(63&e[i++])<<6|63&e[i++]):(n=((15&n)<<18|(63&e[i++])<<12|(63&e[i++])<<6|63&e[i++])-65536,r+=String.fromCharCode(55296|n>>10,56320|1023&n))}return r}(Yl(Xl(JSON.stringify(e))),!0)}function yp(e){return e.type===Wh.Custom&&"sessionIdle"===e.data.tag}class bp{get sessionId(){return this._sessionId}get _sessionIdleThresholdMilliseconds(){return this._instance.config.session_recording.session_idle_threshold_ms||3e5}get started(){return this._captureStarted}get _sessionManager(){if(!this._instance.sessionManager)throw new Error(hp+" must be started with a valid sessionManager.");return this._instance.sessionManager}get _fullSnapshotIntervalMillis(){var e,t;return this._triggerMatching.triggerStatus(this.sessionId)===ep?6e4:null!==(e=null==(t=this._instance.config.session_recording)?void 0:t.full_snapshot_interval_millis)&&void 0!==e?e:gp}get _isSampled(){var e=this._instance.get_property(fn);return bi(e)?e:null}get _sessionDuration(){var e,t,r=null==(e=this._buffer)?void 0:e.data[(null==(t=this._buffer)?void 0:t.data.length)-1],{sessionStartTimestamp:i}=this._sessionManager.checkAndGetSessionAndWindowId(!0);return r?r.timestamp-i:null}get _isRecordingEnabled(){var e=!!this._instance.get_property(an),t=!this._instance.config.disable_session_recording;return Br&&e&&t}get _isConsoleLogCaptureEnabled(){var e=!!this._instance.get_property(ln),t=this._instance.config.enable_recording_console_log;return null!=t?t:e}get _canvasRecording(){var e,t,r,i,n,s,o=this._instance.config.session_recording.captureCanvas,a=this._instance.get_property(dn),l=null!==(e=null!==(t=null==o?void 0:o.recordCanvas)&&void 0!==t?t:null==a?void 0:a.enabled)&&void 0!==e&&e,u=null!==(r=null!==(i=null==o?void 0:o.canvasFps)&&void 0!==i?i:null==a?void 0:a.fps)&&void 0!==r?r:4,c=null!==(n=null!==(s=null==o?void 0:o.canvasQuality)&&void 0!==s?s:null==a?void 0:a.quality)&&void 0!==n?n:.4;if("string"==typeof c){var d=parseFloat(c);c=isNaN(d)?.4:d}return{enabled:l,fps:Lh(u,0,12,"canvas recording fps",4),quality:Lh(c,0,1,"canvas recording quality",.4)}}get _networkPayloadCapture(){var e,t,r=this._instance.get_property(un),i={recordHeaders:null==(e=this._instance.config.session_recording)?void 0:e.recordHeaders,recordBody:null==(t=this._instance.config.session_recording)?void 0:t.recordBody},n=(null==i?void 0:i.recordHeaders)||(null==r?void 0:r.recordHeaders),s=(null==i?void 0:i.recordBody)||(null==r?void 0:r.recordBody),o=hi(this._instance.config.capture_performance)?this._instance.config.capture_performance.network_timing:this._instance.config.capture_performance,a=!!(bi(o)?o:null==r?void 0:r.capturePerformance);return n||s||a?{recordHeaders:n,recordBody:s,recordPerformance:a}:void 0}get _masking(){var e,t,r,i,n,s,o=this._instance.get_property(cn),a={maskAllInputs:null==(e=this._instance.config.session_recording)?void 0:e.maskAllInputs,maskTextSelector:null==(t=this._instance.config.session_recording)?void 0:t.maskTextSelector,blockSelector:null==(r=this._instance.config.session_recording)?void 0:r.blockSelector},l=null!==(i=null==a?void 0:a.maskAllInputs)&&void 0!==i?i:null==o?void 0:o.maskAllInputs,u=null!==(n=null==a?void 0:a.maskTextSelector)&&void 0!==n?n:null==o?void 0:o.maskTextSelector,c=null!==(s=null==a?void 0:a.blockSelector)&&void 0!==s?s:null==o?void 0:o.blockSelector;return _i(l)&&_i(u)&&_i(c)?void 0:{maskAllInputs:null==l||l,maskTextSelector:u,blockSelector:c}}get _sampleRate(){var e=this._instance.get_property(hn);return yi(e)?e:null}get _minimumDuration(){var e=this._instance.get_property(pn);return yi(e)?e:null}get status(){return this._receivedFlags?this._statusMatcher({receivedFlags:this._receivedFlags,isRecordingEnabled:this._isRecordingEnabled,isSampled:this._isSampled,urlTriggerMatching:this._urlTriggerMatching,eventTriggerMatching:this._eventTriggerMatching,linkedFlagMatching:this._linkedFlagMatching,sessionId:this.sessionId}):Xh}constructor(e){if(this._statusMatcher=up,this._receivedFlags=!1,this._queuedRRWebEvents=[],this._isIdle="unknown",this._lastActivityTimestamp=Date.now(),this._triggerMatching=new sp,this._removePageViewCaptureHook=void 0,this._onSessionIdListener=void 0,this._persistFlagsOnSessionListener=void 0,this._samplingSessionListener=void 0,this._removeEventTriggerCaptureHook=void 0,this._forceAllowLocalhostNetworkCapture=!1,this._onBeforeUnload=()=>{this._flushBuffer()},this._onOffline=()=>{this._tryAddCustomEvent("browser offline",{})},this._onOnline=()=>{this._tryAddCustomEvent("browser online",{})},this._onVisibilityChange=()=>{if(null!=Ur&&Ur.visibilityState){var e="window "+Ur.visibilityState;this._tryAddCustomEvent(e,{})}},this._instance=e,this._captureStarted=!1,this._endpoint="/s/",this._stopRrweb=void 0,this._receivedFlags=!1,!this._instance.sessionManager)throw pp.error("started without valid sessionManager"),new Error(hp+" started without valid sessionManager. This is a bug.");if(this._instance.config.__preview_experimental_cookieless_mode)throw new Error(hp+" cannot be used with __preview_experimental_cookieless_mode.");this._linkedFlagMatching=new ap(this._instance),this._urlTriggerMatching=new op(this._instance),this._eventTriggerMatching=new lp(this._instance);var{sessionId:t,windowId:r}=this._sessionManager.checkAndGetSessionAndWindowId();this._sessionId=t,this._windowId=r,this._buffer=this._clearBuffer(),this._sessionIdleThresholdMilliseconds>=this._sessionManager.sessionTimeoutMs&&pp.warn("session_idle_threshold_ms ("+this._sessionIdleThresholdMilliseconds+") is greater than the session timeout ("+this._sessionManager.sessionTimeoutMs+"). Session will never be detected as idle")}startIfEnabledOrStop(e){this._isRecordingEnabled?(this._startCapture(e),$i(Br,"beforeunload",this._onBeforeUnload),$i(Br,"offline",this._onOffline),$i(Br,"online",this._onOnline),$i(Br,"visibilitychange",this._onVisibilityChange),this._setupSampling(),this._addEventTriggerListener(),mi(this._removePageViewCaptureHook)&&(this._removePageViewCaptureHook=this._instance.on("eventCaptured",(e=>{try{if("$pageview"===e.event){var t=null!=e&&e.properties.$current_url?this._maskUrl(null==e?void 0:e.properties.$current_url):"";if(!t)return;this._tryAddCustomEvent("$pageview",{href:t})}}catch(e){pp.error("Could not add $pageview to rrweb session",e)}}))),this._onSessionIdListener||(this._onSessionIdListener=this._sessionManager.onSessionId(((e,t,r)=>{var i,n;r&&(this._tryAddCustomEvent("$session_id_change",{sessionId:e,windowId:t,changeReason:r}),null==(i=this._instance)||null==(i=i.persistence)||i.unregister(mn),null==(n=this._instance)||null==(n=n.persistence)||n.unregister(vn))})))):this.stopRecording()}stopRecording(){var e,t,r,i;this._captureStarted&&this._stopRrweb&&(this._stopRrweb(),this._stopRrweb=void 0,this._captureStarted=!1,null==Br||Br.removeEventListener("beforeunload",this._onBeforeUnload),null==Br||Br.removeEventListener("offline",this._onOffline),null==Br||Br.removeEventListener("online",this._onOnline),null==Br||Br.removeEventListener("visibilitychange",this._onVisibilityChange),this._clearBuffer(),clearInterval(this._fullSnapshotTimer),null==(e=this._removePageViewCaptureHook)||e.call(this),this._removePageViewCaptureHook=void 0,null==(t=this._removeEventTriggerCaptureHook)||t.call(this),this._removeEventTriggerCaptureHook=void 0,null==(r=this._onSessionIdListener)||r.call(this),this._onSessionIdListener=void 0,null==(i=this._samplingSessionListener)||i.call(this),this._samplingSessionListener=void 0,this._eventTriggerMatching.stop(),this._urlTriggerMatching.stop(),this._linkedFlagMatching.stop(),pp.info("stopped"))}_resetSampling(){var e;null==(e=this._instance.persistence)||e.unregister(fn)}_makeSamplingDecision(e){var t,r=this._sessionId!==e,i=this._sampleRate;if(yi(i)){var n=this._isSampled,s=r||!bi(n),o=s?zh(e,i):n;s&&(o?this._reportStarted(jh):pp.warn("Sample rate ("+i+") has determined that this sessionId ("+e+") will not be sent to the server."),this._tryAddCustomEvent("samplingDecisionMade",{sampleRate:i,isSampled:o})),null==(t=this._instance.persistence)||t.register({[fn]:o})}else this._resetSampling()}onRemoteConfig(e){var t,r,i,n;(this._tryAddCustomEvent("$remote_config_received",e),this._persistRemoteConfig(e),null!=(t=e.sessionRecording)&&t.endpoint)&&(this._endpoint=null==(n=e.sessionRecording)?void 0:n.endpoint);this._setupSampling(),"any"===(null==(r=e.sessionRecording)?void 0:r.triggerMatchType)?(this._statusMatcher=cp,this._triggerMatching=new ip([this._eventTriggerMatching,this._urlTriggerMatching])):(this._statusMatcher=dp,this._triggerMatching=new np([this._eventTriggerMatching,this._urlTriggerMatching])),this._instance.register_for_session({$sdk_debug_replay_remote_trigger_matching_config:null==(i=e.sessionRecording)?void 0:i.triggerMatchType}),this._urlTriggerMatching.onRemoteConfig(e),this._eventTriggerMatching.onRemoteConfig(e),this._linkedFlagMatching.onRemoteConfig(e,((e,t)=>{this._reportStarted("linked_flag_matched",{flag:e,variant:t})})),this._receivedFlags=!0,this.startIfEnabledOrStop()}_setupSampling(){yi(this._sampleRate)&&mi(this._samplingSessionListener)&&(this._samplingSessionListener=this._sessionManager.onSessionId((e=>{this._makeSamplingDecision(e)})))}_persistRemoteConfig(e){if(this._instance.persistence){var t,i=this._instance.persistence,n=()=>{var t,n,s,o,a,l,u,c,d,h=null==(t=e.sessionRecording)?void 0:t.sampleRate,p=mi(h)?null:parseFloat(h);mi(p)&&this._resetSampling();var _=null==(n=e.sessionRecording)?void 0:n.minimumDurationMilliseconds;i.register({[an]:!!e.sessionRecording,[ln]:null==(s=e.sessionRecording)?void 0:s.consoleLogRecordingEnabled,[un]:r({capturePerformance:e.capturePerformance},null==(o=e.sessionRecording)?void 0:o.networkPayloadCapture),[cn]:null==(a=e.sessionRecording)?void 0:a.masking,[dn]:{enabled:null==(l=e.sessionRecording)?void 0:l.recordCanvas,fps:null==(u=e.sessionRecording)?void 0:u.canvasFps,quality:null==(c=e.sessionRecording)?void 0:c.canvasQuality},[hn]:p,[pn]:_i(_)?null:_,[_n]:null==(d=e.sessionRecording)?void 0:d.scriptConfig})};n(),null==(t=this._persistFlagsOnSessionListener)||t.call(this),this._persistFlagsOnSessionListener=this._sessionManager.onSessionId(n)}}log(e,t){var r;void 0===t&&(t="log"),null==(r=this._instance.sessionRecording)||r.onRRwebEmit({type:6,data:{plugin:"rrweb/console@1",payload:{level:t,trace:[],payload:[JSON.stringify(e)]}},timestamp:Date.now()})}_startCapture(e){if(!_i(Object.assign)&&!_i(Array.from)&&!(this._captureStarted||this._instance.config.disable_session_recording||this._instance.consent.isOptedOut())){var t;if(this._captureStarted=!0,this._sessionManager.checkAndGetSessionAndWindowId(),_p())this._onScriptLoaded();else null==(t=Xr.__PosthogExtensions__)||null==t.loadExternalDependency||t.loadExternalDependency(this._instance,this._scriptName,(e=>{if(e)return pp.error("could not load recorder",e);this._onScriptLoaded()}));pp.info("starting"),this.status===Yh&&this._reportStarted(e||"recording_initialized")}}get _scriptName(){var e;return(null==(e=this._instance)||null==(e=e.persistence)||null==(e=e.get_property(_n))?void 0:e.script)||"recorder"}_isInteractiveEvent(e){var t;return 3===e.type&&-1!==fp.indexOf(null==(t=e.data)?void 0:t.source)}_updateWindowAndSessionIds(e){var t=this._isInteractiveEvent(e);t||this._isIdle||e.timestamp-this._lastActivityTimestamp>this._sessionIdleThresholdMilliseconds&&(this._isIdle=!0,clearInterval(this._fullSnapshotTimer),this._tryAddCustomEvent("sessionIdle",{eventTimestamp:e.timestamp,lastActivityTimestamp:this._lastActivityTimestamp,threshold:this._sessionIdleThresholdMilliseconds,bufferLength:this._buffer.data.length,bufferSize:this._buffer.size}),this._flushBuffer());var r=!1;if(t&&(this._lastActivityTimestamp=e.timestamp,this._isIdle)){var i="unknown"===this._isIdle;this._isIdle=!1,i||(this._tryAddCustomEvent("sessionNoLongerIdle",{reason:"user activity",type:e.type}),r=!0)}if(!this._isIdle){var{windowId:n,sessionId:s}=this._sessionManager.checkAndGetSessionAndWindowId(!t,e.timestamp),o=this._sessionId!==s,a=this._windowId!==n;this._windowId=n,this._sessionId=s,o||a?(this.stopRecording(),this.startIfEnabledOrStop("session_id_changed")):r&&this._scheduleFullSnapshot()}}_tryRRWebMethod(e){try{return e.rrwebMethod(),!0}catch(t){return this._queuedRRWebEvents.length<10?this._queuedRRWebEvents.push({enqueuedAt:e.enqueuedAt||Date.now(),attempt:e.attempt++,rrwebMethod:e.rrwebMethod}):pp.warn("could not emit queued rrweb event.",t,e),!1}}_tryAddCustomEvent(e,t){return this._tryRRWebMethod(vp((()=>_p().addCustomEvent(e,t))))}_tryTakeFullSnapshot(){return this._tryRRWebMethod(vp((()=>_p().takeFullSnapshot())))}_onScriptLoaded(){var e,t,i,n,s={blockClass:"ph-no-capture",blockSelector:void 0,ignoreClass:"ph-ignore-input",maskTextClass:"ph-mask",maskTextSelector:void 0,maskTextFn:void 0,maskAllInputs:!0,maskInputOptions:{password:!0},maskInputFn:void 0,slimDOMOptions:{},collectFonts:!1,inlineStylesheet:!0,recordCrossOriginIframes:!1},o=this._instance.config.session_recording;for(var[a,l]of Object.entries(o||{}))a in s&&("maskInputOptions"===a?s.maskInputOptions=r({password:!0},l):s[a]=l);(this._canvasRecording&&this._canvasRecording.enabled&&(s.recordCanvas=!0,s.sampling={canvas:this._canvasRecording.fps},s.dataURLOptions={type:"image/webp",quality:this._canvasRecording.quality}),this._masking)&&(s.maskAllInputs=null===(t=this._masking.maskAllInputs)||void 0===t||t,s.maskTextSelector=null!==(i=this._masking.maskTextSelector)&&void 0!==i?i:void 0,s.blockSelector=null!==(n=this._masking.blockSelector)&&void 0!==n?n:void 0);var u=_p();if(u){this._mutationThrottler=null!==(e=this._mutationThrottler)&&void 0!==e?e:new Zh(u,{refillRate:this._instance.config.session_recording.__mutationThrottlerRefillRate,bucketSize:this._instance.config.session_recording.__mutationThrottlerBucketSize,onBlockedNode:(e,t)=>{var r="Too many mutations on node '"+e+"'. Rate limiting. This could be due to SVG animations or something similar";pp.info(r,{node:t}),this.log(hp+" "+r,"warn")}});var c=this._gatherRRWebPlugins();this._stopRrweb=u(r({emit:e=>{this.onRRwebEmit(e)},plugins:c},s)),this._lastActivityTimestamp=Date.now(),this._isIdle=bi(this._isIdle)?this._isIdle:"unknown",this._tryAddCustomEvent("$session_options",{sessionRecordingOptions:s,activePlugins:c.map((e=>null==e?void 0:e.name))}),this._tryAddCustomEvent("$posthog_config",{config:this._instance.config})}else pp.error("onScriptLoaded was called but rrwebRecord is not available. This indicates something has gone wrong.")}_scheduleFullSnapshot(){if(this._fullSnapshotTimer&&clearInterval(this._fullSnapshotTimer),!0!==this._isIdle){var e=this._fullSnapshotIntervalMillis;e&&(this._fullSnapshotTimer=setInterval((()=>{this._tryTakeFullSnapshot()}),e))}}_gatherRRWebPlugins(){var e,t,r=[],i=null==(e=Xr.__PosthogExtensions__)||null==(e=e.rrwebPlugins)?void 0:e.getRecordConsolePlugin;i&&this._isConsoleLogCaptureEnabled&&r.push(i());var n=null==(t=Xr.__PosthogExtensions__)||null==(t=t.rrwebPlugins)?void 0:t.getRecordNetworkPlugin;this._networkPayloadCapture&&ui(n)&&(!Vi.includes(location.hostname)||this._forceAllowLocalhostNetworkCapture?r.push(n(ys(this._instance.config,this._networkPayloadCapture))):pp.info("NetworkCapture not started because we are on localhost."));return r}onRRwebEmit(e){var t;if(this._processQueuedEvents(),e&&hi(e)){if(e.type===Wh.Meta){var i=this._maskUrl(e.data.href);if(this._lastHref=i,!i)return;e.data.href=i}else this._pageViewFallBack();if(this._urlTriggerMatching.checkUrlTriggerConditions((()=>this._pauseRecording()),(()=>this._resumeRecording()),(e=>this._activateTrigger(e))),!this._urlTriggerMatching.urlBlocked||function(e){return e.type===Wh.Custom&&"recording paused"===e.data.tag}(e)){e.type===Wh.FullSnapshot&&this._scheduleFullSnapshot(),e.type===Wh.FullSnapshot&&this._receivedFlags&&this._triggerMatching.triggerStatus(this.sessionId)===ep&&this._clearBuffer();var n=this._mutationThrottler?this._mutationThrottler.throttleMutations(e):e;if(n){var s=function(e){var t=e;if(t&&hi(t)&&6===t.type&&hi(t.data)&&"rrweb/console@1"===t.data.plugin){t.data.payload.payload.length>10&&(t.data.payload.payload=t.data.payload.payload.slice(0,10),t.data.payload.payload.push("...[truncated]"));for(var r=[],i=0;i<t.data.payload.payload.length;i++)t.data.payload.payload[i]&&t.data.payload.payload[i].length>2e3?r.push(t.data.payload.payload[i].slice(0,2e3)+"...[truncated]"):r.push(t.data.payload.payload[i]);return t.data.payload.payload=r,e}return e}(n);if(this._updateWindowAndSessionIds(s),!0!==this._isIdle||yp(s)){if(yp(s)){var o=s.data.payload;if(o){var a=o.lastActivityTimestamp,l=o.threshold;s.timestamp=a+l}}var u=null===(t=this._instance.config.session_recording.compress_events)||void 0===t||t?function(e){if($h(e)<1024)return e;try{if(e.type===Wh.FullSnapshot)return r({},e,{data:mp(e.data),cv:"2024-10"});if(e.type===Wh.IncrementalSnapshot&&e.data.source===Uh.Mutation)return r({},e,{cv:"2024-10",data:r({},e.data,{texts:mp(e.data.texts),attributes:mp(e.data.attributes),removes:mp(e.data.removes),adds:mp(e.data.adds)})});if(e.type===Wh.IncrementalSnapshot&&e.data.source===Uh.StyleSheetRule)return r({},e,{cv:"2024-10",data:r({},e.data,{adds:e.data.adds?mp(e.data.adds):void 0,removes:e.data.removes?mp(e.data.removes):void 0})})}catch(e){pp.error("could not compress event - will use uncompressed event",e)}return e}(s):s,c={$snapshot_bytes:$h(u),$snapshot_data:u,$session_id:this._sessionId,$window_id:this._windowId};this.status!==Gh?this._captureSnapshotBuffered(c):this._clearBuffer()}}}}}_pageViewFallBack(){if(!this._instance.config.capture_pageview&&Br){var e=this._maskUrl(Br.location.href);this._lastHref!==e&&(this._tryAddCustomEvent("$url_changed",{href:e}),this._lastHref=e)}}_processQueuedEvents(){if(this._queuedRRWebEvents.length){var e=[...this._queuedRRWebEvents];this._queuedRRWebEvents=[],e.forEach((e=>{Date.now()-e.enqueuedAt<=2e3&&this._tryRRWebMethod(e)}))}}_maskUrl(e){var t=this._instance.config.session_recording;if(t.maskNetworkRequestFn){var r,i={url:e};return null==(r=i=t.maskNetworkRequestFn(i))?void 0:r.url}return e}_clearBuffer(){return this._buffer={size:0,data:[],sessionId:this._sessionId,windowId:this._windowId},this._buffer}_flushBuffer(){this._flushBufferTimer&&(clearTimeout(this._flushBufferTimer),this._flushBufferTimer=void 0);var e=this._minimumDuration,t=this._sessionDuration,r=yi(t)&&t>=0,i=yi(e)&&r&&t<e;if(this.status===Xh||this.status===Jh||this.status===Gh||i)return this._flushBufferTimer=setTimeout((()=>{this._flushBuffer()}),2e3),this._buffer;this._buffer.data.length>0&&Vh(this._buffer).forEach((e=>{this._captureSnapshot({$snapshot_bytes:e.size,$snapshot_data:e.data,$session_id:e.sessionId,$window_id:e.windowId,$lib:"web",$lib_version:Ii.LIB_VERSION})}));return this._clearBuffer()}_captureSnapshotBuffered(e){var t,r=2+((null==(t=this._buffer)?void 0:t.data.length)||0);!this._isIdle&&(this._buffer.size+e.$snapshot_bytes+r>943718.4||this._buffer.sessionId!==this._sessionId)&&(this._buffer=this._flushBuffer()),this._buffer.size+=e.$snapshot_bytes,this._buffer.data.push(e.$snapshot_data),this._flushBufferTimer||this._isIdle||(this._flushBufferTimer=setTimeout((()=>{this._flushBuffer()}),2e3))}_captureSnapshot(e){this._instance.capture("$snapshot",e,{_url:this._instance.requestRouter.endpointFor("api",this._endpoint),_noTruncate:!0,_batchKey:"recordings",skip_client_rate_limiting:!0})}_activateTrigger(e){var t;this._triggerMatching.triggerStatus(this.sessionId)===ep&&(null==(t=this._instance)||null==(t=t.persistence)||t.register({["url"===e?vn:mn]:this._sessionId}),this._flushBuffer(),this._reportStarted(e+"_trigger_matched"))}_pauseRecording(){this._urlTriggerMatching.urlBlocked||(this._urlTriggerMatching.urlBlocked=!0,clearInterval(this._fullSnapshotTimer),pp.info("recording paused due to URL blocker"),this._tryAddCustomEvent("recording paused",{reason:"url blocker"}))}_resumeRecording(){this._urlTriggerMatching.urlBlocked&&(this._urlTriggerMatching.urlBlocked=!1,this._tryTakeFullSnapshot(),this._scheduleFullSnapshot(),this._tryAddCustomEvent("recording resumed",{reason:"left blocked url"}),pp.info("recording resumed"))}_addEventTriggerListener(){0!==this._eventTriggerMatching._eventTriggers.length&&mi(this._removeEventTriggerCaptureHook)&&(this._removeEventTriggerCaptureHook=this._instance.on("eventCaptured",(e=>{try{this._eventTriggerMatching._eventTriggers.includes(e.event)&&this._activateTrigger("event")}catch(e){pp.error("Could not activate event trigger",e)}})))}overrideLinkedFlag(){this._linkedFlagMatching.linkedFlagSeen=!0,this._tryTakeFullSnapshot(),this._reportStarted("linked_flag_overridden")}overrideSampling(){var e;null==(e=this._instance.persistence)||e.register({[fn]:!0}),this._tryTakeFullSnapshot(),this._reportStarted("sampling_overridden")}overrideTrigger(e){this._activateTrigger(e)}_reportStarted(e,t){this._instance.register_for_session({$session_recording_start_reason:e}),pp.info(e.replace("_"," "),t),ti(["recording_initialized","session_id_changed"],e)||this._tryAddCustomEvent(e,t)}get sdkDebugProperties(){var{sessionStartTimestamp:e}=this._sessionManager.checkAndGetSessionAndWindowId(!0);return{$recording_status:this.status,$sdk_debug_replay_internal_buffer_length:this._buffer.data.length,$sdk_debug_replay_internal_buffer_size:this._buffer.size,$sdk_debug_current_session_duration:this._sessionDuration,$sdk_debug_session_start:e}}}var Sp=xi("[SegmentIntegration]");function wp(e,t){var r=e.config.segment;if(!r)return t();!function(e,t){var r=e.config.segment;if(!r)return t();var i=r=>{var i=()=>r.anonymousId()||_a();e.config.get_device_id=i,r.id()&&(e.register({distinct_id:r.id(),$device_id:i()}),e.persistence.set_property(xn,"identified")),t()},n=r.user();"then"in n&&ui(n.then)?n.then((e=>i(e))):i(n)}(e,(()=>{r.register((e=>{Promise&&Promise.resolve||Sp.warn("This browser does not have Promise support, and can not use the segment integration");var t=(t,r)=>{if(!r)return t;t.event.userId||t.event.anonymousId===e.get_distinct_id()||(Sp.info("No userId set, resetting PostHog"),e.reset()),t.event.userId&&t.event.userId!==e.get_distinct_id()&&(Sp.info("UserId set, identifying with PostHog"),e.identify(t.event.userId));var i=e.calculateEventProperties(r,t.event.properties);return t.event.properties=Object.assign({},i,t.event.properties),t};return{name:"PostHog JS",type:"enrichment",version:"1.0.0",isLoaded:()=>!0,load:()=>Promise.resolve(),track:e=>t(e,e.event.event),page:e=>t(e,"$pageview"),identify:e=>t(e,"$identify"),screen:e=>t(e,"$screen")}})(e)).then((()=>{t()}))}))}var Cp="posthog-js";function Ip(e,t){var{organization:i,projectId:n,prefix:s,severityAllowList:o=["error"]}=void 0===t?{}:t;return t=>{var a,l,u,c,d;if(!("*"===o||o.includes(t.level))||!e.__loaded)return t;t.tags||(t.tags={});var h=e.requestRouter.endpointFor("ui","/project/"+e.config.token+"/person/"+e.get_distinct_id());t.tags["PostHog Person URL"]=h,e.sessionRecordingStarted()&&(t.tags["PostHog Recording URL"]=e.get_session_replay_url({withTimestamp:!0}));var p=(null==(a=t.exception)?void 0:a.values)||[],_=p.map((e=>r({},e,{stacktrace:e.stacktrace?r({},e.stacktrace,{type:"raw",frames:(e.stacktrace.frames||[]).map((e=>r({},e,{platform:"web:javascript"})))}):void 0}))),g={$exception_message:(null==(l=p[0])?void 0:l.value)||t.message,$exception_type:null==(u=p[0])?void 0:u.type,$exception_personURL:h,$exception_level:t.level,$exception_list:_,$sentry_event_id:t.event_id,$sentry_exception:t.exception,$sentry_exception_message:(null==(c=p[0])?void 0:c.value)||t.message,$sentry_exception_type:null==(d=p[0])?void 0:d.type,$sentry_tags:t.tags};return i&&n&&(g.$sentry_url=(s||"https://sentry.io/organizations/")+i+"/issues/?project="+n+"&query="+t.event_id),e.exceptions.sendExceptionEvent(g),t}}class kp{constructor(e,t,r,i,n){this.name=Cp,this.setupOnce=function(s){s(Ip(e,{organization:t,projectId:r,prefix:i,severityAllowList:n}))}}}var Ep=null!=Br&&Br.location?Gi(Br.location.hash,"__posthog")||Gi(location.hash,"state"):null,xp="_postHogToolbarParams",Tp=xi("[Toolbar]"),Mp=function(e){return e[e.UNINITIALIZED=0]="UNINITIALIZED",e[e.LOADING=1]="LOADING",e[e.LOADED=2]="LOADED",e}(Mp||{});class Rp{constructor(e){this.instance=e}_setToolbarState(e){Xr.ph_toolbar_state=e}_getToolbarState(){var e;return null!==(e=Xr.ph_toolbar_state)&&void 0!==e?e:Mp.UNINITIALIZED}maybeLoadToolbar(e,t,r){if(void 0===e&&(e=void 0),void 0===t&&(t=void 0),void 0===r&&(r=void 0),!Br||!Ur)return!1;e=null!=e?e:Br.location,r=null!=r?r:Br.history;try{if(!t){try{Br.localStorage.setItem("test","test"),Br.localStorage.removeItem("test")}catch(e){return!1}t=null==Br?void 0:Br.localStorage}var i,n=Ep||Gi(e.hash,"__posthog")||Gi(e.hash,"state"),s=n?Pi((()=>JSON.parse(atob(decodeURIComponent(n)))))||Pi((()=>JSON.parse(decodeURIComponent(n)))):null;return s&&"ph_authorize"===s.action?((i=s).source="url",i&&Object.keys(i).length>0&&(s.desiredHash?e.hash=s.desiredHash:r?r.replaceState(r.state,"",e.pathname+e.search):e.hash="")):((i=JSON.parse(t.getItem(xp)||"{}")).source="localstorage",delete i.userIntent),!(!i.token||this.instance.config.token!==i.token)&&(this.loadToolbar(i),!0)}catch(e){return!1}}_callLoadToolbar(e){var t=Xr.ph_load_toolbar||Xr.ph_load_editor;!mi(t)&&ui(t)?t(e,this.instance):Tp.warn("No toolbar load function found")}loadToolbar(e){var t=!(null==Ur||!Ur.getElementById(Pn));if(!Br||t)return!1;var i="custom"===this.instance.requestRouter.region&&this.instance.config.advanced_disable_toolbar_metrics,n=r({token:this.instance.config.token},e,{apiURL:this.instance.requestRouter.endpointFor("ui")},i?{instrument:!1}:{});if(Br.localStorage.setItem(xp,JSON.stringify(r({},n,{source:void 0}))),this._getToolbarState()===Mp.LOADED)this._callLoadToolbar(n);else if(this._getToolbarState()===Mp.UNINITIALIZED){var s;this._setToolbarState(Mp.LOADING),null==(s=Xr.__PosthogExtensions__)||null==s.loadExternalDependency||s.loadExternalDependency(this.instance,"toolbar",(e=>{if(e)return Tp.error("[Toolbar] Failed to load",e),void this._setToolbarState(Mp.UNINITIALIZED);this._setToolbarState(Mp.LOADED),this._callLoadToolbar(n)})),$i(Br,"turbolinks:load",(()=>{this._setToolbarState(Mp.UNINITIALIZED),this.loadToolbar(n)}))}return!0}_loadEditor(e){return this.loadToolbar(e)}maybeLoadEditor(e,t,r){return void 0===e&&(e=void 0),void 0===t&&(t=void 0),void 0===r&&(r=void 0),this.maybeLoadToolbar(e,t,r)}}var Ap=xi("[TracingHeaders]");class Fp{constructor(e){this._restoreXHRPatch=void 0,this._restoreFetchPatch=void 0,this._startCapturing=()=>{var e,t;_i(this._restoreXHRPatch)&&(null==(e=Xr.__PosthogExtensions__)||null==(e=e.tracingHeadersPatchFns)||e._patchXHR(this._instance.get_distinct_id(),this._instance.sessionManager));_i(this._restoreFetchPatch)&&(null==(t=Xr.__PosthogExtensions__)||null==(t=t.tracingHeadersPatchFns)||t._patchFetch(this._instance.get_distinct_id(),this._instance.sessionManager))},this._instance=e}_loadScript(e){var t,r;null!=(t=Xr.__PosthogExtensions__)&&t.tracingHeadersPatchFns&&e(),null==(r=Xr.__PosthogExtensions__)||null==r.loadExternalDependency||r.loadExternalDependency(this._instance,"tracing-headers",(t=>{if(t)return Ap.error("failed to load script",t);e()}))}startIfEnabledOrStop(){var e,t;this._instance.config.__add_tracing_headers?this._loadScript(this._startCapturing):(null==(e=this._restoreXHRPatch)||e.call(this),null==(t=this._restoreFetchPatch)||t.call(this),this._restoreXHRPatch=void 0,this._restoreFetchPatch=void 0)}}var Np=xi("[Web Vitals]"),Pp=9e5;class Op{constructor(e){var t;this._enabledServerSide=!1,this._initialized=!1,this._buffer={url:void 0,metrics:[],firstMetricTimestamp:void 0},this._flushToCapture=()=>{clearTimeout(this._delayedFlushTimer),0!==this._buffer.metrics.length&&(this._instance.capture("$web_vitals",this._buffer.metrics.reduce(((e,t)=>r({},e,{["$web_vitals_"+t.name+"_event"]:r({},t),["$web_vitals_"+t.name+"_value"]:t.value})),{})),this._buffer={url:void 0,metrics:[],firstMetricTimestamp:void 0})},this._addToBuffer=e=>{var t,i=null==(t=this._instance.sessionManager)?void 0:t.checkAndGetSessionAndWindowId(!0);if(_i(i))Np.error("Could not read session ID. Dropping metrics!");else{this._buffer=this._buffer||{url:void 0,metrics:[],firstMetricTimestamp:void 0};var n=this._currentURL();if(!_i(n))if(mi(null==e?void 0:e.name)||mi(null==e?void 0:e.value))Np.error("Invalid metric received",e);else if(this._maxAllowedValue&&e.value>=this._maxAllowedValue)Np.error("Ignoring metric with value >= "+this._maxAllowedValue,e);else this._buffer.url!==n&&(this._flushToCapture(),this._delayedFlushTimer=setTimeout(this._flushToCapture,this.flushToCaptureTimeoutMs)),_i(this._buffer.url)&&(this._buffer.url=n),this._buffer.firstMetricTimestamp=_i(this._buffer.firstMetricTimestamp)?Date.now():this._buffer.firstMetricTimestamp,e.attribution&&e.attribution.interactionTargetElement&&(e.attribution.interactionTargetElement=void 0),this._buffer.metrics.push(r({},e,{$current_url:n,$session_id:i.sessionId,$window_id:i.windowId,timestamp:Date.now()})),this._buffer.metrics.length===this.allowedMetrics.length&&this._flushToCapture()}},this._startCapturing=()=>{var e,t,r,i,n=Xr.__PosthogExtensions__;_i(n)||_i(n.postHogWebVitalsCallbacks)||({onLCP:e,onCLS:t,onFCP:r,onINP:i}=n.postHogWebVitalsCallbacks),e&&t&&r&&i?(this.allowedMetrics.indexOf("LCP")>-1&&e(this._addToBuffer.bind(this)),this.allowedMetrics.indexOf("CLS")>-1&&t(this._addToBuffer.bind(this)),this.allowedMetrics.indexOf("FCP")>-1&&r(this._addToBuffer.bind(this)),this.allowedMetrics.indexOf("INP")>-1&&i(this._addToBuffer.bind(this)),this._initialized=!0):Np.error("web vitals callbacks not loaded - not starting")},this._instance=e,this._enabledServerSide=!(null==(t=this._instance.persistence)||!t.props[nn]),this.startIfEnabled()}get allowedMetrics(){var e,t,r=hi(this._instance.config.capture_performance)?null==(e=this._instance.config.capture_performance)?void 0:e.web_vitals_allowed_metrics:void 0;return _i(r)?(null==(t=this._instance.persistence)?void 0:t.props[on])||["CLS","FCP","INP","LCP"]:r}get flushToCaptureTimeoutMs(){return(hi(this._instance.config.capture_performance)?this._instance.config.capture_performance.web_vitals_delayed_flush_ms:void 0)||5e3}get _maxAllowedValue(){var e=hi(this._instance.config.capture_performance)&&yi(this._instance.config.capture_performance.__web_vitals_max_value)?this._instance.config.capture_performance.__web_vitals_max_value:Pp;return 0<e&&e<=6e4?Pp:e}get isEnabled(){var e=null==Zr?void 0:Zr.protocol;if("http:"!==e&&"https:"!==e)return Np.info("Web Vitals are disabled on non-http/https protocols"),!1;var t=hi(this._instance.config.capture_performance)?this._instance.config.capture_performance.web_vitals:bi(this._instance.config.capture_performance)?this._instance.config.capture_performance:void 0;return bi(t)?t:this._enabledServerSide}startIfEnabled(){this.isEnabled&&!this._initialized&&(Np.info("enabled, starting..."),this._loadScript(this._startCapturing))}onRemoteConfig(e){var t=hi(e.capturePerformance)&&!!e.capturePerformance.web_vitals,r=hi(e.capturePerformance)?e.capturePerformance.web_vitals_allowed_metrics:void 0;this._instance.persistence&&(this._instance.persistence.register({[nn]:t}),this._instance.persistence.register({[on]:r})),this._enabledServerSide=t,this.startIfEnabled()}_loadScript(e){var t,r;null!=(t=Xr.__PosthogExtensions__)&&t.postHogWebVitalsCallbacks&&e(),null==(r=Xr.__PosthogExtensions__)||null==r.loadExternalDependency||r.loadExternalDependency(this._instance,"web-vitals",(t=>{t?Np.error("failed to load script",t):e()}))}_currentURL(){var e=Br?Br.location.href:void 0;return e||Np.error("Could not determine current URL"),e}}var Lp=xi("[Heatmaps]");function Dp(e){return hi(e)&&"clientX"in e&&"clientY"in e&&yi(e.clientX)&&yi(e.clientY)}class Bp{constructor(e){var t;this.rageclicks=new ah,this._enabledServerSide=!1,this._initialized=!1,this._flushInterval=null,this.instance=e,this._enabledServerSide=!(null==(t=this.instance.persistence)||!t.props[en])}get flushIntervalMilliseconds(){var e=5e3;return hi(this.instance.config.capture_heatmaps)&&this.instance.config.capture_heatmaps.flush_interval_milliseconds&&(e=this.instance.config.capture_heatmaps.flush_interval_milliseconds),e}get isEnabled(){return _i(this.instance.config.capture_heatmaps)?_i(this.instance.config.enable_heatmaps)?this._enabledServerSide:this.instance.config.enable_heatmaps:!1!==this.instance.config.capture_heatmaps}startIfEnabled(){if(this.isEnabled){if(this._initialized)return;Lp.info("starting..."),this._setupListeners(),this._flushInterval=setInterval(this._flush.bind(this),this.flushIntervalMilliseconds)}else{var e,t;clearInterval(null!==(e=this._flushInterval)&&void 0!==e?e:void 0),null==(t=this._deadClicksCapture)||t.stop(),this.getAndClearBuffer()}}onRemoteConfig(e){var t=!!e.heatmaps;this.instance.persistence&&this.instance.persistence.register({[en]:t}),this._enabledServerSide=t,this.startIfEnabled()}getAndClearBuffer(){var e=this._buffer;return this._buffer=void 0,e}_onDeadClick(e){this._onClick(e.originalEvent,"deadclick")}_setupListeners(){Br&&Ur&&($i(Br,"beforeunload",this._flush.bind(this)),$i(Ur,"click",(e=>this._onClick(e||(null==Br?void 0:Br.event))),{capture:!0}),$i(Ur,"mousemove",(e=>this._onMouseMove(e||(null==Br?void 0:Br.event))),{capture:!0}),this._deadClicksCapture=new Oh(this.instance,Nh,this._onDeadClick.bind(this)),this._deadClicksCapture.startIfEnabled(),this._initialized=!0)}_getProperties(e,t){var r=this.instance.scrollManager.scrollY(),i=this.instance.scrollManager.scrollX(),n=this.instance.scrollManager.scrollElement(),s=function(e,t,r){for(var i=e;i&&Bn(i)&&!qn(i,"body");){if(i===r)return!1;if(ti(t,null==Br?void 0:Br.getComputedStyle(i).position))return!0;i=Yn(i)}return!1}(Gn(e),["fixed","sticky"],n);return{x:e.clientX+(s?0:i),y:e.clientY+(s?0:r),target_fixed:s,type:t}}_onClick(e,t){var i;if(void 0===t&&(t="click"),!Dn(e.target)&&Dp(e)){var n=this._getProperties(e,t);null!=(i=this.rageclicks)&&i.isRageClick(e.clientX,e.clientY,(new Date).getTime())&&this._capture(r({},n,{type:"rageclick"})),this._capture(n)}}_onMouseMove(e){!Dn(e.target)&&Dp(e)&&(clearTimeout(this._mouseMoveTimeout),this._mouseMoveTimeout=setTimeout((()=>{this._capture(this._getProperties(e,"mousemove"))}),500))}_capture(e){if(Br){var t=Br.location.href;this._buffer=this._buffer||{},this._buffer[t]||(this._buffer[t]=[]),this._buffer[t].push(e)}}_flush(){this._buffer&&!pi(this._buffer)&&this.instance.capture("$$heatmap",{$heatmap_data:this.getAndClearBuffer()})}}class qp{constructor(e){this._instance=e}doPageView(e,t){var r,i=this._previousPageViewProperties(e,t);return this._currentPageview={pathname:null!==(r=null==Br?void 0:Br.location.pathname)&&void 0!==r?r:"",pageViewId:t,timestamp:e},this._instance.scrollManager.resetContext(),i}doPageLeave(e){var t;return this._previousPageViewProperties(e,null==(t=this._currentPageview)?void 0:t.pageViewId)}doEvent(){var e;return{$pageview_id:null==(e=this._currentPageview)?void 0:e.pageViewId}}_previousPageViewProperties(e,t){var r=this._currentPageview;if(!r)return{$pageview_id:t};var i={$pageview_id:t,$prev_pageview_id:r.pageViewId},n=this._instance.scrollManager.getContext();if(n&&!this._instance.config.disable_scroll_properties){var{maxScrollHeight:s,lastScrollY:o,maxScrollY:a,maxContentHeight:l,lastContentY:u,maxContentY:c}=n;if(!(_i(s)||_i(o)||_i(a)||_i(l)||_i(u)||_i(c))){s=Math.ceil(s),o=Math.ceil(o),a=Math.ceil(a),l=Math.ceil(l),u=Math.ceil(u),c=Math.ceil(c);var d=s<=1?1:Lh(o/s,0,1),h=s<=1?1:Lh(a/s,0,1),p=l<=1?1:Lh(u/l,0,1),_=l<=1?1:Lh(c/l,0,1);i=Ai(i,{$prev_pageview_last_scroll:o,$prev_pageview_last_scroll_percentage:d,$prev_pageview_max_scroll:a,$prev_pageview_max_scroll_percentage:h,$prev_pageview_last_content:u,$prev_pageview_last_content_percentage:p,$prev_pageview_max_content:c,$prev_pageview_max_content_percentage:_})}}return r.pathname&&(i.$prev_pageview_pathname=r.pathname),r.timestamp&&(i.$prev_pageview_duration=(e.getTime()-r.timestamp.getTime())/1e3),i}}var Hp=xi("[Error tracking]");class $p{constructor(e){var t,r;this._suppressionRules=[],this._instance=e,this._suppressionRules=null!==(t=null==(r=this._instance.persistence)?void 0:r.get_property(rn))&&void 0!==t?t:[]}onRemoteConfig(e){var t,r,i=null!==(t=null==(r=e.errorTracking)?void 0:r.suppressionRules)&&void 0!==t?t:[];this._suppressionRules=i,this._instance.persistence&&this._instance.persistence.register({[rn]:this._suppressionRules})}sendExceptionEvent(e){this._matchesSuppressionRule(e)?Hp.info("Skipping exception capture because a suppression rule matched"):this._instance.capture("$exception",e,{_noTruncate:!0,_batchKey:"exceptionEvent"})}_matchesSuppressionRule(e){var t=e.$exception_list;if(!t||!li(t)||0===t.length)return!1;var r=t.reduce(((e,t)=>{var{type:r,value:i}=t;return gi(r)&&r.length>0&&e.$exception_types.push(r),gi(i)&&i.length>0&&e.$exception_values.push(i),e}),{$exception_types:[],$exception_values:[]});return this._suppressionRules.some((e=>{var t=e.values.map((e=>{var t,i=ou[e.operator],n=li(e.value)?e.value:[e.value],s=null!==(t=r[e.key])&&void 0!==t?t:[];return n.length>0&&i(n,s)}));return"OR"===e.type?t.some(Boolean):t.every(Boolean)}))}}var Vp="https?://(.*)",Wp=["gclid","gclsrc","dclid","gbraid","wbraid","fbclid","msclkid","twclid","li_fat_id","igshid","ttclid","rdt_cid","epik","qclid","sccid","irclid","_kx"],Up=Fi(["utm_source","utm_medium","utm_campaign","utm_content","utm_term","gad_source","mc_cid"],Wp),Zp="<masked>",zp=["li_fat_id"];function Gp(e,t,r){if(!Ur)return{};var i,n=t?Fi([],Wp,r||[]):[],s=jp(zi(Ur.URL,n,Zp),e),o=(i={},Ri(zp,(function(e){var t=Sh._get(e);i[e]=t||null})),i);return Ai(o,s)}function jp(e,t){var r=Up.concat(t||[]),i={};return Ri(r,(function(t){var r=Zi(e,t);i[t]=r||null})),i}function Yp(e){var t=function(e){return e?0===e.search(Vp+"google.([^/?]*)")?"google":0===e.search(Vp+"bing.com")?"bing":0===e.search(Vp+"yahoo.com")?"yahoo":0===e.search(Vp+"duckduckgo.com")?"duckduckgo":null:null}(e),r="yahoo"!=t?"q":"p",i={};if(!vi(t)){i.$search_engine=t;var n=Ur?Zi(Ur.referrer,r):"";n.length&&(i.ph_keyword=n)}return i}function Xp(){return navigator.language||navigator.userLanguage}function Jp(){return(null==Ur?void 0:Ur.referrer)||"$direct"}function Kp(e,t){var r=e?Fi([],Wp,t||[]):[],i=null==Zr?void 0:Zr.href.substring(0,1e3);return{r:Jp().substring(0,1e3),u:i?zi(i,r,Zp):void 0}}function Qp(e){var t,{r:r,u:i}=e,n={$referrer:r,$referring_domain:null==r?void 0:"$direct"==r?"$direct":null==(t=Wi(r))?void 0:t.host};if(i){n.$current_url=i;var s=Wi(i);n.$host=null==s?void 0:s.host,n.$pathname=null==s?void 0:s.pathname;var o=jp(i);Ai(n,o)}if(r){var a=Yp(r);Ai(n,a)}return n}function e_(){try{return Intl.DateTimeFormat().resolvedOptions().timeZone}catch(e){return}}function t_(){try{return(new Date).getTimezoneOffset()}catch(e){return}}function r_(e,t){if(!Yr)return{};var r,i=e?Fi([],Wp,t||[]):[],[n,s]=function(e){for(var t=0;t<cl.length;t++){var[r,i]=cl[t],n=r.exec(e),s=n&&(ui(i)?i(n,e):i);if(s)return s}return["",""]}(Yr);return Ai(Li({$os:n,$os_version:s,$browser:al(Yr,navigator.vendor),$device:dl(Yr),$device_type:hl(Yr),$timezone:e_(),$timezone_offset:t_()}),{$current_url:zi(null==Zr?void 0:Zr.href,i,Zp),$host:null==Zr?void 0:Zr.host,$pathname:null==Zr?void 0:Zr.pathname,$raw_user_agent:Yr.length>1e3?Yr.substring(0,997)+"...":Yr,$browser_version:ul(Yr,navigator.vendor),$browser_language:Xp(),$browser_language_prefix:(r=Xp(),"string"==typeof r?r.split("-")[0]:void 0),$screen_height:null==Br?void 0:Br.screen.height,$screen_width:null==Br?void 0:Br.screen.width,$viewport_height:null==Br?void 0:Br.innerHeight,$viewport_width:null==Br?void 0:Br.innerWidth,$lib:"web",$lib_version:Ii.LIB_VERSION,$insert_id:Math.random().toString(36).substring(2,10)+Math.random().toString(36).substring(2,10),$time:Date.now()/1e3})}var i_=xi("[FeatureFlags]"),n_="$active_feature_flags",s_="$override_feature_flags",o_="$feature_flag_payloads",a_="$override_feature_flag_payloads",l_="$feature_flag_request_id",u_=e=>{var t={};for(var[r,i]of Ni(e||{}))i&&(t[r]=i);return t},c_=e=>{var t=e.flags;return t?(e.featureFlags=Object.fromEntries(Object.keys(t).map((e=>{var r;return[e,null!==(r=t[e].variant)&&void 0!==r?r:t[e].enabled]}))),e.featureFlagPayloads=Object.fromEntries(Object.keys(t).filter((e=>t[e].enabled)).filter((e=>{var r;return null==(r=t[e].metadata)?void 0:r.payload})).map((e=>{var r;return[e,null==(r=t[e].metadata)?void 0:r.payload]})))):i_.warn("Using an older version of the feature flags endpoint. Please upgrade your PostHog server to the latest version"),e},d_=function(e){return e.FeatureFlags="feature_flags",e.Recordings="recordings",e}({});var h_=new Set(["7c6f7b45","66c1f69c","2727f65a","f3287528","8cc9a311","eb9f671b","c0e1c6f9","057989ec","723f4019","7b102104","563359d3","bad973ea","f6f2c4f4","59454a61","89ad1076","4edd0da1","26c52e72","a970bd2e","89cf4454","16e2b4e7","fba0e7b6","301c8488","bc65d69e","fe66a3c5","37926ca6","52a196df","d32a7577","42c4c9ef","6883bd5a","04809ff7","e59430a8","61be3dd8","7fa5500b","bf027177","8cfdba9b","96f6df5f","569798e9","0ebc61a5","1b5d7b92","17ebb0a4","f97ea965","85cc817b","3044dfc1","0c3fe5c3","b1f95fa3","8a6342e8","72365c68","12d34ad9","733853ec","3beeb69a","0645bb64","32de7f98","5dcbee21","3fe85053","ad960278","9466e5dd","7ca97b2d","2ee2a65c","28fde5f2","85c52f49","0ad823f4","f11b6cc9","aacf8af9","ab3e62b3","3a85ff15","8a67d3c4","f5e91ef1","4b873698","c5dae949","5b643d76","9599c892","34377448","2189e408","3be9ad53","1a14ce7c","2a164ded","8d53ea86","53bdb37d","bfc3f590","8df38ede","bdb81e49","38fde5c0","8d707e6d","73cbc496","f9d8a5ef","d3a9f8c4","a980d8cd","5bcfe086","e4818f68","4f11fb39","a13c6ae3","150c7fbb","98f3d658","f84f7377","1924dd9c","1f6b63b3","24748755","7c0f717c","8a87f11b","49f57f22","3c9e9234","3772f65b","dff631b6","cd609d40","f853c7f7","952db5ee","c5aa8a79","2d21b6fd","79b7164c","4110e26c","a7d3b43f","84e1b8f6","75cc0998","07f78e33","10ca9b1a","ce441b18","01eb8256","c0ac4b67","8e8e5216","db7943dd","fa133a95","498a4508","21bbda67","7dbfed69","be3ec24c","fc80b8e2"]);class p_{constructor(e){this._override_warning=!1,this._hasLoadedFlags=!1,this._requestInFlight=!1,this._reloadingDisabled=!1,this._additionalReloadRequested=!1,this._flagsCalled=!1,this._flagsLoadedFromRemote=!1,this._instance=e,this.featureFlagEventHandlers=[]}flags(){if(this._instance.config.__preview_remote_config)this._flagsCalled=!0;else{var e=!this._reloadDebouncer&&(this._instance.config.advanced_disable_feature_flags||this._instance.config.advanced_disable_feature_flags_on_first_load);this._callFlagsEndpoint({disableFlags:e})}}get hasLoadedFlags(){return this._hasLoadedFlags}getFlags(){return Object.keys(this.getFlagVariants())}getFlagsWithDetails(){var e=this._instance.get_property(Sn),t=this._instance.get_property(s_),i=this._instance.get_property(a_);if(!i&&!t)return e||{};var n=Ai({},e||{}),s=[...new Set([...Object.keys(i||{}),...Object.keys(t||{})])];for(var o of s){var a,l,u=n[o],c=null==t?void 0:t[o],d=_i(c)?null!==(a=null==u?void 0:u.enabled)&&void 0!==a&&a:!!c,h=_i(c)?u.variant:"string"==typeof c?c:void 0,p=null==i?void 0:i[o],_=r({},u,{enabled:d,variant:d?null!=h?h:null==u?void 0:u.variant:void 0});if(d!==(null==u?void 0:u.enabled)&&(_.original_enabled=null==u?void 0:u.enabled),h!==(null==u?void 0:u.variant)&&(_.original_variant=null==u?void 0:u.variant),p)_.metadata=r({},null==u?void 0:u.metadata,{payload:p,original_payload:null==u||null==(l=u.metadata)?void 0:l.payload});n[o]=_}return this._override_warning||(i_.warn(" Overriding feature flag details!",{flagDetails:e,overriddenPayloads:i,finalDetails:n}),this._override_warning=!0),n}getFlagVariants(){var e=this._instance.get_property(yn),t=this._instance.get_property(s_);if(!t)return e||{};for(var r=Ai({},e),i=Object.keys(t),n=0;n<i.length;n++)r[i[n]]=t[i[n]];return this._override_warning||(i_.warn(" Overriding feature flags!",{enabledFlags:e,overriddenFlags:t,finalFlags:r}),this._override_warning=!0),r}getFlagPayloads(){var e=this._instance.get_property(o_),t=this._instance.get_property(a_);if(!t)return e||{};for(var r=Ai({},e||{}),i=Object.keys(t),n=0;n<i.length;n++)r[i[n]]=t[i[n]];return this._override_warning||(i_.warn(" Overriding feature flag payloads!",{flagPayloads:e,overriddenPayloads:t,finalPayloads:r}),this._override_warning=!0),r}reloadFeatureFlags(){this._reloadingDisabled||this._instance.config.advanced_disable_feature_flags||this._reloadDebouncer||(this._reloadDebouncer=setTimeout((()=>{this._callFlagsEndpoint()}),5))}_clearDebouncer(){clearTimeout(this._reloadDebouncer),this._reloadDebouncer=void 0}ensureFlagsLoaded(){this._hasLoadedFlags||this._requestInFlight||this._reloadDebouncer||this.reloadFeatureFlags()}setAnonymousDistinctId(e){this.$anon_distinct_id=e}setReloadingPaused(e){this._reloadingDisabled=e}_callFlagsEndpoint(e){var t;if(this._clearDebouncer(),!this._instance._shouldDisableFlags())if(this._requestInFlight)this._additionalReloadRequested=!0;else{var i={token:this._instance.config.token,distinct_id:this._instance.get_distinct_id(),groups:this._instance.getGroups(),$anon_distinct_id:this.$anon_distinct_id,person_properties:r({},(null==(t=this._instance.persistence)?void 0:t.get_initial_props())||{},this._instance.get_property(wn)||{}),group_properties:this._instance.get_property(Cn)};(null!=e&&e.disableFlags||this._instance.config.advanced_disable_feature_flags)&&(i.disable_flags=!0);var n=this._instance.config.__preview_flags_v2&&this._instance.config.__preview_remote_config,s=function(e){var t=function(e){for(var t=2166136261,r=0;r<e.length;r++)t^=e.charCodeAt(r),t+=(t<<1)+(t<<4)+(t<<7)+(t<<8)+(t<<24);return("00000000"+(t>>>0).toString(16)).slice(-8)}(e);return null==h_?void 0:h_.has(t)}(this._instance.config.token)?"/decide?v=4":n?"/flags/?v=2":"/flags/?v=2&config=true",o=this._instance.config.advanced_only_evaluate_survey_feature_flags?"&only_evaluate_survey_feature_flags=true":"",a=this._instance.requestRouter.endpointFor("api",s+o);n&&(i.timezone=e_()),this._requestInFlight=!0,this._instance._send_request({method:"POST",url:a,data:i,compression:this._instance.config.disable_compression?void 0:Qr.Base64,timeout:this._instance.config.feature_flag_request_timeout_ms,callback:e=>{var t,r,n=!0;(200===e.statusCode&&(this._additionalReloadRequested||(this.$anon_distinct_id=void 0),n=!1),this._requestInFlight=!1,this._flagsCalled)||(this._flagsCalled=!0,this._instance._onRemoteConfig(null!==(r=e.json)&&void 0!==r?r:{}));if(!i.disable_flags||this._additionalReloadRequested)if(this._flagsLoadedFromRemote=!n,e.json&&null!=(t=e.json.quotaLimited)&&t.includes(d_.FeatureFlags))i_.warn("You have hit your feature flags quota limit, and will not be able to load feature flags until the quota is reset.  Please visit https://posthog.com/docs/billing/limits-alerts to learn more.");else{var s;if(!i.disable_flags)this.receivedFeatureFlags(null!==(s=e.json)&&void 0!==s?s:{},n);this._additionalReloadRequested&&(this._additionalReloadRequested=!1,this._callFlagsEndpoint())}}})}}getFeatureFlag(e,t){if(void 0===t&&(t={}),this._hasLoadedFlags||this.getFlags()&&this.getFlags().length>0){var r=this.getFlagVariants()[e],i=""+r,n=this._instance.get_property(l_)||void 0,s=this._instance.get_property(En)||{};if((t.send_event||!("send_event"in t))&&(!(e in s)||!s[e].includes(i))){var o,a,l,u,c,d,h,p,_;li(s[e])?s[e].push(i):s[e]=[i],null==(o=this._instance.persistence)||o.register({[En]:s});var g=this.getFeatureFlagDetails(e),f={$feature_flag:e,$feature_flag_response:r,$feature_flag_payload:this.getFeatureFlagPayload(e)||null,$feature_flag_request_id:n,$feature_flag_bootstrapped_response:(null==(a=this._instance.config.bootstrap)||null==(a=a.featureFlags)?void 0:a[e])||null,$feature_flag_bootstrapped_payload:(null==(l=this._instance.config.bootstrap)||null==(l=l.featureFlagPayloads)?void 0:l[e])||null,$used_bootstrap_value:!this._flagsLoadedFromRemote};_i(null==g||null==(u=g.metadata)?void 0:u.version)||(f.$feature_flag_version=g.metadata.version);var v,m=null!==(c=null==g||null==(d=g.reason)?void 0:d.description)&&void 0!==c?c:null==g||null==(h=g.reason)?void 0:h.code;if(m&&(f.$feature_flag_reason=m),null!=g&&null!=(p=g.metadata)&&p.id&&(f.$feature_flag_id=g.metadata.id),_i(null==g?void 0:g.original_variant)&&_i(null==g?void 0:g.original_enabled)||(f.$feature_flag_original_response=_i(g.original_variant)?g.original_enabled:g.original_variant),null!=g&&null!=(_=g.metadata)&&_.original_payload)f.$feature_flag_original_payload=null==g||null==(v=g.metadata)?void 0:v.original_payload;this._instance.capture("$feature_flag_called",f)}return r}i_.warn('getFeatureFlag for key "'+e+"\" failed. Feature flags didn't load in time.")}getFeatureFlagDetails(e){return this.getFlagsWithDetails()[e]}getFeatureFlagPayload(e){return this.getFlagPayloads()[e]}getRemoteConfigPayload(e,t){var r=this._instance.config.token;this._instance._send_request({method:"POST",url:this._instance.requestRouter.endpointFor("api","/flags/?v=2&config=true"),data:{distinct_id:this._instance.get_distinct_id(),token:r},compression:this._instance.config.disable_compression?void 0:Qr.Base64,timeout:this._instance.config.feature_flag_request_timeout_ms,callback:r=>{var i,n=null==(i=r.json)?void 0:i.featureFlagPayloads;t((null==n?void 0:n[e])||void 0)}})}isFeatureEnabled(e,t){if(void 0===t&&(t={}),this._hasLoadedFlags||this.getFlags()&&this.getFlags().length>0)return!!this.getFeatureFlag(e,t);i_.warn('isFeatureEnabled for key "'+e+"\" failed. Feature flags didn't load in time.")}addFeatureFlagsHandler(e){this.featureFlagEventHandlers.push(e)}removeFeatureFlagsHandler(e){this.featureFlagEventHandlers=this.featureFlagEventHandlers.filter((t=>t!==e))}receivedFeatureFlags(e,t){if(this._instance.persistence){this._hasLoadedFlags=!0;var i=this.getFlagVariants(),n=this.getFlagPayloads(),s=this.getFlagsWithDetails();!function(e,t,i,n,s){void 0===i&&(i={}),void 0===n&&(n={}),void 0===s&&(s={});var o=c_(e),a=o.flags,l=o.featureFlags,u=o.featureFlagPayloads;if(l){var c=e.requestId;if(li(l)){i_.warn("v1 of the feature flags endpoint is deprecated. Please use the latest version.");var d={};if(l)for(var h=0;h<l.length;h++)d[l[h]]=!0;t&&t.register({[n_]:l,[yn]:d})}else{var p=l,_=u,g=a;e.errorsWhileComputingFlags&&(p=r({},i,p),_=r({},n,_),g=r({},s,g)),t&&t.register(r({[n_]:Object.keys(u_(p)),[yn]:p||{},[o_]:_||{},[Sn]:g||{}},c?{[l_]:c}:{}))}}}(e,this._instance.persistence,i,n,s),this._fireFeatureFlagsCallbacks(t)}}override(e,t){void 0===t&&(t=!1),i_.warn("override is deprecated. Please use overrideFeatureFlags instead."),this.overrideFeatureFlags({flags:e,suppressWarning:t})}overrideFeatureFlags(e){if(!this._instance.__loaded||!this._instance.persistence)return i_.uninitializedWarning("posthog.featureFlags.overrideFeatureFlags");if(!1===e)return this._instance.persistence.unregister(s_),this._instance.persistence.unregister(a_),void this._fireFeatureFlagsCallbacks();if(e&&"object"==typeof e&&("flags"in e||"payloads"in e)){var t,r=e;if(this._override_warning=Boolean(null!==(t=r.suppressWarning)&&void 0!==t&&t),"flags"in r)if(!1===r.flags)this._instance.persistence.unregister(s_);else if(r.flags)if(li(r.flags)){for(var i={},n=0;n<r.flags.length;n++)i[r.flags[n]]=!0;this._instance.persistence.register({[s_]:i})}else this._instance.persistence.register({[s_]:r.flags});return"payloads"in r&&(!1===r.payloads?this._instance.persistence.unregister(a_):r.payloads&&this._instance.persistence.register({[a_]:r.payloads})),void this._fireFeatureFlagsCallbacks()}this._fireFeatureFlagsCallbacks()}onFeatureFlags(e){if(this.addFeatureFlagsHandler(e),this._hasLoadedFlags){var{flags:t,flagVariants:r}=this._prepareFeatureFlagsForCallbacks();e(t,r)}return()=>this.removeFeatureFlagsHandler(e)}updateEarlyAccessFeatureEnrollment(e,t){var i,n=(this._instance.get_property(bn)||[]).find((t=>t.flagKey===e)),s={["$feature_enrollment/"+e]:t},o={$feature_flag:e,$feature_enrollment:t,$set:s};n&&(o.$early_access_feature_name=n.name),this._instance.capture("$feature_enrollment_update",o),this.setPersonPropertiesForFlags(s,!1);var a=r({},this.getFlagVariants(),{[e]:t});null==(i=this._instance.persistence)||i.register({[n_]:Object.keys(u_(a)),[yn]:a}),this._fireFeatureFlagsCallbacks()}getEarlyAccessFeatures(e,t,r){void 0===t&&(t=!1);var i=this._instance.get_property(bn),n=r?"&"+r.map((e=>"stage="+e)).join("&"):"";if(i&&!t)return e(i);this._instance._send_request({url:this._instance.requestRouter.endpointFor("api","/api/early_access_features/?token="+this._instance.config.token+n),method:"GET",callback:t=>{var r;if(t.json){var i=t.json.earlyAccessFeatures;return null==(r=this._instance.persistence)||r.register({[bn]:i}),e(i)}}})}_prepareFeatureFlagsForCallbacks(){var e=this.getFlags(),t=this.getFlagVariants();return{flags:e.filter((e=>t[e])),flagVariants:Object.keys(t).filter((e=>t[e])).reduce(((e,r)=>(e[r]=t[r],e)),{})}}_fireFeatureFlagsCallbacks(e){var{flags:t,flagVariants:r}=this._prepareFeatureFlagsForCallbacks();this.featureFlagEventHandlers.forEach((i=>i(t,r,{errorsLoading:e})))}setPersonPropertiesForFlags(e,t){void 0===t&&(t=!0);var i=this._instance.get_property(wn)||{};this._instance.register({[wn]:r({},i,e)}),t&&this._instance.reloadFeatureFlags()}resetPersonPropertiesForFlags(){this._instance.unregister(wn)}setGroupPropertiesForFlags(e,t){void 0===t&&(t=!0);var i=this._instance.get_property(Cn)||{};0!==Object.keys(i).length&&Object.keys(i).forEach((t=>{i[t]=r({},i[t],e[t]),delete e[t]})),this._instance.register({[Cn]:r({},i,e)}),t&&this._instance.reloadFeatureFlags()}resetGroupPropertiesForFlags(e){if(e){var t=this._instance.get_property(Cn)||{};this._instance.register({[Cn]:r({},t,{[e]:{}})})}else this._instance.unregister(Cn)}}var __=["cookie","localstorage","localstorage+cookie","sessionstorage","memory"];class g_{constructor(e){this._config=e,this.props={},this._campaign_params_saved=!1,this._name=(e=>{var t="";return e.token&&(t=e.token.replace(/\+/g,"PL").replace(/\//g,"SL").replace(/=/g,"EQ")),e.persistence_name?"ph_"+e.persistence_name:"ph_"+t+"_posthog"})(e),this._storage=this._buildStorage(e),this.load(),e.debug&&Ei.info("Persistence loaded",e.persistence,r({},this.props)),this.update_config(e,e),this.save()}_buildStorage(e){-1===__.indexOf(e.persistence.toLowerCase())&&(Ei.critical("Unknown persistence type "+e.persistence+"; falling back to localStorage+cookie"),e.persistence="localStorage+cookie");var t=e.persistence.toLowerCase();return"localstorage"===t&&Ch._is_supported()?Ch:"localstorage+cookie"===t&&kh._is_supported()?kh:"sessionstorage"===t&&Mh._is_supported()?Mh:"memory"===t?xh:"cookie"===t?Sh:kh._is_supported()?kh:Sh}properties(){var e={};return Ri(this.props,(function(t,r){if(r===yn&&hi(t))for(var i=Object.keys(t),n=0;n<i.length;n++)e["$feature/"+i[n]]=t[i[n]];else o=r,a=!1,(vi(s=Ln)?a:Vr&&s.indexOf===Vr?-1!=s.indexOf(o):(Ri(s,(function(e){if(a||(a=e===o))return Ti})),a))||(e[r]=t);var s,o,a})),e}load(){if(!this._disabled){var e=this._storage._parse(this._name);e&&(this.props=Ai({},e))}}save(){this._disabled||this._storage._set(this._name,this.props,this._expire_days,this._cross_subdomain,this._secure,this._config.debug)}remove(){this._storage._remove(this._name,!1),this._storage._remove(this._name,!0)}clear(){this.remove(),this.props={}}register_once(e,t,r){if(hi(e)){_i(t)&&(t="None"),this._expire_days=_i(r)?this._default_expiry:r;var i=!1;if(Ri(e,((e,r)=>{this.props.hasOwnProperty(r)&&this.props[r]!==t||(this.props[r]=e,i=!0)})),i)return this.save(),!0}return!1}register(e,t){if(hi(e)){this._expire_days=_i(t)?this._default_expiry:t;var r=!1;if(Ri(e,((t,i)=>{e.hasOwnProperty(i)&&this.props[i]!==t&&(this.props[i]=t,r=!0)})),r)return this.save(),!0}return!1}unregister(e){e in this.props&&(delete this.props[e],this.save())}update_campaign_params(){if(!this._campaign_params_saved){var e=Gp(this._config.custom_campaign_params,this._config.mask_personal_data_properties,this._config.custom_personal_data_properties);pi(Li(e))||this.register(e),this._campaign_params_saved=!0}}update_search_keyword(){var e;this.register((e=null==Ur?void 0:Ur.referrer)?Yp(e):{})}update_referrer_info(){var e;this.register_once({$referrer:Jp(),$referring_domain:null!=Ur&&Ur.referrer&&(null==(e=Wi(Ur.referrer))?void 0:e.host)||"$direct"},void 0)}set_initial_person_info(){this.props[Rn]||this.props[An]||this.register_once({[Fn]:Kp(this._config.mask_personal_data_properties,this._config.custom_personal_data_properties)},void 0)}get_initial_props(){var e={};Ri([An,Rn],(t=>{var r=this.props[t];r&&Ri(r,(function(t,r){e["$initial_"+ii(r)]=t}))}));var t,r,i=this.props[Fn];if(i){var n=(t=Qp(i),r={},Ri(t,(function(e,t){r["$initial_"+ii(t)]=e})),r);Ai(e,n)}return e}safe_merge(e){return Ri(this.props,(function(t,r){r in e||(e[r]=t)})),e}update_config(e,t){if(this._default_expiry=this._expire_days=e.cookie_expiration,this.set_disabled(e.disable_persistence),this.set_cross_subdomain(e.cross_subdomain_cookie),this.set_secure(e.secure_cookie),e.persistence!==t.persistence){var r=this._buildStorage(e),i=this.props;this.clear(),this._storage=r,this.props=i,this.save()}}set_disabled(e){this._disabled=e,this._disabled?this.remove():this.save()}set_cross_subdomain(e){e!==this._cross_subdomain&&(this._cross_subdomain=e,this.remove(),this.save())}set_secure(e){e!==this._secure&&(this._secure=e,this.remove(),this.save())}set_event_timer(e,t){var r=this.props[Ki]||{};r[e]=t,this.props[Ki]=r,this.save()}remove_event_timer(e){var t=(this.props[Ki]||{})[e];return _i(t)||(delete this.props[Ki][e],this.save()),t}get_property(e){return this.props[e]}set_property(e,t){this.props[e]=t,this.save()}}class f_{constructor(){this._events={},this._events={}}on(e,t){return this._events[e]||(this._events[e]=[]),this._events[e].push(t),()=>{this._events[e]=this._events[e].filter((e=>e!==t))}}emit(e,t){for(var r of this._events[e]||[])r(t);for(var i of this._events["*"]||[])i(e,t)}}class v_{constructor(e){this._debugEventEmitter=new f_,this._checkStep=(e,t)=>this._checkStepEvent(e,t)&&this._checkStepUrl(e,t)&&this._checkStepElement(e,t),this._checkStepEvent=(e,t)=>null==t||!t.event||(null==e?void 0:e.event)===(null==t?void 0:t.event),this._instance=e,this._actionEvents=new Set,this._actionRegistry=new Set}init(){var e;if(!_i(null==(e=this._instance)?void 0:e._addCaptureHook)){var t;null==(t=this._instance)||t._addCaptureHook(((e,t)=>{this.on(e,t)}))}}register(e){var t,r;if(!_i(null==(t=this._instance)?void 0:t._addCaptureHook)&&(e.forEach((e=>{var t,r;null==(t=this._actionRegistry)||t.add(e),null==(r=e.steps)||r.forEach((e=>{var t;null==(t=this._actionEvents)||t.add((null==e?void 0:e.event)||"")}))})),null!=(r=this._instance)&&r.autocapture)){var i,n=new Set;e.forEach((e=>{var t;null==(t=e.steps)||t.forEach((e=>{null!=e&&e.selector&&n.add(null==e?void 0:e.selector)}))})),null==(i=this._instance)||i.autocapture.setElementSelectors(n)}}on(e,t){var r;null!=t&&0!=e.length&&(this._actionEvents.has(e)||this._actionEvents.has(null==t?void 0:t.event))&&this._actionRegistry&&(null==(r=this._actionRegistry)?void 0:r.size)>0&&this._actionRegistry.forEach((e=>{this._checkAction(t,e)&&this._debugEventEmitter.emit("actionCaptured",e.name)}))}_addActionHook(e){this.onAction("actionCaptured",(t=>e(t)))}_checkAction(e,t){if(null==(null==t?void 0:t.steps))return!1;for(var r of t.steps)if(this._checkStep(e,r))return!0;return!1}onAction(e,t){return this._debugEventEmitter.on(e,t)}_checkStepUrl(e,t){if(null!=t&&t.url){var r,i=null==e||null==(r=e.properties)?void 0:r.$current_url;if(!i||"string"!=typeof i)return!1;if(!v_._matchString(i,null==t?void 0:t.url,(null==t?void 0:t.url_matching)||"contains"))return!1}return!0}static _matchString(e,t,r){switch(r){case"regex":return!!Br&&nu(e,t);case"exact":return t===e;case"contains":var i=v_._escapeStringRegexp(t).replace(/_/g,".").replace(/%/g,".*");return nu(e,i);default:return!1}}static _escapeStringRegexp(e){return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}_checkStepElement(e,t){if((null!=t&&t.href||null!=t&&t.tag_name||null!=t&&t.text)&&!this._getElementsList(e).some((e=>!(null!=t&&t.href&&!v_._matchString(e.href||"",null==t?void 0:t.href,(null==t?void 0:t.href_matching)||"exact"))&&((null==t||!t.tag_name||e.tag_name===(null==t?void 0:t.tag_name))&&!(null!=t&&t.text&&!v_._matchString(e.text||"",null==t?void 0:t.text,(null==t?void 0:t.text_matching)||"exact")&&!v_._matchString(e.$el_text||"",null==t?void 0:t.text,(null==t?void 0:t.text_matching)||"exact"))))))return!1;if(null!=t&&t.selector){var r,i=null==e||null==(r=e.properties)?void 0:r.$element_selectors;if(!i)return!1;if(!i.includes(null==t?void 0:t.selector))return!1}return!0}_getElementsList(e){return null==(null==e?void 0:e.properties.$elements)?[]:null==e?void 0:e.properties.$elements}}class m_{constructor(e){this._instance=e,this._eventToSurveys=new Map,this._actionToSurveys=new Map}register(e){var t;_i(null==(t=this._instance)?void 0:t._addCaptureHook)||(this._setupEventBasedSurveys(e),this._setupActionBasedSurveys(e))}_setupActionBasedSurveys(e){var t=e.filter((e=>{var t,r;return(null==(t=e.conditions)?void 0:t.actions)&&(null==(r=e.conditions)||null==(r=r.actions)||null==(r=r.values)?void 0:r.length)>0}));if(0!==t.length){if(null==this._actionMatcher){this._actionMatcher=new v_(this._instance),this._actionMatcher.init();this._actionMatcher._addActionHook((e=>{this.onAction(e)}))}t.forEach((e=>{var t,r,i,n,s;e.conditions&&null!=(t=e.conditions)&&t.actions&&null!=(r=e.conditions)&&null!=(r=r.actions)&&r.values&&(null==(i=e.conditions)||null==(i=i.actions)||null==(i=i.values)?void 0:i.length)>0&&(null==(n=this._actionMatcher)||n.register(e.conditions.actions.values),null==(s=e.conditions)||null==(s=s.actions)||null==(s=s.values)||s.forEach((t=>{if(t&&t.name){var r=this._actionToSurveys.get(t.name);r&&r.push(e.id),this._actionToSurveys.set(t.name,r||[e.id])}})))}))}}_setupEventBasedSurveys(e){var t;if(0!==e.filter((e=>{var t,r;return(null==(t=e.conditions)?void 0:t.events)&&(null==(r=e.conditions)||null==(r=r.events)||null==(r=r.values)?void 0:r.length)>0})).length){null==(t=this._instance)||t._addCaptureHook(((e,t)=>{this.onEvent(e,t)})),e.forEach((e=>{var t;null==(t=e.conditions)||null==(t=t.events)||null==(t=t.values)||t.forEach((t=>{if(t&&t.name){var r=this._eventToSurveys.get(t.name);r&&r.push(e.id),this._eventToSurveys.set(t.name,r||[e.id])}}))}))}}onEvent(e,t){var r,i=(null==(r=this._instance)||null==(r=r.persistence)?void 0:r.props[kn])||[];if("survey shown"===e&&t&&i.length>0){var n;ta.info("survey event matched, removing survey from activated surveys",{event:e,eventPayload:t,existingActivatedSurveys:i});var s=null==t||null==(n=t.properties)?void 0:n.$survey_id;if(s){var o=i.indexOf(s);o>=0&&(i.splice(o,1),this._updateActivatedSurveys(i))}}else this._eventToSurveys.has(e)&&(ta.info("survey event matched, updating activated surveys",{event:e,surveys:this._eventToSurveys.get(e)}),this._updateActivatedSurveys(i.concat(this._eventToSurveys.get(e)||[])))}onAction(e){var t,r=(null==(t=this._instance)||null==(t=t.persistence)?void 0:t.props[kn])||[];this._actionToSurveys.has(e)&&this._updateActivatedSurveys(r.concat(this._actionToSurveys.get(e)||[]))}_updateActivatedSurveys(e){var t;null==(t=this._instance)||null==(t=t.persistence)||t.register({[kn]:[...new Set(e)]})}getSurveys(){var e,t=null==(e=this._instance)||null==(e=e.persistence)?void 0:e.props[kn];return t||[]}getEventToSurveys(){return this._eventToSurveys}_getActionMatcher(){return this._actionMatcher}}class y_{constructor(e){this._surveyManager=null,this._isFetchingSurveys=!1,this._isInitializingSurveys=!1,this._surveyCallbacks=[],this._instance=e,this._surveyEventReceiver=null}onRemoteConfig(e){var t=e.surveys;if(mi(t))return ta.warn("Flags not loaded yet. Not loading surveys.");var r=li(t);this._hasSurveys=r?t.length>0:t,ta.info("flags response received, hasSurveys: "+this._hasSurveys),this._hasSurveys&&this.loadIfEnabled()}reset(){localStorage.removeItem("lastSeenSurveyDate");for(var e=[],t=0;t<localStorage.length;t++){var r=localStorage.key(t);(null!=r&&r.startsWith(sa)||null!=r&&r.startsWith(oa))&&e.push(r)}e.forEach((e=>localStorage.removeItem(e)))}loadIfEnabled(){if(!this._surveyManager)if(this._isInitializingSurveys)ta.info("Already initializing surveys, skipping...");else if(this._instance.config.disable_surveys)ta.info("Disabled. Not loading surveys.");else if(this._hasSurveys){var e=null==Xr?void 0:Xr.__PosthogExtensions__;if(e){this._isInitializingSurveys=!0;try{var t=e.generateSurveys;if(t)return void this._completeSurveyInitialization(t);var r=e.loadExternalDependency;if(!r)return void this._handleSurveyLoadError("PostHog loadExternalDependency extension not found.");r(this._instance,"surveys",(t=>{t||!e.generateSurveys?this._handleSurveyLoadError("Could not load surveys script",t):this._completeSurveyInitialization(e.generateSurveys)}))}catch(e){throw this._handleSurveyLoadError("Error initializing surveys",e),e}finally{this._isInitializingSurveys=!1}}else ta.error("PostHog Extensions not found.")}else ta.info("No surveys to load.")}_completeSurveyInitialization(e){this._surveyManager=e(this._instance),this._surveyEventReceiver=new m_(this._instance),ta.info("Surveys loaded successfully"),this._notifySurveyCallbacks({isLoaded:!0})}_handleSurveyLoadError(e,t){ta.error(e,t),this._notifySurveyCallbacks({isLoaded:!1,error:e})}onSurveysLoaded(e){return this._surveyCallbacks.push(e),this._surveyManager&&this._notifySurveyCallbacks({isLoaded:!0}),()=>{this._surveyCallbacks=this._surveyCallbacks.filter((t=>t!==e))}}getSurveys(e,t){if(void 0===t&&(t=!1),this._instance.config.disable_surveys)return ta.info("Disabled. Not loading surveys."),e([]);var r=this._instance.get_property(In);if(r&&!t)return e(r,{isLoaded:!0});if(this._isFetchingSurveys)return e([],{isLoaded:!1,error:"Surveys are already being loaded"});try{this._isFetchingSurveys=!0,this._instance._send_request({url:this._instance.requestRouter.endpointFor("api","/api/surveys/?token="+this._instance.config.token),method:"GET",timeout:this._instance.config.surveys_request_timeout_ms,callback:t=>{var r;this._isFetchingSurveys=!1;var i=t.statusCode;if(200!==i||!t.json){var n="Surveys API could not be loaded, status: "+i;return ta.error(n),e([],{isLoaded:!1,error:n})}var s,o=t.json.surveys||[],a=o.filter((e=>ra(e)&&(ia(e)||na(e))));a.length>0&&(null==(s=this._surveyEventReceiver)||s.register(a));return null==(r=this._instance.persistence)||r.register({[In]:o}),e(o,{isLoaded:!0})}})}catch(e){throw this._isFetchingSurveys=!1,e}}_notifySurveyCallbacks(e){for(var t of this._surveyCallbacks)try{e.isLoaded?this.getSurveys(t):t([],e)}catch(e){ta.error("Error in survey callback",e)}}getActiveMatchingSurveys(e,t){if(void 0===t&&(t=!1),!mi(this._surveyManager))return this._surveyManager.getActiveMatchingSurveys(e,t);ta.warn("init was not called")}_getSurveyById(e){var t=null;return this.getSurveys((r=>{var i;t=null!==(i=r.find((t=>t.id===e)))&&void 0!==i?i:null})),t}_checkSurveyEligibility(e){if(mi(this._surveyManager))return{eligible:!1,reason:"SDK is not enabled or survey functionality is not yet loaded"};var t="string"==typeof e?this._getSurveyById(e):e;return t?this._surveyManager.checkSurveyEligibility(t):{eligible:!1,reason:"Survey not found"}}canRenderSurvey(e){if(mi(this._surveyManager))return ta.warn("init was not called"),{visible:!1,disabledReason:"SDK is not enabled or survey functionality is not yet loaded"};var t=this._checkSurveyEligibility(e);return{visible:t.eligible,disabledReason:t.reason}}canRenderSurveyAsync(e,t){return mi(this._surveyManager)?(ta.warn("init was not called"),Promise.resolve({visible:!1,disabledReason:"SDK is not enabled or survey functionality is not yet loaded"})):new Promise((r=>{this.getSurveys((t=>{var i,n=null!==(i=t.find((t=>t.id===e)))&&void 0!==i?i:null;if(n){var s=this._checkSurveyEligibility(n);r({visible:s.eligible,disabledReason:s.reason})}else r({visible:!1,disabledReason:"Survey not found"})}),t)}))}renderSurvey(e,t){if(mi(this._surveyManager))ta.warn("init was not called");else{var r=this._getSurveyById(e),i=null==Ur?void 0:Ur.querySelector(t);r?i?this._surveyManager.renderSurvey(r,i):ta.warn("Survey element not found"):ta.warn("Survey not found")}}}var b_=xi("[RateLimiter]");class S_{constructor(e){var t,r;this.serverLimits={},this.lastEventRateLimited=!1,this.checkForLimiting=e=>{var t=e.text;if(t&&t.length)try{(JSON.parse(t).quota_limited||[]).forEach((e=>{b_.info((e||"events")+" is quota limited."),this.serverLimits[e]=(new Date).getTime()+6e4}))}catch(e){return void b_.warn('could not rate limit - continuing. Error: "'+(null==e?void 0:e.message)+'"',{text:t})}},this.instance=e,this.captureEventsPerSecond=(null==(t=e.config.rate_limiting)?void 0:t.events_per_second)||10,this.captureEventsBurstLimit=Math.max((null==(r=e.config.rate_limiting)?void 0:r.events_burst_limit)||10*this.captureEventsPerSecond,this.captureEventsPerSecond),this.lastEventRateLimited=this.clientRateLimitContext(!0).isRateLimited}clientRateLimitContext(e){var t,r,i;void 0===e&&(e=!1);var n=(new Date).getTime(),s=null!==(t=null==(r=this.instance.persistence)?void 0:r.get_property(Mn))&&void 0!==t?t:{tokens:this.captureEventsBurstLimit,last:n};s.tokens+=(n-s.last)/1e3*this.captureEventsPerSecond,s.last=n,s.tokens>this.captureEventsBurstLimit&&(s.tokens=this.captureEventsBurstLimit);var o=s.tokens<1;return o||e||(s.tokens=Math.max(0,s.tokens-1)),!o||this.lastEventRateLimited||e||this.instance.capture("$$client_ingestion_warning",{$$client_ingestion_warning_message:"posthog-js client rate limited. Config is set to "+this.captureEventsPerSecond+" events per second and "+this.captureEventsBurstLimit+" events burst limit."},{skip_client_rate_limiting:!0}),this.lastEventRateLimited=o,null==(i=this.instance.persistence)||i.set_property(Mn,s),{isRateLimited:o,remainingTokens:s.tokens}}isServerRateLimited(e){var t=this.serverLimits[e||"events"]||!1;return!1!==t&&(new Date).getTime()<t}}var w_=xi("[RemoteConfig]");class C_{constructor(e){this._instance=e}get remoteConfig(){var e;return null==(e=Xr._POSTHOG_REMOTE_CONFIG)||null==(e=e[this._instance.config.token])?void 0:e.config}_loadRemoteConfigJs(e){var t,r;null!=(t=Xr.__PosthogExtensions__)&&t.loadExternalDependency?null==(r=Xr.__PosthogExtensions__)||null==r.loadExternalDependency||r.loadExternalDependency(this._instance,"remote-config",(()=>e(this.remoteConfig))):(w_.error("PostHog Extensions not found. Cannot load remote config."),e())}_loadRemoteConfigJSON(e){this._instance._send_request({method:"GET",url:this._instance.requestRouter.endpointFor("assets","/array/"+this._instance.config.token+"/config"),callback:t=>{e(t.json)}})}load(){try{if(this.remoteConfig)return w_.info("Using preloaded remote config",this.remoteConfig),void this._onRemoteConfig(this.remoteConfig);if(this._instance._shouldDisableFlags())return void w_.warn("Remote config is disabled. Falling back to local config.");this._loadRemoteConfigJs((e=>{if(!e)return w_.info("No config found after loading remote JS config. Falling back to JSON."),void this._loadRemoteConfigJSON((e=>{this._onRemoteConfig(e)}));this._onRemoteConfig(e)}))}catch(e){w_.error("Error loading remote config",e)}}_onRemoteConfig(e){e?this._instance.config.__preview_remote_config?(this._instance._onRemoteConfig(e),!1!==e.hasFeatureFlags&&this._instance.featureFlags.ensureFlagsLoaded()):w_.info("__preview_remote_config is disabled. Logging config instead",e):w_.error("Failed to fetch remote config from PostHog.")}}var I_=3e3;class k_{constructor(e,t){this._isPaused=!0,this._queue=[],this._flushTimeoutMs=Lh((null==t?void 0:t.flush_interval_ms)||I_,250,5e3,"flush interval",I_),this._sendRequest=e}enqueue(e){this._queue.push(e),this._flushTimeout||this._setFlushTimeout()}unload(){this._clearFlushTimeout();var e=this._queue.length>0?this._formatQueue():{},t=Object.values(e),i=[...t.filter((e=>0===e.url.indexOf("/e"))),...t.filter((e=>0!==e.url.indexOf("/e")))];i.map((e=>{this._sendRequest(r({},e,{transport:"sendBeacon"}))}))}enable(){this._isPaused=!1,this._setFlushTimeout()}_setFlushTimeout(){var e=this;this._isPaused||(this._flushTimeout=setTimeout((()=>{if(this._clearFlushTimeout(),this._queue.length>0){var t=this._formatQueue(),r=function(){var r=t[i],n=(new Date).getTime();r.data&&li(r.data)&&Ri(r.data,(e=>{e.offset=Math.abs(e.timestamp-n),delete e.timestamp})),e._sendRequest(r)};for(var i in t)r()}}),this._flushTimeoutMs))}_clearFlushTimeout(){clearTimeout(this._flushTimeout),this._flushTimeout=void 0}_formatQueue(){var e={};return Ri(this._queue,(t=>{var i,n=t,s=(n?n.batchKey:null)||n.url;_i(e[s])&&(e[s]=r({},n,{data:[]})),null==(i=e[s].data)||i.push(n.data)})),this._queue=[],e}}var E_=["retriesPerformedSoFar"];class x_{constructor(e){this._isPolling=!1,this._pollIntervalMs=3e3,this._queue=[],this._instance=e,this._queue=[],this._areWeOnline=!0,!_i(Br)&&"onLine"in Br.navigator&&(this._areWeOnline=Br.navigator.onLine,$i(Br,"online",(()=>{this._areWeOnline=!0,this._flush()})),$i(Br,"offline",(()=>{this._areWeOnline=!1})))}get length(){return this._queue.length}retriableRequest(e){var{retriesPerformedSoFar:t}=e,n=i(e,E_);yi(t)&&t>0&&(n.url=eu(n.url,{retry_count:t})),this._instance._send_request(r({},n,{callback:e=>{200!==e.statusCode&&(e.statusCode<400||e.statusCode>=500)&&(null!=t?t:0)<10?this._enqueue(r({retriesPerformedSoFar:t},n)):null==n.callback||n.callback(e)}}))}_enqueue(e){var t=e.retriesPerformedSoFar||0;e.retriesPerformedSoFar=t+1;var r=function(e){var t=3e3*Math.pow(2,e),r=t/2,i=Math.min(18e5,t),n=(Math.random()-.5)*(i-r);return Math.ceil(i+n)}(t),i=Date.now()+r;this._queue.push({retryAt:i,requestOptions:e});var n="Enqueued failed request for retry in "+r;navigator.onLine||(n+=" (Browser is offline)"),Ei.warn(n),this._isPolling||(this._isPolling=!0,this._poll())}_poll(){this._poller&&clearTimeout(this._poller),this._poller=setTimeout((()=>{this._areWeOnline&&this._queue.length>0&&this._flush(),this._poll()}),this._pollIntervalMs)}_flush(){var e=Date.now(),t=[],r=this._queue.filter((r=>r.retryAt<e||(t.push(r),!1)));if(this._queue=t,r.length>0)for(var{requestOptions:i}of r)this.retriableRequest(i)}unload(){for(var{requestOptions:e}of(this._poller&&(clearTimeout(this._poller),this._poller=void 0),this._queue))try{this._instance._send_request(r({},e,{transport:"sendBeacon"}))}catch(e){Ei.error(e)}this._queue=[]}}class T_{constructor(e){this._updateScrollData=()=>{var e,t,r,i;this._context||(this._context={});var n=this.scrollElement(),s=this.scrollY(),o=n?Math.max(0,n.scrollHeight-n.clientHeight):0,a=s+((null==n?void 0:n.clientHeight)||0),l=(null==n?void 0:n.scrollHeight)||0;this._context.lastScrollY=Math.ceil(s),this._context.maxScrollY=Math.max(s,null!==(e=this._context.maxScrollY)&&void 0!==e?e:0),this._context.maxScrollHeight=Math.max(o,null!==(t=this._context.maxScrollHeight)&&void 0!==t?t:0),this._context.lastContentY=a,this._context.maxContentY=Math.max(a,null!==(r=this._context.maxContentY)&&void 0!==r?r:0),this._context.maxContentHeight=Math.max(l,null!==(i=this._context.maxContentHeight)&&void 0!==i?i:0)},this._instance=e}getContext(){return this._context}resetContext(){var e=this._context;return setTimeout(this._updateScrollData,0),e}startMeasuringScrollPosition(){$i(Br,"scroll",this._updateScrollData,{capture:!0}),$i(Br,"scrollend",this._updateScrollData,{capture:!0}),$i(Br,"resize",this._updateScrollData)}scrollElement(){if(!this._instance.config.scroll_root_selector)return null==Br?void 0:Br.document.documentElement;var e=li(this._instance.config.scroll_root_selector)?this._instance.config.scroll_root_selector:[this._instance.config.scroll_root_selector];for(var t of e){var r=null==Br?void 0:Br.document.querySelector(t);if(r)return r}}scrollY(){if(this._instance.config.scroll_root_selector){var e=this.scrollElement();return e&&e.scrollTop||0}return Br&&(Br.scrollY||Br.pageYOffset||Br.document.documentElement.scrollTop)||0}scrollX(){if(this._instance.config.scroll_root_selector){var e=this.scrollElement();return e&&e.scrollLeft||0}return Br&&(Br.scrollX||Br.pageXOffset||Br.document.documentElement.scrollLeft)||0}}var M_=e=>Kp(null==e?void 0:e.config.mask_personal_data_properties,null==e?void 0:e.config.custom_personal_data_properties);class R_{constructor(e,t,r,i){this._onSessionIdCallback=e=>{var t=this._getStored();if(!t||t.sessionId!==e){var r={sessionId:e,props:this._sessionSourceParamGenerator(this._instance)};this._persistence.register({[Tn]:r})}},this._instance=e,this._sessionIdManager=t,this._persistence=r,this._sessionSourceParamGenerator=i||M_,this._sessionIdManager.onSessionId(this._onSessionIdCallback)}_getStored(){return this._persistence.props[Tn]}getSetOnceProps(){var e,t=null==(e=this._getStored())?void 0:e.props;return t?"r"in t?Qp(t):{$referring_domain:t.referringDomain,$pathname:t.initialPathName,utm_source:t.utm_source,utm_campaign:t.utm_campaign,utm_medium:t.utm_medium,utm_content:t.utm_content,utm_term:t.utm_term}:{}}getSessionProps(){var e={};return Ri(Li(this.getSetOnceProps()),((t,r)=>{"$current_url"===r&&(r="url"),e["$session_entry_"+ii(r)]=t})),e}}var A_=xi("[SessionId]");class F_{constructor(e,t,r){var i;if(this._sessionIdChangedHandlers=[],!e.persistence)throw new Error("SessionIdManager requires a PostHogPersistence instance");if(e.config.__preview_experimental_cookieless_mode)throw new Error("SessionIdManager cannot be used with __preview_experimental_cookieless_mode");this._config=e.config,this._persistence=e.persistence,this._windowId=void 0,this._sessionId=void 0,this._sessionStartTimestamp=null,this._sessionActivityTimestamp=null,this._sessionIdGenerator=t||_a,this._windowIdGenerator=r||_a;var n=this._config.persistence_name||this._config.token,s=this._config.session_idle_timeout_seconds||1800;if(this._sessionTimeoutMs=1e3*Lh(s,60,36e3,"session_idle_timeout_seconds",1800),e.register({$configured_session_timeout_ms:this._sessionTimeoutMs}),this._resetIdleTimer(),this._window_id_storage_key="ph_"+n+"_window_id",this._primary_window_exists_storage_key="ph_"+n+"_primary_window_exists",this._canUseSessionStorage()){var o=Mh._parse(this._window_id_storage_key),a=Mh._parse(this._primary_window_exists_storage_key);o&&!a?this._windowId=o:Mh._remove(this._window_id_storage_key),Mh._set(this._primary_window_exists_storage_key,!0)}if(null!=(i=this._config.bootstrap)&&i.sessionID)try{var l=(e=>{var t=e.replace(/-/g,"");if(32!==t.length)throw new Error("Not a valid UUID");if("7"!==t[12])throw new Error("Not a UUIDv7");return parseInt(t.substring(0,12),16)})(this._config.bootstrap.sessionID);this._setSessionId(this._config.bootstrap.sessionID,(new Date).getTime(),l)}catch(e){A_.error("Invalid sessionID in bootstrap",e)}this._listenToReloadWindow()}get sessionTimeoutMs(){return this._sessionTimeoutMs}onSessionId(e){return _i(this._sessionIdChangedHandlers)&&(this._sessionIdChangedHandlers=[]),this._sessionIdChangedHandlers.push(e),this._sessionId&&e(this._sessionId,this._windowId),()=>{this._sessionIdChangedHandlers=this._sessionIdChangedHandlers.filter((t=>t!==e))}}_canUseSessionStorage(){return"memory"!==this._config.persistence&&!this._persistence._disabled&&Mh._is_supported()}_setWindowId(e){e!==this._windowId&&(this._windowId=e,this._canUseSessionStorage()&&Mh._set(this._window_id_storage_key,e))}_getWindowId(){return this._windowId?this._windowId:this._canUseSessionStorage()?Mh._parse(this._window_id_storage_key):null}_setSessionId(e,t,r){e===this._sessionId&&t===this._sessionActivityTimestamp&&r===this._sessionStartTimestamp||(this._sessionStartTimestamp=r,this._sessionActivityTimestamp=t,this._sessionId=e,this._persistence.register({[gn]:[t,e,r]}))}_getSessionId(){if(this._sessionId&&this._sessionActivityTimestamp&&this._sessionStartTimestamp)return[this._sessionActivityTimestamp,this._sessionId,this._sessionStartTimestamp];var e=this._persistence.props[gn];return li(e)&&2===e.length&&e.push(e[0]),e||[0,null,0]}resetSessionId(){this._setSessionId(null,null,null)}_listenToReloadWindow(){$i(Br,"beforeunload",(()=>{this._canUseSessionStorage()&&Mh._remove(this._primary_window_exists_storage_key)}),{capture:!1})}checkAndGetSessionAndWindowId(e,t){if(void 0===e&&(e=!1),void 0===t&&(t=null),this._config.__preview_experimental_cookieless_mode)throw new Error("checkAndGetSessionAndWindowId should not be called in __preview_experimental_cookieless_mode");var r=t||(new Date).getTime(),[i,n,s]=this._getSessionId(),o=this._getWindowId(),a=yi(s)&&s>0&&Math.abs(r-s)>864e5,l=!1,u=!n,c=!e&&Math.abs(r-i)>this.sessionTimeoutMs;u||c||a?(n=this._sessionIdGenerator(),o=this._windowIdGenerator(),A_.info("new session ID generated",{sessionId:n,windowId:o,changeReason:{noSessionId:u,activityTimeout:c,sessionPastMaximumLength:a}}),s=r,l=!0):o||(o=this._windowIdGenerator(),l=!0);var d=0===i||!e||a?r:i,h=0===s?(new Date).getTime():s;return this._setWindowId(o),this._setSessionId(n,d,h),e||this._resetIdleTimer(),l&&this._sessionIdChangedHandlers.forEach((e=>e(n,o,l?{noSessionId:u,activityTimeout:c,sessionPastMaximumLength:a}:void 0))),{sessionId:n,windowId:o,sessionStartTimestamp:h,changeReason:l?{noSessionId:u,activityTimeout:c,sessionPastMaximumLength:a}:void 0,lastActivityTimestamp:i}}_resetIdleTimer(){clearTimeout(this._enforceIdleTimeout),this._enforceIdleTimeout=setTimeout((()=>{this.resetSessionId()}),1.1*this.sessionTimeoutMs)}}var N_=["$set_once","$set"],P_=xi("[SiteApps]");class O_{constructor(e){this._instance=e,this._bufferedInvocations=[],this.apps={}}get isEnabled(){return!!this._instance.config.opt_in_site_apps}_eventCollector(e,t){if(t){var r=this.globalsForEvent(t);this._bufferedInvocations.push(r),this._bufferedInvocations.length>1e3&&(this._bufferedInvocations=this._bufferedInvocations.slice(10))}}get siteAppLoaders(){var e;return null==(e=Xr._POSTHOG_REMOTE_CONFIG)||null==(e=e[this._instance.config.token])?void 0:e.siteApps}init(){if(this.isEnabled){var e=this._instance._addCaptureHook(this._eventCollector.bind(this));this._stopBuffering=()=>{e(),this._bufferedInvocations=[],this._stopBuffering=void 0}}}globalsForEvent(e){var t,n,s,o,a,l,u;if(!e)throw new Error("Event payload is required");var c={},d=this._instance.get_property("$groups")||[],h=this._instance.get_property("$stored_group_properties")||{};for(var[p,_]of Object.entries(h))c[p]={id:d[p],type:p,properties:_};var{$set_once:g,$set:f}=e;return{event:r({},i(e,N_),{properties:r({},e.properties,f?{$set:r({},null!==(t=null==(n=e.properties)?void 0:n.$set)&&void 0!==t?t:{},f)}:{},g?{$set_once:r({},null!==(s=null==(o=e.properties)?void 0:o.$set_once)&&void 0!==s?s:{},g)}:{}),elements_chain:null!==(a=null==(l=e.properties)?void 0:l.$elements_chain)&&void 0!==a?a:"",distinct_id:null==(u=e.properties)?void 0:u.distinct_id}),person:{properties:this._instance.get_property("$stored_person_properties")},groups:c}}setupSiteApp(e){var t=this.apps[e.id],r=()=>{var r;(!t.errored&&this._bufferedInvocations.length&&(P_.info("Processing "+this._bufferedInvocations.length+" events for site app with id "+e.id),this._bufferedInvocations.forEach((e=>null==t.processEvent?void 0:t.processEvent(e))),t.processedBuffer=!0),Object.values(this.apps).every((e=>e.processedBuffer||e.errored)))&&(null==(r=this._stopBuffering)||r.call(this))},i=!1,n=n=>{t.errored=!n,t.loaded=!0,P_.info("Site app with id "+e.id+" "+(n?"loaded":"errored")),i&&r()};try{var{processEvent:s}=e.init({posthog:this._instance,callback:e=>{n(e)}});s&&(t.processEvent=s),i=!0}catch(t){P_.error("Error while initializing PostHog app with config id "+e.id,t),n(!1)}if(i&&t.loaded)try{r()}catch(r){P_.error("Error while processing buffered events PostHog app with config id "+e.id,r),t.errored=!0}}_setupSiteApps(){var e=this.siteAppLoaders||[];for(var t of e)this.apps[t.id]={id:t.id,loaded:!1,errored:!1,processedBuffer:!1};for(var r of e)this.setupSiteApp(r)}_onCapturedEvent(e){if(0!==Object.keys(this.apps).length){var t=this.globalsForEvent(e);for(var r of Object.values(this.apps))try{null==r.processEvent||r.processEvent(t)}catch(t){P_.error("Error while processing event "+e.event+" for site app "+r.id,t)}}}onRemoteConfig(e){var t,r,i,n=this;if(null!=(t=this.siteAppLoaders)&&t.length)return this.isEnabled?(this._setupSiteApps(),void this._instance.on("eventCaptured",(e=>this._onCapturedEvent(e)))):void P_.error('PostHog site apps are disabled. Enable the "opt_in_site_apps" config to proceed.');if(null==(r=this._stopBuffering)||r.call(this),null!=(i=e.siteApps)&&i.length)if(this.isEnabled){var s=function(e){var t;Xr["__$$ph_site_app_"+e]=n._instance,null==(t=Xr.__PosthogExtensions__)||null==t.loadSiteApp||t.loadSiteApp(n._instance,a,(t=>{if(t)return P_.error("Error while initializing PostHog app with config id "+e,t)}))};for(var{id:o,url:a}of e.siteApps)s(o)}else P_.error('PostHog site apps are disabled. Enable the "opt_in_site_apps" config to proceed.')}}var L_=["amazonbot","amazonproductbot","app.hypefactors.com","applebot","archive.org_bot","awariobot","backlinksextendedbot","baiduspider","bingbot","bingpreview","chrome-lighthouse","dataforseobot","deepscan","duckduckbot","facebookexternal","facebookcatalog","http://yandex.com/bots","hubspot","ia_archiver","leikibot","linkedinbot","meta-externalagent","mj12bot","msnbot","nessus","petalbot","pinterest","prerender","rogerbot","screaming frog","sebot-wa","sitebulb","slackbot","slurp","trendictionbot","turnitin","twitterbot","vercelbot","yahoo! slurp","yandexbot","zoombot","bot.htm","bot.php","(bot;","bot/","crawler","ahrefsbot","ahrefssiteaudit","semrushbot","siteauditbot","splitsignalbot","gptbot","oai-searchbot","chatgpt-user","perplexitybot","better uptime bot","sentryuptimebot","uptimerobot","headlesschrome","cypress","google-hoteladsverifier","adsbot-google","apis-google","duplexweb-google","feedfetcher-google","google favicon","google web preview","google-read-aloud","googlebot","googleother","google-cloudvertexbot","googleweblight","mediapartners-google","storebot-google","google-inspectiontool","bytespider"],D_=function(e,t){if(!e)return!1;var r=e.toLowerCase();return L_.concat(t||[]).some((e=>{var t=e.toLowerCase();return-1!==r.indexOf(t)}))},B_=function(e,t){if(!e)return!1;var r=e.userAgent;if(r&&D_(r,t))return!0;try{var i=null==e?void 0:e.userAgentData;if(null!=i&&i.brands&&i.brands.some((e=>D_(null==e?void 0:e.brand,t))))return!0}catch(e){}return!!e.webdriver},q_=function(e){return e.US="us",e.EU="eu",e.CUSTOM="custom",e}({}),H_="i.posthog.com";class $_{constructor(e){this._regionCache={},this.instance=e}get apiHost(){var e=this.instance.config.api_host.trim().replace(/\/$/,"");return"https://app.posthog.com"===e?"https://us.i.posthog.com":e}get uiHost(){var e,t=null==(e=this.instance.config.ui_host)?void 0:e.replace(/\/$/,"");return t||(t=this.apiHost.replace("."+H_,".posthog.com")),"https://app.posthog.com"===t?"https://us.posthog.com":t}get region(){return this._regionCache[this.apiHost]||(/https:\/\/(app|us|us-assets)(\.i)?\.posthog\.com/i.test(this.apiHost)?this._regionCache[this.apiHost]=q_.US:/https:\/\/(eu|eu-assets)(\.i)?\.posthog\.com/i.test(this.apiHost)?this._regionCache[this.apiHost]=q_.EU:this._regionCache[this.apiHost]=q_.CUSTOM),this._regionCache[this.apiHost]}endpointFor(e,t){if(void 0===t&&(t=""),t&&(t="/"===t[0]?t:"/"+t),"ui"===e)return this.uiHost+t;if(this.region===q_.CUSTOM)return this.apiHost+t;var r=H_+t;switch(e){case"assets":return"https://"+this.region+"-assets."+r;case"api":return"https://"+this.region+"."+r}}}var V_={icontains:(e,t)=>!!Br&&t.href.toLowerCase().indexOf(e.toLowerCase())>-1,not_icontains:(e,t)=>!!Br&&-1===t.href.toLowerCase().indexOf(e.toLowerCase()),regex:(e,t)=>!!Br&&nu(t.href,e),not_regex:(e,t)=>!!Br&&!nu(t.href,e),exact:(e,t)=>t.href===e,is_not:(e,t)=>t.href!==e};class W_{constructor(e){var t=this;this.getWebExperimentsAndEvaluateDisplayLogic=function(e){void 0===e&&(e=!1),t.getWebExperiments((e=>{W_._logInfo("retrieved web experiments from the server"),t._flagToExperiments=new Map,e.forEach((e=>{if(e.feature_flag_key){var r;if(t._flagToExperiments)W_._logInfo("setting flag key ",e.feature_flag_key," to web experiment ",e),null==(r=t._flagToExperiments)||r.set(e.feature_flag_key,e);var i=t._instance.getFeatureFlag(e.feature_flag_key);gi(i)&&e.variants[i]&&t._applyTransforms(e.name,i,e.variants[i].transforms)}else if(e.variants)for(var n in e.variants){var s=e.variants[n];W_._matchesTestVariant(s)&&t._applyTransforms(e.name,n,s.transforms)}}))}),e)},this._instance=e,this._instance.onFeatureFlags((e=>{this.onFeatureFlags(e)}))}onFeatureFlags(e){if(this._is_bot())W_._logInfo("Refusing to render web experiment since the viewer is a likely bot");else if(!this._instance.config.disable_web_experiments){if(mi(this._flagToExperiments))return this._flagToExperiments=new Map,this.loadIfEnabled(),void this.previewWebExperiment();W_._logInfo("applying feature flags",e),e.forEach((e=>{var t;if(this._flagToExperiments&&null!=(t=this._flagToExperiments)&&t.has(e)){var r,i=this._instance.getFeatureFlag(e),n=null==(r=this._flagToExperiments)?void 0:r.get(e);i&&null!=n&&n.variants[i]&&this._applyTransforms(n.name,i,n.variants[i].transforms)}}))}}previewWebExperiment(){var e=W_.getWindowLocation();if(null!=e&&e.search){var t=Zi(null==e?void 0:e.search,"__experiment_id"),r=Zi(null==e?void 0:e.search,"__experiment_variant");t&&r&&(W_._logInfo("previewing web experiments "+t+" && "+r),this.getWebExperiments((e=>{this._showPreviewWebExperiment(parseInt(t),r,e)}),!1,!0))}}loadIfEnabled(){this._instance.config.disable_web_experiments||this.getWebExperimentsAndEvaluateDisplayLogic()}getWebExperiments(e,t,r){if(this._instance.config.disable_web_experiments&&!r)return e([]);var i=this._instance.get_property("$web_experiments");if(i&&!t)return e(i);this._instance._send_request({url:this._instance.requestRouter.endpointFor("api","/api/web_experiments/?token="+this._instance.config.token),method:"GET",callback:t=>{if(200!==t.statusCode||!t.json)return e([]);var r=t.json.experiments||[];return e(r)}})}_showPreviewWebExperiment(e,t,r){var i=r.filter((t=>t.id===e));i&&i.length>0&&(W_._logInfo("Previewing web experiment ["+i[0].name+"] with variant ["+t+"]"),this._applyTransforms(i[0].name,t,i[0].variants[t].transforms))}static _matchesTestVariant(e){return!mi(e.conditions)&&(W_._matchUrlConditions(e)&&W_._matchUTMConditions(e))}static _matchUrlConditions(e){var t;if(mi(e.conditions)||mi(null==(t=e.conditions)?void 0:t.url))return!0;var r,i,n,s=W_.getWindowLocation();return!!s&&(null==(r=e.conditions)||!r.url||V_[null!==(i=null==(n=e.conditions)?void 0:n.urlMatchType)&&void 0!==i?i:"icontains"](e.conditions.url,s))}static getWindowLocation(){return null==Br?void 0:Br.location}static _matchUTMConditions(e){var t;if(mi(e.conditions)||mi(null==(t=e.conditions)?void 0:t.utm))return!0;var r=Gp();if(r.utm_source){var i,n,s,o,a,l,u,c,d=null==(i=e.conditions)||null==(i=i.utm)||!i.utm_campaign||(null==(n=e.conditions)||null==(n=n.utm)?void 0:n.utm_campaign)==r.utm_campaign,h=null==(s=e.conditions)||null==(s=s.utm)||!s.utm_source||(null==(o=e.conditions)||null==(o=o.utm)?void 0:o.utm_source)==r.utm_source,p=null==(a=e.conditions)||null==(a=a.utm)||!a.utm_medium||(null==(l=e.conditions)||null==(l=l.utm)?void 0:l.utm_medium)==r.utm_medium,_=null==(u=e.conditions)||null==(u=u.utm)||!u.utm_term||(null==(c=e.conditions)||null==(c=c.utm)?void 0:c.utm_term)==r.utm_term;return d&&p&&_&&h}return!1}static _logInfo(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];Ei.info("[WebExperiments] "+e,r)}_applyTransforms(e,t,r){this._is_bot()?W_._logInfo("Refusing to render web experiment since the viewer is a likely bot"):"control"!==t?r.forEach((r=>{if(r.selector){var i;W_._logInfo("applying transform of variant "+t+" for experiment "+e+" ",r);var n=null==(i=document)?void 0:i.querySelectorAll(r.selector);null==n||n.forEach((e=>{var t=e;r.html&&(t.innerHTML=r.html),r.css&&t.setAttribute("style",r.css)}))}})):W_._logInfo("Control variants leave the page unmodified.")}_is_bot(){return Wr&&this._instance?B_(Wr,this._instance.config.custom_blocked_useragents):void 0}}var U_={},Z_=()=>{},z_="posthog",G_=!Kl&&-1===(null==Yr?void 0:Yr.indexOf("MSIE"))&&-1===(null==Yr?void 0:Yr.indexOf("Mozilla")),j_=e=>{var t;return{api_host:"https://us.i.posthog.com",ui_host:null,token:"",autocapture:!0,rageclick:!0,cross_subdomain_cookie:qi(null==Ur?void 0:Ur.location),persistence:"localStorage+cookie",persistence_name:"",loaded:Z_,save_campaign_params:!0,custom_campaign_params:[],custom_blocked_useragents:[],save_referrer:!0,capture_pageview:"2025-05-24"!==e||"history_change",capture_pageleave:"if_capture_pageview",defaults:null!=e?e:"unset",debug:Zr&&gi(null==Zr?void 0:Zr.search)&&-1!==Zr.search.indexOf("__posthog_debug=true")||!1,cookie_expiration:365,upgrade:!1,disable_session_recording:!1,disable_persistence:!1,disable_web_experiments:!0,disable_surveys:!1,disable_surveys_automatic_display:!1,disable_external_dependency_loading:!1,enable_recording_console_log:void 0,secure_cookie:"https:"===(null==Br||null==(t=Br.location)?void 0:t.protocol),ip:!0,opt_out_capturing_by_default:!1,opt_out_persistence_by_default:!1,opt_out_useragent_filter:!1,opt_out_capturing_persistence_type:"localStorage",opt_out_capturing_cookie_prefix:null,opt_in_site_apps:!1,property_denylist:[],respect_dnt:!1,sanitize_properties:null,request_headers:{},request_batching:!0,properties_string_max_length:65535,session_recording:{},mask_all_element_attributes:!1,mask_all_text:!1,mask_personal_data_properties:!1,custom_personal_data_properties:[],advanced_disable_flags:!1,advanced_disable_decide:!1,advanced_disable_feature_flags:!1,advanced_disable_feature_flags_on_first_load:!1,advanced_only_evaluate_survey_feature_flags:!1,advanced_disable_toolbar_metrics:!1,feature_flag_request_timeout_ms:3e3,surveys_request_timeout_ms:1e4,on_request_error:e=>{var t="Bad HTTP status: "+e.statusCode+" "+e.text;Ei.error(t)},get_device_id:e=>e,capture_performance:void 0,name:"posthog",bootstrap:{},disable_compression:!1,session_idle_timeout_seconds:1800,person_profiles:"identified_only",before_send:void 0,request_queue_config:{flush_interval_ms:I_},error_tracking:{},_onCapture:Z_}},Y_=e=>{var t={};_i(e.process_person)||(t.person_profiles=e.process_person),_i(e.xhr_headers)||(t.request_headers=e.xhr_headers),_i(e.cookie_name)||(t.persistence_name=e.cookie_name),_i(e.disable_cookie)||(t.disable_persistence=e.disable_cookie),_i(e.store_google)||(t.save_campaign_params=e.store_google),_i(e.verbose)||(t.debug=e.verbose);var r=Ai({},t,e);return li(e.property_blacklist)&&(_i(e.property_denylist)?r.property_denylist=e.property_blacklist:li(e.property_denylist)?r.property_denylist=[...e.property_blacklist,...e.property_denylist]:Ei.error("Invalid value for property_denylist config: "+e.property_denylist)),r};class X_{constructor(){this.__forceAllowLocalhost=!1}get _forceAllowLocalhost(){return this.__forceAllowLocalhost}set _forceAllowLocalhost(e){Ei.error("WebPerformanceObserver is deprecated and has no impact on network capture. Use `_forceAllowLocalhostNetworkCapture` on `posthog.sessionRecording`"),this.__forceAllowLocalhost=e}}class J_{get decideEndpointWasHit(){var e,t;return null!==(e=null==(t=this.featureFlags)?void 0:t.hasLoadedFlags)&&void 0!==e&&e}get flagsEndpointWasHit(){var e,t;return null!==(e=null==(t=this.featureFlags)?void 0:t.hasLoadedFlags)&&void 0!==e&&e}constructor(){this.webPerformance=new X_,this._personProcessingSetOncePropertiesSent=!1,this.version=Ii.LIB_VERSION,this._internalEventEmitter=new f_,this._calculate_event_properties=this.calculateEventProperties.bind(this),this.config=j_(),this.SentryIntegration=kp,this.sentryIntegration=e=>function(e,t){var r=Ip(e,t);return{name:Cp,processEvent:e=>r(e)}}(this,e),this.__request_queue=[],this.__loaded=!1,this.analyticsDefaultEndpoint="/e/",this._initialPageviewCaptured=!1,this._visibilityStateListener=null,this._initialPersonProfilesConfig=null,this._cachedPersonProperties=null,this.featureFlags=new p_(this),this.toolbar=new Rp(this),this.scrollManager=new T_(this),this.pageViewManager=new qp(this),this.surveys=new y_(this),this.experiments=new W_(this),this.exceptions=new $p(this),this.rateLimiter=new S_(this),this.requestRouter=new $_(this),this.consent=new Ah(this),this.people={set:(e,t,r)=>{var i=gi(e)?{[e]:t}:e;this.setPersonProperties(i),null==r||r({})},set_once:(e,t,r)=>{var i=gi(e)?{[e]:t}:e;this.setPersonProperties(void 0,i),null==r||r({})}},this.on("eventCaptured",(e=>Ei.info('send "'+(null==e?void 0:e.event)+'"',e)))}init(e,t,r){if(r&&r!==z_){var i,n=null!==(i=U_[r])&&void 0!==i?i:new J_;return n._init(e,t,r),U_[r]=n,U_[z_][r]=n,n}return this._init(e,t,r)}_init(e,t,i){var n,s;if(void 0===t&&(t={}),_i(e)||fi(e))return Ei.critical("PostHog was initialized without a token. This likely indicates a misconfiguration. Please check the first argument passed to posthog.init()"),this;if(this.__loaded)return Ei.warn("You have already initialized PostHog! Re-initializing is a no-op"),this;this.__loaded=!0,this.config={},this._originalUserConfig=t,this._triggered_notifs=[],t.person_profiles&&(this._initialPersonProfilesConfig=t.person_profiles),this.set_config(Ai({},j_(t.defaults),Y_(t),{name:i,token:e})),this.config.on_xhr_error&&Ei.error("on_xhr_error is deprecated. Use on_request_error instead"),this.compression=t.disable_compression?void 0:Qr.GZipJS,this.persistence=new g_(this.config),this.sessionPersistence="sessionStorage"===this.config.persistence||"memory"===this.config.persistence?this.persistence:new g_(r({},this.config,{persistence:"sessionStorage"}));var o=r({},this.persistence.props),a=r({},this.sessionPersistence.props);if(this.register({$initialization_time:(new Date).toISOString()}),this._requestQueue=new k_((e=>this._send_retriable_request(e)),this.config.request_queue_config),this._retryQueue=new x_(this),this.__request_queue=[],this.config.__preview_experimental_cookieless_mode||(this.sessionManager=new F_(this),this.sessionPropsManager=new R_(this,this.sessionManager,this.persistence)),new Fp(this).startIfEnabledOrStop(),this.siteApps=new O_(this),null==(n=this.siteApps)||n.init(),this.config.__preview_experimental_cookieless_mode||(this.sessionRecording=new bp(this),this.sessionRecording.startIfEnabledOrStop()),this.config.disable_scroll_properties||this.scrollManager.startMeasuringScrollPosition(),this.autocapture=new ph(this),this.autocapture.startIfEnabled(),this.surveys.loadIfEnabled(),this.heatmaps=new Bp(this),this.heatmaps.startIfEnabled(),this.webVitalsAutocapture=new Op(this),this.exceptionObserver=new qh(this),this.exceptionObserver.startIfEnabled(),this.deadClicksAutocapture=new Oh(this,Ph),this.deadClicksAutocapture.startIfEnabled(),this.historyAutocapture=new Hh(this),this.historyAutocapture.startIfEnabled(),Ii.DEBUG=Ii.DEBUG||this.config.debug,Ii.DEBUG&&Ei.info("Starting in debug mode",{this:this,config:t,thisC:r({},this.config),p:o,s:a}),this._sync_opt_out_with_persistence(),void 0!==(null==(s=t.bootstrap)?void 0:s.distinctID)){var l,u,c=this.config.get_device_id(_a()),d=null!=(l=t.bootstrap)&&l.isIdentifiedID?c:t.bootstrap.distinctID;this.persistence.set_property(xn,null!=(u=t.bootstrap)&&u.isIdentifiedID?"identified":"anonymous"),this.register({distinct_id:t.bootstrap.distinctID,$device_id:d})}if(this._hasBootstrappedFeatureFlags()){var h,p,_=Object.keys((null==(h=t.bootstrap)?void 0:h.featureFlags)||{}).filter((e=>{var r;return!(null==(r=t.bootstrap)||null==(r=r.featureFlags)||!r[e])})).reduce(((e,r)=>{var i;return e[r]=(null==(i=t.bootstrap)||null==(i=i.featureFlags)?void 0:i[r])||!1,e}),{}),g=Object.keys((null==(p=t.bootstrap)?void 0:p.featureFlagPayloads)||{}).filter((e=>_[e])).reduce(((e,r)=>{var i,n;null!=(i=t.bootstrap)&&null!=(i=i.featureFlagPayloads)&&i[r]&&(e[r]=null==(n=t.bootstrap)||null==(n=n.featureFlagPayloads)?void 0:n[r]);return e}),{});this.featureFlags.receivedFeatureFlags({featureFlags:_,featureFlagPayloads:g})}if(this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:On,$device_id:null},"");else if(!this.get_distinct_id()){var f=this.config.get_device_id(_a());this.register_once({distinct_id:f,$device_id:f},""),this.persistence.set_property(xn,"anonymous")}return $i(Br,"onpagehide"in self?"pagehide":"unload",this._handle_unload.bind(this),{passive:!1}),this.toolbar.maybeLoadToolbar(),t.segment?wp(this,(()=>this._loaded())):this._loaded(),ui(this.config._onCapture)&&this.config._onCapture!==Z_&&(Ei.warn("onCapture is deprecated. Please use `before_send` instead"),this.on("eventCaptured",(e=>this.config._onCapture(e.event,e)))),this}_onRemoteConfig(e){var t,r,i,n,s,o,a,l;if(!Ur||!Ur.body)return Ei.info("document not ready yet, trying again in 500 milliseconds..."),void setTimeout((()=>{this._onRemoteConfig(e)}),500);this.compression=void 0,e.supportedCompression&&!this.config.disable_compression&&(this.compression=ti(e.supportedCompression,Qr.GZipJS)?Qr.GZipJS:ti(e.supportedCompression,Qr.Base64)?Qr.Base64:void 0),null!=(t=e.analytics)&&t.endpoint&&(this.analyticsDefaultEndpoint=e.analytics.endpoint),this.set_config({person_profiles:this._initialPersonProfilesConfig?this._initialPersonProfilesConfig:"identified_only"}),null==(r=this.siteApps)||r.onRemoteConfig(e),null==(i=this.sessionRecording)||i.onRemoteConfig(e),null==(n=this.autocapture)||n.onRemoteConfig(e),null==(s=this.heatmaps)||s.onRemoteConfig(e),this.surveys.onRemoteConfig(e),null==(o=this.webVitalsAutocapture)||o.onRemoteConfig(e),null==(a=this.exceptionObserver)||a.onRemoteConfig(e),this.exceptions.onRemoteConfig(e),null==(l=this.deadClicksAutocapture)||l.onRemoteConfig(e)}_loaded(){try{this.config.loaded(this)}catch(e){Ei.critical("`loaded` function failed",e)}this._start_queue_if_opted_in(),this.config.capture_pageview&&setTimeout((()=>{this.consent.isOptedIn()&&this._captureInitialPageview()}),1),new C_(this).load(),this.featureFlags.flags()}_start_queue_if_opted_in(){var e;this.has_opted_out_capturing()||this.config.request_batching&&(null==(e=this._requestQueue)||e.enable())}_dom_loaded(){this.has_opted_out_capturing()||Mi(this.__request_queue,(e=>this._send_retriable_request(e))),this.__request_queue=[],this._start_queue_if_opted_in()}_handle_unload(){var e,t;this.config.request_batching?(this._shouldCapturePageleave()&&this.capture("$pageleave"),null==(e=this._requestQueue)||e.unload(),null==(t=this._retryQueue)||t.unload()):this._shouldCapturePageleave()&&this.capture("$pageleave",null,{transport:"sendBeacon"})}_send_request(e){this.__loaded&&(G_?this.__request_queue.push(e):this.rateLimiter.isServerRateLimited(e.batchKey)||(e.transport=e.transport||this.config.api_transport,e.url=eu(e.url,{ip:this.config.ip?1:0}),e.headers=r({},this.config.request_headers),e.compression="best-available"===e.compression?this.compression:e.compression,e.fetchOptions=e.fetchOptions||this.config.fetch_options,(e=>{var t,i,n,s=r({},e);s.timeout=s.timeout||6e4,s.url=eu(s.url,{_:(new Date).getTime().toString(),ver:Ii.LIB_VERSION,compression:s.compression});var o=null!==(t=s.transport)&&void 0!==t?t:"fetch",a=null!==(i=null==(n=Hi(iu,(e=>e.transport===o)))?void 0:n.method)&&void 0!==i?i:iu[0].method;if(!a)throw new Error("No available transport method");a(s)})(r({},e,{callback:t=>{var r,i;(this.rateLimiter.checkForLimiting(t),t.statusCode>=400)&&(null==(r=(i=this.config).on_request_error)||r.call(i,t));null==e.callback||e.callback(t)}}))))}_send_retriable_request(e){this._retryQueue?this._retryQueue.retriableRequest(e):this._send_request(e)}_execute_array(e){var t,r=[],i=[],n=[];Mi(e,(e=>{e&&(t=e[0],li(t)?n.push(e):ui(e)?e.call(this):li(e)&&"alias"===t?r.push(e):li(e)&&-1!==t.indexOf("capture")&&ui(this[t])?n.push(e):i.push(e))}));var s=function(e,t){Mi(e,(function(e){if(li(e[0])){var r=t;Ri(e,(function(e){r=r[e[0]].apply(r,e.slice(1))}))}else this[e[0]].apply(this,e.slice(1))}),t)};s(r,this),s(i,this),s(n,this)}_hasBootstrappedFeatureFlags(){var e,t;return(null==(e=this.config.bootstrap)?void 0:e.featureFlags)&&Object.keys(null==(t=this.config.bootstrap)?void 0:t.featureFlags).length>0||!1}push(e){this._execute_array([e])}capture(e,t,i){var n;if(this.__loaded&&this.persistence&&this.sessionPersistence&&this._requestQueue){if(!this.consent.isOptedOut())if(!_i(e)&&gi(e)){if(this.config.opt_out_useragent_filter||!this._is_bot()){var s=null!=i&&i.skip_client_rate_limiting?void 0:this.rateLimiter.clientRateLimitContext();if(null==s||!s.isRateLimited){null!=t&&t.$current_url&&!gi(null==t?void 0:t.$current_url)&&(Ei.error("Invalid `$current_url` property provided to `posthog.capture`. Input must be a string. Ignoring provided value."),null==t||delete t.$current_url),this.sessionPersistence.update_search_keyword(),this.config.save_campaign_params&&this.sessionPersistence.update_campaign_params(),this.config.save_referrer&&this.sessionPersistence.update_referrer_info(),(this.config.save_campaign_params||this.config.save_referrer)&&this.persistence.set_initial_person_info();var o=new Date,a=(null==i?void 0:i.timestamp)||o,l=_a(),u={uuid:l,event:e,properties:this.calculateEventProperties(e,t||{},a,l)};s&&(u.properties.$lib_rate_limit_remaining_tokens=s.remainingTokens),(null==i?void 0:i.$set)&&(u.$set=null==i?void 0:i.$set);var c,d,h=this._calculate_set_once_properties(null==i?void 0:i.$set_once);if(h&&(u.$set_once=h),(u=Di(u,null!=i&&i._noTruncate?null:this.config.properties_string_max_length)).timestamp=a,_i(null==i?void 0:i.timestamp)||(u.properties.$event_time_override_provided=!0,u.properties.$event_time_override_system_time=o),e===Qo.DISMISSED||e===Qo.SENT){var p=null==t?void 0:t[ea.SURVEY_ID],_=null==t?void 0:t[ea.SURVEY_ITERATION];localStorage.setItem((d=""+sa+(c={id:p,current_iteration:_}).id,c.current_iteration&&c.current_iteration>0&&(d=""+sa+c.id+"_"+c.current_iteration),d),"true"),u.$set=r({},u.$set,{[aa({id:p,current_iteration:_},e===Qo.SENT?"responded":"dismissed")]:!0})}var g=r({},u.properties.$set,u.$set);if(pi(g)||this.setPersonPropertiesForFlags(g),!mi(this.config.before_send)){var f=this._runBeforeSend(u);if(!f)return;u=f}this._internalEventEmitter.emit("eventCaptured",u);var v={method:"POST",url:null!==(n=null==i?void 0:i._url)&&void 0!==n?n:this.requestRouter.endpointFor("api",this.analyticsDefaultEndpoint),data:u,compression:"best-available",batchKey:null==i?void 0:i._batchKey};return!this.config.request_batching||i&&(null==i||!i._batchKey)||null!=i&&i.send_instantly?this._send_retriable_request(v):this._requestQueue.enqueue(v),u}Ei.critical("This capture call is ignored due to client rate limiting.")}}else Ei.error("No event name provided to posthog.capture")}else Ei.uninitializedWarning("posthog.capture")}_addCaptureHook(e){return this.on("eventCaptured",(t=>e(t.event,t)))}calculateEventProperties(e,t,i,n,s){if(i=i||new Date,!this.persistence||!this.sessionPersistence)return t;var o=s?void 0:this.persistence.remove_event_timer(e),a=r({},t);if(a.token=this.config.token,a.$config_defaults=this.config.defaults,this.config.__preview_experimental_cookieless_mode&&(a.$cookieless_mode=!0),"$snapshot"===e){var l=r({},this.persistence.properties(),this.sessionPersistence.properties());return a.distinct_id=l.distinct_id,(!gi(a.distinct_id)&&!yi(a.distinct_id)||fi(a.distinct_id))&&Ei.error("Invalid distinct_id for replay event. This indicates a bug in your implementation"),a}var u,c=r_(this.config.mask_personal_data_properties,this.config.custom_personal_data_properties);if(this.sessionManager){var{sessionId:d,windowId:h}=this.sessionManager.checkAndGetSessionAndWindowId(s,i.getTime());a.$session_id=d,a.$window_id=h}this.sessionPropsManager&&Ai(a,this.sessionPropsManager.getSessionProps());try{var p;this.sessionRecording&&Ai(a,this.sessionRecording.sdkDebugProperties),a.$sdk_debug_retry_queue_size=null==(p=this._retryQueue)?void 0:p.length}catch(e){a.$sdk_debug_error_capturing_properties=String(e)}if(this.requestRouter.region===q_.CUSTOM&&(a.$lib_custom_api_host=this.config.api_host),u="$pageview"!==e||s?"$pageleave"!==e||s?this.pageViewManager.doEvent():this.pageViewManager.doPageLeave(i):this.pageViewManager.doPageView(i,n),a=Ai(a,u),"$pageview"===e&&Ur&&(a.title=Ur.title),!_i(o)){var _=i.getTime()-o;a.$duration=parseFloat((_/1e3).toFixed(3))}Yr&&this.config.opt_out_useragent_filter&&(a.$browser_type=this._is_bot()?"bot":"browser"),(a=Ai({},c,this.persistence.properties(),this.sessionPersistence.properties(),a)).$is_identified=this._isIdentified(),li(this.config.property_denylist)?Ri(this.config.property_denylist,(function(e){delete a[e]})):Ei.error("Invalid value for property_denylist config: "+this.config.property_denylist+" or property_blacklist config: "+this.config.property_blacklist);var g=this.config.sanitize_properties;g&&(Ei.error("sanitize_properties is deprecated. Use before_send instead"),a=g(a,e));var f=this._hasPersonProcessing();return a.$process_person_profile=f,f&&!s&&this._requirePersonProcessing("_calculate_event_properties"),a}_calculate_set_once_properties(e){var t;if(!this.persistence||!this._hasPersonProcessing())return e;if(this._personProcessingSetOncePropertiesSent)return e;var r=this.persistence.get_initial_props(),i=null==(t=this.sessionPropsManager)?void 0:t.getSetOnceProps(),n=Ai({},r,i||{},e||{}),s=this.config.sanitize_properties;return s&&(Ei.error("sanitize_properties is deprecated. Use before_send instead"),n=s(n,"$set_once")),this._personProcessingSetOncePropertiesSent=!0,pi(n)?void 0:n}register(e,t){var r;null==(r=this.persistence)||r.register(e,t)}register_once(e,t,r){var i;null==(i=this.persistence)||i.register_once(e,t,r)}register_for_session(e){var t;null==(t=this.sessionPersistence)||t.register(e)}unregister(e){var t;null==(t=this.persistence)||t.unregister(e)}unregister_for_session(e){var t;null==(t=this.sessionPersistence)||t.unregister(e)}_register_single(e,t){this.register({[e]:t})}getFeatureFlag(e,t){return this.featureFlags.getFeatureFlag(e,t)}getFeatureFlagPayload(e){var t=this.featureFlags.getFeatureFlagPayload(e);try{return JSON.parse(t)}catch(e){return t}}isFeatureEnabled(e,t){return this.featureFlags.isFeatureEnabled(e,t)}reloadFeatureFlags(){this.featureFlags.reloadFeatureFlags()}updateEarlyAccessFeatureEnrollment(e,t){this.featureFlags.updateEarlyAccessFeatureEnrollment(e,t)}getEarlyAccessFeatures(e,t,r){return void 0===t&&(t=!1),this.featureFlags.getEarlyAccessFeatures(e,t,r)}on(e,t){return this._internalEventEmitter.on(e,t)}onFeatureFlags(e){return this.featureFlags.onFeatureFlags(e)}onSurveysLoaded(e){return this.surveys.onSurveysLoaded(e)}onSessionId(e){var t,r;return null!==(t=null==(r=this.sessionManager)?void 0:r.onSessionId(e))&&void 0!==t?t:()=>{}}getSurveys(e,t){void 0===t&&(t=!1),this.surveys.getSurveys(e,t)}getActiveMatchingSurveys(e,t){void 0===t&&(t=!1),this.surveys.getActiveMatchingSurveys(e,t)}renderSurvey(e,t){this.surveys.renderSurvey(e,t)}canRenderSurvey(e){return this.surveys.canRenderSurvey(e)}canRenderSurveyAsync(e,t){return void 0===t&&(t=!1),this.surveys.canRenderSurveyAsync(e,t)}identify(e,t,i){if(!this.__loaded||!this.persistence)return Ei.uninitializedWarning("posthog.identify");if(yi(e)&&(e=e.toString(),Ei.warn("The first argument to posthog.identify was a number, but it should be a string. It has been converted to a string.")),e)if(["distinct_id","distinctid"].includes(e.toLowerCase()))Ei.critical('The string "'+e+'" was set in posthog.identify which indicates an error. This ID should be unique to the user and not a hardcoded string.');else if(e!==On){if(this._requirePersonProcessing("posthog.identify")){var n=this.get_distinct_id();if(this.register({$user_id:e}),!this.get_property("$device_id")){var s=n;this.register_once({$had_persisted_distinct_id:!0,$device_id:s},"")}e!==n&&e!==this.get_property(Ji)&&(this.unregister(Ji),this.register({distinct_id:e}));var o="anonymous"===(this.persistence.get_property(xn)||"anonymous");e!==n&&o?(this.persistence.set_property(xn,"identified"),this.setPersonPropertiesForFlags(r({},i||{},t||{}),!1),this.capture("$identify",{distinct_id:e,$anon_distinct_id:n},{$set:t||{},$set_once:i||{}}),this._cachedPersonProperties=su(e,t,i),this.featureFlags.setAnonymousDistinctId(n)):(t||i)&&this.setPersonProperties(t,i),e!==n&&(this.reloadFeatureFlags(),this.unregister(En))}}else Ei.critical('The string "'+On+'" was set in posthog.identify which indicates an error. This ID is only used as a sentinel value.');else Ei.error("Unique user id has not been set in posthog.identify")}setPersonProperties(e,t){if((e||t)&&this._requirePersonProcessing("posthog.setPersonProperties")){var i=su(this.get_distinct_id(),e,t);this._cachedPersonProperties!==i?(this.setPersonPropertiesForFlags(r({},t||{},e||{})),this.capture("$set",{$set:e||{},$set_once:t||{}}),this._cachedPersonProperties=i):Ei.info("A duplicate setPersonProperties call was made with the same properties. It has been ignored.")}}group(e,t,i){if(e&&t){if(this._requirePersonProcessing("posthog.group")){var n=this.getGroups();n[e]!==t&&this.resetGroupPropertiesForFlags(e),this.register({$groups:r({},n,{[e]:t})}),i&&(this.capture("$groupidentify",{$group_type:e,$group_key:t,$group_set:i}),this.setGroupPropertiesForFlags({[e]:i})),n[e]===t||i||this.reloadFeatureFlags()}}else Ei.error("posthog.group requires a group type and group key")}resetGroups(){this.register({$groups:{}}),this.resetGroupPropertiesForFlags(),this.reloadFeatureFlags()}setPersonPropertiesForFlags(e,t){void 0===t&&(t=!0),this.featureFlags.setPersonPropertiesForFlags(e,t)}resetPersonPropertiesForFlags(){this.featureFlags.resetPersonPropertiesForFlags()}setGroupPropertiesForFlags(e,t){void 0===t&&(t=!0),this._requirePersonProcessing("posthog.setGroupPropertiesForFlags")&&this.featureFlags.setGroupPropertiesForFlags(e,t)}resetGroupPropertiesForFlags(e){this.featureFlags.resetGroupPropertiesForFlags(e)}reset(e){var t,r,i,n;if(Ei.info("reset"),!this.__loaded)return Ei.uninitializedWarning("posthog.reset");var s=this.get_property("$device_id");if(this.consent.reset(),null==(t=this.persistence)||t.clear(),null==(r=this.sessionPersistence)||r.clear(),this.surveys.reset(),null==(i=this.persistence)||i.set_property(xn,"anonymous"),null==(n=this.sessionManager)||n.resetSessionId(),this._cachedPersonProperties=null,this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:On,$device_id:null},"");else{var o=this.config.get_device_id(_a());this.register_once({distinct_id:o,$device_id:e?o:s},"")}this.register({$last_posthog_reset:(new Date).toISOString()},1)}get_distinct_id(){return this.get_property("distinct_id")}getGroups(){return this.get_property("$groups")||{}}get_session_id(){var e,t;return null!==(e=null==(t=this.sessionManager)?void 0:t.checkAndGetSessionAndWindowId(!0).sessionId)&&void 0!==e?e:""}get_session_replay_url(e){if(!this.sessionManager)return"";var{sessionId:t,sessionStartTimestamp:r}=this.sessionManager.checkAndGetSessionAndWindowId(!0),i=this.requestRouter.endpointFor("ui","/project/"+this.config.token+"/replay/"+t);if(null!=e&&e.withTimestamp&&r){var n,s=null!==(n=e.timestampLookBack)&&void 0!==n?n:10;if(!r)return i;i+="?t="+Math.max(Math.floor(((new Date).getTime()-r)/1e3)-s,0)}return i}alias(e,t){return e===this.get_property(Xi)?(Ei.critical("Attempting to create alias for existing People user - aborting."),-2):this._requirePersonProcessing("posthog.alias")?(_i(t)&&(t=this.get_distinct_id()),e!==t?(this._register_single(Ji,e),this.capture("$create_alias",{alias:e,distinct_id:t})):(Ei.warn("alias matches current distinct_id - skipping api call."),this.identify(e),-1)):void 0}set_config(e){var t,i,n,s,o=r({},this.config);hi(e)&&(Ai(this.config,Y_(e)),null==(t=this.persistence)||t.update_config(this.config,o),this.sessionPersistence="sessionStorage"===this.config.persistence||"memory"===this.config.persistence?this.persistence:new g_(r({},this.config,{persistence:"sessionStorage"})),Ch._is_supported()&&"true"===Ch._get("ph_debug")&&(this.config.debug=!0),this.config.debug&&(Ii.DEBUG=!0,Ei.info("set_config",{config:e,oldConfig:o,newConfig:r({},this.config)})),null==(i=this.sessionRecording)||i.startIfEnabledOrStop(),null==(n=this.autocapture)||n.startIfEnabled(),null==(s=this.heatmaps)||s.startIfEnabled(),this.surveys.loadIfEnabled(),this._sync_opt_out_with_persistence())}startSessionRecording(e){var t=!0===e,r={sampling:t||!(null==e||!e.sampling),linked_flag:t||!(null==e||!e.linked_flag),url_trigger:t||!(null==e||!e.url_trigger),event_trigger:t||!(null==e||!e.event_trigger)};if(Object.values(r).some(Boolean)){var i,n,s,o,a;if(null==(i=this.sessionManager)||i.checkAndGetSessionAndWindowId(),r.sampling)null==(n=this.sessionRecording)||n.overrideSampling();if(r.linked_flag)null==(s=this.sessionRecording)||s.overrideLinkedFlag();if(r.url_trigger)null==(o=this.sessionRecording)||o.overrideTrigger("url");if(r.event_trigger)null==(a=this.sessionRecording)||a.overrideTrigger("event")}this.set_config({disable_session_recording:!1})}stopSessionRecording(){this.set_config({disable_session_recording:!0})}sessionRecordingStarted(){var e;return!(null==(e=this.sessionRecording)||!e.started)}captureException(e,t){var i=new Error("PostHog syntheticException");this.exceptions.sendExceptionEvent(r({},rd((e=>e instanceof Error)(e)?{error:e,event:e.message}:{event:e},{syntheticException:i}),t))}loadToolbar(e){return this.toolbar.loadToolbar(e)}get_property(e){var t;return null==(t=this.persistence)?void 0:t.props[e]}getSessionProperty(e){var t;return null==(t=this.sessionPersistence)?void 0:t.props[e]}toString(){var e,t=null!==(e=this.config.name)&&void 0!==e?e:z_;return t!==z_&&(t=z_+"."+t),t}_isIdentified(){var e,t;return"identified"===(null==(e=this.persistence)?void 0:e.get_property(xn))||"identified"===(null==(t=this.sessionPersistence)?void 0:t.get_property(xn))}_hasPersonProcessing(){var e,t;return!("never"===this.config.person_profiles||"identified_only"===this.config.person_profiles&&!this._isIdentified()&&pi(this.getGroups())&&(null==(e=this.persistence)||null==(e=e.props)||!e[Ji])&&(null==(t=this.persistence)||null==(t=t.props)||!t[Nn]))}_shouldCapturePageleave(){return!0===this.config.capture_pageleave||"if_capture_pageview"===this.config.capture_pageleave&&(!0===this.config.capture_pageview||"history_change"===this.config.capture_pageview)}createPersonProfile(){this._hasPersonProcessing()||this._requirePersonProcessing("posthog.createPersonProfile")&&this.setPersonProperties({},{})}_requirePersonProcessing(e){return"never"===this.config.person_profiles?(Ei.error(e+' was called, but process_person is set to "never". This call will be ignored.'),!1):(this._register_single(Nn,!0),!0)}_sync_opt_out_with_persistence(){var e,t,r,i,n=this.consent.isOptedOut(),s=this.config.opt_out_persistence_by_default,o=this.config.disable_persistence||n&&!!s;(null==(e=this.persistence)?void 0:e._disabled)!==o&&(null==(r=this.persistence)||r.set_disabled(o));(null==(t=this.sessionPersistence)?void 0:t._disabled)!==o&&(null==(i=this.sessionPersistence)||i.set_disabled(o))}opt_in_capturing(e){var t;(this.consent.optInOut(!0),this._sync_opt_out_with_persistence(),_i(null==e?void 0:e.captureEventName)||null!=e&&e.captureEventName)&&this.capture(null!==(t=null==e?void 0:e.captureEventName)&&void 0!==t?t:"$opt_in",null==e?void 0:e.captureProperties,{send_instantly:!0});this.config.capture_pageview&&this._captureInitialPageview()}opt_out_capturing(){this.consent.optInOut(!1),this._sync_opt_out_with_persistence()}has_opted_in_capturing(){return this.consent.isOptedIn()}has_opted_out_capturing(){return this.consent.isOptedOut()}clear_opt_in_out_capturing(){this.consent.reset(),this._sync_opt_out_with_persistence()}_is_bot(){return Wr?B_(Wr,this.config.custom_blocked_useragents):void 0}_captureInitialPageview(){Ur&&("visible"===Ur.visibilityState?this._initialPageviewCaptured||(this._initialPageviewCaptured=!0,this.capture("$pageview",{title:Ur.title},{send_instantly:!0}),this._visibilityStateListener&&(Ur.removeEventListener("visibilitychange",this._visibilityStateListener),this._visibilityStateListener=null)):this._visibilityStateListener||(this._visibilityStateListener=this._captureInitialPageview.bind(this),$i(Ur,"visibilitychange",this._visibilityStateListener)))}debug(e){!1===e?(null==Br||Br.console.log("You've disabled debug mode."),localStorage&&localStorage.removeItem("ph_debug"),this.set_config({debug:!1})):(null==Br||Br.console.log("You're now in debug mode. All calls to PostHog will be logged in your console.\nYou can disable this with `posthog.debug(false)`."),localStorage&&localStorage.setItem("ph_debug","true"),this.set_config({debug:!0}))}_shouldDisableFlags(){var e,t,r,i,n,s,o,a=this._originalUserConfig||{};return"advanced_disable_flags"in a?!!a.advanced_disable_flags:!1!==this.config.advanced_disable_flags?!!this.config.advanced_disable_flags:!0===this.config.advanced_disable_decide?(Ei.warn("Config field 'advanced_disable_decide' is deprecated. Please use 'advanced_disable_flags' instead. The old field will be removed in a future major version."),!0):(r="advanced_disable_decide",i=!1,n=Ei,s=(t="advanced_disable_flags")in(e=a)&&!_i(e[t]),o=r in e&&!_i(e[r]),s?e[t]:o?(n&&n.warn("Config field '"+r+"' is deprecated. Please use '"+t+"' instead. The old field will be removed in a future major version."),e[r]):i)}_runBeforeSend(e){if(mi(this.config.before_send))return e;var t=li(this.config.before_send)?this.config.before_send:[this.config.before_send],r=e;for(var i of t){if(r=i(r),mi(r)){var n="Event '"+e.event+"' was rejected in beforeSend function";return Ci(e.event)?Ei.warn(n+". This can cause unexpected behavior."):Ei.info(n),null}r.properties&&!pi(r.properties)||Ei.warn("Event '"+e.event+"' has no properties after beforeSend function, this is likely an error.")}return r}getPageViewId(){var e;return null==(e=this.pageViewManager._currentPageview)?void 0:e.pageViewId}captureTraceFeedback(e,t){this.capture("$ai_feedback",{$ai_trace_id:String(e),$ai_feedback_text:t})}captureTraceMetric(e,t,r){this.capture("$ai_metric",{$ai_trace_id:String(e),$ai_metric_name:t,$ai_metric_value:String(r)})}}!function(e,t){for(var r=0;r<t.length;r++)e.prototype[t[r]]=Oi(e.prototype[t[r]])}(J_,["identify"]);var K_,Q_;K_=U_[z_]=new J_,(Q_=Xr.posthog)&&Ri(Q_._i,(function(e){if(e&&li(e)){var t=K_.init(e[0],e[1],e[2]),r=Q_[e[2]]||Q_;t&&(t._execute_array.call(t.people,r.people),t._execute_array(r))}})),Xr.posthog=K_,function(){function e(){e.done||(e.done=!0,G_=!1,Ri(U_,(function(e){e._dom_loaded()})))}null!=Ur&&Ur.addEventListener?"complete"===Ur.readyState?e():$i(Ur,"DOMContentLoaded",e,{capture:!1}):Br&&Ei.error("Browser doesn't support `document.addEventListener` so PostHog couldn't be initialized")}()}();
//# sourceMappingURL=array.full.no-external.js.map
