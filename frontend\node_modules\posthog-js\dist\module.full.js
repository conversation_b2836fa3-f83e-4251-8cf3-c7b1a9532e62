function t(t,e,i,r,n,o,s){try{var a=t[o](s),l=a.value}catch(t){return void i(t)}a.done?e(l):Promise.resolve(l).then(r,n)}function e(e){return function(){var i=this,r=arguments;return new Promise((function(n,o){var s=e.apply(i,r);function a(e){t(s,n,o,a,l,"next",e)}function l(e){t(s,n,o,a,l,"throw",e)}a(void 0)}))}}function i(){return i=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var r in i)({}).hasOwnProperty.call(i,r)&&(t[r]=i[r])}return t},i.apply(null,arguments)}function r(t,e){if(null==t)return{};var i={};for(var r in t)if({}.hasOwnProperty.call(t,r)){if(-1!==e.indexOf(r))continue;i[r]=t[r]}return i}var n,o=["type"],s=Object.defineProperty,a=(t,e,i)=>((t,e,i)=>e in t?s(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i)(t,"symbol"!=typeof e?e+"":e,i),l=Object.defineProperty,u=(t,e,i)=>((t,e,i)=>e in t?l(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i)(t,"symbol"!=typeof e?e+"":e,i),h=(t=>(t[t.Document=0]="Document",t[t.DocumentType=1]="DocumentType",t[t.Element=2]="Element",t[t.Text=3]="Text",t[t.CDATA=4]="CDATA",t[t.Comment=5]="Comment",t))(h||{}),c={Node:["childNodes","parentNode","parentElement","textContent"],ShadowRoot:["host","styleSheets"],Element:["shadowRoot","querySelector","querySelectorAll"],MutationObserver:[]},d={Node:["contains","getRootNode"],ShadowRoot:["getSelection"],Element:[],MutationObserver:["constructor"]};var v={};function f(t){if(v[t])return v[t];var e=function(t){var e,i=null==globalThis||null==(e=globalThis.Zone)||null==e.__symbol__?void 0:e.__symbol__(t);return i&&globalThis[i]?globalThis[i]:void 0}(t)||globalThis[t],i=e.prototype,r=t in c?c[t]:void 0,n=Boolean(r&&r.every((t=>{var e,r;return Boolean(null==(r=null==(e=Object.getOwnPropertyDescriptor(i,t))?void 0:e.get)?void 0:r.toString().includes("[native code]"))}))),o=t in d?d[t]:void 0,s=Boolean(o&&o.every((t=>{var e;return"function"==typeof i[t]&&(null==(e=i[t])?void 0:e.toString().includes("[native code]"))})));if(n&&s)return v[t]=e.prototype,e.prototype;try{var a=document.createElement("iframe");document.body.appendChild(a);var l=a.contentWindow;if(!l)return e.prototype;var u=l[t].prototype;return document.body.removeChild(a),u?v[t]=u:e.prototype}catch(t){return e.prototype}}var p={};function g(t,e,i){var r,n=t+"."+String(i);if(p[n])return p[n].call(e);var o=f(t),s=null==(r=Object.getOwnPropertyDescriptor(o,i))?void 0:r.get;return s?(p[n]=s,s.call(e)):e[i]}var m={};function y(t,e,i){var r=t+"."+String(i);if(m[r])return m[r].bind(e);var n=f(t)[i];return"function"!=typeof n?e[i]:(m[r]=n,n.bind(e))}var b={childNodes:function(t){return g("Node",t,"childNodes")},parentNode:function(t){return g("Node",t,"parentNode")},parentElement:function(t){return g("Node",t,"parentElement")},textContent:function(t){return g("Node",t,"textContent")},contains:function(t,e){return y("Node",t,"contains")(e)},getRootNode:function(t){return y("Node",t,"getRootNode")()},host:function(t){return t&&"host"in t?g("ShadowRoot",t,"host"):null},styleSheets:function(t){return t.styleSheets},shadowRoot:function(t){return t&&"shadowRoot"in t?g("Element",t,"shadowRoot"):null},querySelector:function(t,e){return g("Element",t,"querySelector")(e)},querySelectorAll:function(t,e){return g("Element",t,"querySelectorAll")(e)},mutationObserver:function(){return f("MutationObserver").constructor}};function _(t){return t.nodeType===t.ELEMENT_NODE}function w(t){var e=t&&"host"in t&&"mode"in t&&b.host(t)||null;return Boolean(e&&"shadowRoot"in e&&b.shadowRoot(e)===t)}function I(t){return"[object ShadowRoot]"===Object.prototype.toString.call(t)}function C(t){try{var e=t.rules||t.cssRules;if(!e)return null;var i=Array.from(e,(e=>S(e,t.href))).join("");return(r=i).includes(" background-clip: text;")&&!r.includes(" -webkit-background-clip: text;")&&(r=r.replace(/\sbackground-clip:\s*text;/g," -webkit-background-clip: text; background-clip: text;")),r}catch(t){return null}var r}function S(t,e){if(function(t){return"styleSheet"in t}(t)){var i;try{i=C(t.styleSheet)||function(t){var{cssText:e}=t;if(e.split('"').length<3)return e;var i=["@import","url("+JSON.stringify(t.href)+")"];return""===t.layerName?i.push("layer"):t.layerName&&i.push("layer("+t.layerName+")"),t.supportsText&&i.push("supports("+t.supportsText+")"),t.media.length&&i.push(t.media.mediaText),i.join(" ")+";"}(t)}catch(e){i=t.cssText}return t.styleSheet.href?D(i,t.styleSheet.href):i}var r,n=t.cssText;return function(t){return"selectorText"in t}(t)&&t.selectorText.includes(":")&&(r=/(\[(?:[\w-]+)[^\\])(:(?:[\w-]+)\])/gm,n=n.replace(r,"$1\\$2")),e?D(n,e):n}function k(t,e){return Array.from(t.styleSheets).find((t=>t.href===e))}let x=class{constructor(){u(this,"idNodeMap",new Map),u(this,"nodeMetaMap",new WeakMap)}getId(t){var e;if(!t)return-1;var i=null==(e=this.getMeta(t))?void 0:e.id;return null!=i?i:-1}getNode(t){return this.idNodeMap.get(t)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(t){return this.nodeMetaMap.get(t)||null}removeNodeFromMap(t){var e=this.getId(t);this.idNodeMap.delete(e),t.childNodes&&t.childNodes.forEach((t=>this.removeNodeFromMap(t)))}has(t){return this.idNodeMap.has(t)}hasNode(t){return this.nodeMetaMap.has(t)}add(t,e){var i=e.id;this.idNodeMap.set(i,t),this.nodeMetaMap.set(t,e)}replace(t,e){var i=this.getNode(t);if(i){var r=this.nodeMetaMap.get(i);r&&this.nodeMetaMap.set(e,r)}this.idNodeMap.set(t,e)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}};function A(t){var{element:e,maskInputOptions:i,tagName:r,type:n,value:o,maskInputFn:s}=t,a=o||"",l=n&&T(n);return(i[r.toLowerCase()]||l&&i[l])&&(a=s?s(a,e):"*".repeat(a.length)),a}function T(t){return t.toLowerCase()}var M="__rrweb_original__";function R(t){var e=t.type;return t.hasAttribute("data-rr-is-password")?"password":e?T(e):null}function N(t,e){var i,r;try{r=new URL(t,null!=e?e:window.location.href)}catch(t){return null}var n=r.pathname.match(/\.([0-9a-z]+)(?:$)/i);return null!==(i=null==n?void 0:n[1])&&void 0!==i?i:null}var E=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,F=/^(?:[a-z+]+:)?\/\//i,O=/^www\..*/i,P=/^(data:)([^,]*),(.*)/i;function D(t,e){return(t||"").replace(E,((t,i,r,n,o,s)=>{var a,l=r||o||s,u=i||n||"";if(!l)return t;if(F.test(l)||O.test(l))return"url("+u+l+u+")";if(P.test(l))return"url("+u+l+u+")";if("/"===l[0])return"url("+u+(((a=e).indexOf("//")>-1?a.split("/").slice(0,3).join("/"):a.split("/")[0]).split("?")[0]+l)+u+")";var h=e.split("/"),c=l.split("/");for(var d of(h.pop(),c))"."!==d&&(".."===d?h.pop():h.push(d));return"url("+u+h.join("/")+u+")"}))}var L,B,$=1,Z=new RegExp("[^a-z0-9-_:]"),q=-2;function H(){return $++}var V=/^[^ \t\n\r\u000c]+/,j=/^[, \t\n\r\u000c]+/;var Y=new WeakMap;function z(t,e){return e&&""!==e.trim()?W(t,e):e}function G(t){return Boolean("svg"===t.tagName||t.ownerSVGElement)}function W(t,e){var i=Y.get(t);if(i||(i=t.createElement("a"),Y.set(t,i)),e){if(e.startsWith("blob:")||e.startsWith("data:"))return e}else e="";return i.setAttribute("href",e),i.href}function U(t,e,i,r){return r?"src"===i||"href"===i&&("use"!==e||"#"!==r[0])||"xlink:href"===i&&"#"!==r[0]?z(t,r):"background"!==i||"table"!==e&&"td"!==e&&"th"!==e?"srcset"===i?function(t,e){if(""===e.trim())return e;var i=0;function r(t){var r,n=t.exec(e.substring(i));return n?(r=n[0],i+=r.length,r):""}for(var n=[];r(j),!(i>=e.length);){var o=r(V);if(","===o.slice(-1))o=z(t,o.substring(0,o.length-1)),n.push(o);else{var s="";o=z(t,o);for(var a=!1;;){var l=e.charAt(i);if(""===l){n.push((o+s).trim());break}if(a)")"===l&&(a=!1);else{if(","===l){i+=1,n.push((o+s).trim());break}"("===l&&(a=!0)}s+=l,i+=1}}}return n.join(", ")}(t,r):"style"===i?D(r,W(t)):"object"===e&&"data"===i?z(t,r):r:z(t,r):r}function X(t,e,i){return("video"===t||"audio"===t)&&"autoplay"===e}function J(t,e,i){if(!t)return!1;if(t.nodeType!==t.ELEMENT_NODE)return!!i&&J(b.parentNode(t),e,i);for(var r=t.classList.length;r--;){var n=t.classList[r];if(e.test(n))return!0}return!!i&&J(b.parentNode(t),e,i)}function K(t,e,i,r){var n;if(_(t)){if(n=t,!b.childNodes(n).length)return!1}else{if(null===b.parentElement(t))return!1;n=b.parentElement(t)}try{if("string"==typeof e){if(r){if(n.closest("."+e))return!0}else if(n.classList.contains(e))return!0}else if(J(n,e,r))return!0;if(i)if(r){if(n.closest(i))return!0}else if(n.matches(i))return!0}catch(t){}return!1}function Q(t,e){var{doc:i,mirror:r,blockClass:n,blockSelector:o,needsMask:s,inlineStylesheet:a,maskInputOptions:l={},maskTextFn:u,maskInputFn:c,dataURLOptions:d={},inlineImages:v,recordCanvas:f,keepIframeSrcFn:p,newlyAddedElement:g=!1}=e,m=function(t,e){if(!e.hasNode(t))return;var i=e.getId(t);return 1===i?void 0:i}(i,r);switch(t.nodeType){case t.DOCUMENT_NODE:return"CSS1Compat"!==t.compatMode?{type:h.Document,childNodes:[],compatMode:t.compatMode}:{type:h.Document,childNodes:[]};case t.DOCUMENT_TYPE_NODE:return{type:h.DocumentType,name:t.name,publicId:t.publicId,systemId:t.systemId,rootId:m};case t.ELEMENT_NODE:return function(t,e){for(var i,{doc:r,blockClass:n,blockSelector:o,inlineStylesheet:s,maskInputOptions:a={},maskInputFn:l,dataURLOptions:u={},inlineImages:c,recordCanvas:d,keepIframeSrcFn:v,newlyAddedElement:f=!1,rootId:p}=e,g=function(t,e,i){try{if("string"==typeof e){if(t.classList.contains(e))return!0}else for(var r=t.classList.length;r--;){var n=t.classList[r];if(e.test(n))return!0}if(i)return t.matches(i)}catch(t){}return!1}(t,n,o),m=function(t){if(t instanceof HTMLFormElement)return"form";var e=T(t.tagName);return Z.test(e)?"div":e}(t),y={},_=t.attributes.length,w=0;w<_;w++){var I=t.attributes[w];X(m,I.name,I.value)||(y[I.name]=U(r,m,T(I.name),I.value))}if("link"===m&&s){var S=function(t){return null==t?void 0:t.href}(t);if(S){var x=k(r,S);if(!x&&S.includes(".css"))x=k(r,window.location.origin+"/"+S.replace(window.location.href,""));var N=null;x&&(N=C(x)),N&&(delete y.rel,delete y.href,y._cssText=N)}}if("style"===m&&t.sheet&&!(t.innerText||b.textContent(t)||"").trim().length){var E=C(t.sheet);E&&(y._cssText=E)}if("input"===m||"textarea"===m||"select"===m){var F=t.value,O=t.checked;"radio"!==y.type&&"checkbox"!==y.type&&"submit"!==y.type&&"button"!==y.type&&F?y.value=A({element:t,type:R(t),tagName:m,value:F,maskInputOptions:a,maskInputFn:l}):O&&(y.checked=O)}"option"===m&&(t.selected&&!a.select?y.selected=!0:delete y.selected);if("dialog"===m&&t.open)try{y.rr_open_mode=t.matches("dialog:modal")?"modal":"non-modal"}catch(t){y.rr_open_mode="modal",y.ph_rr_could_not_detect_modal=!0}if("canvas"===m&&d)if("2d"===t.__context)(function(t){var e=t.getContext("2d");if(!e)return!0;for(var i=0;i<t.width;i+=50)for(var r=0;r<t.height;r+=50){var n=e.getImageData,o=M in n?n[M]:n;if(new Uint32Array(o.call(e,i,r,Math.min(50,t.width-i),Math.min(50,t.height-r)).data.buffer).some((t=>0!==t)))return!1}return!0})(t)||(y.rr_dataURL=t.toDataURL(u.type,u.quality));else if(!("__context"in t)){var P=t.toDataURL(u.type,u.quality),D=r.createElement("canvas");D.width=t.width,D.height=t.height,P!==D.toDataURL(u.type,u.quality)&&(y.rr_dataURL=P)}if("img"===m&&c){L||(L=r.createElement("canvas"),B=L.getContext("2d"));var $=t,q=$.currentSrc||$.getAttribute("src")||"<unknown-src>",H=$.crossOrigin,V=()=>{$.removeEventListener("load",V);try{L.width=$.naturalWidth,L.height=$.naturalHeight,B.drawImage($,0,0),y.rr_dataURL=L.toDataURL(u.type,u.quality)}catch(t){if("anonymous"!==$.crossOrigin)return $.crossOrigin="anonymous",void($.complete&&0!==$.naturalWidth?V():$.addEventListener("load",V));console.warn("Cannot inline img src="+q+"! Error: "+t)}"anonymous"===$.crossOrigin&&(H?y.crossOrigin=H:$.removeAttribute("crossorigin"))};$.complete&&0!==$.naturalWidth?V():$.addEventListener("load",V)}if("audio"===m||"video"===m){var j=y;j.rr_mediaState=t.paused?"paused":"played",j.rr_mediaCurrentTime=t.currentTime,j.rr_mediaPlaybackRate=t.playbackRate,j.rr_mediaMuted=t.muted,j.rr_mediaLoop=t.loop,j.rr_mediaVolume=t.volume}f||(t.scrollLeft&&(y.rr_scrollLeft=t.scrollLeft),t.scrollTop&&(y.rr_scrollTop=t.scrollTop));if(g){var{width:Y,height:z}=t.getBoundingClientRect();y={class:y.class,rr_width:Y+"px",rr_height:z+"px"}}"iframe"!==m||v(y.src)||(t.contentDocument||(y.rr_src=y.src),delete y.src);try{customElements.get(m)&&(i=!0)}catch(t){}return{type:h.Element,tagName:m,attributes:y,childNodes:[],isSVG:G(t)||void 0,needBlock:g,rootId:p,isCustom:i}}(t,{doc:i,blockClass:n,blockSelector:o,inlineStylesheet:a,maskInputOptions:l,maskInputFn:c,dataURLOptions:d,inlineImages:v,recordCanvas:f,keepIframeSrcFn:p,newlyAddedElement:g,rootId:m});case t.TEXT_NODE:return function(t,e){var i,{needsMask:r,maskTextFn:n,rootId:o}=e,s=b.parentNode(t),a=s&&s.tagName,l=b.textContent(t),u="STYLE"===a||void 0,c="SCRIPT"===a||void 0;if(u&&l){try{t.nextSibling||t.previousSibling||(null==(i=s.sheet)?void 0:i.cssRules)&&(l=C(s.sheet))}catch(e){console.warn("Cannot get CSS styles from text's parentNode. Error: "+e,t)}l=D(l,W(e.doc))}c&&(l="SCRIPT_PLACEHOLDER");!u&&!c&&l&&r&&(l=n?n(l,b.parentElement(t)):l.replace(/[\S]/g,"*"));return{type:h.Text,textContent:l||"",isStyle:u,rootId:o}}(t,{doc:i,needsMask:s,maskTextFn:u,rootId:m});case t.CDATA_SECTION_NODE:return{type:h.CDATA,textContent:"",rootId:m};case t.COMMENT_NODE:return{type:h.Comment,textContent:b.textContent(t)||"",rootId:m};default:return!1}}function tt(t){return null==t?"":t.toLowerCase()}function et(t,e){var{doc:i,mirror:r,blockClass:n,blockSelector:o,maskTextClass:s,maskTextSelector:a,skipChild:l=!1,inlineStylesheet:u=!0,maskInputOptions:c={},maskTextFn:d,maskInputFn:v,slimDOMOptions:f,dataURLOptions:p={},inlineImages:g=!1,recordCanvas:m=!1,onSerialize:y,onIframeLoad:C,iframeLoadTimeout:S=5e3,onStylesheetLoad:k,stylesheetLoadTimeout:x=5e3,keepIframeSrcFn:A=(()=>!1),newlyAddedElement:T=!1}=e,{needsMask:M}=e,{preserveWhiteSpace:R=!0}=e;M||(M=K(t,s,a,void 0===M));var E,F=Q(t,{doc:i,mirror:r,blockClass:n,blockSelector:o,needsMask:M,inlineStylesheet:u,maskInputOptions:c,maskTextFn:d,maskInputFn:v,dataURLOptions:p,inlineImages:g,recordCanvas:m,keepIframeSrcFn:A,newlyAddedElement:T});if(!F)return console.warn(t,"not serialized"),null;E=r.hasNode(t)?r.getId(t):!function(t,e){if(e.comment&&t.type===h.Comment)return!0;if(t.type===h.Element){if(e.script&&("script"===t.tagName||"link"===t.tagName&&("preload"===t.attributes.rel||"modulepreload"===t.attributes.rel)&&"script"===t.attributes.as||"link"===t.tagName&&"prefetch"===t.attributes.rel&&"string"==typeof t.attributes.href&&"js"===N(t.attributes.href)))return!0;if(e.headFavicon&&("link"===t.tagName&&"shortcut icon"===t.attributes.rel||"meta"===t.tagName&&(tt(t.attributes.name).match(/^msapplication-tile(image|color)$/)||"application-name"===tt(t.attributes.name)||"icon"===tt(t.attributes.rel)||"apple-touch-icon"===tt(t.attributes.rel)||"shortcut icon"===tt(t.attributes.rel))))return!0;if("meta"===t.tagName){if(e.headMetaDescKeywords&&tt(t.attributes.name).match(/^description|keywords$/))return!0;if(e.headMetaSocial&&(tt(t.attributes.property).match(/^(og|twitter|fb):/)||tt(t.attributes.name).match(/^(og|twitter):/)||"pinterest"===tt(t.attributes.name)))return!0;if(e.headMetaRobots&&("robots"===tt(t.attributes.name)||"googlebot"===tt(t.attributes.name)||"bingbot"===tt(t.attributes.name)))return!0;if(e.headMetaHttpEquiv&&void 0!==t.attributes["http-equiv"])return!0;if(e.headMetaAuthorship&&("author"===tt(t.attributes.name)||"generator"===tt(t.attributes.name)||"framework"===tt(t.attributes.name)||"publisher"===tt(t.attributes.name)||"progid"===tt(t.attributes.name)||tt(t.attributes.property).match(/^article:/)||tt(t.attributes.property).match(/^product:/)))return!0;if(e.headMetaVerification&&("google-site-verification"===tt(t.attributes.name)||"yandex-verification"===tt(t.attributes.name)||"csrf-token"===tt(t.attributes.name)||"p:domain_verify"===tt(t.attributes.name)||"verify-v1"===tt(t.attributes.name)||"verification"===tt(t.attributes.name)||"shopify-checkout-api-token"===tt(t.attributes.name)))return!0}}return!1}(F,f)&&(R||F.type!==h.Text||F.isStyle||F.textContent.replace(/^\s+|\s+$/gm,"").length)?H():q;var O=Object.assign(F,{id:E});if(r.add(t,O),E===q)return null;y&&y(t);var P=!l;if(O.type===h.Element){P=P&&!O.needBlock,delete O.needBlock;var D=b.shadowRoot(t);D&&I(D)&&(O.isShadowHost=!0)}if((O.type===h.Document||O.type===h.Element)&&P){f.headWhitespace&&O.type===h.Element&&"head"===O.tagName&&(R=!1);var L={doc:i,mirror:r,blockClass:n,blockSelector:o,needsMask:M,maskTextClass:s,maskTextSelector:a,skipChild:l,inlineStylesheet:u,maskInputOptions:c,maskTextFn:d,maskInputFn:v,slimDOMOptions:f,dataURLOptions:p,inlineImages:g,recordCanvas:m,preserveWhiteSpace:R,onSerialize:y,onIframeLoad:C,iframeLoadTimeout:S,onStylesheetLoad:k,stylesheetLoadTimeout:x,keepIframeSrcFn:A};if(O.type===h.Element&&"textarea"===O.tagName&&void 0!==O.attributes.value);else for(var B of Array.from(b.childNodes(t))){var $=et(B,L);$&&O.childNodes.push($)}var Z=null;if(_(t)&&(Z=b.shadowRoot(t)))for(var V of Array.from(b.childNodes(Z))){var j=et(V,L);j&&(I(Z)&&(j.isShadow=!0),O.childNodes.push(j))}}var Y=b.parentNode(t);return Y&&w(Y)&&I(Y)&&(O.isShadow=!0),O.type===h.Element&&"iframe"===O.tagName&&function(t,e,i){var r=t.contentWindow;if(r){var n,o=!1;try{n=r.document.readyState}catch(t){return}if("complete"===n){var s="about:blank";if(r.location.href!==s||t.src===s||""===t.src)return setTimeout(e,0),t.addEventListener("load",e);t.addEventListener("load",e)}else{var a=setTimeout((()=>{o||(e(),o=!0)}),i);t.addEventListener("load",(()=>{clearTimeout(a),o=!0,e()}))}}}(t,(()=>{var e=t.contentDocument;if(e&&C){var i=et(e,{doc:e,mirror:r,blockClass:n,blockSelector:o,needsMask:M,maskTextClass:s,maskTextSelector:a,skipChild:!1,inlineStylesheet:u,maskInputOptions:c,maskTextFn:d,maskInputFn:v,slimDOMOptions:f,dataURLOptions:p,inlineImages:g,recordCanvas:m,preserveWhiteSpace:R,onSerialize:y,onIframeLoad:C,iframeLoadTimeout:S,onStylesheetLoad:k,stylesheetLoadTimeout:x,keepIframeSrcFn:A});i&&C(t,i)}}),S),O.type===h.Element&&"link"===O.tagName&&"string"==typeof O.attributes.rel&&("stylesheet"===O.attributes.rel||"preload"===O.attributes.rel&&"string"==typeof O.attributes.href&&"css"===N(O.attributes.href))&&function(t,e,i){var r,n=!1;try{r=t.sheet}catch(t){return}if(!r){var o=setTimeout((()=>{n||(e(),n=!0)}),i);t.addEventListener("load",(()=>{clearTimeout(o),n=!0,e()}))}}(t,(()=>{if(k){var e=et(t,{doc:i,mirror:r,blockClass:n,blockSelector:o,needsMask:M,maskTextClass:s,maskTextSelector:a,skipChild:!1,inlineStylesheet:u,maskInputOptions:c,maskTextFn:d,maskInputFn:v,slimDOMOptions:f,dataURLOptions:p,inlineImages:g,recordCanvas:m,preserveWhiteSpace:R,onSerialize:y,onIframeLoad:C,iframeLoadTimeout:S,onStylesheetLoad:k,stylesheetLoadTimeout:x,keepIframeSrcFn:A});e&&k(t,e)}}),x),O}let it=class t{constructor(){__publicField2(this,"parentElement",null),__publicField2(this,"parentNode",null),__publicField2(this,"ownerDocument"),__publicField2(this,"firstChild",null),__publicField2(this,"lastChild",null),__publicField2(this,"previousSibling",null),__publicField2(this,"nextSibling",null),__publicField2(this,"ELEMENT_NODE",1),__publicField2(this,"TEXT_NODE",3),__publicField2(this,"nodeType"),__publicField2(this,"nodeName"),__publicField2(this,"RRNodeType")}get childNodes(){for(var t=[],e=this.firstChild;e;)t.push(e),e=e.nextSibling;return t}contains(e){if(!(e instanceof t))return!1;if(e.ownerDocument!==this.ownerDocument)return!1;if(e===this)return!0;for(;e.parentNode;){if(e.parentNode===this)return!0;e=e.parentNode}return!1}appendChild(t){throw new Error("RRDomException: Failed to execute 'appendChild' on 'RRNode': This RRNode type does not support this method.")}insertBefore(t,e){throw new Error("RRDomException: Failed to execute 'insertBefore' on 'RRNode': This RRNode type does not support this method.")}removeChild(t){throw new Error("RRDomException: Failed to execute 'removeChild' on 'RRNode': This RRNode type does not support this method.")}toString(){return"RRNode"}};var rt={Node:["childNodes","parentNode","parentElement","textContent"],ShadowRoot:["host","styleSheets"],Element:["shadowRoot","querySelector","querySelectorAll"],MutationObserver:[]},nt={Node:["contains","getRootNode"],ShadowRoot:["getSelection"],Element:[],MutationObserver:["constructor"]};var ot={};function st(t){if(ot[t])return ot[t];var e=function(t){var e,i=null==globalThis||null==(e=globalThis.Zone)||null==e.__symbol__?void 0:e.__symbol__(t);return i&&globalThis[i]?globalThis[i]:void 0}(t)||globalThis[t],i=e.prototype,r=t in rt?rt[t]:void 0,n=Boolean(r&&r.every((t=>{var e,r;return Boolean(null==(r=null==(e=Object.getOwnPropertyDescriptor(i,t))?void 0:e.get)?void 0:r.toString().includes("[native code]"))}))),o=t in nt?nt[t]:void 0,s=Boolean(o&&o.every((t=>{var e;return"function"==typeof i[t]&&(null==(e=i[t])?void 0:e.toString().includes("[native code]"))})));if(n&&s)return ot[t]=e.prototype,e.prototype;try{var a=document.createElement("iframe");document.body.appendChild(a);var l=a.contentWindow;if(!l)return e.prototype;var u=l[t].prototype;return document.body.removeChild(a),u?ot[t]=u:i}catch(t){return i}}var at={};function lt(t,e,i){var r,n=t+"."+String(i);if(at[n])return at[n].call(e);var o=st(t),s=null==(r=Object.getOwnPropertyDescriptor(o,i))?void 0:r.get;return s?(at[n]=s,s.call(e)):e[i]}var ut={};function ht(t,e,i){var r=t+"."+String(i);if(ut[r])return ut[r].bind(e);var n=st(t)[i];return"function"!=typeof n?e[i]:(ut[r]=n,n.bind(e))}function ct(){return st("MutationObserver").constructor}var dt={childNodes:function(t){return lt("Node",t,"childNodes")},parentNode:function(t){return lt("Node",t,"parentNode")},parentElement:function(t){return lt("Node",t,"parentElement")},textContent:function(t){return lt("Node",t,"textContent")},contains:function(t,e){return ht("Node",t,"contains")(e)},getRootNode:function(t){return ht("Node",t,"getRootNode")()},host:function(t){return t&&"host"in t?lt("ShadowRoot",t,"host"):null},styleSheets:function(t){return t.styleSheets},shadowRoot:function(t){return t&&"shadowRoot"in t?lt("Element",t,"shadowRoot"):null},querySelector:function(t,e){return lt("Element",t,"querySelector")(e)},querySelectorAll:function(t,e){return lt("Element",t,"querySelectorAll")(e)},mutationObserver:ct};function vt(t,e,i){void 0===i&&(i=document);var r={capture:!0,passive:!0};return i.addEventListener(t,e,r),()=>i.removeEventListener(t,e,r)}var ft="Please stop import mirror directly. Instead of that,\r\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\r\nor you can use record.mirror to access the mirror instance during recording.",pt={map:{},getId:()=>(console.error(ft),-1),getNode:()=>(console.error(ft),null),removeNodeFromMap(){console.error(ft)},has:()=>(console.error(ft),!1),reset(){console.error(ft)}};function gt(t,e,i){void 0===i&&(i={});var r=null,n=0;return function(){for(var o=arguments.length,s=new Array(o),a=0;a<o;a++)s[a]=arguments[a];var l=Date.now();n||!1!==i.leading||(n=l);var u=e-(l-n),h=this;u<=0||u>e?(r&&(clearTimeout(r),r=null),n=l,t.apply(h,s)):r||!1===i.trailing||(r=setTimeout((()=>{n=!1===i.leading?0:Date.now(),r=null,t.apply(h,s)}),u))}}function mt(t,e,i,r,n){void 0===n&&(n=window);var o=n.Object.getOwnPropertyDescriptor(t,e);return n.Object.defineProperty(t,e,r?i:{set(t){setTimeout((()=>{i.set.call(this,t)}),0),o&&o.set&&o.set.call(this,t)}}),()=>mt(t,e,o||{},!0)}function yt(t,e,i){try{if(!(e in t))return()=>{};var r=t[e],n=i(r);return"function"==typeof n&&(n.prototype=n.prototype||{},Object.defineProperties(n,{__rrweb_original__:{enumerable:!1,value:r}})),t[e]=n,()=>{t[e]=r}}catch(t){return()=>{}}}"undefined"!=typeof window&&window.Proxy&&window.Reflect&&(pt=new Proxy(pt,{get:(t,e,i)=>("map"===e&&console.error(ft),Reflect.get(t,e,i))}));var bt=Date.now;function _t(t){var e,i,r,n,o=t.document;return{left:o.scrollingElement?o.scrollingElement.scrollLeft:void 0!==t.pageXOffset?t.pageXOffset:o.documentElement.scrollLeft||(null==o?void 0:o.body)&&(null==(e=dt.parentElement(o.body))?void 0:e.scrollLeft)||(null==(i=null==o?void 0:o.body)?void 0:i.scrollLeft)||0,top:o.scrollingElement?o.scrollingElement.scrollTop:void 0!==t.pageYOffset?t.pageYOffset:(null==o?void 0:o.documentElement.scrollTop)||(null==o?void 0:o.body)&&(null==(r=dt.parentElement(o.body))?void 0:r.scrollTop)||(null==(n=null==o?void 0:o.body)?void 0:n.scrollTop)||0}}function wt(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight}function It(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth}function Ct(t){return t?t.nodeType===t.ELEMENT_NODE?t:dt.parentElement(t):null}function St(t,e,i,r){if(!t)return!1;var n=Ct(t);if(!n)return!1;try{if("string"==typeof e){if(n.classList.contains(e))return!0;if(r&&null!==n.closest("."+e))return!0}else if(J(n,e,r))return!0}catch(t){}if(i){if(n.matches(i))return!0;if(r&&null!==n.closest(i))return!0}return!1}function kt(t,e,i){return!("TITLE"!==t.tagName||!i.headTitleMutations)||e.getId(t)===q}function xt(t,e){if(w(t))return!1;var i=e.getId(t);if(!e.has(i))return!0;var r=dt.parentNode(t);return(!r||r.nodeType!==t.DOCUMENT_NODE)&&(!r||xt(r,e))}function At(t){return Boolean(t.changedTouches)}function Tt(t,e){return Boolean("IFRAME"===t.nodeName&&e.getMeta(t))}function Mt(t,e){return Boolean("LINK"===t.nodeName&&t.nodeType===t.ELEMENT_NODE&&t.getAttribute&&"stylesheet"===t.getAttribute("rel")&&e.getMeta(t))}function Rt(t){return!!t&&(t instanceof it&&"shadowRoot"in t?Boolean(t.shadowRoot):Boolean(dt.shadowRoot(t)))}/[1-9][0-9]{12}/.test(Date.now().toString())||(bt=()=>(new Date).getTime());let Nt=class{constructor(){a(this,"id",1),a(this,"styleIDMap",new WeakMap),a(this,"idStyleMap",new Map)}getId(t){var e;return null!==(e=this.styleIDMap.get(t))&&void 0!==e?e:-1}has(t){return this.styleIDMap.has(t)}add(t,e){return this.has(t)?this.getId(t):(i=void 0===e?this.id++:e,this.styleIDMap.set(t,i),this.idStyleMap.set(i,t),i);var i}getStyle(t){return this.idStyleMap.get(t)||null}reset(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}generateId(){return this.id++}};function Et(t){var e,i=null;return"getRootNode"in t&&(null==(e=dt.getRootNode(t))?void 0:e.nodeType)===Node.DOCUMENT_FRAGMENT_NODE&&dt.host(dt.getRootNode(t))&&(i=dt.host(dt.getRootNode(t))),i}function Ft(t){var e=t.ownerDocument;if(!e)return!1;var i=function(t){for(var e,i=t;e=Et(i);)i=e;return i}(t);return dt.contains(e,i)}function Ot(t){var e=t.ownerDocument;return!!e&&(dt.contains(e,t)||Ft(t))}var Pt=(t=>(t[t.DomContentLoaded=0]="DomContentLoaded",t[t.Load=1]="Load",t[t.FullSnapshot=2]="FullSnapshot",t[t.IncrementalSnapshot=3]="IncrementalSnapshot",t[t.Meta=4]="Meta",t[t.Custom=5]="Custom",t[t.Plugin=6]="Plugin",t))(Pt||{}),Dt=(t=>(t[t.Mutation=0]="Mutation",t[t.MouseMove=1]="MouseMove",t[t.MouseInteraction=2]="MouseInteraction",t[t.Scroll=3]="Scroll",t[t.ViewportResize=4]="ViewportResize",t[t.Input=5]="Input",t[t.TouchMove=6]="TouchMove",t[t.MediaInteraction=7]="MediaInteraction",t[t.StyleSheetRule=8]="StyleSheetRule",t[t.CanvasMutation=9]="CanvasMutation",t[t.Font=10]="Font",t[t.Log=11]="Log",t[t.Drag=12]="Drag",t[t.StyleDeclaration=13]="StyleDeclaration",t[t.Selection=14]="Selection",t[t.AdoptedStyleSheet=15]="AdoptedStyleSheet",t[t.CustomElement=16]="CustomElement",t))(Dt||{}),Lt=(t=>(t[t.MouseUp=0]="MouseUp",t[t.MouseDown=1]="MouseDown",t[t.Click=2]="Click",t[t.ContextMenu=3]="ContextMenu",t[t.DblClick=4]="DblClick",t[t.Focus=5]="Focus",t[t.Blur=6]="Blur",t[t.TouchStart=7]="TouchStart",t[t.TouchMove_Departed=8]="TouchMove_Departed",t[t.TouchEnd=9]="TouchEnd",t[t.TouchCancel=10]="TouchCancel",t))(Lt||{}),Bt=(t=>(t[t.Mouse=0]="Mouse",t[t.Pen=1]="Pen",t[t.Touch=2]="Touch",t))(Bt||{}),$t=(t=>(t[t["2D"]=0]="2D",t[t.WebGL=1]="WebGL",t[t.WebGL2=2]="WebGL2",t))($t||{}),Zt=(t=>(t[t.Play=0]="Play",t[t.Pause=1]="Pause",t[t.Seeked=2]="Seeked",t[t.VolumeChange=3]="VolumeChange",t[t.RateChange=4]="RateChange",t))(Zt||{});function qt(t){return"__ln"in t}class Ht{constructor(){a(this,"length",0),a(this,"head",null),a(this,"tail",null)}get(t){if(t>=this.length)throw new Error("Position outside of list range");for(var e=this.head,i=0;i<t;i++)e=(null==e?void 0:e.next)||null;return e}addNode(t){var e={value:t,previous:null,next:null};if(t.__ln=e,t.previousSibling&&qt(t.previousSibling)){var i=t.previousSibling.__ln.next;e.next=i,e.previous=t.previousSibling.__ln,t.previousSibling.__ln.next=e,i&&(i.previous=e)}else if(t.nextSibling&&qt(t.nextSibling)&&t.nextSibling.__ln.previous){var r=t.nextSibling.__ln.previous;e.previous=r,e.next=t.nextSibling.__ln,t.nextSibling.__ln.previous=e,r&&(r.next=e)}else this.head&&(this.head.previous=e),e.next=this.head,this.head=e;null===e.next&&(this.tail=e),this.length++}removeNode(t){var e=t.__ln;this.head&&(e.previous?(e.previous.next=e.next,e.next?e.next.previous=e.previous:this.tail=e.previous):(this.head=e.next,this.head?this.head.previous=null:this.tail=null),t.__ln&&delete t.__ln,this.length--)}}var Vt,jt=(t,e)=>t+"@"+e;class Yt{constructor(){a(this,"frozen",!1),a(this,"locked",!1),a(this,"texts",[]),a(this,"attributes",[]),a(this,"attributeMap",new WeakMap),a(this,"removes",[]),a(this,"mapRemoves",[]),a(this,"movedMap",{}),a(this,"addedSet",new Set),a(this,"movedSet",new Set),a(this,"droppedSet",new Set),a(this,"mutationCb"),a(this,"blockClass"),a(this,"blockSelector"),a(this,"maskTextClass"),a(this,"maskTextSelector"),a(this,"inlineStylesheet"),a(this,"maskInputOptions"),a(this,"maskTextFn"),a(this,"maskInputFn"),a(this,"keepIframeSrcFn"),a(this,"recordCanvas"),a(this,"inlineImages"),a(this,"slimDOMOptions"),a(this,"dataURLOptions"),a(this,"doc"),a(this,"mirror"),a(this,"iframeManager"),a(this,"stylesheetManager"),a(this,"shadowDomManager"),a(this,"canvasManager"),a(this,"processedNodeManager"),a(this,"unattachedDoc"),a(this,"processMutations",(t=>{t.forEach(this.processMutation),this.emit()})),a(this,"emit",(()=>{if(!this.frozen&&!this.locked){for(var t=[],e=new Set,i=new Ht,r=t=>{for(var e=t,i=q;i===q;)i=(e=e&&e.nextSibling)&&this.mirror.getId(e);return i},n=n=>{var o=dt.parentNode(n);if(o&&Ot(n)&&"TEXTAREA"!==o.tagName){var s=w(o)?this.mirror.getId(Et(n)):this.mirror.getId(o),a=r(n);if(-1===s||-1===a)return i.addNode(n);var l=et(n,{doc:this.doc,mirror:this.mirror,blockClass:this.blockClass,blockSelector:this.blockSelector,maskTextClass:this.maskTextClass,maskTextSelector:this.maskTextSelector,skipChild:!0,newlyAddedElement:!0,inlineStylesheet:this.inlineStylesheet,maskInputOptions:this.maskInputOptions,maskTextFn:this.maskTextFn,maskInputFn:this.maskInputFn,slimDOMOptions:this.slimDOMOptions,dataURLOptions:this.dataURLOptions,recordCanvas:this.recordCanvas,inlineImages:this.inlineImages,onSerialize:t=>{Tt(t,this.mirror)&&this.iframeManager.addIframe(t),Mt(t,this.mirror)&&this.stylesheetManager.trackLinkElement(t),Rt(n)&&this.shadowDomManager.addShadowRoot(dt.shadowRoot(n),this.doc)},onIframeLoad:(t,e)=>{this.iframeManager.attachIframe(t,e),this.shadowDomManager.observeAttachShadow(t)},onStylesheetLoad:(t,e)=>{this.stylesheetManager.attachLinkElement(t,e)}});l&&(t.push({parentId:s,nextId:a,node:l}),e.add(l.id))}};this.mapRemoves.length;)this.mirror.removeNodeFromMap(this.mapRemoves.shift());for(var o of this.movedSet)Gt(this.removes,o,this.mirror)&&!this.movedSet.has(dt.parentNode(o))||n(o);for(var s of this.addedSet)Wt(this.droppedSet,s)||Gt(this.removes,s,this.mirror)?Wt(this.movedSet,s)?n(s):this.droppedSet.add(s):n(s);for(var a=null;i.length;){var l=null;if(a){var u=this.mirror.getId(dt.parentNode(a.value)),h=r(a.value);-1!==u&&-1!==h&&(l=a)}if(!l)for(var c=i.tail;c;){var d=c;if(c=c.previous,d){var v=this.mirror.getId(dt.parentNode(d.value));if(-1===r(d.value))continue;if(-1!==v){l=d;break}var f=d.value,p=dt.parentNode(f);if(p&&p.nodeType===Node.DOCUMENT_FRAGMENT_NODE){var g=dt.host(p);if(-1!==this.mirror.getId(g)){l=d;break}}}}if(!l){for(;i.head;)i.removeNode(i.head.value);break}a=l.previous,i.removeNode(l.value),n(l.value)}var m={texts:this.texts.map((t=>{var e=t.node,i=dt.parentNode(e);return i&&"TEXTAREA"===i.tagName&&this.genTextAreaValueMutation(i),{id:this.mirror.getId(e),value:t.value}})).filter((t=>!e.has(t.id))).filter((t=>this.mirror.has(t.id))),attributes:this.attributes.map((t=>{var{attributes:e}=t;if("string"==typeof e.style){var i=JSON.stringify(t.styleDiff),r=JSON.stringify(t._unchangedStyles);i.length<e.style.length&&(i+r).split("var(").length===e.style.split("var(").length&&(e.style=t.styleDiff)}return{id:this.mirror.getId(t.node),attributes:e}})).filter((t=>!e.has(t.id))).filter((t=>this.mirror.has(t.id))),removes:this.removes,adds:t};(m.texts.length||m.attributes.length||m.removes.length||m.adds.length)&&(this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.movedMap={},this.mutationCb(m))}})),a(this,"genTextAreaValueMutation",(t=>{var e=this.attributeMap.get(t);e||(e={node:t,attributes:{},styleDiff:{},_unchangedStyles:{}},this.attributes.push(e),this.attributeMap.set(t,e)),e.attributes.value=Array.from(dt.childNodes(t),(t=>dt.textContent(t)||"")).join("")})),a(this,"processMutation",(t=>{if(!kt(t.target,this.mirror,this.slimDOMOptions))switch(t.type){case"characterData":var e=dt.textContent(t.target);St(t.target,this.blockClass,this.blockSelector,!1)||e===t.oldValue||this.texts.push({value:K(t.target,this.maskTextClass,this.maskTextSelector,!0)&&e?this.maskTextFn?this.maskTextFn(e,Ct(t.target)):e.replace(/[\S]/g,"*"):e,node:t.target});break;case"attributes":var i=t.target,r=t.attributeName,n=t.target.getAttribute(r);if("value"===r){var o=R(i);n=A({element:i,maskInputOptions:this.maskInputOptions,tagName:i.tagName,type:o,value:n,maskInputFn:this.maskInputFn})}if(St(t.target,this.blockClass,this.blockSelector,!1)||n===t.oldValue)return;var s=this.attributeMap.get(t.target);if("IFRAME"===i.tagName&&"src"===r&&!this.keepIframeSrcFn(n)){if(i.contentDocument)return;r="rr_src"}if(s||(s={node:t.target,attributes:{},styleDiff:{},_unchangedStyles:{}},this.attributes.push(s),this.attributeMap.set(t.target,s)),"type"===r&&"INPUT"===i.tagName&&"password"===(t.oldValue||"").toLowerCase()&&i.setAttribute("data-rr-is-password","true"),!X(i.tagName,r))if(s.attributes[r]=U(this.doc,T(i.tagName),T(r),n),"style"===r){if(!this.unattachedDoc)try{this.unattachedDoc=document.implementation.createHTMLDocument()}catch(t){this.unattachedDoc=this.doc}var a=this.unattachedDoc.createElement("span");for(var l of(t.oldValue&&a.setAttribute("style",t.oldValue),Array.from(i.style))){var u=i.style.getPropertyValue(l),h=i.style.getPropertyPriority(l);u!==a.style.getPropertyValue(l)||h!==a.style.getPropertyPriority(l)?s.styleDiff[l]=""===h?u:[u,h]:s._unchangedStyles[l]=[u,h]}for(var c of Array.from(a.style))""===i.style.getPropertyValue(c)&&(s.styleDiff[c]=!1)}else"open"===r&&"DIALOG"===i.tagName&&(i.matches("dialog:modal")?s.attributes.rr_open_mode="modal":s.attributes.rr_open_mode="non-modal");break;case"childList":if(St(t.target,this.blockClass,this.blockSelector,!0))return;if("TEXTAREA"===t.target.tagName)return void this.genTextAreaValueMutation(t.target);t.addedNodes.forEach((e=>this.genAdds(e,t.target))),t.removedNodes.forEach((e=>{var i=this.mirror.getId(e),r=w(t.target)?this.mirror.getId(dt.host(t.target)):this.mirror.getId(t.target);St(t.target,this.blockClass,this.blockSelector,!1)||kt(e,this.mirror,this.slimDOMOptions)||!function(t,e){return-1!==e.getId(t)}(e,this.mirror)||(this.addedSet.has(e)?(zt(this.addedSet,e),this.droppedSet.add(e)):this.addedSet.has(t.target)&&-1===i||xt(t.target,this.mirror)||(this.movedSet.has(e)&&this.movedMap[jt(i,r)]?zt(this.movedSet,e):this.removes.push({parentId:r,id:i,isShadow:!(!w(t.target)||!I(t.target))||void 0})),this.mapRemoves.push(e))}))}})),a(this,"genAdds",((t,e)=>{if(!this.processedNodeManager.inOtherBuffer(t,this)&&!this.addedSet.has(t)&&!this.movedSet.has(t)){if(this.mirror.hasNode(t)){if(kt(t,this.mirror,this.slimDOMOptions))return;this.movedSet.add(t);var i=null;e&&this.mirror.hasNode(e)&&(i=this.mirror.getId(e)),i&&-1!==i&&(this.movedMap[jt(this.mirror.getId(t),i)]=!0)}else this.addedSet.add(t),this.droppedSet.delete(t);St(t,this.blockClass,this.blockSelector,!1)||(dt.childNodes(t).forEach((t=>this.genAdds(t))),Rt(t)&&dt.childNodes(dt.shadowRoot(t)).forEach((e=>{this.processedNodeManager.add(e,this),this.genAdds(e,t)})))}}))}init(t){["mutationCb","blockClass","blockSelector","maskTextClass","maskTextSelector","inlineStylesheet","maskInputOptions","maskTextFn","maskInputFn","keepIframeSrcFn","recordCanvas","inlineImages","slimDOMOptions","dataURLOptions","doc","mirror","iframeManager","stylesheetManager","shadowDomManager","canvasManager","processedNodeManager"].forEach((e=>{this[e]=t[e]}))}freeze(){this.frozen=!0,this.canvasManager.freeze()}unfreeze(){this.frozen=!1,this.canvasManager.unfreeze(),this.emit()}isFrozen(){return this.frozen}lock(){this.locked=!0,this.canvasManager.lock()}unlock(){this.locked=!1,this.canvasManager.unlock(),this.emit()}reset(){this.shadowDomManager.reset(),this.canvasManager.reset()}}function zt(t,e){t.delete(e),dt.childNodes(e).forEach((e=>zt(t,e)))}function Gt(t,e,i){return 0!==t.length&&function(t,e,i){var r,n=dt.parentNode(e),o=function(){var e=i.getId(n);if(t.some((t=>t.id===e)))return{v:!0};n=dt.parentNode(n)};for(;n;)if(r=o())return r.v;return!1}(t,e,i)}function Wt(t,e){return 0!==t.size&&Ut(t,e)}function Ut(t,e){var i=dt.parentNode(e);return!!i&&(!!t.has(i)||Ut(t,i))}var Xt=t=>{if(!Vt)return t;return function(){try{return t(...arguments)}catch(t){if(Vt&&!0===Vt(t))return;throw t}}},Jt=[];function Kt(t){try{if("composedPath"in t){var e=t.composedPath();if(e.length)return e[0]}else if("path"in t&&t.path.length)return t.path[0]}catch(t){}return t&&t.target}function Qt(t,e){var i=new Yt;Jt.push(i),i.init(t);var r=new(ct())(Xt(i.processMutations.bind(i)));return r.observe(e,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}),r}function te(t){var{mouseInteractionCb:e,doc:r,mirror:n,blockClass:o,blockSelector:s,sampling:a}=t;if(!1===a.mouseInteraction)return()=>{};var l=!0===a.mouseInteraction||void 0===a.mouseInteraction?{}:a.mouseInteraction,u=[],h=null;return Object.keys(Lt).filter((t=>Number.isNaN(Number(t))&&!t.endsWith("_Departed")&&!1!==l[t])).forEach((t=>{var a=T(t),l=(t=>r=>{var a=Kt(r);if(!St(a,o,s,!0)){var l=null,u=t;if("pointerType"in r){switch(r.pointerType){case"mouse":l=Bt.Mouse;break;case"touch":l=Bt.Touch;break;case"pen":l=Bt.Pen}l===Bt.Touch?Lt[t]===Lt.MouseDown?u="TouchStart":Lt[t]===Lt.MouseUp&&(u="TouchEnd"):Bt.Pen}else At(r)&&(l=Bt.Touch);null!==l?(h=l,(u.startsWith("Touch")&&l===Bt.Touch||u.startsWith("Mouse")&&l===Bt.Mouse)&&(l=null)):Lt[t]===Lt.Click&&(l=h,h=null);var c=At(r)?r.changedTouches[0]:r;if(c){var d=n.getId(a),{clientX:v,clientY:f}=c;Xt(e)(i({type:Lt[u],id:d,x:v,y:f},null!==l&&{pointerType:l}))}}})(t);if(window.PointerEvent)switch(Lt[t]){case Lt.MouseDown:case Lt.MouseUp:a=a.replace("mouse","pointer");break;case Lt.TouchStart:case Lt.TouchEnd:return}u.push(vt(a,l,r))})),Xt((()=>{u.forEach((t=>t()))}))}function ee(t){var{scrollCb:e,doc:i,mirror:r,blockClass:n,blockSelector:o,sampling:s}=t;return vt("scroll",Xt(gt(Xt((t=>{var s=Kt(t);if(s&&!St(s,n,o,!0)){var a=r.getId(s);if(s===i&&i.defaultView){var l=_t(i.defaultView);e({id:a,x:l.left,y:l.top})}else e({id:a,x:s.scrollLeft,y:s.scrollTop})}})),s.scroll||100)),i)}var ie=["INPUT","TEXTAREA","SELECT"],re=new WeakMap;function ne(t){return function(t,e){if(le("CSSGroupingRule")&&t.parentRule instanceof CSSGroupingRule||le("CSSMediaRule")&&t.parentRule instanceof CSSMediaRule||le("CSSSupportsRule")&&t.parentRule instanceof CSSSupportsRule||le("CSSConditionRule")&&t.parentRule instanceof CSSConditionRule){var i=Array.from(t.parentRule.cssRules).indexOf(t);e.unshift(i)}else if(t.parentStyleSheet){var r=Array.from(t.parentStyleSheet.cssRules).indexOf(t);e.unshift(r)}return e}(t,[])}function oe(t,e,i){var r,n;return t?(t.ownerNode?r=e.getId(t.ownerNode):n=i.getId(t),{styleId:n,id:r}):{}}function se(t,e){var i,r,n,{mirror:o,stylesheetManager:s}=t,a=null;a="#document"===e.nodeName?o.getId(e):o.getId(dt.host(e));var l="#document"===e.nodeName?null==(i=e.defaultView)?void 0:i.Document:null==(n=null==(r=e.ownerDocument)?void 0:r.defaultView)?void 0:n.ShadowRoot,u=(null==l?void 0:l.prototype)?Object.getOwnPropertyDescriptor(null==l?void 0:l.prototype,"adoptedStyleSheets"):void 0;return null!==a&&-1!==a&&l&&u?(Object.defineProperty(e,"adoptedStyleSheets",{configurable:u.configurable,enumerable:u.enumerable,get(){var t;return null==(t=u.get)?void 0:t.call(this)},set(t){var e,i=null==(e=u.set)?void 0:e.call(this,t);if(null!==a&&-1!==a)try{s.adoptStyleSheets(t,a)}catch(t){}return i}}),Xt((()=>{Object.defineProperty(e,"adoptedStyleSheets",{configurable:u.configurable,enumerable:u.enumerable,get:u.get,set:u.set})}))):()=>{}}function ae(t,e){void 0===e&&(e={});var r,n=t.doc.defaultView;if(!n)return()=>{};!function(t,e){var{mutationCb:i,mousemoveCb:r,mouseInteractionCb:n,scrollCb:o,viewportResizeCb:s,inputCb:a,mediaInteractionCb:l,styleSheetRuleCb:u,styleDeclarationCb:h,canvasMutationCb:c,fontCb:d,selectionCb:v,customElementCb:f}=t;t.mutationCb=function(){e.mutation&&e.mutation(...arguments),i(...arguments)},t.mousemoveCb=function(){e.mousemove&&e.mousemove(...arguments),r(...arguments)},t.mouseInteractionCb=function(){e.mouseInteraction&&e.mouseInteraction(...arguments),n(...arguments)},t.scrollCb=function(){e.scroll&&e.scroll(...arguments),o(...arguments)},t.viewportResizeCb=function(){e.viewportResize&&e.viewportResize(...arguments),s(...arguments)},t.inputCb=function(){e.input&&e.input(...arguments),a(...arguments)},t.mediaInteractionCb=function(){e.mediaInteaction&&e.mediaInteaction(...arguments),l(...arguments)},t.styleSheetRuleCb=function(){e.styleSheetRule&&e.styleSheetRule(...arguments),u(...arguments)},t.styleDeclarationCb=function(){e.styleDeclaration&&e.styleDeclaration(...arguments),h(...arguments)},t.canvasMutationCb=function(){e.canvasMutation&&e.canvasMutation(...arguments),c(...arguments)},t.fontCb=function(){e.font&&e.font(...arguments),d(...arguments)},t.selectionCb=function(){e.selection&&e.selection(...arguments),v(...arguments)},t.customElementCb=function(){e.customElement&&e.customElement(...arguments),f(...arguments)}}(t,e),t.recordDOM&&(r=Qt(t,t.doc));var o=function(t){var{mousemoveCb:e,sampling:i,doc:r,mirror:n}=t;if(!1===i.mousemove)return()=>{};var o,s="number"==typeof i.mousemove?i.mousemove:50,a="number"==typeof i.mousemoveCallback?i.mousemoveCallback:500,l=[],u=gt(Xt((t=>{var i=Date.now()-o;e(l.map((t=>(t.timeOffset-=i,t))),t),l=[],o=null})),a),h=Xt(gt(Xt((t=>{var e=Kt(t),{clientX:i,clientY:r}=At(t)?t.changedTouches[0]:t;o||(o=bt()),l.push({x:i,y:r,id:n.getId(e),timeOffset:bt()-o}),u("undefined"!=typeof DragEvent&&t instanceof DragEvent?Dt.Drag:t instanceof MouseEvent?Dt.MouseMove:Dt.TouchMove)})),s,{trailing:!1})),c=[vt("mousemove",h,r),vt("touchmove",h,r),vt("drag",h,r)];return Xt((()=>{c.forEach((t=>t()))}))}(t),s=te(t),a=ee(t),l=function(t,e){var{viewportResizeCb:i}=t,{win:r}=e,n=-1,o=-1;return vt("resize",Xt(gt(Xt((()=>{var t=wt(),e=It();n===t&&o===e||(i({width:Number(e),height:Number(t)}),n=t,o=e)})),200)),r)}(t,{win:n}),u=function(t){var{inputCb:e,doc:r,mirror:n,blockClass:o,blockSelector:s,ignoreClass:a,ignoreSelector:l,maskInputOptions:u,maskInputFn:h,sampling:c,userTriggeredOnInput:d}=t;function v(t){var e=Kt(t),i=t.isTrusted,n=e&&e.tagName;if(e&&"OPTION"===n&&(e=dt.parentElement(e)),e&&n&&!(ie.indexOf(n)<0)&&!St(e,o,s,!0)&&!(e.classList.contains(a)||l&&e.matches(l))){var c=e.value,v=!1,p=R(e)||"";"radio"===p||"checkbox"===p?v=e.checked:(u[n.toLowerCase()]||u[p])&&(c=A({element:e,maskInputOptions:u,tagName:n,type:p,value:c,maskInputFn:h})),f(e,d?{text:c,isChecked:v,userTriggered:i}:{text:c,isChecked:v});var g=e.name;"radio"===p&&g&&v&&r.querySelectorAll('input[type="radio"][name="'+g+'"]').forEach((t=>{if(t!==e){var i=t.value;f(t,d?{text:i,isChecked:!v,userTriggered:!1}:{text:i,isChecked:!v})}}))}}function f(t,r){var o=re.get(t);if(!o||o.text!==r.text||o.isChecked!==r.isChecked){re.set(t,r);var s=n.getId(t);Xt(e)(i({},r,{id:s}))}}var p=("last"===c.input?["change"]:["input","change"]).map((t=>vt(t,Xt(v),r))),g=r.defaultView;if(!g)return()=>{p.forEach((t=>t()))};var m=g.Object.getOwnPropertyDescriptor(g.HTMLInputElement.prototype,"value"),y=[[g.HTMLInputElement.prototype,"value"],[g.HTMLInputElement.prototype,"checked"],[g.HTMLSelectElement.prototype,"value"],[g.HTMLTextAreaElement.prototype,"value"],[g.HTMLSelectElement.prototype,"selectedIndex"],[g.HTMLOptionElement.prototype,"selected"]];return m&&m.set&&p.push(...y.map((t=>mt(t[0],t[1],{set(){Xt(v)({target:this,isTrusted:!1})}},!1,g)))),Xt((()=>{p.forEach((t=>t()))}))}(t),h=function(t){var{mediaInteractionCb:e,blockClass:i,blockSelector:r,mirror:n,sampling:o,doc:s}=t,a=Xt((t=>gt(Xt((o=>{var s=Kt(o);if(s&&!St(s,i,r,!0)){var{currentTime:a,volume:l,muted:u,playbackRate:h,loop:c}=s;e({type:t,id:n.getId(s),currentTime:a,volume:l,muted:u,playbackRate:h,loop:c})}})),o.media||500))),l=[vt("play",a(Zt.Play),s),vt("pause",a(Zt.Pause),s),vt("seeked",a(Zt.Seeked),s),vt("volumechange",a(Zt.VolumeChange),s),vt("ratechange",a(Zt.RateChange),s)];return Xt((()=>{l.forEach((t=>t()))}))}(t),c=()=>{},d=()=>{},v=()=>{},f=()=>{};t.recordDOM&&(c=function(t,e){var{styleSheetRuleCb:i,mirror:r,stylesheetManager:n}=t,{win:o}=e;if(!o.CSSStyleSheet||!o.CSSStyleSheet.prototype)return()=>{};var s=o.CSSStyleSheet.prototype.insertRule;o.CSSStyleSheet.prototype.insertRule=new Proxy(s,{apply:Xt(((t,e,o)=>{var[s,a]=o,{id:l,styleId:u}=oe(e,r,n.styleMirror);return(l&&-1!==l||u&&-1!==u)&&i({id:l,styleId:u,adds:[{rule:s,index:a}]}),t.apply(e,o)}))}),o.CSSStyleSheet.prototype.addRule=function(t,e,i){void 0===i&&(i=this.cssRules.length);var r=t+" { "+e+" }";return o.CSSStyleSheet.prototype.insertRule.apply(this,[r,i])};var a,l,u=o.CSSStyleSheet.prototype.deleteRule;o.CSSStyleSheet.prototype.deleteRule=new Proxy(u,{apply:Xt(((t,e,o)=>{var[s]=o,{id:a,styleId:l}=oe(e,r,n.styleMirror);return(a&&-1!==a||l&&-1!==l)&&i({id:a,styleId:l,removes:[{index:s}]}),t.apply(e,o)}))}),o.CSSStyleSheet.prototype.removeRule=function(t){return o.CSSStyleSheet.prototype.deleteRule.apply(this,[t])},o.CSSStyleSheet.prototype.replace&&(a=o.CSSStyleSheet.prototype.replace,o.CSSStyleSheet.prototype.replace=new Proxy(a,{apply:Xt(((t,e,o)=>{var[s]=o,{id:a,styleId:l}=oe(e,r,n.styleMirror);return(a&&-1!==a||l&&-1!==l)&&i({id:a,styleId:l,replace:s}),t.apply(e,o)}))})),o.CSSStyleSheet.prototype.replaceSync&&(l=o.CSSStyleSheet.prototype.replaceSync,o.CSSStyleSheet.prototype.replaceSync=new Proxy(l,{apply:Xt(((t,e,o)=>{var[s]=o,{id:a,styleId:l}=oe(e,r,n.styleMirror);return(a&&-1!==a||l&&-1!==l)&&i({id:a,styleId:l,replaceSync:s}),t.apply(e,o)}))}));var h={};ue("CSSGroupingRule")?h.CSSGroupingRule=o.CSSGroupingRule:(ue("CSSMediaRule")&&(h.CSSMediaRule=o.CSSMediaRule),ue("CSSConditionRule")&&(h.CSSConditionRule=o.CSSConditionRule),ue("CSSSupportsRule")&&(h.CSSSupportsRule=o.CSSSupportsRule));var c={};return Object.entries(h).forEach((t=>{var[e,o]=t;c[e]={insertRule:o.prototype.insertRule,deleteRule:o.prototype.deleteRule},o.prototype.insertRule=new Proxy(c[e].insertRule,{apply:Xt(((t,e,o)=>{var[s,a]=o,{id:l,styleId:u}=oe(e.parentStyleSheet,r,n.styleMirror);return(l&&-1!==l||u&&-1!==u)&&i({id:l,styleId:u,adds:[{rule:s,index:[...ne(e),a||0]}]}),t.apply(e,o)}))}),o.prototype.deleteRule=new Proxy(c[e].deleteRule,{apply:Xt(((t,e,o)=>{var[s]=o,{id:a,styleId:l}=oe(e.parentStyleSheet,r,n.styleMirror);return(a&&-1!==a||l&&-1!==l)&&i({id:a,styleId:l,removes:[{index:[...ne(e),s]}]}),t.apply(e,o)}))})})),Xt((()=>{o.CSSStyleSheet.prototype.insertRule=s,o.CSSStyleSheet.prototype.deleteRule=u,a&&(o.CSSStyleSheet.prototype.replace=a),l&&(o.CSSStyleSheet.prototype.replaceSync=l),Object.entries(h).forEach((t=>{var[e,i]=t;i.prototype.insertRule=c[e].insertRule,i.prototype.deleteRule=c[e].deleteRule}))}))}(t,{win:n}),d=se(t,t.doc),v=function(t,e){var{styleDeclarationCb:i,mirror:r,ignoreCSSAttributes:n,stylesheetManager:o}=t,{win:s}=e,a=s.CSSStyleDeclaration.prototype.setProperty;s.CSSStyleDeclaration.prototype.setProperty=new Proxy(a,{apply:Xt(((t,e,s)=>{var l,[u,h,c]=s;if(n.has(u))return a.apply(e,[u,h,c]);var{id:d,styleId:v}=oe(null==(l=e.parentRule)?void 0:l.parentStyleSheet,r,o.styleMirror);return(d&&-1!==d||v&&-1!==v)&&i({id:d,styleId:v,set:{property:u,value:h,priority:c},index:ne(e.parentRule)}),t.apply(e,s)}))});var l=s.CSSStyleDeclaration.prototype.removeProperty;return s.CSSStyleDeclaration.prototype.removeProperty=new Proxy(l,{apply:Xt(((t,e,s)=>{var a,[u]=s;if(n.has(u))return l.apply(e,[u]);var{id:h,styleId:c}=oe(null==(a=e.parentRule)?void 0:a.parentStyleSheet,r,o.styleMirror);return(h&&-1!==h||c&&-1!==c)&&i({id:h,styleId:c,remove:{property:u},index:ne(e.parentRule)}),t.apply(e,s)}))}),Xt((()=>{s.CSSStyleDeclaration.prototype.setProperty=a,s.CSSStyleDeclaration.prototype.removeProperty=l}))}(t,{win:n}),t.collectFonts&&(f=function(t){var{fontCb:e,doc:i}=t,r=i.defaultView;if(!r)return()=>{};var n=[],o=new WeakMap,s=r.FontFace;r.FontFace=function(t,e,i){var r=new s(t,e,i);return o.set(r,{family:t,buffer:"string"!=typeof e,descriptors:i,fontSource:"string"==typeof e?e:JSON.stringify(Array.from(new Uint8Array(e)))}),r};var a=yt(i.fonts,"add",(function(t){return function(i){return setTimeout(Xt((()=>{var t=o.get(i);t&&(e(t),o.delete(i))})),0),t.apply(this,[i])}}));return n.push((()=>{r.FontFace=s})),n.push(a),Xt((()=>{n.forEach((t=>t()))}))}(t)));var p=function(t){var{doc:e,mirror:i,blockClass:r,blockSelector:n,selectionCb:o}=t,s=!0,a=Xt((()=>{var t=e.getSelection();if(!(!t||s&&(null==t?void 0:t.isCollapsed))){s=t.isCollapsed||!1;for(var a=[],l=t.rangeCount||0,u=0;u<l;u++){var h=t.getRangeAt(u),{startContainer:c,startOffset:d,endContainer:v,endOffset:f}=h;St(c,r,n,!0)||St(v,r,n,!0)||a.push({start:i.getId(c),startOffset:d,end:i.getId(v),endOffset:f})}o({ranges:a})}}));return a(),vt("selectionchange",a)}(t),g=function(t){var{doc:e,customElementCb:i}=t,r=e.defaultView;if(!r||!r.customElements)return()=>{};var n=yt(r.customElements,"define",(function(t){return function(e,r,n){try{i({define:{name:e}})}catch(t){console.warn("Custom element callback failed for "+e)}return t.apply(this,[e,r,n])}}));return n}(t),m=[];for(var y of t.plugins)m.push(y.observer(y.callback,n,y.options));return Xt((()=>{Jt.forEach((t=>t.reset())),null==r||r.disconnect(),o(),s(),a(),l(),u(),h(),c(),d(),v(),f(),p(),g(),m.forEach((t=>t()))}))}function le(t){return void 0!==window[t]}function ue(t){return Boolean(void 0!==window[t]&&window[t].prototype&&"insertRule"in window[t].prototype&&"deleteRule"in window[t].prototype)}class he{constructor(t){a(this,"iframeIdToRemoteIdMap",new WeakMap),a(this,"iframeRemoteIdToIdMap",new WeakMap),this.generateIdFn=t}getId(t,e,i,r){var n=i||this.getIdToRemoteIdMap(t),o=r||this.getRemoteIdToIdMap(t),s=n.get(e);return s||(s=this.generateIdFn(),n.set(e,s),o.set(s,e)),s}getIds(t,e){var i=this.getIdToRemoteIdMap(t),r=this.getRemoteIdToIdMap(t);return e.map((e=>this.getId(t,e,i,r)))}getRemoteId(t,e,i){var r=i||this.getRemoteIdToIdMap(t);if("number"!=typeof e)return e;var n=r.get(e);return n||-1}getRemoteIds(t,e){var i=this.getRemoteIdToIdMap(t);return e.map((e=>this.getRemoteId(t,e,i)))}reset(t){if(!t)return this.iframeIdToRemoteIdMap=new WeakMap,void(this.iframeRemoteIdToIdMap=new WeakMap);this.iframeIdToRemoteIdMap.delete(t),this.iframeRemoteIdToIdMap.delete(t)}getIdToRemoteIdMap(t){var e=this.iframeIdToRemoteIdMap.get(t);return e||(e=new Map,this.iframeIdToRemoteIdMap.set(t,e)),e}getRemoteIdToIdMap(t){var e=this.iframeRemoteIdToIdMap.get(t);return e||(e=new Map,this.iframeRemoteIdToIdMap.set(t,e)),e}}class ce{constructor(t){a(this,"iframes",new WeakMap),a(this,"crossOriginIframeMap",new WeakMap),a(this,"crossOriginIframeMirror",new he(H)),a(this,"crossOriginIframeStyleMirror"),a(this,"crossOriginIframeRootIdMap",new WeakMap),a(this,"mirror"),a(this,"mutationCb"),a(this,"wrappedEmit"),a(this,"loadListener"),a(this,"stylesheetManager"),a(this,"recordCrossOriginIframes"),this.mutationCb=t.mutationCb,this.wrappedEmit=t.wrappedEmit,this.stylesheetManager=t.stylesheetManager,this.recordCrossOriginIframes=t.recordCrossOriginIframes,this.crossOriginIframeStyleMirror=new he(this.stylesheetManager.styleMirror.generateId.bind(this.stylesheetManager.styleMirror)),this.mirror=t.mirror,this.recordCrossOriginIframes&&window.addEventListener("message",this.handleMessage.bind(this))}addIframe(t){this.iframes.set(t,!0),t.contentWindow&&this.crossOriginIframeMap.set(t.contentWindow,t)}addLoadListener(t){this.loadListener=t}attachIframe(t,e){var i,r;this.mutationCb({adds:[{parentId:this.mirror.getId(t),nextId:null,node:e}],removes:[],texts:[],attributes:[],isAttachIframe:!0}),this.recordCrossOriginIframes&&(null==(i=t.contentWindow)||i.addEventListener("message",this.handleMessage.bind(this))),null==(r=this.loadListener)||r.call(this,t),t.contentDocument&&t.contentDocument.adoptedStyleSheets&&t.contentDocument.adoptedStyleSheets.length>0&&this.stylesheetManager.adoptStyleSheets(t.contentDocument.adoptedStyleSheets,this.mirror.getId(t.contentDocument))}handleMessage(t){var e=t;if("rrweb"===e.data.type&&e.origin===e.data.origin&&t.source){var i=this.crossOriginIframeMap.get(t.source);if(i){var r=this.transformCrossOriginEvent(i,e.data.event);r&&this.wrappedEmit(r,e.data.isCheckout)}}}transformCrossOriginEvent(t,e){var i;switch(e.type){case Pt.FullSnapshot:this.crossOriginIframeMirror.reset(t),this.crossOriginIframeStyleMirror.reset(t),this.replaceIdOnNode(e.data.node,t);var r=e.data.node.id;return this.crossOriginIframeRootIdMap.set(t,r),this.patchRootIdOnNode(e.data.node,r),{timestamp:e.timestamp,type:Pt.IncrementalSnapshot,data:{source:Dt.Mutation,adds:[{parentId:this.mirror.getId(t),nextId:null,node:e.data.node}],removes:[],texts:[],attributes:[],isAttachIframe:!0}};case Pt.Meta:case Pt.Load:case Pt.DomContentLoaded:return!1;case Pt.Plugin:return e;case Pt.Custom:return this.replaceIds(e.data.payload,t,["id","parentId","previousId","nextId"]),e;case Pt.IncrementalSnapshot:switch(e.data.source){case Dt.Mutation:return e.data.adds.forEach((e=>{this.replaceIds(e,t,["parentId","nextId","previousId"]),this.replaceIdOnNode(e.node,t);var i=this.crossOriginIframeRootIdMap.get(t);i&&this.patchRootIdOnNode(e.node,i)})),e.data.removes.forEach((e=>{this.replaceIds(e,t,["parentId","id"])})),e.data.attributes.forEach((e=>{this.replaceIds(e,t,["id"])})),e.data.texts.forEach((e=>{this.replaceIds(e,t,["id"])})),e;case Dt.Drag:case Dt.TouchMove:case Dt.MouseMove:return e.data.positions.forEach((e=>{this.replaceIds(e,t,["id"])})),e;case Dt.ViewportResize:return!1;case Dt.MediaInteraction:case Dt.MouseInteraction:case Dt.Scroll:case Dt.CanvasMutation:case Dt.Input:return this.replaceIds(e.data,t,["id"]),e;case Dt.StyleSheetRule:case Dt.StyleDeclaration:return this.replaceIds(e.data,t,["id"]),this.replaceStyleIds(e.data,t,["styleId"]),e;case Dt.Font:return e;case Dt.Selection:return e.data.ranges.forEach((e=>{this.replaceIds(e,t,["start","end"])})),e;case Dt.AdoptedStyleSheet:return this.replaceIds(e.data,t,["id"]),this.replaceStyleIds(e.data,t,["styleIds"]),null==(i=e.data.styles)||i.forEach((e=>{this.replaceStyleIds(e,t,["styleId"])})),e}}return!1}replace(t,e,i,r){for(var n of r)(Array.isArray(e[n])||"number"==typeof e[n])&&(Array.isArray(e[n])?e[n]=t.getIds(i,e[n]):e[n]=t.getId(i,e[n]));return e}replaceIds(t,e,i){return this.replace(this.crossOriginIframeMirror,t,e,i)}replaceStyleIds(t,e,i){return this.replace(this.crossOriginIframeStyleMirror,t,e,i)}replaceIdOnNode(t,e){this.replaceIds(t,e,["id","rootId"]),"childNodes"in t&&t.childNodes.forEach((t=>{this.replaceIdOnNode(t,e)}))}patchRootIdOnNode(t,e){t.type===h.Document||t.rootId||(t.rootId=e),"childNodes"in t&&t.childNodes.forEach((t=>{this.patchRootIdOnNode(t,e)}))}}class de{constructor(t){a(this,"shadowDoms",new WeakSet),a(this,"mutationCb"),a(this,"scrollCb"),a(this,"bypassOptions"),a(this,"mirror"),a(this,"restoreHandlers",[]),this.mutationCb=t.mutationCb,this.scrollCb=t.scrollCb,this.bypassOptions=t.bypassOptions,this.mirror=t.mirror,this.init()}init(){this.reset(),this.patchAttachShadow(Element,document)}addShadowRoot(t,e){if(I(t)&&!this.shadowDoms.has(t)){this.shadowDoms.add(t);var r=Qt(i({},this.bypassOptions,{doc:e,mutationCb:this.mutationCb,mirror:this.mirror,shadowDomManager:this}),t);this.restoreHandlers.push((()=>r.disconnect())),this.restoreHandlers.push(ee(i({},this.bypassOptions,{scrollCb:this.scrollCb,doc:t,mirror:this.mirror}))),setTimeout((()=>{t.adoptedStyleSheets&&t.adoptedStyleSheets.length>0&&this.bypassOptions.stylesheetManager.adoptStyleSheets(t.adoptedStyleSheets,this.mirror.getId(dt.host(t))),this.restoreHandlers.push(se({mirror:this.mirror,stylesheetManager:this.bypassOptions.stylesheetManager},t))}),0)}}observeAttachShadow(t){t.contentWindow&&t.contentDocument&&this.patchAttachShadow(t.contentWindow.Element,t.contentDocument)}patchAttachShadow(t,e){var i=this;this.restoreHandlers.push(yt(t.prototype,"attachShadow",(function(t){return function(r){var n=t.call(this,r),o=dt.shadowRoot(this);return o&&Ot(this)&&i.addShadowRoot(o,e),n}})))}reset(){this.restoreHandlers.forEach((t=>{try{t()}catch(t){}})),this.restoreHandlers=[],this.shadowDoms=new WeakSet}}for(var ve="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",fe="undefined"==typeof Uint8Array?[]:new Uint8Array(256),pe=0;pe<64;pe++)fe[ve.charCodeAt(pe)]=pe;var ge=new Map;var me=(t,e,i)=>{if(t&&(_e(t,e)||"object"==typeof t)){var r=function(t,e){var i=ge.get(t);return i||(i=new Map,ge.set(t,i)),i.has(e)||i.set(e,[]),i.get(e)}(i,t.constructor.name),n=r.indexOf(t);return-1===n&&(n=r.length,r.push(t)),n}};function ye(t,e,i){if(t instanceof Array)return t.map((t=>ye(t,e,i)));if(null===t)return t;if(t instanceof Float32Array||t instanceof Float64Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Uint8Array||t instanceof Uint16Array||t instanceof Int16Array||t instanceof Int8Array||t instanceof Uint8ClampedArray)return{rr_type:t.constructor.name,args:[Object.values(t)]};if(t instanceof ArrayBuffer)return{rr_type:t.constructor.name,base64:function(t){var e,i=new Uint8Array(t),r=i.length,n="";for(e=0;e<r;e+=3)n+=ve[i[e]>>2],n+=ve[(3&i[e])<<4|i[e+1]>>4],n+=ve[(15&i[e+1])<<2|i[e+2]>>6],n+=ve[63&i[e+2]];return r%3==2?n=n.substring(0,n.length-1)+"=":r%3==1&&(n=n.substring(0,n.length-2)+"=="),n}(t)};if(t instanceof DataView)return{rr_type:t.constructor.name,args:[ye(t.buffer,e,i),t.byteOffset,t.byteLength]};if(t instanceof HTMLImageElement){var r=t.constructor.name,{src:n}=t;return{rr_type:r,src:n}}if(t instanceof HTMLCanvasElement){return{rr_type:"HTMLImageElement",src:t.toDataURL()}}return t instanceof ImageData?{rr_type:t.constructor.name,args:[ye(t.data,e,i),t.width,t.height]}:_e(t,e)||"object"==typeof t?{rr_type:t.constructor.name,index:me(t,e,i)}:t}var be=(t,e,i)=>t.map((t=>ye(t,e,i))),_e=(t,e)=>{var i=["WebGLActiveInfo","WebGLBuffer","WebGLFramebuffer","WebGLProgram","WebGLRenderbuffer","WebGLShader","WebGLShaderPrecisionFormat","WebGLTexture","WebGLUniformLocation","WebGLVertexArrayObject","WebGLVertexArrayObjectOES"].filter((t=>"function"==typeof e[t]));return Boolean(i.find((i=>t instanceof e[i])))};function we(t,e,i,r){var n=[];try{var o=yt(t.HTMLCanvasElement.prototype,"getContext",(function(t){return function(n){for(var o=arguments.length,s=new Array(o>1?o-1:0),a=1;a<o;a++)s[a-1]=arguments[a];if(!St(this,e,i,!0)){var l=function(t){return"experimental-webgl"===t?"webgl":t}(n);if("__context"in this||(this.__context=l),r&&["webgl","webgl2"].includes(l))if(s[0]&&"object"==typeof s[0]){var u=s[0];u.preserveDrawingBuffer||(u.preserveDrawingBuffer=!0)}else s.splice(0,1,{preserveDrawingBuffer:!0})}return t.apply(this,[n,...s])}}));n.push(o)}catch(t){console.error("failed to patch HTMLCanvasElement.prototype.getContext")}return()=>{n.forEach((t=>t()))}}function Ie(t,e,i,r,n,o){var s=[],a=Object.getOwnPropertyNames(t),l=function(a){if(["isContextLost","canvas","drawingBufferWidth","drawingBufferHeight"].includes(a))return 0;try{if("function"!=typeof t[a])return 0;var l=yt(t,a,(function(t){return function(){for(var s=arguments.length,l=new Array(s),u=0;u<s;u++)l[u]=arguments[u];var h=t.apply(this,l);if(me(h,o,this),"tagName"in this.canvas&&!St(this.canvas,r,n,!0)){var c=be(l,o,this),d={type:e,property:a,args:c};i(this.canvas,d)}return h}}));s.push(l)}catch(r){var u=mt(t,a,{set(t){i(this.canvas,{type:e,property:a,args:[t],setter:!0})}});s.push(u)}};for(var u of a)l(u);return s}var Ce,Se,ke,xe,Ae="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",Te="undefined"!=typeof window&&window.Blob&&new Blob([(Ce=Ae,Uint8Array.from(atob(Ce),(t=>t.charCodeAt(0))))],{type:"text/javascript;charset=utf-8"});function Me(t){var e;try{if(!(e=Te&&(window.URL||window.webkitURL).createObjectURL(Te)))throw"";var i=new Worker(e,{name:null==t?void 0:t.name});return i.addEventListener("error",(()=>{(window.URL||window.webkitURL).revokeObjectURL(e)})),i}catch(e){return new Worker("data:text/javascript;base64,"+Ae,{name:null==t?void 0:t.name})}finally{e&&(window.URL||window.webkitURL).revokeObjectURL(e)}}class Re{constructor(t){a(this,"pendingCanvasMutations",new Map),a(this,"rafStamps",{latestId:0,invokeId:null}),a(this,"mirror"),a(this,"mutationCb"),a(this,"resetObservers"),a(this,"frozen",!1),a(this,"locked",!1),a(this,"processMutation",((t,e)=>{!(this.rafStamps.invokeId&&this.rafStamps.latestId!==this.rafStamps.invokeId)&&this.rafStamps.invokeId||(this.rafStamps.invokeId=this.rafStamps.latestId),this.pendingCanvasMutations.has(t)||this.pendingCanvasMutations.set(t,[]),this.pendingCanvasMutations.get(t).push(e)}));var{sampling:e="all",win:i,blockClass:r,blockSelector:n,recordCanvas:o,dataURLOptions:s}=t;this.mutationCb=t.mutationCb,this.mirror=t.mirror,o&&"all"===e&&this.initCanvasMutationObserver(i,r,n),o&&"number"==typeof e&&this.initCanvasFPSObserver(e,i,r,n,{dataURLOptions:s})}reset(){this.pendingCanvasMutations.clear(),this.resetObservers&&this.resetObservers()}freeze(){this.frozen=!0}unfreeze(){this.frozen=!1}lock(){this.locked=!0}unlock(){this.locked=!1}initCanvasFPSObserver(t,i,r,n,o){var s=this,a=we(i,r,n,!0),l=new Map,u=new Me;u.onmessage=t=>{var{id:e}=t.data;if(l.set(e,!1),"base64"in t.data){var{base64:i,type:r,width:n,height:o}=t.data;this.mutationCb({id:e,type:$t["2D"],commands:[{property:"clearRect",args:[0,0,n,o]},{property:"drawImage",args:[{rr_type:"ImageBitmap",args:[{rr_type:"Blob",data:[{rr_type:"ArrayBuffer",base64:i}],type:r}]},0,0]}]})}};var h,c=1e3/t,d=0,v=t=>{var a,f;d&&t-d<c?h=requestAnimationFrame(v):(d=t,(a=[],f=t=>{t.querySelectorAll("canvas").forEach((t=>{St(t,r,n,!0)||a.push(t)})),t.querySelectorAll("*").forEach((t=>{t.shadowRoot&&f(t.shadowRoot)}))},f(i.document),a).forEach(function(){var t=e((function*(t){var e,i=s.mirror.getId(t);if(!l.get(i)&&0!==t.width&&0!==t.height){if(l.set(i,!0),["webgl","webgl2"].includes(t.__context)){var r=t.getContext(t.__context);!1===(null==(e=null==r?void 0:r.getContextAttributes())?void 0:e.preserveDrawingBuffer)&&r.clear(r.COLOR_BUFFER_BIT)}var n=t.clientWidth||t.width,a=t.clientHeight||t.height,h=yield createImageBitmap(t,{resizeWidth:n,resizeHeight:a});u.postMessage({id:i,bitmap:h,width:n,height:a,dataURLOptions:o.dataURLOptions},[h])}}));return function(e){return t.apply(this,arguments)}}()),h=requestAnimationFrame(v))};h=requestAnimationFrame(v),this.resetObservers=()=>{a(),cancelAnimationFrame(h)}}initCanvasMutationObserver(t,e,i){this.startRAFTimestamping(),this.startPendingCanvasMutationFlusher();var r=we(t,e,i,!1),n=function(t,e,i,r){var n=[],o=Object.getOwnPropertyNames(e.CanvasRenderingContext2D.prototype),s=function(o){try{if("function"!=typeof e.CanvasRenderingContext2D.prototype[o])return 1;var s=yt(e.CanvasRenderingContext2D.prototype,o,(function(n){return function(){for(var s=arguments.length,a=new Array(s),l=0;l<s;l++)a[l]=arguments[l];return St(this.canvas,i,r,!0)||setTimeout((()=>{var i=be(a,e,this);t(this.canvas,{type:$t["2D"],property:o,args:i})}),0),n.apply(this,a)}}));n.push(s)}catch(i){var a=mt(e.CanvasRenderingContext2D.prototype,o,{set(e){t(this.canvas,{type:$t["2D"],property:o,args:[e],setter:!0})}});n.push(a)}};for(var a of o)s(a);return()=>{n.forEach((t=>t()))}}(this.processMutation.bind(this),t,e,i),o=function(t,e,i,r){var n=[];return n.push(...Ie(e.WebGLRenderingContext.prototype,$t.WebGL,t,i,r,e)),void 0!==e.WebGL2RenderingContext&&n.push(...Ie(e.WebGL2RenderingContext.prototype,$t.WebGL2,t,i,r,e)),()=>{n.forEach((t=>t()))}}(this.processMutation.bind(this),t,e,i);this.resetObservers=()=>{r(),n(),o()}}startPendingCanvasMutationFlusher(){requestAnimationFrame((()=>this.flushPendingCanvasMutations()))}startRAFTimestamping(){var t=e=>{this.rafStamps.latestId=e,requestAnimationFrame(t)};requestAnimationFrame(t)}flushPendingCanvasMutations(){this.pendingCanvasMutations.forEach(((t,e)=>{var i=this.mirror.getId(e);this.flushPendingCanvasMutationFor(e,i)})),requestAnimationFrame((()=>this.flushPendingCanvasMutations()))}flushPendingCanvasMutationFor(t,e){if(!this.frozen&&!this.locked){var i=this.pendingCanvasMutations.get(t);if(i&&-1!==e){var n=i.map((t=>r(t,o))),{type:s}=i[0];this.mutationCb({id:e,type:s,commands:n}),this.pendingCanvasMutations.delete(t)}}}}class Ne{constructor(t){a(this,"trackedLinkElements",new WeakSet),a(this,"mutationCb"),a(this,"adoptedStyleSheetCb"),a(this,"styleMirror",new Nt),this.mutationCb=t.mutationCb,this.adoptedStyleSheetCb=t.adoptedStyleSheetCb}attachLinkElement(t,e){"_cssText"in e.attributes&&this.mutationCb({adds:[],removes:[],texts:[],attributes:[{id:e.id,attributes:e.attributes}]}),this.trackLinkElement(t)}trackLinkElement(t){this.trackedLinkElements.has(t)||(this.trackedLinkElements.add(t),this.trackStylesheetInLinkElement(t))}adoptStyleSheets(t,e){var i=this;if(0!==t.length){var r={id:e,styleIds:[]},n=[],o=function(t){var e;i.styleMirror.has(t)?e=i.styleMirror.getId(t):(e=i.styleMirror.add(t),n.push({styleId:e,rules:Array.from(t.rules||CSSRule,((e,i)=>({rule:S(e,t.href),index:i})))})),r.styleIds.push(e)};for(var s of t)o(s);n.length>0&&(r.styles=n),this.adoptedStyleSheetCb(r)}}reset(){this.styleMirror.reset(),this.trackedLinkElements=new WeakSet}trackStylesheetInLinkElement(t){}}class Ee{constructor(){a(this,"nodeMap",new WeakMap),a(this,"active",!1)}inOtherBuffer(t,e){var i=this.nodeMap.get(t);return i&&Array.from(i).some((t=>t!==e))}add(t,e){this.active||(this.active=!0,requestAnimationFrame((()=>{this.nodeMap=new WeakMap,this.active=!1}))),this.nodeMap.set(t,(this.nodeMap.get(t)||new Set).add(e))}destroy(){}}var Fe=!1;try{if(2!==Array.from([1],(t=>2*t))[0]){var Oe=document.createElement("iframe");document.body.appendChild(Oe),Array.from=(null==(n=Oe.contentWindow)?void 0:n.Array.from)||Array.from,document.body.removeChild(Oe)}}catch(t){console.debug("Unable to override Array.from",t)}var Pe,De,Le=new x;function Be(t){void 0===t&&(t={});var{emit:e,checkoutEveryNms:r,checkoutEveryNth:n,blockClass:o="rr-block",blockSelector:s=null,ignoreClass:a="rr-ignore",ignoreSelector:l=null,maskTextClass:u="rr-mask",maskTextSelector:h=null,inlineStylesheet:c=!0,maskAllInputs:d,maskInputOptions:v,slimDOMOptions:f,maskInputFn:p,maskTextFn:g,hooks:m,packFn:y,sampling:b={},dataURLOptions:_={},mousemoveWait:w,recordDOM:I=!0,recordCanvas:C=!1,recordCrossOriginIframes:S=!1,recordAfter:k=("DOMContentLoaded"===t.recordAfter?t.recordAfter:"load"),userTriggeredOnInput:A=!1,collectFonts:T=!1,inlineImages:M=!1,plugins:R,keepIframeSrcFn:N=(()=>!1),ignoreCSSAttributes:E=new Set([]),errorHandler:F}=t;Vt=F;var O=!S||window.parent===window,P=!1;if(!O)try{window.parent.document&&(P=!1)}catch(t){P=!0}if(O&&!e)throw new Error("emit function is required");if(!O&&!P)return()=>{};void 0!==w&&void 0===b.mousemove&&(b.mousemove=w),Le.reset();var D,L=!0===d?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,password:!0}:void 0!==v?v:{password:!0},B=!0===f||"all"===f?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaVerification:!0,headMetaAuthorship:"all"===f,headMetaDescKeywords:"all"===f,headTitleMutations:"all"===f}:f||{};!function(t){void 0===t&&(t=window),"NodeList"in t&&!t.NodeList.prototype.forEach&&(t.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in t&&!t.DOMTokenList.prototype.forEach&&(t.DOMTokenList.prototype.forEach=Array.prototype.forEach)}();var $=0,Z=t=>{for(var e of R||[])e.eventProcessor&&(t=e.eventProcessor(t));return y&&!P&&(t=y(t)),t};Se=(t,i)=>{var o,s=t;if(s.timestamp=bt(),!(null==(o=Jt[0])?void 0:o.isFrozen())||s.type===Pt.FullSnapshot||s.type===Pt.IncrementalSnapshot&&s.data.source===Dt.Mutation||Jt.forEach((t=>t.unfreeze())),O)null==e||e(Z(s),i);else if(P){var a={type:"rrweb",event:Z(s),origin:window.location.origin,isCheckout:i};window.parent.postMessage(a,"*")}if(s.type===Pt.FullSnapshot)D=s,$=0;else if(s.type===Pt.IncrementalSnapshot){if(s.data.source===Dt.Mutation&&s.data.isAttachIframe)return;$++;var l=n&&$>=n,u=r&&s.timestamp-D.timestamp>r;(l||u)&&ke(!0)}};var q=t=>{Se({type:Pt.IncrementalSnapshot,data:i({source:Dt.Mutation},t)})},H=t=>Se({type:Pt.IncrementalSnapshot,data:i({source:Dt.Scroll},t)}),V=t=>Se({type:Pt.IncrementalSnapshot,data:i({source:Dt.CanvasMutation},t)}),j=new Ne({mutationCb:q,adoptedStyleSheetCb:t=>Se({type:Pt.IncrementalSnapshot,data:i({source:Dt.AdoptedStyleSheet},t)})}),Y=new ce({mirror:Le,mutationCb:q,stylesheetManager:j,recordCrossOriginIframes:S,wrappedEmit:Se});for(var z of R||[])z.getMirror&&z.getMirror({nodeMirror:Le,crossOriginIframeMirror:Y.crossOriginIframeMirror,crossOriginIframeStyleMirror:Y.crossOriginIframeStyleMirror});var G=new Ee;xe=new Re({recordCanvas:C,mutationCb:V,win:window,blockClass:o,blockSelector:s,mirror:Le,sampling:b.canvas,dataURLOptions:_});var W=new de({mutationCb:q,scrollCb:H,bypassOptions:{blockClass:o,blockSelector:s,maskTextClass:u,maskTextSelector:h,inlineStylesheet:c,maskInputOptions:L,dataURLOptions:_,maskTextFn:g,maskInputFn:p,recordCanvas:C,inlineImages:M,sampling:b,slimDOMOptions:B,iframeManager:Y,stylesheetManager:j,canvasManager:xe,keepIframeSrcFn:N,processedNodeManager:G},mirror:Le});ke=function(t){if(void 0===t&&(t=!1),I){Se({type:Pt.Meta,data:{href:window.location.href,width:It(),height:wt()}},t),j.reset(),W.init(),Jt.forEach((t=>t.lock()));var e=function(t,e){var{mirror:i=new x,blockClass:r="rr-block",blockSelector:n=null,maskTextClass:o="rr-mask",maskTextSelector:s=null,inlineStylesheet:a=!0,inlineImages:l=!1,recordCanvas:u=!1,maskAllInputs:h=!1,maskTextFn:c,maskInputFn:d,slimDOM:v=!1,dataURLOptions:f,preserveWhiteSpace:p,onSerialize:g,onIframeLoad:m,iframeLoadTimeout:y,onStylesheetLoad:b,stylesheetLoadTimeout:_,keepIframeSrcFn:w=(()=>!1)}=e||{};return et(t,{doc:t,mirror:i,blockClass:r,blockSelector:n,maskTextClass:o,maskTextSelector:s,skipChild:!1,inlineStylesheet:a,maskInputOptions:!0===h?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,password:!0}:!1===h?{password:!0}:h,maskTextFn:c,maskInputFn:d,slimDOMOptions:!0===v||"all"===v?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaDescKeywords:"all"===v,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaAuthorship:!0,headMetaVerification:!0}:!1===v?{}:v,dataURLOptions:f,inlineImages:l,recordCanvas:u,preserveWhiteSpace:p,onSerialize:g,onIframeLoad:m,iframeLoadTimeout:y,onStylesheetLoad:b,stylesheetLoadTimeout:_,keepIframeSrcFn:w,newlyAddedElement:!1})}(document,{mirror:Le,blockClass:o,blockSelector:s,maskTextClass:u,maskTextSelector:h,inlineStylesheet:c,maskAllInputs:L,maskTextFn:g,maskInputFn:p,slimDOM:B,dataURLOptions:_,recordCanvas:C,inlineImages:M,onSerialize:t=>{Tt(t,Le)&&Y.addIframe(t),Mt(t,Le)&&j.trackLinkElement(t),Rt(t)&&W.addShadowRoot(dt.shadowRoot(t),document)},onIframeLoad:(t,e)=>{Y.attachIframe(t,e),W.observeAttachShadow(t)},onStylesheetLoad:(t,e)=>{j.attachLinkElement(t,e)},keepIframeSrcFn:N});if(!e)return console.warn("Failed to snapshot the document");Se({type:Pt.FullSnapshot,data:{node:e,initialOffset:_t(window)}},t),Jt.forEach((t=>t.unlock())),document.adoptedStyleSheets&&document.adoptedStyleSheets.length>0&&j.adoptStyleSheets(document.adoptedStyleSheets,Le.getId(document))}};try{var U=[],X=t=>{var e;return Xt(ae)({mutationCb:q,mousemoveCb:(t,e)=>Se({type:Pt.IncrementalSnapshot,data:{source:e,positions:t}}),mouseInteractionCb:t=>Se({type:Pt.IncrementalSnapshot,data:i({source:Dt.MouseInteraction},t)}),scrollCb:H,viewportResizeCb:t=>Se({type:Pt.IncrementalSnapshot,data:i({source:Dt.ViewportResize},t)}),inputCb:t=>Se({type:Pt.IncrementalSnapshot,data:i({source:Dt.Input},t)}),mediaInteractionCb:t=>Se({type:Pt.IncrementalSnapshot,data:i({source:Dt.MediaInteraction},t)}),styleSheetRuleCb:t=>Se({type:Pt.IncrementalSnapshot,data:i({source:Dt.StyleSheetRule},t)}),styleDeclarationCb:t=>Se({type:Pt.IncrementalSnapshot,data:i({source:Dt.StyleDeclaration},t)}),canvasMutationCb:V,fontCb:t=>Se({type:Pt.IncrementalSnapshot,data:i({source:Dt.Font},t)}),selectionCb:t=>{Se({type:Pt.IncrementalSnapshot,data:i({source:Dt.Selection},t)})},customElementCb:t=>{Se({type:Pt.IncrementalSnapshot,data:i({source:Dt.CustomElement},t)})},blockClass:o,ignoreClass:a,ignoreSelector:l,maskTextClass:u,maskTextSelector:h,maskInputOptions:L,inlineStylesheet:c,sampling:b,recordDOM:I,recordCanvas:C,inlineImages:M,userTriggeredOnInput:A,collectFonts:T,doc:t,maskInputFn:p,maskTextFn:g,keepIframeSrcFn:N,blockSelector:s,slimDOMOptions:B,dataURLOptions:_,mirror:Le,iframeManager:Y,stylesheetManager:j,shadowDomManager:W,processedNodeManager:G,canvasManager:xe,ignoreCSSAttributes:E,plugins:(null==(e=null==R?void 0:R.filter((t=>t.observer)))?void 0:e.map((t=>({observer:t.observer,options:t.options,callback:e=>Se({type:Pt.Plugin,data:{plugin:t.name,payload:e}})}))))||[]},m)};Y.addLoadListener((t=>{try{U.push(X(t.contentDocument))}catch(t){console.warn(t)}}));var J=()=>{ke(),U.push(X(document)),Fe=!0};return"interactive"===document.readyState||"complete"===document.readyState?J():(U.push(vt("DOMContentLoaded",(()=>{Se({type:Pt.DomContentLoaded,data:{}}),"DOMContentLoaded"===k&&J()}))),U.push(vt("load",(()=>{Se({type:Pt.Load,data:{}}),"load"===k&&J()}),window))),()=>{U.forEach((t=>t())),G.destroy(),Fe=!1,Vt=void 0}}catch(t){console.warn(t)}}Be.addCustomEvent=(t,e)=>{if(!Fe)throw new Error("please add custom event after start recording");Se({type:Pt.Custom,data:{tag:t,payload:e}})},Be.freezePage=()=>{Jt.forEach((t=>t.freeze()))},Be.takeFullSnapshot=t=>{if(!Fe)throw new Error("please take full snapshot after start recording");ke(t)},Be.mirror=Le,(De=Pe||(Pe={}))[De.NotStarted=0]="NotStarted",De[De.Running=1]="Running",De[De.Stopped=2]="Stopped";var $e,Ze=Object.defineProperty,qe=(t,e,i)=>((t,e,i)=>e in t?Ze(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i)(t,"symbol"!=typeof e?e+"":e,i),He=Object.defineProperty,Ve=(t,e,i)=>((t,e,i)=>e in t?He(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i)(t,"symbol"!=typeof e?e+"":e,i),je=Object.defineProperty,Ye=(t,e,i)=>((t,e,i)=>e in t?je(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i)(t,"symbol"!=typeof e?e+"":e,i),ze={Node:["childNodes","parentNode","parentElement","textContent"],ShadowRoot:["host","styleSheets"],Element:["shadowRoot","querySelector","querySelectorAll"],MutationObserver:[]},Ge={Node:["contains","getRootNode"],ShadowRoot:["getSelection"],Element:[],MutationObserver:["constructor"]},We={};function Ue(t){if(We[t])return We[t];var e=globalThis[t],i=e.prototype,r=t in ze?ze[t]:void 0,n=Boolean(r&&r.every((t=>{var e,r;return Boolean(null==(r=null==(e=Object.getOwnPropertyDescriptor(i,t))?void 0:e.get)?void 0:r.toString().includes("[native code]"))}))),o=t in Ge?Ge[t]:void 0,s=Boolean(o&&o.every((t=>{var e;return"function"==typeof i[t]&&(null==(e=i[t])?void 0:e.toString().includes("[native code]"))})));if(n&&s)return We[t]=e.prototype,e.prototype;try{var a=document.createElement("iframe");document.body.appendChild(a);var l=a.contentWindow;if(!l)return e.prototype;var u=l[t].prototype;return document.body.removeChild(a),u?We[t]=u:i}catch(t){return i}}var Xe={};function Je(t,e,i){var r,n=t+"."+String(i);if(Xe[n])return Xe[n].call(e);var o=Ue(t),s=null==(r=Object.getOwnPropertyDescriptor(o,i))?void 0:r.get;return s?(Xe[n]=s,s.call(e)):e[i]}var Ke={};function Qe(t,e,i){var r=t+"."+String(i);if(Ke[r])return Ke[r].bind(e);var n=Ue(t)[i];return"function"!=typeof n?e[i]:(Ke[r]=n,n.bind(e))}var ti={childNodes:function(t){return Je("Node",t,"childNodes")},parentNode:function(t){return Je("Node",t,"parentNode")},parentElement:function(t){return Je("Node",t,"parentElement")},textContent:function(t){return Je("Node",t,"textContent")},contains:function(t,e){return Qe("Node",t,"contains")(e)},getRootNode:function(t){return Qe("Node",t,"getRootNode")()},host:function(t){return t&&"host"in t?Je("ShadowRoot",t,"host"):null},styleSheets:function(t){return t.styleSheets},shadowRoot:function(t){return t&&"shadowRoot"in t?Je("Element",t,"shadowRoot"):null},querySelector:function(t,e){return Je("Element",t,"querySelector")(e)},querySelectorAll:function(t,e){return Je("Element",t,"querySelectorAll")(e)},mutationObserver:function(){return Ue("MutationObserver").constructor}};class ei{constructor(){Ye(this,"idNodeMap",new Map),Ye(this,"nodeMetaMap",new WeakMap)}getId(t){var e;if(!t)return-1;var i=null==(e=this.getMeta(t))?void 0:e.id;return null!=i?i:-1}getNode(t){return this.idNodeMap.get(t)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(t){return this.nodeMetaMap.get(t)||null}removeNodeFromMap(t){var e=this.getId(t);this.idNodeMap.delete(e),t.childNodes&&t.childNodes.forEach((t=>this.removeNodeFromMap(t)))}has(t){return this.idNodeMap.has(t)}hasNode(t){return this.nodeMetaMap.has(t)}add(t,e){var i=e.id;this.idNodeMap.set(i,t),this.nodeMetaMap.set(t,e)}replace(t,e){var i=this.getNode(t);if(i){var r=this.nodeMetaMap.get(i);r&&this.nodeMetaMap.set(e,r)}this.idNodeMap.set(t,e)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}}function ii(t,e,i){if(!t)return!1;if(t.nodeType!==t.ELEMENT_NODE)return!!i&&ii(ti.parentNode(t),e,i);for(var r=t.classList.length;r--;){var n=t.classList[r];if(e.test(n))return!0}return!!i&&ii(ti.parentNode(t),e,i)}class ri{constructor(){__publicField22(this,"parentElement",null),__publicField22(this,"parentNode",null),__publicField22(this,"ownerDocument"),__publicField22(this,"firstChild",null),__publicField22(this,"lastChild",null),__publicField22(this,"previousSibling",null),__publicField22(this,"nextSibling",null),__publicField22(this,"ELEMENT_NODE",1),__publicField22(this,"TEXT_NODE",3),__publicField22(this,"nodeType"),__publicField22(this,"nodeName"),__publicField22(this,"RRNodeType")}get childNodes(){for(var t=[],e=this.firstChild;e;)t.push(e),e=e.nextSibling;return t}contains(t){if(!(t instanceof ri))return!1;if(t.ownerDocument!==this.ownerDocument)return!1;if(t===this)return!0;for(;t.parentNode;){if(t.parentNode===this)return!0;t=t.parentNode}return!1}appendChild(t){throw new Error("RRDomException: Failed to execute 'appendChild' on 'RRNode': This RRNode type does not support this method.")}insertBefore(t,e){throw new Error("RRDomException: Failed to execute 'insertBefore' on 'RRNode': This RRNode type does not support this method.")}removeChild(t){throw new Error("RRDomException: Failed to execute 'removeChild' on 'RRNode': This RRNode type does not support this method.")}toString(){return"RRNode"}}var ni={Node:["childNodes","parentNode","parentElement","textContent"],ShadowRoot:["host","styleSheets"],Element:["shadowRoot","querySelector","querySelectorAll"],MutationObserver:[]},oi={Node:["contains","getRootNode"],ShadowRoot:["getSelection"],Element:[],MutationObserver:["constructor"]},si={};function ai(t){if(si[t])return si[t];var e=globalThis[t],i=e.prototype,r=t in ni?ni[t]:void 0,n=Boolean(r&&r.every((t=>{var e,r;return Boolean(null==(r=null==(e=Object.getOwnPropertyDescriptor(i,t))?void 0:e.get)?void 0:r.toString().includes("[native code]"))}))),o=t in oi?oi[t]:void 0,s=Boolean(o&&o.every((t=>{var e;return"function"==typeof i[t]&&(null==(e=i[t])?void 0:e.toString().includes("[native code]"))})));if(n&&s)return si[t]=e.prototype,e.prototype;try{var a=document.createElement("iframe");document.body.appendChild(a);var l=a.contentWindow;if(!l)return e.prototype;var u=l[t].prototype;return document.body.removeChild(a),u?si[t]=u:i}catch(t){return i}}var li={};function ui(t,e,i){var r,n=t+"."+String(i);if(li[n])return li[n].call(e);var o=ai(t),s=null==(r=Object.getOwnPropertyDescriptor(o,i))?void 0:r.get;return s?(li[n]=s,s.call(e)):e[i]}var hi={};function ci(t,e,i){var r=t+"."+String(i);if(hi[r])return hi[r].bind(e);var n=ai(t)[i];return"function"!=typeof n?e[i]:(hi[r]=n,n.bind(e))}var di={childNodes:function(t){return ui("Node",t,"childNodes")},parentNode:function(t){return ui("Node",t,"parentNode")},parentElement:function(t){return ui("Node",t,"parentElement")},textContent:function(t){return ui("Node",t,"textContent")},contains:function(t,e){return ci("Node",t,"contains")(e)},getRootNode:function(t){return ci("Node",t,"getRootNode")()},host:function(t){return t&&"host"in t?ui("ShadowRoot",t,"host"):null},styleSheets:function(t){return t.styleSheets},shadowRoot:function(t){return t&&"shadowRoot"in t?ui("Element",t,"shadowRoot"):null},querySelector:function(t,e){return ui("Element",t,"querySelector")(e)},querySelectorAll:function(t,e){return ui("Element",t,"querySelectorAll")(e)},mutationObserver:function(){return ai("MutationObserver").constructor}};var vi="Please stop import mirror directly. Instead of that,\r\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\r\nor you can use record.mirror to access the mirror instance during recording.",fi={map:{},getId:()=>(console.error(vi),-1),getNode:()=>(console.error(vi),null),removeNodeFromMap(){console.error(vi)},has:()=>(console.error(vi),!1),reset(){console.error(vi)}};"undefined"!=typeof window&&window.Proxy&&window.Reflect&&(fi=new Proxy(fi,{get:(t,e,i)=>("map"===e&&console.error(vi),Reflect.get(t,e,i))}));var pi=Date.now;function gi(t){return t?t.nodeType===t.ELEMENT_NODE?t:di.parentElement(t):null}/[1-9][0-9]{12}/.test(Date.now().toString())||(pi=()=>(new Date).getTime());function mi(t){var e,i=null;return"getRootNode"in t&&(null==(e=di.getRootNode(t))?void 0:e.nodeType)===Node.DOCUMENT_FRAGMENT_NODE&&di.host(di.getRootNode(t))&&(i=di.host(di.getRootNode(t))),i}function yi(t){for(var e,i=t;e=mi(i);)i=e;return i}function bi(t){var e=t.ownerDocument;if(!e)return!1;var i=yi(t);return di.contains(e,i)}for(var _i=Object.freeze(Object.defineProperty({__proto__:null,StyleSheetMirror:class{constructor(){Ve(this,"id",1),Ve(this,"styleIDMap",new WeakMap),Ve(this,"idStyleMap",new Map)}getId(t){var e;return null!==(e=this.styleIDMap.get(t))&&void 0!==e?e:-1}has(t){return this.styleIDMap.has(t)}add(t,e){return this.has(t)?this.getId(t):(i=void 0===e?this.id++:e,this.styleIDMap.set(t,i),this.idStyleMap.set(i,t),i);var i}getStyle(t){return this.idStyleMap.get(t)||null}reset(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}generateId(){return this.id++}},get _mirror(){return fi},closestElementOfNode:gi,getBaseDimension:function t(e,i){var r,n,o=null==(n=null==(r=e.ownerDocument)?void 0:r.defaultView)?void 0:n.frameElement;if(!o||o===i)return{x:0,y:0,relativeScale:1,absoluteScale:1};var s=o.getBoundingClientRect(),a=t(o,i),l=s.height/o.clientHeight;return{x:s.x*a.relativeScale+a.x,y:s.y*a.relativeScale+a.y,relativeScale:l,absoluteScale:a.absoluteScale*l}},getNestedRule:function t(e,i){var r=e[i[0]];return 1===i.length?r:t(r.cssRules[i[1]].cssRules,i.slice(2))},getPositionsAndIndex:function(t){var e=[...t],i=e.pop();return{positions:e,index:i}},getRootShadowHost:yi,getShadowHost:mi,getWindowHeight:function(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight},getWindowScroll:function(t){var e,i,r,n,o=t.document;return{left:o.scrollingElement?o.scrollingElement.scrollLeft:void 0!==t.pageXOffset?t.pageXOffset:o.documentElement.scrollLeft||(null==o?void 0:o.body)&&(null==(e=di.parentElement(o.body))?void 0:e.scrollLeft)||(null==(i=null==o?void 0:o.body)?void 0:i.scrollLeft)||0,top:o.scrollingElement?o.scrollingElement.scrollTop:void 0!==t.pageYOffset?t.pageYOffset:(null==o?void 0:o.documentElement.scrollTop)||(null==o?void 0:o.body)&&(null==(r=di.parentElement(o.body))?void 0:r.scrollTop)||(null==(n=null==o?void 0:o.body)?void 0:n.scrollTop)||0}},getWindowWidth:function(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth},hasShadowRoot:function(t){return!!t&&(t instanceof ri&&"shadowRoot"in t?Boolean(t.shadowRoot):Boolean(di.shadowRoot(t)))},hookSetter:function t(e,i,r,n,o){void 0===o&&(o=window);var s=o.Object.getOwnPropertyDescriptor(e,i);return o.Object.defineProperty(e,i,n?r:{set(t){setTimeout((()=>{r.set.call(this,t)}),0),s&&s.set&&s.set.call(this,t)}}),()=>t(e,i,s||{},!0)},inDom:function(t){var e=t.ownerDocument;return!!e&&(di.contains(e,t)||bi(t))},isAncestorRemoved:function t(e,i){if(n=(r=e)&&"host"in r&&"mode"in r&&ti.host(r)||null,Boolean(n&&"shadowRoot"in n&&ti.shadowRoot(n)===r))return!1;var r,n,o=i.getId(e);if(!i.has(o))return!0;var s=di.parentNode(e);return(!s||s.nodeType!==e.DOCUMENT_NODE)&&(!s||t(s,i))},isBlocked:function(t,e,i,r){if(!t)return!1;var n=gi(t);if(!n)return!1;try{if("string"==typeof e){if(n.classList.contains(e))return!0;if(r&&null!==n.closest("."+e))return!0}else if(ii(n,e,r))return!0}catch(t){}if(i){if(n.matches(i))return!0;if(r&&null!==n.closest(i))return!0}return!1},isIgnored:function(t,e,i){return!("TITLE"!==t.tagName||!i.headTitleMutations)||-2===e.getId(t)},isSerialized:function(t,e){return-1!==e.getId(t)},isSerializedIframe:function(t,e){return Boolean("IFRAME"===t.nodeName&&e.getMeta(t))},isSerializedStylesheet:function(t,e){return Boolean("LINK"===t.nodeName&&t.nodeType===t.ELEMENT_NODE&&t.getAttribute&&"stylesheet"===t.getAttribute("rel")&&e.getMeta(t))},iterateResolveTree:function t(e,i){i(e.value);for(var r=e.children.length-1;r>=0;r--)t(e.children[r],i)},legacy_isTouchEvent:function(t){return Boolean(t.changedTouches)},get nowTimestamp(){return pi},on:function(t,e,i){void 0===i&&(i=document);var r={capture:!0,passive:!0};return i.addEventListener(t,e,r),()=>i.removeEventListener(t,e,r)},patch:function(t,e,i){try{if(!(e in t))return()=>{};var r=t[e],n=i(r);return"function"==typeof n&&(n.prototype=n.prototype||{},Object.defineProperties(n,{__rrweb_original__:{enumerable:!1,value:r}})),t[e]=n,()=>{t[e]=r}}catch(t){return()=>{}}},polyfill:function(t){void 0===t&&(t=window),"NodeList"in t&&!t.NodeList.prototype.forEach&&(t.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in t&&!t.DOMTokenList.prototype.forEach&&(t.DOMTokenList.prototype.forEach=Array.prototype.forEach)},queueToResolveTrees:function(t){var e={},i=(t,i)=>{var r={value:t,parent:i,children:[]};return e[t.node.id]=r,r},r=[];for(var n of t){var{nextId:o,parentId:s}=n;if(o&&o in e){var a=e[o];if(a.parent){var l=a.parent.children.indexOf(a);a.parent.children.splice(l,0,i(n,a.parent))}else{var u=r.indexOf(a);r.splice(u,0,i(n,null))}}else if(s in e){var h=e[s];h.children.push(i(n,h))}else r.push(i(n,null))}return r},shadowHostInDom:bi,throttle:function(t,e,i){void 0===i&&(i={});var r=null,n=0;return function(){for(var o=arguments.length,s=new Array(o),a=0;a<o;a++)s[a]=arguments[a];var l=Date.now();n||!1!==i.leading||(n=l);var u=e-(l-n),h=this;u<=0||u>e?(r&&(clearTimeout(r),r=null),n=l,t.apply(h,s)):r||!1===i.trailing||(r=setTimeout((()=>{n=!1===i.leading?0:Date.now(),r=null,t.apply(h,s)}),u))}},uniqueTextMutations:function(t){for(var e=new Set,i=[],r=t.length;r--;){var n=t[r];e.has(n.id)||(i.push(n),e.add(n.id))}return i}},Symbol.toStringTag,{value:"Module"})),wi="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Ii="undefined"==typeof Uint8Array?[]:new Uint8Array(256),Ci=0;Ci<64;Ci++)Ii[wi.charCodeAt(Ci)]=Ci;var Si;"undefined"!=typeof window&&window.Blob&&new Blob([(t=>Uint8Array.from(atob(t),(t=>t.charCodeAt(0))))("KGZ1bmN0aW9uKCkgewogICJ1c2Ugc3RyaWN0IjsKICB2YXIgY2hhcnMgPSAiQUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVphYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ejAxMjM0NTY3ODkrLyI7CiAgdmFyIGxvb2t1cCA9IHR5cGVvZiBVaW50OEFycmF5ID09PSAidW5kZWZpbmVkIiA/IFtdIDogbmV3IFVpbnQ4QXJyYXkoMjU2KTsKICBmb3IgKHZhciBpID0gMDsgaSA8IGNoYXJzLmxlbmd0aDsgaSsrKSB7CiAgICBsb29rdXBbY2hhcnMuY2hhckNvZGVBdChpKV0gPSBpOwogIH0KICB2YXIgZW5jb2RlID0gZnVuY3Rpb24oYXJyYXlidWZmZXIpIHsKICAgIHZhciBieXRlcyA9IG5ldyBVaW50OEFycmF5KGFycmF5YnVmZmVyKSwgaTIsIGxlbiA9IGJ5dGVzLmxlbmd0aCwgYmFzZTY0ID0gIiI7CiAgICBmb3IgKGkyID0gMDsgaTIgPCBsZW47IGkyICs9IDMpIHsKICAgICAgYmFzZTY0ICs9IGNoYXJzW2J5dGVzW2kyXSA+PiAyXTsKICAgICAgYmFzZTY0ICs9IGNoYXJzWyhieXRlc1tpMl0gJiAzKSA8PCA0IHwgYnl0ZXNbaTIgKyAxXSA+PiA0XTsKICAgICAgYmFzZTY0ICs9IGNoYXJzWyhieXRlc1tpMiArIDFdICYgMTUpIDw8IDIgfCBieXRlc1tpMiArIDJdID4+IDZdOwogICAgICBiYXNlNjQgKz0gY2hhcnNbYnl0ZXNbaTIgKyAyXSAmIDYzXTsKICAgIH0KICAgIGlmIChsZW4gJSAzID09PSAyKSB7CiAgICAgIGJhc2U2NCA9IGJhc2U2NC5zdWJzdHJpbmcoMCwgYmFzZTY0Lmxlbmd0aCAtIDEpICsgIj0iOwogICAgfSBlbHNlIGlmIChsZW4gJSAzID09PSAxKSB7CiAgICAgIGJhc2U2NCA9IGJhc2U2NC5zdWJzdHJpbmcoMCwgYmFzZTY0Lmxlbmd0aCAtIDIpICsgIj09IjsKICAgIH0KICAgIHJldHVybiBiYXNlNjQ7CiAgfTsKICBjb25zdCBsYXN0QmxvYk1hcCA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgTWFwKCk7CiAgY29uc3QgdHJhbnNwYXJlbnRCbG9iTWFwID0gLyogQF9fUFVSRV9fICovIG5ldyBNYXAoKTsKICBhc3luYyBmdW5jdGlvbiBnZXRUcmFuc3BhcmVudEJsb2JGb3Iod2lkdGgsIGhlaWdodCwgZGF0YVVSTE9wdGlvbnMpIHsKICAgIGNvbnN0IGlkID0gYCR7d2lkdGh9LSR7aGVpZ2h0fWA7CiAgICBpZiAoIk9mZnNjcmVlbkNhbnZhcyIgaW4gZ2xvYmFsVGhpcykgewogICAgICBpZiAodHJhbnNwYXJlbnRCbG9iTWFwLmhhcyhpZCkpIHJldHVybiB0cmFuc3BhcmVudEJsb2JNYXAuZ2V0KGlkKTsKICAgICAgY29uc3Qgb2Zmc2NyZWVuID0gbmV3IE9mZnNjcmVlbkNhbnZhcyh3aWR0aCwgaGVpZ2h0KTsKICAgICAgb2Zmc2NyZWVuLmdldENvbnRleHQoIjJkIik7CiAgICAgIGNvbnN0IGJsb2IgPSBhd2FpdCBvZmZzY3JlZW4uY29udmVydFRvQmxvYihkYXRhVVJMT3B0aW9ucyk7CiAgICAgIGNvbnN0IGFycmF5QnVmZmVyID0gYXdhaXQgYmxvYi5hcnJheUJ1ZmZlcigpOwogICAgICBjb25zdCBiYXNlNjQgPSBlbmNvZGUoYXJyYXlCdWZmZXIpOwogICAgICB0cmFuc3BhcmVudEJsb2JNYXAuc2V0KGlkLCBiYXNlNjQpOwogICAgICByZXR1cm4gYmFzZTY0OwogICAgfSBlbHNlIHsKICAgICAgcmV0dXJuICIiOwogICAgfQogIH0KICBjb25zdCB3b3JrZXIgPSBzZWxmOwogIHdvcmtlci5vbm1lc3NhZ2UgPSBhc3luYyBmdW5jdGlvbihlKSB7CiAgICBpZiAoIk9mZnNjcmVlbkNhbnZhcyIgaW4gZ2xvYmFsVGhpcykgewogICAgICBjb25zdCB7IGlkLCBiaXRtYXAsIHdpZHRoLCBoZWlnaHQsIGRhdGFVUkxPcHRpb25zIH0gPSBlLmRhdGE7CiAgICAgIGNvbnN0IHRyYW5zcGFyZW50QmFzZTY0ID0gZ2V0VHJhbnNwYXJlbnRCbG9iRm9yKAogICAgICAgIHdpZHRoLAogICAgICAgIGhlaWdodCwKICAgICAgICBkYXRhVVJMT3B0aW9ucwogICAgICApOwogICAgICBjb25zdCBvZmZzY3JlZW4gPSBuZXcgT2Zmc2NyZWVuQ2FudmFzKHdpZHRoLCBoZWlnaHQpOwogICAgICBjb25zdCBjdHggPSBvZmZzY3JlZW4uZ2V0Q29udGV4dCgiMmQiKTsKICAgICAgY3R4LmRyYXdJbWFnZShiaXRtYXAsIDAsIDApOwogICAgICBiaXRtYXAuY2xvc2UoKTsKICAgICAgY29uc3QgYmxvYiA9IGF3YWl0IG9mZnNjcmVlbi5jb252ZXJ0VG9CbG9iKGRhdGFVUkxPcHRpb25zKTsKICAgICAgY29uc3QgdHlwZSA9IGJsb2IudHlwZTsKICAgICAgY29uc3QgYXJyYXlCdWZmZXIgPSBhd2FpdCBibG9iLmFycmF5QnVmZmVyKCk7CiAgICAgIGNvbnN0IGJhc2U2NCA9IGVuY29kZShhcnJheUJ1ZmZlcik7CiAgICAgIGlmICghbGFzdEJsb2JNYXAuaGFzKGlkKSAmJiBhd2FpdCB0cmFuc3BhcmVudEJhc2U2NCA9PT0gYmFzZTY0KSB7CiAgICAgICAgbGFzdEJsb2JNYXAuc2V0KGlkLCBiYXNlNjQpOwogICAgICAgIHJldHVybiB3b3JrZXIucG9zdE1lc3NhZ2UoeyBpZCB9KTsKICAgICAgfQogICAgICBpZiAobGFzdEJsb2JNYXAuZ2V0KGlkKSA9PT0gYmFzZTY0KSByZXR1cm4gd29ya2VyLnBvc3RNZXNzYWdlKHsgaWQgfSk7CiAgICAgIHdvcmtlci5wb3N0TWVzc2FnZSh7CiAgICAgICAgaWQsCiAgICAgICAgdHlwZSwKICAgICAgICBiYXNlNjQsCiAgICAgICAgd2lkdGgsCiAgICAgICAgaGVpZ2h0CiAgICAgIH0pOwogICAgICBsYXN0QmxvYk1hcC5zZXQoaWQsIGJhc2U2NCk7CiAgICB9IGVsc2UgewogICAgICByZXR1cm4gd29ya2VyLnBvc3RNZXNzYWdlKHsgaWQ6IGUuZGF0YS5pZCB9KTsKICAgIH0KICB9Owp9KSgpOwovLyMgc291cmNlTWFwcGluZ1VSTD1pbWFnZS1iaXRtYXAtZGF0YS11cmwtd29ya2VyLUlKcEM3Z19iLmpzLm1hcAo=")],{type:"text/javascript;charset=utf-8"});try{if(2!==Array.from([1],(t=>2*t))[0]){var ki=document.createElement("iframe");document.body.appendChild(ki),Array.from=(null==($e=ki.contentWindow)?void 0:$e.Array.from)||Array.from,document.body.removeChild(ki)}}catch(t){console.debug("Unable to override Array.from",t)}new ei,function(t){t[t.NotStarted=0]="NotStarted",t[t.Running=1]="Running",t[t.Stopped=2]="Stopped"}(Si||(Si={}));class xi{constructor(t){qe(this,"fileName"),qe(this,"functionName"),qe(this,"lineNumber"),qe(this,"columnNumber"),this.fileName=t.fileName||"",this.functionName=t.functionName||"",this.lineNumber=t.lineNumber,this.columnNumber=t.columnNumber}toString(){var t=this.lineNumber||"",e=this.columnNumber||"";return this.functionName?this.functionName+" ("+this.fileName+":"+t+":"+e+")":this.fileName+":"+t+":"+e}}var Ai=/(^|@)\S+:\d+/,Ti=/^\s*at .*(\S+:\d+|\(native\))/m,Mi=/^(eval@)?(\[native code])?$/,Ri={parse:function(t){return t?void 0!==t.stacktrace||void 0!==t["opera#sourceloc"]?this.parseOpera(t):t.stack&&t.stack.match(Ti)?this.parseV8OrIE(t):t.stack?this.parseFFOrSafari(t):(console.warn("[console-record-plugin]: Failed to parse error object:",t),[]):[]},extractLocation:function(t){if(-1===t.indexOf(":"))return[t];var e=/(.+?)(?::(\d+))?(?::(\d+))?$/.exec(t.replace(/[()]/g,""));if(!e)throw new Error("Cannot parse given url: "+t);return[e[1],e[2]||void 0,e[3]||void 0]},parseV8OrIE:function(t){return t.stack.split("\n").filter((function(t){return!!t.match(Ti)}),this).map((function(t){t.indexOf("(eval ")>-1&&(t=t.replace(/eval code/g,"eval").replace(/(\(eval at [^()]*)|(\),.*$)/g,""));var e=t.replace(/^\s+/,"").replace(/\(eval code/g,"("),i=e.match(/ (\((.+):(\d+):(\d+)\)$)/),r=(e=i?e.replace(i[0],""):e).split(/\s+/).slice(1),n=this.extractLocation(i?i[1]:r.pop()),o=r.join(" ")||void 0,s=["eval","<anonymous>"].indexOf(n[0])>-1?void 0:n[0];return new xi({functionName:o,fileName:s,lineNumber:n[1],columnNumber:n[2]})}),this)},parseFFOrSafari:function(t){return t.stack.split("\n").filter((function(t){return!t.match(Mi)}),this).map((function(t){if(t.indexOf(" > eval")>-1&&(t=t.replace(/ line (\d+)(?: > eval line \d+)* > eval:\d+:\d+/g,":$1")),-1===t.indexOf("@")&&-1===t.indexOf(":"))return new xi({functionName:t});var e=/((.*".+"[^@]*)?[^@]*)(?:@)/,i=t.match(e),r=i&&i[1]?i[1]:void 0,n=this.extractLocation(t.replace(e,""));return new xi({functionName:r,fileName:n[0],lineNumber:n[1],columnNumber:n[2]})}),this)},parseOpera:function(t){return!t.stacktrace||t.message.indexOf("\n")>-1&&t.message.split("\n").length>t.stacktrace.split("\n").length?this.parseOpera9(t):t.stack?this.parseOpera11(t):this.parseOpera10(t)},parseOpera9:function(t){for(var e=/Line (\d+).*script (?:in )?(\S+)/i,i=t.message.split("\n"),r=[],n=2,o=i.length;n<o;n+=2){var s=e.exec(i[n]);s&&r.push(new xi({fileName:s[2],lineNumber:parseFloat(s[1])}))}return r},parseOpera10:function(t){for(var e=/Line (\d+).*script (?:in )?(\S+)(?:: In function (\S+))?$/i,i=t.stacktrace.split("\n"),r=[],n=0,o=i.length;n<o;n+=2){var s=e.exec(i[n]);s&&r.push(new xi({functionName:s[3]||void 0,fileName:s[2],lineNumber:parseFloat(s[1])}))}return r},parseOpera11:function(t){return t.stack.split("\n").filter((function(t){return!!t.match(Ai)&&!t.match(/^Error created at/)}),this).map((function(t){var e=t.split("@"),i=this.extractLocation(e.pop()),r=(e.shift()||"").replace(/<anonymous function(: (\w+))?>/,"$2").replace(/\([^)]*\)/g,"")||void 0;return new xi({functionName:r,fileName:i[0],lineNumber:i[1],columnNumber:i[2]})}),this)}};function Ni(t){if(!t||!t.outerHTML)return"";for(var e="";t.parentElement;){var i=t.localName;if(!i)break;i=i.toLowerCase();var r=t.parentElement,n=[];if(r.children&&r.children.length>0)for(var o=0;o<r.children.length;o++){var s=r.children[o];s.localName&&s.localName.toLowerCase&&s.localName.toLowerCase()===i&&n.push(s)}n.length>1&&(i+=":eq("+n.indexOf(t)+")"),e=i+(e?">"+e:""),t=r}return e}function Ei(t){return"[object Object]"===Object.prototype.toString.call(t)}function Fi(t,e){if(0===e)return!0;var i=Object.keys(t);for(var r of i)if(Ei(t[r])&&Fi(t[r],e-1))return!0;return!1}function Oi(t,e){var i={numOfKeysLimit:50,depthOfLimit:4};Object.assign(i,e);var r=[],n=[];return JSON.stringify(t,(function(t,e){if(r.length>0){var o=r.indexOf(this);~o?r.splice(o+1):r.push(this),~o?n.splice(o,1/0,t):n.push(t),~r.indexOf(e)&&(e=r[0]===e?"[Circular ~]":"[Circular ~."+n.slice(0,r.indexOf(e)).join(".")+"]")}else r.push(e);if(null===e)return e;if(void 0===e)return"undefined";if(function(t){if(Ei(t)&&Object.keys(t).length>i.numOfKeysLimit)return!0;if("function"==typeof t)return!0;if(Ei(t)&&Fi(t,i.depthOfLimit))return!0;return!1}(e))return function(t){var e=t.toString();i.stringLengthLimit&&e.length>i.stringLengthLimit&&(e=e.slice(0,i.stringLengthLimit)+"...");return e}(e);if("bigint"==typeof e)return e.toString()+"n";if(e instanceof Event){var s={};for(var a in e){var l=e[a];Array.isArray(l)?s[a]=Ni(l.length?l[0]:null):s[a]=l}return s}return e instanceof Node?e instanceof HTMLElement?e?e.outerHTML:"":e.nodeName:e instanceof Error?e.stack?e.stack+"\nEnd of stack for Error object":e.name+": "+e.message:e}))}var Pi={level:["assert","clear","count","countReset","debug","dir","dirxml","error","group","groupCollapsed","groupEnd","info","log","table","time","timeEnd","timeLog","trace","warn"],lengthThreshold:1e3,logger:"console"};function Di(t,e,i){var r,n=i?Object.assign({},Pi,i):Pi,o=n.logger;if(!o)return()=>{};r="string"==typeof o?e[o]:o;var s=0,a=!1,l=[];if(n.level.includes("error")){var u=e=>{var i=e.message,r=e.error,o=Ri.parse(r).map((t=>t.toString())),s=[Oi(i,n.stringifyOptions)];t({level:"error",trace:o,payload:s})};e.addEventListener("error",u),l.push((()=>{e.removeEventListener("error",u)}));var h=e=>{var i,r;e.reason instanceof Error?r=[Oi("Uncaught (in promise) "+(i=e.reason).name+": "+i.message,n.stringifyOptions)]:(i=new Error,r=[Oi("Uncaught (in promise)",n.stringifyOptions),Oi(e.reason,n.stringifyOptions)]);var o=Ri.parse(i).map((t=>t.toString()));t({level:"error",trace:o,payload:r})};e.addEventListener("unhandledrejection",h),l.push((()=>{e.removeEventListener("unhandledrejection",h)}))}for(var c of n.level)l.push(d(r,c));return()=>{l.forEach((t=>t()))};function d(e,i){var r=this;return e[i]?_i.patch(e,i,(e=>function(){for(var o=arguments.length,l=new Array(o),u=0;u<o;u++)l[u]=arguments[u];if(e.apply(r,l),!("assert"===i&&l[0]||a)){a=!0;try{var h=Ri.parse(new Error).map((t=>t.toString())).splice(1),c=("assert"===i?l.slice(1):l).map((t=>Oi(t,n.stringifyOptions)));++s<n.lengthThreshold?t({level:i,trace:h,payload:c}):s===n.lengthThreshold&&t({level:"warn",trace:[],payload:[Oi("The number of log records reached the threshold.")]})}catch(t){e("rrweb logger error:",t,...l)}finally{a=!1}}})):()=>{}}}var Li=t=>({name:"rrweb/console@1",observer:Di,options:t}),Bi="undefined"!=typeof window?window:void 0,$i="undefined"!=typeof globalThis?globalThis:Bi,Zi=Array.prototype,qi=Zi.forEach,Hi=Zi.indexOf,Vi=null==$i?void 0:$i.navigator,ji=null==$i?void 0:$i.document,Yi=null==$i?void 0:$i.location,zi=null==$i?void 0:$i.fetch,Gi=null!=$i&&$i.XMLHttpRequest&&"withCredentials"in new $i.XMLHttpRequest?$i.XMLHttpRequest:void 0,Wi=null==$i?void 0:$i.AbortController,Ui=null==Vi?void 0:Vi.userAgent,Xi=null!=Bi?Bi:{},Ji="$copy_autocapture",Ki=["$snapshot","$pageview","$pageleave","$set","survey dismissed","survey sent","survey shown","$identify","$groupidentify","$create_alias","$$client_ingestion_warning","$web_experiment_applied","$feature_enrollment_update","$feature_flag_called"],Qi=function(t){return t.GZipJS="gzip-js",t.Base64="base64",t}({}),tr=["fatal","error","warning","log","info","debug"];function er(t,e){return-1!==t.indexOf(e)}var ir=function(t){return t.trim()},rr=function(t){return t.replace(/^\$/,"")};var nr=Array.isArray,or=Object.prototype,sr=or.hasOwnProperty,ar=or.toString,lr=nr||function(t){return"[object Array]"===ar.call(t)},ur=t=>"function"==typeof t,hr=t=>ur(t)&&-1!==t.toString().indexOf("[native code]"),cr=()=>!!Bi.Zone,dr=t=>t===Object(t)&&!lr(t),vr=t=>{if(dr(t)){for(var e in t)if(sr.call(t,e))return!1;return!0}return!1},fr=t=>void 0===t,pr=t=>"[object String]"==ar.call(t),gr=t=>pr(t)&&0===t.trim().length,mr=t=>null===t,yr=t=>fr(t)||mr(t),br=t=>"[object Number]"==ar.call(t),_r=t=>"[object Boolean]"===ar.call(t),wr=t=>t instanceof Document,Ir=t=>t instanceof FormData,Cr=t=>er(Ki,t),Sr={DEBUG:!1,LIB_VERSION:"1.255.1"},kr=t=>{var e={o:function(e){if(Bi&&(Sr.DEBUG||Xi.POSTHOG_DEBUG)&&!fr(Bi.console)&&Bi.console){for(var i=("__rrweb_original__"in Bi.console[e]?Bi.console[e].__rrweb_original__:Bi.console[e]),r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];i(t,...n)}},info:function(){for(var t=arguments.length,i=new Array(t),r=0;r<t;r++)i[r]=arguments[r];e.o("log",...i)},warn:function(){for(var t=arguments.length,i=new Array(t),r=0;r<t;r++)i[r]=arguments[r];e.o("warn",...i)},error:function(){for(var t=arguments.length,i=new Array(t),r=0;r<t;r++)i[r]=arguments[r];e.o("error",...i)},critical:function(){for(var e=arguments.length,i=new Array(e),r=0;r<e;r++)i[r]=arguments[r];console.error(t,...i)},uninitializedWarning:t=>{e.error("You must initialize PostHog before calling "+t)},createLogger:e=>kr(t+" "+e)};return e},xr=kr("[PostHog.js]"),Ar=xr.createLogger,Tr={};function Mr(t,e,i){if(lr(t))if(qi&&t.forEach===qi)t.forEach(e,i);else if("length"in t&&t.length===+t.length)for(var r=0,n=t.length;r<n;r++)if(r in t&&e.call(i,t[r],r)===Tr)return}function Rr(t,e,i){if(!yr(t)){if(lr(t))return Mr(t,e,i);if(Ir(t)){for(var r of t.entries())if(e.call(i,r[1],r[0])===Tr)return}else for(var n in t)if(sr.call(t,n)&&e.call(i,t[n],n)===Tr)return}}var Nr=function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];return Mr(i,(function(e){for(var i in e)void 0!==e[i]&&(t[i]=e[i])})),t},Er=function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];return Mr(i,(function(e){Mr(e,(function(e){t.push(e)}))})),t};function Fr(t){for(var e=Object.keys(t),i=e.length,r=new Array(i);i--;)r[i]=[e[i],t[e[i]]];return r}var Or=function(t){try{return t()}catch(t){return}},Pr=function(t){return function(){try{for(var e=arguments.length,i=new Array(e),r=0;r<e;r++)i[r]=arguments[r];return t.apply(this,i)}catch(t){xr.critical("Implementation error. Please turn on debug mode and open a ticket on https://app.posthog.com/home#panel=support%3Asupport%3A."),xr.critical(t)}}},Dr=function(t){var e={};return Rr(t,(function(t,i){(pr(t)&&t.length>0||br(t))&&(e[i]=t)})),e};function Lr(t,e){return i=t,r=t=>pr(t)&&!mr(e)?t.slice(0,e):t,n=new Set,function t(e,i){return e!==Object(e)?r?r(e,i):e:n.has(e)?void 0:(n.add(e),lr(e)?(o=[],Mr(e,(e=>{o.push(t(e))}))):(o={},Rr(e,((e,i)=>{n.has(e)||(o[i]=t(e,i))}))),o);var o}(i);var i,r,n}var Br=["herokuapp.com","vercel.app","netlify.app"];function $r(t){var e=null==t?void 0:t.hostname;if(!pr(e))return!1;var i=e.split(".").slice(-2).join(".");for(var r of Br)if(i===r)return!1;return!0}function Zr(t,e){for(var i=0;i<t.length;i++)if(e(t[i]))return t[i]}function qr(t,e,i,r){var{capture:n=!1,passive:o=!0}=null!=r?r:{};null==t||t.addEventListener(e,i,{capture:n,passive:o})}var Hr=["localhost","127.0.0.1"],Vr=t=>{var e=null==ji?void 0:ji.createElement("a");return fr(e)?null:(e.href=t,e)},jr=function(t,e){var i,r;void 0===e&&(e="&");var n=[];return Rr(t,(function(t,e){fr(t)||fr(e)||"undefined"===e||(i=encodeURIComponent((t=>t instanceof File)(t)?t.name:t.toString()),r=encodeURIComponent(e),n[n.length]=r+"="+i)})),n.join(e)},Yr=function(t,e){for(var i,r=((t.split("#")[0]||"").split(/\?(.*)/)[1]||"").replace(/^\?+/g,"").split("&"),n=0;n<r.length;n++){var o=r[n].split("=");if(o[0]===e){i=o;break}}if(!lr(i)||i.length<2)return"";var s=i[1];try{s=decodeURIComponent(s)}catch(t){xr.error("Skipping decoding for malformed query param: "+s)}return s.replace(/\+/g," ")},zr=function(t,e,i){if(!t||!e||!e.length)return t;for(var r=t.split("#"),n=r[0]||"",o=r[1],s=n.split("?"),a=s[1],l=s[0],u=(a||"").split("&"),h=[],c=0;c<u.length;c++){var d=u[c].split("=");lr(d)&&(e.includes(d[0])?h.push(d[0]+"="+i):h.push(u[c]))}var v=l;return null!=a&&(v+="?"+h.join("&")),null!=o&&(v+="#"+o),v},Gr=function(t,e){var i=t.match(new RegExp(e+"=([^&]*)"));return i?i[1]:null};function Wr(t,e,i){try{if(!(e in t))return()=>{};var r=t[e],n=i(r);return ur(n)&&(n.prototype=n.prototype||{},Object.defineProperties(n,{__posthog_wrapped__:{enumerable:!1,value:!0}})),t[e]=n,()=>{t[e]=r}}catch(t){return()=>{}}}function Ur(t,e){var i,r=function(t){try{return"string"==typeof t?new URL(t).hostname:"url"in t?new URL(t.url).hostname:t.hostname}catch(t){return null}}(t),n={hostname:r,isHostDenied:!1};if(null==(i=e.payloadHostDenyList)||!i.length||null==r||!r.trim().length)return n;for(var o of e.payloadHostDenyList)if(r.endsWith(o))return{hostname:r,isHostDenied:!0};return n}var Xr="$people_distinct_id",Jr="__alias",Kr="__timers",Qr="$autocapture_disabled_server_side",tn="$heatmaps_enabled_server_side",en="$exception_capture_enabled_server_side",rn="$error_tracking_suppression_rules",nn="$web_vitals_enabled_server_side",on="$dead_clicks_enabled_server_side",sn="$web_vitals_allowed_metrics",an="$session_recording_enabled_server_side",ln="$console_log_recording_enabled_server_side",un="$session_recording_network_payload_capture",hn="$session_recording_masking",cn="$session_recording_canvas_recording",dn="$replay_sample_rate",vn="$replay_minimum_duration",fn="$replay_script_config",pn="$sesid",gn="$session_is_sampled",mn="$session_recording_url_trigger_activated_session",yn="$session_recording_event_trigger_activated_session",bn="$enabled_feature_flags",_n="$early_access_features",wn="$feature_flag_details",In="$stored_person_properties",Cn="$stored_group_properties",Sn="$surveys",kn="$surveys_activated",xn="$flag_call_reported",An="$user_state",Tn="$client_session_props",Mn="$capture_rate_limit",Rn="$initial_campaign_params",Nn="$initial_referrer_info",En="$initial_person_info",Fn="$epp",On="__POSTHOG_TOOLBAR__",Pn="$posthog_cookieless",Dn=[Xr,Jr,"__cmpns",Kr,an,tn,pn,bn,rn,An,_n,wn,Cn,In,Sn,xn,Tn,Mn,Rn,Nn,Fn,En];function Ln(t){return t instanceof Element&&(t.id===On||!(null==t.closest||!t.closest(".toolbar-global-fade-container")))}function Bn(t){return!!t&&1===t.nodeType}function $n(t,e){return!!t&&!!t.tagName&&t.tagName.toLowerCase()===e.toLowerCase()}function Zn(t){return!!t&&3===t.nodeType}function qn(t){return!!t&&11===t.nodeType}function Hn(t){return t?ir(t).split(/\s+/):[]}function Vn(t){var e=null==Bi?void 0:Bi.location.href;return!!(e&&t&&t.some((t=>e.match(t))))}function jn(t){var e="";switch(typeof t.className){case"string":e=t.className;break;case"object":e=(t.className&&"baseVal"in t.className?t.className.baseVal:null)||t.getAttribute("class")||"";break;default:e=""}return Hn(e)}function Yn(t){return yr(t)?null:ir(t).split(/(\s+)/).filter((t=>oo(t))).join("").replace(/[\r\n]/g," ").replace(/[ ]+/g," ").substring(0,255)}function zn(t){var e="";return Jn(t)&&!Kn(t)&&t.childNodes&&t.childNodes.length&&Rr(t.childNodes,(function(t){var i;Zn(t)&&t.textContent&&(e+=null!==(i=Yn(t.textContent))&&void 0!==i?i:"")})),ir(e)}function Gn(t){return fr(t.target)?t.srcElement||null:null!=(e=t.target)&&e.shadowRoot?t.composedPath()[0]||null:t.target||null;var e}var Wn=["a","button","form","input","select","textarea","label"];function Un(t){var e=t.parentNode;return!(!e||!Bn(e))&&e}function Xn(t,e,i,r,n){var o,s,a;if(void 0===i&&(i=void 0),!Bi||!t||$n(t,"html")||!Bn(t))return!1;if(null!=(o=i)&&o.url_allowlist&&!Vn(i.url_allowlist))return!1;if(null!=(s=i)&&s.url_ignorelist&&Vn(i.url_ignorelist))return!1;if(null!=(a=i)&&a.dom_event_allowlist){var l=i.dom_event_allowlist;if(l&&!l.some((t=>e.type===t)))return!1}for(var u=!1,h=[t],c=!0,d=t;d.parentNode&&!$n(d,"body");)if(qn(d.parentNode))h.push(d.parentNode.host),d=d.parentNode.host;else{if(!(c=Un(d)))break;if(r||Wn.indexOf(c.tagName.toLowerCase())>-1)u=!0;else{var v=Bi.getComputedStyle(c);v&&"pointer"===v.getPropertyValue("cursor")&&(u=!0)}h.push(c),d=c}if(!function(t,e){var i=null==e?void 0:e.element_allowlist;if(fr(i))return!0;var r,n=function(t){if(i.some((e=>t.tagName.toLowerCase()===e)))return{v:!0}};for(var o of t)if(r=n(o))return r.v;return!1}(h,i))return!1;if(!function(t,e){var i=null==e?void 0:e.css_selector_allowlist;if(fr(i))return!0;var r,n=function(t){if(i.some((e=>t.matches(e))))return{v:!0}};for(var o of t)if(r=n(o))return r.v;return!1}(h,i))return!1;var f=Bi.getComputedStyle(t);if(f&&"pointer"===f.getPropertyValue("cursor")&&"click"===e.type)return!0;var p=t.tagName.toLowerCase();switch(p){case"html":return!1;case"form":return(n||["submit"]).indexOf(e.type)>=0;case"input":case"select":case"textarea":return(n||["change","click"]).indexOf(e.type)>=0;default:return u?(n||["click"]).indexOf(e.type)>=0:(n||["click"]).indexOf(e.type)>=0&&(Wn.indexOf(p)>-1||"true"===t.getAttribute("contenteditable"))}}function Jn(t){for(var e=t;e.parentNode&&!$n(e,"body");e=e.parentNode){var i=jn(e);if(er(i,"ph-sensitive")||er(i,"ph-no-capture"))return!1}if(er(jn(t),"ph-include"))return!0;var r=t.type||"";if(pr(r))switch(r.toLowerCase()){case"hidden":case"password":return!1}var n=t.name||t.id||"";if(pr(n)){if(/^cc|cardnum|ccnum|creditcard|csc|cvc|cvv|exp|pass|pwd|routing|seccode|securitycode|securitynum|socialsec|socsec|ssn/i.test(n.replace(/[^a-zA-Z0-9]/g,"")))return!1}return!0}function Kn(t){return!!($n(t,"input")&&!["button","checkbox","submit","reset"].includes(t.type)||$n(t,"select")||$n(t,"textarea")||"true"===t.getAttribute("contenteditable"))}var Qn="(4[0-9]{12}(?:[0-9]{3})?)|(5[1-5][0-9]{14})|(6(?:011|5[0-9]{2})[0-9]{12})|(3[47][0-9]{13})|(3(?:0[0-5]|[68][0-9])[0-9]{11})|((?:2131|1800|35[0-9]{3})[0-9]{11})",to=new RegExp("^(?:"+Qn+")$"),eo=new RegExp(Qn),io="\\d{3}-?\\d{2}-?\\d{4}",ro=new RegExp("^("+io+")$"),no=new RegExp("("+io+")");function oo(t,e){if(void 0===e&&(e=!0),yr(t))return!1;if(pr(t)){if(t=ir(t),(e?to:eo).test((t||"").replace(/[- ]/g,"")))return!1;if((e?ro:no).test(t))return!1}return!0}function so(t){var e=zn(t);return oo(e=(e+" "+ao(t)).trim())?e:""}function ao(t){var e="";return t&&t.childNodes&&t.childNodes.length&&Rr(t.childNodes,(function(t){var i;if(t&&"span"===(null==(i=t.tagName)?void 0:i.toLowerCase()))try{var r=zn(t);e=(e+" "+r).trim(),t.childNodes&&t.childNodes.length&&(e=(e+" "+ao(t)).trim())}catch(t){xr.error("[AutoCapture]",t)}})),e}function lo(t){return function(t){var e=t.map((t=>{var e,r,n="";if(t.tag_name&&(n+=t.tag_name),t.attr_class)for(var o of(t.attr_class.sort(),t.attr_class))n+="."+o.replace(/"/g,"");var s=i({},t.text?{text:t.text}:{},{"nth-child":null!==(e=t.nth_child)&&void 0!==e?e:0,"nth-of-type":null!==(r=t.nth_of_type)&&void 0!==r?r:0},t.href?{href:t.href}:{},t.attr_id?{attr_id:t.attr_id}:{},t.attributes),a={};return Fr(s).sort(((t,e)=>{var[i]=t,[r]=e;return i.localeCompare(r)})).forEach((t=>{var[e,i]=t;return a[uo(e.toString())]=uo(i.toString())})),n+=":",n+=Fr(a).map((t=>{var[e,i]=t;return e+'="'+i+'"'})).join("")}));return e.join(";")}(function(t){return t.map((t=>{var e,i,r={text:null==(e=t.$el_text)?void 0:e.slice(0,400),tag_name:t.tag_name,href:null==(i=t.attr__href)?void 0:i.slice(0,2048),attr_class:ho(t),attr_id:t.attr__id,nth_child:t.nth_child,nth_of_type:t.nth_of_type,attributes:{}};return Fr(t).filter((t=>{var[e]=t;return 0===e.indexOf("attr__")})).forEach((t=>{var[e,i]=t;return r.attributes[e]=i})),r}))}(t))}function uo(t){return t.replace(/"|\\"/g,'\\"')}function ho(t){var e=t.attr__class;return e?lr(e)?e:Hn(e):void 0}var co="[SessionRecording]",vo="redacted",fo={initiatorTypes:["audio","beacon","body","css","early-hint","embed","fetch","frame","iframe","icon","image","img","input","link","navigation","object","ping","script","track","video","xmlhttprequest"],maskRequestFn:t=>t,recordHeaders:!1,recordBody:!1,recordInitialRequests:!1,recordPerformance:!1,performanceEntryTypeToObserve:["first-input","navigation","paint","resource"],payloadSizeLimitBytes:1e6,payloadHostDenyList:[".lr-ingest.io",".ingest.sentry.io",".clarity.ms","analytics.google.com","bam.nr-data.net"]},po=["authorization","x-forwarded-for","authorization","cookie","set-cookie","x-api-key","x-real-ip","remote-addr","forwarded","proxy-authorization","x-csrf-token","x-csrftoken","x-xsrf-token"],go=["password","secret","passwd","api_key","apikey","auth","credentials","mysql_pwd","privatekey","private_key","token"],mo=["/s/","/e/","/i/"];function yo(t,e,i,r){if(yr(t))return t;var n=(null==e?void 0:e["content-length"])||function(t){return new Blob([t]).size}(t);return pr(n)&&(n=parseInt(n)),n>i?co+" "+r+" body too large to record ("+n+" bytes)":t}function bo(t,e){if(yr(t))return t;var i=t;return oo(i,!1)||(i=co+" "+e+" body "+vo),Rr(go,(t=>{var r,n;null!=(r=i)&&r.length&&-1!==(null==(n=i)?void 0:n.indexOf(t))&&(i=co+" "+e+" body "+vo+" as might contain: "+t)})),i}var _o=(t,e)=>{var r,n,o,s={payloadSizeLimitBytes:fo.payloadSizeLimitBytes,performanceEntryTypeToObserve:[...fo.performanceEntryTypeToObserve],payloadHostDenyList:[...e.payloadHostDenyList||[],...fo.payloadHostDenyList]},a=!1!==t.session_recording.recordHeaders&&e.recordHeaders,l=!1!==t.session_recording.recordBody&&e.recordBody,u=!1!==t.capture_performance&&e.recordPerformance,h=(r=s,o=Math.min(1e6,null!==(n=r.payloadSizeLimitBytes)&&void 0!==n?n:1e6),t=>(null!=t&&t.requestBody&&(t.requestBody=yo(t.requestBody,t.requestHeaders,o,"Request")),null!=t&&t.responseBody&&(t.responseBody=yo(t.responseBody,t.responseHeaders,o,"Response")),t)),c=e=>{return h(((t,e)=>{var i,r=Vr(t.name),n=0===e.indexOf("http")?null==(i=Vr(e))?void 0:i.pathname:e;"/"===n&&(n="");var o=null==r?void 0:r.pathname.replace(n||"","");if(!(r&&o&&mo.some((t=>0===o.indexOf(t)))))return t})((r=(i=e).requestHeaders,yr(r)||Rr(Object.keys(null!=r?r:{}),(t=>{po.includes(t.toLowerCase())&&(r[t]=vo)})),i),t.api_host));var i,r},d=ur(t.session_recording.maskNetworkRequestFn);return d&&ur(t.session_recording.maskCapturedNetworkRequestFn)&&xr.warn("Both `maskNetworkRequestFn` and `maskCapturedNetworkRequestFn` are defined. `maskNetworkRequestFn` will be ignored."),d&&(t.session_recording.maskCapturedNetworkRequestFn=e=>{var r=t.session_recording.maskNetworkRequestFn({url:e.name});return i({},e,{name:null==r?void 0:r.url})}),s.maskRequestFn=ur(t.session_recording.maskCapturedNetworkRequestFn)?e=>{var i,r=c(e);return r&&null!==(i=null==t.session_recording.maskCapturedNetworkRequestFn?void 0:t.session_recording.maskCapturedNetworkRequestFn(r))&&void 0!==i?i:void 0}:t=>function(t){if(!fr(t))return t.requestBody=bo(t.requestBody,"Request"),t.responseBody=bo(t.responseBody,"Response"),t}(c(t)),i({},fo,s,{recordHeaders:a,recordBody:l,recordPerformance:u,recordInitialRequests:u})},wo=Ar("[Recorder]"),Io=t=>"navigation"===t.entryType,Co=t=>"resource"===t.entryType;function So(t,e,i){if(i.recordInitialRequests){var r=e.performance.getEntries().filter((t=>Io(t)||Co(t)&&i.initiatorTypes.includes(t.initiatorType)));t({requests:r.flatMap((t=>No({entry:t,method:void 0,status:void 0,networkRequest:{},isInitial:!0}))),isInitial:!0})}var n=new e.PerformanceObserver((e=>{var r=e.getEntries().filter((t=>Io(t)||Co(t)&&i.initiatorTypes.includes(t.initiatorType)&&(t=>!i.recordBody&&!i.recordHeaders||"xmlhttprequest"!==t.initiatorType&&"fetch"!==t.initiatorType)(t)));t({requests:r.flatMap((t=>No({entry:t,method:void 0,status:void 0,networkRequest:{}})))})})),o=PerformanceObserver.supportedEntryTypes.filter((t=>i.performanceEntryTypeToObserve.includes(t)));return n.observe({entryTypes:o}),()=>{n.disconnect()}}function ko(t,e){return!!e&&(_r(e)||e[t])}function xo(t){var{type:e,recordBody:i,headers:r,url:n}=t;function o(t){var e=Object.keys(r).find((t=>"content-type"===t.toLowerCase())),i=e&&r[e];return t.some((t=>null==i?void 0:i.includes(t)))}if(!i)return!1;if(function t(e){try{return"string"==typeof e?e.startsWith("blob:"):e instanceof URL?"blob:"===e.protocol:e instanceof Request&&t(e.url)}catch(t){return!1}}(n))return!1;if(_r(i))return!0;if(lr(i))return o(i);var s=i[e];return _r(s)?s:o(s)}function Ao(t,e,i,r,n,o){return To.apply(this,arguments)}function To(){return To=e((function*(t,e,i,r,n,o){if(void 0===o&&(o=0),o>10)return wo.warn("Failed to get performance entry for request",{url:i,initiatorType:e}),null;var s=function(t,e){for(var i=t.length-1;i>=0;i-=1)if(e(t[i]))return t[i]}(t.performance.getEntriesByName(i),(t=>Co(t)&&t.initiatorType===e&&(fr(r)||t.startTime>=r)&&(fr(n)||t.startTime<=n)));return s||(yield new Promise((t=>setTimeout(t,50*o))),Ao(t,e,i,r,n,o+1))})),To.apply(this,arguments)}function Mo(t){var{body:e,options:i,url:r}=t;if(yr(e))return null;var{hostname:n,isHostDenied:o}=Ur(r,i);if(o)return n+" is in deny list";if(pr(e))return e;if(wr(e))return e.textContent;if(Ir(e))return jr(e);if(dr(e))try{return JSON.stringify(e)}catch(t){return"[SessionReplay] Failed to stringify response object"}return"[SessionReplay] Cannot read body of type "+toString.call(e)}var Ro=t=>!mr(t)&&("navigation"===t.entryType||"resource"===t.entryType);function No(t){var{entry:e,method:r,status:n,networkRequest:o,isInitial:s,start:a,end:l,url:u,initiatorType:h}=t;a=e?e.startTime:a,l=e?e.responseEnd:l;var c=Math.floor(Date.now()-performance.now()),d=Math.floor(c+(a||0)),v=[i({},e?e.toJSON():{name:u},{startTime:fr(a)?void 0:Math.round(a),endTime:fr(l)?void 0:Math.round(l),timeOrigin:c,timestamp:d,method:r,initiatorType:h||(e?e.initiatorType:void 0),status:n,requestHeaders:o.requestHeaders,requestBody:o.requestBody,responseHeaders:o.responseHeaders,responseBody:o.responseBody,isInitial:s})];if(Ro(e))for(var f of e.serverTiming||[])v.push({timeOrigin:c,timestamp:d,startTime:Math.round(e.startTime),name:f.name,duration:f.duration,entryType:"serverTiming"});return v}var Eo=["video/","audio/"];function Fo(t){return new Promise(((e,i)=>{var r=setTimeout((()=>e("[SessionReplay] Timeout while trying to read body")),500);try{t.clone().text().then((t=>e(t)),(t=>i(t))).finally((()=>clearTimeout(r)))}catch(t){clearTimeout(r),e("[SessionReplay] Failed to read body")}}))}function Oo(){return Oo=e((function*(t){var{r:e,options:i,url:r}=t,{hostname:n,isHostDenied:o}=Ur(r,i);return o?Promise.resolve(n+" is in deny list"):Fo(e)})),Oo.apply(this,arguments)}function Po(){return Po=e((function*(t){var{r:e,options:i,url:r}=t,n=function(t){var e,{r:i,options:r,url:n}=t;if("chunked"===i.headers.get("Transfer-Encoding"))return"Chunked Transfer-Encoding is not supported";var o=null==(e=i.headers.get("Content-Type"))?void 0:e.toLowerCase(),s=Eo.some((t=>null==o?void 0:o.startsWith(t)));if(o&&s)return"Content-Type "+o+" is not supported";var{hostname:a,isHostDenied:l}=Ur(n,r);return l?a+" is in deny list":null}({r:e,options:i,url:r});return mr(n)?Fo(e):Promise.resolve(n)})),Po.apply(this,arguments)}function Do(t,i,r){if(!r.initiatorTypes.includes("fetch"))return()=>{};var n=ko("request",r.recordHeaders),o=ko("response",r.recordHeaders),s=Wr(i,"fetch",(s=>function(){var a=e((function*(e,a){var l,u,h,c=new Request(e,a),d={};try{var v={};c.headers.forEach(((t,e)=>{v[e]=t})),n&&(d.requestHeaders=v),xo({type:"request",headers:v,url:e,recordBody:r.recordBody})&&(d.requestBody=yield function(t){return Oo.apply(this,arguments)}({r:c,options:r,url:e})),u=i.performance.now(),l=yield s(c),h=i.performance.now();var f={};return l.headers.forEach(((t,e)=>{f[e]=t})),o&&(d.responseHeaders=f),xo({type:"response",headers:f,url:e,recordBody:r.recordBody})&&(d.responseBody=yield function(t){return Po.apply(this,arguments)}({r:l,options:r,url:e})),l}finally{Ao(i,"fetch",c.url,u,h).then((e=>{var i,r=No({entry:e,method:c.method,status:null==(i=l)?void 0:i.status,networkRequest:d,start:u,end:h,url:c.url,initiatorType:"fetch"});t({requests:r})})).catch((()=>{}))}}));return function(t,e){return a.apply(this,arguments)}}()));return()=>{s()}}var Lo=null;function Bo(t,e,r){if(!("performance"in e))return()=>{};if(Lo)return wo.warn("Network observer already initialised, doing nothing"),()=>{};var n=r?Object.assign({},fo,r):fo,o=e=>{var r=[];e.requests.forEach((t=>{var e=n.maskRequestFn(t);e&&r.push(e)})),r.length>0&&t(i({},e,{requests:r}))},s=So(o,e,n),a=()=>{},l=()=>{};return(n.recordHeaders||n.recordBody)&&(a=function(t,e,i){if(!i.initiatorTypes.includes("xmlhttprequest"))return()=>{};var r=ko("request",i.recordHeaders),n=ko("response",i.recordHeaders),o=Wr(e.XMLHttpRequest.prototype,"open",(o=>function(s,a,l,u,h){void 0===l&&(l=!0);var c,d,v=this,f=new Request(a),p={},g={},m=v.setRequestHeader.bind(v);v.setRequestHeader=(t,e)=>(g[t]=e,m(t,e)),r&&(p.requestHeaders=g);var y=v.send.bind(v);v.send=t=>(xo({type:"request",headers:g,url:a,recordBody:i.recordBody})&&(p.requestBody=Mo({body:t,options:i,url:a})),c=e.performance.now(),y(t)),v.addEventListener("readystatechange",(()=>{if(v.readyState===v.DONE){d=e.performance.now();var r={};v.getAllResponseHeaders().trim().split(/[\r\n]+/).forEach((t=>{var e=t.split(": "),i=e.shift(),n=e.join(": ");i&&(r[i]=n)})),n&&(p.responseHeaders=r),xo({type:"response",headers:r,url:a,recordBody:i.recordBody})&&(p.responseBody=Mo({body:v.response,options:i,url:a})),Ao(e,"xmlhttprequest",f.url,c,d).then((e=>{var i=No({entry:e,method:s,status:null==v?void 0:v.status,networkRequest:p,start:c,end:d,url:a.toString(),initiatorType:"xmlhttprequest"});t({requests:i})})).catch((()=>{}))}})),o.call(v,s,a,l,u,h)}));return()=>{o()}}(o,e,n),l=Do(o,e,n)),Lo=()=>{s(),a(),l()}}var $o=t=>({name:"rrweb/network@1",observer:Bo,options:t});Xi.__PosthogExtensions__=Xi.__PosthogExtensions__||{},Xi.__PosthogExtensions__.rrwebPlugins={getRecordConsolePlugin:Li,getRecordNetworkPlugin:$o},Xi.__PosthogExtensions__.rrweb={record:Be,version:"v2"},Xi.rrweb={record:Be,version:"v2"},Xi.rrwebConsoleRecord={getRecordConsolePlugin:Li},Xi.getRecordNetworkPlugin=$o;var Zo,qo,Ho,Vo,jo,Yo,zo,Go,Wo={},Uo=[],Xo=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,Jo=Array.isArray;function Ko(t,e){for(var i in e)t[i]=e[i];return t}function Qo(t){var e=t.parentNode;e&&e.removeChild(t)}function ts(t,e,i){var r,n,o,s={};for(o in e)"key"==o?r=e[o]:"ref"==o?n=e[o]:s[o]=e[o];if(arguments.length>2&&(s.children=arguments.length>3?Zo.call(arguments,2):i),"function"==typeof t&&null!=t.defaultProps)for(o in t.defaultProps)void 0===s[o]&&(s[o]=t.defaultProps[o]);return es(t,s,r,n,null)}function es(t,e,i,r,n){var o={type:t,props:e,key:i,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==n?++Ho:n,__i:-1,__u:0};return null==n&&null!=qo.vnode&&qo.vnode(o),o}function is(t){return t.children}function rs(t,e){this.props=t,this.context=e}function ns(t,e){if(null==e)return t.__?ns(t.__,t.__i+1):null;for(var i;e<t.__k.length;e++)if(null!=(i=t.__k[e])&&null!=i.__e)return i.__e;return"function"==typeof t.type?ns(t):null}function os(t){var e,i;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,e=0;e<t.__k.length;e++)if(null!=(i=t.__k[e])&&null!=i.__e){t.__e=t.__c.base=i.__e;break}return os(t)}}function ss(t){(!t.__d&&(t.__d=!0)&&Vo.push(t)&&!as.__r++||jo!==qo.debounceRendering)&&((jo=qo.debounceRendering)||Yo)(as)}function as(){var t,e,i,r,n,o,s,a,l;for(Vo.sort(zo);t=Vo.shift();)t.__d&&(e=Vo.length,r=void 0,o=(n=(i=t).__v).__e,a=[],l=[],(s=i.__P)&&((r=Ko({},n)).__v=n.__v+1,qo.vnode&&qo.vnode(r),ps(s,r,n,i.__n,void 0!==s.ownerSVGElement,32&n.__u?[o]:null,a,null==o?ns(n):o,!!(32&n.__u),l),r.__.__k[r.__i]=r,gs(a,r,l),r.__e!=o&&os(r)),Vo.length>e&&Vo.sort(zo));as.__r=0}function ls(t,e,i,r,n,o,s,a,l,u,h){var c,d,v,f,p,g=r&&r.__k||Uo,m=e.length;for(i.__d=l,function(t,e,i){var r,n,o,s,a,l=e.length,u=i.length,h=u,c=0;for(t.__k=[],r=0;r<l;r++)null!=(n=t.__k[r]=null==(n=e[r])||"boolean"==typeof n||"function"==typeof n?null:"string"==typeof n||"number"==typeof n||"bigint"==typeof n||n.constructor==String?es(null,n,null,null,n):Jo(n)?es(is,{children:n},null,null,null):void 0===n.constructor&&n.__b>0?es(n.type,n.props,n.key,n.ref?n.ref:null,n.__v):n)?(n.__=t,n.__b=t.__b+1,a=hs(n,i,s=r+c,h),n.__i=a,o=null,-1!==a&&(h--,(o=i[a])&&(o.__u|=131072)),null==o||null===o.__v?(-1==a&&c--,"function"!=typeof n.type&&(n.__u|=65536)):a!==s&&(a===s+1?c++:a>s?h>l-s?c+=a-s:c--:c=a<s&&a==s-1?a-s:0,a!==r+c&&(n.__u|=65536))):(o=i[r])&&null==o.key&&o.__e&&(o.__e==t.__d&&(t.__d=ns(o)),ys(o,o,!1),i[r]=null,h--);if(h)for(r=0;r<u;r++)null!=(o=i[r])&&0==(131072&o.__u)&&(o.__e==t.__d&&(t.__d=ns(o)),ys(o,o))}(i,e,g),l=i.__d,c=0;c<m;c++)null!=(v=i.__k[c])&&"boolean"!=typeof v&&"function"!=typeof v&&(d=-1===v.__i?Wo:g[v.__i]||Wo,v.__i=c,ps(t,v,d,n,o,s,a,l,u,h),f=v.__e,v.ref&&d.ref!=v.ref&&(d.ref&&ms(d.ref,null,v),h.push(v.ref,v.__c||f,v)),null==p&&null!=f&&(p=f),65536&v.__u||d.__k===v.__k?l=us(v,l,t):"function"==typeof v.type&&void 0!==v.__d?l=v.__d:f&&(l=f.nextSibling),v.__d=void 0,v.__u&=-196609);i.__d=l,i.__e=p}function us(t,e,i){var r,n;if("function"==typeof t.type){for(r=t.__k,n=0;r&&n<r.length;n++)r[n]&&(r[n].__=t,e=us(r[n],e,i));return e}return t.__e!=e&&(i.insertBefore(t.__e,e||null),e=t.__e),e&&e.nextSibling}function hs(t,e,i,r){var n=t.key,o=t.type,s=i-1,a=i+1,l=e[i];if(null===l||l&&n==l.key&&o===l.type)return i;if(r>(null!=l&&0==(131072&l.__u)?1:0))for(;s>=0||a<e.length;){if(s>=0){if((l=e[s])&&0==(131072&l.__u)&&n==l.key&&o===l.type)return s;s--}if(a<e.length){if((l=e[a])&&0==(131072&l.__u)&&n==l.key&&o===l.type)return a;a++}}return-1}function cs(t,e,i){"-"===e[0]?t.setProperty(e,null==i?"":i):t[e]=null==i?"":"number"!=typeof i||Xo.test(e)?i:i+"px"}function ds(t,e,i,r,n){var o;t:if("style"===e)if("string"==typeof i)t.style.cssText=i;else{if("string"==typeof r&&(t.style.cssText=r=""),r)for(e in r)i&&e in i||cs(t.style,e,"");if(i)for(e in i)r&&i[e]===r[e]||cs(t.style,e,i[e])}else if("o"===e[0]&&"n"===e[1])o=e!==(e=e.replace(/(PointerCapture)$|Capture$/,"$1")),e=e.toLowerCase()in t?e.toLowerCase().slice(2):e.slice(2),t.l||(t.l={}),t.l[e+o]=i,i?r?i.u=r.u:(i.u=Date.now(),t.addEventListener(e,o?fs:vs,o)):t.removeEventListener(e,o?fs:vs,o);else{if(n)e=e.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!==e&&"height"!==e&&"href"!==e&&"list"!==e&&"form"!==e&&"tabIndex"!==e&&"download"!==e&&"rowSpan"!==e&&"colSpan"!==e&&"role"!==e&&e in t)try{t[e]=null==i?"":i;break t}catch(t){}"function"==typeof i||(null==i||!1===i&&"-"!==e[4]?t.removeAttribute(e):t.setAttribute(e,i))}}function vs(t){var e=this.l[t.type+!1];if(t.t){if(t.t<=e.u)return}else t.t=Date.now();return e(qo.event?qo.event(t):t)}function fs(t){return this.l[t.type+!0](qo.event?qo.event(t):t)}function ps(t,e,i,r,n,o,s,a,l,u){var h,c,d,v,f,p,g,m,y,b,_,w,I,C,S,k=e.type;if(void 0!==e.constructor)return null;128&i.__u&&(l=!!(32&i.__u),o=[a=e.__e=i.__e]),(h=qo.__b)&&h(e);t:if("function"==typeof k)try{if(m=e.props,y=(h=k.contextType)&&r[h.__c],b=h?y?y.props.value:h.__:r,i.__c?g=(c=e.__c=i.__c).__=c.__E:("prototype"in k&&k.prototype.render?e.__c=c=new k(m,b):(e.__c=c=new rs(m,b),c.constructor=k,c.render=bs),y&&y.sub(c),c.props=m,c.state||(c.state={}),c.context=b,c.__n=r,d=c.__d=!0,c.__h=[],c._sb=[]),null==c.__s&&(c.__s=c.state),null!=k.getDerivedStateFromProps&&(c.__s==c.state&&(c.__s=Ko({},c.__s)),Ko(c.__s,k.getDerivedStateFromProps(m,c.__s))),v=c.props,f=c.state,c.__v=e,d)null==k.getDerivedStateFromProps&&null!=c.componentWillMount&&c.componentWillMount(),null!=c.componentDidMount&&c.__h.push(c.componentDidMount);else{if(null==k.getDerivedStateFromProps&&m!==v&&null!=c.componentWillReceiveProps&&c.componentWillReceiveProps(m,b),!c.__e&&(null!=c.shouldComponentUpdate&&!1===c.shouldComponentUpdate(m,c.__s,b)||e.__v===i.__v)){for(e.__v!==i.__v&&(c.props=m,c.state=c.__s,c.__d=!1),e.__e=i.__e,e.__k=i.__k,e.__k.forEach((function(t){t&&(t.__=e)})),_=0;_<c._sb.length;_++)c.__h.push(c._sb[_]);c._sb=[],c.__h.length&&s.push(c);break t}null!=c.componentWillUpdate&&c.componentWillUpdate(m,c.__s,b),null!=c.componentDidUpdate&&c.__h.push((function(){c.componentDidUpdate(v,f,p)}))}if(c.context=b,c.props=m,c.__P=t,c.__e=!1,w=qo.__r,I=0,"prototype"in k&&k.prototype.render){for(c.state=c.__s,c.__d=!1,w&&w(e),h=c.render(c.props,c.state,c.context),C=0;C<c._sb.length;C++)c.__h.push(c._sb[C]);c._sb=[]}else do{c.__d=!1,w&&w(e),h=c.render(c.props,c.state,c.context),c.state=c.__s}while(c.__d&&++I<25);c.state=c.__s,null!=c.getChildContext&&(r=Ko(Ko({},r),c.getChildContext())),d||null==c.getSnapshotBeforeUpdate||(p=c.getSnapshotBeforeUpdate(v,f)),ls(t,Jo(S=null!=h&&h.type===is&&null==h.key?h.props.children:h)?S:[S],e,i,r,n,o,s,a,l,u),c.base=e.__e,e.__u&=-161,c.__h.length&&s.push(c),g&&(c.__E=c.__=null)}catch(t){e.__v=null,l||null!=o?(e.__e=a,e.__u|=l?160:32,o[o.indexOf(a)]=null):(e.__e=i.__e,e.__k=i.__k),qo.__e(t,e,i)}else null==o&&e.__v===i.__v?(e.__k=i.__k,e.__e=i.__e):e.__e=function(t,e,i,r,n,o,s,a,l){var u,h,c,d,v,f,p,g=i.props,m=e.props,y=e.type;if("svg"===y&&(n=!0),null!=o)for(u=0;u<o.length;u++)if((v=o[u])&&"setAttribute"in v==!!y&&(y?v.localName===y:3===v.nodeType)){t=v,o[u]=null;break}if(null==t){if(null===y)return document.createTextNode(m);t=n?document.createElementNS("http://www.w3.org/2000/svg",y):document.createElement(y,m.is&&m),o=null,a=!1}if(null===y)g===m||a&&t.data===m||(t.data=m);else{if(o=o&&Zo.call(t.childNodes),g=i.props||Wo,!a&&null!=o)for(g={},u=0;u<t.attributes.length;u++)g[(v=t.attributes[u]).name]=v.value;for(u in g)v=g[u],"children"==u||("dangerouslySetInnerHTML"==u?c=v:"key"===u||u in m||ds(t,u,null,v,n));for(u in m)v=m[u],"children"==u?d=v:"dangerouslySetInnerHTML"==u?h=v:"value"==u?f=v:"checked"==u?p=v:"key"===u||a&&"function"!=typeof v||g[u]===v||ds(t,u,v,g[u],n);if(h)a||c&&(h.__html===c.__html||h.__html===t.innerHTML)||(t.innerHTML=h.__html),e.__k=[];else if(c&&(t.innerHTML=""),ls(t,Jo(d)?d:[d],e,i,r,n&&"foreignObject"!==y,o,s,o?o[0]:i.__k&&ns(i,0),a,l),null!=o)for(u=o.length;u--;)null!=o[u]&&Qo(o[u]);a||(u="value",void 0!==f&&(f!==t[u]||"progress"===y&&!f||"option"===y&&f!==g[u])&&ds(t,u,f,g[u],!1),u="checked",void 0!==p&&p!==t[u]&&ds(t,u,p,g[u],!1))}return t}(i.__e,e,i,r,n,o,s,l,u);(h=qo.diffed)&&h(e)}function gs(t,e,i){e.__d=void 0;for(var r=0;r<i.length;r++)ms(i[r],i[++r],i[++r]);qo.__c&&qo.__c(e,t),t.some((function(e){try{t=e.__h,e.__h=[],t.some((function(t){t.call(e)}))}catch(t){qo.__e(t,e.__v)}}))}function ms(t,e,i){try{"function"==typeof t?t(e):t.current=e}catch(t){qo.__e(t,i)}}function ys(t,e,i){var r,n;if(qo.unmount&&qo.unmount(t),(r=t.ref)&&(r.current&&r.current!==t.__e||ms(r,null,e)),null!=(r=t.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(t){qo.__e(t,e)}r.base=r.__P=null,t.__c=void 0}if(r=t.__k)for(n=0;n<r.length;n++)r[n]&&ys(r[n],e,i||"function"!=typeof t.type);i||null==t.__e||Qo(t.__e),t.__=t.__e=t.__d=void 0}function bs(t,e,i){return this.constructor(t,i)}function _s(t,e,i){var r,n,o,s;qo.__&&qo.__(t,e),n=(r="function"==typeof i)?null:e.__k,o=[],s=[],ps(e,t=(!r&&i||e).__k=ts(is,null,[t]),n||Wo,Wo,void 0!==e.ownerSVGElement,!r&&i?[i]:n?null:e.firstChild?Zo.call(e.childNodes):null,o,!r&&i?i:n?n.__e:e.firstChild,r,s),gs(o,t,s)}function ws(t,e,i){var r,n,o,s,a=Ko({},t.props);for(o in t.type&&t.type.defaultProps&&(s=t.type.defaultProps),e)"key"==o?r=e[o]:"ref"==o?n=e[o]:a[o]=void 0===e[o]&&void 0!==s?s[o]:e[o];return arguments.length>2&&(a.children=arguments.length>3?Zo.call(arguments,2):i),es(t.type,a,r||t.key,n||t.ref,null)}Zo=Uo.slice,qo={__e:function(t,e,i,r){for(var n,o,s;e=e.__;)if((n=e.__c)&&!n.__)try{if((o=n.constructor)&&null!=o.getDerivedStateFromError&&(n.setState(o.getDerivedStateFromError(t)),s=n.__d),null!=n.componentDidCatch&&(n.componentDidCatch(t,r||{}),s=n.__d),s)return n.__E=n}catch(e){t=e}throw t}},Ho=0,rs.prototype.setState=function(t,e){var i;i=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=Ko({},this.state),"function"==typeof t&&(t=t(Ko({},i),this.props)),t&&Ko(i,t),null!=t&&this.__v&&(e&&this._sb.push(e),ss(this))},rs.prototype.forceUpdate=function(t){this.__v&&(this.__e=!0,t&&this.__h.push(t),ss(this))},rs.prototype.render=is,Vo=[],Yo="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,zo=function(t,e){return t.__v.__b-e.__v.__b},as.__r=0,Go=0;var Is,Cs,Ss,ks,xs=0,As=[],Ts=[],Ms=qo.__b,Rs=qo.__r,Ns=qo.diffed,Es=qo.__c,Fs=qo.unmount;function Os(t,e){qo.__h&&qo.__h(Cs,t,xs||e),xs=0;var i=Cs.__H||(Cs.__H={__:[],__h:[]});return t>=i.__.length&&i.__.push({__V:Ts}),i.__[t]}function Ps(t){return xs=1,function(t,e,i){var r=Os(Is++,2);if(r.t=t,!r.__c&&(r.__=[zs(void 0,e),function(t){var e=r.__N?r.__N[0]:r.__[0],i=r.t(e,t);e!==i&&(r.__N=[i,r.__[1]],r.__c.setState({}))}],r.__c=Cs,!Cs.u)){var n=function(t,e,i){if(!r.__c.__H)return!0;var n=r.__c.__H.__.filter((function(t){return t.__c}));if(n.every((function(t){return!t.__N})))return!o||o.call(this,t,e,i);var s=!1;return n.forEach((function(t){if(t.__N){var e=t.__[0];t.__=t.__N,t.__N=void 0,e!==t.__[0]&&(s=!0)}})),!(!s&&r.__c.props===t)&&(!o||o.call(this,t,e,i))};Cs.u=!0;var o=Cs.shouldComponentUpdate,s=Cs.componentWillUpdate;Cs.componentWillUpdate=function(t,e,i){if(this.__e){var r=o;o=void 0,n(t,e,i),o=r}s&&s.call(this,t,e,i)},Cs.shouldComponentUpdate=n}return r.__N||r.__}(zs,t)}function Ds(t,e){var i=Os(Is++,3);!qo.__s&&Ys(i.__H,e)&&(i.__=t,i.i=e,Cs.__H.__h.push(i))}function Ls(t){return xs=5,Bs((function(){return{current:t}}),[])}function Bs(t,e){var i=Os(Is++,7);return Ys(i.__H,e)?(i.__V=t(),i.i=e,i.__h=t,i.__V):i.__}function $s(t){var e=Cs.context[t.__c],i=Os(Is++,9);return i.c=t,e?(null==i.__&&(i.__=!0,e.sub(Cs)),e.props.value):t.__}function Zs(){for(var t;t=As.shift();)if(t.__P&&t.__H)try{t.__H.__h.forEach(Vs),t.__H.__h.forEach(js),t.__H.__h=[]}catch(e){t.__H.__h=[],qo.__e(e,t.__v)}}qo.__b=function(t){Cs=null,Ms&&Ms(t)},qo.__r=function(t){Rs&&Rs(t),Is=0;var e=(Cs=t.__c).__H;e&&(Ss===Cs?(e.__h=[],Cs.__h=[],e.__.forEach((function(t){t.__N&&(t.__=t.__N),t.__V=Ts,t.__N=t.i=void 0}))):(e.__h.forEach(Vs),e.__h.forEach(js),e.__h=[],Is=0)),Ss=Cs},qo.diffed=function(t){Ns&&Ns(t);var e=t.__c;e&&e.__H&&(e.__H.__h.length&&(1!==As.push(e)&&ks===qo.requestAnimationFrame||((ks=qo.requestAnimationFrame)||Hs)(Zs)),e.__H.__.forEach((function(t){t.i&&(t.__H=t.i),t.__V!==Ts&&(t.__=t.__V),t.i=void 0,t.__V=Ts}))),Ss=Cs=null},qo.__c=function(t,e){e.some((function(t){try{t.__h.forEach(Vs),t.__h=t.__h.filter((function(t){return!t.__||js(t)}))}catch(i){e.some((function(t){t.__h&&(t.__h=[])})),e=[],qo.__e(i,t.__v)}})),Es&&Es(t,e)},qo.unmount=function(t){Fs&&Fs(t);var e,i=t.__c;i&&i.__H&&(i.__H.__.forEach((function(t){try{Vs(t)}catch(t){e=t}})),i.__H=void 0,e&&qo.__e(e,i.__v))};var qs="function"==typeof requestAnimationFrame;function Hs(t){var e,i=function(){clearTimeout(r),qs&&cancelAnimationFrame(e),setTimeout(t)},r=setTimeout(i,100);qs&&(e=requestAnimationFrame(i))}function Vs(t){var e=Cs,i=t.__c;"function"==typeof i&&(t.__c=void 0,i()),Cs=e}function js(t){var e=Cs;t.__c=t.__(),Cs=e}function Ys(t,e){return!t||t.length!==e.length||e.some((function(e,i){return e!==t[i]}))}function zs(t,e){return"function"==typeof e?e(t):e}var Gs=function(t){return t.Button="button",t.Tab="tab",t.Selector="selector",t}({}),Ws=function(t){return t.TopLeft="top_left",t.TopRight="top_right",t.TopCenter="top_center",t.MiddleLeft="middle_left",t.MiddleRight="middle_right",t.MiddleCenter="middle_center",t.Left="left",t.Center="center",t.Right="right",t.NextToTrigger="next_to_trigger",t}({}),Us=function(t){return t.Popover="popover",t.API="api",t.Widget="widget",t}({}),Xs=function(t){return t.Open="open",t.MultipleChoice="multiple_choice",t.SingleChoice="single_choice",t.Rating="rating",t.Link="link",t}({}),Js=function(t){return t.NextQuestion="next_question",t.End="end",t.ResponseBased="response_based",t.SpecificQuestion="specific_question",t}({}),Ks=function(t){return t.Once="once",t.Recurring="recurring",t.Always="always",t}({}),Qs=function(t){return t.SHOWN="survey shown",t.DISMISSED="survey dismissed",t.SENT="survey sent",t}({}),ta=function(t){return t.SURVEY_ID="$survey_id",t.SURVEY_NAME="$survey_name",t.SURVEY_RESPONSE="$survey_response",t.SURVEY_ITERATION="$survey_iteration",t.SURVEY_ITERATION_START_DATE="$survey_iteration_start_date",t.SURVEY_PARTIALLY_COMPLETED="$survey_partially_completed",t.SURVEY_SUBMISSION_ID="$survey_submission_id",t.SURVEY_QUESTIONS="$survey_questions",t.SURVEY_COMPLETED="$survey_completed",t}({}),ea=Ar("[Surveys]");function ia(t){return!(!t.start_date||t.end_date)}function ra(t){var e;return!(null==(e=t.conditions)||null==(e=e.events)||null==(e=e.values)||!e.length)}function na(t){var e;return!(null==(e=t.conditions)||null==(e=e.actions)||null==(e=e.values)||!e.length)}var oa="seenSurvey_",sa="inProgressSurvey_",aa=(t,e)=>{var i="$survey_"+e+"/"+t.id;return t.current_iteration&&t.current_iteration>0&&(i="$survey_"+e+"/"+t.id+"/"+t.current_iteration),i};Math.trunc||(Math.trunc=function(t){return t<0?Math.ceil(t):Math.floor(t)}),Number.isInteger||(Number.isInteger=function(t){return br(t)&&isFinite(t)&&Math.floor(t)===t});var la="0123456789abcdef";class ua{constructor(t){if(this.bytes=t,16!==t.length)throw new TypeError("not 128-bit length")}static fromFieldsV7(t,e,i,r){if(!Number.isInteger(t)||!Number.isInteger(e)||!Number.isInteger(i)||!Number.isInteger(r)||t<0||e<0||i<0||r<0||t>0xffffffffffff||e>4095||i>1073741823||r>4294967295)throw new RangeError("invalid field value");var n=new Uint8Array(16);return n[0]=t/Math.pow(2,40),n[1]=t/Math.pow(2,32),n[2]=t/Math.pow(2,24),n[3]=t/Math.pow(2,16),n[4]=t/Math.pow(2,8),n[5]=t,n[6]=112|e>>>8,n[7]=e,n[8]=128|i>>>24,n[9]=i>>>16,n[10]=i>>>8,n[11]=i,n[12]=r>>>24,n[13]=r>>>16,n[14]=r>>>8,n[15]=r,new ua(n)}toString(){for(var t="",e=0;e<this.bytes.length;e++)t=t+la.charAt(this.bytes[e]>>>4)+la.charAt(15&this.bytes[e]),3!==e&&5!==e&&7!==e&&9!==e||(t+="-");if(36!==t.length)throw new Error("Invalid UUIDv7 was generated");return t}clone(){return new ua(this.bytes.slice(0))}equals(t){return 0===this.compareTo(t)}compareTo(t){for(var e=0;e<16;e++){var i=this.bytes[e]-t.bytes[e];if(0!==i)return Math.sign(i)}return 0}}class ha{constructor(){this.h=0,this.m=0,this.I=new va}generate(){var t=this.generateOrAbort();if(fr(t)){this.h=0;var e=this.generateOrAbort();if(fr(e))throw new Error("Could not generate UUID after timestamp reset");return e}return t}generateOrAbort(){var t=Date.now();if(t>this.h)this.h=t,this.C();else{if(!(t+1e4>this.h))return;this.m++,this.m>4398046511103&&(this.h++,this.C())}return ua.fromFieldsV7(this.h,Math.trunc(this.m/Math.pow(2,30)),this.m&Math.pow(2,30)-1,this.I.nextUint32())}C(){this.m=1024*this.I.nextUint32()+(1023&this.I.nextUint32())}}var ca,da=t=>{if("undefined"!=typeof UUIDV7_DENY_WEAK_RNG&&UUIDV7_DENY_WEAK_RNG)throw new Error("no cryptographically strong RNG available");for(var e=0;e<t.length;e++)t[e]=65536*Math.trunc(65536*Math.random())+Math.trunc(65536*Math.random());return t};Bi&&!fr(Bi.crypto)&&crypto.getRandomValues&&(da=t=>crypto.getRandomValues(t));class va{constructor(){this.S=new Uint32Array(8),this.k=1/0}nextUint32(){return this.k>=this.S.length&&(da(this.S),this.k=0),this.S[this.k++]}}var fa=()=>pa().toString(),pa=()=>(ca||(ca=new ha)).generate(),ga="Mobile",ma="iOS",ya="Android",ba="Tablet",_a=ya+" "+ba,wa="iPad",Ia="Apple",Ca=Ia+" Watch",Sa="Safari",ka="BlackBerry",xa="Samsung",Aa=xa+"Browser",Ta=xa+" Internet",Ma="Chrome",Ra=Ma+" OS",Na=Ma+" "+ma,Ea="Internet Explorer",Fa=Ea+" "+ga,Oa="Opera",Pa=Oa+" Mini",Da="Edge",La="Microsoft "+Da,Ba="Firefox",$a=Ba+" "+ma,Za="Nintendo",qa="PlayStation",Ha="Xbox",Va=ya+" "+ga,ja=ga+" "+Sa,Ya="Windows",za=Ya+" Phone",Ga="Nokia",Wa="Ouya",Ua="Generic",Xa=Ua+" "+ga.toLowerCase(),Ja=Ua+" "+ba.toLowerCase(),Ka="Konqueror",Qa="(\\d+(\\.\\d+)?)",tl=new RegExp("Version/"+Qa),el=new RegExp(Ha,"i"),il=new RegExp(qa+" \\w+","i"),rl=new RegExp(Za+" \\w+","i"),nl=new RegExp(ka+"|PlayBook|BB10","i"),ol={"NT3.51":"NT 3.11","NT4.0":"NT 4.0","5.0":"2000",5.1:"XP",5.2:"XP","6.0":"Vista",6.1:"7",6.2:"8",6.3:"8.1",6.4:"10","10.0":"10"};var sl=(t,e)=>e&&er(e,Ia)||function(t){return er(t,Sa)&&!er(t,Ma)&&!er(t,ya)}(t),al=function(t,e){return e=e||"",er(t," OPR/")&&er(t,"Mini")?Pa:er(t," OPR/")?Oa:nl.test(t)?ka:er(t,"IE"+ga)||er(t,"WPDesktop")?Fa:er(t,Aa)?Ta:er(t,Da)||er(t,"Edg/")?La:er(t,"FBIOS")?"Facebook "+ga:er(t,"UCWEB")||er(t,"UCBrowser")?"UC Browser":er(t,"CriOS")?Na:er(t,"CrMo")||er(t,Ma)?Ma:er(t,ya)&&er(t,Sa)?Va:er(t,"FxiOS")?$a:er(t.toLowerCase(),Ka.toLowerCase())?Ka:sl(t,e)?er(t,ga)?ja:Sa:er(t,Ba)?Ba:er(t,"MSIE")||er(t,"Trident/")?Ea:er(t,"Gecko")?Ba:""},ll={[Fa]:[new RegExp("rv:"+Qa)],[La]:[new RegExp(Da+"?\\/"+Qa)],[Ma]:[new RegExp("("+Ma+"|CrMo)\\/"+Qa)],[Na]:[new RegExp("CriOS\\/"+Qa)],"UC Browser":[new RegExp("(UCBrowser|UCWEB)\\/"+Qa)],[Sa]:[tl],[ja]:[tl],[Oa]:[new RegExp("(Opera|OPR)\\/"+Qa)],[Ba]:[new RegExp(Ba+"\\/"+Qa)],[$a]:[new RegExp("FxiOS\\/"+Qa)],[Ka]:[new RegExp("Konqueror[:/]?"+Qa,"i")],[ka]:[new RegExp(ka+" "+Qa),tl],[Va]:[new RegExp("android\\s"+Qa,"i")],[Ta]:[new RegExp(Aa+"\\/"+Qa)],[Ea]:[new RegExp("(rv:|MSIE )"+Qa)],Mozilla:[new RegExp("rv:"+Qa)]},ul=function(t,e){var i=al(t,e),r=ll[i];if(fr(r))return null;for(var n=0;n<r.length;n++){var o=r[n],s=t.match(o);if(s)return parseFloat(s[s.length-2])}return null},hl=[[new RegExp(Ha+"; "+Ha+" (.*?)[);]","i"),t=>[Ha,t&&t[1]||""]],[new RegExp(Za,"i"),[Za,""]],[new RegExp(qa,"i"),[qa,""]],[nl,[ka,""]],[new RegExp(Ya,"i"),(t,e)=>{if(/Phone/.test(e)||/WPDesktop/.test(e))return[za,""];if(new RegExp(ga).test(e)&&!/IEMobile\b/.test(e))return[Ya+" "+ga,""];var i=/Windows NT ([0-9.]+)/i.exec(e);if(i&&i[1]){var r=i[1],n=ol[r]||"";return/arm/i.test(e)&&(n="RT"),[Ya,n]}return[Ya,""]}],[/((iPhone|iPad|iPod).*?OS (\d+)_(\d+)_?(\d+)?|iPhone)/,t=>{if(t&&t[3]){var e=[t[3],t[4],t[5]||"0"];return[ma,e.join(".")]}return[ma,""]}],[/(watch.*\/(\d+\.\d+\.\d+)|watch os,(\d+\.\d+),)/i,t=>{var e="";return t&&t.length>=3&&(e=fr(t[2])?t[3]:t[2]),["watchOS",e]}],[new RegExp("("+ya+" (\\d+)\\.(\\d+)\\.?(\\d+)?|"+ya+")","i"),t=>{if(t&&t[2]){var e=[t[2],t[3],t[4]||"0"];return[ya,e.join(".")]}return[ya,""]}],[/Mac OS X (\d+)[_.](\d+)[_.]?(\d+)?/i,t=>{var e=["Mac OS X",""];if(t&&t[1]){var i=[t[1],t[2],t[3]||"0"];e[1]=i.join(".")}return e}],[/Mac/i,["Mac OS X",""]],[/CrOS/,[Ra,""]],[/Linux|debian/i,["Linux",""]]],cl=function(t){return rl.test(t)?Za:il.test(t)?qa:el.test(t)?Ha:new RegExp(Wa,"i").test(t)?Wa:new RegExp("("+za+"|WPDesktop)","i").test(t)?za:/iPad/.test(t)?wa:/iPod/.test(t)?"iPod Touch":/iPhone/.test(t)?"iPhone":/(watch)(?: ?os[,/]|\d,\d\/)[\d.]+/i.test(t)?Ca:nl.test(t)?ka:/(kobo)\s(ereader|touch)/i.test(t)?"Kobo":new RegExp(Ga,"i").test(t)?Ga:/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i.test(t)||/(kf[a-z]+)( bui|\)).+silk\//i.test(t)?"Kindle Fire":/(Android|ZTE)/i.test(t)?!new RegExp(ga).test(t)||/(9138B|TB782B|Nexus [97]|pixel c|HUAWEISHT|BTV|noble nook|smart ultra 6)/i.test(t)?/pixel[\daxl ]{1,6}/i.test(t)&&!/pixel c/i.test(t)||/(huaweimed-al00|tah-|APA|SM-G92|i980|zte|U304AA)/i.test(t)||/lmy47v/i.test(t)&&!/QTAQZ3/i.test(t)?ya:_a:ya:new RegExp("(pda|"+ga+")","i").test(t)?Xa:new RegExp(ba,"i").test(t)&&!new RegExp(ba+" pc","i").test(t)?Ja:""},dl=function(t){var e=cl(t);return e===wa||e===_a||"Kobo"===e||"Kindle Fire"===e||e===Ja?ba:e===Za||e===Ha||e===qa||e===Wa?"Console":e===Ca?"Wearable":e?ga:"Desktop"},vl=Uint8Array,fl=Uint16Array,pl=Uint32Array,gl=new vl([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),ml=new vl([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),yl=new vl([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),bl=function(t,e){for(var i=new fl(31),r=0;r<31;++r)i[r]=e+=1<<t[r-1];var n=new pl(i[30]);for(r=1;r<30;++r)for(var o=i[r];o<i[r+1];++o)n[o]=o-i[r]<<5|r;return[i,n]},_l=bl(gl,2),wl=_l[0],Il=_l[1];wl[28]=258,Il[258]=28;for(var Cl=bl(ml,0)[1],Sl=new fl(32768),kl=0;kl<32768;++kl){var xl=(43690&kl)>>>1|(21845&kl)<<1;xl=(61680&(xl=(52428&xl)>>>2|(13107&xl)<<2))>>>4|(3855&xl)<<4,Sl[kl]=((65280&xl)>>>8|(255&xl)<<8)>>>1}var Al=function(t,e,i){for(var r=t.length,n=0,o=new fl(e);n<r;++n)++o[t[n]-1];var s,a=new fl(e);for(n=0;n<e;++n)a[n]=a[n-1]+o[n-1]<<1;if(i){s=new fl(1<<e);var l=15-e;for(n=0;n<r;++n)if(t[n])for(var u=n<<4|t[n],h=e-t[n],c=a[t[n]-1]++<<h,d=c|(1<<h)-1;c<=d;++c)s[Sl[c]>>>l]=u}else for(s=new fl(r),n=0;n<r;++n)s[n]=Sl[a[t[n]-1]++]>>>15-t[n];return s},Tl=new vl(288);for(kl=0;kl<144;++kl)Tl[kl]=8;for(kl=144;kl<256;++kl)Tl[kl]=9;for(kl=256;kl<280;++kl)Tl[kl]=7;for(kl=280;kl<288;++kl)Tl[kl]=8;var Ml=new vl(32);for(kl=0;kl<32;++kl)Ml[kl]=5;var Rl=Al(Tl,9,0),Nl=Al(Ml,5,0),El=function(t){return(t/8>>0)+(7&t&&1)},Fl=function(t,e,i){(null==i||i>t.length)&&(i=t.length);var r=new(t instanceof fl?fl:t instanceof pl?pl:vl)(i-e);return r.set(t.subarray(e,i)),r},Ol=function(t,e,i){i<<=7&e;var r=e/8>>0;t[r]|=i,t[r+1]|=i>>>8},Pl=function(t,e,i){i<<=7&e;var r=e/8>>0;t[r]|=i,t[r+1]|=i>>>8,t[r+2]|=i>>>16},Dl=function(t,e){for(var i=[],r=0;r<t.length;++r)t[r]&&i.push({s:r,f:t[r]});var n=i.length,o=i.slice();if(!n)return[new vl(0),0];if(1==n){var s=new vl(i[0].s+1);return s[i[0].s]=1,[s,1]}i.sort((function(t,e){return t.f-e.f})),i.push({s:-1,f:25001});var a=i[0],l=i[1],u=0,h=1,c=2;for(i[0]={s:-1,f:a.f+l.f,l:a,r:l};h!=n-1;)a=i[i[u].f<i[c].f?u++:c++],l=i[u!=h&&i[u].f<i[c].f?u++:c++],i[h++]={s:-1,f:a.f+l.f,l:a,r:l};var d=o[0].s;for(r=1;r<n;++r)o[r].s>d&&(d=o[r].s);var v=new fl(d+1),f=Ll(i[h-1],v,0);if(f>e){r=0;var p=0,g=f-e,m=1<<g;for(o.sort((function(t,e){return v[e.s]-v[t.s]||t.f-e.f}));r<n;++r){var y=o[r].s;if(!(v[y]>e))break;p+=m-(1<<f-v[y]),v[y]=e}for(p>>>=g;p>0;){var b=o[r].s;v[b]<e?p-=1<<e-v[b]++-1:++r}for(;r>=0&&p;--r){var _=o[r].s;v[_]==e&&(--v[_],++p)}f=e}return[new vl(v),f]},Ll=function(t,e,i){return-1==t.s?Math.max(Ll(t.l,e,i+1),Ll(t.r,e,i+1)):e[t.s]=i},Bl=function(t){for(var e=t.length;e&&!t[--e];);for(var i=new fl(++e),r=0,n=t[0],o=1,s=function(t){i[r++]=t},a=1;a<=e;++a)if(t[a]==n&&a!=e)++o;else{if(!n&&o>2){for(;o>138;o-=138)s(32754);o>2&&(s(o>10?o-11<<5|28690:o-3<<5|12305),o=0)}else if(o>3){for(s(n),--o;o>6;o-=6)s(8304);o>2&&(s(o-3<<5|8208),o=0)}for(;o--;)s(n);o=1,n=t[a]}return[i.subarray(0,r),e]},$l=function(t,e){for(var i=0,r=0;r<e.length;++r)i+=t[r]*e[r];return i},Zl=function(t,e,i){var r=i.length,n=El(e+2);t[n]=255&r,t[n+1]=r>>>8,t[n+2]=255^t[n],t[n+3]=255^t[n+1];for(var o=0;o<r;++o)t[n+o+4]=i[o];return 8*(n+4+r)},ql=function(t,e,i,r,n,o,s,a,l,u,h){Ol(e,h++,i),++n[256];for(var c=Dl(n,15),d=c[0],v=c[1],f=Dl(o,15),p=f[0],g=f[1],m=Bl(d),y=m[0],b=m[1],_=Bl(p),w=_[0],I=_[1],C=new fl(19),S=0;S<y.length;++S)C[31&y[S]]++;for(S=0;S<w.length;++S)C[31&w[S]]++;for(var k=Dl(C,7),x=k[0],A=k[1],T=19;T>4&&!x[yl[T-1]];--T);var M,R,N,E,F=u+5<<3,O=$l(n,Tl)+$l(o,Ml)+s,P=$l(n,d)+$l(o,p)+s+14+3*T+$l(C,x)+(2*C[16]+3*C[17]+7*C[18]);if(F<=O&&F<=P)return Zl(e,h,t.subarray(l,l+u));if(Ol(e,h,1+(P<O)),h+=2,P<O){M=Al(d,v,0),R=d,N=Al(p,g,0),E=p;var D=Al(x,A,0);Ol(e,h,b-257),Ol(e,h+5,I-1),Ol(e,h+10,T-4),h+=14;for(S=0;S<T;++S)Ol(e,h+3*S,x[yl[S]]);h+=3*T;for(var L=[y,w],B=0;B<2;++B){var $=L[B];for(S=0;S<$.length;++S){var Z=31&$[S];Ol(e,h,D[Z]),h+=x[Z],Z>15&&(Ol(e,h,$[S]>>>5&127),h+=$[S]>>>12)}}}else M=Rl,R=Tl,N=Nl,E=Ml;for(S=0;S<a;++S)if(r[S]>255){Z=r[S]>>>18&31;Pl(e,h,M[Z+257]),h+=R[Z+257],Z>7&&(Ol(e,h,r[S]>>>23&31),h+=gl[Z]);var q=31&r[S];Pl(e,h,N[q]),h+=E[q],q>3&&(Pl(e,h,r[S]>>>5&8191),h+=ml[q])}else Pl(e,h,M[r[S]]),h+=R[r[S]];return Pl(e,h,M[256]),h+R[256]},Hl=new pl([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),Vl=function(){for(var t=new pl(256),e=0;e<256;++e){for(var i=e,r=9;--r;)i=(1&i&&3988292384)^i>>>1;t[e]=i}return t}(),jl=function(){var t=4294967295;return{p:function(e){for(var i=t,r=0;r<e.length;++r)i=Vl[255&i^e[r]]^i>>>8;t=i},d:function(){return 4294967295^t}}},Yl=function(t,e,i,r,n){return function(t,e,i,r,n,o){var s=t.length,a=new vl(r+s+5*(1+Math.floor(s/7e3))+n),l=a.subarray(r,a.length-n),u=0;if(!e||s<8)for(var h=0;h<=s;h+=65535){var c=h+65535;c<s?u=Zl(l,u,t.subarray(h,c)):(l[h]=o,u=Zl(l,u,t.subarray(h,s)))}else{for(var d=Hl[e-1],v=d>>>13,f=8191&d,p=(1<<i)-1,g=new fl(32768),m=new fl(p+1),y=Math.ceil(i/3),b=2*y,_=function(e){return(t[e]^t[e+1]<<y^t[e+2]<<b)&p},w=new pl(25e3),I=new fl(288),C=new fl(32),S=0,k=0,x=(h=0,0),A=0,T=0;h<s;++h){var M=_(h),R=32767&h,N=m[M];if(g[R]=N,m[M]=R,A<=h){var E=s-h;if((S>7e3||x>24576)&&E>423){u=ql(t,l,0,w,I,C,k,x,T,h-T,u),x=S=k=0,T=h;for(var F=0;F<286;++F)I[F]=0;for(F=0;F<30;++F)C[F]=0}var O=2,P=0,D=f,L=R-N&32767;if(E>2&&M==_(h-L))for(var B=Math.min(v,E)-1,$=Math.min(32767,h),Z=Math.min(258,E);L<=$&&--D&&R!=N;){if(t[h+O]==t[h+O-L]){for(var q=0;q<Z&&t[h+q]==t[h+q-L];++q);if(q>O){if(O=q,P=L,q>B)break;var H=Math.min(L,q-2),V=0;for(F=0;F<H;++F){var j=h-L+F+32768&32767,Y=j-g[j]+32768&32767;Y>V&&(V=Y,N=j)}}}L+=(R=N)-(N=g[R])+32768&32767}if(P){w[x++]=268435456|Il[O]<<18|Cl[P];var z=31&Il[O],G=31&Cl[P];k+=gl[z]+ml[G],++I[257+z],++C[G],A=h+O,++S}else w[x++]=t[h],++I[t[h]]}}u=ql(t,l,o,w,I,C,k,x,T,h-T,u)}return Fl(a,0,r+El(u)+n)}(t,null==e.level?6:e.level,null==e.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(t.length)))):12+e.mem,i,r,!n)},zl=function(t,e,i){for(;i;++e)t[e]=i,i>>>=8},Gl=function(t,e){var i=e.filename;if(t[0]=31,t[1]=139,t[2]=8,t[8]=e.level<2?4:9==e.level?2:0,t[9]=3,0!=e.mtime&&zl(t,4,Math.floor(new Date(e.mtime||Date.now())/1e3)),i){t[3]=8;for(var r=0;r<=i.length;++r)t[r+10]=i.charCodeAt(r)}},Wl=function(t){return 10+(t.filename&&t.filename.length+1||0)};function Ul(t,e){void 0===e&&(e={});var i=jl(),r=t.length;i.p(t);var n=Yl(t,e,Wl(e),8),o=n.length;return Gl(n,e),zl(n,o-8,i.d()),zl(n,o-4,r),n}function Xl(t,e){var i=t.length;if("undefined"!=typeof TextEncoder)return(new TextEncoder).encode(t);for(var r=new vl(t.length+(t.length>>>1)),n=0,o=function(t){r[n++]=t},s=0;s<i;++s){if(n+5>r.length){var a=new vl(n+8+(i-s<<1));a.set(r),r=a}var l=t.charCodeAt(s);l<128||e?o(l):l<2048?(o(192|l>>>6),o(128|63&l)):l>55295&&l<57344?(o(240|(l=65536+(1047552&l)|1023&t.charCodeAt(++s))>>>18),o(128|l>>>12&63),o(128|l>>>6&63),o(128|63&l)):(o(224|l>>>12),o(128|l>>>6&63),o(128|63&l))}return Fl(r,0,n)}var Jl=function(t){var e,i,r,n,o="";for(e=i=0,r=(t=(t+"").replace(/\r\n/g,"\n").replace(/\r/g,"\n")).length,n=0;n<r;n++){var s=t.charCodeAt(n),a=null;s<128?i++:a=s>127&&s<2048?String.fromCharCode(s>>6|192,63&s|128):String.fromCharCode(s>>12|224,s>>6&63|128,63&s|128),mr(a)||(i>e&&(o+=t.substring(e,i)),o+=a,e=i=n+1)}return i>e&&(o+=t.substring(e,t.length)),o},Kl=!!Gi||!!zi,Ql="text/plain",tu=(t,e)=>{var[r,n]=t.split("?"),o=i({},e);null==n||n.split("&").forEach((t=>{var[e]=t.split("=");delete o[e]}));var s=jr(o);return r+"?"+(s=s?(n?n+"&":"")+s:n)},eu=(t,e)=>JSON.stringify(t,((t,e)=>"bigint"==typeof e?e.toString():e),e),iu=t=>{var{data:e,compression:i}=t;if(e){if(i===Qi.GZipJS){var r=Ul(Xl(eu(e)),{mtime:0}),n=new Blob([r],{type:Ql});return{contentType:Ql,body:n,estimatedSize:n.size}}if(i===Qi.Base64){var o=function(t){var e,i,r,n,o,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",a=0,l=0,u="",h=[];if(!t)return t;t=Jl(t);do{e=(o=t.charCodeAt(a++)<<16|t.charCodeAt(a++)<<8|t.charCodeAt(a++))>>18&63,i=o>>12&63,r=o>>6&63,n=63&o,h[l++]=s.charAt(e)+s.charAt(i)+s.charAt(r)+s.charAt(n)}while(a<t.length);switch(u=h.join(""),t.length%3){case 1:u=u.slice(0,-2)+"==";break;case 2:u=u.slice(0,-1)+"="}return u}(eu(e)),s=(t=>"data="+encodeURIComponent("string"==typeof t?t:eu(t)))(o);return{contentType:"application/x-www-form-urlencoded",body:s,estimatedSize:new Blob([s]).size}}var a=eu(e);return{contentType:"application/json",body:a,estimatedSize:new Blob([a]).size}}},ru=[];zi&&ru.push({transport:"fetch",method:t=>{var e,r,{contentType:n,body:o,estimatedSize:s}=null!==(e=iu(t))&&void 0!==e?e:{},a=new Headers;Rr(t.headers,(function(t,e){a.append(e,t)})),n&&a.append("Content-Type",n);var l=t.url,u=null;if(Wi){var h=new Wi;u={signal:h.signal,timeout:setTimeout((()=>h.abort()),t.timeout)}}zi(l,i({method:(null==t?void 0:t.method)||"GET",headers:a,keepalive:"POST"===t.method&&(s||0)<52428.8,body:o,signal:null==(r=u)?void 0:r.signal},t.fetchOptions)).then((e=>e.text().then((i=>{var r={statusCode:e.status,text:i};if(200===e.status)try{r.json=JSON.parse(i)}catch(t){xr.error(t)}null==t.callback||t.callback(r)})))).catch((e=>{xr.error(e),null==t.callback||t.callback({statusCode:0,text:e})})).finally((()=>u?clearTimeout(u.timeout):null))}}),Gi&&ru.push({transport:"XHR",method:t=>{var e,i=new Gi;i.open(t.method||"GET",t.url,!0);var{contentType:r,body:n}=null!==(e=iu(t))&&void 0!==e?e:{};Rr(t.headers,(function(t,e){i.setRequestHeader(e,t)})),r&&i.setRequestHeader("Content-Type",r),t.timeout&&(i.timeout=t.timeout),i.withCredentials=!0,i.onreadystatechange=()=>{if(4===i.readyState){var e={statusCode:i.status,text:i.responseText};if(200===i.status)try{e.json=JSON.parse(i.responseText)}catch(t){}null==t.callback||t.callback(e)}},i.send(n)}}),null!=Vi&&Vi.sendBeacon&&ru.push({transport:"sendBeacon",method:t=>{var e=tu(t.url,{beacon:"1"});try{var i,{contentType:r,body:n}=null!==(i=iu(t))&&void 0!==i?i:{},o="string"==typeof n?new Blob([n],{type:r}):n;Vi.sendBeacon(e,o)}catch(t){}}});var nu=function(t,e){if(!function(t){try{new RegExp(t)}catch(t){return!1}return!0}(e))return!1;try{return new RegExp(e).test(t)}catch(t){return!1}};function ou(t,e,i){return eu({distinct_id:t,userPropertiesToSet:e,userPropertiesToSetOnce:i})}var su={exact:(t,e)=>e.some((e=>t.some((t=>e===t)))),is_not:(t,e)=>e.every((e=>t.every((t=>e!==t)))),regex:(t,e)=>e.some((e=>t.some((t=>nu(e,t))))),not_regex:(t,e)=>e.every((e=>t.every((t=>!nu(e,t))))),icontains:(t,e)=>e.map(au).some((e=>t.map(au).some((t=>e.includes(t))))),not_icontains:(t,e)=>e.map(au).every((e=>t.map(au).every((t=>!e.includes(t)))))},au=t=>t.toLowerCase(),lu=Ar("[Stylesheet Loader]"),uu=Bi,hu=ji;function cu(t){return"$survey_response_"+t}var du="#020617",vu={fontFamily:"inherit",backgroundColor:"#eeeded",submitButtonColor:"black",submitButtonTextColor:"white",ratingButtonColor:"white",ratingButtonActiveColor:"black",borderColor:"#c9c6c6",placeholder:"Start typing...",whiteLabel:!1,displayThankYouMessage:!0,thankYouMessageHeader:"Thank you for your feedback!",position:Ws.Right,widgetType:Gs.Tab,widgetLabel:"Feedback",widgetColor:"black",zIndex:"2147483647",disabledButtonOpacity:"0.6",maxWidth:"300px",textSubtleColor:"#939393",boxPadding:"20px 24px",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)",borderRadius:"10px",shuffleQuestions:!1,surveyPopupDelaySeconds:void 0,outlineColor:"rgba(59, 130, 246, 0.8)",inputBackground:"white",inputTextColor:du,scrollbarThumbColor:"var(--ph-survey-border-color)",scrollbarTrackColor:"var(--ph-survey-background-color)"};function fu(t){if("#"===t[0]){var e=t.replace(/^#/,"");return"rgb("+parseInt(e.slice(0,2),16)+","+parseInt(e.slice(2,4),16)+","+parseInt(e.slice(4,6),16)+")"}return"rgb(255, 255, 255)"}function pu(t){var e;void 0===t&&(t=vu.backgroundColor),"#"===t[0]&&(e=fu(t)),t.startsWith("rgb")&&(e=t);var i=function(t){return{aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4","indianred ":"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370d8",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#d87093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"}[t.toLowerCase()]}(t);if(i&&(e=fu(i)),!e)return du;var r=e.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/);if(r){var n=parseInt(r[1]),o=parseInt(r[2]),s=parseInt(r[3]);return Math.sqrt(n*n*.299+o*o*.587+s*s*.114)>127.5?du:"white"}return du}function gu(t){var e=((t,e,i)=>{var r,n=t.createElement("style");return n.innerText=e,null!=i&&null!=(r=i.config)&&r.prepare_external_dependency_stylesheet&&(n=i.config.prepare_external_dependency_stylesheet(n)),n||(lu.error("prepare_external_dependency_stylesheet returned null"),null)})(hu,':host{--ph-survey-font-family:-apple-system,BlinkMacSystemFont,"Inter","Segoe UI","Roboto",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";--ph-survey-box-padding:20px 24px;--ph-survey-max-width:300px;--ph-survey-z-index:2147483647;--ph-survey-border-color:#dcdcdc;--ph-survey-border-bottom:1.5px solid var(--ph-survey-border-color);--ph-survey-border-radius:10px;--ph-survey-background-color:#eeeded;--ph-survey-box-shadow:0 4px 12px rgba(0,0,0,.15);--ph-survey-submit-button-color:#000;--ph-survey-submit-button-text-color:#fff;--ph-survey-rating-bg-color:#fff;--ph-survey-rating-text-color:#020617;--ph-survey-rating-active-bg-color:#000;--ph-survey-rating-active-text-color:#fff;--ph-survey-text-primary-color:#020617;--ph-survey-text-subtle-color:#939393;--ph-widget-color:#e0a045;--ph-widget-text-color:#fff;--ph-survey-scrollbar-thumb-color:var(--ph-survey-border-color);--ph-survey-scrollbar-track-color:var(--ph-survey-background-color);--ph-survey-outline-color:rgba(59,130,246,.8);--ph-survey-input-background:#fff;--ph-survey-input-text-color:#020617;--ph-survey-disabled-button-opacity:0.6}.ph-survey{bottom:0;height:fit-content;margin:0;max-width:85%;min-width:300px;position:fixed;width:var(--ph-survey-max-width);z-index:var(--ph-survey-z-index)}.ph-survey h3,.ph-survey p{margin:0}.ph-survey *{box-sizing:border-box;color:var(--ph-survey-text-primary-color);font-family:var(--ph-survey-font-family)}.ph-survey .multiple-choice-options label,.ph-survey input[type=text],.ph-survey textarea{background:var(--ph-survey-input-background);border:1.5px solid var(--ph-survey-border-color);border-radius:4px;color:var(--ph-survey-input-text-color);padding:10px;transition:border-color .2s ease-out,box-shadow .2s ease-out,transform .15s ease-out}.ph-survey input[type=text],.ph-survey textarea{transition:border-color .2s ease-out,box-shadow .2s ease-out,transform .15s ease-out}.ph-survey input{margin:0}.ph-survey .form-submit:focus,.ph-survey .form-submit:focus-visible,.ph-survey input[type=checkbox]:focus,.ph-survey input[type=checkbox]:focus-visible,.ph-survey input[type=radio]:focus,.ph-survey input[type=radio]:focus-visible,.ph-survey input[type=text]:focus,.ph-survey input[type=text]:focus-visible,.ph-survey textarea:focus,.ph-survey textarea:focus-visible{border-color:var(--ph-survey-rating-active-bg-color);outline:1.5px solid var(--ph-survey-outline-color);outline-offset:2px}.ph-survey button:focus:not(:focus-visible),.ph-survey input[type=checkbox]:focus:not(:focus-visible),.ph-survey input[type=radio]:focus:not(:focus-visible),.ph-survey input[type=text]:focus:not(:focus-visible),.ph-survey textarea:focus:not(:focus-visible){outline:none}.ph-survey input[type=text]:hover:not(:focus),.ph-survey textarea:hover:not(:focus){border-color:var(--ph-survey-rating-active-bg-color)}@media (max-width:768px){.ph-survey input[type=text],.ph-survey textarea{font-size:1rem}}.ph-survey .form-cancel,.ph-survey .multiple-choice-options label,.ph-survey .rating-options-number,.ph-survey input[type=checkbox],.ph-survey input[type=radio]{border:1.5px solid var(--ph-survey-border-color)}.ph-survey .footer-branding,.ph-survey .form-cancel,.ph-survey .form-submit,.ph-survey .ratings-emoji,.ph-survey .ratings-number,.ph-survey input[type=checkbox],.ph-survey input[type=radio],.ph-survey label{transition:all .2s ease-out}@media (prefers-reduced-motion:no-preference){.ph-survey button:active,.ph-survey input[type=checkbox]:active,.ph-survey input[type=radio]:active,.ph-survey label:active{transition-duration:.1s}}.ph-survey-widget-tab{background:var(--ph-widget-color);border:none;border-radius:3px 3px 0 0;color:var(--ph-widget-text-color);cursor:pointer;padding:10px 12px;position:fixed;right:0;text-align:center;top:50%;transform:rotate(-90deg) translateY(-100%);transform-origin:right top;transition:padding-bottom .2s ease-out;z-index:var(--ph-survey-z-index)}.ph-survey-widget-tab:hover{padding-bottom:16px}@keyframes ph-survey-fade-in{0%{opacity:0}to{opacity:1}}.survey-box{gap:16px}.bottom-section,.survey-box{display:flex;flex-direction:column}.bottom-section{gap:8px}.thank-you-message-header~.bottom-section{padding-top:16px}.question-container,.thank-you-message{display:flex;flex-direction:column;gap:8px}.survey-question{font-size:14px;font-weight:500}.survey-question-description{font-size:13px;opacity:.8;padding-top:4px}.question-textarea-wrapper{display:flex;flex-direction:column}.survey-form{animation:ph-survey-fade-in .3s ease-out forwards}.survey-form,.thank-you-message{background:var(--ph-survey-background-color);border:1.5px solid var(--ph-survey-border-color);border-bottom:var(--ph-survey-border-bottom);border-radius:var(--ph-survey-border-radius);box-shadow:var(--ph-survey-box-shadow);margin:0;padding:var(--ph-survey-box-padding);position:relative;text-align:left;width:100%;z-index:var(--ph-survey-z-index)}.survey-form input[type=text],.survey-form textarea{min-width:100%}:is(.survey-form textarea):focus,:is(.survey-form textarea):focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.08)}:is(.survey-form textarea):focus:not(:focus-visible){box-shadow:none}.survey-box:has(.survey-question:empty):not(:has(.survey-question-description)) .multiple-choice-options,.survey-box:has(.survey-question:empty):not(:has(.survey-question-description)) textarea{margin-top:0}.multiple-choice-options{border:none;display:flex;flex-direction:column;font-size:14px;gap:8px;margin:0;padding:1px 0}.multiple-choice-options label{align-items:center;cursor:pointer;display:flex;font-size:13px;gap:8px}:is(.multiple-choice-options label):hover:not(:has(input:checked)){border-color:var(--ph-survey-text-subtle-color);box-shadow:0 2px 8px rgba(0,0,0,.08)}:is(.multiple-choice-options label):has(input:checked){border-color:var(--ph-survey-rating-active-bg-color);box-shadow:0 1px 4px rgba(0,0,0,.05)}.choice-option-open:is(.multiple-choice-options label){flex-wrap:wrap}:is(.multiple-choice-options label) span{color:inherit}.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]{appearance:none;-webkit-appearance:none;-moz-appearance:none;background:var(--ph-survey-input-background);border-radius:3px;cursor:pointer;flex-shrink:0;height:1rem;position:relative;transition:all .2s cubic-bezier(.4,0,.2,1),transform .15s ease-out;width:1rem;z-index:1}:is(.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]):focus{transform:scale(1.05)}:is(.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]):hover{border-color:var(--ph-survey-rating-active-bg-color);transform:scale(1.05)}:is(.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]):active{transform:scale(.95)}:is(.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]):checked{background:var(--ph-survey-rating-active-bg-color);border-color:var(--ph-survey-rating-active-bg-color);transform:scale(1)}:is(.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]):checked:hover{transform:scale(1.05)}.multiple-choice-options input[type=checkbox]:checked:after{animation:ph-survey-checkmark-reveal .2s ease-out .1s forwards;border:solid var(--ph-survey-rating-active-text-color);border-width:0 2px 2px 0;height:8px;left:4px;transform:rotate(45deg) scale(0);width:4px}.multiple-choice-options input[type=radio]:checked:after{animation:ph-survey-radio-reveal .15s ease-out .05s forwards;background:var(--ph-survey-rating-active-text-color);border-radius:50%;height:6px;left:5px;top:5px;transform:scale(0);width:6px}.multiple-choice-options input[type=checkbox]:checked:after,.multiple-choice-options input[type=radio]:checked:after{box-sizing:content-box;content:"";position:absolute}.multiple-choice-options input[type=radio]{border-radius:50%}.multiple-choice-options input[type=radio]:checked{border:none}:is(.multiple-choice-options input[type=checkbox]:checked,.multiple-choice-options input[type=radio]:checked)~*{font-weight:700}:is(:is(.multiple-choice-options .choice-option-open) input[type=text])::placeholder{color:var(--ph-survey-text-subtle-color);font-weight:400}.rating-options-emoji{display:flex;justify-content:space-between}.ratings-emoji{background-color:transparent;border:none;font-size:16px;opacity:.5;padding:0}.ratings-emoji:hover{cursor:pointer;opacity:1;transform:scale(1.15)}.ratings-emoji.rating-active{opacity:1}.ratings-emoji svg{fill:var(--ph-survey-text-primary-color);transition:fill .2s ease-out}.rating-options-number{border-radius:6px;display:grid;grid-auto-columns:1fr;grid-auto-flow:column;overflow:hidden}.rating-options-number .ratings-number{border:none;border-right:1px solid var(--ph-survey-border-color);color:var(--ph-survey-rating-text-color);cursor:pointer;font-weight:700;text-align:center}:is(.rating-options-number .ratings-number):last-of-type{border-right:0}.rating-active:is(.rating-options-number .ratings-number){background:var(--ph-survey-rating-active-bg-color);color:var(--ph-survey-rating-active-text-color)}.ratings-number{background-color:var(--ph-survey-rating-bg-color);border:none;padding:8px 0}.ratings-number .rating-active{background-color:var(--ph-survey-rating-active-bg-color)}.ratings-number:hover{cursor:pointer}.rating-text{display:flex;flex-direction:row;font-size:11px;justify-content:space-between;opacity:.7}.form-submit{background:var(--ph-survey-submit-button-color);border:none;border-radius:6px;box-shadow:0 2px 0 rgba(0,0,0,.045);color:var(--ph-survey-submit-button-text-color);cursor:pointer;font-weight:700;min-width:100%;padding:12px;text-align:center;user-select:none}.form-submit:not([disabled]):hover{box-shadow:0 4px 8px rgba(0,0,0,.1);transform:scale(1.02)}.form-submit:not([disabled]):active{box-shadow:0 1px 2px rgba(0,0,0,.05);transform:scale(.98)}.form-submit[disabled]{cursor:not-allowed;opacity:var(--ph-survey-disabled-button-opacity)}.form-cancel{background:#fff;border-radius:100%;cursor:pointer;line-height:0;padding:12px;position:absolute;right:0;top:0;transform:translate(50%,-50%)}.form-cancel:hover{opacity:.7;transform:translate(50%,-50%) scale(1.1)}.footer-branding{align-items:center;display:flex;font-size:11px;font-weight:500;gap:4px;justify-content:center;opacity:.6;text-decoration:none}.footer-branding:hover{opacity:1}.footer-branding a{text-decoration:none}.thank-you-message{text-align:center}.thank-you-message-header{margin:10px 0 0}.thank-you-message-body{font-size:14px;opacity:.8}.limit-height{max-height:256px;overflow-x:hidden;overflow-y:auto;scrollbar-color:var(--ph-survey-scrollbar-thumb-color) var(--ph-survey-scrollbar-track-color);scrollbar-width:thin}.limit-height::-webkit-scrollbar{width:8px}.limit-height::-webkit-scrollbar-track{background:var(--ph-survey-scrollbar-track-color);border-radius:4px}.limit-height::-webkit-scrollbar-thumb{background-color:var(--ph-survey-scrollbar-thumb-color);border:2px solid var(--ph-survey-scrollbar-track-color);border-radius:4px}:is(.limit-height::-webkit-scrollbar-thumb):hover{background-color:var(--ph-survey-text-subtle-color)}.sr-only{clip:rect(0,0,0,0);border:0;height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;white-space:nowrap;width:1px}@media (prefers-reduced-motion:reduce){.ph-survey *{animation-duration:.01ms!important;animation-iteration-count:1!important;scroll-behavior:auto!important;transition-duration:.01ms!important}}@keyframes ph-survey-checkmark-reveal{0%{opacity:0;transform:rotate(45deg) scale(0)}50%{opacity:1;transform:rotate(45deg) scale(1.2)}to{opacity:1;transform:rotate(45deg) scale(1)}}@keyframes ph-survey-radio-reveal{0%{opacity:0;transform:scale(0)}50%{opacity:1;transform:scale(1.3)}to{opacity:1;transform:scale(1)}}',t);return null==e||e.setAttribute("data-ph-survey-style","true"),e}var mu=(t,e,r)=>{var n=Du(t),o=hu.querySelector("."+n);if(o&&o.shadowRoot)return{shadow:o.shadowRoot,isNewlyCreated:!1};var s=hu.createElement("div");((t,e,r)=>{var n=i({},vu,r),o=t.style,s=![Ws.Center,Ws.Left,Ws.Right].includes(n.position)||e===Us.Widget&&(null==r?void 0:r.widgetType)===Gs.Tab;o.setProperty("--ph-survey-font-family",function(t){if("inherit"===t)return"inherit";var e='BlinkMacSystemFont, "Inter", "Segoe UI", "Roboto", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"';return t?t+", "+e:"-apple-system, "+e}(n.fontFamily)),o.setProperty("--ph-survey-box-padding",n.boxPadding),o.setProperty("--ph-survey-max-width",n.maxWidth),o.setProperty("--ph-survey-z-index",n.zIndex),o.setProperty("--ph-survey-border-color",n.borderColor),s?(o.setProperty("--ph-survey-border-radius",n.borderRadius),o.setProperty("--ph-survey-border-bottom","1.5px solid var(--ph-survey-border-color)")):(o.setProperty("--ph-survey-border-bottom","none"),o.setProperty("--ph-survey-border-radius",n.borderRadius+" "+n.borderRadius+" 0 0")),o.setProperty("--ph-survey-background-color",n.backgroundColor),o.setProperty("--ph-survey-box-shadow",n.boxShadow),o.setProperty("--ph-survey-disabled-button-opacity",n.disabledButtonOpacity),o.setProperty("--ph-survey-submit-button-color",n.submitButtonColor),o.setProperty("--ph-survey-submit-button-text-color",(null==r?void 0:r.submitButtonTextColor)||pu(n.submitButtonColor)),o.setProperty("--ph-survey-rating-bg-color",n.ratingButtonColor),o.setProperty("--ph-survey-rating-text-color",pu(n.ratingButtonColor)),o.setProperty("--ph-survey-rating-active-bg-color",n.ratingButtonActiveColor),o.setProperty("--ph-survey-rating-active-text-color",pu(n.ratingButtonActiveColor)),o.setProperty("--ph-survey-text-primary-color",pu(n.backgroundColor)),o.setProperty("--ph-survey-text-subtle-color",n.textSubtleColor),o.setProperty("--ph-widget-color",n.widgetColor),o.setProperty("--ph-widget-text-color",pu(n.widgetColor)),o.setProperty("--ph-widget-z-index",n.zIndex),"white"===n.backgroundColor&&o.setProperty("--ph-survey-input-background","#f8f8f8"),o.setProperty("--ph-survey-input-background",n.inputBackground),o.setProperty("--ph-survey-input-text-color",pu(n.inputBackground)),o.setProperty("--ph-survey-scrollbar-thumb-color",n.scrollbarThumbColor),o.setProperty("--ph-survey-scrollbar-track-color",n.scrollbarTrackColor),o.setProperty("--ph-survey-outline-color",n.outlineColor)})(s,t.type,t.appearance),s.className=n;var a=s.attachShadow({mode:"open"}),l=gu(e);if(l){var u=a.querySelector("style");u&&a.removeChild(u),a.appendChild(l)}return hu.body.appendChild(s),{shadow:a,isNewlyCreated:!0}},yu=(t,e)=>{if(!e)return null;var i=t[cu(e)];return lr(i)?[...i]:i},bu=t=>{var{responses:e,survey:r,surveySubmissionId:n,posthog:o,isSurveyCompleted:s}=t;o?(o.capture(Qs.SENT,i({[ta.SURVEY_NAME]:r.name,[ta.SURVEY_ID]:r.id,[ta.SURVEY_ITERATION]:r.current_iteration,[ta.SURVEY_ITERATION_START_DATE]:r.current_iteration_start_date,[ta.SURVEY_QUESTIONS]:r.questions.map((t=>({id:t.id,question:t.question,response:yu(e,t.id)}))),[ta.SURVEY_SUBMISSION_ID]:n,[ta.SURVEY_COMPLETED]:s,sessionRecordingUrl:null==o.get_session_replay_url?void 0:o.get_session_replay_url()},e)),s&&(uu.dispatchEvent(new CustomEvent("PHSurveySent",{detail:{surveyId:r.id}})),Pu(r))):ea.error("[survey sent] event not captured, PostHog instance not found.")},_u=(t,e,r)=>{if(e){if(!r){var n=Fu(t);e.capture(Qs.DISMISSED,i({[ta.SURVEY_NAME]:t.name,[ta.SURVEY_ID]:t.id,[ta.SURVEY_ITERATION]:t.current_iteration,[ta.SURVEY_ITERATION_START_DATE]:t.current_iteration_start_date,[ta.SURVEY_PARTIALLY_COMPLETED]:Object.values((null==n?void 0:n.responses)||{}).filter((t=>!yr(t))).length>0,sessionRecordingUrl:null==e.get_session_replay_url?void 0:e.get_session_replay_url()},null==n?void 0:n.responses,{[ta.SURVEY_SUBMISSION_ID]:null==n?void 0:n.surveySubmissionId,[ta.SURVEY_QUESTIONS]:t.questions.map((t=>({id:t.id,question:t.question,response:yu((null==n?void 0:n.responses)||{},t.id)})))})),Pu(t),localStorage.setItem(ku(t),"true"),uu.dispatchEvent(new CustomEvent("PHSurveyClosed",{detail:{surveyId:t.id}}))}}else ea.error("[survey dismissed] event not captured, PostHog instance not found.")},wu=t=>t.map((t=>({sort:Math.floor(10*Math.random()),value:t}))).sort(((t,e)=>t.sort-e.sort)).map((t=>t.value)),Iu=(t,e)=>t.length===e.length&&t.every(((t,i)=>t===e[i]))?e.reverse():e,Cu=t=>t.appearance&&t.appearance.shuffleQuestions&&!t.enable_partial_responses?Iu(t.questions,wu(t.questions)):t.questions,Su=t=>{var e;return!(null==(e=t.conditions)||null==(e=e.events)||!e.repeatedActivation||!(t=>{var e,i;return null!=(null==(e=t.conditions)||null==(e=e.events)||null==(e=e.values)?void 0:e.length)&&(null==(i=t.conditions)||null==(i=i.events)||null==(i=i.values)?void 0:i.length)>0})(t))||t.schedule===Ks.Always||Ou(t)},ku=t=>{var e=""+oa+t.id;return t.current_iteration&&t.current_iteration>0&&(e=""+oa+t.id+"_"+t.current_iteration),e},xu=function(t,e){var i={__c:e="__cC"+Go++,__:t,Consumer:function(t,e){return t.children(e)},Provider:function(t){var i,r;return this.getChildContext||(i=[],(r={})[e]=this,this.getChildContext=function(){return r},this.shouldComponentUpdate=function(t){this.props.value!==t.value&&i.some((function(t){t.__e=!0,ss(t)}))},this.sub=function(t){i.push(t);var e=t.componentWillUnmount;t.componentWillUnmount=function(){i.splice(i.indexOf(t),1),e&&e.call(t)}}),t.children}};return i.Provider.__=i.Consumer.contextType=i}({isPreviewMode:!1,previewPageIndex:0,onPopupSurveyDismissed:()=>{},isPopup:!0,onPreviewSubmit:()=>{},surveySubmissionId:""}),Au=()=>$s(xu),Tu=t=>{var{component:e,children:i,renderAsHtml:r,style:n}=t;return ws(e,r?{dangerouslySetInnerHTML:{__html:i},style:n}:{children:i,style:n})};function Mu(t){return null!=t?t:"icontains"}function Ru(t){var e,i,r;if(null==(e=t.conditions)||!e.url)return!0;var n=null==uu||null==(i=uu.location)?void 0:i.href;if(!n)return!1;var o=[t.conditions.url],s=Mu(null==(r=t.conditions)?void 0:r.urlMatchType);return su[s](o,[n])}var Nu=t=>{var e=""+sa+t.id;return t.current_iteration&&t.current_iteration>0&&(e=""+sa+t.id+"_"+t.current_iteration),e},Eu=(t,e)=>{try{localStorage.setItem(Nu(t),JSON.stringify(e))}catch(t){ea.error("Error setting in-progress survey state in localStorage",t)}},Fu=t=>{try{var e=localStorage.getItem(Nu(t));if(e)return JSON.parse(e)}catch(t){ea.error("Error getting in-progress survey state from localStorage",t)}return null},Ou=t=>{var e=Fu(t);return!yr(null==e?void 0:e.surveySubmissionId)},Pu=t=>{try{localStorage.removeItem(Nu(t))}catch(t){ea.error("Error clearing in-progress survey state from localStorage",t)}};function Du(t,e){void 0===e&&(e=!1);var i="PostHogSurvey-"+t.id;return e?"."+i:i}var Lu=0;function Bu(t,e,i,r,n,o){var s,a,l={};for(a in e)"ref"==a?s=e[a]:l[a]=e[a];var u={type:t,props:l,key:i,ref:s,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--Lu,__i:-1,__u:0,__source:n,__self:o};if("function"==typeof t&&(s=t.defaultProps))for(a in s)void 0===l[a]&&(l[a]=s[a]);return qo.vnode&&qo.vnode(u),u}var $u=Bu("svg",{className:"emoji-svg",xmlns:"http://www.w3.org/2000/svg",height:"48",viewBox:"0 -960 960 960",width:"48",children:Bu("path",{d:"M626-533q22.5 0 38.25-15.75T680-587q0-22.5-15.75-38.25T626-641q-22.5 0-38.25 15.75T572-587q0 22.5 15.75 38.25T626-533Zm-292 0q22.5 0 38.25-15.75T388-587q0-22.5-15.75-38.25T334-641q-22.5 0-38.25 15.75T280-587q0 22.5 15.75 38.25T334-533Zm146 272q66 0 121.5-35.5T682-393h-52q-23 40-63 61.5T480.5-310q-46.5 0-87-21T331-393h-53q26 61 81 96.5T480-261Zm0 181q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 340q142.375 0 241.188-98.812Q820-337.625 820-480t-98.812-241.188Q622.375-820 480-820t-241.188 98.812Q140-622.375 140-480t98.812 241.188Q337.625-140 480-140Z"})}),Zu=Bu("svg",{className:"emoji-svg",xmlns:"http://www.w3.org/2000/svg",height:"48",viewBox:"0 -960 960 960",width:"48",children:Bu("path",{d:"M626-533q22.5 0 38.25-15.75T680-587q0-22.5-15.75-38.25T626-641q-22.5 0-38.25 15.75T572-587q0 22.5 15.75 38.25T626-533Zm-292 0q22.5 0 38.25-15.75T388-587q0-22.5-15.75-38.25T334-641q-22.5 0-38.25 15.75T280-587q0 22.5 15.75 38.25T334-533Zm20 194h253v-49H354v49ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 340q142.375 0 241.188-98.812Q820-337.625 820-480t-98.812-241.188Q622.375-820 480-820t-241.188 98.812Q140-622.375 140-480t98.812 241.188Q337.625-140 480-140Z"})}),qu=Bu("svg",{className:"emoji-svg",xmlns:"http://www.w3.org/2000/svg",height:"48",viewBox:"0 -960 960 960",width:"48",children:Bu("path",{d:"M626-533q22.5 0 38.25-15.75T680-587q0-22.5-15.75-38.25T626-641q-22.5 0-38.25 15.75T572-587q0 22.5 15.75 38.25T626-533Zm-292 0q22.5 0 38.25-15.75T388-587q0-22.5-15.75-38.25T334-641q-22.5 0-38.25 15.75T280-587q0 22.5 15.75 38.25T334-533Zm146.174 116Q413-417 358.5-379.5T278-280h53q22-42 62.173-65t87.5-23Q528-368 567.5-344.5T630-280h52q-25-63-79.826-100-54.826-37-122-37ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 340q142.375 0 241.188-98.812Q820-337.625 820-480t-98.812-241.188Q622.375-820 480-820t-241.188 98.812Q140-622.375 140-480t98.812 241.188Q337.625-140 480-140Z"})}),Hu=Bu("svg",{className:"emoji-svg",xmlns:"http://www.w3.org/2000/svg",height:"48",viewBox:"0 -960 960 960",width:"48",children:Bu("path",{d:"M480-417q-67 0-121.5 37.5T278-280h404q-25-63-80-100t-122-37Zm-183-72 50-45 45 45 31-36-45-45 45-45-31-36-45 45-50-45-31 36 45 45-45 45 31 36Zm272 0 44-45 51 45 31-36-45-45 45-45-31-36-51 45-44-45-31 36 44 45-44 45 31 36ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 340q142 0 241-99t99-241q0-142-99-241t-241-99q-142 0-241 99t-99 241q0 142 99 241t241 99Z"})}),Vu=Bu("svg",{className:"emoji-svg",xmlns:"http://www.w3.org/2000/svg",height:"48",viewBox:"0 -960 960 960",width:"48",children:Bu("path",{d:"M479.504-261Q537-261 585.5-287q48.5-26 78.5-72.4 6-11.6-.75-22.6-6.75-11-20.25-11H316.918Q303-393 296.5-382t-.5 22.6q30 46.4 78.5 72.4 48.5 26 105.004 26ZM347-578l27 27q7.636 8 17.818 8Q402-543 410-551q8-8 8-18t-8-18l-42-42q-8.8-9-20.9-9-12.1 0-21.1 9l-42 42q-8 7.636-8 17.818Q276-559 284-551q8 8 18 8t18-8l27-27Zm267 0 27 27q7.714 8 18 8t18-8q8-7.636 8-17.818Q685-579 677-587l-42-42q-8.8-9-20.9-9-12.1 0-21.1 9l-42 42q-8 7.714-8 18t8 18q7.636 8 17.818 8Q579-543 587-551l27-27ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 340q142.375 0 241.188-98.812Q820-337.625 820-480t-98.812-241.188Q622.375-820 480-820t-241.188 98.812Q140-622.375 140-480t98.812 241.188Q337.625-140 480-140Z"})}),ju=Bu("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-labelledby":"close-survey-title",children:[Bu("title",{id:"close-survey-title",children:"Close survey"}),Bu("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M0.164752 0.164752C0.384422 -0.0549175 0.740578 -0.0549175 0.960248 0.164752L6 5.20451L11.0398 0.164752C11.2594 -0.0549175 11.6156 -0.0549175 11.8352 0.164752C12.0549 0.384422 12.0549 0.740578 11.8352 0.960248L6.79549 6L11.8352 11.0398C12.0549 11.2594 12.0549 11.6156 11.8352 11.8352C11.6156 12.0549 11.2594 12.0549 11.0398 11.8352L6 6.79549L0.960248 11.8352C0.740578 12.0549 0.384422 12.0549 0.164752 11.8352C-0.0549175 11.6156 -0.0549175 11.2594 0.164752 11.0398L5.20451 6L0.164752 0.960248C-0.0549175 0.740578 -0.0549175 0.384422 0.164752 0.164752Z",fill:"black"})]}),Yu=Bu("svg",{width:"77",height:"14",viewBox:"0 0 77 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[Bu("g",{"clip-path":"url(#clip0_2415_6911)",children:[Bu("mask",{id:"mask0_2415_6911",style:{maskType:"luminance"},maskUnits:"userSpaceOnUse",x:"0",y:"0",width:"77",height:"14",children:Bu("path",{d:"M0.5 0H76.5V14H0.5V0Z",fill:"white"})}),Bu("g",{mask:"url(#mask0_2415_6911)",children:[Bu("path",{d:"M5.77226 8.02931C5.59388 8.37329 5.08474 8.37329 4.90634 8.02931L4.4797 7.20672C4.41155 7.07535 4.41155 6.9207 4.4797 6.78933L4.90634 5.96669C5.08474 5.62276 5.59388 5.62276 5.77226 5.96669L6.19893 6.78933C6.26709 6.9207 6.26709 7.07535 6.19893 7.20672L5.77226 8.02931ZM5.77226 12.6946C5.59388 13.0386 5.08474 13.0386 4.90634 12.6946L4.4797 11.872C4.41155 11.7406 4.41155 11.586 4.4797 11.4546L4.90634 10.632C5.08474 10.288 5.59388 10.288 5.77226 10.632L6.19893 11.4546C6.26709 11.586 6.26709 11.7406 6.19893 11.872L5.77226 12.6946Z",fill:"#1D4AFF"}),Bu("path",{d:"M0.5 10.9238C0.5 10.508 1.02142 10.2998 1.32637 10.5938L3.54508 12.7327C3.85003 13.0267 3.63405 13.5294 3.20279 13.5294H0.984076C0.716728 13.5294 0.5 13.3205 0.5 13.0627V10.9238ZM0.5 8.67083C0.5 8.79459 0.551001 8.91331 0.641783 9.00081L5.19753 13.3927C5.28831 13.4802 5.41144 13.5294 5.53982 13.5294H8.0421C8.47337 13.5294 8.68936 13.0267 8.3844 12.7327L1.32637 5.92856C1.02142 5.63456 0.5 5.84278 0.5 6.25854V8.67083ZM0.5 4.00556C0.5 4.12932 0.551001 4.24802 0.641783 4.33554L10.0368 13.3927C10.1276 13.4802 10.2508 13.5294 10.3791 13.5294H12.8814C13.3127 13.5294 13.5287 13.0267 13.2237 12.7327L1.32637 1.26329C1.02142 0.969312 0.5 1.17752 0.5 1.59327V4.00556ZM5.33931 4.00556C5.33931 4.12932 5.39033 4.24802 5.4811 4.33554L14.1916 12.7327C14.4965 13.0267 15.0179 12.8185 15.0179 12.4028V9.99047C15.0179 9.86671 14.9669 9.74799 14.8762 9.66049L6.16568 1.26329C5.86071 0.969307 5.33931 1.17752 5.33931 1.59327V4.00556ZM11.005 1.26329C10.7 0.969307 10.1786 1.17752 10.1786 1.59327V4.00556C10.1786 4.12932 10.2296 4.24802 10.3204 4.33554L14.1916 8.06748C14.4965 8.36148 15.0179 8.15325 15.0179 7.7375V5.3252C15.0179 5.20144 14.9669 5.08272 14.8762 4.99522L11.005 1.26329Z",fill:"#F9BD2B"}),Bu("path",{d:"M21.0852 10.981L16.5288 6.58843C16.2238 6.29443 15.7024 6.50266 15.7024 6.91841V13.0627C15.7024 13.3205 15.9191 13.5294 16.1865 13.5294H23.2446C23.5119 13.5294 23.7287 13.3205 23.7287 13.0627V12.5032C23.7287 12.2455 23.511 12.0396 23.2459 12.0063C22.4323 11.9042 21.6713 11.546 21.0852 10.981ZM18.0252 12.0365C17.5978 12.0365 17.251 11.7021 17.251 11.2901C17.251 10.878 17.5978 10.5436 18.0252 10.5436C18.4527 10.5436 18.7996 10.878 18.7996 11.2901C18.7996 11.7021 18.4527 12.0365 18.0252 12.0365Z",fill:"currentColor"}),Bu("path",{d:"M0.5 13.0627C0.5 13.3205 0.716728 13.5294 0.984076 13.5294H3.20279C3.63405 13.5294 3.85003 13.0267 3.54508 12.7327L1.32637 10.5938C1.02142 10.2998 0.5 10.508 0.5 10.9238V13.0627ZM5.33931 5.13191L1.32637 1.26329C1.02142 0.969306 0.5 1.17752 0.5 1.59327V4.00556C0.5 4.12932 0.551001 4.24802 0.641783 4.33554L5.33931 8.86412V5.13191ZM1.32637 5.92855C1.02142 5.63455 0.5 5.84278 0.5 6.25853V8.67083C0.5 8.79459 0.551001 8.91331 0.641783 9.00081L5.33931 13.5294V9.79717L1.32637 5.92855Z",fill:"#1D4AFF"}),Bu("path",{d:"M10.1787 5.3252C10.1787 5.20144 10.1277 5.08272 10.0369 4.99522L6.16572 1.26329C5.8608 0.969306 5.33936 1.17752 5.33936 1.59327V4.00556C5.33936 4.12932 5.39037 4.24802 5.48114 4.33554L10.1787 8.86412V5.3252ZM5.33936 13.5294H8.04214C8.47341 13.5294 8.6894 13.0267 8.38443 12.7327L5.33936 9.79717V13.5294ZM5.33936 5.13191V8.67083C5.33936 8.79459 5.39037 8.91331 5.48114 9.00081L10.1787 13.5294V9.99047C10.1787 9.86671 10.1277 9.74803 10.0369 9.66049L5.33936 5.13191Z",fill:"#F54E00"}),Bu("path",{d:"M29.375 11.6667H31.3636V8.48772H33.0249C34.8499 8.48772 36.0204 7.4443 36.0204 5.83052C36.0204 4.21681 34.8499 3.17334 33.0249 3.17334H29.375V11.6667ZM31.3636 6.84972V4.81136H32.8236C33.5787 4.81136 34.0318 5.19958 34.0318 5.83052C34.0318 6.4615 33.5787 6.84972 32.8236 6.84972H31.3636ZM39.618 11.7637C41.5563 11.7637 42.9659 10.429 42.9659 8.60905C42.9659 6.78905 41.5563 5.45438 39.618 5.45438C37.6546 5.45438 36.2701 6.78905 36.2701 8.60905C36.2701 10.429 37.6546 11.7637 39.618 11.7637ZM38.1077 8.60905C38.1077 7.63838 38.7118 6.97105 39.618 6.97105C40.5116 6.97105 41.1157 7.63838 41.1157 8.60905C41.1157 9.57972 40.5116 10.2471 39.618 10.2471C38.7118 10.2471 38.1077 9.57972 38.1077 8.60905ZM46.1482 11.7637C47.6333 11.7637 48.6402 10.8658 48.6402 9.81025C48.6402 7.33505 45.2294 8.13585 45.2294 7.16518C45.2294 6.8983 45.5189 6.72843 45.9342 6.72843C46.3622 6.72843 46.8782 6.98318 47.0418 7.54132L48.527 6.94678C48.2375 6.06105 47.1677 5.45438 45.8713 5.45438C44.4743 5.45438 43.6058 6.25518 43.6058 7.21372C43.6058 9.53118 46.9663 8.88812 46.9663 9.84665C46.9663 10.1864 46.6391 10.417 46.1482 10.417C45.4434 10.417 44.9525 9.94376 44.8015 9.3735L43.3164 9.93158C43.6436 10.8537 44.6001 11.7637 46.1482 11.7637ZM53.4241 11.606L53.2982 10.0651C53.0843 10.1743 52.8074 10.2106 52.5808 10.2106C52.1278 10.2106 51.8257 9.89523 51.8257 9.34918V7.03172H53.3612V5.55145H51.8257V3.78001H49.9755V5.55145H48.9687V7.03172H49.9755V9.57972C49.9755 11.06 51.0202 11.7637 52.3921 11.7637C52.7696 11.7637 53.122 11.7031 53.4241 11.606ZM59.8749 3.17334V6.47358H56.376V3.17334H54.3874V11.6667H56.376V8.11158H59.8749V11.6667H61.8761V3.17334H59.8749ZM66.2899 11.7637C68.2281 11.7637 69.6378 10.429 69.6378 8.60905C69.6378 6.78905 68.2281 5.45438 66.2899 5.45438C64.3265 5.45438 62.942 6.78905 62.942 8.60905C62.942 10.429 64.3265 11.7637 66.2899 11.7637ZM64.7796 8.60905C64.7796 7.63838 65.3837 6.97105 66.2899 6.97105C67.1835 6.97105 67.7876 7.63838 67.7876 8.60905C67.7876 9.57972 67.1835 10.2471 66.2899 10.2471C65.3837 10.2471 64.7796 9.57972 64.7796 8.60905ZM73.2088 11.4725C73.901 11.4725 74.5177 11.242 74.845 10.8416V11.424C74.845 12.1034 74.2786 12.5767 73.4102 12.5767C72.7935 12.5767 72.2523 12.2854 72.1642 11.788L70.4776 12.0428C70.7042 13.1955 71.925 13.972 73.4102 13.972C75.361 13.972 76.6574 12.8679 76.6574 11.2298V5.55145H74.8324V6.07318C74.4926 5.69705 73.9136 5.45438 73.171 5.45438C71.409 5.45438 70.3014 6.61918 70.3014 8.46345C70.3014 10.3077 71.409 11.4725 73.2088 11.4725ZM72.1012 8.46345C72.1012 7.55345 72.655 6.97105 73.5109 6.97105C74.3793 6.97105 74.9331 7.55345 74.9331 8.46345C74.9331 9.37345 74.3793 9.95585 73.5109 9.95585C72.655 9.95585 72.1012 9.37345 72.1012 8.46345Z",fill:"currentColor"})]})]}),Bu("defs",{children:Bu("clipPath",{id:"clip0_2415_6911",children:Bu("rect",{width:"76",height:"14",fill:"white",transform:"translate(0.5)"})})})]});function zu(){return Bu("a",{href:"https://posthog.com/surveys",target:"_blank",rel:"noopener",className:"footer-branding",children:["Survey by ",Yu]})}function Gu(t){var{text:e,submitDisabled:i,appearance:r,onSubmit:n,link:o,onPreviewSubmit:s,skipSubmitButton:a}=t,{isPreviewMode:l}=$s(xu);return Bu("div",{className:"bottom-section",children:[!a&&Bu("button",{className:"form-submit",disabled:i,"aria-label":"Submit survey",type:"button",onClick:()=>{o&&(null==Bi||Bi.open(o)),l?null==s||s():n()},children:e}),!r.whiteLabel&&Bu(zu,{})]})}function Wu(t){var{question:e,forceDisableHtml:i,htmlFor:r}=t;return Bu("div",{class:"question-header",children:[Bu(e.type===Xs.Open?"label":"h3",{className:"survey-question",htmlFor:r,children:e.question}),e.description&&Tu({component:ts("p",{className:"survey-question-description"}),children:e.description,renderAsHtml:!i&&"text"!==e.descriptionContentType})]})}function Uu(t){var{onClick:e}=t,{isPreviewMode:i}=$s(xu);return Bu("button",{className:"form-cancel",onClick:e,disabled:i,"aria-label":"Close survey",type:"button",children:ju})}Bu("svg",{width:"16",height:"12",viewBox:"0 0 16 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:Bu("path",{d:"M5.30769 10.6923L4.77736 11.2226C4.91801 11.3633 5.10878 11.4423 5.30769 11.4423C5.5066 11.4423 5.69737 11.3633 5.83802 11.2226L5.30769 10.6923ZM15.5303 1.53033C15.8232 1.23744 15.8232 0.762563 15.5303 0.46967C15.2374 0.176777 14.7626 0.176777 14.4697 0.46967L15.5303 1.53033ZM1.53033 5.85429C1.23744 5.56139 0.762563 5.56139 0.46967 5.85429C0.176777 6.14718 0.176777 6.62205 0.46967 6.91495L1.53033 5.85429ZM5.83802 11.2226L15.5303 1.53033L14.4697 0.46967L4.77736 10.162L5.83802 11.2226ZM0.46967 6.91495L4.77736 11.2226L5.83802 10.162L1.53033 5.85429L0.46967 6.91495Z",fill:"currentColor"})});var Xu=Bi;function Ju(t){var{header:e,description:i,contentType:r,forceDisableHtml:n,appearance:o,onClose:s}=t,{isPopup:a}=$s(xu);return Ds((()=>{var t=t=>{"Enter"!==t.key&&"Escape"!==t.key||(t.preventDefault(),s())};return qr(Xu,"keydown",t),()=>{Xu.removeEventListener("keydown",t)}}),[s]),Bu("div",{className:"thank-you-message",role:"status",tabIndex:0,"aria-atomic":"true",children:[a&&Bu(Uu,{onClick:()=>s()}),Bu("h3",{className:"thank-you-message-header",children:e}),i&&Tu({component:ts("p",{className:"thank-you-message-body"}),children:i,renderAsHtml:!n&&"text"!==r}),a&&Bu(Gu,{text:o.thankYouMessageCloseButtonText||"Close",submitDisabled:!1,appearance:o,onSubmit:()=>s()})]})}var Ku=t=>lr(t)&&t.every((t=>pr(t)));function Qu(t){var{question:e,forceDisableHtml:i,appearance:r,onSubmit:n,onPreviewSubmit:o,displayQuestionIndex:s,initialValue:a}=t,{isPreviewMode:l}=Au(),u=Ls(null),[h,c]=Ps((()=>pr(a)?a:""));Ds((()=>{setTimeout((()=>{var t;l||(null==(t=u.current)||t.focus())}),100)}),[l]);var d="surveyQuestion"+s;return Bu(is,{children:[Bu("div",{className:"question-container",children:[Bu(Wu,{question:e,forceDisableHtml:i,htmlFor:d}),Bu("textarea",{ref:u,id:d,rows:4,placeholder:null==r?void 0:r.placeholder,onInput:t=>{c(t.currentTarget.value),t.stopPropagation()},onKeyDown:t=>{t.stopPropagation()},value:h})]}),Bu(Gu,{text:e.buttonText||"Submit",submitDisabled:!h&&!e.optional,appearance:r,onSubmit:()=>n(h),onPreviewSubmit:()=>o(h)})]})}function th(t){var{question:e,forceDisableHtml:i,appearance:r,onSubmit:n,onPreviewSubmit:o}=t;return Bu(is,{children:[Bu("div",{className:"question-container",children:Bu(Wu,{question:e,forceDisableHtml:i})}),Bu(Gu,{text:e.buttonText||"Submit",submitDisabled:!1,link:e.link,appearance:r,onSubmit:()=>n("link clicked"),onPreviewSubmit:()=>o("link clicked")})]})}function eh(t){var{question:e,forceDisableHtml:i,displayQuestionIndex:r,appearance:n,onSubmit:o,onPreviewSubmit:s,initialValue:a}=t,l=e.scale,u=10===e.scale?0:1,[h,c]=Ps((()=>br(a)?a:lr(a)&&a.length>0&&br(parseInt(a[0]))?parseInt(a[0]):pr(a)&&br(parseInt(a))?parseInt(a):null)),{isPreviewMode:d}=Au(),v=t=>d?s(t):o(t);return Bu(is,{children:[Bu("div",{className:"question-container",children:[Bu(Wu,{question:e,forceDisableHtml:i}),Bu("div",{className:"rating-section",children:[Bu("div",{className:"rating-options",children:["emoji"===e.display&&Bu("div",{className:"rating-options-emoji",children:(3===e.scale?nh:oh).map(((t,i)=>Bu("button",{"aria-label":"Rate "+(i+1),className:"ratings-emoji question-"+r+"-rating-"+i+" "+(i+1===h?"rating-active":""),value:i+1,type:"button",onClick:()=>{var t=i+1;c(t),e.skipSubmitButton&&v(t)},children:t},i)))}),"number"===e.display&&Bu("div",{className:"rating-options-number",style:{gridTemplateColumns:"repeat("+(l-u+1)+", minmax(0, 1fr))"},children:uh(e.scale).map(((t,i)=>Bu(ih,{displayQuestionIndex:r,active:h===t,appearance:n,num:t,setActiveNumber:t=>{c(t),e.skipSubmitButton&&v(t)}},i)))})]}),Bu("div",{className:"rating-text",children:[Bu("div",{children:e.lowerBoundLabel}),Bu("div",{children:e.upperBoundLabel})]})]})]}),Bu(Gu,{text:e.buttonText||(null==n?void 0:n.submitButtonText)||"Submit",submitDisabled:mr(h)&&!e.optional,appearance:n,onSubmit:()=>o(h),onPreviewSubmit:()=>s(h),skipSubmitButton:e.skipSubmitButton})]})}function ih(t){var{num:e,active:i,displayQuestionIndex:r,setActiveNumber:n}=t;return Bu("button",{"aria-label":"Rate "+e,className:"ratings-number question-"+r+"-rating-"+e+" "+(i?"rating-active":""),type:"button",onClick:()=>{n(e)},children:e})}function rh(t){var{question:e,forceDisableHtml:r,displayQuestionIndex:n,appearance:o,onSubmit:s,onPreviewSubmit:a,initialValue:l}=t,u=Ls(null),h=Bs((()=>(t=>{if(!t.shuffleOptions)return t.choices;var e=t.choices,i="";t.hasOpenChoice&&(i=e.pop());var r=Iu(e,wu(e));return t.hasOpenChoice&&(t.choices.push(i),r.push(i)),r})(e)),[e]),[c,d]=Ps((()=>((t,e)=>pr(t)||Ku(t)?t:e===Xs.SingleChoice?null:[])(l,e.type))),[v,f]=Ps((()=>((t,e)=>{if(pr(t)&&!e.includes(t))return{isSelected:!0,inputValue:t};if(Ku(t)){var i=t.find((t=>!e.includes(t)));if(i)return{isSelected:!0,inputValue:i}}return{isSelected:!1,inputValue:""}})(l,h))),{isPreviewMode:p}=Au(),g=e.type===Xs.SingleChoice,m=e.type===Xs.MultipleChoice,y=e.skipSubmitButton&&g&&!e.hasOpenChoice,b=(t,e)=>{if(e){var r=!v.isSelected;return f((t=>i({},t,{isSelected:r,inputValue:r?t.inputValue:""}))),g&&d(""),void(r&&setTimeout((()=>{var t;return null==(t=u.current)?void 0:t.focus()}),75))}if(g)return d(t),f((t=>i({},t,{isSelected:!1,inputValue:""}))),void(y&&(s(t),p&&a(t)));m&&lr(c)&&(c.includes(t)?d(c.filter((e=>e!==t))):d([...c,t]))},_=t=>{t.stopPropagation();var e=t.currentTarget.value;f((t=>i({},t,{inputValue:e}))),g&&d(e)},w=t=>{t.stopPropagation(),"Enter"!==t.key||I()||(t.preventDefault(),C()),"Escape"===t.key&&(t.preventDefault(),f((t=>i({},t,{isSelected:!1,inputValue:""}))),g&&d(null))},I=()=>!e.optional&&(!!mr(c)||(!(!lr(c)||v.isSelected||0!==c.length)||!(!v.isSelected||""!==v.inputValue.trim()))),C=()=>{v.isSelected&&m?lr(c)&&(p?a([...c,v.inputValue]):s([...c,v.inputValue])):p?a(c):s(c)};return Bu(is,{children:[Bu("div",{className:"question-container",children:[Bu(Wu,{question:e,forceDisableHtml:r}),Bu("fieldset",{className:"multiple-choice-options limit-height",children:[Bu("legend",{className:"sr-only",children:m?" Select all that apply":" Select one"}),h.map(((t,i)=>{var r=!!e.hasOpenChoice&&i===e.choices.length-1,o="surveyQuestion"+n+"Choice"+i,s=o+"Open",a=r?v.isSelected:g?c===t:lr(c)&&c.includes(t);return Bu("label",{className:r?"choice-option-open":"",children:[Bu("input",{type:g?"radio":"checkbox",name:o,checked:a,onChange:()=>b(t,r),id:o,"aria-controls":s}),Bu("span",{children:r?t+":":t}),r&&Bu("input",{type:"text",ref:u,id:s,name:"question"+n+"Open",value:v.inputValue,onKeyDown:w,onInput:_,onClick:e=>{v.isSelected||b(t,!0),e.stopPropagation()},"aria-label":t+" - please specify"})]},i)}))]})]}),Bu(Gu,{text:e.buttonText||"Submit",submitDisabled:I(),appearance:o,onSubmit:C,onPreviewSubmit:C,skipSubmitButton:y})]})}var nh=[qu,Zu,$u],oh=[Hu,qu,Zu,$u,Vu],sh=[1,2,3,4,5],ah=[1,2,3,4,5,6,7],lh=[0,1,2,3,4,5,6,7,8,9,10];function uh(t){switch(t){case 5:default:return sh;case 7:return ah;case 10:return lh}}var hh=Bi,ch=ji,dh="ph:show_survey_widget",vh="PHWidgetSurveyClickListener";function fh(t,e,i){var r,n=t.questions[e],o=e+1;if(null==(r=n.branching)||!r.type)return e===t.questions.length-1?Js.End:o;if(n.branching.type===Js.End)return Js.End;if(n.branching.type===Js.SpecificQuestion){if(Number.isInteger(n.branching.index))return n.branching.index}else if(n.branching.type===Js.ResponseBased){if(n.type===Xs.SingleChoice){var s,a=n.choices.indexOf(""+i);if(-1===a&&n.hasOpenChoice&&(a=n.choices.length-1),null!=(s=n.branching)&&null!=(s=s.responseValues)&&s.hasOwnProperty(a)){var l=n.branching.responseValues[a];return Number.isInteger(l)?l:l===Js.End?Js.End:o}}else if(n.type===Xs.Rating){var u;if("number"!=typeof i||!Number.isInteger(i))throw new Error("The response type must be an integer");var h=function(t,e){if(3===e){if(t<1||t>3)throw new Error("The response must be in range 1-3");return 1===t?"negative":2===t?"neutral":"positive"}if(5===e){if(t<1||t>5)throw new Error("The response must be in range 1-5");return t<=2?"negative":3===t?"neutral":"positive"}if(7===e){if(t<1||t>7)throw new Error("The response must be in range 1-7");return t<=3?"negative":4===t?"neutral":"positive"}if(10===e){if(t<0||t>10)throw new Error("The response must be in range 0-10");return t<=6?"detractors":t<=8?"passives":"promoters"}throw new Error("The scale must be one of: 3, 5, 7, 10")}(i,n.scale);if(null!=(u=n.branching)&&null!=(u=u.responseValues)&&u.hasOwnProperty(h)){var c=n.branching.responseValues[h];return Number.isInteger(c)?c:c===Js.End?Js.End:o}}return o}return ea.warn("Falling back to next question index due to unexpected branching type"),o}var ph=250,gh=20,mh=12;class yh{constructor(t){var e=this;this.A=new Map,this.T=new Map,this.M=t=>{var e;this.R(t.id),this.N(t);var r=(null==(e=t.appearance)?void 0:e.surveyPopupDelaySeconds)||0,{shadow:n}=mu(t,this._posthog);if(r<=0)return _s(Bu(Ih,{posthog:this._posthog,survey:t,removeSurveyFromFocus:this.F}),n);var o=setTimeout((()=>{if(!Ru(t))return this.F(t);_s(Bu(Ih,{posthog:this._posthog,survey:i({},t,{appearance:i({},t.appearance,{surveyPopupDelaySeconds:0})}),removeSurveyFromFocus:this.F}),n)}),1e3*r);this.A.set(t.id,o)},this.O=t=>{var{shadow:e,isNewlyCreated:i}=mu(t,this._posthog);i&&_s(Bu(Sh,{posthog:this._posthog,survey:t},t.id),e)},this.P=t=>{this.D(t);var e=this.T.get(t.id);e&&(e.element.removeEventListener("click",e.listener),e.element.removeAttribute(vh),this.T.delete(t.id),ea.info("Removed click listener for survey "+t.id))},this.L=(t,e)=>{var i=ch.querySelector(e),r=this.T.get(t.id);if(i){if(this.O(t),r){if(i===r.element)return;ea.info("Selector element changed for survey "+t.id+". Re-attaching listener."),this.P(t)}if(!i.hasAttribute(vh)){var n=e=>{var i,r;e.stopPropagation();var n=(null==(i=t.appearance)?void 0:i.position)===Ws.NextToTrigger?function(t,e){try{var i=t.getBoundingClientRect(),r=hh.innerHeight,n=hh.innerWidth,o=ph,s=i.left+i.width/2-e/2;s+e>n-gh&&(s=n-e-gh),s<gh&&(s=gh);var a=mh,l=r-i.bottom,u=i.top,h=l<o&&u>l;return{position:"fixed",top:h?"auto":i.bottom+a+"px",left:s+"px",right:"auto",bottom:h?r-i.top+a+"px":"auto",zIndex:vu.zIndex}}catch(t){return ea.warn("Failed to calculate trigger position:",t),null}}(e.currentTarget,parseInt((null==(r=t.appearance)?void 0:r.maxWidth)||vu.maxWidth)):{};hh.dispatchEvent(new CustomEvent(dh,{detail:{surveyId:t.id,position:n}}))};qr(i,"click",n),i.setAttribute(vh,"true"),this.T.set(t.id,{element:i,listener:n,survey:t}),ea.info("Attached click listener for feedback button survey "+t.id)}}else r&&this.P(t)},this.renderSurvey=(t,e)=>{_s(Bu(Ih,{posthog:this._posthog,survey:t,removeSurveyFromFocus:this.F,isPopup:!1}),e)},this.getActiveMatchingSurveys=function(t,i){var r;void 0===i&&(i=!1),null==(r=e._posthog)||r.surveys.getSurveys((i=>{var r=i.filter((t=>e.checkSurveyEligibility(t).eligible&&e.B(t)&&e.$(t)&&e.Z(t)));t(r)}),i)},this.callSurveysAndEvaluateDisplayLogic=function(t){void 0===t&&(t=!1),e.getActiveMatchingSurveys((t=>{var i=t.filter((t=>t.type!==Us.API)),r=e.q(i),n=new Set;r.forEach((t=>{if(t.type===Us.Widget){var i,r,o,s;if((null==(i=t.appearance)?void 0:i.widgetType)===Gs.Tab)return void e.O(t);if((null==(r=t.appearance)?void 0:r.widgetType)===Gs.Selector&&null!=(o=t.appearance)&&o.widgetSelector)n.add(t.id),e.L(t,null==(s=t.appearance)?void 0:s.widgetSelector)}mr(e.H)&&t.type===Us.Popover&&e.M(t)})),e.T.forEach((t=>{var{survey:i}=t;n.has(i.id)||e.P(i)}))}),t)},this.N=t=>{mr(this.H)||ea.error("Survey "+[...this.H]+" already in focus. Cannot add survey "+t.id+"."),this.H=t.id},this.F=t=>{this.H!==t.id&&ea.error("Survey "+t.id+" is not in focus. Cannot remove survey "+t.id+"."),this.R(t.id),this.H=null,this.D(t)},this._posthog=t,this.H=null}R(t){var e=this.A.get(t);e&&(clearTimeout(e),this.A.delete(t))}q(t){return t.sort(((t,e)=>{var i,r,n=Ou(t),o=Ou(e);if(n&&!o)return-1;if(!n&&o)return 1;var s=t.schedule===Ks.Always,a=e.schedule===Ks.Always;return s&&!a?1:!s&&a?-1:((null==(i=t.appearance)?void 0:i.surveyPopupDelaySeconds)||0)-((null==(r=e.appearance)?void 0:r.surveyPopupDelaySeconds)||0)}))}V(t){return!t||!!this._posthog.featureFlags.isFeatureEnabled(t,{send_event:!t.startsWith("survey-targeting-")})}B(t){return!t.conditions||Ru(t)&&function(t){var e,i,r;if(null==(e=t.conditions)||!e.deviceTypes||0===(null==(i=t.conditions)?void 0:i.deviceTypes.length))return!0;if(!Ui)return!1;var n=dl(Ui);return su[Mu(null==(r=t.conditions)?void 0:r.deviceTypesMatchType)](t.conditions.deviceTypes,[n])}(t)&&function(t){var e;return null==(e=t.conditions)||!e.selector||!(null==hu||!hu.querySelector(t.conditions.selector))}(t)}j(t){return Su(t)||this.V(t.internal_targeting_flag_key)||Ou(t)}checkSurveyEligibility(t){var e,i={eligible:!0,reason:void 0};return ia(t)?this.V(t.linked_flag_key)?this.V(t.targeting_flag_key)?this.j(t)?(t=>{var e=localStorage.getItem("lastSeenSurveyDate");if(!t||!e)return!0;var i=new Date,r=Math.abs(i.getTime()-new Date(e).getTime());return Math.ceil(r/864e5)>t})(null==(e=t.conditions)?void 0:e.seenSurveyWaitPeriodInDays)?(t=>!!localStorage.getItem(ku(t))&&!Su(t))(t)?(i.eligible=!1,i.reason="Survey has already been seen and it can't be activated again",i):i:(i.eligible=!1,i.reason="Survey wait period has not passed",i):(i.eligible=!1,i.reason="Survey internal targeting flag is not enabled and survey cannot activate repeatedly and survey is not in progress",i):(i.eligible=!1,i.reason="Survey targeting feature flag is not enabled",i):(i.eligible=!1,i.reason="Survey linked feature flag is not enabled",i):(i.eligible=!1,i.reason="Survey is not running. It was completed on "+t.end_date,i)}$(t){var e;if(!ra(t)&&!na(t))return!0;var i=null==(e=this._posthog.surveys._surveyEventReceiver)?void 0:e.getSurveys();return!(null==i||!i.includes(t.id))}Z(t){var e;return null==(e=t.feature_flag_keys)||!e.length||t.feature_flag_keys.every((t=>{var{key:e,value:i}=t;return!e||!i||this.V(i)}))}D(t){try{var e=ch.querySelector(Du(t,!0));null!=e&&e.shadowRoot&&_s(null,e.shadowRoot),null==e||e.remove()}catch(e){ea.warn("Failed to remove survey "+t.id+" from DOM:",e)}}getTestAPI(){return{addSurveyToFocus:this.N,removeSurveyFromFocus:this.F,surveyInFocus:this.H,surveyTimeouts:this.A,handleWidget:this.O,handlePopoverSurvey:this.M,manageWidgetSelectorListener:this.L,sortSurveysByAppearanceDelay:this.q,checkFlags:this.Z.bind(this),isSurveyFeatureFlagEnabled:this.V.bind(this)}}}function bh(t){if(ch&&hh){var e=new yh(t);return t.config.disable_surveys_automatic_display?(ea.info("Surveys automatic display is disabled. Skipping call surveys and evaluate display logic."),e):(e.callSurveysAndEvaluateDisplayLogic(!0),setInterval((()=>{e.callSurveysAndEvaluateDisplayLogic(!1)}),1e3),e)}}function _h(t){var{survey:e,removeSurveyFromFocus:i=(()=>{}),setSurveyVisible:r,isPreviewMode:n=!1}=t;Ds((()=>{var t;if(!n&&null!=(t=e.conditions)&&t.url){var o=()=>{var t,n=e.type===Us.Widget,o=Ru(e),s=(null==(t=e.appearance)?void 0:t.widgetType)===Gs.Tab&&n;if(!o)return ea.info("Hiding survey "+e.id+" because URL does not match"),r(!1),i(e);s&&(ea.info("Showing survey "+e.id+" because it is a feedback button tab and URL matches"),r(!0))};qr(hh,"popstate",o),qr(hh,"hashchange",o);var s=hh.history.pushState,a=hh.history.replaceState;return hh.history.pushState=function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];s.apply(this,e),o()},hh.history.replaceState=function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];a.apply(this,e),o()},()=>{hh.removeEventListener("popstate",o),hh.removeEventListener("hashchange",o),hh.history.pushState=s,hh.history.replaceState=a}}}),[n,e,i,r])}function wh(t,e,i){switch(void 0===e&&(e=Ws.Right),e){case Ws.TopLeft:return{top:"0",left:"0",transform:"translate(30px, 30px)"};case Ws.TopRight:return{top:"0",right:"0",transform:"translate(-30px, 30px)"};case Ws.TopCenter:return{top:"0",left:"50%",transform:"translate(-50%, 30px)"};case Ws.MiddleLeft:return{top:"50%",left:"0",transform:"translate(30px, -50%)"};case Ws.MiddleRight:return{top:"50%",right:"0",transform:"translate(-30px, -50%)"};case Ws.MiddleCenter:return{top:"50%",left:"50%",transform:"translate(-50%, -50%)"};case Ws.Left:return{left:"30px"};case Ws.Center:return{left:"50%",transform:"translateX(-50%)"};default:case Ws.Right:return{right:t===Us.Widget&&i===Gs.Tab?"60px":"30px"}}}function Ih(t){var e,r,n,o,s,a,{survey:l,forceDisableHtml:u,posthog:h,style:c={},previewPageIndex:d,removeSurveyFromFocus:v=(()=>{}),isPopup:f=!0,onPreviewSubmit:p=(()=>{}),onPopupSurveyDismissed:g=(()=>{}),onCloseConfirmationMessage:m=(()=>{})}=t,y=Ls(null),b=Number.isInteger(d),_=null!=(e=l.appearance)&&e.surveyPopupDelaySeconds?1e3*l.appearance.surveyPopupDelaySeconds:0,{isPopupVisible:w,isSurveySent:I,hidePopupWithViewTransition:C}=function(t,e,i,r,n,o){var[s,a]=Ps(r||0===i),[l,u]=Ps(!1),h=()=>{var e=()=>{t.type===Us.Popover&&n(t),a(!1)};ch.startViewTransition?ch.startViewTransition((()=>{var t;null==o||null==(t=o.current)||t.remove()})).finished.then((()=>{setTimeout((()=>{e()}),100)})):e()},c=e=>{e.detail.surveyId===t.id&&h()};return Ds((()=>{if(e){if(!r){var n=e=>{var i,r;if(e.detail.surveyId===t.id){if(null==(i=t.appearance)||!i.displayThankYouMessage)return h();u(!0),null!=(r=t.appearance)&&r.autoDisappear&&setTimeout((()=>{h()}),5e3)}},o=()=>{Ru(t)&&(a(!0),hh.dispatchEvent(new Event("PHSurveyShown")),e.capture(Qs.SHOWN,{[ta.SURVEY_NAME]:t.name,[ta.SURVEY_ID]:t.id,[ta.SURVEY_ITERATION]:t.current_iteration,[ta.SURVEY_ITERATION_START_DATE]:t.current_iteration_start_date,sessionRecordingUrl:null==e.get_session_replay_url?void 0:e.get_session_replay_url()}),localStorage.setItem("lastSeenSurveyDate",(new Date).toISOString()))};if(qr(hh,"PHSurveyClosed",c),qr(hh,"PHSurveySent",n),i>0){var s=setTimeout(o,i);return()=>{clearTimeout(s),hh.removeEventListener("PHSurveyClosed",c),hh.removeEventListener("PHSurveySent",n)}}return o(),()=>{hh.removeEventListener("PHSurveyClosed",c),hh.removeEventListener("PHSurveySent",n)}}}else ea.error("usePopupVisibility hook called without a PostHog instance.")}),[]),_h({survey:t,removeSurveyFromFocus:n,setSurveyVisible:a,isPreviewMode:r}),{isPopupVisible:s,isSurveySent:l,setIsPopupVisible:a,hidePopupWithViewTransition:h}}(l,h,_,b,v,y),S=I||d===l.questions.length,k=Bs((()=>{var t=Fu(l);return{isPreviewMode:b,previewPageIndex:d,onPopupSurveyDismissed:()=>{_u(l,h,b),g()},isPopup:f||!1,surveySubmissionId:(null==t?void 0:t.surveySubmissionId)||fa(),onPreviewSubmit:p,posthog:h}}),[b,d,f,h,l,g,p]);return w?Bu(xu.Provider,{value:k,children:Bu("div",{className:"ph-survey",style:i({},wh(l.type,null==(r=l.appearance)?void 0:r.position,null==(n=l.appearance)?void 0:n.widgetType),c),ref:y,children:S?Bu(Ju,{header:(null==(o=l.appearance)?void 0:o.thankYouMessageHeader)||"Thank you!",description:(null==(s=l.appearance)?void 0:s.thankYouMessageDescription)||"",forceDisableHtml:!!u,contentType:null==(a=l.appearance)?void 0:a.thankYouMessageDescriptionContentType,appearance:l.appearance||vu,onClose:()=>{C(),m()}}):Bu(Ch,{survey:l,forceDisableHtml:!!u,posthog:h})})}):null}function Ch(t){var{survey:e,forceDisableHtml:r,posthog:n}=t,[o,s]=Ps((()=>{var t=Fu(e);return null!=t&&t.responses&&ea.info("Survey is already in progress, filling in initial responses"),(null==t?void 0:t.responses)||{}})),{previewPageIndex:a,onPopupSurveyDismissed:l,isPopup:u,onPreviewSubmit:h,surveySubmissionId:c,isPreviewMode:d}=$s(xu),[v,f]=Ps((()=>{var t=Fu(e);return a||(null==t?void 0:t.lastQuestionIndex)||0})),p=Bs((()=>Cu(e)),[e]);Ds((()=>{d&&!fr(a)&&f(a)}),[a,d]);var g=p.at(v);return g?Bu("form",{className:"survey-form",name:"surveyForm",children:[u&&Bu(Uu,{onClick:()=>{l()}}),Bu("div",{className:"survey-box",children:kh({question:g,forceDisableHtml:r,displayQuestionIndex:v,appearance:e.appearance||vu,onSubmit:t=>(t=>{var{res:r,displayQuestionIndex:a,questionId:l}=t;if(n)if(l){var u=cu(l),h=i({},o,{[u]:r});s(h);var d=fh(e,a,r),v=d===Js.End;v||(f(d),Eu(e,{surveySubmissionId:c,responses:h,lastQuestionIndex:d})),(e.enable_partial_responses||v)&&bu({responses:h,survey:e,surveySubmissionId:c,isSurveyCompleted:v,posthog:n})}else ea.error("onNextButtonClick called without a questionId.");else ea.error("onNextButtonClick called without a PostHog instance.")})({res:t,displayQuestionIndex:v,questionId:g.id}),onPreviewSubmit:h,initialValue:g.id?o[cu(g.id)]:void 0})})]}):null}function Sh(t){var e,i,r,n,o,{survey:s,forceDisableHtml:a,posthog:l,readOnly:u}=t,[h,c]=Ps(!0),[d,v]=Ps(!1),[f,p]=Ps({}),g=()=>{v(!d)};if(Ds((()=>{var t;if(l){if(!u){"tab"===(null==(t=s.appearance)?void 0:t.widgetType)&&p({top:"50%",bottom:"auto"});var e=t=>{var e,i=t;(null==(e=i.detail)?void 0:e.surveyId)===s.id&&(ea.info("Received show event for feedback button survey "+s.id),p(i.detail.position||{}),g())};return qr(hh,dh,e),()=>{hh.removeEventListener(dh,e)}}}else ea.error("FeedbackWidget called without a PostHog instance.")}),[l,u,s.id,null==(e=s.appearance)?void 0:e.widgetType,null==(i=s.appearance)?void 0:i.widgetSelector,null==(r=s.appearance)?void 0:r.borderColor]),_h({survey:s,setSurveyVisible:c}),!h)return null;var m=()=>{s.schedule!==Ks.Always&&c(!1),setTimeout((()=>{v(!1)}),200)};return Bu(is,{children:["tab"===(null==(n=s.appearance)?void 0:n.widgetType)&&Bu("button",{className:"ph-survey-widget-tab",onClick:g,disabled:u,children:(null==(o=s.appearance)?void 0:o.widgetLabel)||""}),d&&Bu(Ih,{posthog:l,survey:s,forceDisableHtml:a,style:f,onPopupSurveyDismissed:m,onCloseConfirmationMessage:m})]})}var kh=t=>{var{question:e,forceDisableHtml:r,displayQuestionIndex:n,appearance:o,onSubmit:s,onPreviewSubmit:a,initialValue:l}=t,u={forceDisableHtml:r,appearance:o,onPreviewSubmit:t=>{a(t)},onSubmit:t=>{s(t)},initialValue:l,displayQuestionIndex:n};switch(e.type){case Xs.Open:return ts(Qu,i({},u,{question:e,key:e.id}));case Xs.Link:return ts(th,i({},u,{question:e,key:e.id}));case Xs.Rating:return ts(eh,i({},u,{question:e,key:e.id}));case Xs.SingleChoice:case Xs.MultipleChoice:return ts(rh,i({},u,{question:e,key:e.id}));default:return ea.error("Unsupported question type: "+e.type),null}};function xh(t){return!fr(Event)&&Ah(t,Event)}function Ah(t,e){try{return t instanceof e}catch(t){return!1}}function Th(t){return mr(t)||!dr(t)&&!ur(t)}function Mh(t){switch(Object.prototype.toString.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object DOMError]":return!0;default:return Ah(t,Error)}}function Rh(t,e){return Object.prototype.toString.call(t)==="[object "+e+"]"}function Nh(t){return Rh(t,"DOMError")}Xi.__PosthogExtensions__=Xi.__PosthogExtensions__||{},Xi.__PosthogExtensions__.generateSurveys=bh,Xi.extendPostHogWithSurveys=bh;var Eh=/\(error: (.*)\)/,Fh=50,Oh="?";function Ph(t,e,i,r){var n={platform:"web:javascript",filename:t,function:"<anonymous>"===e?Oh:e,in_app:!0};return fr(i)||(n.lineno=i),fr(r)||(n.colno=r),n}var Dh=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,Lh=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,Bh=/\((\S*)(?::(\d+))(?::(\d+))\)/,$h=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,Zh=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,qh=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e.sort(((t,e)=>t[0]-e[0])).map((t=>t[1]));return function(t,e){void 0===e&&(e=0);for(var r=[],o=t.split("\n"),s=e;s<o.length;s++){var a=o[s];if(!(a.length>1024)){var l=Eh.test(a)?a.replace(Eh,"$1"):a;if(!l.match(/\S*Error: /)){for(var u of n){var h=u(l);if(h){r.push(h);break}}if(r.length>=Fh)break}}}return function(t){if(!t.length)return[];var e=Array.from(t);return e.reverse(),e.slice(0,Fh).map((t=>i({},t,{filename:t.filename||Hh(e).filename,function:t.function||Oh})))}(r)}}(...[[30,t=>{var e=Dh.exec(t);if(e){var[,i,r,n]=e;return Ph(i,Oh,+r,+n)}var o=Lh.exec(t);if(o){if(o[2]&&0===o[2].indexOf("eval")){var s=Bh.exec(o[2]);s&&(o[2]=s[1],o[3]=s[2],o[4]=s[3])}var[a,l]=zh(o[1]||Oh,o[2]);return Ph(l,a,o[3]?+o[3]:void 0,o[4]?+o[4]:void 0)}}],[50,t=>{var e=$h.exec(t);if(e){if(e[3]&&e[3].indexOf(" > eval")>-1){var i=Zh.exec(e[3]);i&&(e[1]=e[1]||"eval",e[3]=i[1],e[4]=i[2],e[5]="")}var r=e[3],n=e[1]||Oh;return[n,r]=zh(n,r),Ph(r,n,e[4]?+e[4]:void 0,e[5]?+e[5]:void 0)}}]]);function Hh(t){return t[t.length-1]||{}}var Vh,jh,Yh,zh=(t,e)=>{var i=-1!==t.indexOf("safari-extension"),r=-1!==t.indexOf("safari-web-extension");return i||r?[-1!==t.indexOf("@")?t.split("@")[0]:Oh,i?"safari-extension:"+e:"safari-web-extension:"+e]:[t,e]};var Gh=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i;function Wh(t,e){void 0===e&&(e=0);var i=t.stacktrace||t.stack||"",r=function(t){if(t&&Uh.test(t.message))return 1;return 0}(t);try{var n=qh,o=function(t,e){var i=function(t){var e=globalThis._posthogChunkIds;if(!e)return{};var i=Object.keys(e);return Yh&&i.length===jh||(jh=i.length,Yh=i.reduce(((i,r)=>{Vh||(Vh={});var n=Vh[r];if(n)i[n[0]]=n[1];else for(var o=t(r),s=o.length-1;s>=0;s--){var a=o[s],l=null==a?void 0:a.filename,u=e[r];if(l&&u){i[l]=u,Vh[r]=[l,u];break}}return i}),{})),Yh}(e);return t.forEach((t=>{t.filename&&(t.chunk_id=i[t.filename])})),t}(n(i,r),n);return o.slice(0,o.length-e)}catch(t){}return[]}var Uh=/Minified React error #\d+;/i;function Xh(t,e){var i,r,n=Wh(t),o=null===(i=null==e?void 0:e.handled)||void 0===i||i,s=null!==(r=null==e?void 0:e.synthetic)&&void 0!==r&&r;return{type:null!=e&&e.overrideExceptionType?e.overrideExceptionType:t.name,value:function(t){var e=t.message;if(e.error&&"string"==typeof e.error.message)return String(e.error.message);return String(e)}(t),stacktrace:{frames:n,type:"raw"},mechanism:{handled:o,synthetic:s}}}function Jh(t,e){var i=Xh(t,e);return t.cause&&Mh(t.cause)&&t.cause!==t?[i,...Jh(t.cause,{handled:null==e?void 0:e.handled,synthetic:null==e?void 0:e.synthetic})]:[i]}function Kh(t,e){return{$exception_list:Jh(t,e),$exception_level:"error"}}function Qh(t,e){var i,r,n,o=null===(i=null==e?void 0:e.handled)||void 0===i||i,s=null===(r=null==e?void 0:e.synthetic)||void 0===r||r,a={type:null!=e&&e.overrideExceptionType?e.overrideExceptionType:null!==(n=null==e?void 0:e.defaultExceptionType)&&void 0!==n?n:"Error",value:t||(null==e?void 0:e.defaultExceptionMessage),mechanism:{handled:o,synthetic:s}};if(null!=e&&e.syntheticException){var l=Wh(e.syntheticException,1);l.length&&(a.stacktrace={frames:l,type:"raw"})}return{$exception_list:[a],$exception_level:"error"}}function tc(t){return pr(t)&&!gr(t)&&tr.indexOf(t)>=0}function ec(t,e){var i,r,n=null===(i=null==e?void 0:e.handled)||void 0===i||i,o=null===(r=null==e?void 0:e.synthetic)||void 0===r||r,s=null!=e&&e.overrideExceptionType?e.overrideExceptionType:xh(t)?t.constructor.name:"Error",a="Non-Error 'exception' captured with keys: "+function(t,e){void 0===e&&(e=40);var i=Object.keys(t);if(i.sort(),!i.length)return"[object has no keys]";for(var r=i.length;r>0;r--){var n=i.slice(0,r).join(", ");if(!(n.length>e))return r===i.length||n.length<=e?n:n.slice(0,e)+"..."}return""}(t),l={type:s,value:a,mechanism:{handled:n,synthetic:o}};if(null!=e&&e.syntheticException){var u=Wh(null==e?void 0:e.syntheticException,1);u.length&&(l.stacktrace={frames:u,type:"raw"})}return{$exception_list:[l],$exception_level:tc(t.level)?t.level:"error"}}function ic(t,e){var{error:r,event:n}=t,o={$exception_list:[]},s=r||n;if(Nh(s)||function(t){return Rh(t,"DOMException")}(s)){var a=s;if(function(t){return"stack"in t}(s))o=Kh(s,e);else{var l=a.name||(Nh(a)?"DOMError":"DOMException"),u=a.message?l+": "+a.message:l;o=Qh(u,i({},e,{overrideExceptionType:Nh(a)?"DOMError":"DOMException",defaultExceptionMessage:u}))}return"code"in a&&(o.$exception_DOMException_code=""+a.code),o}if(function(t){return Rh(t,"ErrorEvent")}(s)&&s.error)return Kh(s.error,e);if(Mh(s))return Kh(s,e);if(function(t){return Rh(t,"Object")}(s)||xh(s))return ec(s,e);if(fr(r)&&pr(n)){var h="Error",c=n,d=n.match(Gh);return d&&(h=d[1],c=d[2]),Qh(c,i({},e,{overrideExceptionType:h,defaultExceptionMessage:c}))}return Qh(s,e)}function rc(t){var[e]=t,i=function(t){if(Th(t))return t;try{if("reason"in t)return t.reason;if("detail"in t&&"reason"in t.detail)return t.detail.reason}catch(t){}return t}(e);return Th(i)?Qh("Non-Error promise rejection captured with value: "+String(i),{handled:!1,synthetic:!1,overrideExceptionType:"UnhandledRejection"}):ic({event:i},{handled:!1,overrideExceptionType:"UnhandledRejection",defaultExceptionMessage:String(i)})}var nc=Ar("[ExceptionAutocapture]"),oc={wrapOnError:t=>{var e=Bi;e||nc.info("window not available, cannot wrap onerror");var i=e.onerror;return e.onerror=function(){for(var e,r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];var s=ic({event:n[0],error:n[4]});return t(s),null!==(e=null==i?void 0:i(...n))&&void 0!==e&&e},e.onerror.__POSTHOG_INSTRUMENTED__=!0,()=>{var t;null==(t=e.onerror)||delete t.__POSTHOG_INSTRUMENTED__,e.onerror=i}},wrapUnhandledRejection:t=>{var e=Bi;e||nc.info("window not available, cannot wrap onUnhandledRejection");var i=e.onunhandledrejection;return e.onunhandledrejection=function(){for(var r,n=arguments.length,o=new Array(n),s=0;s<n;s++)o[s]=arguments[s];var a=rc(o);return t(a),null!==(r=null==i?void 0:i.apply(e,o))&&void 0!==r&&r},e.onunhandledrejection.__POSTHOG_INSTRUMENTED__=!0,()=>{var t;null==(t=e.onunhandledrejection)||delete t.__POSTHOG_INSTRUMENTED__,e.onunhandledrejection=i}},wrapConsoleError:t=>{var e=console;e||nc.info("console not available, cannot wrap console.error");var i=e.error;return e.error=function(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];var o=r.join(" "),s=r.find((t=>t instanceof Error)),a=s?ic({event:o,error:s}):ic({event:o},{syntheticException:new Error("PostHog syntheticException")});return t(a),null==i?void 0:i(...r)},e.error.__POSTHOG_INSTRUMENTED__=!0,()=>{var t;null==(t=e.error)||delete t.__POSTHOG_INSTRUMENTED__,e.error=i}}};Xi.__PosthogExtensions__=Xi.__PosthogExtensions__||{},Xi.__PosthogExtensions__.errorWrappingFunctions=oc,Xi.posthogErrorWrappingFunctions=oc;var sc=(t,e,i)=>{if(e){var{sessionId:r,windowId:n}=e.checkAndGetSessionAndWindowId(!0);i.headers.set("X-POSTHOG-SESSION-ID",r),i.headers.set("X-POSTHOG-WINDOW-ID",n)}i.headers.set("X-POSTHOG-DISTINCT-ID",t)};Xi.__PosthogExtensions__=Xi.__PosthogExtensions__||{};var ac={_patchFetch:(t,i)=>Wr(Bi,"fetch",(r=>function(){var n=e((function*(e,n){var o=new Request(e,n);return sc(t,i,o),r(o)}));return function(t,e){return n.apply(this,arguments)}}())),_patchXHR:(t,e)=>Wr(Bi.XMLHttpRequest.prototype,"open",(i=>function(r,n,o,s,a){void 0===o&&(o=!0);var l=new Request(n);return sc(t,e,l),i.call(this,r,l.url,o,s,a)}))};Xi.__PosthogExtensions__.tracingHeadersPatchFns=ac,Xi.postHogTracingHeadersPatchFns=ac;var lc,uc,hc=function(){var t=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(t&&t.responseStart>0&&t.responseStart<performance.now())return t},cc=function(t){if("loading"===document.readyState)return"loading";var e=hc();if(e){if(t<e.domInteractive)return"loading";if(0===e.domContentLoadedEventStart||t<e.domContentLoadedEventStart)return"dom-interactive";if(0===e.domComplete||t<e.domComplete)return"dom-content-loaded"}return"complete"},dc=function(t){var e=t.nodeName;return 1===t.nodeType?e.toLowerCase():e.toUpperCase().replace(/^#/,"")},vc=function(t,e){var i="";try{for(;t&&9!==t.nodeType;){var r=t,n=r.id?"#"+r.id:dc(r)+(r.classList&&r.classList.value&&r.classList.value.trim()&&r.classList.value.trim().length?"."+r.classList.value.trim().replace(/\s+/g,"."):"");if(i.length+n.length>(e||100)-1)return i||n;if(i=i?n+">"+i:n,r.id)break;t=r.parentNode}}catch(t){}return i},fc=-1,pc=function(){return fc},gc=function(t){addEventListener("pageshow",(function(e){e.persisted&&(fc=e.timeStamp,t(e))}),!0)},mc=function(){var t=hc();return t&&t.activationStart||0},yc=function(t,e){var i=hc(),r="navigate";return pc()>=0?r="back-forward-cache":i&&(document.prerendering||mc()>0?r="prerender":document.wasDiscarded?r="restore":i.type&&(r=i.type.replace(/_/g,"-"))),{name:t,value:void 0===e?-1:e,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:r}},bc=function(t,e,i){try{if(PerformanceObserver.supportedEntryTypes.includes(t)){var r=new PerformanceObserver((function(t){Promise.resolve().then((function(){e(t.getEntries())}))}));return r.observe(Object.assign({type:t,buffered:!0},i||{})),r}}catch(t){}},_c=function(t,e,i,r){var n,o;return function(s){e.value>=0&&(s||r)&&((o=e.value-(n||0))||void 0===n)&&(n=e.value,e.delta=o,e.rating=function(t,e){return t>e[1]?"poor":t>e[0]?"needs-improvement":"good"}(e.value,i),t(e))}},wc=function(t){requestAnimationFrame((function(){return requestAnimationFrame((function(){return t()}))}))},Ic=function(t){document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&t()}))},Cc=function(t){var e=!1;return function(){e||(t(),e=!0)}},Sc=-1,kc=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},xc=function(t){"hidden"===document.visibilityState&&Sc>-1&&(Sc="visibilitychange"===t.type?t.timeStamp:0,Tc())},Ac=function(){addEventListener("visibilitychange",xc,!0),addEventListener("prerenderingchange",xc,!0)},Tc=function(){removeEventListener("visibilitychange",xc,!0),removeEventListener("prerenderingchange",xc,!0)},Mc=function(){return Sc<0&&(Sc=kc(),Ac(),gc((function(){setTimeout((function(){Sc=kc(),Ac()}),0)}))),{get firstHiddenTime(){return Sc}}},Rc=function(t){document.prerendering?addEventListener("prerenderingchange",(function(){return t()}),!0):t()},Nc=[1800,3e3],Ec=function(t,e){e=e||{},Rc((function(){var i,r=Mc(),n=yc("FCP"),o=bc("paint",(function(t){t.forEach((function(t){"first-contentful-paint"===t.name&&(o.disconnect(),t.startTime<r.firstHiddenTime&&(n.value=Math.max(t.startTime-mc(),0),n.entries.push(t),i(!0)))}))}));o&&(i=_c(t,n,Nc,e.reportAllChanges),gc((function(r){n=yc("FCP"),i=_c(t,n,Nc,e.reportAllChanges),wc((function(){n.value=performance.now()-r.timeStamp,i(!0)}))})))}))},Fc=[.1,.25],Oc=0,Pc=1/0,Dc=0,Lc=function(t){t.forEach((function(t){t.interactionId&&(Pc=Math.min(Pc,t.interactionId),Dc=Math.max(Dc,t.interactionId),Oc=Dc?(Dc-Pc)/7+1:0)}))},Bc=function(){return lc?Oc:performance.interactionCount||0},$c=function(){"interactionCount"in performance||lc||(lc=bc("event",Lc,{type:"event",buffered:!0,durationThreshold:0}))},Zc=[],qc=new Map,Hc=0,Vc=[],jc=function(t){if(Vc.forEach((function(e){return e(t)})),t.interactionId||"first-input"===t.entryType){var e=Zc[Zc.length-1],i=qc.get(t.interactionId);if(i||Zc.length<10||t.duration>e.latency){if(i)t.duration>i.latency?(i.entries=[t],i.latency=t.duration):t.duration===i.latency&&t.startTime===i.entries[0].startTime&&i.entries.push(t);else{var r={id:t.interactionId,latency:t.duration,entries:[t]};qc.set(r.id,r),Zc.push(r)}Zc.sort((function(t,e){return e.latency-t.latency})),Zc.length>10&&Zc.splice(10).forEach((function(t){return qc.delete(t.id)}))}}},Yc=function(t){var e=self.requestIdleCallback||self.setTimeout,i=-1;return t=Cc(t),"hidden"===document.visibilityState?t():(i=e(t),Ic(t)),i},zc=[200,500],Gc=function(t,e){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(e=e||{},Rc((function(){var i;$c();var r,n=yc("INP"),o=function(t){Yc((function(){t.forEach(jc);var e=function(){var t=Math.min(Zc.length-1,Math.floor((Bc()-Hc)/50));return Zc[t]}();e&&e.latency!==n.value&&(n.value=e.latency,n.entries=e.entries,r())}))},s=bc("event",o,{durationThreshold:null!==(i=e.durationThreshold)&&void 0!==i?i:40});r=_c(t,n,zc,e.reportAllChanges),s&&(s.observe({type:"first-input",buffered:!0}),Ic((function(){o(s.takeRecords()),r(!0)})),gc((function(){Hc=Bc(),Zc.length=0,qc.clear(),n=yc("INP"),r=_c(t,n,zc,e.reportAllChanges)})))})))},Wc=[],Uc=[],Xc=0,Jc=new WeakMap,Kc=new Map,Qc=-1,td=function(t){Wc=Wc.concat(t),ed()},ed=function(){Qc<0&&(Qc=Yc(id))},id=function(){Kc.size>10&&Kc.forEach((function(t,e){qc.has(e)||Kc.delete(e)}));var t=Zc.map((function(t){return Jc.get(t.entries[0])})),e=Uc.length-50;Uc=Uc.filter((function(i,r){return r>=e||t.includes(i)}));for(var i=new Set,r=0;r<Uc.length;r++){var n=Uc[r];rd(n.startTime,n.processingEnd).forEach((function(t){i.add(t)}))}var o=Wc.length-1-50;Wc=Wc.filter((function(t,e){return t.startTime>Xc&&e>o||i.has(t)})),Qc=-1};Vc.push((function(t){t.interactionId&&t.target&&!Kc.has(t.interactionId)&&Kc.set(t.interactionId,t.target)}),(function(t){var e,i=t.startTime+t.duration;Xc=Math.max(Xc,t.processingEnd);for(var r=Uc.length-1;r>=0;r--){var n=Uc[r];if(Math.abs(i-n.renderTime)<=8){(e=n).startTime=Math.min(t.startTime,e.startTime),e.processingStart=Math.min(t.processingStart,e.processingStart),e.processingEnd=Math.max(t.processingEnd,e.processingEnd),e.entries.push(t);break}}e||(e={startTime:t.startTime,processingStart:t.processingStart,processingEnd:t.processingEnd,renderTime:i,entries:[t]},Uc.push(e)),(t.interactionId||"first-input"===t.entryType)&&Jc.set(t,e),ed()}));var rd=function(t,e){for(var i,r=[],n=0;i=Wc[n];n++)if(!(i.startTime+i.duration<t)){if(i.startTime>e)break;r.push(i)}return r},nd=[2500,4e3],od={},sd={onLCP:function(t,e){!function(t,e){e=e||{},Rc((function(){var i,r=Mc(),n=yc("LCP"),o=function(t){e.reportAllChanges||(t=t.slice(-1)),t.forEach((function(t){t.startTime<r.firstHiddenTime&&(n.value=Math.max(t.startTime-mc(),0),n.entries=[t],i())}))},s=bc("largest-contentful-paint",o);if(s){i=_c(t,n,nd,e.reportAllChanges);var a=Cc((function(){od[n.id]||(o(s.takeRecords()),s.disconnect(),od[n.id]=!0,i(!0))}));["keydown","click"].forEach((function(t){addEventListener(t,(function(){return Yc(a)}),{once:!0,capture:!0})})),Ic(a),gc((function(r){n=yc("LCP"),i=_c(t,n,nd,e.reportAllChanges),wc((function(){n.value=performance.now()-r.timeStamp,od[n.id]=!0,i(!0)}))}))}}))}((function(e){var i=function(t){var e={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:t.value};if(t.entries.length){var i=hc();if(i){var r=i.activationStart||0,n=t.entries[t.entries.length-1],o=n.url&&performance.getEntriesByType("resource").filter((function(t){return t.name===n.url}))[0],s=Math.max(0,i.responseStart-r),a=Math.max(s,o?(o.requestStart||o.startTime)-r:0),l=Math.max(a,o?o.responseEnd-r:0),u=Math.max(l,n.startTime-r);e={element:vc(n.element),timeToFirstByte:s,resourceLoadDelay:a-s,resourceLoadDuration:l-a,elementRenderDelay:u-l,navigationEntry:i,lcpEntry:n},n.url&&(e.url=n.url),o&&(e.lcpResourceEntry=o)}}return Object.assign(t,{attribution:e})}(e);t(i)}),e)},onCLS:function(t,e){!function(t,e){e=e||{},Ec(Cc((function(){var i,r=yc("CLS",0),n=0,o=[],s=function(t){t.forEach((function(t){if(!t.hadRecentInput){var e=o[0],i=o[o.length-1];n&&t.startTime-i.startTime<1e3&&t.startTime-e.startTime<5e3?(n+=t.value,o.push(t)):(n=t.value,o=[t])}})),n>r.value&&(r.value=n,r.entries=o,i())},a=bc("layout-shift",s);a&&(i=_c(t,r,Fc,e.reportAllChanges),Ic((function(){s(a.takeRecords()),i(!0)})),gc((function(){n=0,r=yc("CLS",0),i=_c(t,r,Fc,e.reportAllChanges),wc((function(){return i()}))})),setTimeout(i,0))})))}((function(e){var i=function(t){var e,i={};if(t.entries.length){var r=t.entries.reduce((function(t,e){return t&&t.value>e.value?t:e}));if(r&&r.sources&&r.sources.length){var n=(e=r.sources).find((function(t){return t.node&&1===t.node.nodeType}))||e[0];n&&(i={largestShiftTarget:vc(n.node),largestShiftTime:r.startTime,largestShiftValue:r.value,largestShiftSource:n,largestShiftEntry:r,loadState:cc(r.startTime)})}}return Object.assign(t,{attribution:i})}(e);t(i)}),e)},onFCP:function(t,e){Ec((function(e){var i=function(t){var e={timeToFirstByte:0,firstByteToFCP:t.value,loadState:cc(pc())};if(t.entries.length){var i=hc(),r=t.entries[t.entries.length-1];if(i){var n=i.activationStart||0,o=Math.max(0,i.responseStart-n);e={timeToFirstByte:o,firstByteToFCP:t.value-o,loadState:cc(t.entries[0].startTime),navigationEntry:i,fcpEntry:r}}}return Object.assign(t,{attribution:e})}(e);t(i)}),e)},onINP:function(t,e){uc||(uc=bc("long-animation-frame",td)),Gc((function(e){var i=function(t){var e=t.entries[0],i=Jc.get(e),r=e.processingStart,n=i.processingEnd,o=i.entries.sort((function(t,e){return t.processingStart-e.processingStart})),s=rd(e.startTime,n),a=t.entries.find((function(t){return t.target})),l=a&&a.target||Kc.get(e.interactionId),u=[e.startTime+e.duration,n].concat(s.map((function(t){return t.startTime+t.duration}))),h=Math.max.apply(Math,u),c={interactionTarget:vc(l),interactionTargetElement:l,interactionType:e.name.startsWith("key")?"keyboard":"pointer",interactionTime:e.startTime,nextPaintTime:h,processedEventEntries:o,longAnimationFrameEntries:s,inputDelay:r-e.startTime,processingDuration:n-r,presentationDelay:Math.max(h-n,0),loadState:cc(e.startTime)};return Object.assign(t,{attribution:c})}(e);t(i)}),e)}};Xi.__PosthogExtensions__=Xi.__PosthogExtensions__||{},Xi.__PosthogExtensions__.postHogWebVitalsCallbacks=sd,Xi.postHogWebVitalsCallbacks=sd;class ad{constructor(){this.clicks=[]}isRageClick(t,e,i){var r=this.clicks[this.clicks.length-1];if(r&&Math.abs(t-r.x)+Math.abs(e-r.y)<30&&i-r.timestamp<1e3){if(this.clicks.push({x:t,y:e,timestamp:i}),3===this.clicks.length)return!0}else this.clicks=[{x:t,y:e,timestamp:i}];return!1}}var ld=Ar("[AutoCapture]");function ud(t,e){return e.length>t?e.slice(0,t)+"...":e}function hd(t){if(t.previousElementSibling)return t.previousElementSibling;var e=t;do{e=e.previousSibling}while(e&&!Bn(e));return e}function cd(t,e,i,r){var n=t.tagName.toLowerCase(),o={tag_name:n};Wn.indexOf(n)>-1&&!i&&("a"===n.toLowerCase()||"button"===n.toLowerCase()?o.$el_text=ud(1024,so(t)):o.$el_text=ud(1024,zn(t)));var s=jn(t);s.length>0&&(o.classes=s.filter((function(t){return""!==t}))),Rr(t.attributes,(function(i){var n;if((!Kn(t)||-1!==["name","id","class","aria-label"].indexOf(i.name))&&((null==r||!r.includes(i.name))&&!e&&oo(i.value)&&(n=i.name,!pr(n)||"_ngcontent"!==n.substring(0,10)&&"_nghost"!==n.substring(0,7)))){var s=i.value;"class"===i.name&&(s=Hn(s).join(" ")),o["attr__"+i.name]=ud(1024,s)}}));for(var a=1,l=1,u=t;u=hd(u);)a++,u.tagName===t.tagName&&l++;return o.nth_child=a,o.nth_of_type=l,o}function dd(t,e){for(var i,r,{e:n,maskAllElementAttributes:o,maskAllText:s,elementAttributeIgnoreList:a,elementsChainAsString:l}=e,u=[t],h=t;h.parentNode&&!$n(h,"body");)qn(h.parentNode)?(u.push(h.parentNode.host),h=h.parentNode.host):(u.push(h.parentNode),h=h.parentNode);var c,d=[],v={},f=!1,p=!1;if(Rr(u,(t=>{var e=Jn(t);"a"===t.tagName.toLowerCase()&&(f=t.getAttribute("href"),f=e&&f&&oo(f)&&f),er(jn(t),"ph-no-capture")&&(p=!0),d.push(cd(t,o,s,a));var i=function(t){if(!Jn(t))return{};var e={};return Rr(t.attributes,(function(t){if(t.name&&0===t.name.indexOf("data-ph-capture-attribute")){var i=t.name.replace("data-ph-capture-attribute-",""),r=t.value;i&&r&&oo(r)&&(e[i]=r)}})),e}(t);Nr(v,i)})),p)return{props:{},explicitNoCapture:p};if(s||("a"===t.tagName.toLowerCase()||"button"===t.tagName.toLowerCase()?d[0].$el_text=so(t):d[0].$el_text=zn(t)),f){var g,m;d[0].attr__href=f;var y=null==(g=Vr(f))?void 0:g.host,b=null==Bi||null==(m=Bi.location)?void 0:m.host;y&&b&&y!==b&&(c=f)}return{props:Nr({$event_type:n.type,$ce_version:1},l?{}:{$elements:d},{$elements_chain:lo(d)},null!=(i=d[0])&&i.$el_text?{$el_text:null==(r=d[0])?void 0:r.$el_text}:{},c&&"click"===n.type?{$external_click_url:c}:{},v)}}class vd{constructor(t){this.Y=!1,this.G=null,this.rageclicks=new ad,this.W=!1,this.instance=t,this.U=null}get X(){var t,e,i=dr(this.instance.config.autocapture)?this.instance.config.autocapture:{};return i.url_allowlist=null==(t=i.url_allowlist)?void 0:t.map((t=>new RegExp(t))),i.url_ignorelist=null==(e=i.url_ignorelist)?void 0:e.map((t=>new RegExp(t))),i}J(){if(this.isBrowserSupported()){if(Bi&&ji){var t=t=>{t=t||(null==Bi?void 0:Bi.event);try{this.K(t)}catch(t){ld.error("Failed to capture event",t)}};if(qr(ji,"submit",t,{capture:!0}),qr(ji,"change",t,{capture:!0}),qr(ji,"click",t,{capture:!0}),this.X.capture_copied_text){var e=t=>{t=t||(null==Bi?void 0:Bi.event),this.K(t,Ji)};qr(ji,"copy",e,{capture:!0}),qr(ji,"cut",e,{capture:!0})}}}else ld.info("Disabling Automatic Event Collection because this browser is not supported")}startIfEnabled(){this.isEnabled&&!this.Y&&(this.J(),this.Y=!0)}onRemoteConfig(t){t.elementsChainAsString&&(this.W=t.elementsChainAsString),this.instance.persistence&&this.instance.persistence.register({[Qr]:!!t.autocapture_opt_out}),this.G=!!t.autocapture_opt_out,this.startIfEnabled()}setElementSelectors(t){this.U=t}getElementSelectors(t){var e,i=[];return null==(e=this.U)||e.forEach((e=>{var r=null==ji?void 0:ji.querySelectorAll(e);null==r||r.forEach((r=>{t===r&&i.push(e)}))})),i}get isEnabled(){var t,e,i=null==(t=this.instance.persistence)?void 0:t.props[Qr],r=this.G;if(mr(r)&&!_r(i)&&!this.instance.tt())return!1;var n=null!==(e=this.G)&&void 0!==e?e:!!i;return!!this.instance.config.autocapture&&!n}K(t,e){if(void 0===e&&(e="$autocapture"),this.isEnabled){var i,r=Gn(t);if(Zn(r)&&(r=r.parentNode||null),"$autocapture"===e&&"click"===t.type&&t instanceof MouseEvent)this.instance.config.rageclick&&null!=(i=this.rageclicks)&&i.isRageClick(t.clientX,t.clientY,(new Date).getTime())&&this.K(t,"$rageclick");var n=e===Ji;if(r&&Xn(r,t,this.X,n,n?["copy","cut"]:void 0)){var{props:o,explicitNoCapture:s}=dd(r,{e:t,maskAllElementAttributes:this.instance.config.mask_all_element_attributes,maskAllText:this.instance.config.mask_all_text,elementAttributeIgnoreList:this.X.element_attribute_ignorelist,elementsChainAsString:this.W});if(s)return!1;var a=this.getElementSelectors(r);if(a&&a.length>0&&(o.$element_selectors=a),e===Ji){var l,u=Yn(null==Bi||null==(l=Bi.getSelection())?void 0:l.toString()),h=t.type||"clipboard";if(!u)return!1;o.$selected_content=u,o.$copy_type=h}return this.instance.capture(e,o),!0}}}isBrowserSupported(){return ur(null==ji?void 0:ji.querySelectorAll)}}var fd={};function pd(t){return function(t,e){var i=fd[t];if(i)return i;var r=e[t];if(hr(r)&&!cr())return fd[t]=r.bind(e);var n=e.document;if(n&&ur(n.createElement))try{var o=n.createElement("iframe");o.hidden=!0,n.head.appendChild(o);var s=o.contentWindow;s&&s[t]&&(r=s[t]),n.head.removeChild(o)}catch(e){xr.warn("Could not create sandbox iframe for "+t+" check, bailing to assignableWindow."+t+": ",e)}return r&&ur(r)?fd[t]=r.bind(e):r}("MutationObserver",t)}function gd(t,e){return br(t)&&t>=e}class md{et(t){var e,i,r,n,o=this.it((null==t?void 0:t.__onCapture)||this.rt.bind(this));return{element_attribute_ignorelist:null!==(e=null==t?void 0:t.element_attribute_ignorelist)&&void 0!==e?e:o.element_attribute_ignorelist,scroll_threshold_ms:null!==(i=null==t?void 0:t.scroll_threshold_ms)&&void 0!==i?i:o.scroll_threshold_ms,selection_change_threshold_ms:null!==(r=null==t?void 0:t.selection_change_threshold_ms)&&void 0!==r?r:o.selection_change_threshold_ms,mutation_threshold_ms:null!==(n=null==t?void 0:t.mutation_threshold_ms)&&void 0!==n?n:o.mutation_threshold_ms,__onCapture:o.__onCapture}}constructor(t,e){this.nt=[],this.it=t=>({element_attribute_ignorelist:[],scroll_threshold_ms:100,selection_change_threshold_ms:100,mutation_threshold_ms:2500,__onCapture:t}),this.ot=t=>{var e=function(t){var e=Gn(t);return e?{node:e,originalEvent:t,timestamp:Date.now()}:null}(t);mr(e)||this.st(e)||this.nt.push(e),this.nt.length&&fr(this.lt)&&(this.lt=Xi.setTimeout((()=>{this.ut()}),1e3))},this.ht=()=>{var t=Date.now();t%50==0&&this.nt.forEach((e=>{fr(e.scrollDelayMs)&&(e.scrollDelayMs=t-e.timestamp)}))},this.ct=()=>{this.dt=Date.now()},this.instance=t,this.X=this.et(e),this._onCapture=this.X.__onCapture}start(t){this.vt(),this.ft(),this.gt(),this.yt(t)}yt(t){if(!this.bt){var e=pd(Xi);this.bt=new e((t=>{this._t(t)})),this.bt.observe(t,{attributes:!0,characterData:!0,childList:!0,subtree:!0})}}stop(){var t;null==(t=this.bt)||t.disconnect(),this.bt=void 0,Xi.removeEventListener("click",this.ot),Xi.removeEventListener("scroll",this.ht,{capture:!0}),Xi.removeEventListener("selectionchange",this.ct)}_t(t){this.wt=Date.now()}vt(){qr(Xi,"click",this.ot)}ft(){qr(Xi,"scroll",this.ht,{capture:!0})}gt(){qr(Xi,"selectionchange",this.ct)}st(t){if(!t)return!0;if(Ln(t.node))return!0;var e=this.nt.some((e=>e.node===t.node&&Math.abs(e.timestamp-t.timestamp)<1e3));return!!e||!(!$n(t.node,"html")&&Bn(t.node)&&!Wn.includes(t.node.tagName.toLowerCase()))}ut(){if(this.nt.length){clearTimeout(this.lt),this.lt=void 0;var t=this.nt;for(var e of(this.nt=[],t)){var i;e.mutationDelayMs=null!==(i=e.mutationDelayMs)&&void 0!==i?i:this.wt&&e.timestamp<=this.wt?this.wt-e.timestamp:void 0,e.absoluteDelayMs=Date.now()-e.timestamp,e.selectionChangedDelayMs=this.dt&&e.timestamp<=this.dt?this.dt-e.timestamp:void 0;var r=gd(e.scrollDelayMs,this.X.scroll_threshold_ms),n=gd(e.selectionChangedDelayMs,this.X.selection_change_threshold_ms),o=gd(e.mutationDelayMs,this.X.mutation_threshold_ms),s=gd(e.absoluteDelayMs,1.1*this.X.mutation_threshold_ms),a=br(e.scrollDelayMs)&&e.scrollDelayMs<this.X.scroll_threshold_ms,l=br(e.mutationDelayMs)&&e.mutationDelayMs<this.X.mutation_threshold_ms,u=br(e.selectionChangedDelayMs)&&e.selectionChangedDelayMs<this.X.selection_change_threshold_ms;a||l||u||(r||o||s||n?this._onCapture(e,{$dead_click_last_mutation_timestamp:this.wt,$dead_click_event_timestamp:e.timestamp,$dead_click_scroll_timeout:r,$dead_click_mutation_timeout:o,$dead_click_absolute_timeout:s,$dead_click_selection_changed_timeout:n}):e.absoluteDelayMs<this.X.mutation_threshold_ms&&this.nt.push(e))}this.nt.length&&fr(this.lt)&&(this.lt=Xi.setTimeout((()=>{this.ut()}),1e3))}}rt(t,e){this.instance.capture("$dead_click",i({},e,dd(t.node,{e:t.originalEvent,maskAllElementAttributes:this.instance.config.mask_all_element_attributes,maskAllText:this.instance.config.mask_all_text,elementAttributeIgnoreList:this.X.element_attribute_ignorelist,elementsChainAsString:!1}).props,{$dead_click_scroll_delay_ms:t.scrollDelayMs,$dead_click_mutation_delay_ms:t.mutationDelayMs,$dead_click_absolute_delay_ms:t.absoluteDelayMs,$dead_click_selection_changed_delay_ms:t.selectionChangedDelayMs}),{timestamp:new Date(t.timestamp)})}}Xi.__PosthogExtensions__=Xi.__PosthogExtensions__||{},Xi.__PosthogExtensions__.initDeadClicksAutocapture=(t,e)=>new md(t,e);var yd=Ar("[ExternalScriptsLoader]"),bd=(t,e,i)=>{if(t.config.disable_external_dependency_loading)return yd.warn(e+" was requested but loading of external scripts is disabled."),i("Loading of external scripts is disabled");var r=null==ji?void 0:ji.querySelectorAll("script");if(r)for(var n=0;n<r.length;n++)if(r[n].src===e)return i();var o=()=>{if(!ji)return i("document not found");var r=ji.createElement("script");if(r.type="text/javascript",r.crossOrigin="anonymous",r.src=e,r.onload=t=>i(void 0,t),r.onerror=t=>i(t),t.config.prepare_external_dependency_script&&(r=t.config.prepare_external_dependency_script(r)),!r)return i("prepare_external_dependency_script returned null");var n,o=ji.querySelectorAll("body > script");o.length>0?null==(n=o[0].parentNode)||n.insertBefore(r,o[0]):ji.body.appendChild(r)};null!=ji&&ji.body?o():null==ji||ji.addEventListener("DOMContentLoaded",o)};Xi.__PosthogExtensions__=Xi.__PosthogExtensions__||{},Xi.__PosthogExtensions__.loadExternalDependency=(t,e,i)=>{var r="/static/"+e+".js?v="+t.version;if("remote-config"===e&&(r="/array/"+t.config.token+"/config.js"),"toolbar"===e){var n=3e5;r=r+"&t="+Math.floor(Date.now()/n)*n}var o=t.requestRouter.endpointFor("assets",r);bd(t,o,i)},Xi.__PosthogExtensions__.loadSiteApp=(t,e,i)=>{var r=t.requestRouter.endpointFor("api",e);bd(t,r,i)};var _d="";var wd=/[a-z0-9][a-z0-9-]+\.[a-z]{2,}$/i;function Id(t,e){if(e){var i=function(t,e){if(void 0===e&&(e=ji),_d)return _d;if(!e)return"";if(["localhost","127.0.0.1"].includes(t))return"";for(var i=t.split("."),r=Math.min(i.length,8),n="dmn_chk_"+fa();!_d&&r--;){var o=i.slice(r).join("."),s=n+"=1;domain=."+o+";path=/";e.cookie=s+";max-age=3",e.cookie.includes(n)&&(e.cookie=s+";max-age=0",_d=o)}return _d}(t);if(!i){var r=(t=>{var e=t.match(wd);return e?e[0]:""})(t);r!==i&&xr.info("Warning: cookie subdomain discovery mismatch",r,i),i=r}return i?"; domain=."+i:""}return""}var Cd={It:()=>!!ji,Ct:function(t){xr.error("cookieStore error: "+t)},St:function(t){if(ji){try{for(var e=t+"=",i=ji.cookie.split(";").filter((t=>t.length)),r=0;r<i.length;r++){for(var n=i[r];" "==n.charAt(0);)n=n.substring(1,n.length);if(0===n.indexOf(e))return decodeURIComponent(n.substring(e.length,n.length))}}catch(t){}return null}},kt:function(t){var e;try{e=JSON.parse(Cd.St(t))||{}}catch(t){}return e},xt:function(t,e,i,r,n){if(ji)try{var o="",s="",a=Id(ji.location.hostname,r);if(i){var l=new Date;l.setTime(l.getTime()+24*i*60*60*1e3),o="; expires="+l.toUTCString()}n&&(s="; secure");var u=t+"="+encodeURIComponent(JSON.stringify(e))+o+"; SameSite=Lax; path=/"+a+s;return u.length>3686.4&&xr.warn("cookieStore warning: large cookie, len="+u.length),ji.cookie=u,u}catch(t){return}},At:function(t,e){try{Cd.xt(t,"",-1,e)}catch(t){return}}},Sd=null,kd={It:function(){if(!mr(Sd))return Sd;var t=!0;if(fr(Bi))t=!1;else try{var e="__mplssupport__";kd.xt(e,"xyz"),'"xyz"'!==kd.St(e)&&(t=!1),kd.At(e)}catch(e){t=!1}return t||xr.error("localStorage unsupported; falling back to cookie store"),Sd=t,t},Ct:function(t){xr.error("localStorage error: "+t)},St:function(t){try{return null==Bi?void 0:Bi.localStorage.getItem(t)}catch(t){kd.Ct(t)}return null},kt:function(t){try{return JSON.parse(kd.St(t))||{}}catch(t){}return null},xt:function(t,e){try{null==Bi||Bi.localStorage.setItem(t,JSON.stringify(e))}catch(t){kd.Ct(t)}},At:function(t){try{null==Bi||Bi.localStorage.removeItem(t)}catch(t){kd.Ct(t)}}},xd=["distinct_id",pn,gn,Fn,En],Ad=i({},kd,{kt:function(t){try{var e={};try{e=Cd.kt(t)||{}}catch(t){}var i=Nr(e,JSON.parse(kd.St(t)||"{}"));return kd.xt(t,i),i}catch(t){}return null},xt:function(t,e,i,r,n,o){try{kd.xt(t,e,void 0,void 0,o);var s={};xd.forEach((t=>{e[t]&&(s[t]=e[t])})),Object.keys(s).length&&Cd.xt(t,s,i,r,n,o)}catch(t){kd.Ct(t)}},At:function(t,e){try{null==Bi||Bi.localStorage.removeItem(t),Cd.At(t,e)}catch(t){kd.Ct(t)}}}),Td={},Md={It:function(){return!0},Ct:function(t){xr.error("memoryStorage error: "+t)},St:function(t){return Td[t]||null},kt:function(t){return Td[t]||null},xt:function(t,e){Td[t]=e},At:function(t){delete Td[t]}},Rd=null,Nd={It:function(){if(!mr(Rd))return Rd;if(Rd=!0,fr(Bi))Rd=!1;else try{var t="__support__";Nd.xt(t,"xyz"),'"xyz"'!==Nd.St(t)&&(Rd=!1),Nd.At(t)}catch(t){Rd=!1}return Rd},Ct:function(t){xr.error("sessionStorage error: ",t)},St:function(t){try{return null==Bi?void 0:Bi.sessionStorage.getItem(t)}catch(t){Nd.Ct(t)}return null},kt:function(t){try{return JSON.parse(Nd.St(t))||null}catch(t){}return null},xt:function(t,e){try{null==Bi||Bi.sessionStorage.setItem(t,JSON.stringify(e))}catch(t){Nd.Ct(t)}},At:function(t){try{null==Bi||Bi.sessionStorage.removeItem(t)}catch(t){Nd.Ct(t)}}},Ed=function(t){return t[t.PENDING=-1]="PENDING",t[t.DENIED=0]="DENIED",t[t.GRANTED=1]="GRANTED",t}({});class Fd{constructor(t){this._instance=t}get X(){return this._instance.config}get consent(){return this.Tt()?Ed.DENIED:this.Mt}isOptedOut(){return this.consent===Ed.DENIED||this.consent===Ed.PENDING&&this.X.opt_out_capturing_by_default}isOptedIn(){return!this.isOptedOut()}optInOut(t){this.Rt.xt(this.Nt,t?1:0,this.X.cookie_expiration,this.X.cross_subdomain_cookie,this.X.secure_cookie)}reset(){this.Rt.At(this.Nt,this.X.cross_subdomain_cookie)}get Nt(){var{token:t,opt_out_capturing_cookie_prefix:e}=this._instance.config;return(e||"__ph_opt_in_out_")+t}get Mt(){var t=this.Rt.St(this.Nt);return"1"===t?Ed.GRANTED:"0"===t?Ed.DENIED:Ed.PENDING}get Rt(){if(!this.Et){var t=this.X.opt_out_capturing_persistence_type;this.Et="localStorage"===t?kd:Cd;var e="localStorage"===t?Cd:kd;e.St(this.Nt)&&(this.Et.St(this.Nt)||this.optInOut("1"===e.St(this.Nt)),e.At(this.Nt,this.X.cross_subdomain_cookie))}return this.Et}Tt(){return!!this.X.respect_dnt&&!!Zr([null==Vi?void 0:Vi.doNotTrack,null==Vi?void 0:Vi.msDoNotTrack,Xi.doNotTrack],(t=>er([!0,1,"1","yes"],t)))}}var Od=Ar("[Dead Clicks]"),Pd=()=>!0,Dd=t=>{var e,i=!(null==(e=t.instance.persistence)||!e.get_property(on)),r=t.instance.config.capture_dead_clicks;return _r(r)?r:i};class Ld{get lazyLoadedDeadClicksAutocapture(){return this.Ft}constructor(t,e,i){this.instance=t,this.isEnabled=e,this.onCapture=i,this.startIfEnabled()}onRemoteConfig(t){this.instance.persistence&&this.instance.persistence.register({[on]:null==t?void 0:t.captureDeadClicks}),this.startIfEnabled()}startIfEnabled(){this.isEnabled(this)&&this.Ot((()=>{this.Pt()}))}Ot(t){var e,i;null!=(e=Xi.__PosthogExtensions__)&&e.initDeadClicksAutocapture&&t(),null==(i=Xi.__PosthogExtensions__)||null==i.loadExternalDependency||i.loadExternalDependency(this.instance,"dead-clicks-autocapture",(e=>{e?Od.error("failed to load script",e):t()}))}Pt(){var t;if(ji){if(!this.Ft&&null!=(t=Xi.__PosthogExtensions__)&&t.initDeadClicksAutocapture){var e=dr(this.instance.config.capture_dead_clicks)?this.instance.config.capture_dead_clicks:{};e.__onCapture=this.onCapture,this.Ft=Xi.__PosthogExtensions__.initDeadClicksAutocapture(this.instance,e),this.Ft.start(ji),Od.info("starting...")}}else Od.error("`document` not found. Cannot start.")}stop(){this.Ft&&(this.Ft.stop(),this.Ft=void 0,Od.info("stopping..."))}}function Bd(t,e,i,r,n){return e>i&&(xr.warn("min cannot be greater than max."),e=i),br(t)?t>i?(r&&xr.warn(r+" cannot be  greater than max: "+i+". Using max value instead."),i):t<e?(r&&xr.warn(r+" cannot be less than min: "+e+". Using min value instead."),e):t:(r&&xr.warn(r+" must be a number. using max or fallback. max: "+i+", fallback: "+n),Bd(n||i,e,i,r))}class $d{constructor(t){this.Dt={},this.Lt=()=>{Object.keys(this.Dt).forEach((t=>{var e=this.Bt(t)+this.$t;e>=this.Zt?delete this.Dt[t]:this.qt(t,e)}))},this.Bt=t=>this.Dt[String(t)],this.qt=(t,e)=>{this.Dt[String(t)]=e},this.consumeRateLimit=t=>{var e,i=null!==(e=this.Bt(t))&&void 0!==e?e:this.Zt;if(0===(i=Math.max(i-1,0)))return!0;this.qt(t,i);var r,n=0===i;n&&(null==(r=this.Ht)||r.call(this,t));return n},this.Vt=t,this.Ht=this.Vt.Ht,this.Zt=Bd(this.Vt.bucketSize,0,100,"rate limiter bucket size"),this.$t=Bd(this.Vt.refillRate,0,this.Zt,"rate limiter refill rate"),this.jt=Bd(this.Vt.refillInterval,0,864e5,"rate limiter refill interval"),setInterval((()=>{this.Lt()}),this.jt)}}var Zd=Ar("[ExceptionAutocapture]");class qd{constructor(t){var e,i,r;this.Yt=()=>{var t;if(Bi&&this.isEnabled&&null!=(t=Xi.__PosthogExtensions__)&&t.errorWrappingFunctions){var e=Xi.__PosthogExtensions__.errorWrappingFunctions.wrapOnError,i=Xi.__PosthogExtensions__.errorWrappingFunctions.wrapUnhandledRejection,r=Xi.__PosthogExtensions__.errorWrappingFunctions.wrapConsoleError;try{!this.zt&&this.X.capture_unhandled_errors&&(this.zt=e(this.captureException.bind(this))),!this.Gt&&this.X.capture_unhandled_rejections&&(this.Gt=i(this.captureException.bind(this))),!this.Wt&&this.X.capture_console_errors&&(this.Wt=r(this.captureException.bind(this)))}catch(t){Zd.error("failed to start",t),this.Ut()}}},this._instance=t,this.Xt=!(null==(e=this._instance.persistence)||!e.props[en]),this.X=this.Jt(),this.Kt=new $d({refillRate:null!==(i=this._instance.config.error_tracking.__exceptionRateLimiterRefillRate)&&void 0!==i?i:1,bucketSize:null!==(r=this._instance.config.error_tracking.__exceptionRateLimiterBucketSize)&&void 0!==r?r:10,refillInterval:1e4}),this.startIfEnabled()}Jt(){var t=this._instance.config.capture_exceptions,e={capture_unhandled_errors:!1,capture_unhandled_rejections:!1,capture_console_errors:!1};return dr(t)?e=i({},e,t):(fr(t)?this.Xt:t)&&(e=i({},e,{capture_unhandled_errors:!0,capture_unhandled_rejections:!0})),e}get isEnabled(){return this.X.capture_console_errors||this.X.capture_unhandled_errors||this.X.capture_unhandled_rejections}startIfEnabled(){this.isEnabled&&(Zd.info("enabled"),this.Ot(this.Yt))}Ot(t){var e,i;null!=(e=Xi.__PosthogExtensions__)&&e.errorWrappingFunctions&&t(),null==(i=Xi.__PosthogExtensions__)||null==i.loadExternalDependency||i.loadExternalDependency(this._instance,"exception-autocapture",(e=>{if(e)return Zd.error("failed to load script",e);t()}))}Ut(){var t,e,i;null==(t=this.zt)||t.call(this),this.zt=void 0,null==(e=this.Gt)||e.call(this),this.Gt=void 0,null==(i=this.Wt)||i.call(this),this.Wt=void 0}onRemoteConfig(t){var e=t.autocaptureExceptions;this.Xt=!!e||!1,this.X=this.Jt(),this._instance.persistence&&this._instance.persistence.register({[en]:this.Xt}),this.startIfEnabled()}captureException(t){var e,i=this._instance.requestRouter.endpointFor("ui");t.$exception_personURL=i+"/project/"+this._instance.config.token+"/person/"+this._instance.get_distinct_id();var r=null!==(e=t.$exception_list[0].type)&&void 0!==e?e:"Exception";this.Kt.consumeRateLimit(r)?Zd.info("Skipping exception capture because of client rate limiting.",{exception:t.$exception_list[0].type}):this._instance.exceptions.sendExceptionEvent(t)}}class Hd{constructor(t){var e;this._instance=t,this.Qt=(null==Bi||null==(e=Bi.location)?void 0:e.pathname)||""}get isEnabled(){return"history_change"===this._instance.config.capture_pageview}startIfEnabled(){this.isEnabled&&(xr.info("History API monitoring enabled, starting..."),this.monitorHistoryChanges())}stop(){this.te&&this.te(),this.te=void 0,xr.info("History API monitoring stopped")}monitorHistoryChanges(){var t,e;if(Bi&&Bi.history){var i=this;null!=(t=Bi.history.pushState)&&t.__posthog_wrapped__||Wr(Bi.history,"pushState",(t=>function(e,r,n){t.call(this,e,r,n),i.ee("pushState")})),null!=(e=Bi.history.replaceState)&&e.__posthog_wrapped__||Wr(Bi.history,"replaceState",(t=>function(e,r,n){t.call(this,e,r,n),i.ee("replaceState")})),this.ie()}}ee(t){try{var e,i=null==Bi||null==(e=Bi.location)?void 0:e.pathname;if(!i)return;i!==this.Qt&&this.isEnabled&&this._instance.capture("$pageview",{navigation_type:t}),this.Qt=i}catch(e){xr.error("Error capturing "+t+" pageview",e)}}ie(){if(!this.te){var t=()=>{this.ee("popstate")};qr(Bi,"popstate",t),this.te=()=>{Bi&&Bi.removeEventListener("popstate",t)}}}}function Vd(t){var e,i;return(null==(e=JSON.stringify(t,(i=[],function(t,e){if(dr(e)){for(;i.length>0&&i[i.length-1]!==this;)i.pop();return i.includes(e)?"[Circular]":(i.push(e),e)}return e})))?void 0:e.length)||0}function jd(t,e){if(void 0===e&&(e=6606028.8),t.size>=e&&t.data.length>1){var i=Math.floor(t.data.length/2),r=t.data.slice(0,i),n=t.data.slice(i);return[jd({size:Vd(r),data:r,sessionId:t.sessionId,windowId:t.windowId}),jd({size:Vd(n),data:n,sessionId:t.sessionId,windowId:t.windowId})].flatMap((t=>t))}return[t]}var Yd=(t=>(t[t.DomContentLoaded=0]="DomContentLoaded",t[t.Load=1]="Load",t[t.FullSnapshot=2]="FullSnapshot",t[t.IncrementalSnapshot=3]="IncrementalSnapshot",t[t.Meta=4]="Meta",t[t.Custom=5]="Custom",t[t.Plugin=6]="Plugin",t))(Yd||{}),zd=(t=>(t[t.Mutation=0]="Mutation",t[t.MouseMove=1]="MouseMove",t[t.MouseInteraction=2]="MouseInteraction",t[t.Scroll=3]="Scroll",t[t.ViewportResize=4]="ViewportResize",t[t.Input=5]="Input",t[t.TouchMove=6]="TouchMove",t[t.MediaInteraction=7]="MediaInteraction",t[t.StyleSheetRule=8]="StyleSheetRule",t[t.CanvasMutation=9]="CanvasMutation",t[t.Font=10]="Font",t[t.Log=11]="Log",t[t.Drag=12]="Drag",t[t.StyleDeclaration=13]="StyleDeclaration",t[t.Selection=14]="Selection",t[t.AdoptedStyleSheet=15]="AdoptedStyleSheet",t[t.CustomElement=16]="CustomElement",t))(zd||{});class Gd{constructor(t,e){var i,r;void 0===e&&(e={}),this.re={},this.ne=t=>{if(!this.re[t]){var e,i;this.re[t]=!0;var r=this.oe(t);null==(e=(i=this.Vt).onBlockedNode)||e.call(i,t,r)}},this.se=t=>{var e=this.oe(t);if("svg"!==(null==e?void 0:e.nodeName)&&e instanceof Element){var i=e.closest("svg");if(i)return[this._rrweb.mirror.getId(i),i]}return[t,e]},this.oe=t=>this._rrweb.mirror.getNode(t),this.ae=t=>{var e,i,r,n,o,s,a,l;return(null!==(e=null==(i=t.removes)?void 0:i.length)&&void 0!==e?e:0)+(null!==(r=null==(n=t.attributes)?void 0:n.length)&&void 0!==r?r:0)+(null!==(o=null==(s=t.texts)?void 0:s.length)&&void 0!==o?o:0)+(null!==(a=null==(l=t.adds)?void 0:l.length)&&void 0!==a?a:0)},this.throttleMutations=t=>{if(3!==t.type||0!==t.data.source)return t;var e=t.data,i=this.ae(e);e.attributes&&(e.attributes=e.attributes.filter((t=>{var[e]=this.se(t.id);return!this.Kt.consumeRateLimit(e)&&t})));var r=this.ae(e);return 0!==r||i===r?t:void 0},this._rrweb=t,this.Vt=e,this.Kt=new $d({bucketSize:null!==(i=this.Vt.bucketSize)&&void 0!==i?i:100,refillRate:null!==(r=this.Vt.refillRate)&&void 0!==r?r:10,refillInterval:1e3,Ht:this.ne})}}function Wd(t,e){return function(t){for(var e=0,i=0;i<t.length;i++)e=(e<<5)-e+t.charCodeAt(i),e|=0;return Math.abs(e)}(t)%100<Bd(100*e,0,100)}var Ud="disabled",Xd="sampled",Jd="active",Kd="buffering",Qd="paused",tv="trigger",ev=tv+"_activated",iv=tv+"_pending",rv=tv+"_"+Ud;function nv(t,e){return e.some((e=>"regex"===e.matching&&new RegExp(e.url).test(t)))}class ov{constructor(t){this.le=t}triggerStatus(t){var e=this.le.map((e=>e.triggerStatus(t)));return e.includes(ev)?ev:e.includes(iv)?iv:rv}stop(){this.le.forEach((t=>t.stop()))}}class sv{constructor(t){this.le=t}triggerStatus(t){var e=new Set;for(var i of this.le)e.add(i.triggerStatus(t));switch(e.delete(rv),e.size){case 0:return rv;case 1:return Array.from(e)[0];default:return iv}}stop(){this.le.forEach((t=>t.stop()))}}class av{triggerStatus(){return iv}stop(){}}class lv{constructor(t){this.ue=[],this.he=[],this.urlBlocked=!1,this._instance=t}onRemoteConfig(t){var e,i;this.ue=(null==(e=t.sessionRecording)?void 0:e.urlTriggers)||[],this.he=(null==(i=t.sessionRecording)?void 0:i.urlBlocklist)||[]}ce(t){var e;return 0===this.ue.length?rv:(null==(e=this._instance)?void 0:e.get_property(mn))===t?ev:iv}triggerStatus(t){var e=this.ce(t),i=e===ev?ev:e===iv?iv:rv;return this._instance.register_for_session({$sdk_debug_replay_url_trigger_status:i}),i}checkUrlTriggerConditions(t,e,i){if(void 0!==Bi&&Bi.location.href){var r=Bi.location.href,n=this.urlBlocked,o=nv(r,this.he);n&&o||(o&&!n?t():!o&&n&&e(),nv(r,this.ue)&&i("url"))}}stop(){}}class uv{constructor(t){this.linkedFlag=null,this.linkedFlagSeen=!1,this.de=()=>{},this._instance=t}triggerStatus(){var t=iv;return yr(this.linkedFlag)&&(t=rv),this.linkedFlagSeen&&(t=ev),this._instance.register_for_session({$sdk_debug_replay_linked_flag_trigger_status:t}),t}onRemoteConfig(t,e){var i;if(this.linkedFlag=(null==(i=t.sessionRecording)?void 0:i.linkedFlag)||null,!yr(this.linkedFlag)&&!this.linkedFlagSeen){var r=pr(this.linkedFlag)?this.linkedFlag:this.linkedFlag.flag,n=pr(this.linkedFlag)?null:this.linkedFlag.variant;this.de=this._instance.onFeatureFlags(((t,i)=>{var o=!1;if(dr(i)&&r in i){var s=i[r];o=_r(s)?!0===s:n?s===n:!!s}this.linkedFlagSeen=o,o&&e(r,n)}))}}stop(){this.de()}}class hv{constructor(t){this.ve=[],this._instance=t}onRemoteConfig(t){var e;this.ve=(null==(e=t.sessionRecording)?void 0:e.eventTriggers)||[]}fe(t){var e;return 0===this.ve.length?rv:(null==(e=this._instance)?void 0:e.get_property(yn))===t?ev:iv}triggerStatus(t){var e=this.fe(t),i=e===ev?ev:e===iv?iv:rv;return this._instance.register_for_session({$sdk_debug_replay_event_trigger_status:i}),i}stop(){}}function cv(t){return t.isRecordingEnabled?Kd:Ud}function dv(t){if(!t.receivedFlags)return Kd;if(!t.isRecordingEnabled)return Ud;if(t.urlTriggerMatching.urlBlocked)return Qd;var e=!0===t.isSampled,i=new ov([t.eventTriggerMatching,t.urlTriggerMatching,t.linkedFlagMatching]).triggerStatus(t.sessionId);return e?Xd:i===ev?Jd:i===iv?Kd:!1===t.isSampled?Ud:Jd}function vv(t){if(!t.receivedFlags)return Kd;if(!t.isRecordingEnabled)return Ud;if(t.urlTriggerMatching.urlBlocked)return Qd;var e=new sv([t.eventTriggerMatching,t.urlTriggerMatching,t.linkedFlagMatching]).triggerStatus(t.sessionId),i=e!==rv,r=_r(t.isSampled);return i&&e===iv?Kd:i&&e===rv||r&&!t.isSampled?Ud:!0===t.isSampled?Xd:Jd}var fv="[SessionRecording]",pv=Ar(fv);function gv(){var t;return null==Xi||null==(t=Xi.__PosthogExtensions__)||null==(t=t.rrweb)?void 0:t.record}var mv=3e5,yv=[zd.MouseMove,zd.MouseInteraction,zd.Scroll,zd.ViewportResize,zd.Input,zd.TouchMove,zd.MediaInteraction,zd.Drag],bv=t=>({rrwebMethod:t,enqueuedAt:Date.now(),attempt:1});function _v(t){return function(t,e){for(var i="",r=0;r<t.length;){var n=t[r++];n<128||e?i+=String.fromCharCode(n):n<224?i+=String.fromCharCode((31&n)<<6|63&t[r++]):n<240?i+=String.fromCharCode((15&n)<<12|(63&t[r++])<<6|63&t[r++]):(n=((15&n)<<18|(63&t[r++])<<12|(63&t[r++])<<6|63&t[r++])-65536,i+=String.fromCharCode(55296|n>>10,56320|1023&n))}return i}(Ul(Xl(JSON.stringify(t))),!0)}function wv(t){return t.type===Yd.Custom&&"sessionIdle"===t.data.tag}class Iv{get sessionId(){return this.pe}get ge(){return this._instance.config.session_recording.session_idle_threshold_ms||3e5}get started(){return this.me}get ye(){if(!this._instance.sessionManager)throw new Error(fv+" must be started with a valid sessionManager.");return this._instance.sessionManager}get be(){var t,e;return this._e.triggerStatus(this.sessionId)===iv?6e4:null!==(t=null==(e=this._instance.config.session_recording)?void 0:e.full_snapshot_interval_millis)&&void 0!==t?t:mv}get we(){var t=this._instance.get_property(gn);return _r(t)?t:null}get Ie(){var t,e,i=null==(t=this.S)?void 0:t.data[(null==(e=this.S)?void 0:e.data.length)-1],{sessionStartTimestamp:r}=this.ye.checkAndGetSessionAndWindowId(!0);return i?i.timestamp-r:null}get Ce(){var t=!!this._instance.get_property(an),e=!this._instance.config.disable_session_recording;return Bi&&t&&e}get Se(){var t=!!this._instance.get_property(ln),e=this._instance.config.enable_recording_console_log;return null!=e?e:t}get ke(){var t,e,i,r,n,o,s=this._instance.config.session_recording.captureCanvas,a=this._instance.get_property(cn),l=null!==(t=null!==(e=null==s?void 0:s.recordCanvas)&&void 0!==e?e:null==a?void 0:a.enabled)&&void 0!==t&&t,u=null!==(i=null!==(r=null==s?void 0:s.canvasFps)&&void 0!==r?r:null==a?void 0:a.fps)&&void 0!==i?i:4,h=null!==(n=null!==(o=null==s?void 0:s.canvasQuality)&&void 0!==o?o:null==a?void 0:a.quality)&&void 0!==n?n:.4;if("string"==typeof h){var c=parseFloat(h);h=isNaN(c)?.4:c}return{enabled:l,fps:Bd(u,0,12,"canvas recording fps",4),quality:Bd(h,0,1,"canvas recording quality",.4)}}get xe(){var t,e,i=this._instance.get_property(un),r={recordHeaders:null==(t=this._instance.config.session_recording)?void 0:t.recordHeaders,recordBody:null==(e=this._instance.config.session_recording)?void 0:e.recordBody},n=(null==r?void 0:r.recordHeaders)||(null==i?void 0:i.recordHeaders),o=(null==r?void 0:r.recordBody)||(null==i?void 0:i.recordBody),s=dr(this._instance.config.capture_performance)?this._instance.config.capture_performance.network_timing:this._instance.config.capture_performance,a=!!(_r(s)?s:null==i?void 0:i.capturePerformance);return n||o||a?{recordHeaders:n,recordBody:o,recordPerformance:a}:void 0}get Ae(){var t,e,i,r,n,o,s=this._instance.get_property(hn),a={maskAllInputs:null==(t=this._instance.config.session_recording)?void 0:t.maskAllInputs,maskTextSelector:null==(e=this._instance.config.session_recording)?void 0:e.maskTextSelector,blockSelector:null==(i=this._instance.config.session_recording)?void 0:i.blockSelector},l=null!==(r=null==a?void 0:a.maskAllInputs)&&void 0!==r?r:null==s?void 0:s.maskAllInputs,u=null!==(n=null==a?void 0:a.maskTextSelector)&&void 0!==n?n:null==s?void 0:s.maskTextSelector,h=null!==(o=null==a?void 0:a.blockSelector)&&void 0!==o?o:null==s?void 0:s.blockSelector;return fr(l)&&fr(u)&&fr(h)?void 0:{maskAllInputs:null==l||l,maskTextSelector:u,blockSelector:h}}get Te(){var t=this._instance.get_property(dn);return br(t)?t:null}get Me(){var t=this._instance.get_property(vn);return br(t)?t:null}get status(){return this.Re?this.Ne({receivedFlags:this.Re,isRecordingEnabled:this.Ce,isSampled:this.we,urlTriggerMatching:this.Ee,eventTriggerMatching:this.Fe,linkedFlagMatching:this.Oe,sessionId:this.sessionId}):Kd}constructor(t){if(this.Ne=cv,this.Re=!1,this.Pe=[],this.De="unknown",this.Le=Date.now(),this._e=new av,this.Be=void 0,this.$e=void 0,this.Ze=void 0,this.qe=void 0,this.He=void 0,this._forceAllowLocalhostNetworkCapture=!1,this.Ve=()=>{this.je()},this.Ye=()=>{this.ze("browser offline",{})},this.Ge=()=>{this.ze("browser online",{})},this.We=()=>{if(null!=ji&&ji.visibilityState){var t="window "+ji.visibilityState;this.ze(t,{})}},this._instance=t,this.me=!1,this.Ue="/s/",this.Xe=void 0,this.Re=!1,!this._instance.sessionManager)throw pv.error("started without valid sessionManager"),new Error(fv+" started without valid sessionManager. This is a bug.");if(this._instance.config.__preview_experimental_cookieless_mode)throw new Error(fv+" cannot be used with __preview_experimental_cookieless_mode.");this.Oe=new uv(this._instance),this.Ee=new lv(this._instance),this.Fe=new hv(this._instance);var{sessionId:e,windowId:i}=this.ye.checkAndGetSessionAndWindowId();this.pe=e,this.Je=i,this.S=this.Ke(),this.ge>=this.ye.sessionTimeoutMs&&pv.warn("session_idle_threshold_ms ("+this.ge+") is greater than the session timeout ("+this.ye.sessionTimeoutMs+"). Session will never be detected as idle")}startIfEnabledOrStop(t){this.Ce?(this.Qe(t),qr(Bi,"beforeunload",this.Ve),qr(Bi,"offline",this.Ye),qr(Bi,"online",this.Ge),qr(Bi,"visibilitychange",this.We),this.ti(),this.ei(),yr(this.Be)&&(this.Be=this._instance.on("eventCaptured",(t=>{try{if("$pageview"===t.event){var e=null!=t&&t.properties.$current_url?this.ii(null==t?void 0:t.properties.$current_url):"";if(!e)return;this.ze("$pageview",{href:e})}}catch(t){pv.error("Could not add $pageview to rrweb session",t)}}))),this.$e||(this.$e=this.ye.onSessionId(((t,e,i)=>{var r,n;i&&(this.ze("$session_id_change",{sessionId:t,windowId:e,changeReason:i}),null==(r=this._instance)||null==(r=r.persistence)||r.unregister(yn),null==(n=this._instance)||null==(n=n.persistence)||n.unregister(mn))})))):this.stopRecording()}stopRecording(){var t,e,i,r;this.me&&this.Xe&&(this.Xe(),this.Xe=void 0,this.me=!1,null==Bi||Bi.removeEventListener("beforeunload",this.Ve),null==Bi||Bi.removeEventListener("offline",this.Ye),null==Bi||Bi.removeEventListener("online",this.Ge),null==Bi||Bi.removeEventListener("visibilitychange",this.We),this.Ke(),clearInterval(this.ri),null==(t=this.Be)||t.call(this),this.Be=void 0,null==(e=this.He)||e.call(this),this.He=void 0,null==(i=this.$e)||i.call(this),this.$e=void 0,null==(r=this.qe)||r.call(this),this.qe=void 0,this.Fe.stop(),this.Ee.stop(),this.Oe.stop(),pv.info("stopped"))}ni(){var t;null==(t=this._instance.persistence)||t.unregister(gn)}oi(t){var e,i=this.pe!==t,r=this.Te;if(br(r)){var n=this.we,o=i||!_r(n),s=o?Wd(t,r):n;o&&(s?this.si(Xd):pv.warn("Sample rate ("+r+") has determined that this sessionId ("+t+") will not be sent to the server."),this.ze("samplingDecisionMade",{sampleRate:r,isSampled:s})),null==(e=this._instance.persistence)||e.register({[gn]:s})}else this.ni()}onRemoteConfig(t){var e,i,r,n;(this.ze("$remote_config_received",t),this.ai(t),null!=(e=t.sessionRecording)&&e.endpoint)&&(this.Ue=null==(n=t.sessionRecording)?void 0:n.endpoint);this.ti(),"any"===(null==(i=t.sessionRecording)?void 0:i.triggerMatchType)?(this.Ne=dv,this._e=new ov([this.Fe,this.Ee])):(this.Ne=vv,this._e=new sv([this.Fe,this.Ee])),this._instance.register_for_session({$sdk_debug_replay_remote_trigger_matching_config:null==(r=t.sessionRecording)?void 0:r.triggerMatchType}),this.Ee.onRemoteConfig(t),this.Fe.onRemoteConfig(t),this.Oe.onRemoteConfig(t,((t,e)=>{this.si("linked_flag_matched",{flag:t,variant:e})})),this.Re=!0,this.startIfEnabledOrStop()}ti(){br(this.Te)&&yr(this.qe)&&(this.qe=this.ye.onSessionId((t=>{this.oi(t)})))}ai(t){if(this._instance.persistence){var e,r=this._instance.persistence,n=()=>{var e,n,o,s,a,l,u,h,c,d=null==(e=t.sessionRecording)?void 0:e.sampleRate,v=yr(d)?null:parseFloat(d);yr(v)&&this.ni();var f=null==(n=t.sessionRecording)?void 0:n.minimumDurationMilliseconds;r.register({[an]:!!t.sessionRecording,[ln]:null==(o=t.sessionRecording)?void 0:o.consoleLogRecordingEnabled,[un]:i({capturePerformance:t.capturePerformance},null==(s=t.sessionRecording)?void 0:s.networkPayloadCapture),[hn]:null==(a=t.sessionRecording)?void 0:a.masking,[cn]:{enabled:null==(l=t.sessionRecording)?void 0:l.recordCanvas,fps:null==(u=t.sessionRecording)?void 0:u.canvasFps,quality:null==(h=t.sessionRecording)?void 0:h.canvasQuality},[dn]:v,[vn]:fr(f)?null:f,[fn]:null==(c=t.sessionRecording)?void 0:c.scriptConfig})};n(),null==(e=this.Ze)||e.call(this),this.Ze=this.ye.onSessionId(n)}}log(t,e){var i;void 0===e&&(e="log"),null==(i=this._instance.sessionRecording)||i.onRRwebEmit({type:6,data:{plugin:"rrweb/console@1",payload:{level:e,trace:[],payload:[JSON.stringify(t)]}},timestamp:Date.now()})}Qe(t){if(!fr(Object.assign)&&!fr(Array.from)&&!(this.me||this._instance.config.disable_session_recording||this._instance.consent.isOptedOut())){var e;if(this.me=!0,this.ye.checkAndGetSessionAndWindowId(),gv())this.li();else null==(e=Xi.__PosthogExtensions__)||null==e.loadExternalDependency||e.loadExternalDependency(this._instance,this.ui,(t=>{if(t)return pv.error("could not load recorder",t);this.li()}));pv.info("starting"),this.status===Jd&&this.si(t||"recording_initialized")}}get ui(){var t;return(null==(t=this._instance)||null==(t=t.persistence)||null==(t=t.get_property(fn))?void 0:t.script)||"recorder"}hi(t){var e;return 3===t.type&&-1!==yv.indexOf(null==(e=t.data)?void 0:e.source)}ci(t){var e=this.hi(t);e||this.De||t.timestamp-this.Le>this.ge&&(this.De=!0,clearInterval(this.ri),this.ze("sessionIdle",{eventTimestamp:t.timestamp,lastActivityTimestamp:this.Le,threshold:this.ge,bufferLength:this.S.data.length,bufferSize:this.S.size}),this.je());var i=!1;if(e&&(this.Le=t.timestamp,this.De)){var r="unknown"===this.De;this.De=!1,r||(this.ze("sessionNoLongerIdle",{reason:"user activity",type:t.type}),i=!0)}if(!this.De){var{windowId:n,sessionId:o}=this.ye.checkAndGetSessionAndWindowId(!e,t.timestamp),s=this.pe!==o,a=this.Je!==n;this.Je=n,this.pe=o,s||a?(this.stopRecording(),this.startIfEnabledOrStop("session_id_changed")):i&&this.di()}}vi(t){try{return t.rrwebMethod(),!0}catch(e){return this.Pe.length<10?this.Pe.push({enqueuedAt:t.enqueuedAt||Date.now(),attempt:t.attempt++,rrwebMethod:t.rrwebMethod}):pv.warn("could not emit queued rrweb event.",e,t),!1}}ze(t,e){return this.vi(bv((()=>gv().addCustomEvent(t,e))))}fi(){return this.vi(bv((()=>gv().takeFullSnapshot())))}li(){var t,e,r,n,o={blockClass:"ph-no-capture",blockSelector:void 0,ignoreClass:"ph-ignore-input",maskTextClass:"ph-mask",maskTextSelector:void 0,maskTextFn:void 0,maskAllInputs:!0,maskInputOptions:{password:!0},maskInputFn:void 0,slimDOMOptions:{},collectFonts:!1,inlineStylesheet:!0,recordCrossOriginIframes:!1},s=this._instance.config.session_recording;for(var[a,l]of Object.entries(s||{}))a in o&&("maskInputOptions"===a?o.maskInputOptions=i({password:!0},l):o[a]=l);(this.ke&&this.ke.enabled&&(o.recordCanvas=!0,o.sampling={canvas:this.ke.fps},o.dataURLOptions={type:"image/webp",quality:this.ke.quality}),this.Ae)&&(o.maskAllInputs=null===(e=this.Ae.maskAllInputs)||void 0===e||e,o.maskTextSelector=null!==(r=this.Ae.maskTextSelector)&&void 0!==r?r:void 0,o.blockSelector=null!==(n=this.Ae.blockSelector)&&void 0!==n?n:void 0);var u=gv();if(u){this.pi=null!==(t=this.pi)&&void 0!==t?t:new Gd(u,{refillRate:this._instance.config.session_recording.__mutationThrottlerRefillRate,bucketSize:this._instance.config.session_recording.__mutationThrottlerBucketSize,onBlockedNode:(t,e)=>{var i="Too many mutations on node '"+t+"'. Rate limiting. This could be due to SVG animations or something similar";pv.info(i,{node:e}),this.log(fv+" "+i,"warn")}});var h=this.gi();this.Xe=u(i({emit:t=>{this.onRRwebEmit(t)},plugins:h},o)),this.Le=Date.now(),this.De=_r(this.De)?this.De:"unknown",this.ze("$session_options",{sessionRecordingOptions:o,activePlugins:h.map((t=>null==t?void 0:t.name))}),this.ze("$posthog_config",{config:this._instance.config})}else pv.error("onScriptLoaded was called but rrwebRecord is not available. This indicates something has gone wrong.")}di(){if(this.ri&&clearInterval(this.ri),!0!==this.De){var t=this.be;t&&(this.ri=setInterval((()=>{this.fi()}),t))}}gi(){var t,e,i=[],r=null==(t=Xi.__PosthogExtensions__)||null==(t=t.rrwebPlugins)?void 0:t.getRecordConsolePlugin;r&&this.Se&&i.push(r());var n=null==(e=Xi.__PosthogExtensions__)||null==(e=e.rrwebPlugins)?void 0:e.getRecordNetworkPlugin;this.xe&&ur(n)&&(!Hr.includes(location.hostname)||this._forceAllowLocalhostNetworkCapture?i.push(n(_o(this._instance.config,this.xe))):pv.info("NetworkCapture not started because we are on localhost."));return i}onRRwebEmit(t){var e;if(this.mi(),t&&dr(t)){if(t.type===Yd.Meta){var r=this.ii(t.data.href);if(this.yi=r,!r)return;t.data.href=r}else this.bi();if(this.Ee.checkUrlTriggerConditions((()=>this.wi()),(()=>this.Ii()),(t=>this.Ci(t))),!this.Ee.urlBlocked||function(t){return t.type===Yd.Custom&&"recording paused"===t.data.tag}(t)){t.type===Yd.FullSnapshot&&this.di(),t.type===Yd.FullSnapshot&&this.Re&&this._e.triggerStatus(this.sessionId)===iv&&this.Ke();var n=this.pi?this.pi.throttleMutations(t):t;if(n){var o=function(t){var e=t;if(e&&dr(e)&&6===e.type&&dr(e.data)&&"rrweb/console@1"===e.data.plugin){e.data.payload.payload.length>10&&(e.data.payload.payload=e.data.payload.payload.slice(0,10),e.data.payload.payload.push("...[truncated]"));for(var i=[],r=0;r<e.data.payload.payload.length;r++)e.data.payload.payload[r]&&e.data.payload.payload[r].length>2e3?i.push(e.data.payload.payload[r].slice(0,2e3)+"...[truncated]"):i.push(e.data.payload.payload[r]);return e.data.payload.payload=i,t}return t}(n);if(this.ci(o),!0!==this.De||wv(o)){if(wv(o)){var s=o.data.payload;if(s){var a=s.lastActivityTimestamp,l=s.threshold;o.timestamp=a+l}}var u=null===(e=this._instance.config.session_recording.compress_events)||void 0===e||e?function(t){if(Vd(t)<1024)return t;try{if(t.type===Yd.FullSnapshot)return i({},t,{data:_v(t.data),cv:"2024-10"});if(t.type===Yd.IncrementalSnapshot&&t.data.source===zd.Mutation)return i({},t,{cv:"2024-10",data:i({},t.data,{texts:_v(t.data.texts),attributes:_v(t.data.attributes),removes:_v(t.data.removes),adds:_v(t.data.adds)})});if(t.type===Yd.IncrementalSnapshot&&t.data.source===zd.StyleSheetRule)return i({},t,{cv:"2024-10",data:i({},t.data,{adds:t.data.adds?_v(t.data.adds):void 0,removes:t.data.removes?_v(t.data.removes):void 0})})}catch(t){pv.error("could not compress event - will use uncompressed event",t)}return t}(o):o,h={$snapshot_bytes:Vd(u),$snapshot_data:u,$session_id:this.pe,$window_id:this.Je};this.status!==Ud?this.Si(h):this.Ke()}}}}}bi(){if(!this._instance.config.capture_pageview&&Bi){var t=this.ii(Bi.location.href);this.yi!==t&&(this.ze("$url_changed",{href:t}),this.yi=t)}}mi(){if(this.Pe.length){var t=[...this.Pe];this.Pe=[],t.forEach((t=>{Date.now()-t.enqueuedAt<=2e3&&this.vi(t)}))}}ii(t){var e=this._instance.config.session_recording;if(e.maskNetworkRequestFn){var i,r={url:t};return null==(i=r=e.maskNetworkRequestFn(r))?void 0:i.url}return t}Ke(){return this.S={size:0,data:[],sessionId:this.pe,windowId:this.Je},this.S}je(){this.ki&&(clearTimeout(this.ki),this.ki=void 0);var t=this.Me,e=this.Ie,i=br(e)&&e>=0,r=br(t)&&i&&e<t;if(this.status===Kd||this.status===Qd||this.status===Ud||r)return this.ki=setTimeout((()=>{this.je()}),2e3),this.S;this.S.data.length>0&&jd(this.S).forEach((t=>{this.xi({$snapshot_bytes:t.size,$snapshot_data:t.data,$session_id:t.sessionId,$window_id:t.windowId,$lib:"web",$lib_version:Sr.LIB_VERSION})}));return this.Ke()}Si(t){var e,i=2+((null==(e=this.S)?void 0:e.data.length)||0);!this.De&&(this.S.size+t.$snapshot_bytes+i>943718.4||this.S.sessionId!==this.pe)&&(this.S=this.je()),this.S.size+=t.$snapshot_bytes,this.S.data.push(t.$snapshot_data),this.ki||this.De||(this.ki=setTimeout((()=>{this.je()}),2e3))}xi(t){this._instance.capture("$snapshot",t,{_url:this._instance.requestRouter.endpointFor("api",this.Ue),_noTruncate:!0,_batchKey:"recordings",skip_client_rate_limiting:!0})}Ci(t){var e;this._e.triggerStatus(this.sessionId)===iv&&(null==(e=this._instance)||null==(e=e.persistence)||e.register({["url"===t?mn:yn]:this.pe}),this.je(),this.si(t+"_trigger_matched"))}wi(){this.Ee.urlBlocked||(this.Ee.urlBlocked=!0,clearInterval(this.ri),pv.info("recording paused due to URL blocker"),this.ze("recording paused",{reason:"url blocker"}))}Ii(){this.Ee.urlBlocked&&(this.Ee.urlBlocked=!1,this.fi(),this.di(),this.ze("recording resumed",{reason:"left blocked url"}),pv.info("recording resumed"))}ei(){0!==this.Fe.ve.length&&yr(this.He)&&(this.He=this._instance.on("eventCaptured",(t=>{try{this.Fe.ve.includes(t.event)&&this.Ci("event")}catch(t){pv.error("Could not activate event trigger",t)}})))}overrideLinkedFlag(){this.Oe.linkedFlagSeen=!0,this.fi(),this.si("linked_flag_overridden")}overrideSampling(){var t;null==(t=this._instance.persistence)||t.register({[gn]:!0}),this.fi(),this.si("sampling_overridden")}overrideTrigger(t){this.Ci(t)}si(t,e){this._instance.register_for_session({$session_recording_start_reason:t}),pv.info(t.replace("_"," "),e),er(["recording_initialized","session_id_changed"],t)||this.ze(t,e)}get sdkDebugProperties(){var{sessionStartTimestamp:t}=this.ye.checkAndGetSessionAndWindowId(!0);return{$recording_status:this.status,$sdk_debug_replay_internal_buffer_length:this.S.data.length,$sdk_debug_replay_internal_buffer_size:this.S.size,$sdk_debug_current_session_duration:this.Ie,$sdk_debug_session_start:t}}}var Cv=Ar("[SegmentIntegration]");function Sv(t,e){var i=t.config.segment;if(!i)return e();!function(t,e){var i=t.config.segment;if(!i)return e();var r=i=>{var r=()=>i.anonymousId()||fa();t.config.get_device_id=r,i.id()&&(t.register({distinct_id:i.id(),$device_id:r()}),t.persistence.set_property(An,"identified")),e()},n=i.user();"then"in n&&ur(n.then)?n.then((t=>r(t))):r(n)}(t,(()=>{i.register((t=>{Promise&&Promise.resolve||Cv.warn("This browser does not have Promise support, and can not use the segment integration");var e=(e,i)=>{if(!i)return e;e.event.userId||e.event.anonymousId===t.get_distinct_id()||(Cv.info("No userId set, resetting PostHog"),t.reset()),e.event.userId&&e.event.userId!==t.get_distinct_id()&&(Cv.info("UserId set, identifying with PostHog"),t.identify(e.event.userId));var r=t.calculateEventProperties(i,e.event.properties);return e.event.properties=Object.assign({},r,e.event.properties),e};return{name:"PostHog JS",type:"enrichment",version:"1.0.0",isLoaded:()=>!0,load:()=>Promise.resolve(),track:t=>e(t,t.event.event),page:t=>e(t,"$pageview"),identify:t=>e(t,"$identify"),screen:t=>e(t,"$screen")}})(t)).then((()=>{e()}))}))}var kv="posthog-js";function xv(t,e){var{organization:r,projectId:n,prefix:o,severityAllowList:s=["error"]}=void 0===e?{}:e;return e=>{var a,l,u,h,c;if(!("*"===s||s.includes(e.level))||!t.__loaded)return e;e.tags||(e.tags={});var d=t.requestRouter.endpointFor("ui","/project/"+t.config.token+"/person/"+t.get_distinct_id());e.tags["PostHog Person URL"]=d,t.sessionRecordingStarted()&&(e.tags["PostHog Recording URL"]=t.get_session_replay_url({withTimestamp:!0}));var v=(null==(a=e.exception)?void 0:a.values)||[],f=v.map((t=>i({},t,{stacktrace:t.stacktrace?i({},t.stacktrace,{type:"raw",frames:(t.stacktrace.frames||[]).map((t=>i({},t,{platform:"web:javascript"})))}):void 0}))),p={$exception_message:(null==(l=v[0])?void 0:l.value)||e.message,$exception_type:null==(u=v[0])?void 0:u.type,$exception_personURL:d,$exception_level:e.level,$exception_list:f,$sentry_event_id:e.event_id,$sentry_exception:e.exception,$sentry_exception_message:(null==(h=v[0])?void 0:h.value)||e.message,$sentry_exception_type:null==(c=v[0])?void 0:c.type,$sentry_tags:e.tags};return r&&n&&(p.$sentry_url=(o||"https://sentry.io/organizations/")+r+"/issues/?project="+n+"&query="+e.event_id),t.exceptions.sendExceptionEvent(p),e}}class Av{constructor(t,e,i,r,n){this.name=kv,this.setupOnce=function(o){o(xv(t,{organization:e,projectId:i,prefix:r,severityAllowList:n}))}}}var Tv=null!=Bi&&Bi.location?Gr(Bi.location.hash,"__posthog")||Gr(location.hash,"state"):null,Mv="_postHogToolbarParams",Rv=Ar("[Toolbar]"),Nv=function(t){return t[t.UNINITIALIZED=0]="UNINITIALIZED",t[t.LOADING=1]="LOADING",t[t.LOADED=2]="LOADED",t}(Nv||{});class Ev{constructor(t){this.instance=t}Ai(t){Xi.ph_toolbar_state=t}Ti(){var t;return null!==(t=Xi.ph_toolbar_state)&&void 0!==t?t:Nv.UNINITIALIZED}maybeLoadToolbar(t,e,i){if(void 0===t&&(t=void 0),void 0===e&&(e=void 0),void 0===i&&(i=void 0),!Bi||!ji)return!1;t=null!=t?t:Bi.location,i=null!=i?i:Bi.history;try{if(!e){try{Bi.localStorage.setItem("test","test"),Bi.localStorage.removeItem("test")}catch(t){return!1}e=null==Bi?void 0:Bi.localStorage}var r,n=Tv||Gr(t.hash,"__posthog")||Gr(t.hash,"state"),o=n?Or((()=>JSON.parse(atob(decodeURIComponent(n)))))||Or((()=>JSON.parse(decodeURIComponent(n)))):null;return o&&"ph_authorize"===o.action?((r=o).source="url",r&&Object.keys(r).length>0&&(o.desiredHash?t.hash=o.desiredHash:i?i.replaceState(i.state,"",t.pathname+t.search):t.hash="")):((r=JSON.parse(e.getItem(Mv)||"{}")).source="localstorage",delete r.userIntent),!(!r.token||this.instance.config.token!==r.token)&&(this.loadToolbar(r),!0)}catch(t){return!1}}Mi(t){var e=Xi.ph_load_toolbar||Xi.ph_load_editor;!yr(e)&&ur(e)?e(t,this.instance):Rv.warn("No toolbar load function found")}loadToolbar(t){var e=!(null==ji||!ji.getElementById(On));if(!Bi||e)return!1;var r="custom"===this.instance.requestRouter.region&&this.instance.config.advanced_disable_toolbar_metrics,n=i({token:this.instance.config.token},t,{apiURL:this.instance.requestRouter.endpointFor("ui")},r?{instrument:!1}:{});if(Bi.localStorage.setItem(Mv,JSON.stringify(i({},n,{source:void 0}))),this.Ti()===Nv.LOADED)this.Mi(n);else if(this.Ti()===Nv.UNINITIALIZED){var o;this.Ai(Nv.LOADING),null==(o=Xi.__PosthogExtensions__)||null==o.loadExternalDependency||o.loadExternalDependency(this.instance,"toolbar",(t=>{if(t)return Rv.error("[Toolbar] Failed to load",t),void this.Ai(Nv.UNINITIALIZED);this.Ai(Nv.LOADED),this.Mi(n)})),qr(Bi,"turbolinks:load",(()=>{this.Ai(Nv.UNINITIALIZED),this.loadToolbar(n)}))}return!0}Ri(t){return this.loadToolbar(t)}maybeLoadEditor(t,e,i){return void 0===t&&(t=void 0),void 0===e&&(e=void 0),void 0===i&&(i=void 0),this.maybeLoadToolbar(t,e,i)}}var Fv=Ar("[TracingHeaders]");class Ov{constructor(t){this.Ni=void 0,this.Ei=void 0,this.Yt=()=>{var t,e;fr(this.Ni)&&(null==(t=Xi.__PosthogExtensions__)||null==(t=t.tracingHeadersPatchFns)||t._patchXHR(this._instance.get_distinct_id(),this._instance.sessionManager));fr(this.Ei)&&(null==(e=Xi.__PosthogExtensions__)||null==(e=e.tracingHeadersPatchFns)||e._patchFetch(this._instance.get_distinct_id(),this._instance.sessionManager))},this._instance=t}Ot(t){var e,i;null!=(e=Xi.__PosthogExtensions__)&&e.tracingHeadersPatchFns&&t(),null==(i=Xi.__PosthogExtensions__)||null==i.loadExternalDependency||i.loadExternalDependency(this._instance,"tracing-headers",(e=>{if(e)return Fv.error("failed to load script",e);t()}))}startIfEnabledOrStop(){var t,e;this._instance.config.__add_tracing_headers?this.Ot(this.Yt):(null==(t=this.Ni)||t.call(this),null==(e=this.Ei)||e.call(this),this.Ni=void 0,this.Ei=void 0)}}var Pv=Ar("[Web Vitals]"),Dv=9e5;class Lv{constructor(t){var e;this.Fi=!1,this.Y=!1,this.S={url:void 0,metrics:[],firstMetricTimestamp:void 0},this.Oi=()=>{clearTimeout(this.Pi),0!==this.S.metrics.length&&(this._instance.capture("$web_vitals",this.S.metrics.reduce(((t,e)=>i({},t,{["$web_vitals_"+e.name+"_event"]:i({},e),["$web_vitals_"+e.name+"_value"]:e.value})),{})),this.S={url:void 0,metrics:[],firstMetricTimestamp:void 0})},this.Di=t=>{var e,r=null==(e=this._instance.sessionManager)?void 0:e.checkAndGetSessionAndWindowId(!0);if(fr(r))Pv.error("Could not read session ID. Dropping metrics!");else{this.S=this.S||{url:void 0,metrics:[],firstMetricTimestamp:void 0};var n=this.Li();if(!fr(n))if(yr(null==t?void 0:t.name)||yr(null==t?void 0:t.value))Pv.error("Invalid metric received",t);else if(this.Bi&&t.value>=this.Bi)Pv.error("Ignoring metric with value >= "+this.Bi,t);else this.S.url!==n&&(this.Oi(),this.Pi=setTimeout(this.Oi,this.flushToCaptureTimeoutMs)),fr(this.S.url)&&(this.S.url=n),this.S.firstMetricTimestamp=fr(this.S.firstMetricTimestamp)?Date.now():this.S.firstMetricTimestamp,t.attribution&&t.attribution.interactionTargetElement&&(t.attribution.interactionTargetElement=void 0),this.S.metrics.push(i({},t,{$current_url:n,$session_id:r.sessionId,$window_id:r.windowId,timestamp:Date.now()})),this.S.metrics.length===this.allowedMetrics.length&&this.Oi()}},this.Yt=()=>{var t,e,i,r,n=Xi.__PosthogExtensions__;fr(n)||fr(n.postHogWebVitalsCallbacks)||({onLCP:t,onCLS:e,onFCP:i,onINP:r}=n.postHogWebVitalsCallbacks),t&&e&&i&&r?(this.allowedMetrics.indexOf("LCP")>-1&&t(this.Di.bind(this)),this.allowedMetrics.indexOf("CLS")>-1&&e(this.Di.bind(this)),this.allowedMetrics.indexOf("FCP")>-1&&i(this.Di.bind(this)),this.allowedMetrics.indexOf("INP")>-1&&r(this.Di.bind(this)),this.Y=!0):Pv.error("web vitals callbacks not loaded - not starting")},this._instance=t,this.Fi=!(null==(e=this._instance.persistence)||!e.props[nn]),this.startIfEnabled()}get allowedMetrics(){var t,e,i=dr(this._instance.config.capture_performance)?null==(t=this._instance.config.capture_performance)?void 0:t.web_vitals_allowed_metrics:void 0;return fr(i)?(null==(e=this._instance.persistence)?void 0:e.props[sn])||["CLS","FCP","INP","LCP"]:i}get flushToCaptureTimeoutMs(){return(dr(this._instance.config.capture_performance)?this._instance.config.capture_performance.web_vitals_delayed_flush_ms:void 0)||5e3}get Bi(){var t=dr(this._instance.config.capture_performance)&&br(this._instance.config.capture_performance.__web_vitals_max_value)?this._instance.config.capture_performance.__web_vitals_max_value:Dv;return 0<t&&t<=6e4?Dv:t}get isEnabled(){var t=null==Yi?void 0:Yi.protocol;if("http:"!==t&&"https:"!==t)return Pv.info("Web Vitals are disabled on non-http/https protocols"),!1;var e=dr(this._instance.config.capture_performance)?this._instance.config.capture_performance.web_vitals:_r(this._instance.config.capture_performance)?this._instance.config.capture_performance:void 0;return _r(e)?e:this.Fi}startIfEnabled(){this.isEnabled&&!this.Y&&(Pv.info("enabled, starting..."),this.Ot(this.Yt))}onRemoteConfig(t){var e=dr(t.capturePerformance)&&!!t.capturePerformance.web_vitals,i=dr(t.capturePerformance)?t.capturePerformance.web_vitals_allowed_metrics:void 0;this._instance.persistence&&(this._instance.persistence.register({[nn]:e}),this._instance.persistence.register({[sn]:i})),this.Fi=e,this.startIfEnabled()}Ot(t){var e,i;null!=(e=Xi.__PosthogExtensions__)&&e.postHogWebVitalsCallbacks&&t(),null==(i=Xi.__PosthogExtensions__)||null==i.loadExternalDependency||i.loadExternalDependency(this._instance,"web-vitals",(e=>{e?Pv.error("failed to load script",e):t()}))}Li(){var t=Bi?Bi.location.href:void 0;return t||Pv.error("Could not determine current URL"),t}}var Bv=Ar("[Heatmaps]");function $v(t){return dr(t)&&"clientX"in t&&"clientY"in t&&br(t.clientX)&&br(t.clientY)}class Zv{constructor(t){var e;this.rageclicks=new ad,this.Fi=!1,this.Y=!1,this.$i=null,this.instance=t,this.Fi=!(null==(e=this.instance.persistence)||!e.props[tn])}get flushIntervalMilliseconds(){var t=5e3;return dr(this.instance.config.capture_heatmaps)&&this.instance.config.capture_heatmaps.flush_interval_milliseconds&&(t=this.instance.config.capture_heatmaps.flush_interval_milliseconds),t}get isEnabled(){return fr(this.instance.config.capture_heatmaps)?fr(this.instance.config.enable_heatmaps)?this.Fi:this.instance.config.enable_heatmaps:!1!==this.instance.config.capture_heatmaps}startIfEnabled(){if(this.isEnabled){if(this.Y)return;Bv.info("starting..."),this.Zi(),this.$i=setInterval(this.qi.bind(this),this.flushIntervalMilliseconds)}else{var t,e;clearInterval(null!==(t=this.$i)&&void 0!==t?t:void 0),null==(e=this.Hi)||e.stop(),this.getAndClearBuffer()}}onRemoteConfig(t){var e=!!t.heatmaps;this.instance.persistence&&this.instance.persistence.register({[tn]:e}),this.Fi=e,this.startIfEnabled()}getAndClearBuffer(){var t=this.S;return this.S=void 0,t}Vi(t){this.ot(t.originalEvent,"deadclick")}Zi(){Bi&&ji&&(qr(Bi,"beforeunload",this.qi.bind(this)),qr(ji,"click",(t=>this.ot(t||(null==Bi?void 0:Bi.event))),{capture:!0}),qr(ji,"mousemove",(t=>this.ji(t||(null==Bi?void 0:Bi.event))),{capture:!0}),this.Hi=new Ld(this.instance,Pd,this.Vi.bind(this)),this.Hi.startIfEnabled(),this.Y=!0)}Yi(t,e){var i=this.instance.scrollManager.scrollY(),r=this.instance.scrollManager.scrollX(),n=this.instance.scrollManager.scrollElement(),o=function(t,e,i){for(var r=t;r&&Bn(r)&&!$n(r,"body");){if(r===i)return!1;if(er(e,null==Bi?void 0:Bi.getComputedStyle(r).position))return!0;r=Un(r)}return!1}(Gn(t),["fixed","sticky"],n);return{x:t.clientX+(o?0:r),y:t.clientY+(o?0:i),target_fixed:o,type:e}}ot(t,e){var r;if(void 0===e&&(e="click"),!Ln(t.target)&&$v(t)){var n=this.Yi(t,e);null!=(r=this.rageclicks)&&r.isRageClick(t.clientX,t.clientY,(new Date).getTime())&&this.zi(i({},n,{type:"rageclick"})),this.zi(n)}}ji(t){!Ln(t.target)&&$v(t)&&(clearTimeout(this.Gi),this.Gi=setTimeout((()=>{this.zi(this.Yi(t,"mousemove"))}),500))}zi(t){if(Bi){var e=Bi.location.href;this.S=this.S||{},this.S[e]||(this.S[e]=[]),this.S[e].push(t)}}qi(){this.S&&!vr(this.S)&&this.instance.capture("$$heatmap",{$heatmap_data:this.getAndClearBuffer()})}}class qv{constructor(t){this._instance=t}doPageView(t,e){var i,r=this.Wi(t,e);return this.Ui={pathname:null!==(i=null==Bi?void 0:Bi.location.pathname)&&void 0!==i?i:"",pageViewId:e,timestamp:t},this._instance.scrollManager.resetContext(),r}doPageLeave(t){var e;return this.Wi(t,null==(e=this.Ui)?void 0:e.pageViewId)}doEvent(){var t;return{$pageview_id:null==(t=this.Ui)?void 0:t.pageViewId}}Wi(t,e){var i=this.Ui;if(!i)return{$pageview_id:e};var r={$pageview_id:e,$prev_pageview_id:i.pageViewId},n=this._instance.scrollManager.getContext();if(n&&!this._instance.config.disable_scroll_properties){var{maxScrollHeight:o,lastScrollY:s,maxScrollY:a,maxContentHeight:l,lastContentY:u,maxContentY:h}=n;if(!(fr(o)||fr(s)||fr(a)||fr(l)||fr(u)||fr(h))){o=Math.ceil(o),s=Math.ceil(s),a=Math.ceil(a),l=Math.ceil(l),u=Math.ceil(u),h=Math.ceil(h);var c=o<=1?1:Bd(s/o,0,1),d=o<=1?1:Bd(a/o,0,1),v=l<=1?1:Bd(u/l,0,1),f=l<=1?1:Bd(h/l,0,1);r=Nr(r,{$prev_pageview_last_scroll:s,$prev_pageview_last_scroll_percentage:c,$prev_pageview_max_scroll:a,$prev_pageview_max_scroll_percentage:d,$prev_pageview_last_content:u,$prev_pageview_last_content_percentage:v,$prev_pageview_max_content:h,$prev_pageview_max_content_percentage:f})}}return i.pathname&&(r.$prev_pageview_pathname=i.pathname),i.timestamp&&(r.$prev_pageview_duration=(t.getTime()-i.timestamp.getTime())/1e3),r}}var Hv=Ar("[Error tracking]");class Vv{constructor(t){var e,i;this.Xi=[],this._instance=t,this.Xi=null!==(e=null==(i=this._instance.persistence)?void 0:i.get_property(rn))&&void 0!==e?e:[]}onRemoteConfig(t){var e,i,r=null!==(e=null==(i=t.errorTracking)?void 0:i.suppressionRules)&&void 0!==e?e:[];this.Xi=r,this._instance.persistence&&this._instance.persistence.register({[rn]:this.Xi})}sendExceptionEvent(t){this.Ji(t)?Hv.info("Skipping exception capture because a suppression rule matched"):this._instance.capture("$exception",t,{_noTruncate:!0,_batchKey:"exceptionEvent"})}Ji(t){var e=t.$exception_list;if(!e||!lr(e)||0===e.length)return!1;var i=e.reduce(((t,e)=>{var{type:i,value:r}=e;return pr(i)&&i.length>0&&t.$exception_types.push(i),pr(r)&&r.length>0&&t.$exception_values.push(r),t}),{$exception_types:[],$exception_values:[]});return this.Xi.some((t=>{var e=t.values.map((t=>{var e,r=su[t.operator],n=lr(t.value)?t.value:[t.value],o=null!==(e=i[t.key])&&void 0!==e?e:[];return n.length>0&&r(n,o)}));return"OR"===t.type?e.some(Boolean):e.every(Boolean)}))}}var jv="https?://(.*)",Yv=["gclid","gclsrc","dclid","gbraid","wbraid","fbclid","msclkid","twclid","li_fat_id","igshid","ttclid","rdt_cid","epik","qclid","sccid","irclid","_kx"],zv=Er(["utm_source","utm_medium","utm_campaign","utm_content","utm_term","gad_source","mc_cid"],Yv),Gv="<masked>",Wv=["li_fat_id"];function Uv(t,e,i){if(!ji)return{};var r,n=e?Er([],Yv,i||[]):[],o=Xv(zr(ji.URL,n,Gv),t),s=(r={},Rr(Wv,(function(t){var e=Cd.St(t);r[t]=e||null})),r);return Nr(s,o)}function Xv(t,e){var i=zv.concat(e||[]),r={};return Rr(i,(function(e){var i=Yr(t,e);r[e]=i||null})),r}function Jv(t){var e=function(t){return t?0===t.search(jv+"google.([^/?]*)")?"google":0===t.search(jv+"bing.com")?"bing":0===t.search(jv+"yahoo.com")?"yahoo":0===t.search(jv+"duckduckgo.com")?"duckduckgo":null:null}(t),i="yahoo"!=e?"q":"p",r={};if(!mr(e)){r.$search_engine=e;var n=ji?Yr(ji.referrer,i):"";n.length&&(r.ph_keyword=n)}return r}function Kv(){return navigator.language||navigator.userLanguage}function Qv(){return(null==ji?void 0:ji.referrer)||"$direct"}function tf(t,e){var i=t?Er([],Yv,e||[]):[],r=null==Yi?void 0:Yi.href.substring(0,1e3);return{r:Qv().substring(0,1e3),u:r?zr(r,i,Gv):void 0}}function ef(t){var e,{r:i,u:r}=t,n={$referrer:i,$referring_domain:null==i?void 0:"$direct"==i?"$direct":null==(e=Vr(i))?void 0:e.host};if(r){n.$current_url=r;var o=Vr(r);n.$host=null==o?void 0:o.host,n.$pathname=null==o?void 0:o.pathname;var s=Xv(r);Nr(n,s)}if(i){var a=Jv(i);Nr(n,a)}return n}function rf(){try{return Intl.DateTimeFormat().resolvedOptions().timeZone}catch(t){return}}function nf(){try{return(new Date).getTimezoneOffset()}catch(t){return}}function of(t,e){if(!Ui)return{};var i,r=t?Er([],Yv,e||[]):[],[n,o]=function(t){for(var e=0;e<hl.length;e++){var[i,r]=hl[e],n=i.exec(t),o=n&&(ur(r)?r(n,t):r);if(o)return o}return["",""]}(Ui);return Nr(Dr({$os:n,$os_version:o,$browser:al(Ui,navigator.vendor),$device:cl(Ui),$device_type:dl(Ui),$timezone:rf(),$timezone_offset:nf()}),{$current_url:zr(null==Yi?void 0:Yi.href,r,Gv),$host:null==Yi?void 0:Yi.host,$pathname:null==Yi?void 0:Yi.pathname,$raw_user_agent:Ui.length>1e3?Ui.substring(0,997)+"...":Ui,$browser_version:ul(Ui,navigator.vendor),$browser_language:Kv(),$browser_language_prefix:(i=Kv(),"string"==typeof i?i.split("-")[0]:void 0),$screen_height:null==Bi?void 0:Bi.screen.height,$screen_width:null==Bi?void 0:Bi.screen.width,$viewport_height:null==Bi?void 0:Bi.innerHeight,$viewport_width:null==Bi?void 0:Bi.innerWidth,$lib:"web",$lib_version:Sr.LIB_VERSION,$insert_id:Math.random().toString(36).substring(2,10)+Math.random().toString(36).substring(2,10),$time:Date.now()/1e3})}var sf=Ar("[FeatureFlags]"),af="$active_feature_flags",lf="$override_feature_flags",uf="$feature_flag_payloads",hf="$override_feature_flag_payloads",cf="$feature_flag_request_id",df=t=>{var e={};for(var[i,r]of Fr(t||{}))r&&(e[i]=r);return e},vf=t=>{var e=t.flags;return e?(t.featureFlags=Object.fromEntries(Object.keys(e).map((t=>{var i;return[t,null!==(i=e[t].variant)&&void 0!==i?i:e[t].enabled]}))),t.featureFlagPayloads=Object.fromEntries(Object.keys(e).filter((t=>e[t].enabled)).filter((t=>{var i;return null==(i=e[t].metadata)?void 0:i.payload})).map((t=>{var i;return[t,null==(i=e[t].metadata)?void 0:i.payload]})))):sf.warn("Using an older version of the feature flags endpoint. Please upgrade your PostHog server to the latest version"),t},ff=function(t){return t.FeatureFlags="feature_flags",t.Recordings="recordings",t}({});var pf=new Set(["7c6f7b45","66c1f69c","2727f65a","f3287528","8cc9a311","eb9f671b","c0e1c6f9","057989ec","723f4019","7b102104","563359d3","bad973ea","f6f2c4f4","59454a61","89ad1076","4edd0da1","26c52e72","a970bd2e","89cf4454","16e2b4e7","fba0e7b6","301c8488","bc65d69e","fe66a3c5","37926ca6","52a196df","d32a7577","42c4c9ef","6883bd5a","04809ff7","e59430a8","61be3dd8","7fa5500b","bf027177","8cfdba9b","96f6df5f","569798e9","0ebc61a5","1b5d7b92","17ebb0a4","f97ea965","85cc817b","3044dfc1","0c3fe5c3","b1f95fa3","8a6342e8","72365c68","12d34ad9","733853ec","3beeb69a","0645bb64","32de7f98","5dcbee21","3fe85053","ad960278","9466e5dd","7ca97b2d","2ee2a65c","28fde5f2","85c52f49","0ad823f4","f11b6cc9","aacf8af9","ab3e62b3","3a85ff15","8a67d3c4","f5e91ef1","4b873698","c5dae949","5b643d76","9599c892","34377448","2189e408","3be9ad53","1a14ce7c","2a164ded","8d53ea86","53bdb37d","bfc3f590","8df38ede","bdb81e49","38fde5c0","8d707e6d","73cbc496","f9d8a5ef","d3a9f8c4","a980d8cd","5bcfe086","e4818f68","4f11fb39","a13c6ae3","150c7fbb","98f3d658","f84f7377","1924dd9c","1f6b63b3","24748755","7c0f717c","8a87f11b","49f57f22","3c9e9234","3772f65b","dff631b6","cd609d40","f853c7f7","952db5ee","c5aa8a79","2d21b6fd","79b7164c","4110e26c","a7d3b43f","84e1b8f6","75cc0998","07f78e33","10ca9b1a","ce441b18","01eb8256","c0ac4b67","8e8e5216","db7943dd","fa133a95","498a4508","21bbda67","7dbfed69","be3ec24c","fc80b8e2"]);class gf{constructor(t){this.Ki=!1,this.Qi=!1,this.tr=!1,this.er=!1,this.ir=!1,this.rr=!1,this.nr=!1,this._instance=t,this.featureFlagEventHandlers=[]}flags(){if(this._instance.config.__preview_remote_config)this.rr=!0;else{var t=!this.sr&&(this._instance.config.advanced_disable_feature_flags||this._instance.config.advanced_disable_feature_flags_on_first_load);this.ar({disableFlags:t})}}get hasLoadedFlags(){return this.Qi}getFlags(){return Object.keys(this.getFlagVariants())}getFlagsWithDetails(){var t=this._instance.get_property(wn),e=this._instance.get_property(lf),r=this._instance.get_property(hf);if(!r&&!e)return t||{};var n=Nr({},t||{}),o=[...new Set([...Object.keys(r||{}),...Object.keys(e||{})])];for(var s of o){var a,l,u=n[s],h=null==e?void 0:e[s],c=fr(h)?null!==(a=null==u?void 0:u.enabled)&&void 0!==a&&a:!!h,d=fr(h)?u.variant:"string"==typeof h?h:void 0,v=null==r?void 0:r[s],f=i({},u,{enabled:c,variant:c?null!=d?d:null==u?void 0:u.variant:void 0});if(c!==(null==u?void 0:u.enabled)&&(f.original_enabled=null==u?void 0:u.enabled),d!==(null==u?void 0:u.variant)&&(f.original_variant=null==u?void 0:u.variant),v)f.metadata=i({},null==u?void 0:u.metadata,{payload:v,original_payload:null==u||null==(l=u.metadata)?void 0:l.payload});n[s]=f}return this.Ki||(sf.warn(" Overriding feature flag details!",{flagDetails:t,overriddenPayloads:r,finalDetails:n}),this.Ki=!0),n}getFlagVariants(){var t=this._instance.get_property(bn),e=this._instance.get_property(lf);if(!e)return t||{};for(var i=Nr({},t),r=Object.keys(e),n=0;n<r.length;n++)i[r[n]]=e[r[n]];return this.Ki||(sf.warn(" Overriding feature flags!",{enabledFlags:t,overriddenFlags:e,finalFlags:i}),this.Ki=!0),i}getFlagPayloads(){var t=this._instance.get_property(uf),e=this._instance.get_property(hf);if(!e)return t||{};for(var i=Nr({},t||{}),r=Object.keys(e),n=0;n<r.length;n++)i[r[n]]=e[r[n]];return this.Ki||(sf.warn(" Overriding feature flag payloads!",{flagPayloads:t,overriddenPayloads:e,finalPayloads:i}),this.Ki=!0),i}reloadFeatureFlags(){this.er||this._instance.config.advanced_disable_feature_flags||this.sr||(this.sr=setTimeout((()=>{this.ar()}),5))}lr(){clearTimeout(this.sr),this.sr=void 0}ensureFlagsLoaded(){this.Qi||this.tr||this.sr||this.reloadFeatureFlags()}setAnonymousDistinctId(t){this.$anon_distinct_id=t}setReloadingPaused(t){this.er=t}ar(t){var e;if(this.lr(),!this._instance.tt())if(this.tr)this.ir=!0;else{var r={token:this._instance.config.token,distinct_id:this._instance.get_distinct_id(),groups:this._instance.getGroups(),$anon_distinct_id:this.$anon_distinct_id,person_properties:i({},(null==(e=this._instance.persistence)?void 0:e.get_initial_props())||{},this._instance.get_property(In)||{}),group_properties:this._instance.get_property(Cn)};(null!=t&&t.disableFlags||this._instance.config.advanced_disable_feature_flags)&&(r.disable_flags=!0);var n=this._instance.config.__preview_flags_v2&&this._instance.config.__preview_remote_config,o=function(t){var e=function(t){for(var e=2166136261,i=0;i<t.length;i++)e^=t.charCodeAt(i),e+=(e<<1)+(e<<4)+(e<<7)+(e<<8)+(e<<24);return("00000000"+(e>>>0).toString(16)).slice(-8)}(t);return null==pf?void 0:pf.has(e)}(this._instance.config.token)?"/decide?v=4":n?"/flags/?v=2":"/flags/?v=2&config=true",s=this._instance.config.advanced_only_evaluate_survey_feature_flags?"&only_evaluate_survey_feature_flags=true":"",a=this._instance.requestRouter.endpointFor("api",o+s);n&&(r.timezone=rf()),this.tr=!0,this._instance.ur({method:"POST",url:a,data:r,compression:this._instance.config.disable_compression?void 0:Qi.Base64,timeout:this._instance.config.feature_flag_request_timeout_ms,callback:t=>{var e,i,n=!0;(200===t.statusCode&&(this.ir||(this.$anon_distinct_id=void 0),n=!1),this.tr=!1,this.rr)||(this.rr=!0,this._instance.hr(null!==(i=t.json)&&void 0!==i?i:{}));if(!r.disable_flags||this.ir)if(this.nr=!n,t.json&&null!=(e=t.json.quotaLimited)&&e.includes(ff.FeatureFlags))sf.warn("You have hit your feature flags quota limit, and will not be able to load feature flags until the quota is reset.  Please visit https://posthog.com/docs/billing/limits-alerts to learn more.");else{var o;if(!r.disable_flags)this.receivedFeatureFlags(null!==(o=t.json)&&void 0!==o?o:{},n);this.ir&&(this.ir=!1,this.ar())}}})}}getFeatureFlag(t,e){if(void 0===e&&(e={}),this.Qi||this.getFlags()&&this.getFlags().length>0){var i=this.getFlagVariants()[t],r=""+i,n=this._instance.get_property(cf)||void 0,o=this._instance.get_property(xn)||{};if((e.send_event||!("send_event"in e))&&(!(t in o)||!o[t].includes(r))){var s,a,l,u,h,c,d,v,f;lr(o[t])?o[t].push(r):o[t]=[r],null==(s=this._instance.persistence)||s.register({[xn]:o});var p=this.getFeatureFlagDetails(t),g={$feature_flag:t,$feature_flag_response:i,$feature_flag_payload:this.getFeatureFlagPayload(t)||null,$feature_flag_request_id:n,$feature_flag_bootstrapped_response:(null==(a=this._instance.config.bootstrap)||null==(a=a.featureFlags)?void 0:a[t])||null,$feature_flag_bootstrapped_payload:(null==(l=this._instance.config.bootstrap)||null==(l=l.featureFlagPayloads)?void 0:l[t])||null,$used_bootstrap_value:!this.nr};fr(null==p||null==(u=p.metadata)?void 0:u.version)||(g.$feature_flag_version=p.metadata.version);var m,y=null!==(h=null==p||null==(c=p.reason)?void 0:c.description)&&void 0!==h?h:null==p||null==(d=p.reason)?void 0:d.code;if(y&&(g.$feature_flag_reason=y),null!=p&&null!=(v=p.metadata)&&v.id&&(g.$feature_flag_id=p.metadata.id),fr(null==p?void 0:p.original_variant)&&fr(null==p?void 0:p.original_enabled)||(g.$feature_flag_original_response=fr(p.original_variant)?p.original_enabled:p.original_variant),null!=p&&null!=(f=p.metadata)&&f.original_payload)g.$feature_flag_original_payload=null==p||null==(m=p.metadata)?void 0:m.original_payload;this._instance.capture("$feature_flag_called",g)}return i}sf.warn('getFeatureFlag for key "'+t+"\" failed. Feature flags didn't load in time.")}getFeatureFlagDetails(t){return this.getFlagsWithDetails()[t]}getFeatureFlagPayload(t){return this.getFlagPayloads()[t]}getRemoteConfigPayload(t,e){var i=this._instance.config.token;this._instance.ur({method:"POST",url:this._instance.requestRouter.endpointFor("api","/flags/?v=2&config=true"),data:{distinct_id:this._instance.get_distinct_id(),token:i},compression:this._instance.config.disable_compression?void 0:Qi.Base64,timeout:this._instance.config.feature_flag_request_timeout_ms,callback:i=>{var r,n=null==(r=i.json)?void 0:r.featureFlagPayloads;e((null==n?void 0:n[t])||void 0)}})}isFeatureEnabled(t,e){if(void 0===e&&(e={}),this.Qi||this.getFlags()&&this.getFlags().length>0)return!!this.getFeatureFlag(t,e);sf.warn('isFeatureEnabled for key "'+t+"\" failed. Feature flags didn't load in time.")}addFeatureFlagsHandler(t){this.featureFlagEventHandlers.push(t)}removeFeatureFlagsHandler(t){this.featureFlagEventHandlers=this.featureFlagEventHandlers.filter((e=>e!==t))}receivedFeatureFlags(t,e){if(this._instance.persistence){this.Qi=!0;var r=this.getFlagVariants(),n=this.getFlagPayloads(),o=this.getFlagsWithDetails();!function(t,e,r,n,o){void 0===r&&(r={}),void 0===n&&(n={}),void 0===o&&(o={});var s=vf(t),a=s.flags,l=s.featureFlags,u=s.featureFlagPayloads;if(l){var h=t.requestId;if(lr(l)){sf.warn("v1 of the feature flags endpoint is deprecated. Please use the latest version.");var c={};if(l)for(var d=0;d<l.length;d++)c[l[d]]=!0;e&&e.register({[af]:l,[bn]:c})}else{var v=l,f=u,p=a;t.errorsWhileComputingFlags&&(v=i({},r,v),f=i({},n,f),p=i({},o,p)),e&&e.register(i({[af]:Object.keys(df(v)),[bn]:v||{},[uf]:f||{},[wn]:p||{}},h?{[cf]:h}:{}))}}}(t,this._instance.persistence,r,n,o),this.cr(e)}}override(t,e){void 0===e&&(e=!1),sf.warn("override is deprecated. Please use overrideFeatureFlags instead."),this.overrideFeatureFlags({flags:t,suppressWarning:e})}overrideFeatureFlags(t){if(!this._instance.__loaded||!this._instance.persistence)return sf.uninitializedWarning("posthog.featureFlags.overrideFeatureFlags");if(!1===t)return this._instance.persistence.unregister(lf),this._instance.persistence.unregister(hf),void this.cr();if(t&&"object"==typeof t&&("flags"in t||"payloads"in t)){var e,i=t;if(this.Ki=Boolean(null!==(e=i.suppressWarning)&&void 0!==e&&e),"flags"in i)if(!1===i.flags)this._instance.persistence.unregister(lf);else if(i.flags)if(lr(i.flags)){for(var r={},n=0;n<i.flags.length;n++)r[i.flags[n]]=!0;this._instance.persistence.register({[lf]:r})}else this._instance.persistence.register({[lf]:i.flags});return"payloads"in i&&(!1===i.payloads?this._instance.persistence.unregister(hf):i.payloads&&this._instance.persistence.register({[hf]:i.payloads})),void this.cr()}this.cr()}onFeatureFlags(t){if(this.addFeatureFlagsHandler(t),this.Qi){var{flags:e,flagVariants:i}=this.dr();t(e,i)}return()=>this.removeFeatureFlagsHandler(t)}updateEarlyAccessFeatureEnrollment(t,e){var r,n=(this._instance.get_property(_n)||[]).find((e=>e.flagKey===t)),o={["$feature_enrollment/"+t]:e},s={$feature_flag:t,$feature_enrollment:e,$set:o};n&&(s.$early_access_feature_name=n.name),this._instance.capture("$feature_enrollment_update",s),this.setPersonPropertiesForFlags(o,!1);var a=i({},this.getFlagVariants(),{[t]:e});null==(r=this._instance.persistence)||r.register({[af]:Object.keys(df(a)),[bn]:a}),this.cr()}getEarlyAccessFeatures(t,e,i){void 0===e&&(e=!1);var r=this._instance.get_property(_n),n=i?"&"+i.map((t=>"stage="+t)).join("&"):"";if(r&&!e)return t(r);this._instance.ur({url:this._instance.requestRouter.endpointFor("api","/api/early_access_features/?token="+this._instance.config.token+n),method:"GET",callback:e=>{var i;if(e.json){var r=e.json.earlyAccessFeatures;return null==(i=this._instance.persistence)||i.register({[_n]:r}),t(r)}}})}dr(){var t=this.getFlags(),e=this.getFlagVariants();return{flags:t.filter((t=>e[t])),flagVariants:Object.keys(e).filter((t=>e[t])).reduce(((t,i)=>(t[i]=e[i],t)),{})}}cr(t){var{flags:e,flagVariants:i}=this.dr();this.featureFlagEventHandlers.forEach((r=>r(e,i,{errorsLoading:t})))}setPersonPropertiesForFlags(t,e){void 0===e&&(e=!0);var r=this._instance.get_property(In)||{};this._instance.register({[In]:i({},r,t)}),e&&this._instance.reloadFeatureFlags()}resetPersonPropertiesForFlags(){this._instance.unregister(In)}setGroupPropertiesForFlags(t,e){void 0===e&&(e=!0);var r=this._instance.get_property(Cn)||{};0!==Object.keys(r).length&&Object.keys(r).forEach((e=>{r[e]=i({},r[e],t[e]),delete t[e]})),this._instance.register({[Cn]:i({},r,t)}),e&&this._instance.reloadFeatureFlags()}resetGroupPropertiesForFlags(t){if(t){var e=this._instance.get_property(Cn)||{};this._instance.register({[Cn]:i({},e,{[t]:{}})})}else this._instance.unregister(Cn)}}var mf=["cookie","localstorage","localstorage+cookie","sessionstorage","memory"];class yf{constructor(t){this.X=t,this.props={},this.vr=!1,this.pr=(t=>{var e="";return t.token&&(e=t.token.replace(/\+/g,"PL").replace(/\//g,"SL").replace(/=/g,"EQ")),t.persistence_name?"ph_"+t.persistence_name:"ph_"+e+"_posthog"})(t),this.Rt=this.gr(t),this.load(),t.debug&&xr.info("Persistence loaded",t.persistence,i({},this.props)),this.update_config(t,t),this.save()}gr(t){-1===mf.indexOf(t.persistence.toLowerCase())&&(xr.critical("Unknown persistence type "+t.persistence+"; falling back to localStorage+cookie"),t.persistence="localStorage+cookie");var e=t.persistence.toLowerCase();return"localstorage"===e&&kd.It()?kd:"localstorage+cookie"===e&&Ad.It()?Ad:"sessionstorage"===e&&Nd.It()?Nd:"memory"===e?Md:"cookie"===e?Cd:Ad.It()?Ad:Cd}properties(){var t={};return Rr(this.props,(function(e,i){if(i===bn&&dr(e))for(var r=Object.keys(e),n=0;n<r.length;n++)t["$feature/"+r[n]]=e[r[n]];else s=i,a=!1,(mr(o=Dn)?a:Hi&&o.indexOf===Hi?-1!=o.indexOf(s):(Rr(o,(function(t){if(a||(a=t===s))return Tr})),a))||(t[i]=e);var o,s,a})),t}load(){if(!this.mr){var t=this.Rt.kt(this.pr);t&&(this.props=Nr({},t))}}save(){this.mr||this.Rt.xt(this.pr,this.props,this.yr,this.br,this._r,this.X.debug)}remove(){this.Rt.At(this.pr,!1),this.Rt.At(this.pr,!0)}clear(){this.remove(),this.props={}}register_once(t,e,i){if(dr(t)){fr(e)&&(e="None"),this.yr=fr(i)?this.wr:i;var r=!1;if(Rr(t,((t,i)=>{this.props.hasOwnProperty(i)&&this.props[i]!==e||(this.props[i]=t,r=!0)})),r)return this.save(),!0}return!1}register(t,e){if(dr(t)){this.yr=fr(e)?this.wr:e;var i=!1;if(Rr(t,((e,r)=>{t.hasOwnProperty(r)&&this.props[r]!==e&&(this.props[r]=e,i=!0)})),i)return this.save(),!0}return!1}unregister(t){t in this.props&&(delete this.props[t],this.save())}update_campaign_params(){if(!this.vr){var t=Uv(this.X.custom_campaign_params,this.X.mask_personal_data_properties,this.X.custom_personal_data_properties);vr(Dr(t))||this.register(t),this.vr=!0}}update_search_keyword(){var t;this.register((t=null==ji?void 0:ji.referrer)?Jv(t):{})}update_referrer_info(){var t;this.register_once({$referrer:Qv(),$referring_domain:null!=ji&&ji.referrer&&(null==(t=Vr(ji.referrer))?void 0:t.host)||"$direct"},void 0)}set_initial_person_info(){this.props[Rn]||this.props[Nn]||this.register_once({[En]:tf(this.X.mask_personal_data_properties,this.X.custom_personal_data_properties)},void 0)}get_initial_props(){var t={};Rr([Nn,Rn],(e=>{var i=this.props[e];i&&Rr(i,(function(e,i){t["$initial_"+rr(i)]=e}))}));var e,i,r=this.props[En];if(r){var n=(e=ef(r),i={},Rr(e,(function(t,e){i["$initial_"+rr(e)]=t})),i);Nr(t,n)}return t}safe_merge(t){return Rr(this.props,(function(e,i){i in t||(t[i]=e)})),t}update_config(t,e){if(this.wr=this.yr=t.cookie_expiration,this.set_disabled(t.disable_persistence),this.set_cross_subdomain(t.cross_subdomain_cookie),this.set_secure(t.secure_cookie),t.persistence!==e.persistence){var i=this.gr(t),r=this.props;this.clear(),this.Rt=i,this.props=r,this.save()}}set_disabled(t){this.mr=t,this.mr?this.remove():this.save()}set_cross_subdomain(t){t!==this.br&&(this.br=t,this.remove(),this.save())}set_secure(t){t!==this._r&&(this._r=t,this.remove(),this.save())}set_event_timer(t,e){var i=this.props[Kr]||{};i[t]=e,this.props[Kr]=i,this.save()}remove_event_timer(t){var e=(this.props[Kr]||{})[t];return fr(e)||(delete this.props[Kr][t],this.save()),e}get_property(t){return this.props[t]}set_property(t,e){this.props[t]=e,this.save()}}class bf{constructor(){this.Ir={},this.Ir={}}on(t,e){return this.Ir[t]||(this.Ir[t]=[]),this.Ir[t].push(e),()=>{this.Ir[t]=this.Ir[t].filter((t=>t!==e))}}emit(t,e){for(var i of this.Ir[t]||[])i(e);for(var r of this.Ir["*"]||[])r(t,e)}}class _f{constructor(t){this.Cr=new bf,this.Sr=(t,e)=>this.kr(t,e)&&this.Ar(t,e)&&this.Tr(t,e),this.kr=(t,e)=>null==e||!e.event||(null==t?void 0:t.event)===(null==e?void 0:e.event),this._instance=t,this.Mr=new Set,this.Rr=new Set}init(){var t;if(!fr(null==(t=this._instance)?void 0:t.Nr)){var e;null==(e=this._instance)||e.Nr(((t,e)=>{this.on(t,e)}))}}register(t){var e,i;if(!fr(null==(e=this._instance)?void 0:e.Nr)&&(t.forEach((t=>{var e,i;null==(e=this.Rr)||e.add(t),null==(i=t.steps)||i.forEach((t=>{var e;null==(e=this.Mr)||e.add((null==t?void 0:t.event)||"")}))})),null!=(i=this._instance)&&i.autocapture)){var r,n=new Set;t.forEach((t=>{var e;null==(e=t.steps)||e.forEach((t=>{null!=t&&t.selector&&n.add(null==t?void 0:t.selector)}))})),null==(r=this._instance)||r.autocapture.setElementSelectors(n)}}on(t,e){var i;null!=e&&0!=t.length&&(this.Mr.has(t)||this.Mr.has(null==e?void 0:e.event))&&this.Rr&&(null==(i=this.Rr)?void 0:i.size)>0&&this.Rr.forEach((t=>{this.Er(e,t)&&this.Cr.emit("actionCaptured",t.name)}))}Fr(t){this.onAction("actionCaptured",(e=>t(e)))}Er(t,e){if(null==(null==e?void 0:e.steps))return!1;for(var i of e.steps)if(this.Sr(t,i))return!0;return!1}onAction(t,e){return this.Cr.on(t,e)}Ar(t,e){if(null!=e&&e.url){var i,r=null==t||null==(i=t.properties)?void 0:i.$current_url;if(!r||"string"!=typeof r)return!1;if(!_f.Or(r,null==e?void 0:e.url,(null==e?void 0:e.url_matching)||"contains"))return!1}return!0}static Or(t,e,i){switch(i){case"regex":return!!Bi&&nu(t,e);case"exact":return e===t;case"contains":var r=_f.Pr(e).replace(/_/g,".").replace(/%/g,".*");return nu(t,r);default:return!1}}static Pr(t){return t.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}Tr(t,e){if((null!=e&&e.href||null!=e&&e.tag_name||null!=e&&e.text)&&!this.Dr(t).some((t=>!(null!=e&&e.href&&!_f.Or(t.href||"",null==e?void 0:e.href,(null==e?void 0:e.href_matching)||"exact"))&&((null==e||!e.tag_name||t.tag_name===(null==e?void 0:e.tag_name))&&!(null!=e&&e.text&&!_f.Or(t.text||"",null==e?void 0:e.text,(null==e?void 0:e.text_matching)||"exact")&&!_f.Or(t.$el_text||"",null==e?void 0:e.text,(null==e?void 0:e.text_matching)||"exact"))))))return!1;if(null!=e&&e.selector){var i,r=null==t||null==(i=t.properties)?void 0:i.$element_selectors;if(!r)return!1;if(!r.includes(null==e?void 0:e.selector))return!1}return!0}Dr(t){return null==(null==t?void 0:t.properties.$elements)?[]:null==t?void 0:t.properties.$elements}}class wf{constructor(t){this._instance=t,this.Lr=new Map,this.Br=new Map}register(t){var e;fr(null==(e=this._instance)?void 0:e.Nr)||(this.$r(t),this.Zr(t))}Zr(t){var e=t.filter((t=>{var e,i;return(null==(e=t.conditions)?void 0:e.actions)&&(null==(i=t.conditions)||null==(i=i.actions)||null==(i=i.values)?void 0:i.length)>0}));if(0!==e.length){if(null==this.qr){this.qr=new _f(this._instance),this.qr.init();this.qr.Fr((t=>{this.onAction(t)}))}e.forEach((t=>{var e,i,r,n,o;t.conditions&&null!=(e=t.conditions)&&e.actions&&null!=(i=t.conditions)&&null!=(i=i.actions)&&i.values&&(null==(r=t.conditions)||null==(r=r.actions)||null==(r=r.values)?void 0:r.length)>0&&(null==(n=this.qr)||n.register(t.conditions.actions.values),null==(o=t.conditions)||null==(o=o.actions)||null==(o=o.values)||o.forEach((e=>{if(e&&e.name){var i=this.Br.get(e.name);i&&i.push(t.id),this.Br.set(e.name,i||[t.id])}})))}))}}$r(t){var e;if(0!==t.filter((t=>{var e,i;return(null==(e=t.conditions)?void 0:e.events)&&(null==(i=t.conditions)||null==(i=i.events)||null==(i=i.values)?void 0:i.length)>0})).length){null==(e=this._instance)||e.Nr(((t,e)=>{this.onEvent(t,e)})),t.forEach((t=>{var e;null==(e=t.conditions)||null==(e=e.events)||null==(e=e.values)||e.forEach((e=>{if(e&&e.name){var i=this.Lr.get(e.name);i&&i.push(t.id),this.Lr.set(e.name,i||[t.id])}}))}))}}onEvent(t,e){var i,r=(null==(i=this._instance)||null==(i=i.persistence)?void 0:i.props[kn])||[];if("survey shown"===t&&e&&r.length>0){var n;ea.info("survey event matched, removing survey from activated surveys",{event:t,eventPayload:e,existingActivatedSurveys:r});var o=null==e||null==(n=e.properties)?void 0:n.$survey_id;if(o){var s=r.indexOf(o);s>=0&&(r.splice(s,1),this.Hr(r))}}else this.Lr.has(t)&&(ea.info("survey event matched, updating activated surveys",{event:t,surveys:this.Lr.get(t)}),this.Hr(r.concat(this.Lr.get(t)||[])))}onAction(t){var e,i=(null==(e=this._instance)||null==(e=e.persistence)?void 0:e.props[kn])||[];this.Br.has(t)&&this.Hr(i.concat(this.Br.get(t)||[]))}Hr(t){var e;null==(e=this._instance)||null==(e=e.persistence)||e.register({[kn]:[...new Set(t)]})}getSurveys(){var t,e=null==(t=this._instance)||null==(t=t.persistence)?void 0:t.props[kn];return e||[]}getEventToSurveys(){return this.Lr}Vr(){return this.qr}}class If{constructor(t){this.jr=null,this.Yr=!1,this.zr=!1,this.Gr=[],this._instance=t,this._surveyEventReceiver=null}onRemoteConfig(t){var e=t.surveys;if(yr(e))return ea.warn("Flags not loaded yet. Not loading surveys.");var i=lr(e);this.Wr=i?e.length>0:e,ea.info("flags response received, hasSurveys: "+this.Wr),this.Wr&&this.loadIfEnabled()}reset(){localStorage.removeItem("lastSeenSurveyDate");for(var t=[],e=0;e<localStorage.length;e++){var i=localStorage.key(e);(null!=i&&i.startsWith(oa)||null!=i&&i.startsWith(sa))&&t.push(i)}t.forEach((t=>localStorage.removeItem(t)))}loadIfEnabled(){if(!this.jr)if(this.zr)ea.info("Already initializing surveys, skipping...");else if(this._instance.config.disable_surveys)ea.info("Disabled. Not loading surveys.");else if(this.Wr){var t=null==Xi?void 0:Xi.__PosthogExtensions__;if(t){this.zr=!0;try{var e=t.generateSurveys;if(e)return void this.Ur(e);var i=t.loadExternalDependency;if(!i)return void this.Xr("PostHog loadExternalDependency extension not found.");i(this._instance,"surveys",(e=>{e||!t.generateSurveys?this.Xr("Could not load surveys script",e):this.Ur(t.generateSurveys)}))}catch(t){throw this.Xr("Error initializing surveys",t),t}finally{this.zr=!1}}else ea.error("PostHog Extensions not found.")}else ea.info("No surveys to load.")}Ur(t){this.jr=t(this._instance),this._surveyEventReceiver=new wf(this._instance),ea.info("Surveys loaded successfully"),this.Jr({isLoaded:!0})}Xr(t,e){ea.error(t,e),this.Jr({isLoaded:!1,error:t})}onSurveysLoaded(t){return this.Gr.push(t),this.jr&&this.Jr({isLoaded:!0}),()=>{this.Gr=this.Gr.filter((e=>e!==t))}}getSurveys(t,e){if(void 0===e&&(e=!1),this._instance.config.disable_surveys)return ea.info("Disabled. Not loading surveys."),t([]);var i=this._instance.get_property(Sn);if(i&&!e)return t(i,{isLoaded:!0});if(this.Yr)return t([],{isLoaded:!1,error:"Surveys are already being loaded"});try{this.Yr=!0,this._instance.ur({url:this._instance.requestRouter.endpointFor("api","/api/surveys/?token="+this._instance.config.token),method:"GET",timeout:this._instance.config.surveys_request_timeout_ms,callback:e=>{var i;this.Yr=!1;var r=e.statusCode;if(200!==r||!e.json){var n="Surveys API could not be loaded, status: "+r;return ea.error(n),t([],{isLoaded:!1,error:n})}var o,s=e.json.surveys||[],a=s.filter((t=>ia(t)&&(ra(t)||na(t))));a.length>0&&(null==(o=this._surveyEventReceiver)||o.register(a));return null==(i=this._instance.persistence)||i.register({[Sn]:s}),t(s,{isLoaded:!0})}})}catch(t){throw this.Yr=!1,t}}Jr(t){for(var e of this.Gr)try{t.isLoaded?this.getSurveys(e):e([],t)}catch(t){ea.error("Error in survey callback",t)}}getActiveMatchingSurveys(t,e){if(void 0===e&&(e=!1),!yr(this.jr))return this.jr.getActiveMatchingSurveys(t,e);ea.warn("init was not called")}Kr(t){var e=null;return this.getSurveys((i=>{var r;e=null!==(r=i.find((e=>e.id===t)))&&void 0!==r?r:null})),e}Qr(t){if(yr(this.jr))return{eligible:!1,reason:"SDK is not enabled or survey functionality is not yet loaded"};var e="string"==typeof t?this.Kr(t):t;return e?this.jr.checkSurveyEligibility(e):{eligible:!1,reason:"Survey not found"}}canRenderSurvey(t){if(yr(this.jr))return ea.warn("init was not called"),{visible:!1,disabledReason:"SDK is not enabled or survey functionality is not yet loaded"};var e=this.Qr(t);return{visible:e.eligible,disabledReason:e.reason}}canRenderSurveyAsync(t,e){return yr(this.jr)?(ea.warn("init was not called"),Promise.resolve({visible:!1,disabledReason:"SDK is not enabled or survey functionality is not yet loaded"})):new Promise((i=>{this.getSurveys((e=>{var r,n=null!==(r=e.find((e=>e.id===t)))&&void 0!==r?r:null;if(n){var o=this.Qr(n);i({visible:o.eligible,disabledReason:o.reason})}else i({visible:!1,disabledReason:"Survey not found"})}),e)}))}renderSurvey(t,e){if(yr(this.jr))ea.warn("init was not called");else{var i=this.Kr(t),r=null==ji?void 0:ji.querySelector(e);i?r?this.jr.renderSurvey(i,r):ea.warn("Survey element not found"):ea.warn("Survey not found")}}}var Cf=Ar("[RateLimiter]");class Sf{constructor(t){var e,i;this.serverLimits={},this.lastEventRateLimited=!1,this.checkForLimiting=t=>{var e=t.text;if(e&&e.length)try{(JSON.parse(e).quota_limited||[]).forEach((t=>{Cf.info((t||"events")+" is quota limited."),this.serverLimits[t]=(new Date).getTime()+6e4}))}catch(t){return void Cf.warn('could not rate limit - continuing. Error: "'+(null==t?void 0:t.message)+'"',{text:e})}},this.instance=t,this.captureEventsPerSecond=(null==(e=t.config.rate_limiting)?void 0:e.events_per_second)||10,this.captureEventsBurstLimit=Math.max((null==(i=t.config.rate_limiting)?void 0:i.events_burst_limit)||10*this.captureEventsPerSecond,this.captureEventsPerSecond),this.lastEventRateLimited=this.clientRateLimitContext(!0).isRateLimited}clientRateLimitContext(t){var e,i,r;void 0===t&&(t=!1);var n=(new Date).getTime(),o=null!==(e=null==(i=this.instance.persistence)?void 0:i.get_property(Mn))&&void 0!==e?e:{tokens:this.captureEventsBurstLimit,last:n};o.tokens+=(n-o.last)/1e3*this.captureEventsPerSecond,o.last=n,o.tokens>this.captureEventsBurstLimit&&(o.tokens=this.captureEventsBurstLimit);var s=o.tokens<1;return s||t||(o.tokens=Math.max(0,o.tokens-1)),!s||this.lastEventRateLimited||t||this.instance.capture("$$client_ingestion_warning",{$$client_ingestion_warning_message:"posthog-js client rate limited. Config is set to "+this.captureEventsPerSecond+" events per second and "+this.captureEventsBurstLimit+" events burst limit."},{skip_client_rate_limiting:!0}),this.lastEventRateLimited=s,null==(r=this.instance.persistence)||r.set_property(Mn,o),{isRateLimited:s,remainingTokens:o.tokens}}isServerRateLimited(t){var e=this.serverLimits[t||"events"]||!1;return!1!==e&&(new Date).getTime()<e}}var kf=Ar("[RemoteConfig]");class xf{constructor(t){this._instance=t}get remoteConfig(){var t;return null==(t=Xi._POSTHOG_REMOTE_CONFIG)||null==(t=t[this._instance.config.token])?void 0:t.config}tn(t){var e,i;null!=(e=Xi.__PosthogExtensions__)&&e.loadExternalDependency?null==(i=Xi.__PosthogExtensions__)||null==i.loadExternalDependency||i.loadExternalDependency(this._instance,"remote-config",(()=>t(this.remoteConfig))):(kf.error("PostHog Extensions not found. Cannot load remote config."),t())}en(t){this._instance.ur({method:"GET",url:this._instance.requestRouter.endpointFor("assets","/array/"+this._instance.config.token+"/config"),callback:e=>{t(e.json)}})}load(){try{if(this.remoteConfig)return kf.info("Using preloaded remote config",this.remoteConfig),void this.hr(this.remoteConfig);if(this._instance.tt())return void kf.warn("Remote config is disabled. Falling back to local config.");this.tn((t=>{if(!t)return kf.info("No config found after loading remote JS config. Falling back to JSON."),void this.en((t=>{this.hr(t)}));this.hr(t)}))}catch(t){kf.error("Error loading remote config",t)}}hr(t){t?this._instance.config.__preview_remote_config?(this._instance.hr(t),!1!==t.hasFeatureFlags&&this._instance.featureFlags.ensureFlagsLoaded()):kf.info("__preview_remote_config is disabled. Logging config instead",t):kf.error("Failed to fetch remote config from PostHog.")}}var Af=3e3;class Tf{constructor(t,e){this.rn=!0,this.nn=[],this.sn=Bd((null==e?void 0:e.flush_interval_ms)||Af,250,5e3,"flush interval",Af),this.an=t}enqueue(t){this.nn.push(t),this.ln||this.un()}unload(){this.hn();var t=this.nn.length>0?this.cn():{},e=Object.values(t),r=[...e.filter((t=>0===t.url.indexOf("/e"))),...e.filter((t=>0!==t.url.indexOf("/e")))];r.map((t=>{this.an(i({},t,{transport:"sendBeacon"}))}))}enable(){this.rn=!1,this.un()}un(){var t=this;this.rn||(this.ln=setTimeout((()=>{if(this.hn(),this.nn.length>0){var e=this.cn(),i=function(){var i=e[r],n=(new Date).getTime();i.data&&lr(i.data)&&Rr(i.data,(t=>{t.offset=Math.abs(t.timestamp-n),delete t.timestamp})),t.an(i)};for(var r in e)i()}}),this.sn))}hn(){clearTimeout(this.ln),this.ln=void 0}cn(){var t={};return Rr(this.nn,(e=>{var r,n=e,o=(n?n.batchKey:null)||n.url;fr(t[o])&&(t[o]=i({},n,{data:[]})),null==(r=t[o].data)||r.push(n.data)})),this.nn=[],t}}var Mf=["retriesPerformedSoFar"];class Rf{constructor(t){this.dn=!1,this.vn=3e3,this.nn=[],this._instance=t,this.nn=[],this.fn=!0,!fr(Bi)&&"onLine"in Bi.navigator&&(this.fn=Bi.navigator.onLine,qr(Bi,"online",(()=>{this.fn=!0,this.qi()})),qr(Bi,"offline",(()=>{this.fn=!1})))}get length(){return this.nn.length}retriableRequest(t){var{retriesPerformedSoFar:e}=t,n=r(t,Mf);br(e)&&e>0&&(n.url=tu(n.url,{retry_count:e})),this._instance.ur(i({},n,{callback:t=>{200!==t.statusCode&&(t.statusCode<400||t.statusCode>=500)&&(null!=e?e:0)<10?this.pn(i({retriesPerformedSoFar:e},n)):null==n.callback||n.callback(t)}}))}pn(t){var e=t.retriesPerformedSoFar||0;t.retriesPerformedSoFar=e+1;var i=function(t){var e=3e3*Math.pow(2,t),i=e/2,r=Math.min(18e5,e),n=(Math.random()-.5)*(r-i);return Math.ceil(r+n)}(e),r=Date.now()+i;this.nn.push({retryAt:r,requestOptions:t});var n="Enqueued failed request for retry in "+i;navigator.onLine||(n+=" (Browser is offline)"),xr.warn(n),this.dn||(this.dn=!0,this.gn())}gn(){this.mn&&clearTimeout(this.mn),this.mn=setTimeout((()=>{this.fn&&this.nn.length>0&&this.qi(),this.gn()}),this.vn)}qi(){var t=Date.now(),e=[],i=this.nn.filter((i=>i.retryAt<t||(e.push(i),!1)));if(this.nn=e,i.length>0)for(var{requestOptions:r}of i)this.retriableRequest(r)}unload(){for(var{requestOptions:t}of(this.mn&&(clearTimeout(this.mn),this.mn=void 0),this.nn))try{this._instance.ur(i({},t,{transport:"sendBeacon"}))}catch(t){xr.error(t)}this.nn=[]}}class Nf{constructor(t){this.yn=()=>{var t,e,i,r;this.bn||(this.bn={});var n=this.scrollElement(),o=this.scrollY(),s=n?Math.max(0,n.scrollHeight-n.clientHeight):0,a=o+((null==n?void 0:n.clientHeight)||0),l=(null==n?void 0:n.scrollHeight)||0;this.bn.lastScrollY=Math.ceil(o),this.bn.maxScrollY=Math.max(o,null!==(t=this.bn.maxScrollY)&&void 0!==t?t:0),this.bn.maxScrollHeight=Math.max(s,null!==(e=this.bn.maxScrollHeight)&&void 0!==e?e:0),this.bn.lastContentY=a,this.bn.maxContentY=Math.max(a,null!==(i=this.bn.maxContentY)&&void 0!==i?i:0),this.bn.maxContentHeight=Math.max(l,null!==(r=this.bn.maxContentHeight)&&void 0!==r?r:0)},this._instance=t}getContext(){return this.bn}resetContext(){var t=this.bn;return setTimeout(this.yn,0),t}startMeasuringScrollPosition(){qr(Bi,"scroll",this.yn,{capture:!0}),qr(Bi,"scrollend",this.yn,{capture:!0}),qr(Bi,"resize",this.yn)}scrollElement(){if(!this._instance.config.scroll_root_selector)return null==Bi?void 0:Bi.document.documentElement;var t=lr(this._instance.config.scroll_root_selector)?this._instance.config.scroll_root_selector:[this._instance.config.scroll_root_selector];for(var e of t){var i=null==Bi?void 0:Bi.document.querySelector(e);if(i)return i}}scrollY(){if(this._instance.config.scroll_root_selector){var t=this.scrollElement();return t&&t.scrollTop||0}return Bi&&(Bi.scrollY||Bi.pageYOffset||Bi.document.documentElement.scrollTop)||0}scrollX(){if(this._instance.config.scroll_root_selector){var t=this.scrollElement();return t&&t.scrollLeft||0}return Bi&&(Bi.scrollX||Bi.pageXOffset||Bi.document.documentElement.scrollLeft)||0}}var Ef=t=>tf(null==t?void 0:t.config.mask_personal_data_properties,null==t?void 0:t.config.custom_personal_data_properties);class Ff{constructor(t,e,i,r){this._n=t=>{var e=this.wn();if(!e||e.sessionId!==t){var i={sessionId:t,props:this.In(this._instance)};this.Cn.register({[Tn]:i})}},this._instance=t,this.Sn=e,this.Cn=i,this.In=r||Ef,this.Sn.onSessionId(this._n)}wn(){return this.Cn.props[Tn]}getSetOnceProps(){var t,e=null==(t=this.wn())?void 0:t.props;return e?"r"in e?ef(e):{$referring_domain:e.referringDomain,$pathname:e.initialPathName,utm_source:e.utm_source,utm_campaign:e.utm_campaign,utm_medium:e.utm_medium,utm_content:e.utm_content,utm_term:e.utm_term}:{}}getSessionProps(){var t={};return Rr(Dr(this.getSetOnceProps()),((e,i)=>{"$current_url"===i&&(i="url"),t["$session_entry_"+rr(i)]=e})),t}}var Of=Ar("[SessionId]");class Pf{constructor(t,e,i){var r;if(this.kn=[],!t.persistence)throw new Error("SessionIdManager requires a PostHogPersistence instance");if(t.config.__preview_experimental_cookieless_mode)throw new Error("SessionIdManager cannot be used with __preview_experimental_cookieless_mode");this.X=t.config,this.Cn=t.persistence,this.Je=void 0,this.pe=void 0,this._sessionStartTimestamp=null,this._sessionActivityTimestamp=null,this.xn=e||fa,this.An=i||fa;var n=this.X.persistence_name||this.X.token,o=this.X.session_idle_timeout_seconds||1800;if(this._sessionTimeoutMs=1e3*Bd(o,60,36e3,"session_idle_timeout_seconds",1800),t.register({$configured_session_timeout_ms:this._sessionTimeoutMs}),this.Tn(),this.Mn="ph_"+n+"_window_id",this.Rn="ph_"+n+"_primary_window_exists",this.Nn()){var s=Nd.kt(this.Mn),a=Nd.kt(this.Rn);s&&!a?this.Je=s:Nd.At(this.Mn),Nd.xt(this.Rn,!0)}if(null!=(r=this.X.bootstrap)&&r.sessionID)try{var l=(t=>{var e=t.replace(/-/g,"");if(32!==e.length)throw new Error("Not a valid UUID");if("7"!==e[12])throw new Error("Not a UUIDv7");return parseInt(e.substring(0,12),16)})(this.X.bootstrap.sessionID);this.En(this.X.bootstrap.sessionID,(new Date).getTime(),l)}catch(t){Of.error("Invalid sessionID in bootstrap",t)}this.Fn()}get sessionTimeoutMs(){return this._sessionTimeoutMs}onSessionId(t){return fr(this.kn)&&(this.kn=[]),this.kn.push(t),this.pe&&t(this.pe,this.Je),()=>{this.kn=this.kn.filter((e=>e!==t))}}Nn(){return"memory"!==this.X.persistence&&!this.Cn.mr&&Nd.It()}On(t){t!==this.Je&&(this.Je=t,this.Nn()&&Nd.xt(this.Mn,t))}Pn(){return this.Je?this.Je:this.Nn()?Nd.kt(this.Mn):null}En(t,e,i){t===this.pe&&e===this._sessionActivityTimestamp&&i===this._sessionStartTimestamp||(this._sessionStartTimestamp=i,this._sessionActivityTimestamp=e,this.pe=t,this.Cn.register({[pn]:[e,t,i]}))}Dn(){if(this.pe&&this._sessionActivityTimestamp&&this._sessionStartTimestamp)return[this._sessionActivityTimestamp,this.pe,this._sessionStartTimestamp];var t=this.Cn.props[pn];return lr(t)&&2===t.length&&t.push(t[0]),t||[0,null,0]}resetSessionId(){this.En(null,null,null)}Fn(){qr(Bi,"beforeunload",(()=>{this.Nn()&&Nd.At(this.Rn)}),{capture:!1})}checkAndGetSessionAndWindowId(t,e){if(void 0===t&&(t=!1),void 0===e&&(e=null),this.X.__preview_experimental_cookieless_mode)throw new Error("checkAndGetSessionAndWindowId should not be called in __preview_experimental_cookieless_mode");var i=e||(new Date).getTime(),[r,n,o]=this.Dn(),s=this.Pn(),a=br(o)&&o>0&&Math.abs(i-o)>864e5,l=!1,u=!n,h=!t&&Math.abs(i-r)>this.sessionTimeoutMs;u||h||a?(n=this.xn(),s=this.An(),Of.info("new session ID generated",{sessionId:n,windowId:s,changeReason:{noSessionId:u,activityTimeout:h,sessionPastMaximumLength:a}}),o=i,l=!0):s||(s=this.An(),l=!0);var c=0===r||!t||a?i:r,d=0===o?(new Date).getTime():o;return this.On(s),this.En(n,c,d),t||this.Tn(),l&&this.kn.forEach((t=>t(n,s,l?{noSessionId:u,activityTimeout:h,sessionPastMaximumLength:a}:void 0))),{sessionId:n,windowId:s,sessionStartTimestamp:d,changeReason:l?{noSessionId:u,activityTimeout:h,sessionPastMaximumLength:a}:void 0,lastActivityTimestamp:r}}Tn(){clearTimeout(this.Ln),this.Ln=setTimeout((()=>{this.resetSessionId()}),1.1*this.sessionTimeoutMs)}}var Df=["$set_once","$set"],Lf=Ar("[SiteApps]");class Bf{constructor(t){this._instance=t,this.Bn=[],this.apps={}}get isEnabled(){return!!this._instance.config.opt_in_site_apps}$n(t,e){if(e){var i=this.globalsForEvent(e);this.Bn.push(i),this.Bn.length>1e3&&(this.Bn=this.Bn.slice(10))}}get siteAppLoaders(){var t;return null==(t=Xi._POSTHOG_REMOTE_CONFIG)||null==(t=t[this._instance.config.token])?void 0:t.siteApps}init(){if(this.isEnabled){var t=this._instance.Nr(this.$n.bind(this));this.Zn=()=>{t(),this.Bn=[],this.Zn=void 0}}}globalsForEvent(t){var e,n,o,s,a,l,u;if(!t)throw new Error("Event payload is required");var h={},c=this._instance.get_property("$groups")||[],d=this._instance.get_property("$stored_group_properties")||{};for(var[v,f]of Object.entries(d))h[v]={id:c[v],type:v,properties:f};var{$set_once:p,$set:g}=t;return{event:i({},r(t,Df),{properties:i({},t.properties,g?{$set:i({},null!==(e=null==(n=t.properties)?void 0:n.$set)&&void 0!==e?e:{},g)}:{},p?{$set_once:i({},null!==(o=null==(s=t.properties)?void 0:s.$set_once)&&void 0!==o?o:{},p)}:{}),elements_chain:null!==(a=null==(l=t.properties)?void 0:l.$elements_chain)&&void 0!==a?a:"",distinct_id:null==(u=t.properties)?void 0:u.distinct_id}),person:{properties:this._instance.get_property("$stored_person_properties")},groups:h}}setupSiteApp(t){var e=this.apps[t.id],i=()=>{var i;(!e.errored&&this.Bn.length&&(Lf.info("Processing "+this.Bn.length+" events for site app with id "+t.id),this.Bn.forEach((t=>null==e.processEvent?void 0:e.processEvent(t))),e.processedBuffer=!0),Object.values(this.apps).every((t=>t.processedBuffer||t.errored)))&&(null==(i=this.Zn)||i.call(this))},r=!1,n=n=>{e.errored=!n,e.loaded=!0,Lf.info("Site app with id "+t.id+" "+(n?"loaded":"errored")),r&&i()};try{var{processEvent:o}=t.init({posthog:this._instance,callback:t=>{n(t)}});o&&(e.processEvent=o),r=!0}catch(e){Lf.error("Error while initializing PostHog app with config id "+t.id,e),n(!1)}if(r&&e.loaded)try{i()}catch(i){Lf.error("Error while processing buffered events PostHog app with config id "+t.id,i),e.errored=!0}}qn(){var t=this.siteAppLoaders||[];for(var e of t)this.apps[e.id]={id:e.id,loaded:!1,errored:!1,processedBuffer:!1};for(var i of t)this.setupSiteApp(i)}Hn(t){if(0!==Object.keys(this.apps).length){var e=this.globalsForEvent(t);for(var i of Object.values(this.apps))try{null==i.processEvent||i.processEvent(e)}catch(e){Lf.error("Error while processing event "+t.event+" for site app "+i.id,e)}}}onRemoteConfig(t){var e,i,r,n=this;if(null!=(e=this.siteAppLoaders)&&e.length)return this.isEnabled?(this.qn(),void this._instance.on("eventCaptured",(t=>this.Hn(t)))):void Lf.error('PostHog site apps are disabled. Enable the "opt_in_site_apps" config to proceed.');if(null==(i=this.Zn)||i.call(this),null!=(r=t.siteApps)&&r.length)if(this.isEnabled){var o=function(t){var e;Xi["__$$ph_site_app_"+t]=n._instance,null==(e=Xi.__PosthogExtensions__)||null==e.loadSiteApp||e.loadSiteApp(n._instance,a,(e=>{if(e)return Lf.error("Error while initializing PostHog app with config id "+t,e)}))};for(var{id:s,url:a}of t.siteApps)o(s)}else Lf.error('PostHog site apps are disabled. Enable the "opt_in_site_apps" config to proceed.')}}var $f=["amazonbot","amazonproductbot","app.hypefactors.com","applebot","archive.org_bot","awariobot","backlinksextendedbot","baiduspider","bingbot","bingpreview","chrome-lighthouse","dataforseobot","deepscan","duckduckbot","facebookexternal","facebookcatalog","http://yandex.com/bots","hubspot","ia_archiver","leikibot","linkedinbot","meta-externalagent","mj12bot","msnbot","nessus","petalbot","pinterest","prerender","rogerbot","screaming frog","sebot-wa","sitebulb","slackbot","slurp","trendictionbot","turnitin","twitterbot","vercelbot","yahoo! slurp","yandexbot","zoombot","bot.htm","bot.php","(bot;","bot/","crawler","ahrefsbot","ahrefssiteaudit","semrushbot","siteauditbot","splitsignalbot","gptbot","oai-searchbot","chatgpt-user","perplexitybot","better uptime bot","sentryuptimebot","uptimerobot","headlesschrome","cypress","google-hoteladsverifier","adsbot-google","apis-google","duplexweb-google","feedfetcher-google","google favicon","google web preview","google-read-aloud","googlebot","googleother","google-cloudvertexbot","googleweblight","mediapartners-google","storebot-google","google-inspectiontool","bytespider"],Zf=function(t,e){if(!t)return!1;var i=t.toLowerCase();return $f.concat(e||[]).some((t=>{var e=t.toLowerCase();return-1!==i.indexOf(e)}))},qf=function(t,e){if(!t)return!1;var i=t.userAgent;if(i&&Zf(i,e))return!0;try{var r=null==t?void 0:t.userAgentData;if(null!=r&&r.brands&&r.brands.some((t=>Zf(null==t?void 0:t.brand,e))))return!0}catch(t){}return!!t.webdriver},Hf=function(t){return t.US="us",t.EU="eu",t.CUSTOM="custom",t}({}),Vf="i.posthog.com";class jf{constructor(t){this.Vn={},this.instance=t}get apiHost(){var t=this.instance.config.api_host.trim().replace(/\/$/,"");return"https://app.posthog.com"===t?"https://us.i.posthog.com":t}get uiHost(){var t,e=null==(t=this.instance.config.ui_host)?void 0:t.replace(/\/$/,"");return e||(e=this.apiHost.replace("."+Vf,".posthog.com")),"https://app.posthog.com"===e?"https://us.posthog.com":e}get region(){return this.Vn[this.apiHost]||(/https:\/\/(app|us|us-assets)(\.i)?\.posthog\.com/i.test(this.apiHost)?this.Vn[this.apiHost]=Hf.US:/https:\/\/(eu|eu-assets)(\.i)?\.posthog\.com/i.test(this.apiHost)?this.Vn[this.apiHost]=Hf.EU:this.Vn[this.apiHost]=Hf.CUSTOM),this.Vn[this.apiHost]}endpointFor(t,e){if(void 0===e&&(e=""),e&&(e="/"===e[0]?e:"/"+e),"ui"===t)return this.uiHost+e;if(this.region===Hf.CUSTOM)return this.apiHost+e;var i=Vf+e;switch(t){case"assets":return"https://"+this.region+"-assets."+i;case"api":return"https://"+this.region+"."+i}}}var Yf={icontains:(t,e)=>!!Bi&&e.href.toLowerCase().indexOf(t.toLowerCase())>-1,not_icontains:(t,e)=>!!Bi&&-1===e.href.toLowerCase().indexOf(t.toLowerCase()),regex:(t,e)=>!!Bi&&nu(e.href,t),not_regex:(t,e)=>!!Bi&&!nu(e.href,t),exact:(t,e)=>e.href===t,is_not:(t,e)=>e.href!==t};class zf{constructor(t){var e=this;this.getWebExperimentsAndEvaluateDisplayLogic=function(t){void 0===t&&(t=!1),e.getWebExperiments((t=>{zf.jn("retrieved web experiments from the server"),e.Yn=new Map,t.forEach((t=>{if(t.feature_flag_key){var i;if(e.Yn)zf.jn("setting flag key ",t.feature_flag_key," to web experiment ",t),null==(i=e.Yn)||i.set(t.feature_flag_key,t);var r=e._instance.getFeatureFlag(t.feature_flag_key);pr(r)&&t.variants[r]&&e.zn(t.name,r,t.variants[r].transforms)}else if(t.variants)for(var n in t.variants){var o=t.variants[n];zf.Gn(o)&&e.zn(t.name,n,o.transforms)}}))}),t)},this._instance=t,this._instance.onFeatureFlags((t=>{this.onFeatureFlags(t)}))}onFeatureFlags(t){if(this._is_bot())zf.jn("Refusing to render web experiment since the viewer is a likely bot");else if(!this._instance.config.disable_web_experiments){if(yr(this.Yn))return this.Yn=new Map,this.loadIfEnabled(),void this.previewWebExperiment();zf.jn("applying feature flags",t),t.forEach((t=>{var e;if(this.Yn&&null!=(e=this.Yn)&&e.has(t)){var i,r=this._instance.getFeatureFlag(t),n=null==(i=this.Yn)?void 0:i.get(t);r&&null!=n&&n.variants[r]&&this.zn(n.name,r,n.variants[r].transforms)}}))}}previewWebExperiment(){var t=zf.getWindowLocation();if(null!=t&&t.search){var e=Yr(null==t?void 0:t.search,"__experiment_id"),i=Yr(null==t?void 0:t.search,"__experiment_variant");e&&i&&(zf.jn("previewing web experiments "+e+" && "+i),this.getWebExperiments((t=>{this.Wn(parseInt(e),i,t)}),!1,!0))}}loadIfEnabled(){this._instance.config.disable_web_experiments||this.getWebExperimentsAndEvaluateDisplayLogic()}getWebExperiments(t,e,i){if(this._instance.config.disable_web_experiments&&!i)return t([]);var r=this._instance.get_property("$web_experiments");if(r&&!e)return t(r);this._instance.ur({url:this._instance.requestRouter.endpointFor("api","/api/web_experiments/?token="+this._instance.config.token),method:"GET",callback:e=>{if(200!==e.statusCode||!e.json)return t([]);var i=e.json.experiments||[];return t(i)}})}Wn(t,e,i){var r=i.filter((e=>e.id===t));r&&r.length>0&&(zf.jn("Previewing web experiment ["+r[0].name+"] with variant ["+e+"]"),this.zn(r[0].name,e,r[0].variants[e].transforms))}static Gn(t){return!yr(t.conditions)&&(zf.Un(t)&&zf.Xn(t))}static Un(t){var e;if(yr(t.conditions)||yr(null==(e=t.conditions)?void 0:e.url))return!0;var i,r,n,o=zf.getWindowLocation();return!!o&&(null==(i=t.conditions)||!i.url||Yf[null!==(r=null==(n=t.conditions)?void 0:n.urlMatchType)&&void 0!==r?r:"icontains"](t.conditions.url,o))}static getWindowLocation(){return null==Bi?void 0:Bi.location}static Xn(t){var e;if(yr(t.conditions)||yr(null==(e=t.conditions)?void 0:e.utm))return!0;var i=Uv();if(i.utm_source){var r,n,o,s,a,l,u,h,c=null==(r=t.conditions)||null==(r=r.utm)||!r.utm_campaign||(null==(n=t.conditions)||null==(n=n.utm)?void 0:n.utm_campaign)==i.utm_campaign,d=null==(o=t.conditions)||null==(o=o.utm)||!o.utm_source||(null==(s=t.conditions)||null==(s=s.utm)?void 0:s.utm_source)==i.utm_source,v=null==(a=t.conditions)||null==(a=a.utm)||!a.utm_medium||(null==(l=t.conditions)||null==(l=l.utm)?void 0:l.utm_medium)==i.utm_medium,f=null==(u=t.conditions)||null==(u=u.utm)||!u.utm_term||(null==(h=t.conditions)||null==(h=h.utm)?void 0:h.utm_term)==i.utm_term;return c&&v&&f&&d}return!1}static jn(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];xr.info("[WebExperiments] "+t,i)}zn(t,e,i){this._is_bot()?zf.jn("Refusing to render web experiment since the viewer is a likely bot"):"control"!==e?i.forEach((i=>{if(i.selector){var r;zf.jn("applying transform of variant "+e+" for experiment "+t+" ",i);var n=null==(r=document)?void 0:r.querySelectorAll(i.selector);null==n||n.forEach((t=>{var e=t;i.html&&(e.innerHTML=i.html),i.css&&e.setAttribute("style",i.css)}))}})):zf.jn("Control variants leave the page unmodified.")}_is_bot(){return Vi&&this._instance?qf(Vi,this._instance.config.custom_blocked_useragents):void 0}}var Gf={},Wf=()=>{},Uf="posthog",Xf=!Kl&&-1===(null==Ui?void 0:Ui.indexOf("MSIE"))&&-1===(null==Ui?void 0:Ui.indexOf("Mozilla")),Jf=t=>{var e;return{api_host:"https://us.i.posthog.com",ui_host:null,token:"",autocapture:!0,rageclick:!0,cross_subdomain_cookie:$r(null==ji?void 0:ji.location),persistence:"localStorage+cookie",persistence_name:"",loaded:Wf,save_campaign_params:!0,custom_campaign_params:[],custom_blocked_useragents:[],save_referrer:!0,capture_pageview:"2025-05-24"!==t||"history_change",capture_pageleave:"if_capture_pageview",defaults:null!=t?t:"unset",debug:Yi&&pr(null==Yi?void 0:Yi.search)&&-1!==Yi.search.indexOf("__posthog_debug=true")||!1,cookie_expiration:365,upgrade:!1,disable_session_recording:!1,disable_persistence:!1,disable_web_experiments:!0,disable_surveys:!1,disable_surveys_automatic_display:!1,disable_external_dependency_loading:!1,enable_recording_console_log:void 0,secure_cookie:"https:"===(null==Bi||null==(e=Bi.location)?void 0:e.protocol),ip:!0,opt_out_capturing_by_default:!1,opt_out_persistence_by_default:!1,opt_out_useragent_filter:!1,opt_out_capturing_persistence_type:"localStorage",opt_out_capturing_cookie_prefix:null,opt_in_site_apps:!1,property_denylist:[],respect_dnt:!1,sanitize_properties:null,request_headers:{},request_batching:!0,properties_string_max_length:65535,session_recording:{},mask_all_element_attributes:!1,mask_all_text:!1,mask_personal_data_properties:!1,custom_personal_data_properties:[],advanced_disable_flags:!1,advanced_disable_decide:!1,advanced_disable_feature_flags:!1,advanced_disable_feature_flags_on_first_load:!1,advanced_only_evaluate_survey_feature_flags:!1,advanced_disable_toolbar_metrics:!1,feature_flag_request_timeout_ms:3e3,surveys_request_timeout_ms:1e4,on_request_error:t=>{var e="Bad HTTP status: "+t.statusCode+" "+t.text;xr.error(e)},get_device_id:t=>t,capture_performance:void 0,name:"posthog",bootstrap:{},disable_compression:!1,session_idle_timeout_seconds:1800,person_profiles:"identified_only",before_send:void 0,request_queue_config:{flush_interval_ms:Af},error_tracking:{},_onCapture:Wf}},Kf=t=>{var e={};fr(t.process_person)||(e.person_profiles=t.process_person),fr(t.xhr_headers)||(e.request_headers=t.xhr_headers),fr(t.cookie_name)||(e.persistence_name=t.cookie_name),fr(t.disable_cookie)||(e.disable_persistence=t.disable_cookie),fr(t.store_google)||(e.save_campaign_params=t.store_google),fr(t.verbose)||(e.debug=t.verbose);var i=Nr({},e,t);return lr(t.property_blacklist)&&(fr(t.property_denylist)?i.property_denylist=t.property_blacklist:lr(t.property_denylist)?i.property_denylist=[...t.property_blacklist,...t.property_denylist]:xr.error("Invalid value for property_denylist config: "+t.property_denylist)),i};class Qf{constructor(){this.__forceAllowLocalhost=!1}get Jn(){return this.__forceAllowLocalhost}set Jn(t){xr.error("WebPerformanceObserver is deprecated and has no impact on network capture. Use `_forceAllowLocalhostNetworkCapture` on `posthog.sessionRecording`"),this.__forceAllowLocalhost=t}}class tp{get decideEndpointWasHit(){var t,e;return null!==(t=null==(e=this.featureFlags)?void 0:e.hasLoadedFlags)&&void 0!==t&&t}get flagsEndpointWasHit(){var t,e;return null!==(t=null==(e=this.featureFlags)?void 0:e.hasLoadedFlags)&&void 0!==t&&t}constructor(){this.webPerformance=new Qf,this.Kn=!1,this.version=Sr.LIB_VERSION,this.Qn=new bf,this._calculate_event_properties=this.calculateEventProperties.bind(this),this.config=Jf(),this.SentryIntegration=Av,this.sentryIntegration=t=>function(t,e){var i=xv(t,e);return{name:kv,processEvent:t=>i(t)}}(this,t),this.__request_queue=[],this.__loaded=!1,this.analyticsDefaultEndpoint="/e/",this.eo=!1,this.io=null,this.ro=null,this.no=null,this.featureFlags=new gf(this),this.toolbar=new Ev(this),this.scrollManager=new Nf(this),this.pageViewManager=new qv(this),this.surveys=new If(this),this.experiments=new zf(this),this.exceptions=new Vv(this),this.rateLimiter=new Sf(this),this.requestRouter=new jf(this),this.consent=new Fd(this),this.people={set:(t,e,i)=>{var r=pr(t)?{[t]:e}:t;this.setPersonProperties(r),null==i||i({})},set_once:(t,e,i)=>{var r=pr(t)?{[t]:e}:t;this.setPersonProperties(void 0,r),null==i||i({})}},this.on("eventCaptured",(t=>xr.info('send "'+(null==t?void 0:t.event)+'"',t)))}init(t,e,i){if(i&&i!==Uf){var r,n=null!==(r=Gf[i])&&void 0!==r?r:new tp;return n._init(t,e,i),Gf[i]=n,Gf[Uf][i]=n,n}return this._init(t,e,i)}_init(t,e,r){var n,o;if(void 0===e&&(e={}),fr(t)||gr(t))return xr.critical("PostHog was initialized without a token. This likely indicates a misconfiguration. Please check the first argument passed to posthog.init()"),this;if(this.__loaded)return xr.warn("You have already initialized PostHog! Re-initializing is a no-op"),this;this.__loaded=!0,this.config={},this.oo=e,this.so=[],e.person_profiles&&(this.ro=e.person_profiles),this.set_config(Nr({},Jf(e.defaults),Kf(e),{name:r,token:t})),this.config.on_xhr_error&&xr.error("on_xhr_error is deprecated. Use on_request_error instead"),this.compression=e.disable_compression?void 0:Qi.GZipJS,this.persistence=new yf(this.config),this.sessionPersistence="sessionStorage"===this.config.persistence||"memory"===this.config.persistence?this.persistence:new yf(i({},this.config,{persistence:"sessionStorage"}));var s=i({},this.persistence.props),a=i({},this.sessionPersistence.props);if(this.register({$initialization_time:(new Date).toISOString()}),this.ao=new Tf((t=>this.lo(t)),this.config.request_queue_config),this.uo=new Rf(this),this.__request_queue=[],this.config.__preview_experimental_cookieless_mode||(this.sessionManager=new Pf(this),this.sessionPropsManager=new Ff(this,this.sessionManager,this.persistence)),new Ov(this).startIfEnabledOrStop(),this.siteApps=new Bf(this),null==(n=this.siteApps)||n.init(),this.config.__preview_experimental_cookieless_mode||(this.sessionRecording=new Iv(this),this.sessionRecording.startIfEnabledOrStop()),this.config.disable_scroll_properties||this.scrollManager.startMeasuringScrollPosition(),this.autocapture=new vd(this),this.autocapture.startIfEnabled(),this.surveys.loadIfEnabled(),this.heatmaps=new Zv(this),this.heatmaps.startIfEnabled(),this.webVitalsAutocapture=new Lv(this),this.exceptionObserver=new qd(this),this.exceptionObserver.startIfEnabled(),this.deadClicksAutocapture=new Ld(this,Dd),this.deadClicksAutocapture.startIfEnabled(),this.historyAutocapture=new Hd(this),this.historyAutocapture.startIfEnabled(),Sr.DEBUG=Sr.DEBUG||this.config.debug,Sr.DEBUG&&xr.info("Starting in debug mode",{this:this,config:e,thisC:i({},this.config),p:s,s:a}),this.ho(),void 0!==(null==(o=e.bootstrap)?void 0:o.distinctID)){var l,u,h=this.config.get_device_id(fa()),c=null!=(l=e.bootstrap)&&l.isIdentifiedID?h:e.bootstrap.distinctID;this.persistence.set_property(An,null!=(u=e.bootstrap)&&u.isIdentifiedID?"identified":"anonymous"),this.register({distinct_id:e.bootstrap.distinctID,$device_id:c})}if(this.co()){var d,v,f=Object.keys((null==(d=e.bootstrap)?void 0:d.featureFlags)||{}).filter((t=>{var i;return!(null==(i=e.bootstrap)||null==(i=i.featureFlags)||!i[t])})).reduce(((t,i)=>{var r;return t[i]=(null==(r=e.bootstrap)||null==(r=r.featureFlags)?void 0:r[i])||!1,t}),{}),p=Object.keys((null==(v=e.bootstrap)?void 0:v.featureFlagPayloads)||{}).filter((t=>f[t])).reduce(((t,i)=>{var r,n;null!=(r=e.bootstrap)&&null!=(r=r.featureFlagPayloads)&&r[i]&&(t[i]=null==(n=e.bootstrap)||null==(n=n.featureFlagPayloads)?void 0:n[i]);return t}),{});this.featureFlags.receivedFeatureFlags({featureFlags:f,featureFlagPayloads:p})}if(this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:Pn,$device_id:null},"");else if(!this.get_distinct_id()){var g=this.config.get_device_id(fa());this.register_once({distinct_id:g,$device_id:g},""),this.persistence.set_property(An,"anonymous")}return qr(Bi,"onpagehide"in self?"pagehide":"unload",this._handle_unload.bind(this),{passive:!1}),this.toolbar.maybeLoadToolbar(),e.segment?Sv(this,(()=>this.do())):this.do(),ur(this.config._onCapture)&&this.config._onCapture!==Wf&&(xr.warn("onCapture is deprecated. Please use `before_send` instead"),this.on("eventCaptured",(t=>this.config._onCapture(t.event,t)))),this}hr(t){var e,i,r,n,o,s,a,l;if(!ji||!ji.body)return xr.info("document not ready yet, trying again in 500 milliseconds..."),void setTimeout((()=>{this.hr(t)}),500);this.compression=void 0,t.supportedCompression&&!this.config.disable_compression&&(this.compression=er(t.supportedCompression,Qi.GZipJS)?Qi.GZipJS:er(t.supportedCompression,Qi.Base64)?Qi.Base64:void 0),null!=(e=t.analytics)&&e.endpoint&&(this.analyticsDefaultEndpoint=t.analytics.endpoint),this.set_config({person_profiles:this.ro?this.ro:"identified_only"}),null==(i=this.siteApps)||i.onRemoteConfig(t),null==(r=this.sessionRecording)||r.onRemoteConfig(t),null==(n=this.autocapture)||n.onRemoteConfig(t),null==(o=this.heatmaps)||o.onRemoteConfig(t),this.surveys.onRemoteConfig(t),null==(s=this.webVitalsAutocapture)||s.onRemoteConfig(t),null==(a=this.exceptionObserver)||a.onRemoteConfig(t),this.exceptions.onRemoteConfig(t),null==(l=this.deadClicksAutocapture)||l.onRemoteConfig(t)}do(){try{this.config.loaded(this)}catch(t){xr.critical("`loaded` function failed",t)}this.vo(),this.config.capture_pageview&&setTimeout((()=>{this.consent.isOptedIn()&&this.fo()}),1),new xf(this).load(),this.featureFlags.flags()}vo(){var t;this.has_opted_out_capturing()||this.config.request_batching&&(null==(t=this.ao)||t.enable())}_dom_loaded(){this.has_opted_out_capturing()||Mr(this.__request_queue,(t=>this.lo(t))),this.__request_queue=[],this.vo()}_handle_unload(){var t,e;this.config.request_batching?(this.po()&&this.capture("$pageleave"),null==(t=this.ao)||t.unload(),null==(e=this.uo)||e.unload()):this.po()&&this.capture("$pageleave",null,{transport:"sendBeacon"})}ur(t){this.__loaded&&(Xf?this.__request_queue.push(t):this.rateLimiter.isServerRateLimited(t.batchKey)||(t.transport=t.transport||this.config.api_transport,t.url=tu(t.url,{ip:this.config.ip?1:0}),t.headers=i({},this.config.request_headers),t.compression="best-available"===t.compression?this.compression:t.compression,t.fetchOptions=t.fetchOptions||this.config.fetch_options,(t=>{var e,r,n,o=i({},t);o.timeout=o.timeout||6e4,o.url=tu(o.url,{_:(new Date).getTime().toString(),ver:Sr.LIB_VERSION,compression:o.compression});var s=null!==(e=o.transport)&&void 0!==e?e:"fetch",a=null!==(r=null==(n=Zr(ru,(t=>t.transport===s)))?void 0:n.method)&&void 0!==r?r:ru[0].method;if(!a)throw new Error("No available transport method");a(o)})(i({},t,{callback:e=>{var i,r;(this.rateLimiter.checkForLimiting(e),e.statusCode>=400)&&(null==(i=(r=this.config).on_request_error)||i.call(r,e));null==t.callback||t.callback(e)}}))))}lo(t){this.uo?this.uo.retriableRequest(t):this.ur(t)}_execute_array(t){var e,i=[],r=[],n=[];Mr(t,(t=>{t&&(e=t[0],lr(e)?n.push(t):ur(t)?t.call(this):lr(t)&&"alias"===e?i.push(t):lr(t)&&-1!==e.indexOf("capture")&&ur(this[e])?n.push(t):r.push(t))}));var o=function(t,e){Mr(t,(function(t){if(lr(t[0])){var i=e;Rr(t,(function(t){i=i[t[0]].apply(i,t.slice(1))}))}else this[t[0]].apply(this,t.slice(1))}),e)};o(i,this),o(r,this),o(n,this)}co(){var t,e;return(null==(t=this.config.bootstrap)?void 0:t.featureFlags)&&Object.keys(null==(e=this.config.bootstrap)?void 0:e.featureFlags).length>0||!1}push(t){this._execute_array([t])}capture(t,e,r){var n;if(this.__loaded&&this.persistence&&this.sessionPersistence&&this.ao){if(!this.consent.isOptedOut())if(!fr(t)&&pr(t)){if(this.config.opt_out_useragent_filter||!this._is_bot()){var o=null!=r&&r.skip_client_rate_limiting?void 0:this.rateLimiter.clientRateLimitContext();if(null==o||!o.isRateLimited){null!=e&&e.$current_url&&!pr(null==e?void 0:e.$current_url)&&(xr.error("Invalid `$current_url` property provided to `posthog.capture`. Input must be a string. Ignoring provided value."),null==e||delete e.$current_url),this.sessionPersistence.update_search_keyword(),this.config.save_campaign_params&&this.sessionPersistence.update_campaign_params(),this.config.save_referrer&&this.sessionPersistence.update_referrer_info(),(this.config.save_campaign_params||this.config.save_referrer)&&this.persistence.set_initial_person_info();var s=new Date,a=(null==r?void 0:r.timestamp)||s,l=fa(),u={uuid:l,event:t,properties:this.calculateEventProperties(t,e||{},a,l)};o&&(u.properties.$lib_rate_limit_remaining_tokens=o.remainingTokens),(null==r?void 0:r.$set)&&(u.$set=null==r?void 0:r.$set);var h,c,d=this.mo(null==r?void 0:r.$set_once);if(d&&(u.$set_once=d),(u=Lr(u,null!=r&&r._noTruncate?null:this.config.properties_string_max_length)).timestamp=a,fr(null==r?void 0:r.timestamp)||(u.properties.$event_time_override_provided=!0,u.properties.$event_time_override_system_time=s),t===Qs.DISMISSED||t===Qs.SENT){var v=null==e?void 0:e[ta.SURVEY_ID],f=null==e?void 0:e[ta.SURVEY_ITERATION];localStorage.setItem((c=""+oa+(h={id:v,current_iteration:f}).id,h.current_iteration&&h.current_iteration>0&&(c=""+oa+h.id+"_"+h.current_iteration),c),"true"),u.$set=i({},u.$set,{[aa({id:v,current_iteration:f},t===Qs.SENT?"responded":"dismissed")]:!0})}var p=i({},u.properties.$set,u.$set);if(vr(p)||this.setPersonPropertiesForFlags(p),!yr(this.config.before_send)){var g=this.yo(u);if(!g)return;u=g}this.Qn.emit("eventCaptured",u);var m={method:"POST",url:null!==(n=null==r?void 0:r._url)&&void 0!==n?n:this.requestRouter.endpointFor("api",this.analyticsDefaultEndpoint),data:u,compression:"best-available",batchKey:null==r?void 0:r._batchKey};return!this.config.request_batching||r&&(null==r||!r._batchKey)||null!=r&&r.send_instantly?this.lo(m):this.ao.enqueue(m),u}xr.critical("This capture call is ignored due to client rate limiting.")}}else xr.error("No event name provided to posthog.capture")}else xr.uninitializedWarning("posthog.capture")}Nr(t){return this.on("eventCaptured",(e=>t(e.event,e)))}calculateEventProperties(t,e,r,n,o){if(r=r||new Date,!this.persistence||!this.sessionPersistence)return e;var s=o?void 0:this.persistence.remove_event_timer(t),a=i({},e);if(a.token=this.config.token,a.$config_defaults=this.config.defaults,this.config.__preview_experimental_cookieless_mode&&(a.$cookieless_mode=!0),"$snapshot"===t){var l=i({},this.persistence.properties(),this.sessionPersistence.properties());return a.distinct_id=l.distinct_id,(!pr(a.distinct_id)&&!br(a.distinct_id)||gr(a.distinct_id))&&xr.error("Invalid distinct_id for replay event. This indicates a bug in your implementation"),a}var u,h=of(this.config.mask_personal_data_properties,this.config.custom_personal_data_properties);if(this.sessionManager){var{sessionId:c,windowId:d}=this.sessionManager.checkAndGetSessionAndWindowId(o,r.getTime());a.$session_id=c,a.$window_id=d}this.sessionPropsManager&&Nr(a,this.sessionPropsManager.getSessionProps());try{var v;this.sessionRecording&&Nr(a,this.sessionRecording.sdkDebugProperties),a.$sdk_debug_retry_queue_size=null==(v=this.uo)?void 0:v.length}catch(t){a.$sdk_debug_error_capturing_properties=String(t)}if(this.requestRouter.region===Hf.CUSTOM&&(a.$lib_custom_api_host=this.config.api_host),u="$pageview"!==t||o?"$pageleave"!==t||o?this.pageViewManager.doEvent():this.pageViewManager.doPageLeave(r):this.pageViewManager.doPageView(r,n),a=Nr(a,u),"$pageview"===t&&ji&&(a.title=ji.title),!fr(s)){var f=r.getTime()-s;a.$duration=parseFloat((f/1e3).toFixed(3))}Ui&&this.config.opt_out_useragent_filter&&(a.$browser_type=this._is_bot()?"bot":"browser"),(a=Nr({},h,this.persistence.properties(),this.sessionPersistence.properties(),a)).$is_identified=this._isIdentified(),lr(this.config.property_denylist)?Rr(this.config.property_denylist,(function(t){delete a[t]})):xr.error("Invalid value for property_denylist config: "+this.config.property_denylist+" or property_blacklist config: "+this.config.property_blacklist);var p=this.config.sanitize_properties;p&&(xr.error("sanitize_properties is deprecated. Use before_send instead"),a=p(a,t));var g=this.bo();return a.$process_person_profile=g,g&&!o&&this._o("_calculate_event_properties"),a}mo(t){var e;if(!this.persistence||!this.bo())return t;if(this.Kn)return t;var i=this.persistence.get_initial_props(),r=null==(e=this.sessionPropsManager)?void 0:e.getSetOnceProps(),n=Nr({},i,r||{},t||{}),o=this.config.sanitize_properties;return o&&(xr.error("sanitize_properties is deprecated. Use before_send instead"),n=o(n,"$set_once")),this.Kn=!0,vr(n)?void 0:n}register(t,e){var i;null==(i=this.persistence)||i.register(t,e)}register_once(t,e,i){var r;null==(r=this.persistence)||r.register_once(t,e,i)}register_for_session(t){var e;null==(e=this.sessionPersistence)||e.register(t)}unregister(t){var e;null==(e=this.persistence)||e.unregister(t)}unregister_for_session(t){var e;null==(e=this.sessionPersistence)||e.unregister(t)}wo(t,e){this.register({[t]:e})}getFeatureFlag(t,e){return this.featureFlags.getFeatureFlag(t,e)}getFeatureFlagPayload(t){var e=this.featureFlags.getFeatureFlagPayload(t);try{return JSON.parse(e)}catch(t){return e}}isFeatureEnabled(t,e){return this.featureFlags.isFeatureEnabled(t,e)}reloadFeatureFlags(){this.featureFlags.reloadFeatureFlags()}updateEarlyAccessFeatureEnrollment(t,e){this.featureFlags.updateEarlyAccessFeatureEnrollment(t,e)}getEarlyAccessFeatures(t,e,i){return void 0===e&&(e=!1),this.featureFlags.getEarlyAccessFeatures(t,e,i)}on(t,e){return this.Qn.on(t,e)}onFeatureFlags(t){return this.featureFlags.onFeatureFlags(t)}onSurveysLoaded(t){return this.surveys.onSurveysLoaded(t)}onSessionId(t){var e,i;return null!==(e=null==(i=this.sessionManager)?void 0:i.onSessionId(t))&&void 0!==e?e:()=>{}}getSurveys(t,e){void 0===e&&(e=!1),this.surveys.getSurveys(t,e)}getActiveMatchingSurveys(t,e){void 0===e&&(e=!1),this.surveys.getActiveMatchingSurveys(t,e)}renderSurvey(t,e){this.surveys.renderSurvey(t,e)}canRenderSurvey(t){return this.surveys.canRenderSurvey(t)}canRenderSurveyAsync(t,e){return void 0===e&&(e=!1),this.surveys.canRenderSurveyAsync(t,e)}identify(t,e,r){if(!this.__loaded||!this.persistence)return xr.uninitializedWarning("posthog.identify");if(br(t)&&(t=t.toString(),xr.warn("The first argument to posthog.identify was a number, but it should be a string. It has been converted to a string.")),t)if(["distinct_id","distinctid"].includes(t.toLowerCase()))xr.critical('The string "'+t+'" was set in posthog.identify which indicates an error. This ID should be unique to the user and not a hardcoded string.');else if(t!==Pn){if(this._o("posthog.identify")){var n=this.get_distinct_id();if(this.register({$user_id:t}),!this.get_property("$device_id")){var o=n;this.register_once({$had_persisted_distinct_id:!0,$device_id:o},"")}t!==n&&t!==this.get_property(Jr)&&(this.unregister(Jr),this.register({distinct_id:t}));var s="anonymous"===(this.persistence.get_property(An)||"anonymous");t!==n&&s?(this.persistence.set_property(An,"identified"),this.setPersonPropertiesForFlags(i({},r||{},e||{}),!1),this.capture("$identify",{distinct_id:t,$anon_distinct_id:n},{$set:e||{},$set_once:r||{}}),this.no=ou(t,e,r),this.featureFlags.setAnonymousDistinctId(n)):(e||r)&&this.setPersonProperties(e,r),t!==n&&(this.reloadFeatureFlags(),this.unregister(xn))}}else xr.critical('The string "'+Pn+'" was set in posthog.identify which indicates an error. This ID is only used as a sentinel value.');else xr.error("Unique user id has not been set in posthog.identify")}setPersonProperties(t,e){if((t||e)&&this._o("posthog.setPersonProperties")){var r=ou(this.get_distinct_id(),t,e);this.no!==r?(this.setPersonPropertiesForFlags(i({},e||{},t||{})),this.capture("$set",{$set:t||{},$set_once:e||{}}),this.no=r):xr.info("A duplicate setPersonProperties call was made with the same properties. It has been ignored.")}}group(t,e,r){if(t&&e){if(this._o("posthog.group")){var n=this.getGroups();n[t]!==e&&this.resetGroupPropertiesForFlags(t),this.register({$groups:i({},n,{[t]:e})}),r&&(this.capture("$groupidentify",{$group_type:t,$group_key:e,$group_set:r}),this.setGroupPropertiesForFlags({[t]:r})),n[t]===e||r||this.reloadFeatureFlags()}}else xr.error("posthog.group requires a group type and group key")}resetGroups(){this.register({$groups:{}}),this.resetGroupPropertiesForFlags(),this.reloadFeatureFlags()}setPersonPropertiesForFlags(t,e){void 0===e&&(e=!0),this.featureFlags.setPersonPropertiesForFlags(t,e)}resetPersonPropertiesForFlags(){this.featureFlags.resetPersonPropertiesForFlags()}setGroupPropertiesForFlags(t,e){void 0===e&&(e=!0),this._o("posthog.setGroupPropertiesForFlags")&&this.featureFlags.setGroupPropertiesForFlags(t,e)}resetGroupPropertiesForFlags(t){this.featureFlags.resetGroupPropertiesForFlags(t)}reset(t){var e,i,r,n;if(xr.info("reset"),!this.__loaded)return xr.uninitializedWarning("posthog.reset");var o=this.get_property("$device_id");if(this.consent.reset(),null==(e=this.persistence)||e.clear(),null==(i=this.sessionPersistence)||i.clear(),this.surveys.reset(),null==(r=this.persistence)||r.set_property(An,"anonymous"),null==(n=this.sessionManager)||n.resetSessionId(),this.no=null,this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:Pn,$device_id:null},"");else{var s=this.config.get_device_id(fa());this.register_once({distinct_id:s,$device_id:t?s:o},"")}this.register({$last_posthog_reset:(new Date).toISOString()},1)}get_distinct_id(){return this.get_property("distinct_id")}getGroups(){return this.get_property("$groups")||{}}get_session_id(){var t,e;return null!==(t=null==(e=this.sessionManager)?void 0:e.checkAndGetSessionAndWindowId(!0).sessionId)&&void 0!==t?t:""}get_session_replay_url(t){if(!this.sessionManager)return"";var{sessionId:e,sessionStartTimestamp:i}=this.sessionManager.checkAndGetSessionAndWindowId(!0),r=this.requestRouter.endpointFor("ui","/project/"+this.config.token+"/replay/"+e);if(null!=t&&t.withTimestamp&&i){var n,o=null!==(n=t.timestampLookBack)&&void 0!==n?n:10;if(!i)return r;r+="?t="+Math.max(Math.floor(((new Date).getTime()-i)/1e3)-o,0)}return r}alias(t,e){return t===this.get_property(Xr)?(xr.critical("Attempting to create alias for existing People user - aborting."),-2):this._o("posthog.alias")?(fr(e)&&(e=this.get_distinct_id()),t!==e?(this.wo(Jr,t),this.capture("$create_alias",{alias:t,distinct_id:e})):(xr.warn("alias matches current distinct_id - skipping api call."),this.identify(t),-1)):void 0}set_config(t){var e,r,n,o,s=i({},this.config);dr(t)&&(Nr(this.config,Kf(t)),null==(e=this.persistence)||e.update_config(this.config,s),this.sessionPersistence="sessionStorage"===this.config.persistence||"memory"===this.config.persistence?this.persistence:new yf(i({},this.config,{persistence:"sessionStorage"})),kd.It()&&"true"===kd.St("ph_debug")&&(this.config.debug=!0),this.config.debug&&(Sr.DEBUG=!0,xr.info("set_config",{config:t,oldConfig:s,newConfig:i({},this.config)})),null==(r=this.sessionRecording)||r.startIfEnabledOrStop(),null==(n=this.autocapture)||n.startIfEnabled(),null==(o=this.heatmaps)||o.startIfEnabled(),this.surveys.loadIfEnabled(),this.ho())}startSessionRecording(t){var e=!0===t,i={sampling:e||!(null==t||!t.sampling),linked_flag:e||!(null==t||!t.linked_flag),url_trigger:e||!(null==t||!t.url_trigger),event_trigger:e||!(null==t||!t.event_trigger)};if(Object.values(i).some(Boolean)){var r,n,o,s,a;if(null==(r=this.sessionManager)||r.checkAndGetSessionAndWindowId(),i.sampling)null==(n=this.sessionRecording)||n.overrideSampling();if(i.linked_flag)null==(o=this.sessionRecording)||o.overrideLinkedFlag();if(i.url_trigger)null==(s=this.sessionRecording)||s.overrideTrigger("url");if(i.event_trigger)null==(a=this.sessionRecording)||a.overrideTrigger("event")}this.set_config({disable_session_recording:!1})}stopSessionRecording(){this.set_config({disable_session_recording:!0})}sessionRecordingStarted(){var t;return!(null==(t=this.sessionRecording)||!t.started)}captureException(t,e){var r=new Error("PostHog syntheticException");this.exceptions.sendExceptionEvent(i({},ic((t=>t instanceof Error)(t)?{error:t,event:t.message}:{event:t},{syntheticException:r}),e))}loadToolbar(t){return this.toolbar.loadToolbar(t)}get_property(t){var e;return null==(e=this.persistence)?void 0:e.props[t]}getSessionProperty(t){var e;return null==(e=this.sessionPersistence)?void 0:e.props[t]}toString(){var t,e=null!==(t=this.config.name)&&void 0!==t?t:Uf;return e!==Uf&&(e=Uf+"."+e),e}_isIdentified(){var t,e;return"identified"===(null==(t=this.persistence)?void 0:t.get_property(An))||"identified"===(null==(e=this.sessionPersistence)?void 0:e.get_property(An))}bo(){var t,e;return!("never"===this.config.person_profiles||"identified_only"===this.config.person_profiles&&!this._isIdentified()&&vr(this.getGroups())&&(null==(t=this.persistence)||null==(t=t.props)||!t[Jr])&&(null==(e=this.persistence)||null==(e=e.props)||!e[Fn]))}po(){return!0===this.config.capture_pageleave||"if_capture_pageview"===this.config.capture_pageleave&&(!0===this.config.capture_pageview||"history_change"===this.config.capture_pageview)}createPersonProfile(){this.bo()||this._o("posthog.createPersonProfile")&&this.setPersonProperties({},{})}_o(t){return"never"===this.config.person_profiles?(xr.error(t+' was called, but process_person is set to "never". This call will be ignored.'),!1):(this.wo(Fn,!0),!0)}ho(){var t,e,i,r,n=this.consent.isOptedOut(),o=this.config.opt_out_persistence_by_default,s=this.config.disable_persistence||n&&!!o;(null==(t=this.persistence)?void 0:t.mr)!==s&&(null==(i=this.persistence)||i.set_disabled(s));(null==(e=this.sessionPersistence)?void 0:e.mr)!==s&&(null==(r=this.sessionPersistence)||r.set_disabled(s))}opt_in_capturing(t){var e;(this.consent.optInOut(!0),this.ho(),fr(null==t?void 0:t.captureEventName)||null!=t&&t.captureEventName)&&this.capture(null!==(e=null==t?void 0:t.captureEventName)&&void 0!==e?e:"$opt_in",null==t?void 0:t.captureProperties,{send_instantly:!0});this.config.capture_pageview&&this.fo()}opt_out_capturing(){this.consent.optInOut(!1),this.ho()}has_opted_in_capturing(){return this.consent.isOptedIn()}has_opted_out_capturing(){return this.consent.isOptedOut()}clear_opt_in_out_capturing(){this.consent.reset(),this.ho()}_is_bot(){return Vi?qf(Vi,this.config.custom_blocked_useragents):void 0}fo(){ji&&("visible"===ji.visibilityState?this.eo||(this.eo=!0,this.capture("$pageview",{title:ji.title},{send_instantly:!0}),this.io&&(ji.removeEventListener("visibilitychange",this.io),this.io=null)):this.io||(this.io=this.fo.bind(this),qr(ji,"visibilitychange",this.io)))}debug(t){!1===t?(null==Bi||Bi.console.log("You've disabled debug mode."),localStorage&&localStorage.removeItem("ph_debug"),this.set_config({debug:!1})):(null==Bi||Bi.console.log("You're now in debug mode. All calls to PostHog will be logged in your console.\nYou can disable this with `posthog.debug(false)`."),localStorage&&localStorage.setItem("ph_debug","true"),this.set_config({debug:!0}))}tt(){var t,e,i,r,n,o,s,a=this.oo||{};return"advanced_disable_flags"in a?!!a.advanced_disable_flags:!1!==this.config.advanced_disable_flags?!!this.config.advanced_disable_flags:!0===this.config.advanced_disable_decide?(xr.warn("Config field 'advanced_disable_decide' is deprecated. Please use 'advanced_disable_flags' instead. The old field will be removed in a future major version."),!0):(i="advanced_disable_decide",r=!1,n=xr,o=(e="advanced_disable_flags")in(t=a)&&!fr(t[e]),s=i in t&&!fr(t[i]),o?t[e]:s?(n&&n.warn("Config field '"+i+"' is deprecated. Please use '"+e+"' instead. The old field will be removed in a future major version."),t[i]):r)}yo(t){if(yr(this.config.before_send))return t;var e=lr(this.config.before_send)?this.config.before_send:[this.config.before_send],i=t;for(var r of e){if(i=r(i),yr(i)){var n="Event '"+t.event+"' was rejected in beforeSend function";return Cr(t.event)?xr.warn(n+". This can cause unexpected behavior."):xr.info(n),null}i.properties&&!vr(i.properties)||xr.warn("Event '"+t.event+"' has no properties after beforeSend function, this is likely an error.")}return i}getPageViewId(){var t;return null==(t=this.pageViewManager.Ui)?void 0:t.pageViewId}captureTraceFeedback(t,e){this.capture("$ai_feedback",{$ai_trace_id:String(t),$ai_feedback_text:e})}captureTraceMetric(t,e,i){this.capture("$ai_metric",{$ai_trace_id:String(t),$ai_metric_name:e,$ai_metric_value:String(i)})}}!function(t,e){for(var i=0;i<e.length;i++)t.prototype[e[i]]=Pr(t.prototype[e[i]])}(tp,["identify"]);var ep,ip=(ep=Gf[Uf]=new tp,function(){function t(){t.done||(t.done=!0,Xf=!1,Rr(Gf,(function(t){t._dom_loaded()})))}null!=ji&&ji.addEventListener?"complete"===ji.readyState?t():qr(ji,"DOMContentLoaded",t,{capture:!1}):Bi&&xr.error("Browser doesn't support `document.addEventListener` so PostHog couldn't be initialized")}(),ep);export{Ji as COPY_AUTOCAPTURE_EVENT,Qi as Compression,tp as PostHog,Qs as SurveyEventName,ta as SurveyEventProperties,Ws as SurveyPosition,Js as SurveyQuestionBranchingType,Xs as SurveyQuestionType,Ks as SurveySchedule,Us as SurveyType,Gs as SurveyWidgetType,ip as default,Ki as knownUnsafeEditableEvent,ip as posthog,tr as severityLevels};
//# sourceMappingURL=module.full.js.map
