Object.defineProperty(exports, '__esModule', { value: true });

const baseclient = require('./baseclient.js');
const checkin = require('./checkin.js');
const currentScopes = require('./currentScopes.js');
const debugBuild = require('./debug-build.js');
const sessionflusher = require('./sessionflusher.js');
const errors = require('./tracing/errors.js');
require('./utils-hoist/debug-build.js');
const logger = require('./utils-hoist/logger.js');
require('./utils-hoist/time.js');
const spanOnScope = require('./utils/spanOnScope.js');
const spanUtils = require('./utils/spanUtils.js');
const misc = require('./utils-hoist/misc.js');
const syncpromise = require('./utils-hoist/syncpromise.js');
const eventbuilder = require('./utils-hoist/eventbuilder.js');
const dynamicSamplingContext = require('./tracing/dynamicSamplingContext.js');

/**
 * The Sentry Server Runtime Client SDK.
 */
class ServerRuntimeClient

 extends baseclient.BaseClient {
  // eslint-disable-next-line deprecation/deprecation

  /**
   * Creates a new Edge SDK instance.
   * @param options Configuration options for this SDK.
   */
   constructor(options) {
    // Server clients always support tracing
    errors.registerSpanErrorInstrumentation();

    super(options);
  }

  /**
   * @inheritDoc
   */
   eventFromException(exception, hint) {
    const event = eventbuilder.eventFromUnknownInput(this, this._options.stackParser, exception, hint);
    event.level = 'error';

    return syncpromise.resolvedSyncPromise(event);
  }

  /**
   * @inheritDoc
   */
   eventFromMessage(
    message,
    level = 'info',
    hint,
  ) {
    return syncpromise.resolvedSyncPromise(
      eventbuilder.eventFromMessage(this._options.stackParser, message, level, hint, this._options.attachStacktrace),
    );
  }

  /**
   * @inheritDoc
   */
   captureException(exception, hint, scope) {
    // Check if `_sessionFlusher` exists because it is initialized (defined) only when the `autoSessionTracking` is enabled.
    // The expectation is that session aggregates are only sent when `autoSessionTracking` is enabled.
    // TODO(v9): Our goal in the future is to not have the `autoSessionTracking` option and instead rely on integrations doing the creation and sending of sessions. We will not have a central kill-switch for sessions.
    // TODO(v9): This should move into the httpIntegration.
    // eslint-disable-next-line deprecation/deprecation
    if (this._options.autoSessionTracking && this._sessionFlusher) {
      // eslint-disable-next-line deprecation/deprecation
      const requestSession = currentScopes.getIsolationScope().getRequestSession();

      // Necessary checks to ensure this is code block is executed only within a request
      // Should override the status only if `requestSession.status` is `Ok`, which is its initial stage
      if (requestSession && requestSession.status === 'ok') {
        requestSession.status = 'errored';
      }
    }

    return super.captureException(exception, hint, scope);
  }

  /**
   * @inheritDoc
   */
   captureEvent(event, hint, scope) {
    // Check if `_sessionFlusher` exists because it is initialized only when the `autoSessionTracking` is enabled.
    // The expectation is that session aggregates are only sent when `autoSessionTracking` is enabled.
    // TODO(v9): Our goal in the future is to not have the `autoSessionTracking` option and instead rely on integrations doing the creation and sending of sessions. We will not have a central kill-switch for sessions.
    // TODO(v9): This should move into the httpIntegration.
    // eslint-disable-next-line deprecation/deprecation
    if (this._options.autoSessionTracking && this._sessionFlusher) {
      const eventType = event.type || 'exception';
      const isException =
        eventType === 'exception' && event.exception && event.exception.values && event.exception.values.length > 0;

      // If the event is of type Exception, then a request session should be captured
      if (isException) {
        // eslint-disable-next-line deprecation/deprecation
        const requestSession = currentScopes.getIsolationScope().getRequestSession();

        // Ensure that this is happening within the bounds of a request, and make sure not to override
        // Session Status if Errored / Crashed
        if (requestSession && requestSession.status === 'ok') {
          requestSession.status = 'errored';
        }
      }
    }

    return super.captureEvent(event, hint, scope);
  }

  /**
   *
   * @inheritdoc
   */
   close(timeout) {
    if (this._sessionFlusher) {
      this._sessionFlusher.close();
    }
    return super.close(timeout);
  }

  /**
   * Initializes an instance of SessionFlusher on the client which will aggregate and periodically flush session data.
   *
   * NOTICE: This method will implicitly create an interval that is periodically called.
   * To clean up this resources, call `.close()` when you no longer intend to use the client.
   * Not doing so will result in a memory leak.
   */
   initSessionFlusher() {
    const { release, environment } = this._options;
    if (!release) {
      debugBuild.DEBUG_BUILD && logger.logger.warn('Cannot initialize an instance of SessionFlusher if no release is provided!');
    } else {
      // eslint-disable-next-line deprecation/deprecation
      this._sessionFlusher = new sessionflusher.SessionFlusher(this, {
        release,
        environment,
      });
    }
  }

  /**
   * Create a cron monitor check in and send it to Sentry.
   *
   * @param checkIn An object that describes a check in.
   * @param upsertMonitorConfig An optional object that describes a monitor config. Use this if you want
   * to create a monitor automatically when sending a check in.
   */
   captureCheckIn(checkIn, monitorConfig, scope) {
    const id = 'checkInId' in checkIn && checkIn.checkInId ? checkIn.checkInId : misc.uuid4();
    if (!this._isEnabled()) {
      debugBuild.DEBUG_BUILD && logger.logger.warn('SDK not enabled, will not capture checkin.');
      return id;
    }

    const options = this.getOptions();
    const { release, environment, tunnel } = options;

    const serializedCheckIn = {
      check_in_id: id,
      monitor_slug: checkIn.monitorSlug,
      status: checkIn.status,
      release,
      environment,
    };

    if ('duration' in checkIn) {
      serializedCheckIn.duration = checkIn.duration;
    }

    if (monitorConfig) {
      serializedCheckIn.monitor_config = {
        schedule: monitorConfig.schedule,
        checkin_margin: monitorConfig.checkinMargin,
        max_runtime: monitorConfig.maxRuntime,
        timezone: monitorConfig.timezone,
        failure_issue_threshold: monitorConfig.failureIssueThreshold,
        recovery_threshold: monitorConfig.recoveryThreshold,
      };
    }

    const [dynamicSamplingContext, traceContext] = this._getTraceInfoFromScope(scope);
    if (traceContext) {
      serializedCheckIn.contexts = {
        trace: traceContext,
      };
    }

    const envelope = checkin.createCheckInEnvelope(
      serializedCheckIn,
      dynamicSamplingContext,
      this.getSdkMetadata(),
      tunnel,
      this.getDsn(),
    );

    debugBuild.DEBUG_BUILD && logger.logger.info('Sending checkin:', checkIn.monitorSlug, checkIn.status);

    // sendEnvelope should not throw
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    this.sendEnvelope(envelope);

    return id;
  }

  /**
   * Method responsible for capturing/ending a request session by calling `incrementSessionStatusCount` to increment
   * appropriate session aggregates bucket
   *
   * @deprecated This method should not be used or extended. It's functionality will move into the `httpIntegration` and not be part of any public API.
   */
   _captureRequestSession() {
    if (!this._sessionFlusher) {
      debugBuild.DEBUG_BUILD && logger.logger.warn('Discarded request mode session because autoSessionTracking option was disabled');
    } else {
      this._sessionFlusher.incrementSessionStatusCount();
    }
  }

  /**
   * @inheritDoc
   */
   _prepareEvent(
    event,
    hint,
    scope,
    isolationScope,
  ) {
    if (this._options.platform) {
      event.platform = event.platform || this._options.platform;
    }

    if (this._options.runtime) {
      event.contexts = {
        ...event.contexts,
        runtime: (event.contexts || {}).runtime || this._options.runtime,
      };
    }

    if (this._options.serverName) {
      event.server_name = event.server_name || this._options.serverName;
    }

    return super._prepareEvent(event, hint, scope, isolationScope);
  }

  /** Extract trace information from scope */
   _getTraceInfoFromScope(
    scope,
  ) {
    if (!scope) {
      return [undefined, undefined];
    }

    const span = spanOnScope._getSpanForScope(scope);

    const traceContext = span ? spanUtils.spanToTraceContext(span) : currentScopes.getTraceContextFromScope(scope);
    const dynamicSamplingContext$1 = span
      ? dynamicSamplingContext.getDynamicSamplingContextFromSpan(span)
      : dynamicSamplingContext.getDynamicSamplingContextFromScope(this, scope);
    return [dynamicSamplingContext$1, traceContext];
  }
}

exports.ServerRuntimeClient = ServerRuntimeClient;
//# sourceMappingURL=server-runtime-client.js.map
