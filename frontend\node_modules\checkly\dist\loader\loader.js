"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnsupportedFileLoaderError = exports.FileLoader = void 0;
const match_1 = require("./match");
class FileLoader {
    fileMatcher;
    constructor(options) {
        this.fileMatcher = options?.match ?? match_1.FileMatch.any();
    }
    /**
     * Checks whether the FileLoader can be used for a file path.
     *
     * @param filePath The file path to evaluate.
     * @returns Whether the FileLoader is authoritative for the file path.
     */
    isAuthoritativeFor(filePath) {
        return this.fileMatcher.match(filePath);
    }
}
exports.FileLoader = FileLoader;
/**
 * Error thrown when a FileLoader is authoritative for a file path but
 * fails to load it.
 */
class UnsupportedFileLoaderError extends Error {
    constructor(message = 'File cannot be loaded by this loader', options) {
        super(message, options);
        this.name = 'UnsupportedFileLoaderError';
    }
}
exports.UnsupportedFileLoaderError = UnsupportedFileLoaderError;
//# sourceMappingURL=loader.js.map