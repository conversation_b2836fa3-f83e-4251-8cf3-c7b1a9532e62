"use strict";
// Copyright 2021-2024 The Connect Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.createConnectTransport = createConnectTransport;
const protocol_connect_1 = require("@connectrpc/connect/protocol-connect");
const node_transport_options_js_1 = require("./node-transport-options.js");
/**
 * Create a Transport for the Connect protocol using the Node.js `http`, `http2`,
 * or `http2` module.
 */
function createConnectTransport(options) {
    return (0, protocol_connect_1.createTransport)((0, node_transport_options_js_1.validateNodeTransportOptions)(options));
}
