export { Logger } from './types/Logger';
export { LoggerProvider } from './types/LoggerProvider';
export { LogAttributes, LogBody, LogRecord, SeverityNumber, } from './types/LogRecord';
export { LoggerOptions } from './types/LoggerOptions';
export { AnyValue, AnyValueMap } from './types/AnyValue';
export { NOOP_LOGGER, NoopLogger } from './NoopLogger';
export { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';
export { ProxyLogger } from './ProxyLogger';
export { ProxyLoggerProvider } from './ProxyLoggerProvider';
import { LogsAPI } from './api/logs';
export declare const logs: LogsAPI;
//# sourceMappingURL=index.d.ts.map