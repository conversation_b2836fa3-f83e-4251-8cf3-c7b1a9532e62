"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BrowserCheckCodegen = void 0;
const codegen_1 = require("./internal/codegen");
const sourcegen_1 = require("../sourcegen");
const check_codegen_1 = require("./check-codegen");
const playwright_config_codegen_1 = require("./playwright-config-codegen");
const construct = 'BrowserCheck';
class BrowserCheckCodegen extends codegen_1.Codegen {
    describe(resource) {
        return `Browser Check: ${resource.name}`;
    }
    gencode(logicalId, resource, context) {
        const filePath = context.filePath('resources/browser-checks', resource.name, {
            tags: resource.tags,
            isolate: true,
            unique: true,
        });
        const file = this.program.generatedConstructFile(filePath.fullPath);
        file.namedImport(construct, 'checkly/constructs');
        file.section((0, sourcegen_1.expr)((0, sourcegen_1.ident)(construct), builder => {
            builder.new(builder => {
                builder.string(logicalId);
                builder.object(builder => {
                    builder.object('code', builder => {
                        (0, codegen_1.validateScript)(resource.script);
                        const snippetFiles = context.findScriptSnippetFiles(resource.script);
                        for (const snippetFile of snippetFiles) {
                            const localSnippetFile = this.program.generatedSupportFile(`${file.dirname}/snippets/${snippetFile.basename}`);
                            localSnippetFile.plainImport(localSnippetFile.relativePath(snippetFile));
                        }
                        const scriptFile = this.program.staticSpecFile(filePath.extless, resource.script);
                        builder.string('entrypoint', file.relativePath(scriptFile));
                    });
                    if (resource.sslCheckDomain !== undefined && resource.sslCheckDomain !== null && resource.sslCheckDomain !== '') {
                        builder.string('sslCheckDomain', resource.sslCheckDomain);
                    }
                    if (resource.playwrightConfig) {
                        builder.value('playwrightConfig', (0, playwright_config_codegen_1.valueForPlaywrightConfig)(resource.playwrightConfig));
                    }
                    (0, check_codegen_1.buildCheckProps)(this.program, file, builder, resource, context);
                });
            });
        }));
    }
}
exports.BrowserCheckCodegen = BrowserCheckCodegen;
//# sourceMappingURL=browser-check-codegen.js.map