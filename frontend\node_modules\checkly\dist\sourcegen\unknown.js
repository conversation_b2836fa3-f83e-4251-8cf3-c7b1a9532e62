"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.unknown = void 0;
const arraybuilder_1 = require("./arraybuilder");
const boolean_1 = require("./boolean");
const null_1 = require("./null");
const number_1 = require("./number");
const objectbuilder_1 = require("./objectbuilder");
const string_1 = require("./string");
const undefined_1 = require("./undefined");
function unknown(value) {
    if (value === null) {
        return new null_1.NullValue();
    }
    if (value === undefined) {
        return new undefined_1.UndefinedValue();
    }
    switch (typeof value) {
        case 'string':
            return new string_1.StringValue(value);
        case 'boolean':
            return new boolean_1.BooleanValue(value);
        case 'number':
            return new number_1.NumberValue(value);
    }
    if (Array.isArray(value)) {
        return (0, arraybuilder_1.array)(builder => {
            for (const item of value) {
                builder.value(unknown(item));
            }
        });
    }
    return (0, objectbuilder_1.object)(builder => {
        for (const [key, val] of Object.entries(value)) {
            builder.value(key, unknown(val));
        }
    });
}
exports.unknown = unknown;
//# sourceMappingURL=unknown.js.map