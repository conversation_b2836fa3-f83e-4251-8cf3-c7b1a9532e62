{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/node_modules_4d6690fd._.js", "server/edge/chunks/[root-of-the-server]__92ca1197._.js", "server/edge/chunks/edge-wrapper_28f5b268.js", "server/edge/chunks/_858adf1d._.js", "server/edge/chunks/node_modules_@bufbuild_protobuf_dist_esm_e8606cf1._.js", "server/edge/chunks/node_modules_@arcjet_protocol_0c492877._.js", "server/edge/chunks/node_modules_@connectrpc_connect_dist_esm_2e5be2d1._.js", "server/edge/chunks/node_modules_@clerk_shared_dist_7041d53c._.js", "server/edge/chunks/node_modules_@clerk_backend_dist_0bec9097._.js", "server/edge/chunks/node_modules_@clerk_nextjs_dist_esm_521d2976._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_@clerk_localizations_dist_index_mjs_84e70144._.js", "server/edge/chunks/node_modules_954a0ee8._.js", "server/edge/chunks/[root-of-the-server]__48506fa0._.js", "server/edge/chunks/edge-wrapper_c68aefd2.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|_vercel|monitoring|.*\\..*).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|_vercel|monitoring|.*\\..*).*)"}], "wasm": [{"name": "wasm_dd92d_modules__arcjet_analyze_wasm_wasm_arcjet_analyze_js_req_component_core_325b988e", "filePath": "server/edge/chunks/dd92d_modules_@arcjet_analyze-wasm_wasm_arcjet_analyze_js_req_component_core_325b988e.wasm"}, {"name": "wasm_8069e__arcjet_analyze_wasm_wasm_arcjet_analyze_js_req_component_core2_325b988e", "filePath": "server/edge/chunks/8069e_@arcjet_analyze-wasm_wasm_arcjet_analyze_js_req_component_core2_325b988e.wasm"}, {"name": "wasm_8069e__arcjet_analyze_wasm_wasm_arcjet_analyze_js_req_component_core3_325b988e", "filePath": "server/edge/chunks/8069e_@arcjet_analyze-wasm_wasm_arcjet_analyze_js_req_component_core3_325b988e.wasm"}], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "BIV2oZKLbyuhZbVZhsIbazN16CLMohyljsJiCWRarK8=", "__NEXT_PREVIEW_MODE_ID": "b9910d5cc251440e58df4bccaa59bfc5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "60aed82f905e68c152451f56d4f360d0ba74e38d48ed5f1e62b9e32624fbad63", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d2758d39a272c9a4d72b2af2fc44fcdbfcca32094f8ce4d248bc37978b79190e"}}}, "sortedMiddleware": ["/"], "functions": {}}