{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/node_modules_4d6690fd._.js", "server/edge/chunks/[root-of-the-server]__92ca1197._.js", "server/edge/chunks/edge-wrapper_28f5b268.js", "server/edge/chunks/_858adf1d._.js", "server/edge/chunks/node_modules_@bufbuild_protobuf_dist_esm_e8606cf1._.js", "server/edge/chunks/node_modules_@arcjet_protocol_0c492877._.js", "server/edge/chunks/node_modules_@connectrpc_connect_dist_esm_2e5be2d1._.js", "server/edge/chunks/node_modules_@clerk_shared_dist_7041d53c._.js", "server/edge/chunks/node_modules_@clerk_backend_dist_0bec9097._.js", "server/edge/chunks/node_modules_@clerk_nextjs_dist_esm_521d2976._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_@clerk_localizations_dist_index_mjs_84e70144._.js", "server/edge/chunks/node_modules_954a0ee8._.js", "server/edge/chunks/[root-of-the-server]__48506fa0._.js", "server/edge/chunks/edge-wrapper_c68aefd2.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|_vercel|monitoring|.*\\..*).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|_vercel|monitoring|.*\\..*).*)"}], "wasm": [{"name": "wasm_8069e__arcjet_analyze_wasm_wasm_arcjet_analyze_js_req_component_core2_325b988e", "filePath": "server/edge/chunks/8069e_@arcjet_analyze-wasm_wasm_arcjet_analyze_js_req_component_core2_325b988e.wasm"}, {"name": "wasm_8069e__arcjet_analyze_wasm_wasm_arcjet_analyze_js_req_component_core3_325b988e", "filePath": "server/edge/chunks/8069e_@arcjet_analyze-wasm_wasm_arcjet_analyze_js_req_component_core3_325b988e.wasm"}, {"name": "wasm_dd92d_modules__arcjet_analyze_wasm_wasm_arcjet_analyze_js_req_component_core_325b988e", "filePath": "server/edge/chunks/dd92d_modules_@arcjet_analyze-wasm_wasm_arcjet_analyze_js_req_component_core_325b988e.wasm"}], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "BIV2oZKLbyuhZbVZhsIbazN16CLMohyljsJiCWRarK8=", "__NEXT_PREVIEW_MODE_ID": "5f25b2d22aa28d3d6360bce6282cc212", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "35890c272d3ad0650ebfccbe29be65498f6f03c4f9981b9673334c7b11c915d0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e2ab3a201fa42266b802b53f7f16c32da2f5ce4709aea3e660a2d92f51d40d5c"}}}, "sortedMiddleware": ["/"], "functions": {}}