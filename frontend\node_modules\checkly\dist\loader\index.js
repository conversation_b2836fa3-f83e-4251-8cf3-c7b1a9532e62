"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TSNodeFileLoader = exports.hasNativeTypeScriptSupport = exports.detectNativeTypeScriptSupport = exports.DenoDetector = exports.BunDetector = exports.NativeFileLoader = exports.MixedFileLoader = exports.FileMatch = exports.UnsupportedFileLoaderError = exports.FileLoader = exports.JitiFileLoader = void 0;
var jiti_1 = require("./jiti");
Object.defineProperty(exports, "JitiFileLoader", { enumerable: true, get: function () { return jiti_1.JitiFileLoader; } });
var loader_1 = require("./loader");
Object.defineProperty(exports, "FileLoader", { enumerable: true, get: function () { return loader_1.FileLoader; } });
Object.defineProperty(exports, "UnsupportedFileLoaderError", { enumerable: true, get: function () { return loader_1.UnsupportedFileLoaderError; } });
var match_1 = require("./match");
Object.defineProperty(exports, "FileMatch", { enumerable: true, get: function () { return match_1.FileMatch; } });
var mixed_1 = require("./mixed");
Object.defineProperty(exports, "MixedFileLoader", { enumerable: true, get: function () { return mixed_1.MixedFileLoader; } });
var native_1 = require("./native");
Object.defineProperty(exports, "NativeFileLoader", { enumerable: true, get: function () { return native_1.NativeFileLoader; } });
Object.defineProperty(exports, "BunDetector", { enumerable: true, get: function () { return native_1.BunDetector; } });
Object.defineProperty(exports, "DenoDetector", { enumerable: true, get: function () { return native_1.DenoDetector; } });
Object.defineProperty(exports, "detectNativeTypeScriptSupport", { enumerable: true, get: function () { return native_1.detectNativeTypeScriptSupport; } });
Object.defineProperty(exports, "hasNativeTypeScriptSupport", { enumerable: true, get: function () { return native_1.hasNativeTypeScriptSupport; } });
var ts_node_1 = require("./ts-node");
Object.defineProperty(exports, "TSNodeFileLoader", { enumerable: true, get: function () { return ts_node_1.TSNodeFileLoader; } });
//# sourceMappingURL=index.js.map