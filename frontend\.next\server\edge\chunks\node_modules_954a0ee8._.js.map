{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/typeid-js/node_modules/uuid/dist/esm-browser/regex.js"], "sourcesContent": ["export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i;"], "names": [], "mappings": ";;;uCAAe", "ignoreList": [0]}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/typeid-js/node_modules/uuid/dist/esm-browser/validate.js"], "sourcesContent": ["import REGEX from './regex.js';\nfunction validate(uuid) {\n  return typeof uuid === 'string' && REGEX.test(uuid);\n}\nexport default validate;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,IAAI;IACpB,OAAO,OAAO,SAAS,YAAY,+LAAA,CAAA,UAAK,CAAC,IAAI,CAAC;AAChD;uCACe", "ignoreList": [0]}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/typeid-js/node_modules/uuid/dist/esm-browser/stringify.js"], "sourcesContent": ["import validate from './validate.js';\n\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\nvar byteToHex = [];\nfor (var i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nexport function unsafeStringify(arr, offset = 0) {\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  //\n  // Note to future-self: No, you can't remove the `toLowerCase()` call.\n  // REF: https://github.com/uuidjs/uuid/pull/677#issuecomment-1757351351\n  return (byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n  var uuid = unsafeStringify(arr, offset);\n  // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n  if (!validate(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n  return uuid;\n}\nexport default stringify;"], "names": [], "mappings": ";;;;AAAA;;AAEA;;;CAGC,GACD,IAAI,YAAY,EAAE;AAClB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;IAC5B,UAAU,IAAI,CAAC,CAAC,IAAI,KAAK,EAAE,QAAQ,CAAC,IAAI,KAAK,CAAC;AAChD;AACO,SAAS,gBAAgB,GAAG,EAAE,SAAS,CAAC;IAC7C,uEAAuE;IACvE,oFAAoF;IACpF,EAAE;IACF,sEAAsE;IACtE,uEAAuE;IACvE,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,MAAM,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,MAAM,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,MAAM,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,MAAM,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,EAAE,WAAW;AAClgB;AACA,SAAS,UAAU,GAAG,EAAE,SAAS,CAAC;IAChC,IAAI,OAAO,gBAAgB,KAAK;IAChC,4EAA4E;IAC5E,oBAAoB;IACpB,wEAAwE;IACxE,2BAA2B;IAC3B,mEAAmE;IACnE,IAAI,CAAC,CAAA,GAAA,kMAAA,CAAA,UAAQ,AAAD,EAAE,OAAO;QACnB,MAAM,UAAU;IAClB;IACA,OAAO;AACT;uCACe", "ignoreList": [0]}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/typeid-js/node_modules/uuid/dist/esm-browser/rng.js"], "sourcesContent": ["// Unique ID creation requires a high quality random # generator. In the browser we therefore\n// require the crypto API and do not support built-in fallback to lower quality random number\n// generators (like Math.random()).\n\nvar getRandomValues;\nvar rnds8 = new Uint8Array(16);\nexport default function rng() {\n  // lazy load so that environments that need to polyfill have a chance to do so\n  if (!getRandomValues) {\n    // getRandomValues needs to be invoked in a context where \"this\" is a Crypto implementation.\n    getRandomValues = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto);\n    if (!getRandomValues) {\n      throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n    }\n  }\n  return getRandomValues(rnds8);\n}"], "names": [], "mappings": "AAAA,6FAA6F;AAC7F,6FAA6F;AAC7F,mCAAmC;;;;AAEnC,IAAI;AACJ,IAAI,QAAQ,IAAI,WAAW;AACZ,SAAS;IACtB,8EAA8E;IAC9E,IAAI,CAAC,iBAAiB;QACpB,4FAA4F;QAC5F,kBAAkB,OAAO,WAAW,eAAe,OAAO,eAAe,IAAI,OAAO,eAAe,CAAC,IAAI,CAAC;QACzG,IAAI,CAAC,iBAAiB;YACpB,MAAM,IAAI,MAAM;QAClB;IACF;IACA,OAAO,gBAAgB;AACzB", "ignoreList": [0]}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/typeid-js/node_modules/uuid/dist/esm-browser/v7.js"], "sourcesContent": ["import rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\n\n/**\n * UUID V7 - Unix Epoch time-based UUID\n *\n * The IETF has published RFC9562, introducing 3 new UUID versions (6,7,8). This\n * implementation of V7 is based on the accepted, though not yet approved,\n * revisions.\n *\n * RFC 9562:https://www.rfc-editor.org/rfc/rfc9562.html Universally Unique\n * IDentifiers (UUIDs)\n\n *\n * Sample V7 value:\n * https://www.rfc-editor.org/rfc/rfc9562.html#name-example-of-a-uuidv7-value\n *\n * Monotonic Bit Layout: RFC rfc9562.6.2 Method 1, Dedicated Counter Bits ref:\n *     https://www.rfc-editor.org/rfc/rfc9562.html#section-6.2-5.1\n *\n *   0                   1                   2                   3 0 1 2 3 4 5 6\n *   7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1\n *  +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+\n *  |                          unix_ts_ms                           |\n *  +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+\n *  |          unix_ts_ms           |  ver  |        seq_hi         |\n *  +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+\n *  |var|               seq_low               |        rand         |\n *  +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+\n *  |                             rand                              |\n *  +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+\n *\n * seq is a 31 bit serialized counter; comprised of 12 bit seq_hi and 19 bit\n * seq_low, and randomly initialized upon timestamp change. 31 bit counter size\n * was selected as any bitwise operations in node are done as _signed_ 32 bit\n * ints. we exclude the sign bit.\n */\n\nvar _seqLow = null;\nvar _seqHigh = null;\nvar _msecs = 0;\nfunction v7(options, buf, offset) {\n  options = options || {};\n\n  // initialize buffer and pointer\n  var i = buf && offset || 0;\n  var b = buf || new Uint8Array(16);\n\n  // rnds is Uint8Array(16) filled with random bytes\n  var rnds = options.random || (options.rng || rng)();\n\n  // milliseconds since unix epoch, 1970-01-01 00:00\n  var msecs = options.msecs !== undefined ? options.msecs : Date.now();\n\n  // seq is user provided 31 bit counter\n  var seq = options.seq !== undefined ? options.seq : null;\n\n  // initialize local seq high/low parts\n  var seqHigh = _seqHigh;\n  var seqLow = _seqLow;\n\n  // check if clock has advanced and user has not provided msecs\n  if (msecs > _msecs && options.msecs === undefined) {\n    _msecs = msecs;\n\n    // unless user provided seq, reset seq parts\n    if (seq !== null) {\n      seqHigh = null;\n      seqLow = null;\n    }\n  }\n\n  // if we have a user provided seq\n  if (seq !== null) {\n    // trim provided seq to 31 bits of value, avoiding overflow\n    if (seq > 0x7fffffff) {\n      seq = 0x7fffffff;\n    }\n\n    // split provided seq into high/low parts\n    seqHigh = seq >>> 19 & 0xfff;\n    seqLow = seq & 0x7ffff;\n  }\n\n  // randomly initialize seq\n  if (seqHigh === null || seqLow === null) {\n    seqHigh = rnds[6] & 0x7f;\n    seqHigh = seqHigh << 8 | rnds[7];\n    seqLow = rnds[8] & 0x3f; // pad for var\n    seqLow = seqLow << 8 | rnds[9];\n    seqLow = seqLow << 5 | rnds[10] >>> 3;\n  }\n\n  // increment seq if within msecs window\n  if (msecs + 10000 > _msecs && seq === null) {\n    if (++seqLow > 0x7ffff) {\n      seqLow = 0;\n      if (++seqHigh > 0xfff) {\n        seqHigh = 0;\n\n        // increment internal _msecs. this allows us to continue incrementing\n        // while staying monotonic. Note, once we hit 10k milliseconds beyond system\n        // clock, we will reset breaking monotonicity (after (2^31)*10000 generations)\n        _msecs++;\n      }\n    }\n  } else {\n    // resetting; we have advanced more than\n    // 10k milliseconds beyond system clock\n    _msecs = msecs;\n  }\n  _seqHigh = seqHigh;\n  _seqLow = seqLow;\n\n  // [bytes 0-5] 48 bits of local timestamp\n  b[i++] = _msecs / 0x10000000000 & 0xff;\n  b[i++] = _msecs / 0x100000000 & 0xff;\n  b[i++] = _msecs / 0x1000000 & 0xff;\n  b[i++] = _msecs / 0x10000 & 0xff;\n  b[i++] = _msecs / 0x100 & 0xff;\n  b[i++] = _msecs & 0xff;\n\n  // [byte 6] - set 4 bits of version (7) with first 4 bits seq_hi\n  b[i++] = seqHigh >>> 4 & 0x0f | 0x70;\n\n  // [byte 7] remaining 8 bits of seq_hi\n  b[i++] = seqHigh & 0xff;\n\n  // [byte 8] - variant (2 bits), first 6 bits seq_low\n  b[i++] = seqLow >>> 13 & 0x3f | 0x80;\n\n  // [byte 9] 8 bits seq_low\n  b[i++] = seqLow >>> 5 & 0xff;\n\n  // [byte 10] remaining 5 bits seq_low, 3 bits random\n  b[i++] = seqLow << 3 & 0xff | rnds[10] & 0x07;\n\n  // [bytes 11-15] always random\n  b[i++] = rnds[11];\n  b[i++] = rnds[12];\n  b[i++] = rnds[13];\n  b[i++] = rnds[14];\n  b[i++] = rnds[15];\n  return buf || unsafeStringify(b);\n}\nexport default v7;"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAiCC,GAED,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,SAAS;AACb,SAAS,GAAG,OAAO,EAAE,GAAG,EAAE,MAAM;IAC9B,UAAU,WAAW,CAAC;IAEtB,gCAAgC;IAChC,IAAI,IAAI,OAAO,UAAU;IACzB,IAAI,IAAI,OAAO,IAAI,WAAW;IAE9B,kDAAkD;IAClD,IAAI,OAAO,QAAQ,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,6LAAA,CAAA,UAAG;IAEhD,kDAAkD;IAClD,IAAI,QAAQ,QAAQ,KAAK,KAAK,YAAY,QAAQ,KAAK,GAAG,KAAK,GAAG;IAElE,sCAAsC;IACtC,IAAI,MAAM,QAAQ,GAAG,KAAK,YAAY,QAAQ,GAAG,GAAG;IAEpD,sCAAsC;IACtC,IAAI,UAAU;IACd,IAAI,SAAS;IAEb,8DAA8D;IAC9D,IAAI,QAAQ,UAAU,QAAQ,KAAK,KAAK,WAAW;QACjD,SAAS;QAET,4CAA4C;QAC5C,IAAI,QAAQ,MAAM;YAChB,UAAU;YACV,SAAS;QACX;IACF;IAEA,iCAAiC;IACjC,IAAI,QAAQ,MAAM;QAChB,2DAA2D;QAC3D,IAAI,MAAM,YAAY;YACpB,MAAM;QACR;QAEA,yCAAyC;QACzC,UAAU,QAAQ,KAAK;QACvB,SAAS,MAAM;IACjB;IAEA,0BAA0B;IAC1B,IAAI,YAAY,QAAQ,WAAW,MAAM;QACvC,UAAU,IAAI,CAAC,EAAE,GAAG;QACpB,UAAU,WAAW,IAAI,IAAI,CAAC,EAAE;QAChC,SAAS,IAAI,CAAC,EAAE,GAAG,MAAM,cAAc;QACvC,SAAS,UAAU,IAAI,IAAI,CAAC,EAAE;QAC9B,SAAS,UAAU,IAAI,IAAI,CAAC,GAAG,KAAK;IACtC;IAEA,uCAAuC;IACvC,IAAI,QAAQ,QAAQ,UAAU,QAAQ,MAAM;QAC1C,IAAI,EAAE,SAAS,SAAS;YACtB,SAAS;YACT,IAAI,EAAE,UAAU,OAAO;gBACrB,UAAU;gBAEV,qEAAqE;gBACrE,4EAA4E;gBAC5E,8EAA8E;gBAC9E;YACF;QACF;IACF,OAAO;QACL,wCAAwC;QACxC,uCAAuC;QACvC,SAAS;IACX;IACA,WAAW;IACX,UAAU;IAEV,yCAAyC;IACzC,CAAC,CAAC,IAAI,GAAG,SAAS,gBAAgB;IAClC,CAAC,CAAC,IAAI,GAAG,SAAS,cAAc;IAChC,CAAC,CAAC,IAAI,GAAG,SAAS,YAAY;IAC9B,CAAC,CAAC,IAAI,GAAG,SAAS,UAAU;IAC5B,CAAC,CAAC,IAAI,GAAG,SAAS,QAAQ;IAC1B,CAAC,CAAC,IAAI,GAAG,SAAS;IAElB,gEAAgE;IAChE,CAAC,CAAC,IAAI,GAAG,YAAY,IAAI,OAAO;IAEhC,sCAAsC;IACtC,CAAC,CAAC,IAAI,GAAG,UAAU;IAEnB,oDAAoD;IACpD,CAAC,CAAC,IAAI,GAAG,WAAW,KAAK,OAAO;IAEhC,0BAA0B;IAC1B,CAAC,CAAC,IAAI,GAAG,WAAW,IAAI;IAExB,oDAAoD;IACpD,CAAC,CAAC,IAAI,GAAG,UAAU,IAAI,OAAO,IAAI,CAAC,GAAG,GAAG;IAEzC,8BAA8B;IAC9B,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG;IACjB,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG;IACjB,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG;IACjB,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG;IACjB,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG;IACjB,OAAO,OAAO,CAAA,GAAA,mMAAA,CAAA,kBAAe,AAAD,EAAE;AAChC;uCACe", "ignoreList": [0]}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/typeid-js/src/typeid.ts", "turbopack:///[project]/node_modules/typeid-js/src/parse_uuid.ts", "turbopack:///[project]/node_modules/typeid-js/src/base32.ts", "turbopack:///[project]/node_modules/typeid-js/src/unboxed/typeid.ts", "turbopack:///[project]/node_modules/typeid-js/src/prefix.ts", "turbopack:///[project]/node_modules/typeid-js/src/unboxed/error.ts"], "sourcesContent": ["import { stringify } from \"uuid\";\nimport { parseUUID } from \"./parse_uuid\";\nimport { encode, decode } from \"./base32\";\nimport {\n  typeidUnboxed,\n  getSuffix,\n  getType,\n  fromString,\n} from \"./unboxed/typeid\";\nimport { TypeIDConversionError } from \"./unboxed/error\";\n\nexport class TypeID<const T extends string> {\n  constructor(private prefix: T, private suffix: string = \"\") {\n    const typeIdRaw = typeidUnboxed(prefix, suffix);\n\n    this.prefix = getType(typeIdRaw);\n    this.suffix = getSuffix(typeIdRaw);\n  }\n\n  public getType(): T {\n    return this.prefix;\n  }\n\n  public getSuffix(): string {\n    return this.suffix;\n  }\n\n  public asType<const U extends string>(prefix: U): TypeID<U> {\n    const self = this as unknown as TypeID<U>;\n    if (self.prefix !== prefix) {\n      throw new TypeIDConversionError(self.prefix, prefix);\n    }\n    return self;\n  }\n\n  public toUUIDBytes(): Uint8Array {\n    return decode(this.suffix);\n  }\n\n  public toUUID(): string {\n    return stringify(this.toUUIDBytes());\n  }\n\n  public toString(): T extends \"\" ? string : `${T}_${string}` {\n    if (this.prefix === \"\") {\n      return this.suffix as T extends \"\" ? string : `${T}_${string}`;\n    }\n    return `${this.prefix}_${this.suffix}` as T extends \"\" ? string : `${T}_${string}`;\n  }\n\n  static fromString<const T extends string>(\n    str: string,\n    prefix?: T\n  ): TypeID<T> {\n    const typeIdRaw = fromString(str, prefix);\n\n    return new TypeID<T>(getType(typeIdRaw) as T, getSuffix(typeIdRaw));\n  }\n\n  static fromUUIDBytes<const T extends string>(\n    prefix: T,\n    bytes: Uint8Array\n  ): TypeID<T> {\n    const suffix = encode(bytes);\n    return new TypeID(prefix, suffix);\n  }\n\n  static fromUUID<const T extends string>(prefix: T, uuid: string): TypeID<T> {\n    const suffix = encode(parseUUID(uuid));\n    return new TypeID(prefix, suffix);\n  }\n}\n\nexport function typeid<T extends string>(): TypeID<\"\">;\nexport function typeid<T extends string>(prefix: T): TypeID<T>;\nexport function typeid<T extends string>(prefix: T, suffix: string): TypeID<T>;\nexport function typeid<T extends string>(\n  prefix: T = \"\" as T,\n  suffix: string = \"\"\n): TypeID<T> {\n  return new TypeID(prefix, suffix);\n}\n", "/* eslint-disable no-bitwise */\nexport function parseUUID(uuid: string) {\n  let v;\n  const arr = new Uint8Array(16);\n\n  // Block 1\n  arr[0] = (v = parseInt(uuid.slice(0, 8), 16)) >>> 24;\n  arr[1] = (v >>> 16) & 0xff;\n  arr[2] = (v >>> 8) & 0xff;\n  arr[3] = v & 0xff;\n\n  // Block 2\n  arr[4] = (v = parseInt(uuid.slice(9, 13), 16)) >>> 8;\n  arr[5] = v & 0xff;\n\n  // Block 3\n  arr[6] = (v = parseInt(uuid.slice(14, 18), 16)) >>> 8;\n  arr[7] = v & 0xff;\n\n  // Block 4\n  arr[8] = (v = parseInt(uuid.slice(19, 23), 16)) >>> 8;\n  arr[9] = v & 0xff;\n\n  // Block 5\n  arr[10] = ((v = parseInt(uuid.slice(24, 36), 16)) / 0x10000000000) & 0xff;\n  arr[11] = (v / 0x100000000) & 0xff;\n  arr[12] = (v >>> 24) & 0xff;\n  arr[13] = (v >>> 16) & 0xff;\n  arr[14] = (v >>> 8) & 0xff;\n  arr[15] = v & 0xff;\n\n  return arr;\n}\n", "/* eslint-disable no-bitwise */\nconst alphabet: string = \"0123456789abcdefghjkmnpqrstvwxyz\";\n\n// Decoding table\nconst dec: Uint8Array = new Uint8Array([\n  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,\n  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,\n  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,\n  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x00, 0x01, 0x02, 0x03,\n  0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,\n  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,\n  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,\n  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x10,\n  0x11, 0xff, 0x12, 0x13, 0xff, 0x14, 0x15, 0xff, 0x16, 0x17, 0x18, 0x19, 0x1a,\n  0xff, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,\n  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,\n  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,\n  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,\n  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,\n  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,\n  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,\n  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,\n  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,\n  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,\n  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,\n]);\n\nexport function encode(src: Uint8Array): string {\n  const dst: string[] = new Array(26).fill(\"\");\n\n  if (src.length !== 16) {\n    throw new Error(\n      `Invalid length. Expected 16 bytes, got ${src.length}. Input: ${src}`\n    );\n  }\n\n  // 10 byte timestamp\n  dst[0] = alphabet[(src[0] & 224) >> 5];\n  dst[1] = alphabet[src[0] & 31];\n  dst[2] = alphabet[(src[1] & 248) >> 3];\n  dst[3] = alphabet[((src[1] & 7) << 2) | ((src[2] & 192) >> 6)];\n  dst[4] = alphabet[(src[2] & 62) >> 1];\n  dst[5] = alphabet[((src[2] & 1) << 4) | ((src[3] & 240) >> 4)];\n  dst[6] = alphabet[((src[3] & 15) << 1) | ((src[4] & 128) >> 7)];\n  dst[7] = alphabet[(src[4] & 124) >> 2];\n  dst[8] = alphabet[((src[4] & 3) << 3) | ((src[5] & 224) >> 5)];\n  dst[9] = alphabet[src[5] & 31];\n\n  // 16 bytes of randomness\n  dst[10] = alphabet[(src[6] & 248) >> 3];\n  dst[11] = alphabet[((src[6] & 7) << 2) | ((src[7] & 192) >> 6)];\n  dst[12] = alphabet[(src[7] & 62) >> 1];\n  dst[13] = alphabet[((src[7] & 1) << 4) | ((src[8] & 240) >> 4)];\n  dst[14] = alphabet[((src[8] & 15) << 1) | ((src[9] & 128) >> 7)];\n  dst[15] = alphabet[(src[9] & 124) >> 2];\n  dst[16] = alphabet[((src[9] & 3) << 3) | ((src[10] & 224) >> 5)];\n  dst[17] = alphabet[src[10] & 31];\n  dst[18] = alphabet[(src[11] & 248) >> 3];\n  dst[19] = alphabet[((src[11] & 7) << 2) | ((src[12] & 192) >> 6)];\n  dst[20] = alphabet[(src[12] & 62) >> 1];\n  dst[21] = alphabet[((src[12] & 1) << 4) | ((src[13] & 240) >> 4)];\n  dst[22] = alphabet[((src[13] & 15) << 1) | ((src[14] & 128) >> 7)];\n  dst[23] = alphabet[(src[14] & 124) >> 2];\n  dst[24] = alphabet[((src[14] & 3) << 3) | ((src[15] & 224) >> 5)];\n  dst[25] = alphabet[src[15] & 31];\n\n  return dst.join(\"\");\n}\n\nexport function decode(s: string): Uint8Array {\n  if (s.length !== 26) {\n    throw new Error(\n      `Invalid length. Expected 26 bytes, got ${s.length}. Input: ${s}`\n    );\n  }\n\n  const encoder = new TextEncoder();\n  const v: Uint8Array = encoder.encode(s);\n\n  // Check if all the characters are part of the expected base32 character set.\n  if (\n    dec[v[0]] === 0xff ||\n    dec[v[1]] === 0xff ||\n    dec[v[2]] === 0xff ||\n    dec[v[3]] === 0xff ||\n    dec[v[4]] === 0xff ||\n    dec[v[5]] === 0xff ||\n    dec[v[6]] === 0xff ||\n    dec[v[7]] === 0xff ||\n    dec[v[8]] === 0xff ||\n    dec[v[9]] === 0xff ||\n    dec[v[10]] === 0xff ||\n    dec[v[11]] === 0xff ||\n    dec[v[12]] === 0xff ||\n    dec[v[13]] === 0xff ||\n    dec[v[14]] === 0xff ||\n    dec[v[15]] === 0xff ||\n    dec[v[16]] === 0xff ||\n    dec[v[17]] === 0xff ||\n    dec[v[18]] === 0xff ||\n    dec[v[19]] === 0xff ||\n    dec[v[20]] === 0xff ||\n    dec[v[21]] === 0xff ||\n    dec[v[22]] === 0xff ||\n    dec[v[23]] === 0xff ||\n    dec[v[24]] === 0xff ||\n    dec[v[25]] === 0xff\n  ) {\n    throw new Error(\"Invalid base32 character\");\n  }\n\n  const id = new Uint8Array(16);\n\n  // 6 bytes timestamp (48 bits)\n  id[0] = (dec[v[0]] << 5) | dec[v[1]];\n  id[1] = (dec[v[2]] << 3) | (dec[v[3]] >> 2);\n  id[2] = ((dec[v[3]] & 3) << 6) | (dec[v[4]] << 1) | (dec[v[5]] >> 4);\n  id[3] = ((dec[v[5]] & 15) << 4) | (dec[v[6]] >> 1);\n  id[4] = ((dec[v[6]] & 1) << 7) | (dec[v[7]] << 2) | (dec[v[8]] >> 3);\n  id[5] = ((dec[v[8]] & 7) << 5) | dec[v[9]];\n\n  // 10 bytes of entropy (80 bits)\n  id[6] = (dec[v[10]] << 3) | (dec[v[11]] >> 2);\n  id[7] = ((dec[v[11]] & 3) << 6) | (dec[v[12]] << 1) | (dec[v[13]] >> 4);\n  id[8] = ((dec[v[13]] & 15) << 4) | (dec[v[14]] >> 1);\n  id[9] = ((dec[v[14]] & 1) << 7) | (dec[v[15]] << 2) | (dec[v[16]] >> 3);\n  id[10] = ((dec[v[16]] & 7) << 5) | dec[v[17]];\n  id[11] = (dec[v[18]] << 3) | (dec[v[19]] >> 2);\n  id[12] = ((dec[v[19]] & 3) << 6) | (dec[v[20]] << 1) | (dec[v[21]] >> 4);\n  id[13] = ((dec[v[21]] & 15) << 4) | (dec[v[22]] >> 1);\n  id[14] = ((dec[v[22]] & 1) << 7) | (dec[v[23]] << 2) | (dec[v[24]] >> 3);\n  id[15] = ((dec[v[24]] & 7) << 5) | dec[v[25]];\n\n  return id;\n}\n", "import { stringify, v7 } from \"uuid\";\nimport { parseUUID } from \"../parse_uuid\";\nimport { encode, decode } from \"../base32\";\nimport { isValidPrefix } from \"../prefix\";\nimport {\n  EmptyPrefixError,\n  InvalidPrefixError,\n  InvalidSuffixCharacterError,\n  InvalidSuffixLengthError,\n  PrefixMismatchError,\n} from \"./error\";\n\nexport type TypeId<T> = string & { __type: T };\n\nexport function typeidUnboxed<T extends string>(\n  prefix: T = \"\" as T,\n  suffix: string = \"\"\n): TypeId<T> {\n  if (!isValidPrefix(prefix)) {\n    throw new InvalidPrefixError(prefix);\n  }\n\n  let finalSuffix: string;\n  if (suffix) {\n    finalSuffix = suffix;\n  } else {\n    const buffer = new Uint8Array(16);\n    v7(undefined, buffer);\n    finalSuffix = encode(buffer);\n  }\n\n  if (finalSuffix.length !== 26) {\n    throw new InvalidSuffixLengthError(finalSuffix.length);\n  }\n\n  if (finalSuffix[0] > \"7\") {\n    throw new InvalidSuffixCharacterError(finalSuffix[0]);\n  }\n\n  // Validate the suffix by decoding it. If it's invalid, an error will be thrown.\n  decode(finalSuffix);\n\n  if (prefix === \"\") {\n    return finalSuffix as TypeId<T>;\n  } else {\n    return `${prefix}_${finalSuffix}` as TypeId<T>;\n  }\n}\n\n/**\n * Constructs a TypeId from a string representation, optionally validating against a provided prefix.\n * This function splits the input `typeId` string by an underscore `_` to separate the prefix and suffix.\n * If the `typeId` contains no underscore, it is assumed to be a suffix with an empty prefix.\n * If a `prefix` is provided, it must match the prefix part of the `typeId`, or an error is thrown.\n *\n * @param {string} typeId - The string representation of the TypeId to be parsed.\n * @param {T} [prefix] - An optional prefix to validate against the prefix in the `typeId`.\n * @returns {TypeId<T>} A new TypeId instance constructed from the parsed `typeId`.\n * @throws {Error} If the `typeId` format is invalid, the prefix is empty when there's a separator,\n *                 or there's a prefix mismatch when a `prefix` is provided.\n * @template T - A string literal type that extends string.\n */\nexport function fromString<T extends string>(\n  typeId: string,\n  prefix?: T\n): TypeId<T> {\n  let p;\n  let s;\n\n  const underscoreIndex = typeId.lastIndexOf(\"_\");\n  if (underscoreIndex === -1) {\n    p = \"\" as T;\n    s = typeId;\n  } else {\n    p = typeId.substring(0, underscoreIndex) as T;\n    s = typeId.substring(underscoreIndex + 1);\n\n    if (!p) {\n      throw new EmptyPrefixError(typeId);\n    }\n  }\n\n  if (!s) {\n    throw new InvalidSuffixLengthError(0);\n  }\n\n  if (prefix && p !== prefix) {\n    throw new PrefixMismatchError(prefix, p);\n  }\n\n  return typeidUnboxed(p, s);\n}\n\n/**\n * Parses a TypeId string into its prefix and suffix components.\n *\n * @param {TypeId<T>} typeId - The TypeId string to parse.\n * @returns {{ prefix: T; suffix: string }} An object containing the prefix and suffix of the TypeId.\n * @throws {Error} If the TypeId format is invalid (not exactly two parts separated by an underscore).\n *\n * @example\n * // For a valid TypeId 'example_00041061050r3gg28a1c60t3gf'\n * const { prefix, suffix } = parseTypeId('example_00041061050r3gg28a1c60t3gf');\n * console.log(prefix); // 'example'\n * console.log(suffix); // '00041061050r3gg28a1c60t3gf'\n *\n * @example\n * // Throws an error for invalid TypeId format\n * try {\n *   parseTypeId('invalidTypeId');\n * } catch (error) {\n *   console.error(error.message); // 'Invalid TypeId format: invalidTypeId'\n * }\n */\nexport function parseTypeId<T extends string>(\n  typeId: TypeId<T>\n): { prefix: T; suffix: string } {\n  return { prefix: getType(typeId), suffix: getSuffix(typeId) };\n}\n\n/**\n * Retrieves the prefix from a TypeId.\n *\n * @param {TypeId<T>} typeId - The TypeId from which to extract the prefix.\n * @returns {T} The prefix of the TypeId.\n */\nexport function getType<T extends string>(typeId: TypeId<T>): T {\n  const underscoreIndex = typeId.lastIndexOf(\"_\");\n  if (underscoreIndex === -1) {\n    return \"\" as T;\n  }\n  return typeId.substring(0, underscoreIndex) as T;\n}\n\n/**\n * Retrieves the suffix from a TypeId.\n *\n * @param {TypeId<T>} typeId - The TypeId from which to extract the suffix.\n * @returns {string} The suffix of the TypeId.\n */\nexport function getSuffix<T extends string>(typeId: TypeId<T>): string {\n  const underscoreIndex = typeId.lastIndexOf(\"_\");\n  if (underscoreIndex === -1) {\n    return typeId;\n  }\n  return typeId.substring(underscoreIndex + 1);\n}\n\nexport function toUUIDBytes<T extends string>(typeId: TypeId<T>): Uint8Array {\n  return decode(getSuffix(typeId));\n}\n\nexport function toUUID<T extends string>(typeId: TypeId<T>) {\n  return stringify(toUUIDBytes(typeId));\n}\n\nexport function fromUUIDBytes(\n  prefix: string,\n  bytes: Uint8Array\n): TypeId<typeof prefix> {\n  const suffix = encode(bytes);\n  return prefix\n    ? (`${prefix}_${suffix}` as TypeId<typeof prefix>)\n    : (suffix as TypeId<typeof prefix>);\n}\n\nexport function fromUUID<T extends string>(\n  uuid: string,\n  prefix?: T\n): TypeId<T> {\n  const suffix = encode(parseUUID(uuid));\n  return prefix ? (`${prefix}_${suffix}` as TypeId<T>) : (suffix as TypeId<T>);\n}\n", "export function isValidPrefix(str: string): boolean {\n  if (str.length > 63) {\n    return false;\n  }\n\n  let code;\n  let i;\n  let len;\n\n  for (i = 0, len = str.length; i < len; i += 1) {\n    code = str.charCodeAt(i);\n    const isLowerAtoZ = code > 96 && code < 123;\n    const isUnderscore = code === 95;\n\n    // first and last char of prefix can only be [a-z]\n    if ((i === 0 || i === len - 1) && !isLowerAtoZ) {\n      return false;\n    }\n\n    if (!(isLowerAtoZ || isUnderscore)) {\n      return false;\n    }\n  }\n  return true;\n}\n", "export class InvalidPrefixError extends Error {\n  constructor(prefix: string) {\n    super(`Invalid prefix \"${prefix}\". Must be at most 63 ASCII letters [a-z_]`);\n    this.name = \"InvalidPrefixError\";\n  }\n}\n\nexport class PrefixMismatchError extends <PERSON>rror {\n  constructor(expected: string, actual: string) {\n    super(`Invalid TypeId. Prefix mismatch. Expected ${expected}, got ${actual}`);\n    this.name = \"PrefixMismatchError\";\n  }\n}\n\nexport class EmptyPrefixError extends Error {\n  constructor(typeId: string) {\n    super(`Invalid TypeId. Prefix cannot be empty when there's a separator: ${typeId}`);\n    this.name = \"EmptyPrefixError\";\n  }\n}\n\nexport class InvalidSuffixLengthError extends Error {\n  constructor(length: number) {\n    super(`Invalid length. Suffix should have 26 characters, got ${length}`);\n    this.name = \"InvalidSuffixLengthError\";\n  }\n}\n\nexport class InvalidSuffixCharacterError extends <PERSON>rror {\n  constructor(firstChar: string) {\n    super(`Invalid suffix. First character \"${firstChar}\" must be in the range [0-7]`);\n    this.name = \"InvalidSuffixCharacterError\";\n  }\n}\n\nexport class TypeIDConversionError extends Error {\n  constructor(actualPrefix: string, expectedPrefix: string) {\n    super(`Cannot convert TypeID of type ${actualPrefix} to type ${expectedPrefix}`);\n    this.name = \"TypeIDConversionError\";\n  }\n}\n"], "names": ["stringify", "stringify"], "mappings": ";;;;;;;;;;;;;;AAAA,SAAS,aAAAA,kBAAiB;;AGA1B,SAAS,WAAW,UAAU;;;AFCvB,SAAS,UAAU,IAAA,EAAc;IACtC,IAAI;IACJ,MAAM,MAAM,IAAI,WAAW,EAAE;IAG7B,GAAA,CAAI,CAAC,CAAA,GAAA,CAAK,IAAI,SAAS,KAAK,KAAA,CAAM,GAAG,CAAC,GAAG,EAAE,CAAA,MAAO;IAClD,GAAA,CAAI,CAAC,CAAA,GAAK,MAAM,KAAM;IACtB,GAAA,CAAI,CAAC,CAAA,GAAK,MAAM,IAAK;IACrB,GAAA,CAAI,CAAC,CAAA,GAAI,IAAI;IAGb,GAAA,CAAI,CAAC,CAAA,GAAA,CAAK,IAAI,SAAS,KAAK,KAAA,CAAM,GAAG,EAAE,GAAG,EAAE,CAAA,MAAO;IACnD,GAAA,CAAI,CAAC,CAAA,GAAI,IAAI;IAGb,GAAA,CAAI,CAAC,CAAA,GAAA,CAAK,IAAI,SAAS,KAAK,KAAA,CAAM,IAAI,EAAE,GAAG,EAAE,CAAA,MAAO;IACpD,GAAA,CAAI,CAAC,CAAA,GAAI,IAAI;IAGb,GAAA,CAAI,CAAC,CAAA,GAAA,CAAK,IAAI,SAAS,KAAK,KAAA,CAAM,IAAI,EAAE,GAAG,EAAE,CAAA,MAAO;IACpD,GAAA,CAAI,CAAC,CAAA,GAAI,IAAI;IAGb,GAAA,CAAI,EAAE,CAAA,GAAA,CAAM,IAAI,SAAS,KAAK,KAAA,CAAM,IAAI,EAAE,GAAG,EAAE,CAAA,IAAK,gBAAiB;IACrE,GAAA,CAAI,EAAE,CAAA,GAAK,IAAI,aAAe;IAC9B,GAAA,CAAI,EAAE,CAAA,GAAK,MAAM,KAAM;IACvB,GAAA,CAAI,EAAE,CAAA,GAAK,MAAM,KAAM;IACvB,GAAA,CAAI,EAAE,CAAA,GAAK,MAAM,IAAK;IACtB,GAAA,CAAI,EAAE,CAAA,GAAI,IAAI;IAEd,OAAO;AACT;;AC/BA,IAAM,WAAmB;AAGzB,IAAM,MAAkB,IAAI,WAAW;IACrC;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACxE;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACxE;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACxE;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACxE;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACxE;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACxE;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACxE;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACxE;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACxE;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACxE;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACxE;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACxE;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACxE;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACxE;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACxE;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACxE;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACxE;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACxE;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACxE;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;CACjD;AAEM,SAAS,OAAO,GAAA,EAAyB;IAC9C,MAAM,MAAgB,IAAI,MAAM,EAAE,EAAE,IAAA,CAAK,EAAE;IAE3C,IAAI,IAAI,MAAA,KAAW,IAAI;QACrB,MAAM,IAAI,MACR,CAAA,uCAAA,EAA0C,IAAI,MAAA,CAAA,SAAA,EAAkB,KAAA;IAEpE;IAGA,GAAA,CAAI,CAAC,CAAA,GAAI,QAAA,CAAA,CAAU,GAAA,CAAI,CAAC,CAAA,GAAI,GAAA,KAAQ,CAAC,CAAA;IACrC,GAAA,CAAI,CAAC,CAAA,GAAI,QAAA,CAAS,GAAA,CAAI,CAAC,CAAA,GAAI,EAAE,CAAA;IAC7B,GAAA,CAAI,CAAC,CAAA,GAAI,QAAA,CAAA,CAAU,GAAA,CAAI,CAAC,CAAA,GAAI,GAAA,KAAQ,CAAC,CAAA;IACrC,GAAA,CAAI,CAAC,CAAA,GAAI,QAAA,CAAA,CAAW,GAAA,CAAI,CAAC,CAAA,GAAI,CAAA,KAAM,IAAA,CAAO,GAAA,CAAI,CAAC,CAAA,GAAI,GAAA,KAAQ,CAAE,CAAA;IAC7D,GAAA,CAAI,CAAC,CAAA,GAAI,QAAA,CAAA,CAAU,GAAA,CAAI,CAAC,CAAA,GAAI,EAAA,KAAO,CAAC,CAAA;IACpC,GAAA,CAAI,CAAC,CAAA,GAAI,QAAA,CAAA,CAAW,GAAA,CAAI,CAAC,CAAA,GAAI,CAAA,KAAM,IAAA,CAAO,GAAA,CAAI,CAAC,CAAA,GAAI,GAAA,KAAQ,CAAE,CAAA;IAC7D,GAAA,CAAI,CAAC,CAAA,GAAI,QAAA,CAAA,CAAW,GAAA,CAAI,CAAC,CAAA,GAAI,EAAA,KAAO,IAAA,CAAO,GAAA,CAAI,CAAC,CAAA,GAAI,GAAA,KAAQ,CAAE,CAAA;IAC9D,GAAA,CAAI,CAAC,CAAA,GAAI,QAAA,CAAA,CAAU,GAAA,CAAI,CAAC,CAAA,GAAI,GAAA,KAAQ,CAAC,CAAA;IACrC,GAAA,CAAI,CAAC,CAAA,GAAI,QAAA,CAAA,CAAW,GAAA,CAAI,CAAC,CAAA,GAAI,CAAA,KAAM,IAAA,CAAO,GAAA,CAAI,CAAC,CAAA,GAAI,GAAA,KAAQ,CAAE,CAAA;IAC7D,GAAA,CAAI,CAAC,CAAA,GAAI,QAAA,CAAS,GAAA,CAAI,CAAC,CAAA,GAAI,EAAE,CAAA;IAG7B,GAAA,CAAI,EAAE,CAAA,GAAI,QAAA,CAAA,CAAU,GAAA,CAAI,CAAC,CAAA,GAAI,GAAA,KAAQ,CAAC,CAAA;IACtC,GAAA,CAAI,EAAE,CAAA,GAAI,QAAA,CAAA,CAAW,GAAA,CAAI,CAAC,CAAA,GAAI,CAAA,KAAM,IAAA,CAAO,GAAA,CAAI,CAAC,CAAA,GAAI,GAAA,KAAQ,CAAE,CAAA;IAC9D,GAAA,CAAI,EAAE,CAAA,GAAI,QAAA,CAAA,CAAU,GAAA,CAAI,CAAC,CAAA,GAAI,EAAA,KAAO,CAAC,CAAA;IACrC,GAAA,CAAI,EAAE,CAAA,GAAI,QAAA,CAAA,CAAW,GAAA,CAAI,CAAC,CAAA,GAAI,CAAA,KAAM,IAAA,CAAO,GAAA,CAAI,CAAC,CAAA,GAAI,GAAA,KAAQ,CAAE,CAAA;IAC9D,GAAA,CAAI,EAAE,CAAA,GAAI,QAAA,CAAA,CAAW,GAAA,CAAI,CAAC,CAAA,GAAI,EAAA,KAAO,IAAA,CAAO,GAAA,CAAI,CAAC,CAAA,GAAI,GAAA,KAAQ,CAAE,CAAA;IAC/D,GAAA,CAAI,EAAE,CAAA,GAAI,QAAA,CAAA,CAAU,GAAA,CAAI,CAAC,CAAA,GAAI,GAAA,KAAQ,CAAC,CAAA;IACtC,GAAA,CAAI,EAAE,CAAA,GAAI,QAAA,CAAA,CAAW,GAAA,CAAI,CAAC,CAAA,GAAI,CAAA,KAAM,IAAA,CAAO,GAAA,CAAI,EAAE,CAAA,GAAI,GAAA,KAAQ,CAAE,CAAA;IAC/D,GAAA,CAAI,EAAE,CAAA,GAAI,QAAA,CAAS,GAAA,CAAI,EAAE,CAAA,GAAI,EAAE,CAAA;IAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,QAAA,CAAA,CAAU,GAAA,CAAI,EAAE,CAAA,GAAI,GAAA,KAAQ,CAAC,CAAA;IACvC,GAAA,CAAI,EAAE,CAAA,GAAI,QAAA,CAAA,CAAW,GAAA,CAAI,EAAE,CAAA,GAAI,CAAA,KAAM,IAAA,CAAO,GAAA,CAAI,EAAE,CAAA,GAAI,GAAA,KAAQ,CAAE,CAAA;IAChE,GAAA,CAAI,EAAE,CAAA,GAAI,QAAA,CAAA,CAAU,GAAA,CAAI,EAAE,CAAA,GAAI,EAAA,KAAO,CAAC,CAAA;IACtC,GAAA,CAAI,EAAE,CAAA,GAAI,QAAA,CAAA,CAAW,GAAA,CAAI,EAAE,CAAA,GAAI,CAAA,KAAM,IAAA,CAAO,GAAA,CAAI,EAAE,CAAA,GAAI,GAAA,KAAQ,CAAE,CAAA;IAChE,GAAA,CAAI,EAAE,CAAA,GAAI,QAAA,CAAA,CAAW,GAAA,CAAI,EAAE,CAAA,GAAI,EAAA,KAAO,IAAA,CAAO,GAAA,CAAI,EAAE,CAAA,GAAI,GAAA,KAAQ,CAAE,CAAA;IACjE,GAAA,CAAI,EAAE,CAAA,GAAI,QAAA,CAAA,CAAU,GAAA,CAAI,EAAE,CAAA,GAAI,GAAA,KAAQ,CAAC,CAAA;IACvC,GAAA,CAAI,EAAE,CAAA,GAAI,QAAA,CAAA,CAAW,GAAA,CAAI,EAAE,CAAA,GAAI,CAAA,KAAM,IAAA,CAAO,GAAA,CAAI,EAAE,CAAA,GAAI,GAAA,KAAQ,CAAE,CAAA;IAChE,GAAA,CAAI,EAAE,CAAA,GAAI,QAAA,CAAS,GAAA,CAAI,EAAE,CAAA,GAAI,EAAE,CAAA;IAE/B,OAAO,IAAI,IAAA,CAAK,EAAE;AACpB;AAEO,SAAS,OAAO,CAAA,EAAuB;IAC5C,IAAI,EAAE,MAAA,KAAW,IAAI;QACnB,MAAM,IAAI,MACR,CAAA,uCAAA,EAA0C,EAAE,MAAA,CAAA,SAAA,EAAkB,GAAA;IAElE;IAEA,MAAM,UAAU,IAAI,YAAY;IAChC,MAAM,IAAgB,QAAQ,MAAA,CAAO,CAAC;IAGtC,IACE,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA,KAAM,OACd,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA,KAAM,OACd,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA,KAAM,OACd,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA,KAAM,OACd,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA,KAAM,OACd,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA,KAAM,OACd,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA,KAAM,OACd,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA,KAAM,OACd,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA,KAAM,OACd,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA,KAAM,OACd,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,KAAM,OACf,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,KAAM,OACf,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,KAAM,OACf,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,KAAM,OACf,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,KAAM,OACf,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,KAAM,OACf,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,KAAM,OACf,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,KAAM,OACf,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,KAAM,OACf,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,KAAM,OACf,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,KAAM,OACf,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,KAAM,OACf,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,KAAM,OACf,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,KAAM,OACf,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,KAAM,OACf,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,KAAM,KACf;QACA,MAAM,IAAI,MAAM,0BAA0B;IAC5C;IAEA,MAAM,KAAK,IAAI,WAAW,EAAE;IAG5B,EAAA,CAAG,CAAC,CAAA,GAAK,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA,IAAK,IAAK,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA;IACnC,EAAA,CAAG,CAAC,CAAA,GAAK,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA,IAAK,IAAM,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA,IAAK;IACzC,EAAA,CAAG,CAAC,CAAA,GAAA,CAAM,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA,GAAI,CAAA,KAAM,IAAM,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA,IAAK,IAAM,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA,IAAK;IAClE,EAAA,CAAG,CAAC,CAAA,GAAA,CAAM,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA,GAAI,EAAA,KAAO,IAAM,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA,IAAK;IAChD,EAAA,CAAG,CAAC,CAAA,GAAA,CAAM,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA,GAAI,CAAA,KAAM,IAAM,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA,IAAK,IAAM,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA,IAAK;IAClE,EAAA,CAAG,CAAC,CAAA,GAAA,CAAM,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA,GAAI,CAAA,KAAM,IAAK,GAAA,CAAI,CAAA,CAAE,CAAC,CAAC,CAAA;IAGzC,EAAA,CAAG,CAAC,CAAA,GAAK,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,IAAK,IAAM,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,IAAK;IAC3C,EAAA,CAAG,CAAC,CAAA,GAAA,CAAM,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,GAAI,CAAA,KAAM,IAAM,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,IAAK,IAAM,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,IAAK;IACrE,EAAA,CAAG,CAAC,CAAA,GAAA,CAAM,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,GAAI,EAAA,KAAO,IAAM,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,IAAK;IAClD,EAAA,CAAG,CAAC,CAAA,GAAA,CAAM,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,GAAI,CAAA,KAAM,IAAM,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,IAAK,IAAM,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,IAAK;IACrE,EAAA,CAAG,EAAE,CAAA,GAAA,CAAM,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,GAAI,CAAA,KAAM,IAAK,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA;IAC5C,EAAA,CAAG,EAAE,CAAA,GAAK,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,IAAK,IAAM,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,IAAK;IAC5C,EAAA,CAAG,EAAE,CAAA,GAAA,CAAM,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,GAAI,CAAA,KAAM,IAAM,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,IAAK,IAAM,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,IAAK;IACtE,EAAA,CAAG,EAAE,CAAA,GAAA,CAAM,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,GAAI,EAAA,KAAO,IAAM,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,IAAK;IACnD,EAAA,CAAG,EAAE,CAAA,GAAA,CAAM,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,GAAI,CAAA,KAAM,IAAM,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,IAAK,IAAM,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,IAAK;IACtE,EAAA,CAAG,EAAE,CAAA,GAAA,CAAM,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA,GAAI,CAAA,KAAM,IAAK,GAAA,CAAI,CAAA,CAAE,EAAE,CAAC,CAAA;IAE5C,OAAO;AACT;;;AEtIO,SAAS,cAAc,GAAA,EAAsB;IAClD,IAAI,IAAI,MAAA,GAAS,IAAI;QACnB,OAAO;IACT;IAEA,IAAI;IACJ,IAAI;IACJ,IAAI;IAEJ,IAAK,IAAI,GAAG,MAAM,IAAI,MAAA,EAAQ,IAAI,KAAK,KAAK,EAAG;QAC7C,OAAO,IAAI,UAAA,CAAW,CAAC;QACvB,MAAM,cAAc,OAAO,MAAM,OAAO;QACxC,MAAM,eAAe,SAAS;QAG9B,IAAA,CAAK,MAAM,KAAK,MAAM,MAAM,CAAA,KAAM,CAAC,aAAa;YAC9C,OAAO;QACT;QAEA,IAAI,CAAA,CAAE,eAAe,YAAA,GAAe;YAClC,OAAO;QACT;IACF;IACA,OAAO;AACT;;ACxBO,IAAM,qBAAN,cAAiC,MAAM;IAC5C,YAAY,MAAA,CAAgB;QAC1B,KAAA,CAAM,CAAA,gBAAA,EAAmB,OAAA,0CAAA,CAAkD;QAC3E,IAAA,CAAK,IAAA,GAAO;IACd;AACF;AAEO,IAAM,sBAAN,cAAkC,MAAM;IAC7C,YAAY,QAAA,EAAkB,MAAA,CAAgB;QAC5C,KAAA,CAAM,CAAA,0CAAA,EAA6C,SAAA,MAAA,EAAiB,QAAQ;QAC5E,IAAA,CAAK,IAAA,GAAO;IACd;AACF;AAEO,IAAM,mBAAN,cAA+B,MAAM;IAC1C,YAAY,MAAA,CAAgB;QAC1B,KAAA,CAAM,CAAA,iEAAA,EAAoE,QAAQ;QAClF,IAAA,CAAK,IAAA,GAAO;IACd;AACF;AAEO,IAAM,2BAAN,cAAuC,MAAM;IAClD,YAAY,MAAA,CAAgB;QAC1B,KAAA,CAAM,CAAA,sDAAA,EAAyD,QAAQ;QACvE,IAAA,CAAK,IAAA,GAAO;IACd;AACF;AAEO,IAAM,8BAAN,cAA0C,MAAM;IACrD,YAAY,SAAA,CAAmB;QAC7B,KAAA,CAAM,CAAA,iCAAA,EAAoC,UAAA,4BAAA,CAAuC;QACjF,IAAA,CAAK,IAAA,GAAO;IACd;AACF;AAEO,IAAM,wBAAN,cAAoC,MAAM;IAC/C,YAAY,YAAA,EAAsB,cAAA,CAAwB;QACxD,KAAA,CAAM,CAAA,8BAAA,EAAiC,aAAA,SAAA,EAAwB,gBAAgB;QAC/E,IAAA,CAAK,IAAA,GAAO;IACd;AACF;;AF1BO,SAAS,cACd,SAAY,EAAA,EACZ,SAAiB,EAAA,EACN;IACX,IAAI,CAAC,cAAc,MAAM,GAAG;QAC1B,MAAM,IAAI,mBAAmB,MAAM;IACrC;IAEA,IAAI;IACJ,IAAI,QAAQ;QACV,cAAc;IAChB,OAAO;QACL,MAAM,SAAS,IAAI,WAAW,EAAE;QAChC,CAAA,GAAA,6NAAA,CAAA,KAAA,EAAG,KAAA,GAAW,MAAM;QACpB,cAAc,OAAO,MAAM;IAC7B;IAEA,IAAI,YAAY,MAAA,KAAW,IAAI;QAC7B,MAAM,IAAI,yBAAyB,YAAY,MAAM;IACvD;IAEA,IAAI,WAAA,CAAY,CAAC,CAAA,GAAI,KAAK;QACxB,MAAM,IAAI,4BAA4B,WAAA,CAAY,CAAC,CAAC;IACtD;IAGA,OAAO,WAAW;IAElB,IAAI,WAAW,IAAI;QACjB,OAAO;IACT,OAAO;QACL,OAAO,GAAG,OAAA,CAAA,EAAU,aAAA;IACtB;AACF;AAeO,SAAS,WACd,MAAA,EACA,MAAA,EACW;IACX,IAAI;IACJ,IAAI;IAEJ,MAAM,kBAAkB,OAAO,WAAA,CAAY,GAAG;IAC9C,IAAI,oBAAoB,CAAA,GAAI;QAC1B,IAAI;QACJ,IAAI;IACN,OAAO;QACL,IAAI,OAAO,SAAA,CAAU,GAAG,eAAe;QACvC,IAAI,OAAO,SAAA,CAAU,kBAAkB,CAAC;QAExC,IAAI,CAAC,GAAG;YACN,MAAM,IAAI,iBAAiB,MAAM;QACnC;IACF;IAEA,IAAI,CAAC,GAAG;QACN,MAAM,IAAI,yBAAyB,CAAC;IACtC;IAEA,IAAI,UAAU,MAAM,QAAQ;QAC1B,MAAM,IAAI,oBAAoB,QAAQ,CAAC;IACzC;IAEA,OAAO,cAAc,GAAG,CAAC;AAC3B;AAuBO,SAAS,YACd,MAAA,EAC+B;IAC/B,OAAO;QAAE,QAAQ,QAAQ,MAAM;QAAG,QAAQ,UAAU,MAAM;IAAE;AAC9D;AAQO,SAAS,QAA0B,MAAA,EAAsB;IAC9D,MAAM,kBAAkB,OAAO,WAAA,CAAY,GAAG;IAC9C,IAAI,oBAAoB,CAAA,GAAI;QAC1B,OAAO;IACT;IACA,OAAO,OAAO,SAAA,CAAU,GAAG,eAAe;AAC5C;AAQO,SAAS,UAA4B,MAAA,EAA2B;IACrE,MAAM,kBAAkB,OAAO,WAAA,CAAY,GAAG;IAC9C,IAAI,oBAAoB,CAAA,GAAI;QAC1B,OAAO;IACT;IACA,OAAO,OAAO,SAAA,CAAU,kBAAkB,CAAC;AAC7C;AAEO,SAAS,YAA8B,MAAA,EAA+B;IAC3E,OAAO,OAAO,UAAU,MAAM,CAAC;AACjC;AAEO,SAAS,OAAyB,MAAA,EAAmB;IAC1D,uPAAO,YAAA,EAAU,YAAY,MAAM,CAAC;AACtC;AAEO,SAAS,cACd,MAAA,EACA,KAAA,EACuB;IACvB,MAAM,SAAS,OAAO,KAAK;IAC3B,OAAO,SACF,GAAG,OAAA,CAAA,EAAU,QAAA,GACb;AACP;AAEO,SAAS,SACd,IAAA,EACA,MAAA,EACW;IACX,MAAM,SAAS,OAAO,UAAU,IAAI,CAAC;IACrC,OAAO,SAAU,GAAG,OAAA,CAAA,EAAU,QAAA,GAA0B;AAC1D;;AHjKO,IAAM,SAAN,MAAqC;IAC1C,YAAoB,MAAA,EAAmB,SAAiB,EAAA,CAAI;QAAxC,IAAA,CAAA,MAAA,GAAA;QAAmB,IAAA,CAAA,MAAA,GAAA;QACrC,MAAM,YAAY,cAAc,QAAQ,MAAM;QAE9C,IAAA,CAAK,MAAA,GAAS,QAAQ,SAAS;QAC/B,IAAA,CAAK,MAAA,GAAS,UAAU,SAAS;IACnC;IAEO,UAAa;QAClB,OAAO,IAAA,CAAK,MAAA;IACd;IAEO,YAAoB;QACzB,OAAO,IAAA,CAAK,MAAA;IACd;IAEO,OAA+B,MAAA,EAAsB;QAC1D,MAAM,OAAO,IAAA;QACb,IAAI,KAAK,MAAA,KAAW,QAAQ;YAC1B,MAAM,IAAI,sBAAsB,KAAK,MAAA,EAAQ,MAAM;QACrD;QACA,OAAO;IACT;IAEO,cAA0B;QAC/B,OAAO,OAAO,IAAA,CAAK,MAAM;IAC3B;IAEO,SAAiB;QACtB,uPAAOC,YAAAA,EAAU,IAAA,CAAK,WAAA,CAAY,CAAC;IACrC;IAEO,WAAqD;QAC1D,IAAI,IAAA,CAAK,MAAA,KAAW,IAAI;YACtB,OAAO,IAAA,CAAK,MAAA;QACd;QACA,OAAO,GAAG,IAAA,CAAK,MAAA,CAAA,CAAA,EAAU,IAAA,CAAK,MAAA,EAAA;IAChC;IAEA,OAAO,WACL,GAAA,EACA,MAAA,EACW;QACX,MAAM,YAAY,WAAW,KAAK,MAAM;QAExC,OAAO,IAAI,OAAU,QAAQ,SAAS,GAAQ,UAAU,SAAS,CAAC;IACpE;IAEA,OAAO,cACL,MAAA,EACA,KAAA,EACW;QACX,MAAM,SAAS,OAAO,KAAK;QAC3B,OAAO,IAAI,OAAO,QAAQ,MAAM;IAClC;IAEA,OAAO,SAAiC,MAAA,EAAW,IAAA,EAAyB;QAC1E,MAAM,SAAS,OAAO,UAAU,IAAI,CAAC;QACrC,OAAO,IAAI,OAAO,QAAQ,MAAM;IAClC;AACF;AAKO,SAAS,OACd,SAAY,EAAA,EACZ,SAAiB,EAAA,EACN;IACX,OAAO,IAAI,OAAO,QAAQ,MAAM;AAClC", "ignoreList": [0, 1, 2, 3, 4, 5]}}, {"offset": {"line": 812, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@arcjet/analyze-wasm/wasm/arcjet_analyze_js_req.component.js"], "sourcesContent": ["function instantiate(getCoreModule, imports, instantiateCore = WebAssembly.instantiate) {\n  \n  class ComponentError extends Error {\n    constructor (value) {\n      const enumerable = typeof value !== 'string';\n      super(enumerable ? `${String(value)} (see error.payload)` : value);\n      Object.defineProperty(this, 'payload', { value, enumerable });\n    }\n  }\n  \n  let dv = new DataView(new ArrayBuffer());\n  const dataView = mem => dv.buffer === mem.buffer ? dv : dv = new DataView(mem.buffer);\n  \n  function throwInvalidBool() {\n    throw new TypeError('invalid variant discriminant for bool');\n  }\n  \n  function toUint32(val) {\n    return val >>> 0;\n  }\n  \n  const utf8Decoder = new TextDecoder();\n  \n  const utf8Encoder = new TextEncoder();\n  \n  let utf8EncodedLen = 0;\n  function utf8Encode(s, realloc, memory) {\n    if (typeof s !== 'string') throw new TypeError('expected a string');\n    if (s.length === 0) {\n      utf8EncodedLen = 0;\n      return 1;\n    }\n    let buf = utf8Encoder.encode(s);\n    let ptr = realloc(0, 0, 1, buf.length);\n    new Uint8Array(memory.buffer).set(buf, ptr);\n    utf8EncodedLen = buf.length;\n    return ptr;\n  }\n  \n  \n  const module0 = getCoreModule('arcjet_analyze_js_req.component.core.wasm');\n  const module1 = getCoreModule('arcjet_analyze_js_req.component.core2.wasm');\n  const module2 = getCoreModule('arcjet_analyze_js_req.component.core3.wasm');\n  \n  const { detect } = imports['arcjet:js-req/bot-identifier'];\n  const { hasGravatar, hasMxRecords, isDisposableEmail, isFreeEmail } = imports['arcjet:js-req/email-validator-overrides'];\n  const { detect: detect$1 } = imports['arcjet:js-req/sensitive-information-identifier'];\n  const { verify } = imports['arcjet:js-req/verify-bot'];\n  let gen = (function* init () {\n    let exports0;\n    let exports1;\n    let memory0;\n    let realloc0;\n    \n    function trampoline0(arg0, arg1, arg2) {\n      var ptr0 = arg0;\n      var len0 = arg1;\n      var result0 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr0, len0));\n      const ret = detect(result0);\n      var vec2 = ret;\n      var len2 = vec2.length;\n      var result2 = realloc0(0, 0, 4, len2 * 8);\n      for (let i = 0; i < vec2.length; i++) {\n        const e = vec2[i];\n        const base = result2 + i * 8;var ptr1 = utf8Encode(e, realloc0, memory0);\n        var len1 = utf8EncodedLen;\n        dataView(memory0).setInt32(base + 4, len1, true);\n        dataView(memory0).setInt32(base + 0, ptr1, true);\n      }\n      dataView(memory0).setInt32(arg2 + 4, len2, true);\n      dataView(memory0).setInt32(arg2 + 0, result2, true);\n    }\n    \n    function trampoline1(arg0, arg1, arg2, arg3) {\n      var ptr0 = arg0;\n      var len0 = arg1;\n      var result0 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr0, len0));\n      var ptr1 = arg2;\n      var len1 = arg3;\n      var result1 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr1, len1));\n      const ret = verify(result0, result1);\n      var val2 = ret;\n      let enum2;\n      switch (val2) {\n        case 'verified': {\n          enum2 = 0;\n          break;\n        }\n        case 'spoofed': {\n          enum2 = 1;\n          break;\n        }\n        case 'unverifiable': {\n          enum2 = 2;\n          break;\n        }\n        default: {\n          if ((ret) instanceof Error) {\n            console.error(ret);\n          }\n          \n          throw new TypeError(`\"${val2}\" is not one of the cases of validator-response`);\n        }\n      }\n      return enum2;\n    }\n    \n    function trampoline2(arg0, arg1) {\n      var ptr0 = arg0;\n      var len0 = arg1;\n      var result0 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr0, len0));\n      const ret = isFreeEmail(result0);\n      var val1 = ret;\n      let enum1;\n      switch (val1) {\n        case 'yes': {\n          enum1 = 0;\n          break;\n        }\n        case 'no': {\n          enum1 = 1;\n          break;\n        }\n        case 'unknown': {\n          enum1 = 2;\n          break;\n        }\n        default: {\n          if ((ret) instanceof Error) {\n            console.error(ret);\n          }\n          \n          throw new TypeError(`\"${val1}\" is not one of the cases of validator-response`);\n        }\n      }\n      return enum1;\n    }\n    \n    function trampoline3(arg0, arg1) {\n      var ptr0 = arg0;\n      var len0 = arg1;\n      var result0 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr0, len0));\n      const ret = isDisposableEmail(result0);\n      var val1 = ret;\n      let enum1;\n      switch (val1) {\n        case 'yes': {\n          enum1 = 0;\n          break;\n        }\n        case 'no': {\n          enum1 = 1;\n          break;\n        }\n        case 'unknown': {\n          enum1 = 2;\n          break;\n        }\n        default: {\n          if ((ret) instanceof Error) {\n            console.error(ret);\n          }\n          \n          throw new TypeError(`\"${val1}\" is not one of the cases of validator-response`);\n        }\n      }\n      return enum1;\n    }\n    \n    function trampoline4(arg0, arg1) {\n      var ptr0 = arg0;\n      var len0 = arg1;\n      var result0 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr0, len0));\n      const ret = hasMxRecords(result0);\n      var val1 = ret;\n      let enum1;\n      switch (val1) {\n        case 'yes': {\n          enum1 = 0;\n          break;\n        }\n        case 'no': {\n          enum1 = 1;\n          break;\n        }\n        case 'unknown': {\n          enum1 = 2;\n          break;\n        }\n        default: {\n          if ((ret) instanceof Error) {\n            console.error(ret);\n          }\n          \n          throw new TypeError(`\"${val1}\" is not one of the cases of validator-response`);\n        }\n      }\n      return enum1;\n    }\n    \n    function trampoline5(arg0, arg1) {\n      var ptr0 = arg0;\n      var len0 = arg1;\n      var result0 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr0, len0));\n      const ret = hasGravatar(result0);\n      var val1 = ret;\n      let enum1;\n      switch (val1) {\n        case 'yes': {\n          enum1 = 0;\n          break;\n        }\n        case 'no': {\n          enum1 = 1;\n          break;\n        }\n        case 'unknown': {\n          enum1 = 2;\n          break;\n        }\n        default: {\n          if ((ret) instanceof Error) {\n            console.error(ret);\n          }\n          \n          throw new TypeError(`\"${val1}\" is not one of the cases of validator-response`);\n        }\n      }\n      return enum1;\n    }\n    \n    function trampoline6(arg0, arg1, arg2) {\n      var len1 = arg1;\n      var base1 = arg0;\n      var result1 = [];\n      for (let i = 0; i < len1; i++) {\n        const base = base1 + i * 8;\n        var ptr0 = dataView(memory0).getInt32(base + 0, true);\n        var len0 = dataView(memory0).getInt32(base + 4, true);\n        var result0 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr0, len0));\n        result1.push(result0);\n      }\n      const ret = detect$1(result1);\n      var vec5 = ret;\n      var len5 = vec5.length;\n      var result5 = realloc0(0, 0, 4, len5 * 16);\n      for (let i = 0; i < vec5.length; i++) {\n        const e = vec5[i];\n        const base = result5 + i * 16;var variant4 = e;\n        if (variant4 === null || variant4=== undefined) {\n          dataView(memory0).setInt8(base + 0, 0, true);\n        } else {\n          const e = variant4;\n          dataView(memory0).setInt8(base + 0, 1, true);\n          var variant3 = e;\n          switch (variant3.tag) {\n            case 'email': {\n              dataView(memory0).setInt8(base + 4, 0, true);\n              break;\n            }\n            case 'phone-number': {\n              dataView(memory0).setInt8(base + 4, 1, true);\n              break;\n            }\n            case 'ip-address': {\n              dataView(memory0).setInt8(base + 4, 2, true);\n              break;\n            }\n            case 'credit-card-number': {\n              dataView(memory0).setInt8(base + 4, 3, true);\n              break;\n            }\n            case 'custom': {\n              const e = variant3.val;\n              dataView(memory0).setInt8(base + 4, 4, true);\n              var ptr2 = utf8Encode(e, realloc0, memory0);\n              var len2 = utf8EncodedLen;\n              dataView(memory0).setInt32(base + 12, len2, true);\n              dataView(memory0).setInt32(base + 8, ptr2, true);\n              break;\n            }\n            default: {\n              throw new TypeError(`invalid variant tag value \\`${JSON.stringify(variant3.tag)}\\` (received \\`${variant3}\\`) specified for \\`SensitiveInfoEntity\\``);\n            }\n          }\n        }\n      }\n      dataView(memory0).setInt32(arg2 + 4, len5, true);\n      dataView(memory0).setInt32(arg2 + 0, result5, true);\n    }\n    let exports2;\n    let postReturn0;\n    let postReturn1;\n    let postReturn2;\n    let postReturn3;\n    let postReturn4;\n    Promise.all([module0, module1, module2]).catch(() => {});\n    ({ exports: exports0 } = yield instantiateCore(yield module1));\n    ({ exports: exports1 } = yield instantiateCore(yield module0, {\n      'arcjet:js-req/bot-identifier': {\n        detect: exports0['0'],\n      },\n      'arcjet:js-req/email-validator-overrides': {\n        'has-gravatar': exports0['5'],\n        'has-mx-records': exports0['4'],\n        'is-disposable-email': exports0['3'],\n        'is-free-email': exports0['2'],\n      },\n      'arcjet:js-req/sensitive-information-identifier': {\n        detect: exports0['6'],\n      },\n      'arcjet:js-req/verify-bot': {\n        verify: exports0['1'],\n      },\n    }));\n    memory0 = exports1.memory;\n    realloc0 = exports1.cabi_realloc;\n    ({ exports: exports2 } = yield instantiateCore(yield module2, {\n      '': {\n        $imports: exports0.$imports,\n        '0': trampoline0,\n        '1': trampoline1,\n        '2': trampoline2,\n        '3': trampoline3,\n        '4': trampoline4,\n        '5': trampoline5,\n        '6': trampoline6,\n      },\n    }));\n    postReturn0 = exports1['cabi_post_detect-bot'];\n    postReturn1 = exports1['cabi_post_generate-fingerprint'];\n    postReturn2 = exports1['cabi_post_validate-characteristics'];\n    postReturn3 = exports1['cabi_post_is-valid-email'];\n    postReturn4 = exports1['cabi_post_detect-sensitive-info'];\n    \n    function detectBot(arg0, arg1) {\n      var ptr0 = utf8Encode(arg0, realloc0, memory0);\n      var len0 = utf8EncodedLen;\n      var variant7 = arg1;\n      let variant7_0;\n      let variant7_1;\n      let variant7_2;\n      let variant7_3;\n      switch (variant7.tag) {\n        case 'allowed-bot-config': {\n          const e = variant7.val;\n          var {entities: v1_0, skipCustomDetect: v1_1 } = e;\n          var vec3 = v1_0;\n          var len3 = vec3.length;\n          var result3 = realloc0(0, 0, 4, len3 * 8);\n          for (let i = 0; i < vec3.length; i++) {\n            const e = vec3[i];\n            const base = result3 + i * 8;var ptr2 = utf8Encode(e, realloc0, memory0);\n            var len2 = utf8EncodedLen;\n            dataView(memory0).setInt32(base + 4, len2, true);\n            dataView(memory0).setInt32(base + 0, ptr2, true);\n          }\n          variant7_0 = 0;\n          variant7_1 = result3;\n          variant7_2 = len3;\n          variant7_3 = v1_1 ? 1 : 0;\n          break;\n        }\n        case 'denied-bot-config': {\n          const e = variant7.val;\n          var {entities: v4_0, skipCustomDetect: v4_1 } = e;\n          var vec6 = v4_0;\n          var len6 = vec6.length;\n          var result6 = realloc0(0, 0, 4, len6 * 8);\n          for (let i = 0; i < vec6.length; i++) {\n            const e = vec6[i];\n            const base = result6 + i * 8;var ptr5 = utf8Encode(e, realloc0, memory0);\n            var len5 = utf8EncodedLen;\n            dataView(memory0).setInt32(base + 4, len5, true);\n            dataView(memory0).setInt32(base + 0, ptr5, true);\n          }\n          variant7_0 = 1;\n          variant7_1 = result6;\n          variant7_2 = len6;\n          variant7_3 = v4_1 ? 1 : 0;\n          break;\n        }\n        default: {\n          throw new TypeError(`invalid variant tag value \\`${JSON.stringify(variant7.tag)}\\` (received \\`${variant7}\\`) specified for \\`BotConfig\\``);\n        }\n      }\n      const ret = exports1['detect-bot'](ptr0, len0, variant7_0, variant7_1, variant7_2, variant7_3);\n      let variant15;\n      switch (dataView(memory0).getUint8(ret + 0, true)) {\n        case 0: {\n          var len9 = dataView(memory0).getInt32(ret + 8, true);\n          var base9 = dataView(memory0).getInt32(ret + 4, true);\n          var result9 = [];\n          for (let i = 0; i < len9; i++) {\n            const base = base9 + i * 8;\n            var ptr8 = dataView(memory0).getInt32(base + 0, true);\n            var len8 = dataView(memory0).getInt32(base + 4, true);\n            var result8 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr8, len8));\n            result9.push(result8);\n          }\n          var len11 = dataView(memory0).getInt32(ret + 16, true);\n          var base11 = dataView(memory0).getInt32(ret + 12, true);\n          var result11 = [];\n          for (let i = 0; i < len11; i++) {\n            const base = base11 + i * 8;\n            var ptr10 = dataView(memory0).getInt32(base + 0, true);\n            var len10 = dataView(memory0).getInt32(base + 4, true);\n            var result10 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr10, len10));\n            result11.push(result10);\n          }\n          var bool12 = dataView(memory0).getUint8(ret + 20, true);\n          var bool13 = dataView(memory0).getUint8(ret + 21, true);\n          variant15= {\n            tag: 'ok',\n            val: {\n              allowed: result9,\n              denied: result11,\n              verified: bool12 == 0 ? false : (bool12 == 1 ? true : throwInvalidBool()),\n              spoofed: bool13 == 0 ? false : (bool13 == 1 ? true : throwInvalidBool()),\n            }\n          };\n          break;\n        }\n        case 1: {\n          var ptr14 = dataView(memory0).getInt32(ret + 4, true);\n          var len14 = dataView(memory0).getInt32(ret + 8, true);\n          var result14 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr14, len14));\n          variant15= {\n            tag: 'err',\n            val: result14\n          };\n          break;\n        }\n        default: {\n          throw new TypeError('invalid variant discriminant for expected');\n        }\n      }\n      const retVal = variant15;\n      postReturn0(ret);\n      if (typeof retVal === 'object' && retVal.tag === 'err') {\n        throw new ComponentError(retVal.val);\n      }\n      return retVal.val;\n    }\n    \n    function generateFingerprint(arg0, arg1) {\n      var ptr0 = utf8Encode(arg0, realloc0, memory0);\n      var len0 = utf8EncodedLen;\n      var vec2 = arg1;\n      var len2 = vec2.length;\n      var result2 = realloc0(0, 0, 4, len2 * 8);\n      for (let i = 0; i < vec2.length; i++) {\n        const e = vec2[i];\n        const base = result2 + i * 8;var ptr1 = utf8Encode(e, realloc0, memory0);\n        var len1 = utf8EncodedLen;\n        dataView(memory0).setInt32(base + 4, len1, true);\n        dataView(memory0).setInt32(base + 0, ptr1, true);\n      }\n      const ret = exports1['generate-fingerprint'](ptr0, len0, result2, len2);\n      let variant5;\n      switch (dataView(memory0).getUint8(ret + 0, true)) {\n        case 0: {\n          var ptr3 = dataView(memory0).getInt32(ret + 4, true);\n          var len3 = dataView(memory0).getInt32(ret + 8, true);\n          var result3 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr3, len3));\n          variant5= {\n            tag: 'ok',\n            val: result3\n          };\n          break;\n        }\n        case 1: {\n          var ptr4 = dataView(memory0).getInt32(ret + 4, true);\n          var len4 = dataView(memory0).getInt32(ret + 8, true);\n          var result4 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr4, len4));\n          variant5= {\n            tag: 'err',\n            val: result4\n          };\n          break;\n        }\n        default: {\n          throw new TypeError('invalid variant discriminant for expected');\n        }\n      }\n      const retVal = variant5;\n      postReturn1(ret);\n      if (typeof retVal === 'object' && retVal.tag === 'err') {\n        throw new ComponentError(retVal.val);\n      }\n      return retVal.val;\n    }\n    \n    function validateCharacteristics(arg0, arg1) {\n      var ptr0 = utf8Encode(arg0, realloc0, memory0);\n      var len0 = utf8EncodedLen;\n      var vec2 = arg1;\n      var len2 = vec2.length;\n      var result2 = realloc0(0, 0, 4, len2 * 8);\n      for (let i = 0; i < vec2.length; i++) {\n        const e = vec2[i];\n        const base = result2 + i * 8;var ptr1 = utf8Encode(e, realloc0, memory0);\n        var len1 = utf8EncodedLen;\n        dataView(memory0).setInt32(base + 4, len1, true);\n        dataView(memory0).setInt32(base + 0, ptr1, true);\n      }\n      const ret = exports1['validate-characteristics'](ptr0, len0, result2, len2);\n      let variant4;\n      switch (dataView(memory0).getUint8(ret + 0, true)) {\n        case 0: {\n          variant4= {\n            tag: 'ok',\n            val: undefined\n          };\n          break;\n        }\n        case 1: {\n          var ptr3 = dataView(memory0).getInt32(ret + 4, true);\n          var len3 = dataView(memory0).getInt32(ret + 8, true);\n          var result3 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr3, len3));\n          variant4= {\n            tag: 'err',\n            val: result3\n          };\n          break;\n        }\n        default: {\n          throw new TypeError('invalid variant discriminant for expected');\n        }\n      }\n      const retVal = variant4;\n      postReturn2(ret);\n      if (typeof retVal === 'object' && retVal.tag === 'err') {\n        throw new ComponentError(retVal.val);\n      }\n      return retVal.val;\n    }\n    \n    function isValidEmail(arg0, arg1) {\n      var ptr0 = utf8Encode(arg0, realloc0, memory0);\n      var len0 = utf8EncodedLen;\n      var variant7 = arg1;\n      let variant7_0;\n      let variant7_1;\n      let variant7_2;\n      let variant7_3;\n      let variant7_4;\n      switch (variant7.tag) {\n        case 'allow-email-validation-config': {\n          const e = variant7.val;\n          var {requireTopLevelDomain: v1_0, allowDomainLiteral: v1_1, allow: v1_2 } = e;\n          var vec3 = v1_2;\n          var len3 = vec3.length;\n          var result3 = realloc0(0, 0, 4, len3 * 8);\n          for (let i = 0; i < vec3.length; i++) {\n            const e = vec3[i];\n            const base = result3 + i * 8;var ptr2 = utf8Encode(e, realloc0, memory0);\n            var len2 = utf8EncodedLen;\n            dataView(memory0).setInt32(base + 4, len2, true);\n            dataView(memory0).setInt32(base + 0, ptr2, true);\n          }\n          variant7_0 = 0;\n          variant7_1 = v1_0 ? 1 : 0;\n          variant7_2 = v1_1 ? 1 : 0;\n          variant7_3 = result3;\n          variant7_4 = len3;\n          break;\n        }\n        case 'deny-email-validation-config': {\n          const e = variant7.val;\n          var {requireTopLevelDomain: v4_0, allowDomainLiteral: v4_1, deny: v4_2 } = e;\n          var vec6 = v4_2;\n          var len6 = vec6.length;\n          var result6 = realloc0(0, 0, 4, len6 * 8);\n          for (let i = 0; i < vec6.length; i++) {\n            const e = vec6[i];\n            const base = result6 + i * 8;var ptr5 = utf8Encode(e, realloc0, memory0);\n            var len5 = utf8EncodedLen;\n            dataView(memory0).setInt32(base + 4, len5, true);\n            dataView(memory0).setInt32(base + 0, ptr5, true);\n          }\n          variant7_0 = 1;\n          variant7_1 = v4_0 ? 1 : 0;\n          variant7_2 = v4_1 ? 1 : 0;\n          variant7_3 = result6;\n          variant7_4 = len6;\n          break;\n        }\n        default: {\n          throw new TypeError(`invalid variant tag value \\`${JSON.stringify(variant7.tag)}\\` (received \\`${variant7}\\`) specified for \\`EmailValidationConfig\\``);\n        }\n      }\n      const ret = exports1['is-valid-email'](ptr0, len0, variant7_0, variant7_1, variant7_2, variant7_3, variant7_4);\n      let variant12;\n      switch (dataView(memory0).getUint8(ret + 0, true)) {\n        case 0: {\n          let enum8;\n          switch (dataView(memory0).getUint8(ret + 4, true)) {\n            case 0: {\n              enum8 = 'valid';\n              break;\n            }\n            case 1: {\n              enum8 = 'invalid';\n              break;\n            }\n            default: {\n              throw new TypeError('invalid discriminant specified for EmailValidity');\n            }\n          }\n          var len10 = dataView(memory0).getInt32(ret + 12, true);\n          var base10 = dataView(memory0).getInt32(ret + 8, true);\n          var result10 = [];\n          for (let i = 0; i < len10; i++) {\n            const base = base10 + i * 8;\n            var ptr9 = dataView(memory0).getInt32(base + 0, true);\n            var len9 = dataView(memory0).getInt32(base + 4, true);\n            var result9 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr9, len9));\n            result10.push(result9);\n          }\n          variant12= {\n            tag: 'ok',\n            val: {\n              validity: enum8,\n              blocked: result10,\n            }\n          };\n          break;\n        }\n        case 1: {\n          var ptr11 = dataView(memory0).getInt32(ret + 4, true);\n          var len11 = dataView(memory0).getInt32(ret + 8, true);\n          var result11 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr11, len11));\n          variant12= {\n            tag: 'err',\n            val: result11\n          };\n          break;\n        }\n        default: {\n          throw new TypeError('invalid variant discriminant for expected');\n        }\n      }\n      const retVal = variant12;\n      postReturn3(ret);\n      if (typeof retVal === 'object' && retVal.tag === 'err') {\n        throw new ComponentError(retVal.val);\n      }\n      return retVal.val;\n    }\n    \n    function detectSensitiveInfo(arg0, arg1) {\n      var ptr0 = utf8Encode(arg0, realloc0, memory0);\n      var len0 = utf8EncodedLen;\n      var {entities: v1_0, contextWindowSize: v1_1, skipCustomDetect: v1_2 } = arg1;\n      var variant8 = v1_0;\n      let variant8_0;\n      let variant8_1;\n      let variant8_2;\n      switch (variant8.tag) {\n        case 'allow': {\n          const e = variant8.val;\n          var vec4 = e;\n          var len4 = vec4.length;\n          var result4 = realloc0(0, 0, 4, len4 * 12);\n          for (let i = 0; i < vec4.length; i++) {\n            const e = vec4[i];\n            const base = result4 + i * 12;var variant3 = e;\n            switch (variant3.tag) {\n              case 'email': {\n                dataView(memory0).setInt8(base + 0, 0, true);\n                break;\n              }\n              case 'phone-number': {\n                dataView(memory0).setInt8(base + 0, 1, true);\n                break;\n              }\n              case 'ip-address': {\n                dataView(memory0).setInt8(base + 0, 2, true);\n                break;\n              }\n              case 'credit-card-number': {\n                dataView(memory0).setInt8(base + 0, 3, true);\n                break;\n              }\n              case 'custom': {\n                const e = variant3.val;\n                dataView(memory0).setInt8(base + 0, 4, true);\n                var ptr2 = utf8Encode(e, realloc0, memory0);\n                var len2 = utf8EncodedLen;\n                dataView(memory0).setInt32(base + 8, len2, true);\n                dataView(memory0).setInt32(base + 4, ptr2, true);\n                break;\n              }\n              default: {\n                throw new TypeError(`invalid variant tag value \\`${JSON.stringify(variant3.tag)}\\` (received \\`${variant3}\\`) specified for \\`SensitiveInfoEntity\\``);\n              }\n            }\n          }\n          variant8_0 = 0;\n          variant8_1 = result4;\n          variant8_2 = len4;\n          break;\n        }\n        case 'deny': {\n          const e = variant8.val;\n          var vec7 = e;\n          var len7 = vec7.length;\n          var result7 = realloc0(0, 0, 4, len7 * 12);\n          for (let i = 0; i < vec7.length; i++) {\n            const e = vec7[i];\n            const base = result7 + i * 12;var variant6 = e;\n            switch (variant6.tag) {\n              case 'email': {\n                dataView(memory0).setInt8(base + 0, 0, true);\n                break;\n              }\n              case 'phone-number': {\n                dataView(memory0).setInt8(base + 0, 1, true);\n                break;\n              }\n              case 'ip-address': {\n                dataView(memory0).setInt8(base + 0, 2, true);\n                break;\n              }\n              case 'credit-card-number': {\n                dataView(memory0).setInt8(base + 0, 3, true);\n                break;\n              }\n              case 'custom': {\n                const e = variant6.val;\n                dataView(memory0).setInt8(base + 0, 4, true);\n                var ptr5 = utf8Encode(e, realloc0, memory0);\n                var len5 = utf8EncodedLen;\n                dataView(memory0).setInt32(base + 8, len5, true);\n                dataView(memory0).setInt32(base + 4, ptr5, true);\n                break;\n              }\n              default: {\n                throw new TypeError(`invalid variant tag value \\`${JSON.stringify(variant6.tag)}\\` (received \\`${variant6}\\`) specified for \\`SensitiveInfoEntity\\``);\n              }\n            }\n          }\n          variant8_0 = 1;\n          variant8_1 = result7;\n          variant8_2 = len7;\n          break;\n        }\n        default: {\n          throw new TypeError(`invalid variant tag value \\`${JSON.stringify(variant8.tag)}\\` (received \\`${variant8}\\`) specified for \\`SensitiveInfoEntities\\``);\n        }\n      }\n      var variant9 = v1_1;\n      let variant9_0;\n      let variant9_1;\n      if (variant9 === null || variant9=== undefined) {\n        variant9_0 = 0;\n        variant9_1 = 0;\n      } else {\n        const e = variant9;\n        variant9_0 = 1;\n        variant9_1 = toUint32(e);\n      }\n      const ret = exports1['detect-sensitive-info'](ptr0, len0, variant8_0, variant8_1, variant8_2, variant9_0, variant9_1, v1_2 ? 1 : 0);\n      var len12 = dataView(memory0).getInt32(ret + 4, true);\n      var base12 = dataView(memory0).getInt32(ret + 0, true);\n      var result12 = [];\n      for (let i = 0; i < len12; i++) {\n        const base = base12 + i * 20;\n        let variant11;\n        switch (dataView(memory0).getUint8(base + 8, true)) {\n          case 0: {\n            variant11= {\n              tag: 'email',\n            };\n            break;\n          }\n          case 1: {\n            variant11= {\n              tag: 'phone-number',\n            };\n            break;\n          }\n          case 2: {\n            variant11= {\n              tag: 'ip-address',\n            };\n            break;\n          }\n          case 3: {\n            variant11= {\n              tag: 'credit-card-number',\n            };\n            break;\n          }\n          case 4: {\n            var ptr10 = dataView(memory0).getInt32(base + 12, true);\n            var len10 = dataView(memory0).getInt32(base + 16, true);\n            var result10 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr10, len10));\n            variant11= {\n              tag: 'custom',\n              val: result10\n            };\n            break;\n          }\n          default: {\n            throw new TypeError('invalid variant discriminant for SensitiveInfoEntity');\n          }\n        }\n        result12.push({\n          start: dataView(memory0).getInt32(base + 0, true) >>> 0,\n          end: dataView(memory0).getInt32(base + 4, true) >>> 0,\n          identifiedType: variant11,\n        });\n      }\n      var len15 = dataView(memory0).getInt32(ret + 12, true);\n      var base15 = dataView(memory0).getInt32(ret + 8, true);\n      var result15 = [];\n      for (let i = 0; i < len15; i++) {\n        const base = base15 + i * 20;\n        let variant14;\n        switch (dataView(memory0).getUint8(base + 8, true)) {\n          case 0: {\n            variant14= {\n              tag: 'email',\n            };\n            break;\n          }\n          case 1: {\n            variant14= {\n              tag: 'phone-number',\n            };\n            break;\n          }\n          case 2: {\n            variant14= {\n              tag: 'ip-address',\n            };\n            break;\n          }\n          case 3: {\n            variant14= {\n              tag: 'credit-card-number',\n            };\n            break;\n          }\n          case 4: {\n            var ptr13 = dataView(memory0).getInt32(base + 12, true);\n            var len13 = dataView(memory0).getInt32(base + 16, true);\n            var result13 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr13, len13));\n            variant14= {\n              tag: 'custom',\n              val: result13\n            };\n            break;\n          }\n          default: {\n            throw new TypeError('invalid variant discriminant for SensitiveInfoEntity');\n          }\n        }\n        result15.push({\n          start: dataView(memory0).getInt32(base + 0, true) >>> 0,\n          end: dataView(memory0).getInt32(base + 4, true) >>> 0,\n          identifiedType: variant14,\n        });\n      }\n      const retVal = {\n        allowed: result12,\n        denied: result15,\n      };\n      postReturn4(ret);\n      return retVal;\n    }\n    \n    return { detectBot, detectSensitiveInfo, generateFingerprint, isValidEmail, validateCharacteristics,  };\n  })();\n  let promise, resolve, reject;\n  function runNext (value) {\n    try {\n      let done;\n      do {\n        ({ value, done } = gen.next(value));\n      } while (!(value instanceof Promise) && !done);\n      if (done) {\n        if (resolve) resolve(value);\n        else return value;\n      }\n      if (!promise) promise = new Promise((_resolve, _reject) => (resolve = _resolve, reject = _reject));\n      value.then(nextVal => done ? resolve() : runNext(nextVal), reject);\n    }\n    catch (e) {\n      if (reject) reject(e);\n      else throw e;\n    }\n  }\n  const maybeSyncReturn = runNext(null);\n  return promise || maybeSyncReturn;\n}\n\nexport { instantiate };\n"], "names": [], "mappings": ";;;AAAA,SAAS,YAAY,aAAa,EAAE,OAAO,EAAE,kBAAkB,YAAY,WAAW;IAEpF,MAAM,uBAAuB;QAC3B,YAAa,KAAK,CAAE;YAClB,MAAM,aAAa,OAAO,UAAU;YACpC,KAAK,CAAC,aAAa,GAAG,OAAO,OAAO,oBAAoB,CAAC,GAAG;YAC5D,OAAO,cAAc,CAAC,IAAI,EAAE,WAAW;gBAAE;gBAAO;YAAW;QAC7D;IACF;IAEA,IAAI,KAAK,IAAI,SAAS,IAAI;IAC1B,MAAM,WAAW,CAAA,MAAO,GAAG,MAAM,KAAK,IAAI,MAAM,GAAG,KAAK,KAAK,IAAI,SAAS,IAAI,MAAM;IAEpF,SAAS;QACP,MAAM,IAAI,UAAU;IACtB;IAEA,SAAS,SAAS,GAAG;QACnB,OAAO,QAAQ;IACjB;IAEA,MAAM,cAAc,IAAI;IAExB,MAAM,cAAc,IAAI;IAExB,IAAI,iBAAiB;IACrB,SAAS,WAAW,CAAC,EAAE,OAAO,EAAE,MAAM;QACpC,IAAI,OAAO,MAAM,UAAU,MAAM,IAAI,UAAU;QAC/C,IAAI,EAAE,MAAM,KAAK,GAAG;YAClB,iBAAiB;YACjB,OAAO;QACT;QACA,IAAI,MAAM,YAAY,MAAM,CAAC;QAC7B,IAAI,MAAM,QAAQ,GAAG,GAAG,GAAG,IAAI,MAAM;QACrC,IAAI,WAAW,OAAO,MAAM,EAAE,GAAG,CAAC,KAAK;QACvC,iBAAiB,IAAI,MAAM;QAC3B,OAAO;IACT;IAGA,MAAM,UAAU,cAAc;IAC9B,MAAM,UAAU,cAAc;IAC9B,MAAM,UAAU,cAAc;IAE9B,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,+BAA+B;IAC1D,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC,0CAA0C;IACxH,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,OAAO,CAAC,iDAAiD;IACtF,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,2BAA2B;IACtD,IAAI,MAAM,AAAC,UAAU;QACnB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QAEJ,SAAS,YAAY,IAAI,EAAE,IAAI,EAAE,IAAI;YACnC,IAAI,OAAO;YACX,IAAI,OAAO;YACX,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;YACtE,MAAM,MAAM,OAAO;YACnB,IAAI,OAAO;YACX,IAAI,OAAO,KAAK,MAAM;YACtB,IAAI,UAAU,SAAS,GAAG,GAAG,GAAG,OAAO;YACvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;gBACpC,MAAM,IAAI,IAAI,CAAC,EAAE;gBACjB,MAAM,OAAO,UAAU,IAAI;gBAAE,IAAI,OAAO,WAAW,GAAG,UAAU;gBAChE,IAAI,OAAO;gBACX,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;gBAC3C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;YAC7C;YACA,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;YAC3C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,SAAS;QAChD;QAEA,SAAS,YAAY,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;YACzC,IAAI,OAAO;YACX,IAAI,OAAO;YACX,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;YACtE,IAAI,OAAO;YACX,IAAI,OAAO;YACX,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;YACtE,MAAM,MAAM,OAAO,SAAS;YAC5B,IAAI,OAAO;YACX,IAAI;YACJ,OAAQ;gBACN,KAAK;oBAAY;wBACf,QAAQ;wBACR;oBACF;gBACA,KAAK;oBAAW;wBACd,QAAQ;wBACR;oBACF;gBACA,KAAK;oBAAgB;wBACnB,QAAQ;wBACR;oBACF;gBACA;oBAAS;wBACP,IAAI,AAAC,eAAgB,OAAO;4BAC1B,QAAQ,KAAK,CAAC;wBAChB;wBAEA,MAAM,IAAI,UAAU,CAAC,CAAC,EAAE,KAAK,+CAA+C,CAAC;oBAC/E;YACF;YACA,OAAO;QACT;QAEA,SAAS,YAAY,IAAI,EAAE,IAAI;YAC7B,IAAI,OAAO;YACX,IAAI,OAAO;YACX,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;YACtE,MAAM,MAAM,YAAY;YACxB,IAAI,OAAO;YACX,IAAI;YACJ,OAAQ;gBACN,KAAK;oBAAO;wBACV,QAAQ;wBACR;oBACF;gBACA,KAAK;oBAAM;wBACT,QAAQ;wBACR;oBACF;gBACA,KAAK;oBAAW;wBACd,QAAQ;wBACR;oBACF;gBACA;oBAAS;wBACP,IAAI,AAAC,eAAgB,OAAO;4BAC1B,QAAQ,KAAK,CAAC;wBAChB;wBAEA,MAAM,IAAI,UAAU,CAAC,CAAC,EAAE,KAAK,+CAA+C,CAAC;oBAC/E;YACF;YACA,OAAO;QACT;QAEA,SAAS,YAAY,IAAI,EAAE,IAAI;YAC7B,IAAI,OAAO;YACX,IAAI,OAAO;YACX,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;YACtE,MAAM,MAAM,kBAAkB;YAC9B,IAAI,OAAO;YACX,IAAI;YACJ,OAAQ;gBACN,KAAK;oBAAO;wBACV,QAAQ;wBACR;oBACF;gBACA,KAAK;oBAAM;wBACT,QAAQ;wBACR;oBACF;gBACA,KAAK;oBAAW;wBACd,QAAQ;wBACR;oBACF;gBACA;oBAAS;wBACP,IAAI,AAAC,eAAgB,OAAO;4BAC1B,QAAQ,KAAK,CAAC;wBAChB;wBAEA,MAAM,IAAI,UAAU,CAAC,CAAC,EAAE,KAAK,+CAA+C,CAAC;oBAC/E;YACF;YACA,OAAO;QACT;QAEA,SAAS,YAAY,IAAI,EAAE,IAAI;YAC7B,IAAI,OAAO;YACX,IAAI,OAAO;YACX,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;YACtE,MAAM,MAAM,aAAa;YACzB,IAAI,OAAO;YACX,IAAI;YACJ,OAAQ;gBACN,KAAK;oBAAO;wBACV,QAAQ;wBACR;oBACF;gBACA,KAAK;oBAAM;wBACT,QAAQ;wBACR;oBACF;gBACA,KAAK;oBAAW;wBACd,QAAQ;wBACR;oBACF;gBACA;oBAAS;wBACP,IAAI,AAAC,eAAgB,OAAO;4BAC1B,QAAQ,KAAK,CAAC;wBAChB;wBAEA,MAAM,IAAI,UAAU,CAAC,CAAC,EAAE,KAAK,+CAA+C,CAAC;oBAC/E;YACF;YACA,OAAO;QACT;QAEA,SAAS,YAAY,IAAI,EAAE,IAAI;YAC7B,IAAI,OAAO;YACX,IAAI,OAAO;YACX,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;YACtE,MAAM,MAAM,YAAY;YACxB,IAAI,OAAO;YACX,IAAI;YACJ,OAAQ;gBACN,KAAK;oBAAO;wBACV,QAAQ;wBACR;oBACF;gBACA,KAAK;oBAAM;wBACT,QAAQ;wBACR;oBACF;gBACA,KAAK;oBAAW;wBACd,QAAQ;wBACR;oBACF;gBACA;oBAAS;wBACP,IAAI,AAAC,eAAgB,OAAO;4BAC1B,QAAQ,KAAK,CAAC;wBAChB;wBAEA,MAAM,IAAI,UAAU,CAAC,CAAC,EAAE,KAAK,+CAA+C,CAAC;oBAC/E;YACF;YACA,OAAO;QACT;QAEA,SAAS,YAAY,IAAI,EAAE,IAAI,EAAE,IAAI;YACnC,IAAI,OAAO;YACX,IAAI,QAAQ;YACZ,IAAI,UAAU,EAAE;YAChB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;gBAC7B,MAAM,OAAO,QAAQ,IAAI;gBACzB,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG;gBAChD,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG;gBAChD,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;gBACtE,QAAQ,IAAI,CAAC;YACf;YACA,MAAM,MAAM,SAAS;YACrB,IAAI,OAAO;YACX,IAAI,OAAO,KAAK,MAAM;YACtB,IAAI,UAAU,SAAS,GAAG,GAAG,GAAG,OAAO;YACvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;gBACpC,MAAM,IAAI,IAAI,CAAC,EAAE;gBACjB,MAAM,OAAO,UAAU,IAAI;gBAAG,IAAI,WAAW;gBAC7C,IAAI,aAAa,QAAQ,aAAY,WAAW;oBAC9C,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;gBACzC,OAAO;oBACL,MAAM,IAAI;oBACV,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;oBACvC,IAAI,WAAW;oBACf,OAAQ,SAAS,GAAG;wBAClB,KAAK;4BAAS;gCACZ,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;gCACvC;4BACF;wBACA,KAAK;4BAAgB;gCACnB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;gCACvC;4BACF;wBACA,KAAK;4BAAc;gCACjB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;gCACvC;4BACF;wBACA,KAAK;4BAAsB;gCACzB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;gCACvC;4BACF;wBACA,KAAK;4BAAU;gCACb,MAAM,IAAI,SAAS,GAAG;gCACtB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;gCACvC,IAAI,OAAO,WAAW,GAAG,UAAU;gCACnC,IAAI,OAAO;gCACX,SAAS,SAAS,QAAQ,CAAC,OAAO,IAAI,MAAM;gCAC5C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;gCAC3C;4BACF;wBACA;4BAAS;gCACP,MAAM,IAAI,UAAU,CAAC,4BAA4B,EAAE,KAAK,SAAS,CAAC,SAAS,GAAG,EAAE,eAAe,EAAE,SAAS,yCAAyC,CAAC;4BACtJ;oBACF;gBACF;YACF;YACA,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;YAC3C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,SAAS;QAChD;QACA,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,QAAQ,GAAG,CAAC;YAAC;YAAS;YAAS;SAAQ,EAAE,KAAK,CAAC,KAAO;QACtD,CAAC,EAAE,SAAS,QAAQ,EAAE,GAAG,MAAM,gBAAgB,CAAA,MAAM,OAAM,EAAE;QAC7D,CAAC,EAAE,SAAS,QAAQ,EAAE,GAAG,MAAM,gBAAgB,CAAA,MAAM,OAAM,GAAG;YAC5D,gCAAgC;gBAC9B,QAAQ,QAAQ,CAAC,IAAI;YACvB;YACA,2CAA2C;gBACzC,gBAAgB,QAAQ,CAAC,IAAI;gBAC7B,kBAAkB,QAAQ,CAAC,IAAI;gBAC/B,uBAAuB,QAAQ,CAAC,IAAI;gBACpC,iBAAiB,QAAQ,CAAC,IAAI;YAChC;YACA,kDAAkD;gBAChD,QAAQ,QAAQ,CAAC,IAAI;YACvB;YACA,4BAA4B;gBAC1B,QAAQ,QAAQ,CAAC,IAAI;YACvB;QACF,EAAE;QACF,UAAU,SAAS,MAAM;QACzB,WAAW,SAAS,YAAY;QAChC,CAAC,EAAE,SAAS,QAAQ,EAAE,GAAG,MAAM,gBAAgB,CAAA,MAAM,OAAM,GAAG;YAC5D,IAAI;gBACF,UAAU,SAAS,QAAQ;gBAC3B,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;QACF,EAAE;QACF,cAAc,QAAQ,CAAC,uBAAuB;QAC9C,cAAc,QAAQ,CAAC,iCAAiC;QACxD,cAAc,QAAQ,CAAC,qCAAqC;QAC5D,cAAc,QAAQ,CAAC,2BAA2B;QAClD,cAAc,QAAQ,CAAC,kCAAkC;QAEzD,SAAS,UAAU,IAAI,EAAE,IAAI;YAC3B,IAAI,OAAO,WAAW,MAAM,UAAU;YACtC,IAAI,OAAO;YACX,IAAI,WAAW;YACf,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,OAAQ,SAAS,GAAG;gBAClB,KAAK;oBAAsB;wBACzB,MAAM,IAAI,SAAS,GAAG;wBACtB,IAAI,EAAC,UAAU,IAAI,EAAE,kBAAkB,IAAI,EAAE,GAAG;wBAChD,IAAI,OAAO;wBACX,IAAI,OAAO,KAAK,MAAM;wBACtB,IAAI,UAAU,SAAS,GAAG,GAAG,GAAG,OAAO;wBACvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;4BACpC,MAAM,IAAI,IAAI,CAAC,EAAE;4BACjB,MAAM,OAAO,UAAU,IAAI;4BAAE,IAAI,OAAO,WAAW,GAAG,UAAU;4BAChE,IAAI,OAAO;4BACX,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;4BAC3C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;wBAC7C;wBACA,aAAa;wBACb,aAAa;wBACb,aAAa;wBACb,aAAa,OAAO,IAAI;wBACxB;oBACF;gBACA,KAAK;oBAAqB;wBACxB,MAAM,IAAI,SAAS,GAAG;wBACtB,IAAI,EAAC,UAAU,IAAI,EAAE,kBAAkB,IAAI,EAAE,GAAG;wBAChD,IAAI,OAAO;wBACX,IAAI,OAAO,KAAK,MAAM;wBACtB,IAAI,UAAU,SAAS,GAAG,GAAG,GAAG,OAAO;wBACvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;4BACpC,MAAM,IAAI,IAAI,CAAC,EAAE;4BACjB,MAAM,OAAO,UAAU,IAAI;4BAAE,IAAI,OAAO,WAAW,GAAG,UAAU;4BAChE,IAAI,OAAO;4BACX,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;4BAC3C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;wBAC7C;wBACA,aAAa;wBACb,aAAa;wBACb,aAAa;wBACb,aAAa,OAAO,IAAI;wBACxB;oBACF;gBACA;oBAAS;wBACP,MAAM,IAAI,UAAU,CAAC,4BAA4B,EAAE,KAAK,SAAS,CAAC,SAAS,GAAG,EAAE,eAAe,EAAE,SAAS,+BAA+B,CAAC;oBAC5I;YACF;YACA,MAAM,MAAM,QAAQ,CAAC,aAAa,CAAC,MAAM,MAAM,YAAY,YAAY,YAAY;YACnF,IAAI;YACJ,OAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;gBAC1C,KAAK;oBAAG;wBACN,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAC/C,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAChD,IAAI,UAAU,EAAE;wBAChB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;4BAC7B,MAAM,OAAO,QAAQ,IAAI;4BACzB,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG;4BAChD,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG;4BAChD,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;4BACtE,QAAQ,IAAI,CAAC;wBACf;wBACA,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,IAAI;wBACjD,IAAI,SAAS,SAAS,SAAS,QAAQ,CAAC,MAAM,IAAI;wBAClD,IAAI,WAAW,EAAE;wBACjB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;4BAC9B,MAAM,OAAO,SAAS,IAAI;4BAC1B,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG;4BACjD,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG;4BACjD,IAAI,WAAW,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,OAAO;4BACxE,SAAS,IAAI,CAAC;wBAChB;wBACA,IAAI,SAAS,SAAS,SAAS,QAAQ,CAAC,MAAM,IAAI;wBAClD,IAAI,SAAS,SAAS,SAAS,QAAQ,CAAC,MAAM,IAAI;wBAClD,YAAW;4BACT,KAAK;4BACL,KAAK;gCACH,SAAS;gCACT,QAAQ;gCACR,UAAU,UAAU,IAAI,QAAS,UAAU,IAAI,OAAO;gCACtD,SAAS,UAAU,IAAI,QAAS,UAAU,IAAI,OAAO;4BACvD;wBACF;wBACA;oBACF;gBACA,KAAK;oBAAG;wBACN,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAChD,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAChD,IAAI,WAAW,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,OAAO;wBACxE,YAAW;4BACT,KAAK;4BACL,KAAK;wBACP;wBACA;oBACF;gBACA;oBAAS;wBACP,MAAM,IAAI,UAAU;oBACtB;YACF;YACA,MAAM,SAAS;YACf,YAAY;YACZ,IAAI,OAAO,WAAW,YAAY,OAAO,GAAG,KAAK,OAAO;gBACtD,MAAM,IAAI,eAAe,OAAO,GAAG;YACrC;YACA,OAAO,OAAO,GAAG;QACnB;QAEA,SAAS,oBAAoB,IAAI,EAAE,IAAI;YACrC,IAAI,OAAO,WAAW,MAAM,UAAU;YACtC,IAAI,OAAO;YACX,IAAI,OAAO;YACX,IAAI,OAAO,KAAK,MAAM;YACtB,IAAI,UAAU,SAAS,GAAG,GAAG,GAAG,OAAO;YACvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;gBACpC,MAAM,IAAI,IAAI,CAAC,EAAE;gBACjB,MAAM,OAAO,UAAU,IAAI;gBAAE,IAAI,OAAO,WAAW,GAAG,UAAU;gBAChE,IAAI,OAAO;gBACX,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;gBAC3C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;YAC7C;YACA,MAAM,MAAM,QAAQ,CAAC,uBAAuB,CAAC,MAAM,MAAM,SAAS;YAClE,IAAI;YACJ,OAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;gBAC1C,KAAK;oBAAG;wBACN,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAC/C,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAC/C,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;wBACtE,WAAU;4BACR,KAAK;4BACL,KAAK;wBACP;wBACA;oBACF;gBACA,KAAK;oBAAG;wBACN,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAC/C,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAC/C,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;wBACtE,WAAU;4BACR,KAAK;4BACL,KAAK;wBACP;wBACA;oBACF;gBACA;oBAAS;wBACP,MAAM,IAAI,UAAU;oBACtB;YACF;YACA,MAAM,SAAS;YACf,YAAY;YACZ,IAAI,OAAO,WAAW,YAAY,OAAO,GAAG,KAAK,OAAO;gBACtD,MAAM,IAAI,eAAe,OAAO,GAAG;YACrC;YACA,OAAO,OAAO,GAAG;QACnB;QAEA,SAAS,wBAAwB,IAAI,EAAE,IAAI;YACzC,IAAI,OAAO,WAAW,MAAM,UAAU;YACtC,IAAI,OAAO;YACX,IAAI,OAAO;YACX,IAAI,OAAO,KAAK,MAAM;YACtB,IAAI,UAAU,SAAS,GAAG,GAAG,GAAG,OAAO;YACvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;gBACpC,MAAM,IAAI,IAAI,CAAC,EAAE;gBACjB,MAAM,OAAO,UAAU,IAAI;gBAAE,IAAI,OAAO,WAAW,GAAG,UAAU;gBAChE,IAAI,OAAO;gBACX,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;gBAC3C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;YAC7C;YACA,MAAM,MAAM,QAAQ,CAAC,2BAA2B,CAAC,MAAM,MAAM,SAAS;YACtE,IAAI;YACJ,OAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;gBAC1C,KAAK;oBAAG;wBACN,WAAU;4BACR,KAAK;4BACL,KAAK;wBACP;wBACA;oBACF;gBACA,KAAK;oBAAG;wBACN,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAC/C,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAC/C,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;wBACtE,WAAU;4BACR,KAAK;4BACL,KAAK;wBACP;wBACA;oBACF;gBACA;oBAAS;wBACP,MAAM,IAAI,UAAU;oBACtB;YACF;YACA,MAAM,SAAS;YACf,YAAY;YACZ,IAAI,OAAO,WAAW,YAAY,OAAO,GAAG,KAAK,OAAO;gBACtD,MAAM,IAAI,eAAe,OAAO,GAAG;YACrC;YACA,OAAO,OAAO,GAAG;QACnB;QAEA,SAAS,aAAa,IAAI,EAAE,IAAI;YAC9B,IAAI,OAAO,WAAW,MAAM,UAAU;YACtC,IAAI,OAAO;YACX,IAAI,WAAW;YACf,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,OAAQ,SAAS,GAAG;gBAClB,KAAK;oBAAiC;wBACpC,MAAM,IAAI,SAAS,GAAG;wBACtB,IAAI,EAAC,uBAAuB,IAAI,EAAE,oBAAoB,IAAI,EAAE,OAAO,IAAI,EAAE,GAAG;wBAC5E,IAAI,OAAO;wBACX,IAAI,OAAO,KAAK,MAAM;wBACtB,IAAI,UAAU,SAAS,GAAG,GAAG,GAAG,OAAO;wBACvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;4BACpC,MAAM,IAAI,IAAI,CAAC,EAAE;4BACjB,MAAM,OAAO,UAAU,IAAI;4BAAE,IAAI,OAAO,WAAW,GAAG,UAAU;4BAChE,IAAI,OAAO;4BACX,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;4BAC3C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;wBAC7C;wBACA,aAAa;wBACb,aAAa,OAAO,IAAI;wBACxB,aAAa,OAAO,IAAI;wBACxB,aAAa;wBACb,aAAa;wBACb;oBACF;gBACA,KAAK;oBAAgC;wBACnC,MAAM,IAAI,SAAS,GAAG;wBACtB,IAAI,EAAC,uBAAuB,IAAI,EAAE,oBAAoB,IAAI,EAAE,MAAM,IAAI,EAAE,GAAG;wBAC3E,IAAI,OAAO;wBACX,IAAI,OAAO,KAAK,MAAM;wBACtB,IAAI,UAAU,SAAS,GAAG,GAAG,GAAG,OAAO;wBACvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;4BACpC,MAAM,IAAI,IAAI,CAAC,EAAE;4BACjB,MAAM,OAAO,UAAU,IAAI;4BAAE,IAAI,OAAO,WAAW,GAAG,UAAU;4BAChE,IAAI,OAAO;4BACX,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;4BAC3C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;wBAC7C;wBACA,aAAa;wBACb,aAAa,OAAO,IAAI;wBACxB,aAAa,OAAO,IAAI;wBACxB,aAAa;wBACb,aAAa;wBACb;oBACF;gBACA;oBAAS;wBACP,MAAM,IAAI,UAAU,CAAC,4BAA4B,EAAE,KAAK,SAAS,CAAC,SAAS,GAAG,EAAE,eAAe,EAAE,SAAS,2CAA2C,CAAC;oBACxJ;YACF;YACA,MAAM,MAAM,QAAQ,CAAC,iBAAiB,CAAC,MAAM,MAAM,YAAY,YAAY,YAAY,YAAY;YACnG,IAAI;YACJ,OAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;gBAC1C,KAAK;oBAAG;wBACN,IAAI;wBACJ,OAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;4BAC1C,KAAK;gCAAG;oCACN,QAAQ;oCACR;gCACF;4BACA,KAAK;gCAAG;oCACN,QAAQ;oCACR;gCACF;4BACA;gCAAS;oCACP,MAAM,IAAI,UAAU;gCACtB;wBACF;wBACA,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,IAAI;wBACjD,IAAI,SAAS,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBACjD,IAAI,WAAW,EAAE;wBACjB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;4BAC9B,MAAM,OAAO,SAAS,IAAI;4BAC1B,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG;4BAChD,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG;4BAChD,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;4BACtE,SAAS,IAAI,CAAC;wBAChB;wBACA,YAAW;4BACT,KAAK;4BACL,KAAK;gCACH,UAAU;gCACV,SAAS;4BACX;wBACF;wBACA;oBACF;gBACA,KAAK;oBAAG;wBACN,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAChD,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAChD,IAAI,WAAW,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,OAAO;wBACxE,YAAW;4BACT,KAAK;4BACL,KAAK;wBACP;wBACA;oBACF;gBACA;oBAAS;wBACP,MAAM,IAAI,UAAU;oBACtB;YACF;YACA,MAAM,SAAS;YACf,YAAY;YACZ,IAAI,OAAO,WAAW,YAAY,OAAO,GAAG,KAAK,OAAO;gBACtD,MAAM,IAAI,eAAe,OAAO,GAAG;YACrC;YACA,OAAO,OAAO,GAAG;QACnB;QAEA,SAAS,oBAAoB,IAAI,EAAE,IAAI;YACrC,IAAI,OAAO,WAAW,MAAM,UAAU;YACtC,IAAI,OAAO;YACX,IAAI,EAAC,UAAU,IAAI,EAAE,mBAAmB,IAAI,EAAE,kBAAkB,IAAI,EAAE,GAAG;YACzE,IAAI,WAAW;YACf,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,OAAQ,SAAS,GAAG;gBAClB,KAAK;oBAAS;wBACZ,MAAM,IAAI,SAAS,GAAG;wBACtB,IAAI,OAAO;wBACX,IAAI,OAAO,KAAK,MAAM;wBACtB,IAAI,UAAU,SAAS,GAAG,GAAG,GAAG,OAAO;wBACvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;4BACpC,MAAM,IAAI,IAAI,CAAC,EAAE;4BACjB,MAAM,OAAO,UAAU,IAAI;4BAAG,IAAI,WAAW;4BAC7C,OAAQ,SAAS,GAAG;gCAClB,KAAK;oCAAS;wCACZ,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;wCACvC;oCACF;gCACA,KAAK;oCAAgB;wCACnB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;wCACvC;oCACF;gCACA,KAAK;oCAAc;wCACjB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;wCACvC;oCACF;gCACA,KAAK;oCAAsB;wCACzB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;wCACvC;oCACF;gCACA,KAAK;oCAAU;wCACb,MAAM,IAAI,SAAS,GAAG;wCACtB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;wCACvC,IAAI,OAAO,WAAW,GAAG,UAAU;wCACnC,IAAI,OAAO;wCACX,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;wCAC3C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;wCAC3C;oCACF;gCACA;oCAAS;wCACP,MAAM,IAAI,UAAU,CAAC,4BAA4B,EAAE,KAAK,SAAS,CAAC,SAAS,GAAG,EAAE,eAAe,EAAE,SAAS,yCAAyC,CAAC;oCACtJ;4BACF;wBACF;wBACA,aAAa;wBACb,aAAa;wBACb,aAAa;wBACb;oBACF;gBACA,KAAK;oBAAQ;wBACX,MAAM,IAAI,SAAS,GAAG;wBACtB,IAAI,OAAO;wBACX,IAAI,OAAO,KAAK,MAAM;wBACtB,IAAI,UAAU,SAAS,GAAG,GAAG,GAAG,OAAO;wBACvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;4BACpC,MAAM,IAAI,IAAI,CAAC,EAAE;4BACjB,MAAM,OAAO,UAAU,IAAI;4BAAG,IAAI,WAAW;4BAC7C,OAAQ,SAAS,GAAG;gCAClB,KAAK;oCAAS;wCACZ,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;wCACvC;oCACF;gCACA,KAAK;oCAAgB;wCACnB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;wCACvC;oCACF;gCACA,KAAK;oCAAc;wCACjB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;wCACvC;oCACF;gCACA,KAAK;oCAAsB;wCACzB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;wCACvC;oCACF;gCACA,KAAK;oCAAU;wCACb,MAAM,IAAI,SAAS,GAAG;wCACtB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;wCACvC,IAAI,OAAO,WAAW,GAAG,UAAU;wCACnC,IAAI,OAAO;wCACX,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;wCAC3C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;wCAC3C;oCACF;gCACA;oCAAS;wCACP,MAAM,IAAI,UAAU,CAAC,4BAA4B,EAAE,KAAK,SAAS,CAAC,SAAS,GAAG,EAAE,eAAe,EAAE,SAAS,yCAAyC,CAAC;oCACtJ;4BACF;wBACF;wBACA,aAAa;wBACb,aAAa;wBACb,aAAa;wBACb;oBACF;gBACA;oBAAS;wBACP,MAAM,IAAI,UAAU,CAAC,4BAA4B,EAAE,KAAK,SAAS,CAAC,SAAS,GAAG,EAAE,eAAe,EAAE,SAAS,2CAA2C,CAAC;oBACxJ;YACF;YACA,IAAI,WAAW;YACf,IAAI;YACJ,IAAI;YACJ,IAAI,aAAa,QAAQ,aAAY,WAAW;gBAC9C,aAAa;gBACb,aAAa;YACf,OAAO;gBACL,MAAM,IAAI;gBACV,aAAa;gBACb,aAAa,SAAS;YACxB;YACA,MAAM,MAAM,QAAQ,CAAC,wBAAwB,CAAC,MAAM,MAAM,YAAY,YAAY,YAAY,YAAY,YAAY,OAAO,IAAI;YACjI,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;YAChD,IAAI,SAAS,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;YACjD,IAAI,WAAW,EAAE;YACjB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC9B,MAAM,OAAO,SAAS,IAAI;gBAC1B,IAAI;gBACJ,OAAQ,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG;oBAC3C,KAAK;wBAAG;4BACN,YAAW;gCACT,KAAK;4BACP;4BACA;wBACF;oBACA,KAAK;wBAAG;4BACN,YAAW;gCACT,KAAK;4BACP;4BACA;wBACF;oBACA,KAAK;wBAAG;4BACN,YAAW;gCACT,KAAK;4BACP;4BACA;wBACF;oBACA,KAAK;wBAAG;4BACN,YAAW;gCACT,KAAK;4BACP;4BACA;wBACF;oBACA,KAAK;wBAAG;4BACN,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,OAAO,IAAI;4BAClD,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,OAAO,IAAI;4BAClD,IAAI,WAAW,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,OAAO;4BACxE,YAAW;gCACT,KAAK;gCACL,KAAK;4BACP;4BACA;wBACF;oBACA;wBAAS;4BACP,MAAM,IAAI,UAAU;wBACtB;gBACF;gBACA,SAAS,IAAI,CAAC;oBACZ,OAAO,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,UAAU;oBACtD,KAAK,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,UAAU;oBACpD,gBAAgB;gBAClB;YACF;YACA,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,IAAI;YACjD,IAAI,SAAS,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;YACjD,IAAI,WAAW,EAAE;YACjB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC9B,MAAM,OAAO,SAAS,IAAI;gBAC1B,IAAI;gBACJ,OAAQ,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG;oBAC3C,KAAK;wBAAG;4BACN,YAAW;gCACT,KAAK;4BACP;4BACA;wBACF;oBACA,KAAK;wBAAG;4BACN,YAAW;gCACT,KAAK;4BACP;4BACA;wBACF;oBACA,KAAK;wBAAG;4BACN,YAAW;gCACT,KAAK;4BACP;4BACA;wBACF;oBACA,KAAK;wBAAG;4BACN,YAAW;gCACT,KAAK;4BACP;4BACA;wBACF;oBACA,KAAK;wBAAG;4BACN,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,OAAO,IAAI;4BAClD,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,OAAO,IAAI;4BAClD,IAAI,WAAW,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,OAAO;4BACxE,YAAW;gCACT,KAAK;gCACL,KAAK;4BACP;4BACA;wBACF;oBACA;wBAAS;4BACP,MAAM,IAAI,UAAU;wBACtB;gBACF;gBACA,SAAS,IAAI,CAAC;oBACZ,OAAO,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,UAAU;oBACtD,KAAK,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,UAAU;oBACpD,gBAAgB;gBAClB;YACF;YACA,MAAM,SAAS;gBACb,SAAS;gBACT,QAAQ;YACV;YACA,YAAY;YACZ,OAAO;QACT;QAEA,OAAO;YAAE;YAAW;YAAqB;YAAqB;YAAc;QAA0B;IACxG;IACA,IAAI,SAAS,SAAS;IACtB,SAAS,QAAS,KAAK;QACrB,IAAI;YACF,IAAI;YACJ,GAAG;gBACD,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,IAAI,IAAI,CAAC,MAAM;YACpC,QAAS,CAAC,CAAC,iBAAiB,OAAO,KAAK,CAAC,KAAM;YAC/C,IAAI,MAAM;gBACR,IAAI,SAAS,QAAQ;qBAChB,OAAO;YACd;YACA,IAAI,CAAC,SAAS,UAAU,IAAI,QAAQ,CAAC,UAAU,UAAY,CAAC,UAAU,UAAU,SAAS,OAAO;YAChG,MAAM,IAAI,CAAC,CAAA,UAAW,OAAO,YAAY,QAAQ,UAAU;QAC7D,EACA,OAAO,GAAG;YACR,IAAI,QAAQ,OAAO;iBACd,MAAM;QACb;IACF;IACA,MAAM,kBAAkB,QAAQ;IAChC,OAAO,WAAW;AACpB", "ignoreList": [0]}}, {"offset": {"line": 1793, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@arcjet/analyze-wasm/wasm/arcjet_analyze_js_req.component.core.wasm_.loader.mjs"], "sourcesContent": ["import wasmPath from \"WASM_PATH\";\n\nconst mod = await __turbopack_wasm_module__(wasmPath);\n\nexport default mod;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,MAAM,MAAM,0BAA0B,oNAAA,CAAA,UAAQ;uCAErC", "ignoreList": [0]}}, {"offset": {"line": 1812, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@arcjet/analyze-wasm/wasm/arcjet_analyze_js_req.component.core2.wasm_.loader.mjs"], "sourcesContent": ["import wasmPath from \"WASM_PATH\";\n\nconst mod = await __turbopack_wasm_module__(wasmPath);\n\nexport default mod;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,MAAM,MAAM,0BAA0B,qNAAA,CAAA,UAAQ;uCAErC", "ignoreList": [0]}}, {"offset": {"line": 1831, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@arcjet/analyze-wasm/wasm/arcjet_analyze_js_req.component.core3.wasm_.loader.mjs"], "sourcesContent": ["import wasmPath from \"WASM_PATH\";\n\nconst mod = await __turbopack_wasm_module__(wasmPath);\n\nexport default mod;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,MAAM,MAAM,0BAA0B,qNAAA,CAAA,UAAQ;uCAErC", "ignoreList": [0]}}, {"offset": {"line": 1845, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@arcjet/analyze-wasm/edge-light.js"], "sourcesContent": ["import { instantiate } from './wasm/arcjet_analyze_js_req.component.js';\nimport componentCoreWasm from './wasm/arcjet_analyze_js_req.component.core.wasm?module';\nimport componentCore2Wasm from './wasm/arcjet_analyze_js_req.component.core2.wasm?module';\nimport componentCore3Wasm from './wasm/arcjet_analyze_js_req.component.core3.wasm?module';\n\nasync function moduleFromPath(path) {\n    if (path === \"arcjet_analyze_js_req.component.core.wasm\") {\n        return componentCoreWasm;\n    }\n    if (path === \"arcjet_analyze_js_req.component.core2.wasm\") {\n        return componentCore2Wasm;\n    }\n    if (path === \"arcjet_analyze_js_req.component.core3.wasm\") {\n        return componentCore3Wasm;\n    }\n    throw new Error(`Unknown path: ${path}`);\n}\nasync function initializeWasm(coreImports) {\n    try {\n        return instantiate(moduleFromPath, coreImports);\n    }\n    catch {\n        return undefined;\n    }\n}\n\nexport { initializeWasm };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;;;;;;;AAEA,eAAe,eAAe,IAAI;IAC9B,IAAI,SAAS,6CAA6C;QACtD,OAAO,uNAAA,CAAA,UAAiB;IAC5B;IACA,IAAI,SAAS,8CAA8C;QACvD,OAAO,wNAAA,CAAA,UAAkB;IAC7B;IACA,IAAI,SAAS,8CAA8C;QACvD,OAAO,wNAAA,CAAA,UAAkB;IAC7B;IACA,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,MAAM;AAC3C;AACA,eAAe,eAAe,WAAW;IACrC,IAAI;QACA,OAAO,CAAA,GAAA,iMAAA,CAAA,cAAW,AAAD,EAAE,gBAAgB;IACvC,EACA,OAAM;QACF,OAAO;IACX;AACJ", "ignoreList": [0]}}, {"offset": {"line": 1889, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@arcjet/analyze/index.js"], "sourcesContent": ["import { initializeWasm } from '@arcjet/analyze-wasm';\n\nconst FREE_EMAIL_PROVIDERS = [\n    \"gmail.com\",\n    \"yahoo.com\",\n    \"hotmail.com\",\n    \"aol.com\",\n    \"hotmail.co.uk\",\n];\nfunction noOpSensitiveInfoDetect() {\n    return [];\n}\nfunction noOpBotsDetect() {\n    return [];\n}\nfunction createCoreImports(detect) {\n    if (typeof detect !== \"function\") {\n        detect = noOpSensitiveInfoDetect;\n    }\n    return {\n        \"arcjet:js-req/bot-identifier\": {\n            detect: noOpBotsDetect,\n        },\n        \"arcjet:js-req/email-validator-overrides\": {\n            isFreeEmail(domain) {\n                if (FREE_EMAIL_PROVIDERS.includes(domain)) {\n                    return \"yes\";\n                }\n                return \"unknown\";\n            },\n            isDisposableEmail() {\n                return \"unknown\";\n            },\n            hasMxRecords() {\n                return \"unknown\";\n            },\n            hasGravatar() {\n                return \"unknown\";\n            },\n        },\n        \"arcjet:js-req/sensitive-information-identifier\": {\n            detect,\n        },\n        \"arcjet:js-req/verify-bot\": {\n            verify() {\n                return \"unverifiable\";\n            },\n        },\n    };\n}\n/**\n * Generate a fingerprint for the client. This is used to identify the client\n * across multiple requests.\n * @param context - The Arcjet Analyze context.\n * @param request - The request to fingerprint.\n * @returns A SHA-256 string fingerprint.\n */\nasync function generateFingerprint(context, request) {\n    const { log } = context;\n    const coreImports = createCoreImports();\n    const analyze = await initializeWasm(coreImports);\n    if (typeof analyze !== \"undefined\") {\n        return analyze.generateFingerprint(JSON.stringify(request), context.characteristics);\n    }\n    else {\n        log.debug(\"WebAssembly is not supported in this runtime\");\n    }\n    return \"\";\n}\nasync function isValidEmail(context, candidate, options) {\n    const { log } = context;\n    const coreImports = createCoreImports();\n    const analyze = await initializeWasm(coreImports);\n    if (typeof analyze !== \"undefined\") {\n        return analyze.isValidEmail(candidate, options);\n    }\n    else {\n        log.debug(\"WebAssembly is not supported in this runtime\");\n        // Skip the local evaluation of the rule if WASM is not available\n        return {\n            validity: \"valid\",\n            blocked: [],\n        };\n    }\n}\nasync function detectBot(context, request, options) {\n    const { log } = context;\n    const coreImports = createCoreImports();\n    const analyze = await initializeWasm(coreImports);\n    if (typeof analyze !== \"undefined\") {\n        return analyze.detectBot(JSON.stringify(request), options);\n    }\n    else {\n        log.debug(\"WebAssembly is not supported in this runtime\");\n        // Skip the local evaluation of the rule if Wasm is not available\n        return {\n            allowed: [],\n            denied: [],\n            spoofed: false,\n            verified: false,\n        };\n    }\n}\nasync function detectSensitiveInfo(context, candidate, entities, contextWindowSize, detect) {\n    const { log } = context;\n    const coreImports = createCoreImports(detect);\n    const analyze = await initializeWasm(coreImports);\n    if (typeof analyze !== \"undefined\") {\n        const skipCustomDetect = typeof detect !== \"function\";\n        return analyze.detectSensitiveInfo(candidate, {\n            entities,\n            contextWindowSize,\n            skipCustomDetect,\n        });\n    }\n    else {\n        log.debug(\"WebAssembly is not supported in this runtime\");\n        throw new Error(\"SENSITIVE_INFO rule failed to run because Wasm is not supported in this environment.\");\n    }\n}\n\nexport { detectBot, detectSensitiveInfo, generateFingerprint, isValidEmail };\n"], "names": [], "mappings": ";;;;;;AAAA;;;;;;AAEA,MAAM,uBAAuB;IACzB;IACA;IACA;IACA;IACA;CACH;AACD,SAAS;IACL,OAAO,EAAE;AACb;AACA,SAAS;IACL,OAAO,EAAE;AACb;AACA,SAAS,kBAAkB,MAAM;IAC7B,IAAI,OAAO,WAAW,YAAY;QAC9B,SAAS;IACb;IACA,OAAO;QACH,gCAAgC;YAC5B,QAAQ;QACZ;QACA,2CAA2C;YACvC,aAAY,MAAM;gBACd,IAAI,qBAAqB,QAAQ,CAAC,SAAS;oBACvC,OAAO;gBACX;gBACA,OAAO;YACX;YACA;gBACI,OAAO;YACX;YACA;gBACI,OAAO;YACX;YACA;gBACI,OAAO;YACX;QACJ;QACA,kDAAkD;YAC9C;QACJ;QACA,4BAA4B;YACxB;gBACI,OAAO;YACX;QACJ;IACJ;AACJ;AACA;;;;;;CAMC,GACD,eAAe,oBAAoB,OAAO,EAAE,OAAO;IAC/C,MAAM,EAAE,GAAG,EAAE,GAAG;IAChB,MAAM,cAAc;IACpB,MAAM,UAAU,MAAM,CAAA,GAAA,oKAAA,CAAA,iBAAc,AAAD,EAAE;IACrC,IAAI,OAAO,YAAY,aAAa;QAChC,OAAO,QAAQ,mBAAmB,CAAC,KAAK,SAAS,CAAC,UAAU,QAAQ,eAAe;IACvF,OACK;QACD,IAAI,KAAK,CAAC;IACd;IACA,OAAO;AACX;AACA,eAAe,aAAa,OAAO,EAAE,SAAS,EAAE,OAAO;IACnD,MAAM,EAAE,GAAG,EAAE,GAAG;IAChB,MAAM,cAAc;IACpB,MAAM,UAAU,MAAM,CAAA,GAAA,oKAAA,CAAA,iBAAc,AAAD,EAAE;IACrC,IAAI,OAAO,YAAY,aAAa;QAChC,OAAO,QAAQ,YAAY,CAAC,WAAW;IAC3C,OACK;QACD,IAAI,KAAK,CAAC;QACV,iEAAiE;QACjE,OAAO;YACH,UAAU;YACV,SAAS,EAAE;QACf;IACJ;AACJ;AACA,eAAe,UAAU,OAAO,EAAE,OAAO,EAAE,OAAO;IAC9C,MAAM,EAAE,GAAG,EAAE,GAAG;IAChB,MAAM,cAAc;IACpB,MAAM,UAAU,MAAM,CAAA,GAAA,oKAAA,CAAA,iBAAc,AAAD,EAAE;IACrC,IAAI,OAAO,YAAY,aAAa;QAChC,OAAO,QAAQ,SAAS,CAAC,KAAK,SAAS,CAAC,UAAU;IACtD,OACK;QACD,IAAI,KAAK,CAAC;QACV,iEAAiE;QACjE,OAAO;YACH,SAAS,EAAE;YACX,QAAQ,EAAE;YACV,SAAS;YACT,UAAU;QACd;IACJ;AACJ;AACA,eAAe,oBAAoB,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,iBAAiB,EAAE,MAAM;IACtF,MAAM,EAAE,GAAG,EAAE,GAAG;IAChB,MAAM,cAAc,kBAAkB;IACtC,MAAM,UAAU,MAAM,CAAA,GAAA,oKAAA,CAAA,iBAAc,AAAD,EAAE;IACrC,IAAI,OAAO,YAAY,aAAa;QAChC,MAAM,mBAAmB,OAAO,WAAW;QAC3C,OAAO,QAAQ,mBAAmB,CAAC,WAAW;YAC1C;YACA;YACA;QACJ;IACJ,OACK;QACD,IAAI,KAAK,CAAC;QACV,MAAM,IAAI,MAAM;IACpB;AACJ", "ignoreList": [0]}}, {"offset": {"line": 2022, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@arcjet/duration/index.js"], "sourcesContent": ["// This Parser is a TypeScript implementation of similar code in the Go stdlib\n// with deviations made to support usage in the Arcjet SDK.\n//\n// Parser source:\n// https://github.com/golang/go/blob/c18ddc84e1ec6406b26f7e9d0e1ee3d1908d7c27/src/time/format.go#L1589-L1686\n//\n// Licensed: BSD 3-Clause \"New\" or \"Revised\" License\n// Copyright (c) 2009 The Go Authors. All rights reserved.\n//\n// Redistribution and use in source and binary forms, with or without\n// modification, are permitted provided that the following conditions are\n// met:\n//\n//    * Redistributions of source code must retain the above copyright\n// notice, this list of conditions and the following disclaimer.\n//    * Redistributions in binary form must reproduce the above\n// copyright notice, this list of conditions and the following disclaimer\n// in the documentation and/or other materials provided with the\n// distribution.\n//    * Neither the name of Google Inc. nor the names of its\n// contributors may be used to endorse or promote products derived from\n// this software without specific prior written permission.\n//\n// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n// \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\nconst second = 1;\nconst minute = 60 * second;\nconst hour = 60 * minute;\nconst day = 24 * hour;\nconst maxUint32 = 4294967295;\nconst units = new Map([\n    [\"s\", second],\n    [\"m\", minute],\n    [\"h\", hour],\n    [\"d\", day],\n]);\nconst integers = [\"0\", \"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\"];\n// leadingInt consumes the leading [0-9]* from s.\nfunction leadingInt(s) {\n    let i = 0;\n    let x = 0;\n    for (; i < s.length; i++) {\n        const c = s[i];\n        if (!integers.includes(c)) {\n            break;\n        }\n        x = x * 10 + parseInt(c, 10);\n        if (x > maxUint32) {\n            // overflow\n            throw new Error(\"bad [0-9]*\"); // never printed\n        }\n    }\n    return [x, s.slice(i)];\n}\n/**\n * Parses a duration into a number representing seconds while ensuring the value\n * fits within an unsigned 32-bit integer.\n *\n * If a JavaScript number is provided to the function, it is validated and\n * returned verbatim.\n *\n * If a string is provided to the function, it must be in the form of digits\n * followed by a unit. Supported units are `s` (seconds), `m` (minutes), `h`\n * (hours), and `d` (days).\n *\n * @param s The value to parse into seconds.\n * @returns A number representing seconds parsed from the provided duration.\n *\n * @example\n * parse(\"1s\") === 1\n * parse(\"1m\") === 60\n * parse(\"1h\") === 3600\n * parse(\"1d\") === 86400\n */\nfunction parse(s) {\n    const original = s;\n    if (typeof s === \"number\") {\n        if (s > maxUint32) {\n            throw new Error(`invalid duration: ${original}`);\n        }\n        if (s < 0) {\n            throw new Error(`invalid duration: ${original}`);\n        }\n        if (!Number.isInteger(s)) {\n            throw new Error(`invalid duration: ${original}`);\n        }\n        return s;\n    }\n    if (typeof s !== \"string\") {\n        throw new Error(\"can only parse a duration string\");\n    }\n    let d = 0;\n    // Special case: if all that is left is \"0\", this is zero.\n    if (s === \"0\") {\n        return 0;\n    }\n    if (s === \"\") {\n        throw new Error(`invalid duration: ${original}`);\n    }\n    while (s !== \"\") {\n        let v = 0;\n        // The next character must be [0-9]\n        if (!integers.includes(s[0])) {\n            throw new Error(`invalid duration: ${original}`);\n        }\n        // Consume [0-9]*\n        [v, s] = leadingInt(s);\n        // Error on decimal (\\.[0-9]*)?\n        if (s !== \"\" && s[0] == \".\") {\n            // TODO: We could support decimals that turn into non-decimal seconds—e.g.\n            // 1.5hours becomes 5400 seconds\n            throw new Error(`unsupported decimal duration: ${original}`);\n        }\n        // Consume unit.\n        let i = 0;\n        for (; i < s.length; i++) {\n            const c = s[i];\n            if (integers.includes(c)) {\n                break;\n            }\n        }\n        if (i == 0) {\n            throw new Error(`missing unit in duration: ${original}`);\n        }\n        const u = s.slice(0, i);\n        s = s.slice(i);\n        const unit = units.get(u);\n        if (typeof unit === \"undefined\") {\n            throw new Error(`unknown unit \"${u}\" in duration ${original}`);\n        }\n        if (v > maxUint32 / unit) {\n            // overflow\n            throw new Error(`invalid duration ${original}`);\n        }\n        v *= unit;\n        d += v;\n        if (d > maxUint32) {\n            throw new Error(`invalid duration ${original}`);\n        }\n    }\n    return d;\n}\n\nexport { parse };\n"], "names": [], "mappings": "AAAA,8EAA8E;AAC9E,2DAA2D;AAC3D,EAAE;AACF,iBAAiB;AACjB,4GAA4G;AAC5G,EAAE;AACF,oDAAoD;AACpD,0DAA0D;AAC1D,EAAE;AACF,qEAAqE;AACrE,yEAAyE;AACzE,OAAO;AACP,EAAE;AACF,sEAAsE;AACtE,gEAAgE;AAChE,+DAA+D;AAC/D,yEAAyE;AACzE,gEAAgE;AAChE,gBAAgB;AAChB,4DAA4D;AAC5D,uEAAuE;AACvE,2DAA2D;AAC3D,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,wEAAwE;AACxE,uEAAuE;AACvE,wEAAwE;AACxE,mEAAmE;AACnE,wEAAwE;AACxE,wEAAwE;AACxE,sEAAsE;AACtE,wEAAwE;AACxE,uEAAuE;;;;AACvE,MAAM,SAAS;AACf,MAAM,SAAS,KAAK;AACpB,MAAM,OAAO,KAAK;AAClB,MAAM,MAAM,KAAK;AACjB,MAAM,YAAY;AAClB,MAAM,QAAQ,IAAI,IAAI;IAClB;QAAC;QAAK;KAAO;IACb;QAAC;QAAK;KAAO;IACb;QAAC;QAAK;KAAK;IACX;QAAC;QAAK;KAAI;CACb;AACD,MAAM,WAAW;IAAC;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;CAAI;AACnE,iDAAiD;AACjD,SAAS,WAAW,CAAC;IACjB,IAAI,IAAI;IACR,IAAI,IAAI;IACR,MAAO,IAAI,EAAE,MAAM,EAAE,IAAK;QACtB,MAAM,IAAI,CAAC,CAAC,EAAE;QACd,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI;YACvB;QACJ;QACA,IAAI,IAAI,KAAK,SAAS,GAAG;QACzB,IAAI,IAAI,WAAW;YACf,WAAW;YACX,MAAM,IAAI,MAAM,eAAe,gBAAgB;QACnD;IACJ;IACA,OAAO;QAAC;QAAG,EAAE,KAAK,CAAC;KAAG;AAC1B;AACA;;;;;;;;;;;;;;;;;;;CAmBC,GACD,SAAS,MAAM,CAAC;IACZ,MAAM,WAAW;IACjB,IAAI,OAAO,MAAM,UAAU;QACvB,IAAI,IAAI,WAAW;YACf,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,UAAU;QACnD;QACA,IAAI,IAAI,GAAG;YACP,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,UAAU;QACnD;QACA,IAAI,CAAC,OAAO,SAAS,CAAC,IAAI;YACtB,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,UAAU;QACnD;QACA,OAAO;IACX;IACA,IAAI,OAAO,MAAM,UAAU;QACvB,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,IAAI;IACR,0DAA0D;IAC1D,IAAI,MAAM,KAAK;QACX,OAAO;IACX;IACA,IAAI,MAAM,IAAI;QACV,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,UAAU;IACnD;IACA,MAAO,MAAM,GAAI;QACb,IAAI,IAAI;QACR,mCAAmC;QACnC,IAAI,CAAC,SAAS,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG;YAC1B,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,UAAU;QACnD;QACA,iBAAiB;QACjB,CAAC,GAAG,EAAE,GAAG,WAAW;QACpB,+BAA+B;QAC/B,IAAI,MAAM,MAAM,CAAC,CAAC,EAAE,IAAI,KAAK;YACzB,0EAA0E;YAC1E,gCAAgC;YAChC,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,UAAU;QAC/D;QACA,gBAAgB;QAChB,IAAI,IAAI;QACR,MAAO,IAAI,EAAE,MAAM,EAAE,IAAK;YACtB,MAAM,IAAI,CAAC,CAAC,EAAE;YACd,IAAI,SAAS,QAAQ,CAAC,IAAI;gBACtB;YACJ;QACJ;QACA,IAAI,KAAK,GAAG;YACR,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,UAAU;QAC3D;QACA,MAAM,IAAI,EAAE,KAAK,CAAC,GAAG;QACrB,IAAI,EAAE,KAAK,CAAC;QACZ,MAAM,OAAO,MAAM,GAAG,CAAC;QACvB,IAAI,OAAO,SAAS,aAAa;YAC7B,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,EAAE,cAAc,EAAE,UAAU;QACjE;QACA,IAAI,IAAI,YAAY,MAAM;YACtB,WAAW;YACX,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,UAAU;QAClD;QACA,KAAK;QACL,KAAK;QACL,IAAI,IAAI,WAAW;YACf,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,UAAU;QAClD;IACJ;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 2208, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@arcjet/headers/index.js"], "sourcesContent": ["function isIterable(val) {\n    return typeof val?.[Symbol.iterator] === \"function\";\n}\n/**\n * This Fetch API interface allows you to perform various actions on HTTP\n * request and response headers. These actions include retrieving, setting,\n * adding to, and removing. A Headers object has an associated header list,\n * which is initially empty and consists of zero or more name and value pairs.\n *\n * You can add to this using methods like `append()`.\n *\n * In all methods of this interface, header names are matched by\n * case-insensitive byte sequence.\n *\n * [MDN Reference](https://developer.mozilla.org/docs/Web/API/Headers)\n */\nclass ArcjetHeaders extends Headers {\n    constructor(init) {\n        super();\n        if (typeof init !== \"undefined\" &&\n            typeof init !== \"string\" &&\n            init !== null) {\n            if (isIterable(init)) {\n                for (const [key, value] of init) {\n                    this.append(key, value);\n                }\n            }\n            else {\n                for (const [key, value] of Object.entries(init)) {\n                    if (typeof value === \"undefined\") {\n                        continue;\n                    }\n                    if (Array.isArray(value)) {\n                        for (const singleValue of value) {\n                            this.append(key, singleValue);\n                        }\n                    }\n                    else {\n                        this.append(key, value);\n                    }\n                }\n            }\n        }\n    }\n    /**\n     * Append a key and value to the headers, while filtering any key named\n     * `cookie`.\n     *\n     * [MDN Reference](https://developer.mozilla.org/docs/Web/API/Headers/append)\n     *\n     * @param key The key to append in the headers\n     * @param value The value to append for the key in the headers\n     */\n    append(key, value) {\n        if (typeof key !== \"string\" || typeof value !== \"string\") {\n            return;\n        }\n        if (key.toLowerCase() !== \"cookie\") {\n            super.append(key, value);\n        }\n    }\n    /**\n     * Set a key and value in the headers, but filtering any key named `cookie`.\n     *\n     * [MDN Reference](https://developer.mozilla.org/docs/Web/API/Headers/set)\n     *\n     * @param key The key to set in the headers\n     * @param value The value to set for the key in the headers\n     */\n    set(key, value) {\n        if (typeof key !== \"string\" || typeof value !== \"string\") {\n            return;\n        }\n        if (key.toLowerCase() !== \"cookie\") {\n            super.set(key, value);\n        }\n    }\n}\n\nexport { ArcjetHeaders as default };\n"], "names": [], "mappings": ";;;AAAA,SAAS,WAAW,GAAG;IACnB,OAAO,OAAO,KAAK,CAAC,OAAO,QAAQ,CAAC,KAAK;AAC7C;AACA;;;;;;;;;;;;CAYC,GACD,MAAM,sBAAsB;IACxB,YAAY,IAAI,CAAE;QACd,KAAK;QACL,IAAI,OAAO,SAAS,eAChB,OAAO,SAAS,YAChB,SAAS,MAAM;YACf,IAAI,WAAW,OAAO;gBAClB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,KAAM;oBAC7B,IAAI,CAAC,MAAM,CAAC,KAAK;gBACrB;YACJ,OACK;gBACD,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,MAAO;oBAC7C,IAAI,OAAO,UAAU,aAAa;wBAC9B;oBACJ;oBACA,IAAI,MAAM,OAAO,CAAC,QAAQ;wBACtB,KAAK,MAAM,eAAe,MAAO;4BAC7B,IAAI,CAAC,MAAM,CAAC,KAAK;wBACrB;oBACJ,OACK;wBACD,IAAI,CAAC,MAAM,CAAC,KAAK;oBACrB;gBACJ;YACJ;QACJ;IACJ;IACA;;;;;;;;KAQC,GACD,OAAO,GAAG,EAAE,KAAK,EAAE;QACf,IAAI,OAAO,QAAQ,YAAY,OAAO,UAAU,UAAU;YACtD;QACJ;QACA,IAAI,IAAI,WAAW,OAAO,UAAU;YAChC,KAAK,CAAC,OAAO,KAAK;QACtB;IACJ;IACA;;;;;;;KAOC,GACD,IAAI,GAAG,EAAE,KAAK,EAAE;QACZ,IAAI,OAAO,QAAQ,YAAY,OAAO,UAAU,UAAU;YACtD;QACJ;QACA,IAAI,IAAI,WAAW,OAAO,UAAU;YAChC,KAAK,CAAC,IAAI,KAAK;QACnB;IACJ;AACJ", "ignoreList": [0]}}, {"offset": {"line": 2289, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@arcjet/runtime/edge-light.js"], "sourcesContent": ["/*\n  This file is mostly a duplication of `index.ts` with the `process` lookup\n  removed. We do this because Next.js uses an error-prone method for showing\n  a warning when compiling for the edge runtime.\n*/\n// This code was improved by detection mechanisms in\n// https://github.com/unjs/std-env/blob/b4ef16832baf4594ece7796a2c1805712fde70a3/src/runtimes.ts\n//\n// MIT License\n//\n// Copyright (c) Pooya <PERSON>rsa <<EMAIL>>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n// SOFTWARE.\nfunction runtime() {\n    // The detection order matters in this function because some platforms will\n    // implement compatibility layers, but we want to detect them accurately.\n    // https://developers.cloudflare.com/workers/configuration/compatibility-dates/#global-navigator\n    if (typeof navigator !== \"undefined\" &&\n        navigator.userAgent === \"Cloudflare-Workers\") {\n        return \"workerd\";\n    }\n    if (typeof Deno !== \"undefined\") {\n        return \"deno\";\n    }\n    if (typeof Bun !== \"undefined\") {\n        return \"bun\";\n    }\n    if (typeof EdgeRuntime !== \"undefined\") {\n        return \"edge-light\";\n    }\n    // Unknown or unsupported runtime\n    return \"\";\n}\n\nexport { runtime };\n"], "names": [], "mappings": "AAAA;;;;AAIA,GACA,oDAAoD;AACpD,gGAAgG;AAChG,EAAE;AACF,cAAc;AACd,EAAE;AACF,2CAA2C;AAC3C,EAAE;AACF,+EAA+E;AAC/E,gFAAgF;AAChF,+EAA+E;AAC/E,4EAA4E;AAC5E,wEAAwE;AACxE,2DAA2D;AAC3D,EAAE;AACF,6EAA6E;AAC7E,sDAAsD;AACtD,EAAE;AACF,6EAA6E;AAC7E,2EAA2E;AAC3E,8EAA8E;AAC9E,yEAAyE;AACzE,gFAAgF;AAChF,gFAAgF;AAChF,YAAY;;;;AACZ,SAAS;IACL,2EAA2E;IAC3E,yEAAyE;IACzE,gGAAgG;IAChG,IAAI,OAAO,cAAc,eACrB,UAAU,SAAS,KAAK,sBAAsB;QAC9C,OAAO;IACX;IACA,IAAI,OAAO,SAAS,aAAa;QAC7B,OAAO;IACX;IACA,IAAI,OAAO,QAAQ,aAAa;QAC5B,OAAO;IACX;IACA,IAAI,4DAAuB,aAAa;QACpC,OAAO;IACX;IACA,iCAAiC;IACjC,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 2346, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@arcjet/stable-hash/hasher.js"], "sourcesContent": ["class Sha256 {\n    encoder;\n    subtle;\n    buf;\n    constructor(subtle) {\n        this.subtle = subtle;\n        this.encoder = new TextEncoder();\n        this.buf = \"\";\n    }\n    writeString(data) {\n        this.buf += data;\n    }\n    async digest() {\n        const buf = this.encoder.encode(this.buf);\n        const digest = await this.subtle.digest(\"SHA-256\", buf);\n        return new Uint8Array(digest);\n    }\n}\n// After this, it needs to wrap to 0\nconst maxUint32 = 4294967295;\nconst fieldSeparator = \":\";\nconst itemSeparator = \",\";\nfunction bool(key, value) {\n    return (data) => {\n        data.writeString(key);\n        data.writeString(fieldSeparator);\n        if (value) {\n            data.writeString(\"true\");\n        }\n        else {\n            data.writeString(\"false\");\n        }\n    };\n}\nfunction uint32(key, value) {\n    return (data) => {\n        data.writeString(key);\n        data.writeString(fieldSeparator);\n        if (value > maxUint32) {\n            data.writeString(\"0\");\n        }\n        else {\n            data.writeString(value.toFixed(0));\n        }\n    };\n}\nfunction string(key, value) {\n    return (data) => {\n        data.writeString(key);\n        data.writeString(fieldSeparator);\n        data.writeString(`\"`);\n        data.writeString(value.replaceAll(`\"`, `\\\\\"`));\n        data.writeString(`\"`);\n    };\n}\nfunction stringSliceOrdered(key, values) {\n    return (data) => {\n        data.writeString(key);\n        data.writeString(fieldSeparator);\n        data.writeString(\"[\");\n        for (const value of Array.from(values).sort()) {\n            data.writeString(`\"`);\n            data.writeString(value.replaceAll(`\"`, `\\\\\"`));\n            data.writeString(`\"`);\n            data.writeString(itemSeparator);\n        }\n        data.writeString(\"]\");\n    };\n}\nfunction makeHasher(subtle) {\n    return async function hash(...hashers) {\n        const h = new Sha256(subtle);\n        for (const hasher of hashers) {\n            hasher(h);\n            h.writeString(itemSeparator);\n        }\n        const digest = await h.digest();\n        return hex(digest);\n    };\n}\n// Hex encoding logic from https://github.com/feross/buffer but adjusted for\n// our use.\n//\n// Licensed: The MIT License (MIT)\n//\n// Copyright (c) Feross Aboukhadijeh, and other contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n// https://github.com/feross/buffer/blob/5857e295f4d37e3ad02c3abcbf7e8e5ef51f3be6/index.js#L2096-L2106\nconst hexSliceLookupTable = (function () {\n    const alphabet = \"0123456789abcdef\";\n    const table = new Array(256);\n    for (let i = 0; i < 16; ++i) {\n        const i16 = i * 16;\n        for (let j = 0; j < 16; ++j) {\n            table[i16 + j] = alphabet[i] + alphabet[j];\n        }\n    }\n    return table;\n})();\n// https://github.com/feross/buffer/blob/5857e295f4d37e3ad02c3abcbf7e8e5ef51f3be6/index.js#L1085-L1096\nfunction hex(buf) {\n    const len = buf.length;\n    const start = 0;\n    const end = len;\n    let out = \"\";\n    for (let i = start; i < end; ++i) {\n        out += hexSliceLookupTable[buf[i]];\n    }\n    return out;\n}\n\nexport { bool, makeHasher, string, stringSliceOrdered, uint32 };\n"], "names": [], "mappings": ";;;;;;;AAAA,MAAM;IACF,QAAQ;IACR,OAAO;IACP,IAAI;IACJ,YAAY,MAAM,CAAE;QAChB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG,IAAI;QACnB,IAAI,CAAC,GAAG,GAAG;IACf;IACA,YAAY,IAAI,EAAE;QACd,IAAI,CAAC,GAAG,IAAI;IAChB;IACA,MAAM,SAAS;QACX,MAAM,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG;QACxC,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW;QACnD,OAAO,IAAI,WAAW;IAC1B;AACJ;AACA,oCAAoC;AACpC,MAAM,YAAY;AAClB,MAAM,iBAAiB;AACvB,MAAM,gBAAgB;AACtB,SAAS,KAAK,GAAG,EAAE,KAAK;IACpB,OAAO,CAAC;QACJ,KAAK,WAAW,CAAC;QACjB,KAAK,WAAW,CAAC;QACjB,IAAI,OAAO;YACP,KAAK,WAAW,CAAC;QACrB,OACK;YACD,KAAK,WAAW,CAAC;QACrB;IACJ;AACJ;AACA,SAAS,OAAO,GAAG,EAAE,KAAK;IACtB,OAAO,CAAC;QACJ,KAAK,WAAW,CAAC;QACjB,KAAK,WAAW,CAAC;QACjB,IAAI,QAAQ,WAAW;YACnB,KAAK,WAAW,CAAC;QACrB,OACK;YACD,KAAK,WAAW,CAAC,MAAM,OAAO,CAAC;QACnC;IACJ;AACJ;AACA,SAAS,OAAO,GAAG,EAAE,KAAK;IACtB,OAAO,CAAC;QACJ,KAAK,WAAW,CAAC;QACjB,KAAK,WAAW,CAAC;QACjB,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC;QACpB,KAAK,WAAW,CAAC,MAAM,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC;QAC5C,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC;IACxB;AACJ;AACA,SAAS,mBAAmB,GAAG,EAAE,MAAM;IACnC,OAAO,CAAC;QACJ,KAAK,WAAW,CAAC;QACjB,KAAK,WAAW,CAAC;QACjB,KAAK,WAAW,CAAC;QACjB,KAAK,MAAM,SAAS,MAAM,IAAI,CAAC,QAAQ,IAAI,GAAI;YAC3C,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC;YACpB,KAAK,WAAW,CAAC,MAAM,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC;YAC5C,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC;YACpB,KAAK,WAAW,CAAC;QACrB;QACA,KAAK,WAAW,CAAC;IACrB;AACJ;AACA,SAAS,WAAW,MAAM;IACtB,OAAO,eAAe,KAAK,GAAG,OAAO;QACjC,MAAM,IAAI,IAAI,OAAO;QACrB,KAAK,MAAM,UAAU,QAAS;YAC1B,OAAO;YACP,EAAE,WAAW,CAAC;QAClB;QACA,MAAM,SAAS,MAAM,EAAE,MAAM;QAC7B,OAAO,IAAI;IACf;AACJ;AACA,4EAA4E;AAC5E,WAAW;AACX,EAAE;AACF,kCAAkC;AAClC,EAAE;AACF,6DAA6D;AAC7D,EAAE;AACF,+EAA+E;AAC/E,gFAAgF;AAChF,+EAA+E;AAC/E,4EAA4E;AAC5E,wEAAwE;AACxE,2DAA2D;AAC3D,EAAE;AACF,6EAA6E;AAC7E,sDAAsD;AACtD,EAAE;AACF,6EAA6E;AAC7E,2EAA2E;AAC3E,8EAA8E;AAC9E,yEAAyE;AACzE,gFAAgF;AAChF,4EAA4E;AAC5E,gBAAgB;AAChB,sGAAsG;AACtG,MAAM,sBAAsB,AAAC;IACzB,MAAM,WAAW;IACjB,MAAM,QAAQ,IAAI,MAAM;IACxB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;QACzB,MAAM,MAAM,IAAI;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;YACzB,KAAK,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;QAC9C;IACJ;IACA,OAAO;AACX;AACA,sGAAsG;AACtG,SAAS,IAAI,GAAG;IACZ,MAAM,MAAM,IAAI,MAAM;IACtB,MAAM,QAAQ;IACd,MAAM,MAAM;IACZ,IAAI,MAAM;IACV,IAAK,IAAI,IAAI,OAAO,IAAI,KAAK,EAAE,EAAG;QAC9B,OAAO,mBAAmB,CAAC,GAAG,CAAC,EAAE,CAAC;IACtC;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 2485, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@arcjet/stable-hash/edge-light.js"], "sourcesContent": ["import { makeHasher } from './hasher.js';\nexport { bool, string, stringSliceOrdered, uint32 } from './hasher.js';\n\nconst hash = makeHasher(crypto.subtle);\n\nexport { hash, makeHasher };\n"], "names": [], "mappings": ";;;AAAA;;;AAGA,MAAM,OAAO,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,OAAO,MAAM", "ignoreList": [0]}}, {"offset": {"line": 2508, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@arcjet/cache/index.js"], "sourcesContent": ["function nowInSeconds() {\n    return Math.floor(Date.now() / 1000);\n}\nclass Bucket {\n    expires;\n    data;\n    constructor() {\n        this.expires = new Map();\n        this.data = new Map();\n    }\n    get(key) {\n        const now = nowInSeconds();\n        const expiresAt = this.expires.get(key) ?? now;\n        const ttl = expiresAt - now;\n        if (ttl > 0) {\n            return [this.data.get(key), ttl];\n        }\n        else {\n            // Cleanup if expired\n            this.expires.delete(key);\n            this.data.delete(key);\n            return [undefined, 0];\n        }\n    }\n    set(key, value, ttl) {\n        const expiresAt = nowInSeconds() + ttl;\n        this.expires.set(key, expiresAt);\n        this.data.set(key, value);\n    }\n}\nclass MemoryCache {\n    namespaces;\n    constructor() {\n        this.namespaces = new Map();\n    }\n    async get(namespace, key) {\n        if (typeof namespace !== \"string\") {\n            throw new Error(\"`namespace` must be a string\");\n        }\n        if (typeof key !== \"string\") {\n            throw new Error(\"`key` must be a string\");\n        }\n        const namespaceCache = this.namespaces.get(namespace);\n        if (typeof namespaceCache === \"undefined\") {\n            return [undefined, 0];\n        }\n        return namespaceCache.get(key);\n    }\n    set(namespace, key, value, ttl) {\n        if (typeof namespace !== \"string\") {\n            throw new Error(\"`namespace` must be a string\");\n        }\n        if (typeof key !== \"string\") {\n            throw new Error(\"`key` must be a string\");\n        }\n        const namespaceCache = this.namespaces.get(namespace) ?? new Bucket();\n        namespaceCache.set(key, value, ttl);\n        this.namespaces.set(namespace, namespaceCache);\n    }\n}\n\nexport { MemoryCache };\n"], "names": [], "mappings": ";;;AAAA,SAAS;IACL,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;AACnC;AACA,MAAM;IACF,QAAQ;IACR,KAAK;IACL,aAAc;QACV,IAAI,CAAC,OAAO,GAAG,IAAI;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI;IACpB;IACA,IAAI,GAAG,EAAE;QACL,MAAM,MAAM;QACZ,MAAM,YAAY,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ;QAC3C,MAAM,MAAM,YAAY;QACxB,IAAI,MAAM,GAAG;YACT,OAAO;gBAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;gBAAM;aAAI;QACpC,OACK;YACD,qBAAqB;YACrB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YACpB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;YACjB,OAAO;gBAAC;gBAAW;aAAE;QACzB;IACJ;IACA,IAAI,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE;QACjB,MAAM,YAAY,iBAAiB;QACnC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK;QACtB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK;IACvB;AACJ;AACA,MAAM;IACF,WAAW;IACX,aAAc;QACV,IAAI,CAAC,UAAU,GAAG,IAAI;IAC1B;IACA,MAAM,IAAI,SAAS,EAAE,GAAG,EAAE;QACtB,IAAI,OAAO,cAAc,UAAU;YAC/B,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,OAAO,QAAQ,UAAU;YACzB,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,iBAAiB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;QAC3C,IAAI,OAAO,mBAAmB,aAAa;YACvC,OAAO;gBAAC;gBAAW;aAAE;QACzB;QACA,OAAO,eAAe,GAAG,CAAC;IAC9B;IACA,IAAI,SAAS,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE;QAC5B,IAAI,OAAO,cAAc,UAAU;YAC/B,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,OAAO,QAAQ,UAAU;YACzB,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,iBAAiB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,cAAc,IAAI;QAC7D,eAAe,GAAG,CAAC,KAAK,OAAO;QAC/B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW;IACnC;AACJ", "ignoreList": [0]}}, {"offset": {"line": 2586, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/arcjet/index.js"], "sourcesContent": ["import { ArcjetErrorDecision, ArcjetErrorReason, ArcjetRuleResult, ArcjetReason, ArcjetDenyDecision, ArcjetBotReason, ArcjetRateLimitReason, ArcjetEmailReason, ArcjetShieldReason, ArcjetSensitiveInfoReason } from '@arcjet/protocol';\nexport * from '@arcjet/protocol';\nimport * as analyze from '@arcjet/analyze';\nimport * as duration from '@arcjet/duration';\nimport ArcjetHeaders from '@arcjet/headers';\nimport { runtime } from '@arcjet/runtime';\nimport * as hasher from '@arcjet/stable-hash';\nimport { MemoryCache } from '@arcjet/cache';\n\nfunction assert(condition, msg) {\n    if (!condition) {\n        throw new Error(msg);\n    }\n}\nfunction errorMessage(err) {\n    if (err) {\n        if (typeof err === \"string\") {\n            return err;\n        }\n        if (typeof err === \"object\" &&\n            \"message\" in err &&\n            typeof err.message === \"string\") {\n            return err.message;\n        }\n    }\n    return \"Unknown problem\";\n}\nconst knownFields = [\n    \"ip\",\n    \"method\",\n    \"protocol\",\n    \"host\",\n    \"path\",\n    \"headers\",\n    \"body\",\n    \"email\",\n    \"cookies\",\n    \"query\",\n];\nfunction isUnknownRequestProperty(key) {\n    return !knownFields.includes(key);\n}\nfunction isEmailType(type) {\n    return (type === \"FREE\" ||\n        type === \"DISPOSABLE\" ||\n        type === \"NO_MX_RECORDS\" ||\n        type === \"NO_GRAVATAR\" ||\n        type === \"INVALID\");\n}\nclass Performance {\n    log;\n    constructor(logger) {\n        this.log = logger;\n    }\n    // TODO(#2020): We should no-op this if loglevel is not `debug` to do less work\n    measure(label) {\n        const start = performance.now();\n        return () => {\n            const end = performance.now();\n            const diff = end - start;\n            this.log.debug(\"LATENCY %s: %sms\", label, diff.toFixed(3));\n        };\n    }\n}\nfunction toString(value) {\n    if (typeof value === \"string\") {\n        return value;\n    }\n    if (typeof value === \"number\") {\n        return `${value}`;\n    }\n    if (typeof value === \"boolean\") {\n        return value ? \"true\" : \"false\";\n    }\n    return \"<unsupported value>\";\n}\n// This is the Symbol that Vercel defines in their infrastructure to access the\n// Context (where available). The Context can contain the `waitUntil` function.\n// https://github.com/vercel/vercel/blob/930d7fb892dc26f240f2b950d963931c45e1e661/packages/functions/src/get-context.ts#L6\nconst SYMBOL_FOR_REQ_CONTEXT = Symbol.for(\"@vercel/request-context\");\nfunction lookupWaitUntil() {\n    const fromSymbol = globalThis;\n    if (typeof fromSymbol[SYMBOL_FOR_REQ_CONTEXT] === \"object\" &&\n        fromSymbol[SYMBOL_FOR_REQ_CONTEXT] !== null &&\n        \"get\" in fromSymbol[SYMBOL_FOR_REQ_CONTEXT] &&\n        typeof fromSymbol[SYMBOL_FOR_REQ_CONTEXT].get === \"function\") {\n        const vercelCtx = fromSymbol[SYMBOL_FOR_REQ_CONTEXT].get();\n        if (typeof vercelCtx === \"object\" &&\n            vercelCtx !== null &&\n            \"waitUntil\" in vercelCtx &&\n            typeof vercelCtx.waitUntil === \"function\") {\n            return vercelCtx.waitUntil;\n        }\n    }\n}\nfunction toAnalyzeRequest(request) {\n    const headers = {};\n    if (typeof request.headers !== \"undefined\") {\n        for (const [key, value] of request.headers.entries()) {\n            headers[key] = value;\n        }\n    }\n    return {\n        ...request,\n        headers,\n    };\n}\nfunction extraProps(details) {\n    const extra = new Map();\n    for (const [key, value] of Object.entries(details)) {\n        if (isUnknownRequestProperty(key)) {\n            extra.set(key, toString(value));\n        }\n    }\n    return Object.fromEntries(extra.entries());\n}\nfunction createTypeValidator(...types) {\n    return (key, value) => {\n        const typeOfValue = typeof value;\n        if (!types.includes(typeOfValue)) {\n            if (types.length === 1) {\n                throw new Error(`invalid type for \\`${key}\\` - expected ${types[0]}`);\n            }\n            else {\n                throw new Error(`invalid type for \\`${key}\\` - expected one of ${types.join(\", \")}`);\n            }\n        }\n        else {\n            return false;\n        }\n    };\n}\nfunction createValueValidator(\n// This uses types to ensure we have at least 2 values\n...values) {\n    return (key, value) => {\n        // We cast the values to unknown because the optionValue isn't known but\n        // we only want to use `values` on string enumerations\n        if (!values.includes(value)) {\n            throw new Error(`invalid value for \\`${key}\\` - expected one of ${values.map((value) => `'${value}'`).join(\", \")}`);\n        }\n    };\n}\nfunction createArrayValidator(validate) {\n    return (key, value) => {\n        if (Array.isArray(value)) {\n            for (const [idx, item] of value.entries()) {\n                validate(`${key}[${idx}]`, item);\n            }\n        }\n        else {\n            throw new Error(`invalid type for \\`${key}\\` - expected an array`);\n        }\n    };\n}\nfunction createValidator({ rule, validations, }) {\n    return (options) => {\n        for (const { key, validate, required } of validations) {\n            if (required && !Object.hasOwn(options, key)) {\n                throw new Error(`\\`${rule}\\` options error: \\`${key}\\` is required`);\n            }\n            const value = options[key];\n            // The `required` flag is checked above, so these should only be validated\n            // if the value is not undefined.\n            if (typeof value !== \"undefined\") {\n                try {\n                    validate(key, value);\n                }\n                catch (err) {\n                    throw new Error(`\\`${rule}\\` options error: ${errorMessage(err)}`);\n                }\n            }\n        }\n    };\n}\nconst validateString = createTypeValidator(\"string\");\nconst validateNumber = createTypeValidator(\"number\");\nconst validateBoolean = createTypeValidator(\"boolean\");\nconst validateFunction = createTypeValidator(\"function\");\nconst validateStringOrNumber = createTypeValidator(\"string\", \"number\");\nconst validateStringArray = createArrayValidator(validateString);\nconst validateMode = createValueValidator(\"LIVE\", \"DRY_RUN\");\nconst validateEmailTypes = createArrayValidator(createValueValidator(\"DISPOSABLE\", \"FREE\", \"NO_MX_RECORDS\", \"NO_GRAVATAR\", \"INVALID\"));\nconst validateTokenBucketOptions = createValidator({\n    rule: \"tokenBucket\",\n    validations: [\n        {\n            key: \"mode\",\n            required: false,\n            validate: validateMode,\n        },\n        {\n            key: \"characteristics\",\n            validate: validateStringArray,\n            required: false,\n        },\n        { key: \"refillRate\", required: true, validate: validateNumber },\n        { key: \"interval\", required: true, validate: validateStringOrNumber },\n        { key: \"capacity\", required: true, validate: validateNumber },\n    ],\n});\nconst validateFixedWindowOptions = createValidator({\n    rule: \"fixedWindow\",\n    validations: [\n        { key: \"mode\", required: false, validate: validateMode },\n        {\n            key: \"characteristics\",\n            validate: validateStringArray,\n            required: false,\n        },\n        { key: \"max\", required: true, validate: validateNumber },\n        { key: \"window\", required: true, validate: validateStringOrNumber },\n    ],\n});\nconst validateSlidingWindowOptions = createValidator({\n    rule: \"slidingWindow\",\n    validations: [\n        { key: \"mode\", required: false, validate: validateMode },\n        {\n            key: \"characteristics\",\n            validate: validateStringArray,\n            required: false,\n        },\n        { key: \"max\", required: true, validate: validateNumber },\n        { key: \"interval\", required: true, validate: validateStringOrNumber },\n    ],\n});\nconst validateSensitiveInfoOptions = createValidator({\n    rule: \"sensitiveInfo\",\n    validations: [\n        { key: \"mode\", required: false, validate: validateMode },\n        { key: \"allow\", required: false, validate: validateStringArray },\n        { key: \"deny\", required: false, validate: validateStringArray },\n        { key: \"contextWindowSize\", required: false, validate: validateNumber },\n        { key: \"detect\", required: false, validate: validateFunction },\n    ],\n});\nconst validateEmailOptions = createValidator({\n    rule: \"validateEmail\",\n    validations: [\n        { key: \"mode\", required: false, validate: validateMode },\n        { key: \"block\", required: false, validate: validateEmailTypes },\n        { key: \"allow\", required: false, validate: validateEmailTypes },\n        { key: \"deny\", required: false, validate: validateEmailTypes },\n        {\n            key: \"requireTopLevelDomain\",\n            required: false,\n            validate: validateBoolean,\n        },\n        { key: \"allowDomainLiteral\", required: false, validate: validateBoolean },\n    ],\n});\nconst validateBotOptions = createValidator({\n    rule: \"detectBot\",\n    validations: [\n        { key: \"mode\", required: false, validate: validateMode },\n        { key: \"allow\", required: false, validate: validateStringArray },\n        { key: \"deny\", required: false, validate: validateStringArray },\n    ],\n});\nconst validateShieldOptions = createValidator({\n    rule: \"shield\",\n    validations: [{ key: \"mode\", required: false, validate: validateMode }],\n});\nconst Priority = {\n    SensitiveInfo: 1,\n    Shield: 2,\n    RateLimit: 3,\n    BotDetection: 4,\n    EmailValidation: 5,\n};\nfunction isRateLimitRule(rule) {\n    return rule.type === \"RATE_LIMIT\";\n}\n/**\n * Arcjet token bucket rate limiting rule. Applying this rule sets a token\n * bucket rate limit.\n *\n * This algorithm is based on a bucket filled with a specific number of tokens.\n * Each request withdraws some amount of tokens from the bucket and the bucket\n * is refilled at a fixed rate. Once the bucket is empty, the client is blocked\n * until the bucket refills.\n *\n * This algorithm is useful when you want to allow clients to make a burst of\n * requests and then still be able to make requests at a slower rate.\n *\n * @param {TokenBucketRateLimitOptions} options - The options for the token\n * bucket rate limiting rule.\n * @param {ArcjetMode} options.mode - The block mode of the rule, either\n * `\"LIVE\"` or `\"DRY_RUN\"`. `\"LIVE\"` will block requests when the rate limit is\n * exceeded, and `\"DRY_RUN\"` will allow all requests while still providing\n * access to the rule results. Defaults to `\"DRY_RUN\"` if not specified.\n * @param {number} options.refillRate - The number of tokens to add to the\n * bucket at each interval. For example, if you set the interval to 60 and the\n * refill rate to 10, the bucket will refill 10 tokens every 60 seconds.\n * @param {string | number} options.interval - The time interval for the refill\n * rate. This can be a string like `\"60s\"` for 60 seconds, `\"1h45m\"` for 1 hour\n * and 45 minutes, or a number like `60` for 60 seconds. Valid string time units\n * are:\n * - `s` for seconds.\n * - `m` for minutes.\n * - `h` for hours.\n * - `d` for days.\n * @param {number} options.capacity - The maximum number of tokens the bucket\n * can hold. The bucket starts at full capacity and will refill until it hits\n * the capacity.\n * @returns {Primitive} The token bucket rule to provide to the SDK in the\n * `rules` option.\n *\n * @example\n * ```ts\n * tokenBucket({ mode: \"LIVE\", refillRate: 10, interval: \"60s\", capacity: 100 });\n * ```\n * @example\n * ```ts\n * const aj = arcjet({\n *   key: process.env.ARCJET_KEY,\n *   rules: [\n *     tokenBucket({\n *       mode: \"LIVE\",\n *       refillRate: 10,\n *       interval: \"60s\",\n *       capacity: 100,\n *     })\n *   ],\n * });\n * ```\n * @link https://docs.arcjet.com/rate-limiting/concepts\n * @link https://docs.arcjet.com/rate-limiting/algorithms#token-bucket\n * @link https://docs.arcjet.com/rate-limiting/reference\n */\nfunction tokenBucket(options) {\n    validateTokenBucketOptions(options);\n    const type = \"RATE_LIMIT\";\n    const version = 0;\n    const mode = options.mode === \"LIVE\" ? \"LIVE\" : \"DRY_RUN\";\n    const characteristics = Array.isArray(options.characteristics)\n        ? options.characteristics\n        : undefined;\n    const refillRate = options.refillRate;\n    const interval = duration.parse(options.interval);\n    const capacity = options.capacity;\n    const rule = {\n        type,\n        version,\n        priority: Priority.RateLimit,\n        mode,\n        characteristics,\n        algorithm: \"TOKEN_BUCKET\",\n        refillRate,\n        interval,\n        capacity,\n        validate() { },\n        async protect(context, details) {\n            const localCharacteristics = characteristics ?? context.characteristics;\n            const ruleId = await hasher.hash(hasher.string(\"type\", type), hasher.uint32(\"version\", version), hasher.string(\"mode\", mode), hasher.string(\"algorithm\", \"TOKEN_BUCKET\"), hasher.stringSliceOrdered(\"characteristics\", localCharacteristics), \n            // Match is deprecated so it is always an empty string in the newest SDKs\n            hasher.string(\"match\", \"\"), hasher.uint32(\"refillRate\", refillRate), hasher.uint32(\"interval\", interval), hasher.uint32(\"capacity\", capacity));\n            const analyzeContext = {\n                characteristics: localCharacteristics,\n                log: context.log,\n            };\n            const fingerprint = await analyze.generateFingerprint(analyzeContext, toAnalyzeRequest(details));\n            const [cached, ttl] = await context.cache.get(ruleId, fingerprint);\n            if (cached && cached.reason.isRateLimit()) {\n                return new ArcjetRuleResult({\n                    ruleId,\n                    fingerprint,\n                    ttl,\n                    state: \"CACHED\",\n                    conclusion: cached.conclusion,\n                    // We rebuild the `ArcjetRateLimitReason` because we need to adjust\n                    // the `reset` based on the current time-to-live\n                    reason: new ArcjetRateLimitReason({\n                        max: cached.reason.max,\n                        remaining: cached.reason.remaining,\n                        reset: ttl,\n                        window: cached.reason.window,\n                        resetTime: cached.reason.resetTime,\n                    }),\n                });\n            }\n            return new ArcjetRuleResult({\n                ruleId,\n                fingerprint,\n                ttl: 0,\n                state: \"NOT_RUN\",\n                conclusion: \"ALLOW\",\n                reason: new ArcjetRateLimitReason({\n                    max: 0,\n                    remaining: 0,\n                    reset: 0,\n                    window: 0,\n                    resetTime: new Date(),\n                }),\n            });\n        },\n    };\n    return [rule];\n}\n/**\n * Arcjet fixed window rate limiting rule. Applying this rule sets a fixed\n * window rate limit which tracks the number of requests made by a client over a\n * fixed time window.\n *\n * This is the simplest algorithm. It tracks the number of requests made by a\n * client over a fixed time window e.g. 60 seconds. If the client exceeds the\n * limit, they are blocked until the window expires.\n *\n * This algorithm is useful when you want to apply a simple fixed limit in a\n * fixed time window. For example, a simple limit on the total number of\n * requests a client can make. However, it can be susceptible to the stampede\n * problem where a client makes a burst of requests at the start of a window and\n * then is blocked for the rest of the window. The sliding window algorithm can\n * be used to avoid this.\n *\n * @param {FixedWindowRateLimitOptions} options - The options for the fixed\n * window rate limiting rule.\n * @param {ArcjetMode} options.mode - The block mode of the rule, either\n * `\"LIVE\"` or `\"DRY_RUN\"`. `\"LIVE\"` will block requests when the rate limit is\n * exceeded, and `\"DRY_RUN\"` will allow all requests while still providing\n * access to the rule results. Defaults to `\"DRY_RUN\"` if not specified.\n * @param {string | number} options.window - The fixed time window. This can be\n * a string like `\"60s\"` for 60 seconds, `\"1h45m\"` for 1 hour and 45 minutes, or\n * a number like `60` for 60 seconds. Valid string time units are:\n * - `s` for seconds.\n * - `m` for minutes.\n * - `h` for hours.\n * - `d` for days.\n * @param {number} options.max - The maximum number of requests allowed in the\n * fixed time window.\n * @returns {Primitive} The fixed window rule to provide to the SDK in the\n * `rules` option.\n *\n * @example\n * ```ts\n * fixedWindow({ mode: \"LIVE\", window: \"60s\", max: 100 });\n * ```\n * @example\n * ```ts\n * const aj = arcjet({\n *    key: process.env.ARCJET_KEY,\n *   rules: [\n *     fixedWindow({\n *       mode: \"LIVE\",\n *       window: \"60s\",\n *       max: 100,\n *     })\n *   ],\n * });\n * ```\n * @link https://docs.arcjet.com/rate-limiting/concepts\n * @link https://docs.arcjet.com/rate-limiting/algorithms#fixed-window\n * @link https://docs.arcjet.com/rate-limiting/reference\n */\nfunction fixedWindow(options) {\n    validateFixedWindowOptions(options);\n    const type = \"RATE_LIMIT\";\n    const version = 0;\n    const mode = options.mode === \"LIVE\" ? \"LIVE\" : \"DRY_RUN\";\n    const characteristics = Array.isArray(options.characteristics)\n        ? options.characteristics\n        : undefined;\n    const max = options.max;\n    const window = duration.parse(options.window);\n    const rule = {\n        type,\n        version,\n        priority: Priority.RateLimit,\n        mode,\n        characteristics,\n        algorithm: \"FIXED_WINDOW\",\n        max,\n        window,\n        validate() { },\n        async protect(context, details) {\n            const localCharacteristics = characteristics ?? context.characteristics;\n            const ruleId = await hasher.hash(hasher.string(\"type\", type), hasher.uint32(\"version\", version), hasher.string(\"mode\", mode), hasher.string(\"algorithm\", \"FIXED_WINDOW\"), hasher.stringSliceOrdered(\"characteristics\", localCharacteristics), \n            // Match is deprecated so it is always an empty string in the newest SDKs\n            hasher.string(\"match\", \"\"), hasher.uint32(\"max\", max), hasher.uint32(\"window\", window));\n            const analyzeContext = {\n                characteristics: localCharacteristics,\n                log: context.log,\n            };\n            const fingerprint = await analyze.generateFingerprint(analyzeContext, toAnalyzeRequest(details));\n            const [cached, ttl] = await context.cache.get(ruleId, fingerprint);\n            if (cached && cached.reason.isRateLimit()) {\n                return new ArcjetRuleResult({\n                    ruleId,\n                    fingerprint,\n                    ttl,\n                    state: \"CACHED\",\n                    conclusion: cached.conclusion,\n                    // We rebuild the `ArcjetRateLimitReason` because we need to adjust\n                    // the `reset` based on the current time-to-live\n                    reason: new ArcjetRateLimitReason({\n                        max: cached.reason.max,\n                        remaining: cached.reason.remaining,\n                        reset: ttl,\n                        window: cached.reason.window,\n                        resetTime: cached.reason.resetTime,\n                    }),\n                });\n            }\n            return new ArcjetRuleResult({\n                ruleId,\n                fingerprint,\n                ttl: 0,\n                state: \"NOT_RUN\",\n                conclusion: \"ALLOW\",\n                reason: new ArcjetRateLimitReason({\n                    max: 0,\n                    remaining: 0,\n                    reset: 0,\n                    window: 0,\n                }),\n            });\n        },\n    };\n    return [rule];\n}\n/**\n * Arcjet sliding window rate limiting rule. Applying this rule sets a sliding\n * window rate limit which tracks the number of requests made by a client over a\n * sliding window so that the window moves with time.\n *\n * This algorithm is useful to avoid the stampede problem of the fixed window.\n * It provides smoother rate limiting over time and can prevent a client from\n * making a burst of requests at the start of a window and then being blocked\n * for the rest of the window.\n *\n * @param {SlidingWindowRateLimitOptions} options - The options for the sliding\n * window rate limiting rule.\n * @param {ArcjetMode} options.mode - The block mode of the rule, either\n * `\"LIVE\"` or `\"DRY_RUN\"`. `\"LIVE\"` will block requests when the rate limit is\n * exceeded, and `\"DRY_RUN\"` will allow all requests while still providing\n * access to the rule results. Defaults to `\"DRY_RUN\"` if not specified.\n * @param {string | number} options.interval - The time interval for the rate\n * limit. This can be a string like `\"60s\"` for 60 seconds, `\"1h45m\"` for 1 hour\n * and 45 minutes, or a number like `60` for 60 seconds. Valid string time units\n * are:\n * - `s` for seconds.\n * - `m` for minutes.\n * - `h` for hours.\n * - `d` for days.\n * @param {number} options.max - The maximum number of requests allowed in the\n * sliding time window.\n * @returns {Primitive} The sliding window rule to provide to the SDK in the\n * `rules` option.\n *\n * @example\n * ```ts\n * slidingWindow({ mode: \"LIVE\", interval: \"60s\", max: 100 });\n * ```\n * @example\n * ```ts\n * const aj = arcjet({\n *   key: process.env.ARCJET_KEY,\n *   rules: [\n *     slidingWindow({\n *       mode: \"LIVE\",\n *       interval: \"60s\",\n *       max: 100,\n *     })\n *   ],\n * });\n * ```\n * @link https://docs.arcjet.com/rate-limiting/concepts\n * @link https://docs.arcjet.com/rate-limiting/algorithms#sliding-window\n * @link https://docs.arcjet.com/rate-limiting/reference\n */\nfunction slidingWindow(options) {\n    validateSlidingWindowOptions(options);\n    const type = \"RATE_LIMIT\";\n    const version = 0;\n    const mode = options.mode === \"LIVE\" ? \"LIVE\" : \"DRY_RUN\";\n    const characteristics = Array.isArray(options.characteristics)\n        ? options.characteristics\n        : undefined;\n    const max = options.max;\n    const interval = duration.parse(options.interval);\n    const rule = {\n        type,\n        version,\n        priority: Priority.RateLimit,\n        mode,\n        characteristics,\n        algorithm: \"SLIDING_WINDOW\",\n        max,\n        interval,\n        validate() { },\n        async protect(context, details) {\n            const localCharacteristics = characteristics ?? context.characteristics;\n            const ruleId = await hasher.hash(hasher.string(\"type\", type), hasher.uint32(\"version\", version), hasher.string(\"mode\", mode), hasher.string(\"algorithm\", \"SLIDING_WINDOW\"), hasher.stringSliceOrdered(\"characteristics\", localCharacteristics), \n            // Match is deprecated so it is always an empty string in the newest SDKs\n            hasher.string(\"match\", \"\"), hasher.uint32(\"max\", max), hasher.uint32(\"interval\", interval));\n            const analyzeContext = {\n                characteristics: localCharacteristics,\n                log: context.log,\n            };\n            const fingerprint = await analyze.generateFingerprint(analyzeContext, toAnalyzeRequest(details));\n            const [cached, ttl] = await context.cache.get(ruleId, fingerprint);\n            if (cached && cached.reason.isRateLimit()) {\n                return new ArcjetRuleResult({\n                    ruleId,\n                    fingerprint,\n                    ttl,\n                    state: \"CACHED\",\n                    conclusion: cached.conclusion,\n                    // We rebuild the `ArcjetRateLimitReason` because we need to adjust\n                    // the `reset` based on the current time-to-live\n                    reason: new ArcjetRateLimitReason({\n                        max: cached.reason.max,\n                        remaining: cached.reason.remaining,\n                        reset: ttl,\n                        window: cached.reason.window,\n                        resetTime: cached.reason.resetTime,\n                    }),\n                });\n            }\n            return new ArcjetRuleResult({\n                ruleId,\n                fingerprint,\n                ttl: 0,\n                state: \"NOT_RUN\",\n                conclusion: \"ALLOW\",\n                reason: new ArcjetRateLimitReason({\n                    max: 0,\n                    remaining: 0,\n                    reset: 0,\n                    window: 0,\n                }),\n            });\n        },\n    };\n    return [rule];\n}\nfunction protocolSensitiveInfoEntitiesToAnalyze(entity) {\n    if (typeof entity !== \"string\") {\n        throw new Error(\"invalid entity type\");\n    }\n    if (entity === \"EMAIL\") {\n        return { tag: \"email\" };\n    }\n    if (entity === \"PHONE_NUMBER\") {\n        return { tag: \"phone-number\" };\n    }\n    if (entity === \"IP_ADDRESS\") {\n        return { tag: \"ip-address\" };\n    }\n    if (entity === \"CREDIT_CARD_NUMBER\") {\n        return { tag: \"credit-card-number\" };\n    }\n    return {\n        tag: \"custom\",\n        val: entity,\n    };\n}\nfunction analyzeSensitiveInfoEntitiesToString(entity) {\n    if (entity.tag === \"email\") {\n        return \"EMAIL\";\n    }\n    if (entity.tag === \"ip-address\") {\n        return \"IP_ADDRESS\";\n    }\n    if (entity.tag === \"credit-card-number\") {\n        return \"CREDIT_CARD_NUMBER\";\n    }\n    if (entity.tag === \"phone-number\") {\n        return \"PHONE_NUMBER\";\n    }\n    return entity.val;\n}\nfunction convertAnalyzeDetectedSensitiveInfoEntity(detectedEntities) {\n    return detectedEntities.map((detectedEntity) => {\n        return {\n            ...detectedEntity,\n            identifiedType: analyzeSensitiveInfoEntitiesToString(detectedEntity.identifiedType),\n        };\n    });\n}\n/**\n * Arcjet sensitive information detection rule. Applying this rule protects\n * against clients sending you sensitive information such as personally\n * identifiable information (PII) that you do not wish to handle. The rule runs\n * entirely locally so no data ever leaves your environment.\n *\n * This rule includes built-in detections for email addresses, credit/debit card\n * numbers, IP addresses, and phone numbers. You can also provide a custom\n * detection function to identify additional sensitive information.\n *\n * @param {SensitiveInfoOptions} options - The options for the sensitive\n * information detection rule.\n * @param {ArcjetMode} options.mode - The block mode of the rule, either\n * `\"LIVE\"` or `\"DRY_RUN\"`. `\"LIVE\"` will block requests when any of the\n * configured sensitive information types are detected, and `\"DRY_RUN\"` will\n * allow all requests while still providing access to the rule results. Defaults\n * to `\"DRY_RUN\"` if not specified.\n * @param {Array<ArcjetSensitiveInfoType>} options.deny - The list of sensitive\n * information types to deny. If provided, the sensitive information types in\n * this list will be denied. You may only provide either `allow` or `deny`, not\n * both. Specify one or more of the following:\n *\n * - `\"EMAIL\"`\n * - `\"PHONE_NUMBER\"`\n * - `\"IP_ADDRESS\"`\n * - `\"CREDIT_CARD_NUMBER\"`\n * @param {Array<ArcjetSensitiveInfoType>} options.allow - The list of sensitive\n * information types to allow. If provided, types in this list will be allowed\n * and all others will be denied. You may only provide either `allow` or `deny`,\n * not both. The same options apply as for `deny`.\n * @param {DetectSensitiveInfoEntities} options.detect - A custom detection\n * function. The function will take a list of tokens and must return a list of\n * either `undefined`, if the corresponding token in the input list is not\n * sensitive, or the name of the entity if it does match. The number of tokens\n * that are provided to the function is controlled by the `contextWindowSize`\n * option, which defaults to `1`. If you need additional context to perform\n * detections then you can increase this value.\n * @param {number} options.contextWindowSize - The number of tokens to provide\n * to the custom detection function. This defaults to 1 if not specified.\n * @returns {Primitive} The sensitive information rule to provide to the SDK in\n * the `rules` option.\n *\n * @example\n * ```ts\n * sensitiveInfo({ mode: \"LIVE\", deny: [\"EMAIL\"] });\n * ```\n * @example\n * ```ts\n * const aj = arcjet({\n *   key: process.env.ARCJET_KEY,\n *   rules: [\n *     sensitiveInfo({\n *       mode: \"LIVE\",\n *       deny: [\"EMAIL\"],\n *     })\n *   ],\n * });\n * ```\n * @example\n * Custom detection function:\n * ```ts\n * function detectDash(tokens: string[]): Array<\"CONTAINS_DASH\" | undefined> {\n *   return tokens.map((token) => {\n *     if (token.includes(\"-\")) {\n *       return \"CONTAINS_DASH\";\n *     }\n *   });\n * }\n *\n * const aj = arcjet({\n *   key: process.env.ARCJET_KEY,\n *   rules: [\n *     sensitiveInfo({\n *       mode: \"LIVE\",\n *       deny: [\"EMAIL\", \"CONTAINS_DASH\"],\n *       detect: detectDash,\n *       contextWindowSize: 2,\n *     })\n *   ],\n * });\n * ```\n * @link https://docs.arcjet.com/sensitive-info/concepts\n * @link https://docs.arcjet.com/sensitive-info/reference\n */\nfunction sensitiveInfo(options) {\n    validateSensitiveInfoOptions(options);\n    if (typeof options.allow !== \"undefined\" &&\n        typeof options.deny !== \"undefined\") {\n        throw new Error(\"`sensitiveInfo` options error: `allow` and `deny` cannot be provided together\");\n    }\n    if (typeof options.allow === \"undefined\" &&\n        typeof options.deny === \"undefined\") {\n        throw new Error(\"`sensitiveInfo` options error: either `allow` or `deny` must be specified\");\n    }\n    const type = \"SENSITIVE_INFO\";\n    const version = 0;\n    const mode = options.mode === \"LIVE\" ? \"LIVE\" : \"DRY_RUN\";\n    const allow = options.allow || [];\n    const deny = options.deny || [];\n    const rule = {\n        version,\n        priority: Priority.SensitiveInfo,\n        type,\n        mode,\n        allow,\n        deny,\n        validate(context, details) { },\n        async protect(context, details) {\n            const ruleId = await hasher.hash(hasher.string(\"type\", type), hasher.uint32(\"version\", version), hasher.string(\"mode\", mode), hasher.stringSliceOrdered(\"allow\", allow), hasher.stringSliceOrdered(\"deny\", deny));\n            const { fingerprint } = context;\n            // No cache is implemented here because the fingerprint can be the same\n            // while the request body changes. This is also why the `sensitiveInfo`\n            // rule results always have a `ttl` of 0.\n            const body = await context.getBody();\n            if (typeof body === \"undefined\") {\n                return new ArcjetRuleResult({\n                    ruleId,\n                    fingerprint,\n                    ttl: 0,\n                    state: \"NOT_RUN\",\n                    conclusion: \"ERROR\",\n                    reason: new ArcjetErrorReason(\"Couldn't read the body of the request to perform sensitive info identification.\"),\n                });\n            }\n            let convertedDetect = undefined;\n            if (typeof options.detect !== \"undefined\") {\n                const detect = options.detect;\n                convertedDetect = (tokens) => {\n                    return detect(tokens)\n                        .filter((e) => typeof e !== \"undefined\")\n                        .map(protocolSensitiveInfoEntitiesToAnalyze);\n                };\n            }\n            let entitiesTag = \"allow\";\n            let entitiesVal = [];\n            if (Array.isArray(options.allow)) {\n                entitiesTag = \"allow\";\n                entitiesVal = options.allow\n                    .filter((e) => typeof e !== \"undefined\")\n                    .map(protocolSensitiveInfoEntitiesToAnalyze);\n            }\n            if (Array.isArray(options.deny)) {\n                entitiesTag = \"deny\";\n                entitiesVal = options.deny\n                    .filter((e) => typeof e !== \"undefined\")\n                    .map(protocolSensitiveInfoEntitiesToAnalyze);\n            }\n            const entities = {\n                tag: entitiesTag,\n                val: entitiesVal,\n            };\n            const result = await analyze.detectSensitiveInfo(context, body, entities, options.contextWindowSize || 1, convertedDetect);\n            const state = mode === \"LIVE\" ? \"RUN\" : \"DRY_RUN\";\n            const reason = new ArcjetSensitiveInfoReason({\n                denied: convertAnalyzeDetectedSensitiveInfoEntity(result.denied),\n                allowed: convertAnalyzeDetectedSensitiveInfoEntity(result.allowed),\n            });\n            if (result.denied.length === 0) {\n                return new ArcjetRuleResult({\n                    ruleId,\n                    fingerprint,\n                    ttl: 0,\n                    state,\n                    conclusion: \"ALLOW\",\n                    reason,\n                });\n            }\n            else {\n                return new ArcjetRuleResult({\n                    ruleId,\n                    fingerprint,\n                    ttl: 0,\n                    state,\n                    conclusion: \"DENY\",\n                    reason,\n                });\n            }\n        },\n    };\n    return [rule];\n}\n/**\n * Arcjet email validation rule. Applying this rule allows you to validate &\n * verify an email address.\n *\n * The first step of the analysis is to validate the email address syntax. This\n * runs locally within the SDK and validates the email address is in the correct\n * format. If the email syntax is valid, the SDK will pass the email address to\n * the Arcjet cloud API to verify the email address. This performs several\n * checks, depending on the rule configuration.\n *\n * @param {EmailOptions} options - The options for the email validation rule.\n * @param {ArcjetMode} options.mode - The block mode of the rule, either\n * `\"LIVE\"` or `\"DRY_RUN\"`. `\"LIVE\"` will block email addresses based on the\n * configuration, and `\"DRY_RUN\"` will allow all requests while still providing\n * access to the rule results. Defaults to `\"DRY_RUN\"` if not specified.\n * @param {Array<ArcjetEmailType>} options.deny - The list of email types to\n * deny. If provided, the email types in this list will be denied. You may only\n * provide either `allow` or `deny`, not both. Specify one or more of the\n * following:\n *\n * - `\"DISPOSABLE\"` - Disposable email addresses.\n * - `\"FREE\"` - Free email addresses.\n * - `\"NO_MX_RECORDS\"` - Email addresses with no MX records.\n * - `\"NO_GRAVATAR\"` - Email addresses with no Gravatar.\n * - `\"INVALID\"` - Invalid email addresses.\n *\n * @param {Array<ArcjetEmailType>} options.allow - The list of email types to\n * allow. If provided, email addresses in this list will be allowed and all\n * others will be denied. You may only provide either `allow` or `deny`, not\n * both. The same options apply as for `deny`.\n * @returns {Primitive} The email rule to provide to the SDK in the `rules`\n * option.\n *\n * @example\n * ```ts\n * validateEmail({ mode: \"LIVE\", deny: [\"DISPOSABLE\", \"INVALID\"] });\n * ```\n * @example\n * ```ts\n * const aj = arcjet({\n *   key: process.env.ARCJET_KEY,\n *   rules: [\n *     validateEmail({\n *       mode: \"LIVE\",\n *       deny: [\"DISPOSABLE\", \"INVALID\"]\n *     })\n *   ],\n * });\n * ```\n * @link https://docs.arcjet.com/email-validation/concepts\n * @link https://docs.arcjet.com/email-validation/reference\n */\nfunction validateEmail(options) {\n    validateEmailOptions(options);\n    if (typeof options.allow !== \"undefined\" &&\n        typeof options.deny !== \"undefined\") {\n        throw new Error(\"`validateEmail` options error: `allow` and `deny` cannot be provided together\");\n    }\n    if (typeof options.allow !== \"undefined\" &&\n        typeof options.block !== \"undefined\") {\n        throw new Error(\"`validateEmail` options error: `allow` and `block` cannot be provided together\");\n    }\n    if (typeof options.deny !== \"undefined\" &&\n        typeof options.block !== \"undefined\") {\n        throw new Error(\"`validateEmail` options error: `deny` and `block` cannot be provided together, `block` is now deprecated so `deny` should be preferred.\");\n    }\n    if (typeof options.allow === \"undefined\" &&\n        typeof options.deny === \"undefined\" &&\n        typeof options.block === \"undefined\") {\n        throw new Error(\"`validateEmail` options error: either `allow` or `deny` must be specified\");\n    }\n    const type = \"EMAIL\";\n    const version = 0;\n    const mode = options.mode === \"LIVE\" ? \"LIVE\" : \"DRY_RUN\";\n    const allow = options.allow ?? [];\n    const deny = options.deny ?? options.block ?? [];\n    const requireTopLevelDomain = options.requireTopLevelDomain ?? true;\n    const allowDomainLiteral = options.allowDomainLiteral ?? false;\n    let config = {\n        tag: \"deny-email-validation-config\",\n        val: {\n            requireTopLevelDomain,\n            allowDomainLiteral,\n            deny: [],\n        },\n    };\n    if (typeof options.allow !== \"undefined\") {\n        config = {\n            tag: \"allow-email-validation-config\",\n            val: {\n                requireTopLevelDomain,\n                allowDomainLiteral,\n                allow: options.allow,\n            },\n        };\n    }\n    if (typeof options.deny !== \"undefined\") {\n        config = {\n            tag: \"deny-email-validation-config\",\n            val: {\n                requireTopLevelDomain,\n                allowDomainLiteral,\n                deny: options.deny,\n            },\n        };\n    }\n    if (typeof options.block !== \"undefined\") {\n        config = {\n            tag: \"deny-email-validation-config\",\n            val: {\n                requireTopLevelDomain,\n                allowDomainLiteral,\n                deny: options.block,\n            },\n        };\n    }\n    const rule = {\n        version,\n        priority: Priority.EmailValidation,\n        type,\n        mode,\n        allow,\n        deny,\n        requireTopLevelDomain,\n        allowDomainLiteral,\n        validate(context, details) {\n            assert(typeof details.email !== \"undefined\", \"ValidateEmail requires `email` to be set.\");\n        },\n        async protect(context, { email }) {\n            const ruleId = await hasher.hash(hasher.string(\"type\", type), hasher.uint32(\"version\", version), hasher.string(\"mode\", mode), hasher.stringSliceOrdered(\"allow\", allow), hasher.stringSliceOrdered(\"deny\", deny), hasher.bool(\"requireTopLevelDomain\", requireTopLevelDomain), hasher.bool(\"allowDomainLiteral\", allowDomainLiteral));\n            const { fingerprint } = context;\n            // No cache is implemented here because the fingerprint can be the same\n            // while the email changes. This is also why the `email` rule results\n            // always have a `ttl` of 0.\n            const result = await analyze.isValidEmail(context, email, config);\n            const state = mode === \"LIVE\" ? \"RUN\" : \"DRY_RUN\";\n            if (result.validity === \"valid\") {\n                return new ArcjetRuleResult({\n                    ruleId,\n                    fingerprint,\n                    ttl: 0,\n                    state,\n                    conclusion: \"ALLOW\",\n                    reason: new ArcjetEmailReason({ emailTypes: [] }),\n                });\n            }\n            else {\n                const typedEmailTypes = result.blocked.filter(isEmailType);\n                return new ArcjetRuleResult({\n                    ruleId,\n                    fingerprint,\n                    ttl: 0,\n                    state,\n                    conclusion: \"DENY\",\n                    reason: new ArcjetEmailReason({\n                        emailTypes: typedEmailTypes,\n                    }),\n                });\n            }\n        },\n    };\n    return [rule];\n}\n/**\n * Arcjet bot detection rule. Applying this rule allows you to manage traffic by\n * automated clients and bots.\n *\n * Bots can be good (such as search engine crawlers or monitoring agents) or bad\n * (such as scrapers or automated scripts). Arcjet allows you to configure which\n * bots you want to allow or deny by specific bot names e.g. curl, as well as by\n * category e.g. search engine bots.\n *\n * Bots are detected based on various signals such as the user agent, IP\n * address, DNS records, and more.\n *\n * @param {BotOptions} options - The options for the bot rule.\n * @param {ArcjetMode} options.mode - The block mode of the rule, either\n * `\"LIVE\"` or `\"DRY_RUN\"`. `\"LIVE\"` will block detected bots, and `\"DRY_RUN\"`\n * will allow all requests while still providing access to the rule results.\n * Defaults to `\"DRY_RUN\"` if not specified.\n * @param {Array<ArcjetWellKnownBot | ArcjetBotCategory>} options.allow - The\n * list of bots to allow. If provided, only the bots in this list will be\n * allowed and any other detected bot will be denied. If empty, all bots will be\n * denied. You may only provide either `allow` or `deny`, not both. You can use\n * specific bots e.g. `\"CURL\"` will allow the default user-agent of the `curl`\n * tool. You can also use categories e.g. `\"CATEGORY:SEARCH_ENGINE\"` will allow\n * all search engine bots. See\n * https://docs.arcjet.com/bot-protection/identifying-bots for the full list of\n * bots and categories.\n * @param {Array<ArcjetWellKnownBot | ArcjetBotCategory>} options.deny - The\n * list of bots to deny. If provided, the bots in this list will be denied and\n * all other detected bots will be allowed. You may only provide either `allow`\n * or `deny`, not both. The same options apply as for `allow`.\n * @returns {Primitive} The bot rule to provide to the SDK in the `rules`\n * option.\n *\n * @example\n * Allows search engine bots and curl, denies all other bots\n *\n * ```ts\n * detectBot({ mode: \"LIVE\", allow: [\"CATEGORY:SEARCH_ENGINE\", \"CURL\"] });\n * ```\n * @example\n * Allows search engine bots and curl, denies all other bots\n *\n * ```ts\n * const aj = arcjet({\n *   key: process.env.ARCJET_KEY,\n *   rules: [\n *     detectBot({\n *       mode: \"LIVE\",\n *       allow: [\"CATEGORY:SEARCH_ENGINE\", \"CURL\"]\n *     })\n *   ],\n * });\n * ```\n * @example\n * Denies AI crawlers, allows all other bots\n *\n * ```ts\n * detectBot({ mode: \"LIVE\", deny: [\"CATEGORY:AI\"] });\n * ```\n * @example\n * Denies AI crawlers, allows all other bots\n * ```ts\n * const aj = arcjet({\n *   key: process.env.ARCJET_KEY,\n *   rules: [\n *     detectBot({\n *       mode: \"LIVE\",\n *       deny: [\"CATEGORY:AI\"]\n *     })\n *   ],\n * });\n * ```\n * @link https://docs.arcjet.com/bot-protection/concepts\n * @link https://docs.arcjet.com/bot-protection/identifying-bots\n * @link https://docs.arcjet.com/bot-protection/reference\n */\nfunction detectBot(options) {\n    validateBotOptions(options);\n    if (typeof options.allow !== \"undefined\" &&\n        typeof options.deny !== \"undefined\") {\n        throw new Error(\"`detectBot` options error: `allow` and `deny` cannot be provided together\");\n    }\n    if (typeof options.allow === \"undefined\" &&\n        typeof options.deny === \"undefined\") {\n        throw new Error(\"`detectBot` options error: either `allow` or `deny` must be specified\");\n    }\n    const type = \"BOT\";\n    const version = 0;\n    const mode = options.mode === \"LIVE\" ? \"LIVE\" : \"DRY_RUN\";\n    const allow = options.allow ?? [];\n    const deny = options.deny ?? [];\n    let config = {\n        tag: \"allowed-bot-config\",\n        val: {\n            entities: [],\n            skipCustomDetect: true,\n        },\n    };\n    if (typeof options.allow !== \"undefined\") {\n        config = {\n            tag: \"allowed-bot-config\",\n            val: {\n                entities: options.allow,\n                skipCustomDetect: true,\n            },\n        };\n    }\n    if (typeof options.deny !== \"undefined\") {\n        config = {\n            tag: \"denied-bot-config\",\n            val: {\n                entities: options.deny,\n                skipCustomDetect: true,\n            },\n        };\n    }\n    const rule = {\n        version,\n        priority: Priority.BotDetection,\n        type,\n        mode,\n        allow,\n        deny,\n        validate(context, details) {\n            if (typeof details.headers === \"undefined\") {\n                throw new Error(\"bot detection requires `headers` to be set\");\n            }\n            if (typeof details.headers.has !== \"function\") {\n                throw new Error(\"bot detection requires `headers` to extend `Headers`\");\n            }\n            if (!details.headers.has(\"user-agent\")) {\n                throw new Error(\"bot detection requires user-agent header\");\n            }\n        },\n        /**\n         * Attempts to call the bot detection on the headers.\n         */\n        async protect(context, request) {\n            const ruleId = await hasher.hash(hasher.string(\"type\", type), hasher.uint32(\"version\", version), hasher.string(\"mode\", mode), hasher.stringSliceOrdered(\"allow\", allow), hasher.stringSliceOrdered(\"deny\", deny));\n            const { fingerprint } = context;\n            const [cached, ttl] = await context.cache.get(ruleId, fingerprint);\n            if (cached) {\n                return new ArcjetRuleResult({\n                    ruleId,\n                    fingerprint,\n                    ttl,\n                    state: \"CACHED\",\n                    conclusion: cached.conclusion,\n                    reason: cached.reason,\n                });\n            }\n            const result = await analyze.detectBot(context, toAnalyzeRequest(request), config);\n            const state = mode === \"LIVE\" ? \"RUN\" : \"DRY_RUN\";\n            // If this is a bot and of a type that we want to block, then block!\n            if (result.denied.length > 0) {\n                return new ArcjetRuleResult({\n                    ruleId,\n                    fingerprint,\n                    ttl: 60,\n                    state,\n                    conclusion: \"DENY\",\n                    reason: new ArcjetBotReason({\n                        allowed: result.allowed,\n                        denied: result.denied,\n                        verified: result.verified,\n                        spoofed: result.spoofed,\n                    }),\n                });\n            }\n            else {\n                return new ArcjetRuleResult({\n                    ruleId,\n                    fingerprint,\n                    ttl: 0,\n                    state,\n                    conclusion: \"ALLOW\",\n                    reason: new ArcjetBotReason({\n                        allowed: result.allowed,\n                        denied: result.denied,\n                        verified: result.verified,\n                        spoofed: result.spoofed,\n                    }),\n                });\n            }\n        },\n    };\n    return [rule];\n}\n/**\n * Arcjet Shield WAF rule. Applying this rule protects your application against\n * common attacks, including the OWASP Top 10.\n *\n * The Arcjet Shield WAF analyzes every request to your application to detect\n * suspicious activity. Once a certain suspicion threshold is reached,\n * subsequent requests from that client are blocked for a period of time.\n *\n * @param {ShieldOptions} options - The options for the Shield rule.\n * @param {ArcjetMode} options.mode - The block mode of the rule, either\n * `\"LIVE\"` or `\"DRY_RUN\"`. `\"LIVE\"` will block suspicious requests, and\n * `\"DRY_RUN\"` will allow all requests while still providing access to the rule\n * results. Defaults to `\"DRY_RUN\"` if not specified.\n * @returns {Primitive} The Shield rule to provide to the SDK in the `rules`\n * option.\n *\n * @example\n * ```ts\n * shield({ mode: \"LIVE\" });\n * ```\n * @example\n * ```ts\n * const aj = arcjet({\n *   key: process.env.ARCJET_KEY,\n *   rules: [shield({ mode: \"LIVE\" })],\n * });\n * ```\n * @link https://docs.arcjet.com/shield/concepts\n * @link https://docs.arcjet.com/shield/reference\n */\nfunction shield(options) {\n    validateShieldOptions(options);\n    const type = \"SHIELD\";\n    const version = 0;\n    const mode = options.mode === \"LIVE\" ? \"LIVE\" : \"DRY_RUN\";\n    const rule = {\n        type,\n        version,\n        priority: Priority.Shield,\n        mode,\n        validate() { },\n        async protect(context, details) {\n            // TODO(#1989): Prefer characteristics defined on rule once available\n            const localCharacteristics = context.characteristics;\n            const ruleId = await hasher.hash(hasher.string(\"type\", type), hasher.uint32(\"version\", version), hasher.string(\"mode\", mode), hasher.stringSliceOrdered(\"characteristics\", localCharacteristics));\n            const analyzeContext = {\n                characteristics: localCharacteristics,\n                log: context.log,\n            };\n            const fingerprint = await analyze.generateFingerprint(analyzeContext, toAnalyzeRequest(details));\n            const [cached, ttl] = await context.cache.get(ruleId, fingerprint);\n            if (cached) {\n                return new ArcjetRuleResult({\n                    ruleId,\n                    fingerprint,\n                    ttl,\n                    state: \"CACHED\",\n                    conclusion: cached.conclusion,\n                    reason: cached.reason,\n                });\n            }\n            return new ArcjetRuleResult({\n                ruleId,\n                fingerprint,\n                ttl: 0,\n                state: \"NOT_RUN\",\n                conclusion: \"ALLOW\",\n                reason: new ArcjetShieldReason({\n                    shieldTriggered: false,\n                }),\n            });\n        },\n    };\n    return [rule];\n}\n/**\n * Arcjet signup form protection rule. Applying this rule combines rate\n * limiting, bot protection, and email validation to protect your signup forms\n * from abuse. Using this rule will configure the following:\n *\n * - Rate limiting - signup forms are a common target for bots. Arcjet’s rate\n *   limiting helps to prevent bots and other automated or malicious clients\n *   from submitting your signup form too many times in a short period of time.\n * - Bot protection - signup forms are usually exclusively used by humans, which\n *   means that any automated submissions to the form are likely to be\n *   fraudulent.\n * - Email validation - email addresses should be validated to ensure the signup\n *   is coming from a legitimate user with a real email address that can\n *   actually receive messages.\n *\n * @param {ProtectSignupOptions} options - The options for the signup form\n * protection rule.\n * @param {ArcjetMode} options.email.mode - The block mode of the rule, either\n * `\"LIVE\"` or `\"DRY_RUN\"`. `\"LIVE\"` will block email addresses based on the\n * configuration, and `\"DRY_RUN\"` will allow all requests while still providing\n * access to the rule results. Defaults to `\"DRY_RUN\"` if not specified.\n * @param {Array<ArcjetEmailType>} options.email.deny - The list of email types\n * to deny. If provided, the email types in this list will be denied. You may\n * only provide either `allow` or `deny`, not both. Specify one or more of the\n * following:\n *\n * - `\"DISPOSABLE\"` - Disposable email addresses.\n * - `\"FREE\"` - Free email addresses.\n * - `\"NO_MX_RECORDS\"` - Email addresses with no MX records.\n * - `\"NO_GRAVATAR\"` - Email addresses with no Gravatar.\n * - `\"INVALID\"` - Invalid email addresses.\n *\n * @param {Array<ArcjetEmailType>} options.email.allow - The list of email types\n * to allow. If provided, email addresses in this list will be allowed and all\n * others will be denied. You may only provide either `allow` or `deny`, not\n * both. The same options apply as for `deny`.\n * @param {ArcjetMode} options.bots.mode - The block mode of the rule, either\n * `\"LIVE\"` or `\"DRY_RUN\"`. `\"LIVE\"` will block detected bots, and `\"DRY_RUN\"`\n * will allow all requests while still providing access to the rule results.\n * Defaults to `\"DRY_RUN\"` if not specified.\n * @param {Array<ArcjetWellKnownBot | ArcjetBotCategory>} options.bots.allow -\n * The list of bots to allow. If provided, only the bots in this list will be\n * allowed and any other detected bot will be denied. If empty, all bots will be\n * denied. You may only provide either `allow` or `deny`, not both. You can use\n * specific bots e.g. `\"CURL\"` will allow the default user-agent of the `curl`\n * tool. You can also use categories e.g. `\"CATEGORY:SEARCH_ENGINE\"` will allow\n * all search engine bots. See\n * https://docs.arcjet.com/bot-protection/identifying-bots for the full list of\n * bots and categories.\n * @param {Array<ArcjetWellKnownBot | ArcjetBotCategory>} options.bots.deny -\n * The list of bots to deny. If provided, the bots in this list will be denied\n * and all other detected bots will be allowed. You may only provide either\n * `allow` or `deny`, not both. The same options apply as for `allow`.\n * @param {SlidingWindowRateLimitOptions} options.rateLimit - The options for\n * the sliding window rate limiting rule.\n * @param {ArcjetMode} options.rateLimit.mode - The block mode of the rule,\n * either `\"LIVE\"` or `\"DRY_RUN\"`. `\"LIVE\"` will block requests when the rate\n * limit is exceeded, and `\"DRY_RUN\"` will allow all requests while still\n * providing access to the rule results. Defaults to `\"DRY_RUN\"` if not\n * specified.\n * @param {string | number} options.rateLimit.interval - The time interval for\n * the rate limit. This can be a string like `\"60s\"` for 60 seconds, `\"1h45m\"`\n * for 1 hour and 45 minutes, or a number like `60` for 60 seconds. Valid string\n * time units are:\n * - `s` for seconds.\n * - `m` for minutes.\n * - `h` for hours.\n * - `d` for days.\n * @param {number} options.rateLimit.max - The maximum number of requests\n * allowed in the sliding time window.\n * @returns {Primitive} The signup form protection rule to provide to the SDK in\n * the `rules` option.\n *\n * @example\n * Our recommended configuration for most signup forms is:\n *\n * - Block emails with invalid syntax, that are from disposable email providers,\n *   or do not have valid MX records configured.\n * - Block all bots.\n * - Apply a rate limit of 5 submissions per 10 minutes from a single IP\n *   address.\n *\n * ```ts\n * const aj = arcjet({\n *   key: process.env.ARCJET_KEY,\n *   rules: [\n *    protectSignup({\n *      email: {\n *        mode: \"LIVE\",\n *        block: [\"DISPOSABLE\", \"INVALID\", \"NO_MX_RECORDS\"],\n *      },\n *      bots: {\n *        mode: \"LIVE\",\n *        allow: [], // block all detected bots\n *      },\n *      rateLimit: {\n *        mode: \"LIVE\",\n *        interval: \"10m\",\n *        max: 5,\n *      },\n *    }),\n *  ],\n * });\n * ```\n * @link https://docs.arcjet.com/signup-protection/concepts\n * @link https://docs.arcjet.com/signup-protection/reference\n */\nfunction protectSignup(options) {\n    return [\n        ...slidingWindow(options.rateLimit),\n        ...detectBot(options.bots),\n        ...validateEmail(options.email),\n    ];\n}\n/**\n * Create a new Arcjet client with the specified {@link ArcjetOptions}.\n *\n * @param options {ArcjetOptions} Arcjet configuration options.\n */\nfunction arcjet(options) {\n    // We destructure here to make the function signature neat when viewed by consumers\n    const { key, rules } = options;\n    const rt = runtime();\n    // TODO: Separate the ArcjetOptions from the SDK Options\n    // It is currently optional in the options so users can override it via an SDK\n    if (typeof options.log === \"undefined\") {\n        throw new Error(\"Log is required\");\n    }\n    const log = options.log;\n    const perf = new Performance(log);\n    // TODO(#207): Remove this when we can default the transport so client is not required\n    // It is currently optional in the options so the Next SDK can override it for the user\n    if (typeof options.client === \"undefined\") {\n        throw new Error(\"Client is required\");\n    }\n    const client = options.client;\n    // A local cache of block decisions. Might be emphemeral per request,\n    // depending on the way the runtime works, but it's worth a try.\n    // TODO(#132): Support configurable caching\n    const cache = new MemoryCache();\n    const rootRules = rules\n        .flat(1)\n        .sort((a, b) => a.priority - b.priority);\n    async function protect(rules, ctx, request) {\n        // This goes against the type definition above, but users might call\n        // `protect()` with no value and we don't want to crash\n        if (typeof request === \"undefined\") {\n            request = {};\n        }\n        const details = Object.freeze({\n            ip: request.ip,\n            method: request.method,\n            protocol: request.protocol,\n            host: request.host,\n            path: request.path,\n            headers: new ArcjetHeaders(request.headers),\n            cookies: request.cookies,\n            query: request.query,\n            // TODO(#208): Re-add body\n            // body: request.body,\n            extra: extraProps(request),\n            email: typeof request.email === \"string\" ? request.email : undefined,\n        });\n        const characteristics = options.characteristics\n            ? [...options.characteristics]\n            : [];\n        const waitUntil = lookupWaitUntil();\n        const baseContext = {\n            key,\n            log,\n            characteristics,\n            waitUntil,\n            ...ctx,\n        };\n        let fingerprint = \"\";\n        const logFingerprintPerf = perf.measure(\"fingerprint\");\n        try {\n            fingerprint = await analyze.generateFingerprint(baseContext, toAnalyzeRequest(details));\n            log.debug(\"fingerprint (%s): %s\", rt, fingerprint);\n        }\n        catch (error) {\n            log.error({ error }, \"Failed to build fingerprint. Please verify your Characteristics.\");\n            const decision = new ArcjetErrorDecision({\n                ttl: 0,\n                reason: new ArcjetErrorReason(`Failed to build fingerprint - ${errorMessage(error)}`),\n                // No results because we couldn't create a fingerprint\n                results: [],\n            });\n            // TODO: Consider sending this to Report when we have an infallible fingerprint\n            return decision;\n        }\n        finally {\n            logFingerprintPerf();\n        }\n        const context = Object.freeze({\n            ...baseContext,\n            cache,\n            fingerprint,\n            runtime: rt,\n        });\n        if (rules.length < 1) {\n            log.warn(\"Calling `protect()` with no rules is deprecated. Did you mean to configure the Shield rule?\");\n        }\n        if (rules.length > 10) {\n            log.error(\"Failure running rules. Only 10 rules may be specified.\");\n            const decision = new ArcjetErrorDecision({\n                ttl: 0,\n                reason: new ArcjetErrorReason(\"Only 10 rules may be specified\"),\n                // No results because the sorted rules were too long and we don't want\n                // to instantiate a ton of NOT_RUN results\n                results: [],\n            });\n            client.report(context, details, decision, \n            // No rules because we've determined they were too long and we don't\n            // want to try to send them to the server\n            []);\n            return decision;\n        }\n        const results = [];\n        for (let idx = 0; idx < rules.length; idx++) {\n            // Default all rules to NOT_RUN/ALLOW before doing anything\n            results[idx] = new ArcjetRuleResult({\n                // TODO(#4030): Figure out if we can get each Rule ID before they are run\n                ruleId: \"\",\n                fingerprint,\n                ttl: 0,\n                state: \"NOT_RUN\",\n                conclusion: \"ALLOW\",\n                reason: new ArcjetReason(),\n            });\n            // Add top-level characteristics to all Rate Limit rules that don't already have\n            // their own set of characteristics.\n            const candidate_rule = rules[idx];\n            if (isRateLimitRule(candidate_rule)) {\n                if (typeof candidate_rule.characteristics === \"undefined\") {\n                    candidate_rule.characteristics = characteristics;\n                    rules[idx] = candidate_rule;\n                }\n            }\n        }\n        const logLocalPerf = perf.measure(\"local\");\n        try {\n            for (const [idx, rule] of rules.entries()) {\n                // This re-assignment is a workaround to a TypeScript error with\n                // assertions where the name was introduced via a destructure\n                const localRule = rule;\n                const logRulePerf = perf.measure(rule.type);\n                try {\n                    if (typeof localRule.validate !== \"function\") {\n                        throw new Error(\"rule must have a `validate` function\");\n                    }\n                    localRule.validate(context, details);\n                    if (typeof localRule.protect !== \"function\") {\n                        throw new Error(\"rule must have a `protect` function\");\n                    }\n                    results[idx] = await localRule.protect(context, details);\n                    // If a rule didn't return a rule result, we need to stub it to avoid\n                    // crashing. This should only happen if a user writes a custom local\n                    // rule incorrectly.\n                    if (typeof results[idx] === \"undefined\") {\n                        results[idx] = new ArcjetRuleResult({\n                            // TODO(#4030): If we can get the Rule ID before running rules,\n                            // this can use it\n                            ruleId: \"\",\n                            fingerprint,\n                            ttl: 0,\n                            state: \"RUN\",\n                            conclusion: \"ERROR\",\n                            reason: new ArcjetErrorReason(\"rule result missing\"),\n                        });\n                    }\n                    log.debug({\n                        id: results[idx].ruleId,\n                        rule: rule.type,\n                        fingerprint,\n                        path: details.path,\n                        runtime: rt,\n                        ttl: results[idx].ttl,\n                        conclusion: results[idx].conclusion,\n                        reason: results[idx].reason,\n                    }, \"Local rule result:\");\n                }\n                catch (err) {\n                    log.error(\"Failure running rule: %s due to %s\", rule.type, errorMessage(err));\n                    results[idx] = new ArcjetRuleResult({\n                        // TODO(#4030): Figure out if we can get a Rule ID in this error case\n                        ruleId: \"\",\n                        fingerprint,\n                        ttl: 0,\n                        state: \"RUN\",\n                        conclusion: \"ERROR\",\n                        reason: new ArcjetErrorReason(err),\n                    });\n                }\n                finally {\n                    logRulePerf();\n                }\n                const result = results[idx];\n                if (result.isDenied()) {\n                    // If the rule is not a DRY_RUN, we want to cache non-zero TTL results\n                    // and return a DENY decision.\n                    if (result.state !== \"DRY_RUN\") {\n                        const decision = new ArcjetDenyDecision({\n                            ttl: result.ttl,\n                            reason: result.reason,\n                            results,\n                        });\n                        // Only a DENY decision is reported to avoid creating 2 entries for\n                        // a request. Upon ALLOW, the `decide` call will create an entry for\n                        // the request.\n                        client.report(context, details, decision, rules);\n                        if (result.ttl > 0) {\n                            log.debug({\n                                fingerprint: result.fingerprint,\n                                conclusion: result.conclusion,\n                                reason: result.reason,\n                            }, \"Caching decision for %d seconds\", decision.ttl);\n                            cache.set(result.ruleId, result.fingerprint, {\n                                conclusion: result.conclusion,\n                                reason: result.reason,\n                            }, result.ttl);\n                        }\n                        return decision;\n                    }\n                    log.warn(`Dry run mode is enabled for \"%s\" rule. Overriding decision. Decision was: DENY`, rule.type);\n                }\n            }\n        }\n        finally {\n            logLocalPerf();\n        }\n        // With no cached values, we take a decision remotely. We use a timeout to\n        // fail open.\n        const logRemotePerf = perf.measure(\"remote\");\n        try {\n            const logDediceApiPerf = perf.measure(\"decideApi\");\n            const decision = await client\n                .decide(context, details, rules)\n                .finally(() => {\n                logDediceApiPerf();\n            });\n            // If the decision is to block and we have a non-zero TTL, we cache the\n            // block locally\n            if (decision.isDenied() && decision.ttl > 0) {\n                log.debug(\"decide: Caching block locally for %d seconds\", decision.ttl);\n                for (const result of decision.results) {\n                    // Cache all DENY results for local cache lookups\n                    if (result.conclusion === \"DENY\") {\n                        cache.set(result.ruleId, result.fingerprint, {\n                            conclusion: result.conclusion,\n                            reason: result.reason,\n                        }, result.ttl);\n                    }\n                }\n            }\n            return decision;\n        }\n        catch (err) {\n            log.error(\"Encountered problem getting remote decision: %s\", errorMessage(err));\n            const decision = new ArcjetErrorDecision({\n                ttl: 0,\n                reason: new ArcjetErrorReason(err),\n                results,\n            });\n            client.report(context, details, decision, rules);\n            return decision;\n        }\n        finally {\n            logRemotePerf();\n        }\n    }\n    // This is a separate function so it can be called recursively\n    function withRule(baseRules, rule) {\n        const rules = [...baseRules, ...rule].sort((a, b) => a.priority - b.priority);\n        return Object.freeze({\n            withRule(rule) {\n                return withRule(rules, rule);\n            },\n            async protect(ctx, request) {\n                return protect(rules, ctx, request);\n            },\n        });\n    }\n    return Object.freeze({\n        withRule(rule) {\n            return withRule(rootRules, rule);\n        },\n        async protect(ctx, request) {\n            return protect(rootRules, ctx, request);\n        },\n    });\n}\n\nexport { arcjet as default, detectBot, fixedWindow, protectSignup, sensitiveInfo, shield, slidingWindow, tokenBucket, validateEmail };\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;;;;;;;;;;;AAEA,SAAS,OAAO,SAAS,EAAE,GAAG;IAC1B,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,MAAM;IACpB;AACJ;AACA,SAAS,aAAa,GAAG;IACrB,IAAI,KAAK;QACL,IAAI,OAAO,QAAQ,UAAU;YACzB,OAAO;QACX;QACA,IAAI,OAAO,QAAQ,YACf,aAAa,OACb,OAAO,IAAI,OAAO,KAAK,UAAU;YACjC,OAAO,IAAI,OAAO;QACtB;IACJ;IACA,OAAO;AACX;AACA,MAAM,cAAc;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD,SAAS,yBAAyB,GAAG;IACjC,OAAO,CAAC,YAAY,QAAQ,CAAC;AACjC;AACA,SAAS,YAAY,IAAI;IACrB,OAAQ,SAAS,UACb,SAAS,gBACT,SAAS,mBACT,SAAS,iBACT,SAAS;AACjB;AACA,MAAM;IACF,IAAI;IACJ,YAAY,MAAM,CAAE;QAChB,IAAI,CAAC,GAAG,GAAG;IACf;IACA,+EAA+E;IAC/E,QAAQ,KAAK,EAAE;QACX,MAAM,QAAQ,YAAY,GAAG;QAC7B,OAAO;YACH,MAAM,MAAM,YAAY,GAAG;YAC3B,MAAM,OAAO,MAAM;YACnB,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,oBAAoB,OAAO,KAAK,OAAO,CAAC;QAC3D;IACJ;AACJ;AACA,SAAS,SAAS,KAAK;IACnB,IAAI,OAAO,UAAU,UAAU;QAC3B,OAAO;IACX;IACA,IAAI,OAAO,UAAU,UAAU;QAC3B,OAAO,GAAG,OAAO;IACrB;IACA,IAAI,OAAO,UAAU,WAAW;QAC5B,OAAO,QAAQ,SAAS;IAC5B;IACA,OAAO;AACX;AACA,+EAA+E;AAC/E,+EAA+E;AAC/E,0HAA0H;AAC1H,MAAM,yBAAyB,OAAO,GAAG,CAAC;AAC1C,SAAS;IACL,MAAM,aAAa;IACnB,IAAI,OAAO,UAAU,CAAC,uBAAuB,KAAK,YAC9C,UAAU,CAAC,uBAAuB,KAAK,QACvC,SAAS,UAAU,CAAC,uBAAuB,IAC3C,OAAO,UAAU,CAAC,uBAAuB,CAAC,GAAG,KAAK,YAAY;QAC9D,MAAM,YAAY,UAAU,CAAC,uBAAuB,CAAC,GAAG;QACxD,IAAI,OAAO,cAAc,YACrB,cAAc,QACd,eAAe,aACf,OAAO,UAAU,SAAS,KAAK,YAAY;YAC3C,OAAO,UAAU,SAAS;QAC9B;IACJ;AACJ;AACA,SAAS,iBAAiB,OAAO;IAC7B,MAAM,UAAU,CAAC;IACjB,IAAI,OAAO,QAAQ,OAAO,KAAK,aAAa;QACxC,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,QAAQ,OAAO,CAAC,OAAO,GAAI;YAClD,OAAO,CAAC,IAAI,GAAG;QACnB;IACJ;IACA,OAAO;QACH,GAAG,OAAO;QACV;IACJ;AACJ;AACA,SAAS,WAAW,OAAO;IACvB,MAAM,QAAQ,IAAI;IAClB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,SAAU;QAChD,IAAI,yBAAyB,MAAM;YAC/B,MAAM,GAAG,CAAC,KAAK,SAAS;QAC5B;IACJ;IACA,OAAO,OAAO,WAAW,CAAC,MAAM,OAAO;AAC3C;AACA,SAAS,oBAAoB,GAAG,KAAK;IACjC,OAAO,CAAC,KAAK;QACT,MAAM,cAAc,OAAO;QAC3B,IAAI,CAAC,MAAM,QAAQ,CAAC,cAAc;YAC9B,IAAI,MAAM,MAAM,KAAK,GAAG;gBACpB,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,IAAI,cAAc,EAAE,KAAK,CAAC,EAAE,EAAE;YACxE,OACK;gBACD,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,IAAI,qBAAqB,EAAE,MAAM,IAAI,CAAC,OAAO;YACvF;QACJ,OACK;YACD,OAAO;QACX;IACJ;AACJ;AACA,SAAS,qBACT,sDAAsD;AACtD,GAAG,MAAM;IACL,OAAO,CAAC,KAAK;QACT,wEAAwE;QACxE,sDAAsD;QACtD,IAAI,CAAC,OAAO,QAAQ,CAAC,QAAQ;YACzB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,IAAI,qBAAqB,EAAE,OAAO,GAAG,CAAC,CAAC,QAAU,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO;QACtH;IACJ;AACJ;AACA,SAAS,qBAAqB,QAAQ;IAClC,OAAO,CAAC,KAAK;QACT,IAAI,MAAM,OAAO,CAAC,QAAQ;YACtB,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,MAAM,OAAO,GAAI;gBACvC,SAAS,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE;YAC/B;QACJ,OACK;YACD,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,IAAI,sBAAsB,CAAC;QACrE;IACJ;AACJ;AACA,SAAS,gBAAgB,EAAE,IAAI,EAAE,WAAW,EAAG;IAC3C,OAAO,CAAC;QACJ,KAAK,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,YAAa;YACnD,IAAI,YAAY,CAAC,OAAO,MAAM,CAAC,SAAS,MAAM;gBAC1C,MAAM,IAAI,MAAM,CAAC,EAAE,EAAE,KAAK,oBAAoB,EAAE,IAAI,cAAc,CAAC;YACvE;YACA,MAAM,QAAQ,OAAO,CAAC,IAAI;YAC1B,0EAA0E;YAC1E,iCAAiC;YACjC,IAAI,OAAO,UAAU,aAAa;gBAC9B,IAAI;oBACA,SAAS,KAAK;gBAClB,EACA,OAAO,KAAK;oBACR,MAAM,IAAI,MAAM,CAAC,EAAE,EAAE,KAAK,kBAAkB,EAAE,aAAa,MAAM;gBACrE;YACJ;QACJ;IACJ;AACJ;AACA,MAAM,iBAAiB,oBAAoB;AAC3C,MAAM,iBAAiB,oBAAoB;AAC3C,MAAM,kBAAkB,oBAAoB;AAC5C,MAAM,mBAAmB,oBAAoB;AAC7C,MAAM,yBAAyB,oBAAoB,UAAU;AAC7D,MAAM,sBAAsB,qBAAqB;AACjD,MAAM,eAAe,qBAAqB,QAAQ;AAClD,MAAM,qBAAqB,qBAAqB,qBAAqB,cAAc,QAAQ,iBAAiB,eAAe;AAC3H,MAAM,6BAA6B,gBAAgB;IAC/C,MAAM;IACN,aAAa;QACT;YACI,KAAK;YACL,UAAU;YACV,UAAU;QACd;QACA;YACI,KAAK;YACL,UAAU;YACV,UAAU;QACd;QACA;YAAE,KAAK;YAAc,UAAU;YAAM,UAAU;QAAe;QAC9D;YAAE,KAAK;YAAY,UAAU;YAAM,UAAU;QAAuB;QACpE;YAAE,KAAK;YAAY,UAAU;YAAM,UAAU;QAAe;KAC/D;AACL;AACA,MAAM,6BAA6B,gBAAgB;IAC/C,MAAM;IACN,aAAa;QACT;YAAE,KAAK;YAAQ,UAAU;YAAO,UAAU;QAAa;QACvD;YACI,KAAK;YACL,UAAU;YACV,UAAU;QACd;QACA;YAAE,KAAK;YAAO,UAAU;YAAM,UAAU;QAAe;QACvD;YAAE,KAAK;YAAU,UAAU;YAAM,UAAU;QAAuB;KACrE;AACL;AACA,MAAM,+BAA+B,gBAAgB;IACjD,MAAM;IACN,aAAa;QACT;YAAE,KAAK;YAAQ,UAAU;YAAO,UAAU;QAAa;QACvD;YACI,KAAK;YACL,UAAU;YACV,UAAU;QACd;QACA;YAAE,KAAK;YAAO,UAAU;YAAM,UAAU;QAAe;QACvD;YAAE,KAAK;YAAY,UAAU;YAAM,UAAU;QAAuB;KACvE;AACL;AACA,MAAM,+BAA+B,gBAAgB;IACjD,MAAM;IACN,aAAa;QACT;YAAE,KAAK;YAAQ,UAAU;YAAO,UAAU;QAAa;QACvD;YAAE,KAAK;YAAS,UAAU;YAAO,UAAU;QAAoB;QAC/D;YAAE,KAAK;YAAQ,UAAU;YAAO,UAAU;QAAoB;QAC9D;YAAE,KAAK;YAAqB,UAAU;YAAO,UAAU;QAAe;QACtE;YAAE,KAAK;YAAU,UAAU;YAAO,UAAU;QAAiB;KAChE;AACL;AACA,MAAM,uBAAuB,gBAAgB;IACzC,MAAM;IACN,aAAa;QACT;YAAE,KAAK;YAAQ,UAAU;YAAO,UAAU;QAAa;QACvD;YAAE,KAAK;YAAS,UAAU;YAAO,UAAU;QAAmB;QAC9D;YAAE,KAAK;YAAS,UAAU;YAAO,UAAU;QAAmB;QAC9D;YAAE,KAAK;YAAQ,UAAU;YAAO,UAAU;QAAmB;QAC7D;YACI,KAAK;YACL,UAAU;YACV,UAAU;QACd;QACA;YAAE,KAAK;YAAsB,UAAU;YAAO,UAAU;QAAgB;KAC3E;AACL;AACA,MAAM,qBAAqB,gBAAgB;IACvC,MAAM;IACN,aAAa;QACT;YAAE,KAAK;YAAQ,UAAU;YAAO,UAAU;QAAa;QACvD;YAAE,KAAK;YAAS,UAAU;YAAO,UAAU;QAAoB;QAC/D;YAAE,KAAK;YAAQ,UAAU;YAAO,UAAU;QAAoB;KACjE;AACL;AACA,MAAM,wBAAwB,gBAAgB;IAC1C,MAAM;IACN,aAAa;QAAC;YAAE,KAAK;YAAQ,UAAU;YAAO,UAAU;QAAa;KAAE;AAC3E;AACA,MAAM,WAAW;IACb,eAAe;IACf,QAAQ;IACR,WAAW;IACX,cAAc;IACd,iBAAiB;AACrB;AACA,SAAS,gBAAgB,IAAI;IACzB,OAAO,KAAK,IAAI,KAAK;AACzB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAwDC,GACD,SAAS,YAAY,OAAO;IACxB,2BAA2B;IAC3B,MAAM,OAAO;IACb,MAAM,UAAU;IAChB,MAAM,OAAO,QAAQ,IAAI,KAAK,SAAS,SAAS;IAChD,MAAM,kBAAkB,MAAM,OAAO,CAAC,QAAQ,eAAe,IACvD,QAAQ,eAAe,GACvB;IACN,MAAM,aAAa,QAAQ,UAAU;IACrC,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,QAAc,AAAD,EAAE,QAAQ,QAAQ;IAChD,MAAM,WAAW,QAAQ,QAAQ;IACjC,MAAM,OAAO;QACT;QACA;QACA,UAAU,SAAS,SAAS;QAC5B;QACA;QACA,WAAW;QACX;QACA;QACA;QACA,aAAa;QACb,MAAM,SAAQ,OAAO,EAAE,OAAO;YAC1B,MAAM,uBAAuB,mBAAmB,QAAQ,eAAe;YACvE,MAAM,SAAS,MAAM,CAAA,GAAA,mLAAA,CAAA,OAAW,AAAD,EAAE,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,QAAQ,OAAO,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,WAAW,UAAU,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,QAAQ,OAAO,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,aAAa,iBAAiB,CAAA,GAAA,4JAAA,CAAA,qBAAyB,AAAD,EAAE,mBAAmB,uBACvN,yEAAyE;YACzE,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,SAAS,KAAK,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,cAAc,aAAa,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,YAAY,WAAW,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,YAAY;YACpI,MAAM,iBAAiB;gBACnB,iBAAiB;gBACjB,KAAK,QAAQ,GAAG;YACpB;YACA,MAAM,cAAc,MAAM,CAAA,GAAA,oJAAA,CAAA,sBAA2B,AAAD,EAAE,gBAAgB,iBAAiB;YACvF,MAAM,CAAC,QAAQ,IAAI,GAAG,MAAM,QAAQ,KAAK,CAAC,GAAG,CAAC,QAAQ;YACtD,IAAI,UAAU,OAAO,MAAM,CAAC,WAAW,IAAI;gBACvC,OAAO,IAAI,qKAAA,CAAA,mBAAgB,CAAC;oBACxB;oBACA;oBACA;oBACA,OAAO;oBACP,YAAY,OAAO,UAAU;oBAC7B,mEAAmE;oBACnE,gDAAgD;oBAChD,QAAQ,IAAI,qKAAA,CAAA,wBAAqB,CAAC;wBAC9B,KAAK,OAAO,MAAM,CAAC,GAAG;wBACtB,WAAW,OAAO,MAAM,CAAC,SAAS;wBAClC,OAAO;wBACP,QAAQ,OAAO,MAAM,CAAC,MAAM;wBAC5B,WAAW,OAAO,MAAM,CAAC,SAAS;oBACtC;gBACJ;YACJ;YACA,OAAO,IAAI,qKAAA,CAAA,mBAAgB,CAAC;gBACxB;gBACA;gBACA,KAAK;gBACL,OAAO;gBACP,YAAY;gBACZ,QAAQ,IAAI,qKAAA,CAAA,wBAAqB,CAAC;oBAC9B,KAAK;oBACL,WAAW;oBACX,OAAO;oBACP,QAAQ;oBACR,WAAW,IAAI;gBACnB;YACJ;QACJ;IACJ;IACA,OAAO;QAAC;KAAK;AACjB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAsDC,GACD,SAAS,YAAY,OAAO;IACxB,2BAA2B;IAC3B,MAAM,OAAO;IACb,MAAM,UAAU;IAChB,MAAM,OAAO,QAAQ,IAAI,KAAK,SAAS,SAAS;IAChD,MAAM,kBAAkB,MAAM,OAAO,CAAC,QAAQ,eAAe,IACvD,QAAQ,eAAe,GACvB;IACN,MAAM,MAAM,QAAQ,GAAG;IACvB,MAAM,SAAS,CAAA,GAAA,qJAAA,CAAA,QAAc,AAAD,EAAE,QAAQ,MAAM;IAC5C,MAAM,OAAO;QACT;QACA;QACA,UAAU,SAAS,SAAS;QAC5B;QACA;QACA,WAAW;QACX;QACA;QACA,aAAa;QACb,MAAM,SAAQ,OAAO,EAAE,OAAO;YAC1B,MAAM,uBAAuB,mBAAmB,QAAQ,eAAe;YACvE,MAAM,SAAS,MAAM,CAAA,GAAA,mLAAA,CAAA,OAAW,AAAD,EAAE,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,QAAQ,OAAO,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,WAAW,UAAU,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,QAAQ,OAAO,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,aAAa,iBAAiB,CAAA,GAAA,4JAAA,CAAA,qBAAyB,AAAD,EAAE,mBAAmB,uBACvN,yEAAyE;YACzE,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,SAAS,KAAK,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,OAAO,MAAM,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,UAAU;YAC/E,MAAM,iBAAiB;gBACnB,iBAAiB;gBACjB,KAAK,QAAQ,GAAG;YACpB;YACA,MAAM,cAAc,MAAM,CAAA,GAAA,oJAAA,CAAA,sBAA2B,AAAD,EAAE,gBAAgB,iBAAiB;YACvF,MAAM,CAAC,QAAQ,IAAI,GAAG,MAAM,QAAQ,KAAK,CAAC,GAAG,CAAC,QAAQ;YACtD,IAAI,UAAU,OAAO,MAAM,CAAC,WAAW,IAAI;gBACvC,OAAO,IAAI,qKAAA,CAAA,mBAAgB,CAAC;oBACxB;oBACA;oBACA;oBACA,OAAO;oBACP,YAAY,OAAO,UAAU;oBAC7B,mEAAmE;oBACnE,gDAAgD;oBAChD,QAAQ,IAAI,qKAAA,CAAA,wBAAqB,CAAC;wBAC9B,KAAK,OAAO,MAAM,CAAC,GAAG;wBACtB,WAAW,OAAO,MAAM,CAAC,SAAS;wBAClC,OAAO;wBACP,QAAQ,OAAO,MAAM,CAAC,MAAM;wBAC5B,WAAW,OAAO,MAAM,CAAC,SAAS;oBACtC;gBACJ;YACJ;YACA,OAAO,IAAI,qKAAA,CAAA,mBAAgB,CAAC;gBACxB;gBACA;gBACA,KAAK;gBACL,OAAO;gBACP,YAAY;gBACZ,QAAQ,IAAI,qKAAA,CAAA,wBAAqB,CAAC;oBAC9B,KAAK;oBACL,WAAW;oBACX,OAAO;oBACP,QAAQ;gBACZ;YACJ;QACJ;IACJ;IACA,OAAO;QAAC;KAAK;AACjB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAiDC,GACD,SAAS,cAAc,OAAO;IAC1B,6BAA6B;IAC7B,MAAM,OAAO;IACb,MAAM,UAAU;IAChB,MAAM,OAAO,QAAQ,IAAI,KAAK,SAAS,SAAS;IAChD,MAAM,kBAAkB,MAAM,OAAO,CAAC,QAAQ,eAAe,IACvD,QAAQ,eAAe,GACvB;IACN,MAAM,MAAM,QAAQ,GAAG;IACvB,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,QAAc,AAAD,EAAE,QAAQ,QAAQ;IAChD,MAAM,OAAO;QACT;QACA;QACA,UAAU,SAAS,SAAS;QAC5B;QACA;QACA,WAAW;QACX;QACA;QACA,aAAa;QACb,MAAM,SAAQ,OAAO,EAAE,OAAO;YAC1B,MAAM,uBAAuB,mBAAmB,QAAQ,eAAe;YACvE,MAAM,SAAS,MAAM,CAAA,GAAA,mLAAA,CAAA,OAAW,AAAD,EAAE,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,QAAQ,OAAO,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,WAAW,UAAU,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,QAAQ,OAAO,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,aAAa,mBAAmB,CAAA,GAAA,4JAAA,CAAA,qBAAyB,AAAD,EAAE,mBAAmB,uBACzN,yEAAyE;YACzE,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,SAAS,KAAK,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,OAAO,MAAM,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,YAAY;YACjF,MAAM,iBAAiB;gBACnB,iBAAiB;gBACjB,KAAK,QAAQ,GAAG;YACpB;YACA,MAAM,cAAc,MAAM,CAAA,GAAA,oJAAA,CAAA,sBAA2B,AAAD,EAAE,gBAAgB,iBAAiB;YACvF,MAAM,CAAC,QAAQ,IAAI,GAAG,MAAM,QAAQ,KAAK,CAAC,GAAG,CAAC,QAAQ;YACtD,IAAI,UAAU,OAAO,MAAM,CAAC,WAAW,IAAI;gBACvC,OAAO,IAAI,qKAAA,CAAA,mBAAgB,CAAC;oBACxB;oBACA;oBACA;oBACA,OAAO;oBACP,YAAY,OAAO,UAAU;oBAC7B,mEAAmE;oBACnE,gDAAgD;oBAChD,QAAQ,IAAI,qKAAA,CAAA,wBAAqB,CAAC;wBAC9B,KAAK,OAAO,MAAM,CAAC,GAAG;wBACtB,WAAW,OAAO,MAAM,CAAC,SAAS;wBAClC,OAAO;wBACP,QAAQ,OAAO,MAAM,CAAC,MAAM;wBAC5B,WAAW,OAAO,MAAM,CAAC,SAAS;oBACtC;gBACJ;YACJ;YACA,OAAO,IAAI,qKAAA,CAAA,mBAAgB,CAAC;gBACxB;gBACA;gBACA,KAAK;gBACL,OAAO;gBACP,YAAY;gBACZ,QAAQ,IAAI,qKAAA,CAAA,wBAAqB,CAAC;oBAC9B,KAAK;oBACL,WAAW;oBACX,OAAO;oBACP,QAAQ;gBACZ;YACJ;QACJ;IACJ;IACA,OAAO;QAAC;KAAK;AACjB;AACA,SAAS,uCAAuC,MAAM;IAClD,IAAI,OAAO,WAAW,UAAU;QAC5B,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,WAAW,SAAS;QACpB,OAAO;YAAE,KAAK;QAAQ;IAC1B;IACA,IAAI,WAAW,gBAAgB;QAC3B,OAAO;YAAE,KAAK;QAAe;IACjC;IACA,IAAI,WAAW,cAAc;QACzB,OAAO;YAAE,KAAK;QAAa;IAC/B;IACA,IAAI,WAAW,sBAAsB;QACjC,OAAO;YAAE,KAAK;QAAqB;IACvC;IACA,OAAO;QACH,KAAK;QACL,KAAK;IACT;AACJ;AACA,SAAS,qCAAqC,MAAM;IAChD,IAAI,OAAO,GAAG,KAAK,SAAS;QACxB,OAAO;IACX;IACA,IAAI,OAAO,GAAG,KAAK,cAAc;QAC7B,OAAO;IACX;IACA,IAAI,OAAO,GAAG,KAAK,sBAAsB;QACrC,OAAO;IACX;IACA,IAAI,OAAO,GAAG,KAAK,gBAAgB;QAC/B,OAAO;IACX;IACA,OAAO,OAAO,GAAG;AACrB;AACA,SAAS,0CAA0C,gBAAgB;IAC/D,OAAO,iBAAiB,GAAG,CAAC,CAAC;QACzB,OAAO;YACH,GAAG,cAAc;YACjB,gBAAgB,qCAAqC,eAAe,cAAc;QACtF;IACJ;AACJ;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAmFC,GACD,SAAS,cAAc,OAAO;IAC1B,6BAA6B;IAC7B,IAAI,OAAO,QAAQ,KAAK,KAAK,eACzB,OAAO,QAAQ,IAAI,KAAK,aAAa;QACrC,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,OAAO,QAAQ,KAAK,KAAK,eACzB,OAAO,QAAQ,IAAI,KAAK,aAAa;QACrC,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,OAAO;IACb,MAAM,UAAU;IAChB,MAAM,OAAO,QAAQ,IAAI,KAAK,SAAS,SAAS;IAChD,MAAM,QAAQ,QAAQ,KAAK,IAAI,EAAE;IACjC,MAAM,OAAO,QAAQ,IAAI,IAAI,EAAE;IAC/B,MAAM,OAAO;QACT;QACA,UAAU,SAAS,aAAa;QAChC;QACA;QACA;QACA;QACA,UAAS,OAAO,EAAE,OAAO,GAAI;QAC7B,MAAM,SAAQ,OAAO,EAAE,OAAO;YAC1B,MAAM,SAAS,MAAM,CAAA,GAAA,mLAAA,CAAA,OAAW,AAAD,EAAE,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,QAAQ,OAAO,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,WAAW,UAAU,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,QAAQ,OAAO,CAAA,GAAA,4JAAA,CAAA,qBAAyB,AAAD,EAAE,SAAS,QAAQ,CAAA,GAAA,4JAAA,CAAA,qBAAyB,AAAD,EAAE,QAAQ;YAC3M,MAAM,EAAE,WAAW,EAAE,GAAG;YACxB,uEAAuE;YACvE,uEAAuE;YACvE,yCAAyC;YACzC,MAAM,OAAO,MAAM,QAAQ,OAAO;YAClC,IAAI,OAAO,SAAS,aAAa;gBAC7B,OAAO,IAAI,qKAAA,CAAA,mBAAgB,CAAC;oBACxB;oBACA;oBACA,KAAK;oBACL,OAAO;oBACP,YAAY;oBACZ,QAAQ,IAAI,qKAAA,CAAA,oBAAiB,CAAC;gBAClC;YACJ;YACA,IAAI,kBAAkB;YACtB,IAAI,OAAO,QAAQ,MAAM,KAAK,aAAa;gBACvC,MAAM,SAAS,QAAQ,MAAM;gBAC7B,kBAAkB,CAAC;oBACf,OAAO,OAAO,QACT,MAAM,CAAC,CAAC,IAAM,OAAO,MAAM,aAC3B,GAAG,CAAC;gBACb;YACJ;YACA,IAAI,cAAc;YAClB,IAAI,cAAc,EAAE;YACpB,IAAI,MAAM,OAAO,CAAC,QAAQ,KAAK,GAAG;gBAC9B,cAAc;gBACd,cAAc,QAAQ,KAAK,CACtB,MAAM,CAAC,CAAC,IAAM,OAAO,MAAM,aAC3B,GAAG,CAAC;YACb;YACA,IAAI,MAAM,OAAO,CAAC,QAAQ,IAAI,GAAG;gBAC7B,cAAc;gBACd,cAAc,QAAQ,IAAI,CACrB,MAAM,CAAC,CAAC,IAAM,OAAO,MAAM,aAC3B,GAAG,CAAC;YACb;YACA,MAAM,WAAW;gBACb,KAAK;gBACL,KAAK;YACT;YACA,MAAM,SAAS,MAAM,CAAA,GAAA,oJAAA,CAAA,sBAA2B,AAAD,EAAE,SAAS,MAAM,UAAU,QAAQ,iBAAiB,IAAI,GAAG;YAC1G,MAAM,QAAQ,SAAS,SAAS,QAAQ;YACxC,MAAM,SAAS,IAAI,qKAAA,CAAA,4BAAyB,CAAC;gBACzC,QAAQ,0CAA0C,OAAO,MAAM;gBAC/D,SAAS,0CAA0C,OAAO,OAAO;YACrE;YACA,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,GAAG;gBAC5B,OAAO,IAAI,qKAAA,CAAA,mBAAgB,CAAC;oBACxB;oBACA;oBACA,KAAK;oBACL;oBACA,YAAY;oBACZ;gBACJ;YACJ,OACK;gBACD,OAAO,IAAI,qKAAA,CAAA,mBAAgB,CAAC;oBACxB;oBACA;oBACA,KAAK;oBACL;oBACA,YAAY;oBACZ;gBACJ;YACJ;QACJ;IACJ;IACA,OAAO;QAAC;KAAK;AACjB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAmDC,GACD,SAAS,cAAc,OAAO;IAC1B,qBAAqB;IACrB,IAAI,OAAO,QAAQ,KAAK,KAAK,eACzB,OAAO,QAAQ,IAAI,KAAK,aAAa;QACrC,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,OAAO,QAAQ,KAAK,KAAK,eACzB,OAAO,QAAQ,KAAK,KAAK,aAAa;QACtC,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,OAAO,QAAQ,IAAI,KAAK,eACxB,OAAO,QAAQ,KAAK,KAAK,aAAa;QACtC,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,OAAO,QAAQ,KAAK,KAAK,eACzB,OAAO,QAAQ,IAAI,KAAK,eACxB,OAAO,QAAQ,KAAK,KAAK,aAAa;QACtC,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,OAAO;IACb,MAAM,UAAU;IAChB,MAAM,OAAO,QAAQ,IAAI,KAAK,SAAS,SAAS;IAChD,MAAM,QAAQ,QAAQ,KAAK,IAAI,EAAE;IACjC,MAAM,OAAO,QAAQ,IAAI,IAAI,QAAQ,KAAK,IAAI,EAAE;IAChD,MAAM,wBAAwB,QAAQ,qBAAqB,IAAI;IAC/D,MAAM,qBAAqB,QAAQ,kBAAkB,IAAI;IACzD,IAAI,SAAS;QACT,KAAK;QACL,KAAK;YACD;YACA;YACA,MAAM,EAAE;QACZ;IACJ;IACA,IAAI,OAAO,QAAQ,KAAK,KAAK,aAAa;QACtC,SAAS;YACL,KAAK;YACL,KAAK;gBACD;gBACA;gBACA,OAAO,QAAQ,KAAK;YACxB;QACJ;IACJ;IACA,IAAI,OAAO,QAAQ,IAAI,KAAK,aAAa;QACrC,SAAS;YACL,KAAK;YACL,KAAK;gBACD;gBACA;gBACA,MAAM,QAAQ,IAAI;YACtB;QACJ;IACJ;IACA,IAAI,OAAO,QAAQ,KAAK,KAAK,aAAa;QACtC,SAAS;YACL,KAAK;YACL,KAAK;gBACD;gBACA;gBACA,MAAM,QAAQ,KAAK;YACvB;QACJ;IACJ;IACA,MAAM,OAAO;QACT;QACA,UAAU,SAAS,eAAe;QAClC;QACA;QACA;QACA;QACA;QACA;QACA,UAAS,OAAO,EAAE,OAAO;YACrB,OAAO,OAAO,QAAQ,KAAK,KAAK,aAAa;QACjD;QACA,MAAM,SAAQ,OAAO,EAAE,EAAE,KAAK,EAAE;YAC5B,MAAM,SAAS,MAAM,CAAA,GAAA,mLAAA,CAAA,OAAW,AAAD,EAAE,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,QAAQ,OAAO,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,WAAW,UAAU,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,QAAQ,OAAO,CAAA,GAAA,4JAAA,CAAA,qBAAyB,AAAD,EAAE,SAAS,QAAQ,CAAA,GAAA,4JAAA,CAAA,qBAAyB,AAAD,EAAE,QAAQ,OAAO,CAAA,GAAA,4JAAA,CAAA,OAAW,AAAD,EAAE,yBAAyB,wBAAwB,CAAA,GAAA,4JAAA,CAAA,OAAW,AAAD,EAAE,sBAAsB;YACjT,MAAM,EAAE,WAAW,EAAE,GAAG;YACxB,uEAAuE;YACvE,qEAAqE;YACrE,4BAA4B;YAC5B,MAAM,SAAS,MAAM,CAAA,GAAA,oJAAA,CAAA,eAAoB,AAAD,EAAE,SAAS,OAAO;YAC1D,MAAM,QAAQ,SAAS,SAAS,QAAQ;YACxC,IAAI,OAAO,QAAQ,KAAK,SAAS;gBAC7B,OAAO,IAAI,qKAAA,CAAA,mBAAgB,CAAC;oBACxB;oBACA;oBACA,KAAK;oBACL;oBACA,YAAY;oBACZ,QAAQ,IAAI,qKAAA,CAAA,oBAAiB,CAAC;wBAAE,YAAY,EAAE;oBAAC;gBACnD;YACJ,OACK;gBACD,MAAM,kBAAkB,OAAO,OAAO,CAAC,MAAM,CAAC;gBAC9C,OAAO,IAAI,qKAAA,CAAA,mBAAgB,CAAC;oBACxB;oBACA;oBACA,KAAK;oBACL;oBACA,YAAY;oBACZ,QAAQ,IAAI,qKAAA,CAAA,oBAAiB,CAAC;wBAC1B,YAAY;oBAChB;gBACJ;YACJ;QACJ;IACJ;IACA,OAAO;QAAC;KAAK;AACjB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2EC,GACD,SAAS,UAAU,OAAO;IACtB,mBAAmB;IACnB,IAAI,OAAO,QAAQ,KAAK,KAAK,eACzB,OAAO,QAAQ,IAAI,KAAK,aAAa;QACrC,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,OAAO,QAAQ,KAAK,KAAK,eACzB,OAAO,QAAQ,IAAI,KAAK,aAAa;QACrC,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,OAAO;IACb,MAAM,UAAU;IAChB,MAAM,OAAO,QAAQ,IAAI,KAAK,SAAS,SAAS;IAChD,MAAM,QAAQ,QAAQ,KAAK,IAAI,EAAE;IACjC,MAAM,OAAO,QAAQ,IAAI,IAAI,EAAE;IAC/B,IAAI,SAAS;QACT,KAAK;QACL,KAAK;YACD,UAAU,EAAE;YACZ,kBAAkB;QACtB;IACJ;IACA,IAAI,OAAO,QAAQ,KAAK,KAAK,aAAa;QACtC,SAAS;YACL,KAAK;YACL,KAAK;gBACD,UAAU,QAAQ,KAAK;gBACvB,kBAAkB;YACtB;QACJ;IACJ;IACA,IAAI,OAAO,QAAQ,IAAI,KAAK,aAAa;QACrC,SAAS;YACL,KAAK;YACL,KAAK;gBACD,UAAU,QAAQ,IAAI;gBACtB,kBAAkB;YACtB;QACJ;IACJ;IACA,MAAM,OAAO;QACT;QACA,UAAU,SAAS,YAAY;QAC/B;QACA;QACA;QACA;QACA,UAAS,OAAO,EAAE,OAAO;YACrB,IAAI,OAAO,QAAQ,OAAO,KAAK,aAAa;gBACxC,MAAM,IAAI,MAAM;YACpB;YACA,IAAI,OAAO,QAAQ,OAAO,CAAC,GAAG,KAAK,YAAY;gBAC3C,MAAM,IAAI,MAAM;YACpB;YACA,IAAI,CAAC,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;gBACpC,MAAM,IAAI,MAAM;YACpB;QACJ;QACA;;SAEC,GACD,MAAM,SAAQ,OAAO,EAAE,OAAO;YAC1B,MAAM,SAAS,MAAM,CAAA,GAAA,mLAAA,CAAA,OAAW,AAAD,EAAE,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,QAAQ,OAAO,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,WAAW,UAAU,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,QAAQ,OAAO,CAAA,GAAA,4JAAA,CAAA,qBAAyB,AAAD,EAAE,SAAS,QAAQ,CAAA,GAAA,4JAAA,CAAA,qBAAyB,AAAD,EAAE,QAAQ;YAC3M,MAAM,EAAE,WAAW,EAAE,GAAG;YACxB,MAAM,CAAC,QAAQ,IAAI,GAAG,MAAM,QAAQ,KAAK,CAAC,GAAG,CAAC,QAAQ;YACtD,IAAI,QAAQ;gBACR,OAAO,IAAI,qKAAA,CAAA,mBAAgB,CAAC;oBACxB;oBACA;oBACA;oBACA,OAAO;oBACP,YAAY,OAAO,UAAU;oBAC7B,QAAQ,OAAO,MAAM;gBACzB;YACJ;YACA,MAAM,SAAS,MAAM,CAAA,GAAA,oJAAA,CAAA,YAAiB,AAAD,EAAE,SAAS,iBAAiB,UAAU;YAC3E,MAAM,QAAQ,SAAS,SAAS,QAAQ;YACxC,oEAAoE;YACpE,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,GAAG;gBAC1B,OAAO,IAAI,qKAAA,CAAA,mBAAgB,CAAC;oBACxB;oBACA;oBACA,KAAK;oBACL;oBACA,YAAY;oBACZ,QAAQ,IAAI,qKAAA,CAAA,kBAAe,CAAC;wBACxB,SAAS,OAAO,OAAO;wBACvB,QAAQ,OAAO,MAAM;wBACrB,UAAU,OAAO,QAAQ;wBACzB,SAAS,OAAO,OAAO;oBAC3B;gBACJ;YACJ,OACK;gBACD,OAAO,IAAI,qKAAA,CAAA,mBAAgB,CAAC;oBACxB;oBACA;oBACA,KAAK;oBACL;oBACA,YAAY;oBACZ,QAAQ,IAAI,qKAAA,CAAA,kBAAe,CAAC;wBACxB,SAAS,OAAO,OAAO;wBACvB,QAAQ,OAAO,MAAM;wBACrB,UAAU,OAAO,QAAQ;wBACzB,SAAS,OAAO,OAAO;oBAC3B;gBACJ;YACJ;QACJ;IACJ;IACA,OAAO;QAAC;KAAK;AACjB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA6BC,GACD,SAAS,OAAO,OAAO;IACnB,sBAAsB;IACtB,MAAM,OAAO;IACb,MAAM,UAAU;IAChB,MAAM,OAAO,QAAQ,IAAI,KAAK,SAAS,SAAS;IAChD,MAAM,OAAO;QACT;QACA;QACA,UAAU,SAAS,MAAM;QACzB;QACA,aAAa;QACb,MAAM,SAAQ,OAAO,EAAE,OAAO;YAC1B,qEAAqE;YACrE,MAAM,uBAAuB,QAAQ,eAAe;YACpD,MAAM,SAAS,MAAM,CAAA,GAAA,mLAAA,CAAA,OAAW,AAAD,EAAE,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,QAAQ,OAAO,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,WAAW,UAAU,CAAA,GAAA,4JAAA,CAAA,SAAa,AAAD,EAAE,QAAQ,OAAO,CAAA,GAAA,4JAAA,CAAA,qBAAyB,AAAD,EAAE,mBAAmB;YAC3K,MAAM,iBAAiB;gBACnB,iBAAiB;gBACjB,KAAK,QAAQ,GAAG;YACpB;YACA,MAAM,cAAc,MAAM,CAAA,GAAA,oJAAA,CAAA,sBAA2B,AAAD,EAAE,gBAAgB,iBAAiB;YACvF,MAAM,CAAC,QAAQ,IAAI,GAAG,MAAM,QAAQ,KAAK,CAAC,GAAG,CAAC,QAAQ;YACtD,IAAI,QAAQ;gBACR,OAAO,IAAI,qKAAA,CAAA,mBAAgB,CAAC;oBACxB;oBACA;oBACA;oBACA,OAAO;oBACP,YAAY,OAAO,UAAU;oBAC7B,QAAQ,OAAO,MAAM;gBACzB;YACJ;YACA,OAAO,IAAI,qKAAA,CAAA,mBAAgB,CAAC;gBACxB;gBACA;gBACA,KAAK;gBACL,OAAO;gBACP,YAAY;gBACZ,QAAQ,IAAI,qKAAA,CAAA,qBAAkB,CAAC;oBAC3B,iBAAiB;gBACrB;YACJ;QACJ;IACJ;IACA,OAAO;QAAC;KAAK;AACjB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA0GC,GACD,SAAS,cAAc,OAAO;IAC1B,OAAO;WACA,cAAc,QAAQ,SAAS;WAC/B,UAAU,QAAQ,IAAI;WACtB,cAAc,QAAQ,KAAK;KACjC;AACL;AACA;;;;CAIC,GACD,SAAS,OAAO,OAAO;IACnB,mFAAmF;IACnF,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG;IACvB,MAAM,KAAK,CAAA,GAAA,4JAAA,CAAA,UAAO,AAAD;IACjB,wDAAwD;IACxD,8EAA8E;IAC9E,IAAI,OAAO,QAAQ,GAAG,KAAK,aAAa;QACpC,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,MAAM,QAAQ,GAAG;IACvB,MAAM,OAAO,IAAI,YAAY;IAC7B,sFAAsF;IACtF,uFAAuF;IACvF,IAAI,OAAO,QAAQ,MAAM,KAAK,aAAa;QACvC,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,SAAS,QAAQ,MAAM;IAC7B,qEAAqE;IACrE,gEAAgE;IAChE,2CAA2C;IAC3C,MAAM,QAAQ,IAAI,kJAAA,CAAA,cAAW;IAC7B,MAAM,YAAY,MACb,IAAI,CAAC,GACL,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;IAC3C,eAAe,QAAQ,KAAK,EAAE,GAAG,EAAE,OAAO;QACtC,oEAAoE;QACpE,uDAAuD;QACvD,IAAI,OAAO,YAAY,aAAa;YAChC,UAAU,CAAC;QACf;QACA,MAAM,UAAU,OAAO,MAAM,CAAC;YAC1B,IAAI,QAAQ,EAAE;YACd,QAAQ,QAAQ,MAAM;YACtB,UAAU,QAAQ,QAAQ;YAC1B,MAAM,QAAQ,IAAI;YAClB,MAAM,QAAQ,IAAI;YAClB,SAAS,IAAI,oJAAA,CAAA,UAAa,CAAC,QAAQ,OAAO;YAC1C,SAAS,QAAQ,OAAO;YACxB,OAAO,QAAQ,KAAK;YACpB,0BAA0B;YAC1B,sBAAsB;YACtB,OAAO,WAAW;YAClB,OAAO,OAAO,QAAQ,KAAK,KAAK,WAAW,QAAQ,KAAK,GAAG;QAC/D;QACA,MAAM,kBAAkB,QAAQ,eAAe,GACzC;eAAI,QAAQ,eAAe;SAAC,GAC5B,EAAE;QACR,MAAM,YAAY;QAClB,MAAM,cAAc;YAChB;YACA;YACA;YACA;YACA,GAAG,GAAG;QACV;QACA,IAAI,cAAc;QAClB,MAAM,qBAAqB,KAAK,OAAO,CAAC;QACxC,IAAI;YACA,cAAc,MAAM,CAAA,GAAA,oJAAA,CAAA,sBAA2B,AAAD,EAAE,aAAa,iBAAiB;YAC9E,IAAI,KAAK,CAAC,wBAAwB,IAAI;QAC1C,EACA,OAAO,OAAO;YACV,IAAI,KAAK,CAAC;gBAAE;YAAM,GAAG;YACrB,MAAM,WAAW,IAAI,qKAAA,CAAA,sBAAmB,CAAC;gBACrC,KAAK;gBACL,QAAQ,IAAI,qKAAA,CAAA,oBAAiB,CAAC,CAAC,8BAA8B,EAAE,aAAa,QAAQ;gBACpF,sDAAsD;gBACtD,SAAS,EAAE;YACf;YACA,+EAA+E;YAC/E,OAAO;QACX,SACQ;YACJ;QACJ;QACA,MAAM,UAAU,OAAO,MAAM,CAAC;YAC1B,GAAG,WAAW;YACd;YACA;YACA,SAAS;QACb;QACA,IAAI,MAAM,MAAM,GAAG,GAAG;YAClB,IAAI,IAAI,CAAC;QACb;QACA,IAAI,MAAM,MAAM,GAAG,IAAI;YACnB,IAAI,KAAK,CAAC;YACV,MAAM,WAAW,IAAI,qKAAA,CAAA,sBAAmB,CAAC;gBACrC,KAAK;gBACL,QAAQ,IAAI,qKAAA,CAAA,oBAAiB,CAAC;gBAC9B,sEAAsE;gBACtE,0CAA0C;gBAC1C,SAAS,EAAE;YACf;YACA,OAAO,MAAM,CAAC,SAAS,SAAS,UAChC,oEAAoE;YACpE,yCAAyC;YACzC,EAAE;YACF,OAAO;QACX;QACA,MAAM,UAAU,EAAE;QAClB,IAAK,IAAI,MAAM,GAAG,MAAM,MAAM,MAAM,EAAE,MAAO;YACzC,2DAA2D;YAC3D,OAAO,CAAC,IAAI,GAAG,IAAI,qKAAA,CAAA,mBAAgB,CAAC;gBAChC,yEAAyE;gBACzE,QAAQ;gBACR;gBACA,KAAK;gBACL,OAAO;gBACP,YAAY;gBACZ,QAAQ,IAAI,qKAAA,CAAA,eAAY;YAC5B;YACA,gFAAgF;YAChF,oCAAoC;YACpC,MAAM,iBAAiB,KAAK,CAAC,IAAI;YACjC,IAAI,gBAAgB,iBAAiB;gBACjC,IAAI,OAAO,eAAe,eAAe,KAAK,aAAa;oBACvD,eAAe,eAAe,GAAG;oBACjC,KAAK,CAAC,IAAI,GAAG;gBACjB;YACJ;QACJ;QACA,MAAM,eAAe,KAAK,OAAO,CAAC;QAClC,IAAI;YACA,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,MAAM,OAAO,GAAI;gBACvC,gEAAgE;gBAChE,6DAA6D;gBAC7D,MAAM,YAAY;gBAClB,MAAM,cAAc,KAAK,OAAO,CAAC,KAAK,IAAI;gBAC1C,IAAI;oBACA,IAAI,OAAO,UAAU,QAAQ,KAAK,YAAY;wBAC1C,MAAM,IAAI,MAAM;oBACpB;oBACA,UAAU,QAAQ,CAAC,SAAS;oBAC5B,IAAI,OAAO,UAAU,OAAO,KAAK,YAAY;wBACzC,MAAM,IAAI,MAAM;oBACpB;oBACA,OAAO,CAAC,IAAI,GAAG,MAAM,UAAU,OAAO,CAAC,SAAS;oBAChD,qEAAqE;oBACrE,oEAAoE;oBACpE,oBAAoB;oBACpB,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,aAAa;wBACrC,OAAO,CAAC,IAAI,GAAG,IAAI,qKAAA,CAAA,mBAAgB,CAAC;4BAChC,+DAA+D;4BAC/D,kBAAkB;4BAClB,QAAQ;4BACR;4BACA,KAAK;4BACL,OAAO;4BACP,YAAY;4BACZ,QAAQ,IAAI,qKAAA,CAAA,oBAAiB,CAAC;wBAClC;oBACJ;oBACA,IAAI,KAAK,CAAC;wBACN,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM;wBACvB,MAAM,KAAK,IAAI;wBACf;wBACA,MAAM,QAAQ,IAAI;wBAClB,SAAS;wBACT,KAAK,OAAO,CAAC,IAAI,CAAC,GAAG;wBACrB,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;wBACnC,QAAQ,OAAO,CAAC,IAAI,CAAC,MAAM;oBAC/B,GAAG;gBACP,EACA,OAAO,KAAK;oBACR,IAAI,KAAK,CAAC,sCAAsC,KAAK,IAAI,EAAE,aAAa;oBACxE,OAAO,CAAC,IAAI,GAAG,IAAI,qKAAA,CAAA,mBAAgB,CAAC;wBAChC,qEAAqE;wBACrE,QAAQ;wBACR;wBACA,KAAK;wBACL,OAAO;wBACP,YAAY;wBACZ,QAAQ,IAAI,qKAAA,CAAA,oBAAiB,CAAC;oBAClC;gBACJ,SACQ;oBACJ;gBACJ;gBACA,MAAM,SAAS,OAAO,CAAC,IAAI;gBAC3B,IAAI,OAAO,QAAQ,IAAI;oBACnB,sEAAsE;oBACtE,8BAA8B;oBAC9B,IAAI,OAAO,KAAK,KAAK,WAAW;wBAC5B,MAAM,WAAW,IAAI,qKAAA,CAAA,qBAAkB,CAAC;4BACpC,KAAK,OAAO,GAAG;4BACf,QAAQ,OAAO,MAAM;4BACrB;wBACJ;wBACA,mEAAmE;wBACnE,oEAAoE;wBACpE,eAAe;wBACf,OAAO,MAAM,CAAC,SAAS,SAAS,UAAU;wBAC1C,IAAI,OAAO,GAAG,GAAG,GAAG;4BAChB,IAAI,KAAK,CAAC;gCACN,aAAa,OAAO,WAAW;gCAC/B,YAAY,OAAO,UAAU;gCAC7B,QAAQ,OAAO,MAAM;4BACzB,GAAG,mCAAmC,SAAS,GAAG;4BAClD,MAAM,GAAG,CAAC,OAAO,MAAM,EAAE,OAAO,WAAW,EAAE;gCACzC,YAAY,OAAO,UAAU;gCAC7B,QAAQ,OAAO,MAAM;4BACzB,GAAG,OAAO,GAAG;wBACjB;wBACA,OAAO;oBACX;oBACA,IAAI,IAAI,CAAC,CAAC,8EAA8E,CAAC,EAAE,KAAK,IAAI;gBACxG;YACJ;QACJ,SACQ;YACJ;QACJ;QACA,0EAA0E;QAC1E,aAAa;QACb,MAAM,gBAAgB,KAAK,OAAO,CAAC;QACnC,IAAI;YACA,MAAM,mBAAmB,KAAK,OAAO,CAAC;YACtC,MAAM,WAAW,MAAM,OAClB,MAAM,CAAC,SAAS,SAAS,OACzB,OAAO,CAAC;gBACT;YACJ;YACA,uEAAuE;YACvE,gBAAgB;YAChB,IAAI,SAAS,QAAQ,MAAM,SAAS,GAAG,GAAG,GAAG;gBACzC,IAAI,KAAK,CAAC,gDAAgD,SAAS,GAAG;gBACtE,KAAK,MAAM,UAAU,SAAS,OAAO,CAAE;oBACnC,iDAAiD;oBACjD,IAAI,OAAO,UAAU,KAAK,QAAQ;wBAC9B,MAAM,GAAG,CAAC,OAAO,MAAM,EAAE,OAAO,WAAW,EAAE;4BACzC,YAAY,OAAO,UAAU;4BAC7B,QAAQ,OAAO,MAAM;wBACzB,GAAG,OAAO,GAAG;oBACjB;gBACJ;YACJ;YACA,OAAO;QACX,EACA,OAAO,KAAK;YACR,IAAI,KAAK,CAAC,mDAAmD,aAAa;YAC1E,MAAM,WAAW,IAAI,qKAAA,CAAA,sBAAmB,CAAC;gBACrC,KAAK;gBACL,QAAQ,IAAI,qKAAA,CAAA,oBAAiB,CAAC;gBAC9B;YACJ;YACA,OAAO,MAAM,CAAC,SAAS,SAAS,UAAU;YAC1C,OAAO;QACX,SACQ;YACJ;QACJ;IACJ;IACA,8DAA8D;IAC9D,SAAS,SAAS,SAAS,EAAE,IAAI;QAC7B,MAAM,QAAQ;eAAI;eAAc;SAAK,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;QAC5E,OAAO,OAAO,MAAM,CAAC;YACjB,UAAS,IAAI;gBACT,OAAO,SAAS,OAAO;YAC3B;YACA,MAAM,SAAQ,GAAG,EAAE,OAAO;gBACtB,OAAO,QAAQ,OAAO,KAAK;YAC/B;QACJ;IACJ;IACA,OAAO,OAAO,MAAM,CAAC;QACjB,UAAS,IAAI;YACT,OAAO,SAAS,WAAW;QAC/B;QACA,MAAM,SAAQ,GAAG,EAAE,OAAO;YACtB,OAAO,QAAQ,WAAW,KAAK;QACnC;IACJ;AACJ", "ignoreList": [0]}}, {"offset": {"line": 4376, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@arcjet/ip/index.js"], "sourcesContent": ["function parseXForwardedFor(value) {\n    if (typeof value !== \"string\") {\n        return [];\n    }\n    const forwardedIps = [];\n    // As per MDN X-Forwarded-For Headers documentation at\n    // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Forwarded-For\n    // The `x-forwarded-for` header may return one or more IP addresses as\n    // \"client IP, proxy 1 IP, proxy 2 IP\", so we want to split by the comma and\n    // trim each item.\n    for (const item of value.split(\",\")) {\n        forwardedIps.push(item.trim());\n    }\n    return forwardedIps;\n}\nfunction isIPv4Cidr(cidr) {\n    return (typeof cidr === \"object\" &&\n        cidr !== null &&\n        \"type\" in cidr &&\n        typeof cidr.type === \"string\" &&\n        cidr.type === \"v4\" &&\n        \"contains\" in cidr &&\n        typeof cidr.contains === \"function\");\n}\nfunction isIPv6Cidr(cidr) {\n    return (typeof cidr === \"object\" &&\n        cidr !== null &&\n        \"type\" in cidr &&\n        typeof cidr.type === \"string\" &&\n        cidr.type === \"v6\" &&\n        \"contains\" in cidr &&\n        typeof cidr.contains === \"function\");\n}\nfunction isTrustedProxy(ip, segments, proxies) {\n    if (Array.isArray(proxies) && proxies.length > 0) {\n        return proxies.some((proxy) => {\n            if (typeof proxy === \"string\") {\n                return proxy === ip;\n            }\n            if (isIPv4Tuple(segments) && isIPv4Cidr(proxy)) {\n                return proxy.contains(segments);\n            }\n            if (isIPv6Tuple(segments) && isIPv6Cidr(proxy)) {\n                return proxy.contains(segments);\n            }\n            return false;\n        });\n    }\n    return false;\n}\nclass CIDR {\n    // Based on CIDR matching implementation in `ipaddr.js`\n    // Source code:\n    // https://github.com/whitequark/ipaddr.js/blob/08c2cd41e2cb3400683cbd503f60421bfdf66921/lib/ipaddr.js#L107-L130\n    //\n    // Licensed: The MIT License (MIT)\n    // Copyright (C) 2011-2017 whitequark <<EMAIL>>\n    //\n    // Permission is hereby granted, free of charge, to any person obtaining a copy\n    // of this software and associated documentation files (the \"Software\"), to deal\n    // in the Software without restriction, including without limitation the rights\n    // to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    // copies of the Software, and to permit persons to whom the Software is\n    // furnished to do so, subject to the following conditions:\n    // The above copyright notice and this permission notice shall be included in\n    // all copies or substantial portions of the Software.\n    // THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    // IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    // FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    // AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    // LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    // OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n    // THE SOFTWARE.\n    contains(ip) {\n        let part = 0;\n        let shift;\n        let cidrBits = this.bits;\n        while (cidrBits > 0) {\n            shift = this.partSize - cidrBits;\n            if (shift < 0) {\n                shift = 0;\n            }\n            if (ip[part] >> shift !== this.parts[part] >> shift) {\n                return false;\n            }\n            cidrBits -= this.partSize;\n            part += 1;\n        }\n        return true;\n    }\n}\nclass IPv4CIDR extends CIDR {\n    type = \"v4\";\n    partSize = 8;\n    parts;\n    bits;\n    constructor(parts, bits) {\n        super();\n        this.bits = bits;\n        this.parts = parts;\n        Object.freeze(this);\n    }\n    contains(ip) {\n        return super.contains(ip);\n    }\n}\nclass IPv6CIDR extends CIDR {\n    type = \"v6\";\n    partSize = 16;\n    parts;\n    bits;\n    constructor(parts, bits) {\n        super();\n        this.bits = bits;\n        this.parts = parts;\n        Object.freeze(this);\n    }\n    contains(ip) {\n        return super.contains(ip);\n    }\n}\nfunction parseCIDR(cidr) {\n    // Pre-condition: `cidr` has be verified to have at least one `/`\n    const cidrParts = cidr.split(\"/\");\n    if (cidrParts.length !== 2) {\n        throw new Error(\"invalid CIDR address: must be exactly 2 parts\");\n    }\n    const parser = new Parser(cidrParts[0]);\n    const maybeIPv4 = parser.readIPv4Address();\n    if (isIPv4Tuple(maybeIPv4)) {\n        const bits = parseInt(cidrParts[1], 10);\n        if (isNaN(bits) || bits < 0 || bits > 32) {\n            throw new Error(\"invalid CIDR address: incorrect amount of bits\");\n        }\n        return new IPv4CIDR(maybeIPv4, bits);\n    }\n    const maybeIPv6 = parser.readIPv6Address();\n    if (isIPv6Tuple(maybeIPv6)) {\n        const bits = parseInt(cidrParts[1], 10);\n        if (isNaN(bits) || bits < 0 || bits > 128) {\n            throw new Error(\"invalid CIDR address: incorrect amount of bits\");\n        }\n        return new IPv6CIDR(maybeIPv6, bits);\n    }\n    throw new Error(\"invalid CIDR address: could not parse IP address\");\n}\nfunction isCIDR(address) {\n    return address.includes(\"/\");\n}\n// Converts a string that looks like a CIDR address into the corresponding class\n// while ignoring non-CIDR IP addresses.\nfunction parseProxy(proxy) {\n    if (isCIDR(proxy)) {\n        return parseCIDR(proxy);\n    }\n    else {\n        return proxy;\n    }\n}\nfunction isIPv4Tuple(segements) {\n    if (typeof segements === \"undefined\") {\n        return false;\n    }\n    return segements.length === 4;\n}\nfunction isIPv6Tuple(segements) {\n    if (typeof segements === \"undefined\") {\n        return false;\n    }\n    return segements.length === 8;\n}\nfunction u16FromBytes(bytes) {\n    const u8 = new Uint8Array(bytes);\n    return new Uint16Array(u8.buffer)[0];\n}\nfunction u32FromBytes(bytes) {\n    const u8 = new Uint8Array(bytes);\n    return new Uint32Array(u8.buffer)[0];\n}\n// This Parser and \"is global\" comparisons are a TypeScript implementation of\n// similar code in the Rust stdlib with only slight deviations as noted.\n//\n// We want to mirror Rust's logic as close as possible, because we'll be relying\n// on its implementation when we add a Wasm library to determine IPs and only\n// falling back to JavaScript in non-Wasm environments.\n//\n// Parser source:\n// https://github.com/rust-lang/rust/blob/07921b50ba6dcb5b2984a1dba039a38d85bffba2/library/core/src/net/parser.rs#L34\n// Comparison source:\n// https://github.com/rust-lang/rust/blob/87e1447aadaa2899ff6ccabe1fa669eb50fb60a1/library/core/src/net/ip_addr.rs#L749\n// https://github.com/rust-lang/rust/blob/87e1447aadaa2899ff6ccabe1fa669eb50fb60a1/library/core/src/net/ip_addr.rs#L1453\n//\n// Licensed: The MIT License (MIT)\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions: The above copyright\n// notice and this permission notice shall be included in all copies or\n// substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n// SOFTWARE.\nclass Parser {\n    state;\n    constructor(input) {\n        this.state = input;\n    }\n    readAtomically(inner) {\n        const state = this.state;\n        const result = inner(this);\n        if (typeof result === \"undefined\") {\n            this.state = state;\n        }\n        return result;\n    }\n    peakChar() {\n        return this.state[0];\n    }\n    readChar() {\n        const b = this.state[0];\n        this.state = this.state.slice(1);\n        return b;\n    }\n    readGivenChar(target) {\n        return this.readAtomically((p) => {\n            const c = p.readChar();\n            if (c === target) {\n                return c;\n            }\n        });\n    }\n    readSeparator(sep, index, inner) {\n        return this.readAtomically((p) => {\n            if (index > 0) {\n                const c = p.readGivenChar(sep);\n                if (typeof c === \"undefined\") {\n                    return;\n                }\n            }\n            return inner(p);\n        });\n    }\n    readNumber(radix, maxDigits, allowZeroPrefix = false) {\n        return this.readAtomically((p) => {\n            let result = 0;\n            let digitCount = 0;\n            const hasLeadingZero = p.peakChar() === \"0\";\n            function nextCharAsDigit() {\n                return p.readAtomically((p) => {\n                    const c = p.readChar();\n                    if (c) {\n                        const n = parseInt(c, radix);\n                        if (!isNaN(n)) {\n                            return n;\n                        }\n                    }\n                });\n            }\n            for (let digit = nextCharAsDigit(); digit !== undefined; digit = nextCharAsDigit()) {\n                result = result * radix;\n                result = result + digit;\n                digitCount += 1;\n                if (typeof maxDigits !== \"undefined\") {\n                    if (digitCount > maxDigits) {\n                        return;\n                    }\n                }\n            }\n            if (digitCount === 0) {\n                return;\n            }\n            else if (!allowZeroPrefix && hasLeadingZero && digitCount > 1) {\n                return;\n            }\n            else {\n                return result;\n            }\n        });\n    }\n    readIPv4Address() {\n        return this.readAtomically((p) => {\n            const groups = [];\n            for (let idx = 0; idx < 4; idx++) {\n                const result = p.readSeparator(\".\", idx, (p) => {\n                    // Disallow octal number in IP string\n                    // https://tools.ietf.org/html/rfc6943#section-3.1.1\n                    return p.readNumber(10, 3, false);\n                });\n                if (result === undefined) {\n                    return;\n                }\n                else {\n                    groups.push(result);\n                }\n            }\n            return groups;\n        });\n    }\n    readIPv6Address() {\n        // Read a chunk of an IPv6 address into `groups`. Returns the number of\n        // groups read, along with a bool indicating if an embedded trailing IPv4\n        // address was read. Specifically, read a series of colon-separated IPv6\n        // groups (0x0000 - 0xFFFF), with an optional trailing embedded IPv4 address\n        const readGroups = (p, groups) => {\n            const limit = groups.length;\n            for (const i of groups.keys()) {\n                // Try to read a trailing embedded IPv4 address. There must be at least\n                // two groups left\n                if (i < limit - 1) {\n                    const ipv4 = p.readSeparator(\":\", i, (p) => p.readIPv4Address());\n                    if (isIPv4Tuple(ipv4)) {\n                        const [one, two, three, four] = ipv4;\n                        groups[i + 0] = u16FromBytes([one, two]);\n                        groups[i + 1] = u16FromBytes([three, four]);\n                        return [i + 2, true];\n                    }\n                }\n                const group = p.readSeparator(\":\", i, (p) => p.readNumber(16, 4, true));\n                if (typeof group !== \"undefined\") {\n                    groups[i] = group;\n                }\n                else {\n                    return [i, false];\n                }\n            }\n            return [groups.length, false];\n        };\n        return this.readAtomically((p) => {\n            // Read the front part of the address; either the whole thing, or up\n            // to the first ::\n            const head = new Uint16Array(8);\n            const [headSize, headIPv4] = readGroups(p, head);\n            if (headSize === 8) {\n                return head;\n            }\n            // IPv4 part is not allowed before `::`\n            if (headIPv4) {\n                return;\n            }\n            // Read `::` if previous code parsed less than 8 groups.\n            // `::` indicates one or more groups of 16 bits of zeros.\n            if (typeof p.readGivenChar(\":\") === \"undefined\") {\n                return;\n            }\n            if (typeof p.readGivenChar(\":\") === \"undefined\") {\n                return;\n            }\n            // Read the back part of the address. The :: must contain at least one\n            // set of zeroes, so our max length is 7.\n            const tail = new Uint16Array(7);\n            const limit = 8 - (headSize + 1);\n            const [tailSize, _] = readGroups(p, tail.subarray(0, limit));\n            head.set(tail.slice(0, tailSize), 8 - tailSize);\n            return head;\n        });\n    }\n    readPort() {\n        return this.readAtomically((p) => {\n            if (typeof p.readGivenChar(\":\") !== \"undefined\") {\n                return p.readNumber(10, undefined, true);\n            }\n        });\n    }\n    readScopeId() {\n        return this.readAtomically((p) => {\n            if (typeof p.readGivenChar(\"%\") !== \"undefined\") {\n                return p.readNumber(10, undefined, true);\n            }\n        });\n    }\n}\nconst IPV4_BROADCAST = u32FromBytes([255, 255, 255, 255]);\nfunction isGlobalIPv4(s, proxies) {\n    if (typeof s !== \"string\") {\n        return false;\n    }\n    const parser = new Parser(s);\n    const octets = parser.readIPv4Address();\n    if (!isIPv4Tuple(octets)) {\n        return false;\n    }\n    if (isTrustedProxy(s, octets, proxies)) {\n        return false;\n    }\n    // Rust doesn't check the remaining state when parsing an IPv4. However, we\n    // want to ensure we have exactly an IP (with optionally a port), so we parse\n    // it and then check remaining parser state.\n    parser.readPort();\n    if (parser.state.length !== 0) {\n        return false;\n    }\n    // \"This network\"\n    if (octets[0] === 0) {\n        return false;\n    }\n    // Private IPv4 address ranges\n    if (octets[0] === 10) {\n        return false;\n    }\n    if (octets[0] === 172 && octets[1] >= 16 && octets[1] <= 31) {\n        return false;\n    }\n    if (octets[0] === 192 && octets[1] === 168) {\n        return false;\n    }\n    // Loopback address\n    if (octets[0] === 127) {\n        return false;\n    }\n    // Shared range\n    if (octets[0] === 100 && (octets[1] & 0b1100_0000) === 0b0100_0000) {\n        return false;\n    }\n    // Link-local range\n    if (octets[0] === 169 && octets[1] === 254) {\n        return false;\n    }\n    // addresses reserved for future protocols (`*********/24`)\n    if (octets[0] === 192 && octets[1] === 0 && octets[2] === 0) {\n        return false;\n    }\n    // Documentation ranges\n    if (octets[0] === 192 && octets[1] === 0 && octets[2] === 2) {\n        return false;\n    }\n    if (octets[0] === 198 && octets[1] === 51 && octets[2] === 100) {\n        return false;\n    }\n    if (octets[0] === 203 && octets[1] === 0 && octets[2] === 113) {\n        return false;\n    }\n    // Benchmarking range\n    if (octets[0] === 198 && (octets[1] & 0xfe) === 18) {\n        return false;\n    }\n    const isBroadcast = u32FromBytes(octets) === IPV4_BROADCAST;\n    // Reserved range\n    if ((octets[0] & 240) === 240 && !isBroadcast) {\n        return false;\n    }\n    // Broadcast address\n    if (isBroadcast) {\n        return false;\n    }\n    for (const octet of octets) {\n        if (octet < 0 || octet > 255) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction isGlobalIPv6(s, proxies) {\n    if (typeof s !== \"string\") {\n        return false;\n    }\n    const parser = new Parser(s);\n    const segments = parser.readIPv6Address();\n    if (!isIPv6Tuple(segments)) {\n        return false;\n    }\n    if (isTrustedProxy(s, segments, proxies)) {\n        return false;\n    }\n    // Rust doesn't check the remaining state when parsing an IPv6. However, we\n    // want to ensure we have exactly an IP (with optionally a scope id), so we\n    // parse it and then check remaining parser state.\n    // TODO: We don't support an IPv6 address with a port because that seems to\n    // require wrapping the address and scope in `[]`, e.g. `[:ffff%1]:8080`\n    parser.readScopeId();\n    if (parser.state.length !== 0) {\n        return false;\n    }\n    // Unspecified address\n    if (segments[0] === 0 &&\n        segments[1] === 0 &&\n        segments[2] === 0 &&\n        segments[3] === 0 &&\n        segments[4] === 0 &&\n        segments[5] === 0 &&\n        segments[6] === 0 &&\n        segments[7] === 0) {\n        return false;\n    }\n    // Loopback address\n    if (segments[0] === 0 &&\n        segments[1] === 0 &&\n        segments[2] === 0 &&\n        segments[3] === 0 &&\n        segments[4] === 0 &&\n        segments[5] === 0 &&\n        segments[6] === 0 &&\n        segments[7] === 0x1) {\n        return false;\n    }\n    // IPv4-mapped Address (`::ffff:0:0/96`)\n    if (segments[0] === 0 &&\n        segments[1] === 0 &&\n        segments[2] === 0 &&\n        segments[3] === 0 &&\n        segments[4] === 0 &&\n        segments[5] === 0xffff) {\n        return false;\n    }\n    // IPv4-IPv6 Translat. (`64:ff9b:1::/48`)\n    if (segments[0] === 0x64 && segments[1] === 0xff9b && segments[2] === 1) {\n        return false;\n    }\n    // Discard-Only Address Block (`100::/64`)\n    if (segments[0] === 0x100 &&\n        segments[1] === 0 &&\n        segments[2] === 0 &&\n        segments[3] === 0) {\n        return false;\n    }\n    // IETF Protocol Assignments (`2001::/23`)\n    if (segments[0] === 0x2001 && segments[1] < 0x200) {\n        // Port Control Protocol Anycast (`2001:1::1`)\n        if (segments[0] === 0x2001 &&\n            segments[1] === 1 &&\n            segments[2] === 0 &&\n            segments[3] === 0 &&\n            segments[4] === 0 &&\n            segments[5] === 0 &&\n            segments[6] === 0 &&\n            segments[7] === 1) {\n            return true;\n        }\n        // Traversal Using Relays around NAT Anycast (`2001:1::2`)\n        if (segments[0] === 0x2001 &&\n            segments[1] === 1 &&\n            segments[2] === 0 &&\n            segments[3] === 0 &&\n            segments[4] === 0 &&\n            segments[5] === 0 &&\n            segments[6] === 0 &&\n            segments[7] === 2) {\n            return true;\n        }\n        // AMT (`2001:3::/32`)\n        if (segments[0] === 0x2001 && segments[1] === 3) {\n            return true;\n        }\n        // AS112-v6 (`2001:4:112::/48`)\n        if (segments[0] === 0x2001 && segments[1] === 4 && segments[2] === 0x112) {\n            return true;\n        }\n        // ORCHIDv2 (`2001:20::/28`)\n        if (segments[0] === 0x2001 && segments[1] >= 0x20 && segments[1] <= 0x2f) {\n            return true;\n        }\n        // Benchmarking range (and others)\n        return false;\n    }\n    // Documentation range\n    if (segments[0] === 0x2001 && segments[1] === 0xdb8) {\n        return false;\n    }\n    // Unique local range\n    if ((segments[0] & 0xfe00) === 0xfc00) {\n        return false;\n    }\n    // Unicast link local range\n    if ((segments[0] & 0xffc0) === 0xfe80) {\n        return false;\n    }\n    return true;\n}\nfunction isGlobalIP(s, proxies) {\n    if (isGlobalIPv4(s, proxies)) {\n        return true;\n    }\n    if (isGlobalIPv6(s, proxies)) {\n        return true;\n    }\n    return false;\n}\nfunction isHeaders(val) {\n    return typeof val.get === \"function\";\n}\nfunction getHeader(headers, headerKey) {\n    if (isHeaders(headers)) {\n        return headers.get(headerKey);\n    }\n    else {\n        const headerValue = headers[headerKey];\n        if (Array.isArray(headerValue)) {\n            return headerValue.join(\",\");\n        }\n        else {\n            return headerValue;\n        }\n    }\n}\n// Heavily based on https://github.com/pbojinov/request-ip\n//\n// Licensed: The MIT License (MIT) Copyright (c) 2022 Petar Bojinov -\n// <EMAIL>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions: The above copyright\n// notice and this permission notice shall be included in all copies or\n// substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n// SOFTWARE.\nfunction findIP(request, options = {}) {\n    const { platform, proxies } = options;\n    // Prefer anything available via the platform over headers since headers can\n    // be set by users. Only if we don't have an IP available in `request` do we\n    // search the `headers`.\n    if (isGlobalIP(request.ip, proxies)) {\n        return request.ip;\n    }\n    const socketRemoteAddress = request.socket?.remoteAddress;\n    if (isGlobalIP(socketRemoteAddress, proxies)) {\n        return socketRemoteAddress;\n    }\n    const infoRemoteAddress = request.info?.remoteAddress;\n    if (isGlobalIP(infoRemoteAddress, proxies)) {\n        return infoRemoteAddress;\n    }\n    // AWS Api Gateway + Lambda\n    const requestContextIdentitySourceIP = request.requestContext?.identity?.sourceIp;\n    if (isGlobalIP(requestContextIdentitySourceIP, proxies)) {\n        return requestContextIdentitySourceIP;\n    }\n    // Validate we have some object for `request.headers`\n    if (typeof request.headers !== \"object\" || request.headers === null) {\n        return \"\";\n    }\n    // Platform-specific headers should only be accepted when we can determine\n    // that we are running on that platform. For example, the `CF-Connecting-IP`\n    // header should only be accepted when running on Cloudflare; otherwise, it\n    // can be spoofed.\n    if (platform === \"cloudflare\") {\n        // CF-Connecting-IPv6: https://developers.cloudflare.com/fundamentals/reference/http-request-headers/#cf-connecting-ipv6\n        const cfConnectingIPv6 = getHeader(request.headers, \"cf-connecting-ipv6\");\n        if (isGlobalIPv6(cfConnectingIPv6, proxies)) {\n            return cfConnectingIPv6;\n        }\n        // CF-Connecting-IP: https://developers.cloudflare.com/fundamentals/reference/http-request-headers/#cf-connecting-ip\n        const cfConnectingIP = getHeader(request.headers, \"cf-connecting-ip\");\n        if (isGlobalIP(cfConnectingIP, proxies)) {\n            return cfConnectingIP;\n        }\n        // If we are using a platform check and don't have a Global IP, we exit\n        // early with an empty IP since the more generic headers shouldn't be\n        // trusted over the platform-specific headers.\n        return \"\";\n    }\n    // Fly.io: https://fly.io/docs/machines/runtime-environment/#fly_app_name\n    if (platform === \"fly-io\") {\n        // Fly-Client-IP: https://fly.io/docs/networking/request-headers/#fly-client-ip\n        const flyClientIP = getHeader(request.headers, \"fly-client-ip\");\n        if (isGlobalIP(flyClientIP, proxies)) {\n            return flyClientIP;\n        }\n        // If we are using a platform check and don't have a Global IP, we exit\n        // early with an empty IP since the more generic headers shouldn't be\n        // trusted over the platform-specific headers.\n        return \"\";\n    }\n    if (platform === \"vercel\") {\n        // https://vercel.com/docs/edge-network/headers/request-headers#x-real-ip\n        // Also used by `@vercel/functions`, see:\n        // https://github.com/vercel/vercel/blob/d7536d52c87712b1b3f83e4b0fd535a1fb7e384c/packages/functions/src/headers.ts#L12\n        const xRealIP = getHeader(request.headers, \"x-real-ip\");\n        if (isGlobalIP(xRealIP, proxies)) {\n            return xRealIP;\n        }\n        // https://vercel.com/docs/edge-network/headers/request-headers#x-vercel-forwarded-for\n        // By default, it seems this will be 1 address, but they discuss trusted\n        // proxy forwarding so we try to parse it like normal. See\n        // https://vercel.com/docs/edge-network/headers/request-headers#custom-x-forwarded-for-ip\n        const xVercelForwardedFor = getHeader(request.headers, \"x-vercel-forwarded-for\");\n        const xVercelForwardedForItems = parseXForwardedFor(xVercelForwardedFor);\n        // As per MDN X-Forwarded-For Headers documentation at\n        // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Forwarded-For\n        // We may find more than one IP in the `x-forwarded-for` header. Since the\n        // first IP will be closest to the user (and the most likely to be spoofed),\n        // we want to iterate tail-to-head so we reverse the list.\n        for (const item of xVercelForwardedForItems.reverse()) {\n            if (isGlobalIP(item, proxies)) {\n                return item;\n            }\n        }\n        // https://vercel.com/docs/edge-network/headers/request-headers#x-forwarded-for\n        // By default, it seems this will be 1 address, but they discuss trusted\n        // proxy forwarding so we try to parse it like normal. See\n        // https://vercel.com/docs/edge-network/headers/request-headers#custom-x-forwarded-for-ip\n        const xForwardedFor = getHeader(request.headers, \"x-forwarded-for\");\n        const xForwardedForItems = parseXForwardedFor(xForwardedFor);\n        // As per MDN X-Forwarded-For Headers documentation at\n        // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Forwarded-For\n        // We may find more than one IP in the `x-forwarded-for` header. Since the\n        // first IP will be closest to the user (and the most likely to be spoofed),\n        // we want to iterate tail-to-head so we reverse the list.\n        for (const item of xForwardedForItems.reverse()) {\n            if (isGlobalIP(item, proxies)) {\n                return item;\n            }\n        }\n        // If we are using a platform check and don't have a Global IP, we exit\n        // early with an empty IP since the more generic headers shouldn't be\n        // trusted over the platform-specific headers.\n        return \"\";\n    }\n    if (platform === \"render\") {\n        // True-Client-IP: https://community.render.com/t/what-number-of-proxies-sit-in-front-of-an-express-app-deployed-on-render/35981/2\n        const trueClientIP = getHeader(request.headers, \"true-client-ip\");\n        if (isGlobalIP(trueClientIP, proxies)) {\n            return trueClientIP;\n        }\n        // If we are using a platform check and don't have a Global IP, we exit\n        // early with an empty IP since the more generic headers shouldn't be\n        // trusted over the platform-specific headers.\n        return \"\";\n    }\n    // Standard headers used by Amazon EC2, Heroku, and others.\n    const xClientIP = getHeader(request.headers, \"x-client-ip\");\n    if (isGlobalIP(xClientIP, proxies)) {\n        return xClientIP;\n    }\n    // Load-balancers (AWS ELB) or proxies.\n    const xForwardedFor = getHeader(request.headers, \"x-forwarded-for\");\n    const xForwardedForItems = parseXForwardedFor(xForwardedFor);\n    // As per MDN X-Forwarded-For Headers documentation at\n    // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Forwarded-For\n    // We may find more than one IP in the `x-forwarded-for` header. Since the\n    // first IP will be closest to the user (and the most likely to be spoofed),\n    // we want to iterate tail-to-head so we reverse the list.\n    for (const item of xForwardedForItems.reverse()) {\n        if (isGlobalIP(item, proxies)) {\n            return item;\n        }\n    }\n    // DigitalOcean.\n    // DO-Connecting-IP: https://www.digitalocean.com/community/questions/app-platform-client-ip\n    const doConnectingIP = getHeader(request.headers, \"do-connecting-ip\");\n    if (isGlobalIP(doConnectingIP, proxies)) {\n        return doConnectingIP;\n    }\n    // Fastly and Firebase hosting header (When forwared to cloud function)\n    // Fastly-Client-IP\n    const fastlyClientIP = getHeader(request.headers, \"fastly-client-ip\");\n    if (isGlobalIP(fastlyClientIP, proxies)) {\n        return fastlyClientIP;\n    }\n    // Akamai\n    // True-Client-IP\n    const trueClientIP = getHeader(request.headers, \"true-client-ip\");\n    if (isGlobalIP(trueClientIP, proxies)) {\n        return trueClientIP;\n    }\n    // Default nginx proxy/fcgi; alternative to x-forwarded-for, used by some proxies\n    // X-Real-IP\n    const xRealIP = getHeader(request.headers, \"x-real-ip\");\n    if (isGlobalIP(xRealIP, proxies)) {\n        return xRealIP;\n    }\n    // Rackspace LB and Riverbed's Stingray?\n    const xClusterClientIP = getHeader(request.headers, \"x-cluster-client-ip\");\n    if (isGlobalIP(xClusterClientIP, proxies)) {\n        return xClusterClientIP;\n    }\n    const xForwarded = getHeader(request.headers, \"x-forwarded\");\n    if (isGlobalIP(xForwarded, proxies)) {\n        return xForwarded;\n    }\n    const forwardedFor = getHeader(request.headers, \"forwarded-for\");\n    if (isGlobalIP(forwardedFor, proxies)) {\n        return forwardedFor;\n    }\n    const forwarded = getHeader(request.headers, \"forwarded\");\n    if (isGlobalIP(forwarded, proxies)) {\n        return forwarded;\n    }\n    // Google Cloud App Engine\n    // X-Appengine-User-IP: https://cloud.google.com/appengine/docs/standard/reference/request-headers?tab=node.js\n    const xAppEngineUserIP = getHeader(request.headers, \"x-appengine-user-ip\");\n    if (isGlobalIP(xAppEngineUserIP, proxies)) {\n        return xAppEngineUserIP;\n    }\n    return \"\";\n}\n\nexport { findIP as default, parseProxy };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,mBAAmB,KAAK;IAC7B,IAAI,OAAO,UAAU,UAAU;QAC3B,OAAO,EAAE;IACb;IACA,MAAM,eAAe,EAAE;IACvB,sDAAsD;IACtD,4EAA4E;IAC5E,sEAAsE;IACtE,4EAA4E;IAC5E,kBAAkB;IAClB,KAAK,MAAM,QAAQ,MAAM,KAAK,CAAC,KAAM;QACjC,aAAa,IAAI,CAAC,KAAK,IAAI;IAC/B;IACA,OAAO;AACX;AACA,SAAS,WAAW,IAAI;IACpB,OAAQ,OAAO,SAAS,YACpB,SAAS,QACT,UAAU,QACV,OAAO,KAAK,IAAI,KAAK,YACrB,KAAK,IAAI,KAAK,QACd,cAAc,QACd,OAAO,KAAK,QAAQ,KAAK;AACjC;AACA,SAAS,WAAW,IAAI;IACpB,OAAQ,OAAO,SAAS,YACpB,SAAS,QACT,UAAU,QACV,OAAO,KAAK,IAAI,KAAK,YACrB,KAAK,IAAI,KAAK,QACd,cAAc,QACd,OAAO,KAAK,QAAQ,KAAK;AACjC;AACA,SAAS,eAAe,EAAE,EAAE,QAAQ,EAAE,OAAO;IACzC,IAAI,MAAM,OAAO,CAAC,YAAY,QAAQ,MAAM,GAAG,GAAG;QAC9C,OAAO,QAAQ,IAAI,CAAC,CAAC;YACjB,IAAI,OAAO,UAAU,UAAU;gBAC3B,OAAO,UAAU;YACrB;YACA,IAAI,YAAY,aAAa,WAAW,QAAQ;gBAC5C,OAAO,MAAM,QAAQ,CAAC;YAC1B;YACA,IAAI,YAAY,aAAa,WAAW,QAAQ;gBAC5C,OAAO,MAAM,QAAQ,CAAC;YAC1B;YACA,OAAO;QACX;IACJ;IACA,OAAO;AACX;AACA,MAAM;IACF,uDAAuD;IACvD,eAAe;IACf,gHAAgH;IAChH,EAAE;IACF,kCAAkC;IAClC,iEAAiE;IACjE,EAAE;IACF,+EAA+E;IAC/E,gFAAgF;IAChF,+EAA+E;IAC/E,4EAA4E;IAC5E,wEAAwE;IACxE,2DAA2D;IAC3D,6EAA6E;IAC7E,sDAAsD;IACtD,6EAA6E;IAC7E,2EAA2E;IAC3E,8EAA8E;IAC9E,yEAAyE;IACzE,gFAAgF;IAChF,4EAA4E;IAC5E,gBAAgB;IAChB,SAAS,EAAE,EAAE;QACT,IAAI,OAAO;QACX,IAAI;QACJ,IAAI,WAAW,IAAI,CAAC,IAAI;QACxB,MAAO,WAAW,EAAG;YACjB,QAAQ,IAAI,CAAC,QAAQ,GAAG;YACxB,IAAI,QAAQ,GAAG;gBACX,QAAQ;YACZ;YACA,IAAI,EAAE,CAAC,KAAK,IAAI,UAAU,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,OAAO;gBACjD,OAAO;YACX;YACA,YAAY,IAAI,CAAC,QAAQ;YACzB,QAAQ;QACZ;QACA,OAAO;IACX;AACJ;AACA,MAAM,iBAAiB;IACnB,OAAO,KAAK;IACZ,WAAW,EAAE;IACb,MAAM;IACN,KAAK;IACL,YAAY,KAAK,EAAE,IAAI,CAAE;QACrB,KAAK;QACL,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;QACb,OAAO,MAAM,CAAC,IAAI;IACtB;IACA,SAAS,EAAE,EAAE;QACT,OAAO,KAAK,CAAC,SAAS;IAC1B;AACJ;AACA,MAAM,iBAAiB;IACnB,OAAO,KAAK;IACZ,WAAW,GAAG;IACd,MAAM;IACN,KAAK;IACL,YAAY,KAAK,EAAE,IAAI,CAAE;QACrB,KAAK;QACL,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;QACb,OAAO,MAAM,CAAC,IAAI;IACtB;IACA,SAAS,EAAE,EAAE;QACT,OAAO,KAAK,CAAC,SAAS;IAC1B;AACJ;AACA,SAAS,UAAU,IAAI;IACnB,iEAAiE;IACjE,MAAM,YAAY,KAAK,KAAK,CAAC;IAC7B,IAAI,UAAU,MAAM,KAAK,GAAG;QACxB,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,SAAS,IAAI,OAAO,SAAS,CAAC,EAAE;IACtC,MAAM,YAAY,OAAO,eAAe;IACxC,IAAI,YAAY,YAAY;QACxB,MAAM,OAAO,SAAS,SAAS,CAAC,EAAE,EAAE;QACpC,IAAI,MAAM,SAAS,OAAO,KAAK,OAAO,IAAI;YACtC,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,IAAI,SAAS,WAAW;IACnC;IACA,MAAM,YAAY,OAAO,eAAe;IACxC,IAAI,YAAY,YAAY;QACxB,MAAM,OAAO,SAAS,SAAS,CAAC,EAAE,EAAE;QACpC,IAAI,MAAM,SAAS,OAAO,KAAK,OAAO,KAAK;YACvC,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,IAAI,SAAS,WAAW;IACnC;IACA,MAAM,IAAI,MAAM;AACpB;AACA,SAAS,OAAO,OAAO;IACnB,OAAO,QAAQ,QAAQ,CAAC;AAC5B;AACA,gFAAgF;AAChF,wCAAwC;AACxC,SAAS,WAAW,KAAK;IACrB,IAAI,OAAO,QAAQ;QACf,OAAO,UAAU;IACrB,OACK;QACD,OAAO;IACX;AACJ;AACA,SAAS,YAAY,SAAS;IAC1B,IAAI,OAAO,cAAc,aAAa;QAClC,OAAO;IACX;IACA,OAAO,UAAU,MAAM,KAAK;AAChC;AACA,SAAS,YAAY,SAAS;IAC1B,IAAI,OAAO,cAAc,aAAa;QAClC,OAAO;IACX;IACA,OAAO,UAAU,MAAM,KAAK;AAChC;AACA,SAAS,aAAa,KAAK;IACvB,MAAM,KAAK,IAAI,WAAW;IAC1B,OAAO,IAAI,YAAY,GAAG,MAAM,CAAC,CAAC,EAAE;AACxC;AACA,SAAS,aAAa,KAAK;IACvB,MAAM,KAAK,IAAI,WAAW;IAC1B,OAAO,IAAI,YAAY,GAAG,MAAM,CAAC,CAAC,EAAE;AACxC;AACA,6EAA6E;AAC7E,wEAAwE;AACxE,EAAE;AACF,gFAAgF;AAChF,6EAA6E;AAC7E,uDAAuD;AACvD,EAAE;AACF,iBAAiB;AACjB,qHAAqH;AACrH,qBAAqB;AACrB,uHAAuH;AACvH,wHAAwH;AACxH,EAAE;AACF,kCAAkC;AAClC,+EAA+E;AAC/E,gFAAgF;AAChF,+EAA+E;AAC/E,4EAA4E;AAC5E,wEAAwE;AACxE,+EAA+E;AAC/E,uEAAuE;AACvE,wCAAwC;AACxC,EAAE;AACF,6EAA6E;AAC7E,2EAA2E;AAC3E,8EAA8E;AAC9E,yEAAyE;AACzE,gFAAgF;AAChF,gFAAgF;AAChF,YAAY;AACZ,MAAM;IACF,MAAM;IACN,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,KAAK,GAAG;IACjB;IACA,eAAe,KAAK,EAAE;QAClB,MAAM,QAAQ,IAAI,CAAC,KAAK;QACxB,MAAM,SAAS,MAAM,IAAI;QACzB,IAAI,OAAO,WAAW,aAAa;YAC/B,IAAI,CAAC,KAAK,GAAG;QACjB;QACA,OAAO;IACX;IACA,WAAW;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE;IACxB;IACA,WAAW;QACP,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;QACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;QAC9B,OAAO;IACX;IACA,cAAc,MAAM,EAAE;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC;YACxB,MAAM,IAAI,EAAE,QAAQ;YACpB,IAAI,MAAM,QAAQ;gBACd,OAAO;YACX;QACJ;IACJ;IACA,cAAc,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE;QAC7B,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC;YACxB,IAAI,QAAQ,GAAG;gBACX,MAAM,IAAI,EAAE,aAAa,CAAC;gBAC1B,IAAI,OAAO,MAAM,aAAa;oBAC1B;gBACJ;YACJ;YACA,OAAO,MAAM;QACjB;IACJ;IACA,WAAW,KAAK,EAAE,SAAS,EAAE,kBAAkB,KAAK,EAAE;QAClD,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC;YACxB,IAAI,SAAS;YACb,IAAI,aAAa;YACjB,MAAM,iBAAiB,EAAE,QAAQ,OAAO;YACxC,SAAS;gBACL,OAAO,EAAE,cAAc,CAAC,CAAC;oBACrB,MAAM,IAAI,EAAE,QAAQ;oBACpB,IAAI,GAAG;wBACH,MAAM,IAAI,SAAS,GAAG;wBACtB,IAAI,CAAC,MAAM,IAAI;4BACX,OAAO;wBACX;oBACJ;gBACJ;YACJ;YACA,IAAK,IAAI,QAAQ,mBAAmB,UAAU,WAAW,QAAQ,kBAAmB;gBAChF,SAAS,SAAS;gBAClB,SAAS,SAAS;gBAClB,cAAc;gBACd,IAAI,OAAO,cAAc,aAAa;oBAClC,IAAI,aAAa,WAAW;wBACxB;oBACJ;gBACJ;YACJ;YACA,IAAI,eAAe,GAAG;gBAClB;YACJ,OACK,IAAI,CAAC,mBAAmB,kBAAkB,aAAa,GAAG;gBAC3D;YACJ,OACK;gBACD,OAAO;YACX;QACJ;IACJ;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC;YACxB,MAAM,SAAS,EAAE;YACjB,IAAK,IAAI,MAAM,GAAG,MAAM,GAAG,MAAO;gBAC9B,MAAM,SAAS,EAAE,aAAa,CAAC,KAAK,KAAK,CAAC;oBACtC,qCAAqC;oBACrC,oDAAoD;oBACpD,OAAO,EAAE,UAAU,CAAC,IAAI,GAAG;gBAC/B;gBACA,IAAI,WAAW,WAAW;oBACtB;gBACJ,OACK;oBACD,OAAO,IAAI,CAAC;gBAChB;YACJ;YACA,OAAO;QACX;IACJ;IACA,kBAAkB;QACd,uEAAuE;QACvE,yEAAyE;QACzE,wEAAwE;QACxE,4EAA4E;QAC5E,MAAM,aAAa,CAAC,GAAG;YACnB,MAAM,QAAQ,OAAO,MAAM;YAC3B,KAAK,MAAM,KAAK,OAAO,IAAI,GAAI;gBAC3B,uEAAuE;gBACvE,kBAAkB;gBAClB,IAAI,IAAI,QAAQ,GAAG;oBACf,MAAM,OAAO,EAAE,aAAa,CAAC,KAAK,GAAG,CAAC,IAAM,EAAE,eAAe;oBAC7D,IAAI,YAAY,OAAO;wBACnB,MAAM,CAAC,KAAK,KAAK,OAAO,KAAK,GAAG;wBAChC,MAAM,CAAC,IAAI,EAAE,GAAG,aAAa;4BAAC;4BAAK;yBAAI;wBACvC,MAAM,CAAC,IAAI,EAAE,GAAG,aAAa;4BAAC;4BAAO;yBAAK;wBAC1C,OAAO;4BAAC,IAAI;4BAAG;yBAAK;oBACxB;gBACJ;gBACA,MAAM,QAAQ,EAAE,aAAa,CAAC,KAAK,GAAG,CAAC,IAAM,EAAE,UAAU,CAAC,IAAI,GAAG;gBACjE,IAAI,OAAO,UAAU,aAAa;oBAC9B,MAAM,CAAC,EAAE,GAAG;gBAChB,OACK;oBACD,OAAO;wBAAC;wBAAG;qBAAM;gBACrB;YACJ;YACA,OAAO;gBAAC,OAAO,MAAM;gBAAE;aAAM;QACjC;QACA,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC;YACxB,oEAAoE;YACpE,kBAAkB;YAClB,MAAM,OAAO,IAAI,YAAY;YAC7B,MAAM,CAAC,UAAU,SAAS,GAAG,WAAW,GAAG;YAC3C,IAAI,aAAa,GAAG;gBAChB,OAAO;YACX;YACA,uCAAuC;YACvC,IAAI,UAAU;gBACV;YACJ;YACA,wDAAwD;YACxD,yDAAyD;YACzD,IAAI,OAAO,EAAE,aAAa,CAAC,SAAS,aAAa;gBAC7C;YACJ;YACA,IAAI,OAAO,EAAE,aAAa,CAAC,SAAS,aAAa;gBAC7C;YACJ;YACA,sEAAsE;YACtE,yCAAyC;YACzC,MAAM,OAAO,IAAI,YAAY;YAC7B,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC;YAC/B,MAAM,CAAC,UAAU,EAAE,GAAG,WAAW,GAAG,KAAK,QAAQ,CAAC,GAAG;YACrD,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,GAAG,WAAW,IAAI;YACtC,OAAO;QACX;IACJ;IACA,WAAW;QACP,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC;YACxB,IAAI,OAAO,EAAE,aAAa,CAAC,SAAS,aAAa;gBAC7C,OAAO,EAAE,UAAU,CAAC,IAAI,WAAW;YACvC;QACJ;IACJ;IACA,cAAc;QACV,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC;YACxB,IAAI,OAAO,EAAE,aAAa,CAAC,SAAS,aAAa;gBAC7C,OAAO,EAAE,UAAU,CAAC,IAAI,WAAW;YACvC;QACJ;IACJ;AACJ;AACA,MAAM,iBAAiB,aAAa;IAAC;IAAK;IAAK;IAAK;CAAI;AACxD,SAAS,aAAa,CAAC,EAAE,OAAO;IAC5B,IAAI,OAAO,MAAM,UAAU;QACvB,OAAO;IACX;IACA,MAAM,SAAS,IAAI,OAAO;IAC1B,MAAM,SAAS,OAAO,eAAe;IACrC,IAAI,CAAC,YAAY,SAAS;QACtB,OAAO;IACX;IACA,IAAI,eAAe,GAAG,QAAQ,UAAU;QACpC,OAAO;IACX;IACA,2EAA2E;IAC3E,6EAA6E;IAC7E,4CAA4C;IAC5C,OAAO,QAAQ;IACf,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,GAAG;QAC3B,OAAO;IACX;IACA,iBAAiB;IACjB,IAAI,MAAM,CAAC,EAAE,KAAK,GAAG;QACjB,OAAO;IACX;IACA,8BAA8B;IAC9B,IAAI,MAAM,CAAC,EAAE,KAAK,IAAI;QAClB,OAAO;IACX;IACA,IAAI,MAAM,CAAC,EAAE,KAAK,OAAO,MAAM,CAAC,EAAE,IAAI,MAAM,MAAM,CAAC,EAAE,IAAI,IAAI;QACzD,OAAO;IACX;IACA,IAAI,MAAM,CAAC,EAAE,KAAK,OAAO,MAAM,CAAC,EAAE,KAAK,KAAK;QACxC,OAAO;IACX;IACA,mBAAmB;IACnB,IAAI,MAAM,CAAC,EAAE,KAAK,KAAK;QACnB,OAAO;IACX;IACA,eAAe;IACf,IAAI,MAAM,CAAC,EAAE,KAAK,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG,WAAW,MAAM,aAAa;QAChE,OAAO;IACX;IACA,mBAAmB;IACnB,IAAI,MAAM,CAAC,EAAE,KAAK,OAAO,MAAM,CAAC,EAAE,KAAK,KAAK;QACxC,OAAO;IACX;IACA,2DAA2D;IAC3D,IAAI,MAAM,CAAC,EAAE,KAAK,OAAO,MAAM,CAAC,EAAE,KAAK,KAAK,MAAM,CAAC,EAAE,KAAK,GAAG;QACzD,OAAO;IACX;IACA,uBAAuB;IACvB,IAAI,MAAM,CAAC,EAAE,KAAK,OAAO,MAAM,CAAC,EAAE,KAAK,KAAK,MAAM,CAAC,EAAE,KAAK,GAAG;QACzD,OAAO;IACX;IACA,IAAI,MAAM,CAAC,EAAE,KAAK,OAAO,MAAM,CAAC,EAAE,KAAK,MAAM,MAAM,CAAC,EAAE,KAAK,KAAK;QAC5D,OAAO;IACX;IACA,IAAI,MAAM,CAAC,EAAE,KAAK,OAAO,MAAM,CAAC,EAAE,KAAK,KAAK,MAAM,CAAC,EAAE,KAAK,KAAK;QAC3D,OAAO;IACX;IACA,qBAAqB;IACrB,IAAI,MAAM,CAAC,EAAE,KAAK,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,MAAM,IAAI;QAChD,OAAO;IACX;IACA,MAAM,cAAc,aAAa,YAAY;IAC7C,iBAAiB;IACjB,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,GAAG,MAAM,OAAO,CAAC,aAAa;QAC3C,OAAO;IACX;IACA,oBAAoB;IACpB,IAAI,aAAa;QACb,OAAO;IACX;IACA,KAAK,MAAM,SAAS,OAAQ;QACxB,IAAI,QAAQ,KAAK,QAAQ,KAAK;YAC1B,OAAO;QACX;IACJ;IACA,OAAO;AACX;AACA,SAAS,aAAa,CAAC,EAAE,OAAO;IAC5B,IAAI,OAAO,MAAM,UAAU;QACvB,OAAO;IACX;IACA,MAAM,SAAS,IAAI,OAAO;IAC1B,MAAM,WAAW,OAAO,eAAe;IACvC,IAAI,CAAC,YAAY,WAAW;QACxB,OAAO;IACX;IACA,IAAI,eAAe,GAAG,UAAU,UAAU;QACtC,OAAO;IACX;IACA,2EAA2E;IAC3E,2EAA2E;IAC3E,kDAAkD;IAClD,2EAA2E;IAC3E,wEAAwE;IACxE,OAAO,WAAW;IAClB,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,GAAG;QAC3B,OAAO;IACX;IACA,sBAAsB;IACtB,IAAI,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,GAAG;QACnB,OAAO;IACX;IACA,mBAAmB;IACnB,IAAI,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAAK;QACrB,OAAO;IACX;IACA,wCAAwC;IACxC,IAAI,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,QAAQ;QACxB,OAAO;IACX;IACA,yCAAyC;IACzC,IAAI,QAAQ,CAAC,EAAE,KAAK,QAAQ,QAAQ,CAAC,EAAE,KAAK,UAAU,QAAQ,CAAC,EAAE,KAAK,GAAG;QACrE,OAAO;IACX;IACA,0CAA0C;IAC1C,IAAI,QAAQ,CAAC,EAAE,KAAK,SAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,GAAG;QACnB,OAAO;IACX;IACA,0CAA0C;IAC1C,IAAI,QAAQ,CAAC,EAAE,KAAK,UAAU,QAAQ,CAAC,EAAE,GAAG,OAAO;QAC/C,8CAA8C;QAC9C,IAAI,QAAQ,CAAC,EAAE,KAAK,UAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,GAAG;YACnB,OAAO;QACX;QACA,0DAA0D;QAC1D,IAAI,QAAQ,CAAC,EAAE,KAAK,UAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,KAChB,QAAQ,CAAC,EAAE,KAAK,GAAG;YACnB,OAAO;QACX;QACA,sBAAsB;QACtB,IAAI,QAAQ,CAAC,EAAE,KAAK,UAAU,QAAQ,CAAC,EAAE,KAAK,GAAG;YAC7C,OAAO;QACX;QACA,+BAA+B;QAC/B,IAAI,QAAQ,CAAC,EAAE,KAAK,UAAU,QAAQ,CAAC,EAAE,KAAK,KAAK,QAAQ,CAAC,EAAE,KAAK,OAAO;YACtE,OAAO;QACX;QACA,4BAA4B;QAC5B,IAAI,QAAQ,CAAC,EAAE,KAAK,UAAU,QAAQ,CAAC,EAAE,IAAI,QAAQ,QAAQ,CAAC,EAAE,IAAI,MAAM;YACtE,OAAO;QACX;QACA,kCAAkC;QAClC,OAAO;IACX;IACA,sBAAsB;IACtB,IAAI,QAAQ,CAAC,EAAE,KAAK,UAAU,QAAQ,CAAC,EAAE,KAAK,OAAO;QACjD,OAAO;IACX;IACA,qBAAqB;IACrB,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,MAAM,MAAM,QAAQ;QACnC,OAAO;IACX;IACA,2BAA2B;IAC3B,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,MAAM,MAAM,QAAQ;QACnC,OAAO;IACX;IACA,OAAO;AACX;AACA,SAAS,WAAW,CAAC,EAAE,OAAO;IAC1B,IAAI,aAAa,GAAG,UAAU;QAC1B,OAAO;IACX;IACA,IAAI,aAAa,GAAG,UAAU;QAC1B,OAAO;IACX;IACA,OAAO;AACX;AACA,SAAS,UAAU,GAAG;IAClB,OAAO,OAAO,IAAI,GAAG,KAAK;AAC9B;AACA,SAAS,UAAU,OAAO,EAAE,SAAS;IACjC,IAAI,UAAU,UAAU;QACpB,OAAO,QAAQ,GAAG,CAAC;IACvB,OACK;QACD,MAAM,cAAc,OAAO,CAAC,UAAU;QACtC,IAAI,MAAM,OAAO,CAAC,cAAc;YAC5B,OAAO,YAAY,IAAI,CAAC;QAC5B,OACK;YACD,OAAO;QACX;IACJ;AACJ;AACA,0DAA0D;AAC1D,EAAE;AACF,qEAAqE;AACrE,gCAAgC;AAChC,EAAE;AACF,+EAA+E;AAC/E,gFAAgF;AAChF,+EAA+E;AAC/E,4EAA4E;AAC5E,wEAAwE;AACxE,+EAA+E;AAC/E,uEAAuE;AACvE,wCAAwC;AACxC,EAAE;AACF,6EAA6E;AAC7E,2EAA2E;AAC3E,8EAA8E;AAC9E,yEAAyE;AACzE,gFAAgF;AAChF,gFAAgF;AAChF,YAAY;AACZ,SAAS,OAAO,OAAO,EAAE,UAAU,CAAC,CAAC;IACjC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;IAC9B,4EAA4E;IAC5E,4EAA4E;IAC5E,wBAAwB;IACxB,IAAI,WAAW,QAAQ,EAAE,EAAE,UAAU;QACjC,OAAO,QAAQ,EAAE;IACrB;IACA,MAAM,sBAAsB,QAAQ,MAAM,EAAE;IAC5C,IAAI,WAAW,qBAAqB,UAAU;QAC1C,OAAO;IACX;IACA,MAAM,oBAAoB,QAAQ,IAAI,EAAE;IACxC,IAAI,WAAW,mBAAmB,UAAU;QACxC,OAAO;IACX;IACA,2BAA2B;IAC3B,MAAM,iCAAiC,QAAQ,cAAc,EAAE,UAAU;IACzE,IAAI,WAAW,gCAAgC,UAAU;QACrD,OAAO;IACX;IACA,qDAAqD;IACrD,IAAI,OAAO,QAAQ,OAAO,KAAK,YAAY,QAAQ,OAAO,KAAK,MAAM;QACjE,OAAO;IACX;IACA,0EAA0E;IAC1E,4EAA4E;IAC5E,2EAA2E;IAC3E,kBAAkB;IAClB,IAAI,aAAa,cAAc;QAC3B,wHAAwH;QACxH,MAAM,mBAAmB,UAAU,QAAQ,OAAO,EAAE;QACpD,IAAI,aAAa,kBAAkB,UAAU;YACzC,OAAO;QACX;QACA,oHAAoH;QACpH,MAAM,iBAAiB,UAAU,QAAQ,OAAO,EAAE;QAClD,IAAI,WAAW,gBAAgB,UAAU;YACrC,OAAO;QACX;QACA,uEAAuE;QACvE,qEAAqE;QACrE,8CAA8C;QAC9C,OAAO;IACX;IACA,yEAAyE;IACzE,IAAI,aAAa,UAAU;QACvB,+EAA+E;QAC/E,MAAM,cAAc,UAAU,QAAQ,OAAO,EAAE;QAC/C,IAAI,WAAW,aAAa,UAAU;YAClC,OAAO;QACX;QACA,uEAAuE;QACvE,qEAAqE;QACrE,8CAA8C;QAC9C,OAAO;IACX;IACA,IAAI,aAAa,UAAU;QACvB,yEAAyE;QACzE,yCAAyC;QACzC,uHAAuH;QACvH,MAAM,UAAU,UAAU,QAAQ,OAAO,EAAE;QAC3C,IAAI,WAAW,SAAS,UAAU;YAC9B,OAAO;QACX;QACA,sFAAsF;QACtF,wEAAwE;QACxE,0DAA0D;QAC1D,yFAAyF;QACzF,MAAM,sBAAsB,UAAU,QAAQ,OAAO,EAAE;QACvD,MAAM,2BAA2B,mBAAmB;QACpD,sDAAsD;QACtD,4EAA4E;QAC5E,0EAA0E;QAC1E,4EAA4E;QAC5E,0DAA0D;QAC1D,KAAK,MAAM,QAAQ,yBAAyB,OAAO,GAAI;YACnD,IAAI,WAAW,MAAM,UAAU;gBAC3B,OAAO;YACX;QACJ;QACA,+EAA+E;QAC/E,wEAAwE;QACxE,0DAA0D;QAC1D,yFAAyF;QACzF,MAAM,gBAAgB,UAAU,QAAQ,OAAO,EAAE;QACjD,MAAM,qBAAqB,mBAAmB;QAC9C,sDAAsD;QACtD,4EAA4E;QAC5E,0EAA0E;QAC1E,4EAA4E;QAC5E,0DAA0D;QAC1D,KAAK,MAAM,QAAQ,mBAAmB,OAAO,GAAI;YAC7C,IAAI,WAAW,MAAM,UAAU;gBAC3B,OAAO;YACX;QACJ;QACA,uEAAuE;QACvE,qEAAqE;QACrE,8CAA8C;QAC9C,OAAO;IACX;IACA,IAAI,aAAa,UAAU;QACvB,kIAAkI;QAClI,MAAM,eAAe,UAAU,QAAQ,OAAO,EAAE;QAChD,IAAI,WAAW,cAAc,UAAU;YACnC,OAAO;QACX;QACA,uEAAuE;QACvE,qEAAqE;QACrE,8CAA8C;QAC9C,OAAO;IACX;IACA,2DAA2D;IAC3D,MAAM,YAAY,UAAU,QAAQ,OAAO,EAAE;IAC7C,IAAI,WAAW,WAAW,UAAU;QAChC,OAAO;IACX;IACA,uCAAuC;IACvC,MAAM,gBAAgB,UAAU,QAAQ,OAAO,EAAE;IACjD,MAAM,qBAAqB,mBAAmB;IAC9C,sDAAsD;IACtD,4EAA4E;IAC5E,0EAA0E;IAC1E,4EAA4E;IAC5E,0DAA0D;IAC1D,KAAK,MAAM,QAAQ,mBAAmB,OAAO,GAAI;QAC7C,IAAI,WAAW,MAAM,UAAU;YAC3B,OAAO;QACX;IACJ;IACA,gBAAgB;IAChB,4FAA4F;IAC5F,MAAM,iBAAiB,UAAU,QAAQ,OAAO,EAAE;IAClD,IAAI,WAAW,gBAAgB,UAAU;QACrC,OAAO;IACX;IACA,uEAAuE;IACvE,mBAAmB;IACnB,MAAM,iBAAiB,UAAU,QAAQ,OAAO,EAAE;IAClD,IAAI,WAAW,gBAAgB,UAAU;QACrC,OAAO;IACX;IACA,SAAS;IACT,iBAAiB;IACjB,MAAM,eAAe,UAAU,QAAQ,OAAO,EAAE;IAChD,IAAI,WAAW,cAAc,UAAU;QACnC,OAAO;IACX;IACA,iFAAiF;IACjF,YAAY;IACZ,MAAM,UAAU,UAAU,QAAQ,OAAO,EAAE;IAC3C,IAAI,WAAW,SAAS,UAAU;QAC9B,OAAO;IACX;IACA,wCAAwC;IACxC,MAAM,mBAAmB,UAAU,QAAQ,OAAO,EAAE;IACpD,IAAI,WAAW,kBAAkB,UAAU;QACvC,OAAO;IACX;IACA,MAAM,aAAa,UAAU,QAAQ,OAAO,EAAE;IAC9C,IAAI,WAAW,YAAY,UAAU;QACjC,OAAO;IACX;IACA,MAAM,eAAe,UAAU,QAAQ,OAAO,EAAE;IAChD,IAAI,WAAW,cAAc,UAAU;QACnC,OAAO;IACX;IACA,MAAM,YAAY,UAAU,QAAQ,OAAO,EAAE;IAC7C,IAAI,WAAW,WAAW,UAAU;QAChC,OAAO;IACX;IACA,0BAA0B;IAC1B,8GAA8G;IAC9G,MAAM,mBAAmB,UAAU,QAAQ,OAAO,EAAE;IACpD,IAAI,WAAW,kBAAkB,UAAU;QACvC,OAAO;IACX;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 5153, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@arcjet/env/index.js"], "sourcesContent": ["function platform(env) {\n    if (typeof env[\"FLY_APP_NAME\"] === \"string\" && env[\"FLY_APP_NAME\"] !== \"\") {\n        return \"fly-io\";\n    }\n    if (typeof env[\"VERCEL\"] === \"string\" && env[\"VERCEL\"] === \"1\") {\n        return \"vercel\";\n    }\n    // https://render.com/docs/environment-variables\n    if (typeof env[\"RENDER\"] === \"string\" && env[\"RENDER\"] === \"true\") {\n        return \"render\";\n    }\n}\nfunction isDevelopment(env) {\n    return (env.NODE_ENV === \"development\" ||\n        env.MODE === \"development\" ||\n        env.ARCJET_ENV === \"development\");\n}\nfunction logLevel(env) {\n    const level = env[\"ARCJET_LOG_LEVEL\"];\n    switch (level) {\n        case \"debug\":\n        case \"info\":\n        case \"warn\":\n        case \"error\":\n            return level;\n        default:\n            // Default to warn if not set\n            return \"warn\";\n    }\n}\nconst baseUrlAllowed = [\n    \"https://decide.arcjet.com\",\n    \"https://decide.arcjettest.com\",\n    \"https://fly.decide.arcjet.com\",\n    \"https://fly.decide.arcjettest.com\",\n    \"https://decide.arcjet.orb.local:4082\",\n];\nfunction baseUrl(env) {\n    // TODO(#90): Remove this conditional before 1.0.0\n    if (isDevelopment(env)) {\n        if (env[\"ARCJET_BASE_URL\"]) {\n            return env[\"ARCJET_BASE_URL\"];\n        }\n        // If we're running on fly.io, use the Arcjet Decide Service hosted on fly\n        // Ref: https://fly.io/docs/machines/runtime-environment/#environment-variables\n        if (platform(env) === \"fly-io\") {\n            return \"https://fly.decide.arcjet.com\";\n        }\n        return \"https://decide.arcjet.com\";\n    }\n    else {\n        // Use ARCJET_BASE_URL if it is set and belongs to our allowlist; otherwise\n        // use the hardcoded default.\n        if (typeof env[\"ARCJET_BASE_URL\"] === \"string\" &&\n            baseUrlAllowed.includes(env[\"ARCJET_BASE_URL\"])) {\n            return env[\"ARCJET_BASE_URL\"];\n        }\n        // If we're running on fly.io, use the Arcjet Decide Service hosted on fly\n        // Ref: https://fly.io/docs/machines/runtime-environment/#environment-variables\n        if (platform(env) === \"fly-io\") {\n            return \"https://fly.decide.arcjet.com\";\n        }\n        return \"https://decide.arcjet.com\";\n    }\n}\nfunction apiKey(env) {\n    const key = env[\"ARCJET_KEY\"];\n    if (typeof key === \"string\" && key.startsWith(\"ajkey_\")) {\n        return key;\n    }\n}\n\nexport { apiKey, baseUrl, isDevelopment, logLevel, platform };\n"], "names": [], "mappings": ";;;;;;;AAAA,SAAS,SAAS,GAAG;IACjB,IAAI,OAAO,GAAG,CAAC,eAAe,KAAK,YAAY,GAAG,CAAC,eAAe,KAAK,IAAI;QACvE,OAAO;IACX;IACA,IAAI,OAAO,GAAG,CAAC,SAAS,KAAK,YAAY,GAAG,CAAC,SAAS,KAAK,KAAK;QAC5D,OAAO;IACX;IACA,gDAAgD;IAChD,IAAI,OAAO,GAAG,CAAC,SAAS,KAAK,YAAY,GAAG,CAAC,SAAS,KAAK,QAAQ;QAC/D,OAAO;IACX;AACJ;AACA,SAAS,cAAc,GAAG;IACtB,OAAQ,IAAI,QAAQ,KAAK,iBACrB,IAAI,IAAI,KAAK,iBACb,IAAI,UAAU,KAAK;AAC3B;AACA,SAAS,SAAS,GAAG;IACjB,MAAM,QAAQ,GAAG,CAAC,mBAAmB;IACrC,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX;YACI,6BAA6B;YAC7B,OAAO;IACf;AACJ;AACA,MAAM,iBAAiB;IACnB;IACA;IACA;IACA;IACA;CACH;AACD,SAAS,QAAQ,GAAG;IAChB,kDAAkD;IAClD,IAAI,cAAc,MAAM;QACpB,IAAI,GAAG,CAAC,kBAAkB,EAAE;YACxB,OAAO,GAAG,CAAC,kBAAkB;QACjC;QACA,0EAA0E;QAC1E,+EAA+E;QAC/E,IAAI,SAAS,SAAS,UAAU;YAC5B,OAAO;QACX;QACA,OAAO;IACX,OACK;QACD,2EAA2E;QAC3E,6BAA6B;QAC7B,IAAI,OAAO,GAAG,CAAC,kBAAkB,KAAK,YAClC,eAAe,QAAQ,CAAC,GAAG,CAAC,kBAAkB,GAAG;YACjD,OAAO,GAAG,CAAC,kBAAkB;QACjC;QACA,0EAA0E;QAC1E,+EAA+E;QAC/E,IAAI,SAAS,SAAS,UAAU;YAC5B,OAAO;QACX;QACA,OAAO;IACX;AACJ;AACA,SAAS,OAAO,GAAG;IACf,MAAM,MAAM,GAAG,CAAC,aAAa;IAC7B,IAAI,OAAO,QAAQ,YAAY,IAAI,UAAU,CAAC,WAAW;QACrD,OAAO;IACX;AACJ", "ignoreList": [0]}}, {"offset": {"line": 5234, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@arcjet/sprintf/index.js"], "sourcesContent": ["function bigintReplacer(key, value) {\n    if (typeof value === \"bigint\") {\n        return \"[BigInt]\";\n    }\n    return value;\n}\n// TODO: Deduplicate this and logger implementation\nfunction tryStringify(o) {\n    try {\n        return JSON.stringify(o, bigintReplacer);\n    }\n    catch {\n        return `\"[Circular]\"`;\n    }\n}\nconst PERCENT_CODE = 37; /* % */\nconst LOWERCASE_D_CODE = 100; /* d */\nconst LOWERCASE_F_CODE = 102; /* f */\nconst LOWERCASE_I_CODE = 105; /* i */\nconst UPPERCASE_O_CODE = 79; /* O */\nconst LOWERCASE_O_CODE = 111; /* o */\nconst LOWERCASE_J_CODE = 106; /* j */\nconst LOWERCASE_S_CODE = 115; /* s */\n// Heavily based on https://github.com/pinojs/quick-format-unescaped\n//\n// The MIT License (MIT)\n//\n// Copyright (c) 2016-2019 <PERSON>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n// SOFTWARE.\nfunction sprintf(str, ...args) {\n    if (typeof str !== \"string\") {\n        throw new TypeError(\"First argument must be a string\");\n    }\n    const argsLength = args.length;\n    if (argsLength === 0) {\n        return str;\n    }\n    let output = \"\";\n    let argIdx = 0;\n    let lastPosition = -1;\n    const strLength = str.length;\n    for (let i = 0; i < strLength;) {\n        if (str.charCodeAt(i) === PERCENT_CODE && i + 1 < strLength) {\n            lastPosition = lastPosition > -1 ? lastPosition : 0;\n            switch (str.charCodeAt(i + 1)) {\n                case LOWERCASE_D_CODE:\n                case LOWERCASE_F_CODE: {\n                    if (argIdx >= argsLength) {\n                        break;\n                    }\n                    const arg = args[argIdx];\n                    if (typeof arg !== \"number\") {\n                        break;\n                    }\n                    if (lastPosition < i) {\n                        output += str.slice(lastPosition, i);\n                    }\n                    output += arg;\n                    lastPosition = i + 2;\n                    i++;\n                    break;\n                }\n                case LOWERCASE_I_CODE: {\n                    if (argIdx >= argsLength) {\n                        break;\n                    }\n                    const arg = args[argIdx];\n                    if (typeof arg !== \"number\") {\n                        break;\n                    }\n                    if (lastPosition < i) {\n                        output += str.slice(lastPosition, i);\n                    }\n                    output += Math.floor(arg);\n                    lastPosition = i + 2;\n                    i++;\n                    break;\n                }\n                case UPPERCASE_O_CODE:\n                case LOWERCASE_O_CODE:\n                case LOWERCASE_J_CODE: {\n                    if (argIdx >= argsLength) {\n                        break;\n                    }\n                    const arg = args[argIdx];\n                    if (arg === undefined) {\n                        break;\n                    }\n                    if (lastPosition < i) {\n                        output += str.slice(lastPosition, i);\n                    }\n                    if (typeof arg === \"string\") {\n                        output += `'${arg}'`;\n                        lastPosition = i + 2;\n                        i++;\n                        break;\n                    }\n                    if (typeof arg === \"bigint\") {\n                        output += `\"[BigInt]\"`;\n                        lastPosition = i + 2;\n                        i++;\n                        break;\n                    }\n                    if (typeof arg === \"function\") {\n                        output += arg.name || \"<anonymous>\";\n                        lastPosition = i + 2;\n                        i++;\n                        break;\n                    }\n                    output += tryStringify(arg);\n                    lastPosition = i + 2;\n                    i++;\n                    break;\n                }\n                case LOWERCASE_S_CODE: {\n                    if (argIdx >= argsLength) {\n                        break;\n                    }\n                    const arg = args[argIdx];\n                    if (typeof arg !== \"string\") {\n                        break;\n                    }\n                    if (lastPosition < i) {\n                        output += str.slice(lastPosition, i);\n                    }\n                    output += arg;\n                    lastPosition = i + 2;\n                    i++;\n                    break;\n                }\n                case PERCENT_CODE: {\n                    if (lastPosition < i) {\n                        output += str.slice(lastPosition, i);\n                    }\n                    output += \"%\";\n                    lastPosition = i + 2;\n                    i++;\n                    argIdx--;\n                    break;\n                }\n            }\n            ++argIdx;\n        }\n        ++i;\n    }\n    if (lastPosition === -1) {\n        return str;\n    }\n    if (lastPosition < strLength) {\n        output += str.slice(lastPosition);\n    }\n    return output;\n}\n\nexport { sprintf as default };\n"], "names": [], "mappings": ";;;AAAA,SAAS,eAAe,GAAG,EAAE,KAAK;IAC9B,IAAI,OAAO,UAAU,UAAU;QAC3B,OAAO;IACX;IACA,OAAO;AACX;AACA,mDAAmD;AACnD,SAAS,aAAa,CAAC;IACnB,IAAI;QACA,OAAO,KAAK,SAAS,CAAC,GAAG;IAC7B,EACA,OAAM;QACF,OAAO,CAAC,YAAY,CAAC;IACzB;AACJ;AACA,MAAM,eAAe,IAAI,KAAK;AAC9B,MAAM,mBAAmB,KAAK,KAAK;AACnC,MAAM,mBAAmB,KAAK,KAAK;AACnC,MAAM,mBAAmB,KAAK,KAAK;AACnC,MAAM,mBAAmB,IAAI,KAAK;AAClC,MAAM,mBAAmB,KAAK,KAAK;AACnC,MAAM,mBAAmB,KAAK,KAAK;AACnC,MAAM,mBAAmB,KAAK,KAAK;AACnC,oEAAoE;AACpE,EAAE;AACF,wBAAwB;AACxB,EAAE;AACF,8CAA8C;AAC9C,EAAE;AACF,+EAA+E;AAC/E,gFAAgF;AAChF,+EAA+E;AAC/E,4EAA4E;AAC5E,wEAAwE;AACxE,2DAA2D;AAC3D,EAAE;AACF,6EAA6E;AAC7E,sDAAsD;AACtD,EAAE;AACF,6EAA6E;AAC7E,2EAA2E;AAC3E,8EAA8E;AAC9E,yEAAyE;AACzE,gFAAgF;AAChF,gFAAgF;AAChF,YAAY;AACZ,SAAS,QAAQ,GAAG,EAAE,GAAG,IAAI;IACzB,IAAI,OAAO,QAAQ,UAAU;QACzB,MAAM,IAAI,UAAU;IACxB;IACA,MAAM,aAAa,KAAK,MAAM;IAC9B,IAAI,eAAe,GAAG;QAClB,OAAO;IACX;IACA,IAAI,SAAS;IACb,IAAI,SAAS;IACb,IAAI,eAAe,CAAC;IACpB,MAAM,YAAY,IAAI,MAAM;IAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,WAAY;QAC5B,IAAI,IAAI,UAAU,CAAC,OAAO,gBAAgB,IAAI,IAAI,WAAW;YACzD,eAAe,eAAe,CAAC,IAAI,eAAe;YAClD,OAAQ,IAAI,UAAU,CAAC,IAAI;gBACvB,KAAK;gBACL,KAAK;oBAAkB;wBACnB,IAAI,UAAU,YAAY;4BACtB;wBACJ;wBACA,MAAM,MAAM,IAAI,CAAC,OAAO;wBACxB,IAAI,OAAO,QAAQ,UAAU;4BACzB;wBACJ;wBACA,IAAI,eAAe,GAAG;4BAClB,UAAU,IAAI,KAAK,CAAC,cAAc;wBACtC;wBACA,UAAU;wBACV,eAAe,IAAI;wBACnB;wBACA;oBACJ;gBACA,KAAK;oBAAkB;wBACnB,IAAI,UAAU,YAAY;4BACtB;wBACJ;wBACA,MAAM,MAAM,IAAI,CAAC,OAAO;wBACxB,IAAI,OAAO,QAAQ,UAAU;4BACzB;wBACJ;wBACA,IAAI,eAAe,GAAG;4BAClB,UAAU,IAAI,KAAK,CAAC,cAAc;wBACtC;wBACA,UAAU,KAAK,KAAK,CAAC;wBACrB,eAAe,IAAI;wBACnB;wBACA;oBACJ;gBACA,KAAK;gBACL,KAAK;gBACL,KAAK;oBAAkB;wBACnB,IAAI,UAAU,YAAY;4BACtB;wBACJ;wBACA,MAAM,MAAM,IAAI,CAAC,OAAO;wBACxB,IAAI,QAAQ,WAAW;4BACnB;wBACJ;wBACA,IAAI,eAAe,GAAG;4BAClB,UAAU,IAAI,KAAK,CAAC,cAAc;wBACtC;wBACA,IAAI,OAAO,QAAQ,UAAU;4BACzB,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;4BACpB,eAAe,IAAI;4BACnB;4BACA;wBACJ;wBACA,IAAI,OAAO,QAAQ,UAAU;4BACzB,UAAU,CAAC,UAAU,CAAC;4BACtB,eAAe,IAAI;4BACnB;4BACA;wBACJ;wBACA,IAAI,OAAO,QAAQ,YAAY;4BAC3B,UAAU,IAAI,IAAI,IAAI;4BACtB,eAAe,IAAI;4BACnB;4BACA;wBACJ;wBACA,UAAU,aAAa;wBACvB,eAAe,IAAI;wBACnB;wBACA;oBACJ;gBACA,KAAK;oBAAkB;wBACnB,IAAI,UAAU,YAAY;4BACtB;wBACJ;wBACA,MAAM,MAAM,IAAI,CAAC,OAAO;wBACxB,IAAI,OAAO,QAAQ,UAAU;4BACzB;wBACJ;wBACA,IAAI,eAAe,GAAG;4BAClB,UAAU,IAAI,KAAK,CAAC,cAAc;wBACtC;wBACA,UAAU;wBACV,eAAe,IAAI;wBACnB;wBACA;oBACJ;gBACA,KAAK;oBAAc;wBACf,IAAI,eAAe,GAAG;4BAClB,UAAU,IAAI,KAAK,CAAC,cAAc;wBACtC;wBACA,UAAU;wBACV,eAAe,IAAI;wBACnB;wBACA;wBACA;oBACJ;YACJ;YACA,EAAE;QACN;QACA,EAAE;IACN;IACA,IAAI,iBAAiB,CAAC,GAAG;QACrB,OAAO;IACX;IACA,IAAI,eAAe,WAAW;QAC1B,UAAU,IAAI,KAAK,CAAC;IACxB;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 5418, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@arcjet/logger/index.js"], "sourcesContent": ["import format from '@arcjet/sprintf';\n\nfunction bigintReplacer(key, value) {\n    if (typeof value === \"bigint\") {\n        return \"[BigInt]\";\n    }\n    return value;\n}\n// TODO: Deduplicate this and sprintf implementation\nfunction tryStringify(o) {\n    try {\n        return JSON.stringify(o, bigintReplacer);\n    }\n    catch {\n        return \"[Circular]\";\n    }\n}\nconst PREFIX = \"✦Aj\";\nfunction getMessage(obj, msg, args) {\n    // The first argument was the message so juggle the args\n    if (typeof obj === \"string\") {\n        args = [msg, ...args];\n        msg = obj;\n    }\n    // Prefer a string message over `obj.msg`, as per Pino:\n    // https://github.com/pinojs/pino/blob/8db130eba0439e61c802448d31eb1998cebfbc98/docs/api.md#message-string\n    if (typeof msg === \"string\") {\n        return format(msg, ...args);\n    }\n    if (typeof obj === \"object\" &&\n        obj !== null &&\n        \"msg\" in obj &&\n        typeof obj.msg === \"string\") {\n        return format(obj.msg, [msg, ...args]);\n    }\n}\nfunction getOutput(obj, msg, args) {\n    let output = getMessage(obj, msg, args);\n    if (typeof output !== \"string\") {\n        return;\n    }\n    if (typeof obj === \"object\" && obj !== null) {\n        for (const [key, value] of Object.entries(obj)) {\n            output += `\\n      ${key}: ${tryStringify(value)}`;\n        }\n    }\n    return output;\n}\nclass Logger {\n    #logLevel;\n    constructor(opts) {\n        if (typeof opts.level !== \"string\") {\n            throw new Error(`Invalid log level`);\n        }\n        switch (opts.level) {\n            case \"debug\":\n                this.#logLevel = 0;\n                break;\n            case \"info\":\n                this.#logLevel = 1;\n                break;\n            case \"warn\":\n                this.#logLevel = 2;\n                break;\n            case \"error\":\n                this.#logLevel = 3;\n                break;\n            default: {\n                throw new Error(`Unknown log level: ${opts.level}`);\n            }\n        }\n    }\n    debug(obj, msg, ...args) {\n        if (this.#logLevel <= 0) {\n            const output = getOutput(obj, msg, args);\n            if (typeof output !== \"undefined\") {\n                console.debug(`${PREFIX} DEBUG ${output}`);\n            }\n        }\n    }\n    info(obj, msg, ...args) {\n        if (this.#logLevel <= 1) {\n            const output = getOutput(obj, msg, args);\n            if (typeof output !== \"undefined\") {\n                console.info(`${PREFIX} INFO ${output}`);\n            }\n        }\n    }\n    warn(obj, msg, ...args) {\n        if (this.#logLevel <= 2) {\n            const output = getOutput(obj, msg, args);\n            if (typeof output !== \"undefined\") {\n                console.warn(`${PREFIX} WARN ${output}`);\n            }\n        }\n    }\n    error(obj, msg, ...args) {\n        if (this.#logLevel <= 3) {\n            const output = getOutput(obj, msg, args);\n            if (typeof output !== \"undefined\") {\n                console.error(`${PREFIX} ERROR ${output}`);\n            }\n        }\n    }\n}\n\nexport { Logger };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,eAAe,GAAG,EAAE,KAAK;IAC9B,IAAI,OAAO,UAAU,UAAU;QAC3B,OAAO;IACX;IACA,OAAO;AACX;AACA,oDAAoD;AACpD,SAAS,aAAa,CAAC;IACnB,IAAI;QACA,OAAO,KAAK,SAAS,CAAC,GAAG;IAC7B,EACA,OAAM;QACF,OAAO;IACX;AACJ;AACA,MAAM,SAAS;AACf,SAAS,WAAW,GAAG,EAAE,GAAG,EAAE,IAAI;IAC9B,wDAAwD;IACxD,IAAI,OAAO,QAAQ,UAAU;QACzB,OAAO;YAAC;eAAQ;SAAK;QACrB,MAAM;IACV;IACA,uDAAuD;IACvD,0GAA0G;IAC1G,IAAI,OAAO,QAAQ,UAAU;QACzB,OAAO,CAAA,GAAA,oJAAA,CAAA,UAAM,AAAD,EAAE,QAAQ;IAC1B;IACA,IAAI,OAAO,QAAQ,YACf,QAAQ,QACR,SAAS,OACT,OAAO,IAAI,GAAG,KAAK,UAAU;QAC7B,OAAO,CAAA,GAAA,oJAAA,CAAA,UAAM,AAAD,EAAE,IAAI,GAAG,EAAE;YAAC;eAAQ;SAAK;IACzC;AACJ;AACA,SAAS,UAAU,GAAG,EAAE,GAAG,EAAE,IAAI;IAC7B,IAAI,SAAS,WAAW,KAAK,KAAK;IAClC,IAAI,OAAO,WAAW,UAAU;QAC5B;IACJ;IACA,IAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;QACzC,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,KAAM;YAC5C,UAAU,CAAC,QAAQ,EAAE,IAAI,EAAE,EAAE,aAAa,QAAQ;QACtD;IACJ;IACA,OAAO;AACX;AACA,MAAM;IACF,CAAA,QAAS,CAAC;IACV,YAAY,IAAI,CAAE;QACd,IAAI,OAAO,KAAK,KAAK,KAAK,UAAU;YAChC,MAAM,IAAI,MAAM,CAAC,iBAAiB,CAAC;QACvC;QACA,OAAQ,KAAK,KAAK;YACd,KAAK;gBACD,IAAI,CAAC,CAAA,QAAS,GAAG;gBACjB;YACJ,KAAK;gBACD,IAAI,CAAC,CAAA,QAAS,GAAG;gBACjB;YACJ,KAAK;gBACD,IAAI,CAAC,CAAA,QAAS,GAAG;gBACjB;YACJ,KAAK;gBACD,IAAI,CAAC,CAAA,QAAS,GAAG;gBACjB;YACJ;gBAAS;oBACL,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,KAAK,KAAK,EAAE;gBACtD;QACJ;IACJ;IACA,MAAM,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE;QACrB,IAAI,IAAI,CAAC,CAAA,QAAS,IAAI,GAAG;YACrB,MAAM,SAAS,UAAU,KAAK,KAAK;YACnC,IAAI,OAAO,WAAW,aAAa;gBAC/B,QAAQ,KAAK,CAAC,GAAG,OAAO,OAAO,EAAE,QAAQ;YAC7C;QACJ;IACJ;IACA,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE;QACpB,IAAI,IAAI,CAAC,CAAA,QAAS,IAAI,GAAG;YACrB,MAAM,SAAS,UAAU,KAAK,KAAK;YACnC,IAAI,OAAO,WAAW,aAAa;gBAC/B,QAAQ,IAAI,CAAC,GAAG,OAAO,MAAM,EAAE,QAAQ;YAC3C;QACJ;IACJ;IACA,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE;QACpB,IAAI,IAAI,CAAC,CAAA,QAAS,IAAI,GAAG;YACrB,MAAM,SAAS,UAAU,KAAK,KAAK;YACnC,IAAI,OAAO,WAAW,aAAa;gBAC/B,QAAQ,IAAI,CAAC,GAAG,OAAO,MAAM,EAAE,QAAQ;YAC3C;QACJ;IACJ;IACA,MAAM,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE;QACrB,IAAI,IAAI,CAAC,CAAA,QAAS,IAAI,GAAG;YACrB,MAAM,SAAS,UAAU,KAAK,KAAK;YACnC,IAAI,OAAO,WAAW,aAAa;gBAC/B,QAAQ,KAAK,CAAC,GAAG,OAAO,OAAO,EAAE,QAAQ;YAC7C;QACJ;IACJ;AACJ", "ignoreList": [0]}}, {"offset": {"line": 5536, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@connectrpc/connect-web/dist/esm/assert-fetch-api.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * Asserts that the fetch API is available.\n */\nexport function assertFetchApi() {\n    try {\n        new Headers();\n    }\n    catch (_) {\n        throw new Error(\"connect-web requires the fetch API. Are you running on an old version of Node.js? Node.js is not supported in Connect for Web - please stay tuned for Connect for Node.\");\n    }\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC;;CAEC;;;AACM,SAAS;IACZ,IAAI;QACA,IAAI;IACR,EACA,OAAO,GAAG;QACN,MAAM,IAAI,MAAM;IACpB;AACJ", "ignoreList": [0]}}, {"offset": {"line": 5567, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@connectrpc/connect-web/dist/esm/connect-transport.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __await = (this && this.__await) || function (v) { return this instanceof __await ? (this.v = v, this) : new __await(v); }\nvar __asyncGenerator = (this && this.__asyncGenerator) || function (thisArg, _arguments, generator) {\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n    function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n    function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n    function fulfill(value) { resume(\"next\", value); }\n    function reject(value) { resume(\"throw\", value); }\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n};\nimport { Message, MethodIdempotency, MethodKind } from \"@bufbuild/protobuf\";\nimport { Code, ConnectError, appendHeaders, createContextValues, } from \"@connectrpc/connect\";\nimport { createClientMethodSerializers, createEnvelopeReadableStream, createMethodUrl, getJsonOptions, encodeEnvelope, runStreamingCall, runUnaryCall, compressedFlag, } from \"@connectrpc/connect/protocol\";\nimport { endStreamFlag, endStreamFromJson, errorFromJson, requestHeader, trailerDemux, transformConnectPostToGetRequest, validateResponse, } from \"@connectrpc/connect/protocol-connect\";\nimport { assertFetchApi } from \"./assert-fetch-api.js\";\n/**\n * Create a Transport for the Connect protocol, which makes unary and\n * server-streaming methods available to web browsers. It uses the fetch\n * API to make HTTP requests.\n */\nexport function createConnectTransport(options) {\n    var _a;\n    assertFetchApi();\n    const useBinaryFormat = (_a = options.useBinaryFormat) !== null && _a !== void 0 ? _a : false;\n    return {\n        async unary(service, method, signal, timeoutMs, header, message, contextValues) {\n            var _a;\n            const { serialize, parse } = createClientMethodSerializers(method, useBinaryFormat, options.jsonOptions, options.binaryOptions);\n            timeoutMs =\n                timeoutMs === undefined\n                    ? options.defaultTimeoutMs\n                    : timeoutMs <= 0\n                        ? undefined\n                        : timeoutMs;\n            return await runUnaryCall({\n                interceptors: options.interceptors,\n                signal,\n                timeoutMs,\n                req: {\n                    stream: false,\n                    service,\n                    method,\n                    url: createMethodUrl(options.baseUrl, service, method),\n                    init: {\n                        method: \"POST\",\n                        credentials: (_a = options.credentials) !== null && _a !== void 0 ? _a : \"same-origin\",\n                        redirect: \"error\",\n                        mode: \"cors\",\n                    },\n                    header: requestHeader(method.kind, useBinaryFormat, timeoutMs, header, false),\n                    contextValues: contextValues !== null && contextValues !== void 0 ? contextValues : createContextValues(),\n                    message,\n                },\n                next: async (req) => {\n                    var _a;\n                    const useGet = options.useHttpGet === true &&\n                        method.idempotency === MethodIdempotency.NoSideEffects;\n                    let body = null;\n                    if (useGet) {\n                        req = transformConnectPostToGetRequest(req, serialize(req.message), useBinaryFormat);\n                    }\n                    else {\n                        body = serialize(req.message);\n                    }\n                    const fetch = (_a = options.fetch) !== null && _a !== void 0 ? _a : globalThis.fetch;\n                    const response = await fetch(req.url, Object.assign(Object.assign({}, req.init), { headers: req.header, signal: req.signal, body }));\n                    const { isUnaryError, unaryError } = validateResponse(method.kind, useBinaryFormat, response.status, response.headers);\n                    if (isUnaryError) {\n                        throw errorFromJson((await response.json()), appendHeaders(...trailerDemux(response.headers)), unaryError);\n                    }\n                    const [demuxedHeader, demuxedTrailer] = trailerDemux(response.headers);\n                    return {\n                        stream: false,\n                        service,\n                        method,\n                        header: demuxedHeader,\n                        message: useBinaryFormat\n                            ? parse(new Uint8Array(await response.arrayBuffer()))\n                            : method.O.fromJson((await response.json()), getJsonOptions(options.jsonOptions)),\n                        trailer: demuxedTrailer,\n                    };\n                },\n            });\n        },\n        async stream(service, method, signal, timeoutMs, header, input, contextValues) {\n            var _a;\n            const { serialize, parse } = createClientMethodSerializers(method, useBinaryFormat, options.jsonOptions, options.binaryOptions);\n            function parseResponseBody(body, trailerTarget, header, signal) {\n                return __asyncGenerator(this, arguments, function* parseResponseBody_1() {\n                    const reader = createEnvelopeReadableStream(body).getReader();\n                    let endStreamReceived = false;\n                    for (;;) {\n                        const result = yield __await(reader.read());\n                        if (result.done) {\n                            break;\n                        }\n                        const { flags, data } = result.value;\n                        if ((flags & compressedFlag) === compressedFlag) {\n                            throw new ConnectError(`protocol error: received unsupported compressed output`, Code.Internal);\n                        }\n                        if ((flags & endStreamFlag) === endStreamFlag) {\n                            endStreamReceived = true;\n                            const endStream = endStreamFromJson(data);\n                            if (endStream.error) {\n                                const error = endStream.error;\n                                header.forEach((value, key) => {\n                                    error.metadata.append(key, value);\n                                });\n                                throw error;\n                            }\n                            endStream.metadata.forEach((value, key) => trailerTarget.set(key, value));\n                            continue;\n                        }\n                        yield yield __await(parse(data));\n                    }\n                    // Node wil not throw an AbortError on `read` if the\n                    // signal is aborted before `getReader` is called.\n                    // As a work around we check at the end and throw.\n                    //\n                    // Ref: https://github.com/nodejs/undici/issues/1940\n                    if (\"throwIfAborted\" in signal) {\n                        // We assume that implementations without `throwIfAborted` (old\n                        // browsers) do honor aborted signals on `read`.\n                        signal.throwIfAborted();\n                    }\n                    if (!endStreamReceived) {\n                        throw \"missing EndStreamResponse\";\n                    }\n                });\n            }\n            async function createRequestBody(input) {\n                if (method.kind != MethodKind.ServerStreaming) {\n                    throw \"The fetch API does not support streaming request bodies\";\n                }\n                const r = await input[Symbol.asyncIterator]().next();\n                if (r.done == true) {\n                    throw \"missing request message\";\n                }\n                return encodeEnvelope(0, serialize(r.value));\n            }\n            timeoutMs =\n                timeoutMs === undefined\n                    ? options.defaultTimeoutMs\n                    : timeoutMs <= 0\n                        ? undefined\n                        : timeoutMs;\n            return await runStreamingCall({\n                interceptors: options.interceptors,\n                timeoutMs,\n                signal,\n                req: {\n                    stream: true,\n                    service,\n                    method,\n                    url: createMethodUrl(options.baseUrl, service, method),\n                    init: {\n                        method: \"POST\",\n                        credentials: (_a = options.credentials) !== null && _a !== void 0 ? _a : \"same-origin\",\n                        redirect: \"error\",\n                        mode: \"cors\",\n                    },\n                    header: requestHeader(method.kind, useBinaryFormat, timeoutMs, header, false),\n                    contextValues: contextValues !== null && contextValues !== void 0 ? contextValues : createContextValues(),\n                    message: input,\n                },\n                next: async (req) => {\n                    var _a;\n                    const fetch = (_a = options.fetch) !== null && _a !== void 0 ? _a : globalThis.fetch;\n                    const fRes = await fetch(req.url, Object.assign(Object.assign({}, req.init), { headers: req.header, signal: req.signal, body: await createRequestBody(req.message) }));\n                    validateResponse(method.kind, useBinaryFormat, fRes.status, fRes.headers);\n                    if (fRes.body === null) {\n                        throw \"missing response body\";\n                    }\n                    const trailer = new Headers();\n                    const res = Object.assign(Object.assign({}, req), { header: fRes.headers, trailer, message: parseResponseBody(fRes.body, trailer, fRes.headers, req.signal) });\n                    return res;\n                },\n            });\n        },\n    };\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;AAcjC;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAjBA,IAAI,UAAU,AAAC,IAAI,IAAI,IAAI,CAAC,OAAO,IAAK,SAAU,CAAC;IAAI,OAAO,IAAI,YAAY,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,QAAQ;AAAI;AAC7H,IAAI,mBAAmB,AAAC,IAAI,IAAI,IAAI,CAAC,gBAAgB,IAAK,SAAU,OAAO,EAAE,UAAU,EAAE,SAAS;IAC9F,IAAI,CAAC,OAAO,aAAa,EAAE,MAAM,IAAI,UAAU;IAC/C,IAAI,IAAI,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,GAAG,GAAG,IAAI,EAAE;IAC7D,OAAO,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,UAAU,KAAK,UAAU,cAAc,CAAC,CAAC,OAAO,aAAa,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG;;IACjI,SAAS,YAAY,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG;QAAS;IAAG;IAC9F,SAAS,KAAK,CAAC,EAAE,CAAC;QAAI,IAAI,CAAC,CAAC,EAAE,EAAE;YAAE,CAAC,CAAC,EAAE,GAAG,SAAU,CAAC;gBAAI,OAAO,IAAI,QAAQ,SAAU,CAAC,EAAE,CAAC;oBAAI,EAAE,IAAI,CAAC;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE,IAAI,KAAK,OAAO,GAAG;gBAAI;YAAI;YAAG,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE;QAAG;IAAE;IACvK,SAAS,OAAO,CAAC,EAAE,CAAC;QAAI,IAAI;YAAE,KAAK,CAAC,CAAC,EAAE,CAAC;QAAK,EAAE,OAAO,GAAG;YAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAAI;IAAE;IACjF,SAAS,KAAK,CAAC;QAAI,EAAE,KAAK,YAAY,UAAU,QAAQ,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,UAAU,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;IAAI;IACvH,SAAS,QAAQ,KAAK;QAAI,OAAO,QAAQ;IAAQ;IACjD,SAAS,OAAO,KAAK;QAAI,OAAO,SAAS;IAAQ;IACjD,SAAS,OAAO,CAAC,EAAE,CAAC;QAAI,IAAI,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;IAAG;AACrF;;;;;;AAWO,SAAS,uBAAuB,OAAO;IAC1C,IAAI;IACJ,CAAA,GAAA,+LAAA,CAAA,iBAAc,AAAD;IACb,MAAM,kBAAkB,CAAC,KAAK,QAAQ,eAAe,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IACxF,OAAO;QACH,MAAM,OAAM,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa;YAC1E,IAAI;YACJ,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,2LAAA,CAAA,gCAA6B,AAAD,EAAE,QAAQ,iBAAiB,QAAQ,WAAW,EAAE,QAAQ,aAAa;YAC9H,YACI,cAAc,YACR,QAAQ,gBAAgB,GACxB,aAAa,IACT,YACA;YACd,OAAO,MAAM,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE;gBACtB,cAAc,QAAQ,YAAY;gBAClC;gBACA;gBACA,KAAK;oBACD,QAAQ;oBACR;oBACA;oBACA,KAAK,CAAA,GAAA,qMAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,OAAO,EAAE,SAAS;oBAC/C,MAAM;wBACF,QAAQ;wBACR,aAAa,CAAC,KAAK,QAAQ,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;wBACzE,UAAU;wBACV,MAAM;oBACV;oBACA,QAAQ,CAAA,GAAA,0MAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,IAAI,EAAE,iBAAiB,WAAW,QAAQ;oBACvE,eAAe,kBAAkB,QAAQ,kBAAkB,KAAK,IAAI,gBAAgB,CAAA,GAAA,mLAAA,CAAA,sBAAmB,AAAD;oBACtG;gBACJ;gBACA,MAAM,OAAO;oBACT,IAAI;oBACJ,MAAM,SAAS,QAAQ,UAAU,KAAK,QAClC,OAAO,WAAW,KAAK,gLAAA,CAAA,oBAAiB,CAAC,aAAa;oBAC1D,IAAI,OAAO;oBACX,IAAI,QAAQ;wBACR,MAAM,CAAA,GAAA,uMAAA,CAAA,mCAAgC,AAAD,EAAE,KAAK,UAAU,IAAI,OAAO,GAAG;oBACxE,OACK;wBACD,OAAO,UAAU,IAAI,OAAO;oBAChC;oBACA,MAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,WAAW,KAAK;oBACpF,MAAM,WAAW,MAAM,MAAM,IAAI,GAAG,EAAE,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,IAAI,GAAG;wBAAE,SAAS,IAAI,MAAM;wBAAE,QAAQ,IAAI,MAAM;wBAAE;oBAAK;oBACjI,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,6MAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,IAAI,EAAE,iBAAiB,SAAS,MAAM,EAAE,SAAS,OAAO;oBACrH,IAAI,cAAc;wBACd,MAAM,CAAA,GAAA,sMAAA,CAAA,gBAAa,AAAD,EAAG,MAAM,SAAS,IAAI,IAAK,CAAA,GAAA,iLAAA,CAAA,gBAAa,AAAD,KAAK,CAAA,GAAA,uMAAA,CAAA,eAAY,AAAD,EAAE,SAAS,OAAO,IAAI;oBACnG;oBACA,MAAM,CAAC,eAAe,eAAe,GAAG,CAAA,GAAA,uMAAA,CAAA,eAAY,AAAD,EAAE,SAAS,OAAO;oBACrE,OAAO;wBACH,QAAQ;wBACR;wBACA;wBACA,QAAQ;wBACR,SAAS,kBACH,MAAM,IAAI,WAAW,MAAM,SAAS,WAAW,OAC/C,OAAO,CAAC,CAAC,QAAQ,CAAE,MAAM,SAAS,IAAI,IAAK,CAAA,GAAA,2LAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,WAAW;wBACnF,SAAS;oBACb;gBACJ;YACJ;QACJ;QACA,MAAM,QAAO,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,aAAa;YACzE,IAAI;YACJ,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,2LAAA,CAAA,gCAA6B,AAAD,EAAE,QAAQ,iBAAiB,QAAQ,WAAW,EAAE,QAAQ,aAAa;YAC9H,SAAS,kBAAkB,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM;gBAC1D,OAAO,iBAAiB,IAAI,EAAE,WAAW,UAAU;oBAC/C,MAAM,SAAS,CAAA,GAAA,sLAAA,CAAA,+BAA4B,AAAD,EAAE,MAAM,SAAS;oBAC3D,IAAI,oBAAoB;oBACxB,OAAS;wBACL,MAAM,SAAS,MAAM,QAAQ,OAAO,IAAI;wBACxC,IAAI,OAAO,IAAI,EAAE;4BACb;wBACJ;wBACA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,OAAO,KAAK;wBACpC,IAAI,CAAC,QAAQ,yLAAA,CAAA,iBAAc,MAAM,yLAAA,CAAA,iBAAc,EAAE;4BAC7C,MAAM,IAAI,kLAAA,CAAA,eAAY,CAAC,CAAC,sDAAsD,CAAC,EAAE,sKAAA,CAAA,OAAI,CAAC,QAAQ;wBAClG;wBACA,IAAI,CAAC,QAAQ,sMAAA,CAAA,gBAAa,MAAM,sMAAA,CAAA,gBAAa,EAAE;4BAC3C,oBAAoB;4BACpB,MAAM,YAAY,CAAA,GAAA,sMAAA,CAAA,oBAAiB,AAAD,EAAE;4BACpC,IAAI,UAAU,KAAK,EAAE;gCACjB,MAAM,QAAQ,UAAU,KAAK;gCAC7B,OAAO,OAAO,CAAC,CAAC,OAAO;oCACnB,MAAM,QAAQ,CAAC,MAAM,CAAC,KAAK;gCAC/B;gCACA,MAAM;4BACV;4BACA,UAAU,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,MAAQ,cAAc,GAAG,CAAC,KAAK;4BAClE;wBACJ;wBACA,MAAM,MAAM,QAAQ,MAAM;oBAC9B;oBACA,oDAAoD;oBACpD,kDAAkD;oBAClD,kDAAkD;oBAClD,EAAE;oBACF,oDAAoD;oBACpD,IAAI,oBAAoB,QAAQ;wBAC5B,+DAA+D;wBAC/D,gDAAgD;wBAChD,OAAO,cAAc;oBACzB;oBACA,IAAI,CAAC,mBAAmB;wBACpB,MAAM;oBACV;gBACJ;YACJ;YACA,eAAe,kBAAkB,KAAK;gBAClC,IAAI,OAAO,IAAI,IAAI,gLAAA,CAAA,aAAU,CAAC,eAAe,EAAE;oBAC3C,MAAM;gBACV;gBACA,MAAM,IAAI,MAAM,KAAK,CAAC,OAAO,aAAa,CAAC,GAAG,IAAI;gBAClD,IAAI,EAAE,IAAI,IAAI,MAAM;oBAChB,MAAM;gBACV;gBACA,OAAO,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD,EAAE,GAAG,UAAU,EAAE,KAAK;YAC9C;YACA,YACI,cAAc,YACR,QAAQ,gBAAgB,GACxB,aAAa,IACT,YACA;YACd,OAAO,MAAM,CAAA,GAAA,yLAAA,CAAA,mBAAgB,AAAD,EAAE;gBAC1B,cAAc,QAAQ,YAAY;gBAClC;gBACA;gBACA,KAAK;oBACD,QAAQ;oBACR;oBACA;oBACA,KAAK,CAAA,GAAA,qMAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,OAAO,EAAE,SAAS;oBAC/C,MAAM;wBACF,QAAQ;wBACR,aAAa,CAAC,KAAK,QAAQ,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;wBACzE,UAAU;wBACV,MAAM;oBACV;oBACA,QAAQ,CAAA,GAAA,0MAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,IAAI,EAAE,iBAAiB,WAAW,QAAQ;oBACvE,eAAe,kBAAkB,QAAQ,kBAAkB,KAAK,IAAI,gBAAgB,CAAA,GAAA,mLAAA,CAAA,sBAAmB,AAAD;oBACtG,SAAS;gBACb;gBACA,MAAM,OAAO;oBACT,IAAI;oBACJ,MAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,WAAW,KAAK;oBACpF,MAAM,OAAO,MAAM,MAAM,IAAI,GAAG,EAAE,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,IAAI,GAAG;wBAAE,SAAS,IAAI,MAAM;wBAAE,QAAQ,IAAI,MAAM;wBAAE,MAAM,MAAM,kBAAkB,IAAI,OAAO;oBAAE;oBACnK,CAAA,GAAA,6MAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,IAAI,EAAE,iBAAiB,KAAK,MAAM,EAAE,KAAK,OAAO;oBACxE,IAAI,KAAK,IAAI,KAAK,MAAM;wBACpB,MAAM;oBACV;oBACA,MAAM,UAAU,IAAI;oBACpB,MAAM,MAAM,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;wBAAE,QAAQ,KAAK,OAAO;wBAAE;wBAAS,SAAS,kBAAkB,KAAK,IAAI,EAAE,SAAS,KAAK,OAAO,EAAE,IAAI,MAAM;oBAAE;oBAC5J,OAAO;gBACX;YACJ;QACJ;IACJ;AACJ", "ignoreList": [0]}}, {"offset": {"line": 5820, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@arcjet/transport/edge-light.js"], "sourcesContent": ["import { createConnectTransport } from '@connectrpc/connect-web';\n\nfunction createTransport(baseUrl) {\n    // The Connect Node client doesn't work on edge runtimes: https://github.com/bufbuild/connect-es/pull/589\n    // so set the transport using connect-web. The interceptor is required for it work in the edge runtime.\n    return createConnectTransport({\n        baseUrl,\n        interceptors: [\n            /**\n             * Ensures redirects are followed to properly support the Next.js/Vercel Edge\n             * Runtime.\n             * @see\n             * https://github.com/connectrpc/connect-es/issues/749#issuecomment-1693507516\n             */\n            (next) => (req) => {\n                req.init.redirect = \"follow\";\n                return next(req);\n            },\n        ],\n        fetch,\n    });\n}\n\nexport { createTransport };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,gBAAgB,OAAO;IAC5B,yGAAyG;IACzG,uGAAuG;IACvG,OAAO,CAAA,GAAA,6LAAA,CAAA,yBAAsB,AAAD,EAAE;QAC1B;QACA,cAAc;YACV;;;;;aAKC,GACD,CAAC,OAAS,CAAC;oBACP,IAAI,IAAI,CAAC,QAAQ,GAAG;oBACpB,OAAO,KAAK;gBAChB;SACH;QACD;IACJ;AACJ", "ignoreList": [0]}}, {"offset": {"line": 5851, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@arcjet/next/index.js"], "sourcesContent": ["import { NextResponse } from 'next/server.js';\nimport { headers, cookies } from 'next/headers.js';\nimport core__default from 'arcjet';\nexport * from 'arcjet';\nimport findIP, { parseProxy } from '@arcjet/ip';\nimport ArcjetHeaders from '@arcjet/headers';\nimport { logLevel, isDevelopment, baseUrl, platform } from '@arcjet/env';\nimport { Logger } from '@arcjet/logger';\nimport { createClient } from '@arcjet/protocol/client.js';\nimport { createTransport } from '@arcjet/transport';\n\nasync function request() {\n    const hdrs = await headers();\n    const cook = await cookies();\n    const cookieEntries = cook\n        .getAll()\n        .map((cookie) => [cookie.name, cookie.value]);\n    return {\n        headers: hdrs,\n        cookies: Object.fromEntries(cookieEntries),\n    };\n}\n// TODO: Deduplicate with other packages\nfunction errorMessage(err) {\n    if (err) {\n        if (typeof err === \"string\") {\n            return err;\n        }\n        if (typeof err === \"object\" &&\n            \"message\" in err &&\n            typeof err.message === \"string\") {\n            return err.message;\n        }\n    }\n    return \"Unknown problem\";\n}\nfunction createRemoteClient(options) {\n    // The base URL for the Arcjet API. Will default to the standard production\n    // API unless environment variable `ARCJET_BASE_URL` is set.\n    const url = options?.baseUrl ?? baseUrl(process.env);\n    // The timeout for the Arcjet API in milliseconds. This is set to a low value\n    // in production so calls fail open.\n    const timeout = options?.timeout ?? (isDevelopment(process.env) ? 1000 : 500);\n    // Transport is the HTTP client that the client uses to make requests.\n    const transport = createTransport(url);\n    const sdkStack = \"NEXTJS\";\n    const sdkVersion = \"1.0.0-beta.8\";\n    return createClient({\n        transport,\n        baseUrl: url,\n        timeout,\n        sdkStack,\n        sdkVersion,\n    });\n}\nfunction isIterable(val) {\n    return typeof val?.[Symbol.iterator] === \"function\";\n}\nfunction cookiesToArray(cookies) {\n    if (typeof cookies === \"undefined\") {\n        return [];\n    }\n    if (isIterable(cookies)) {\n        return Array.from(cookies).map(([_, cookie]) => cookie);\n    }\n    else {\n        return Object.entries(cookies).map(([name, value]) => ({\n            name,\n            value: value ?? \"\",\n        }));\n    }\n}\nfunction cookiesToString(cookies) {\n    // This is essentially the implementation of `RequestCookies#toString` in\n    // Next.js but normalized for NextApiRequest cookies object\n    return cookiesToArray(cookies)\n        .map((v) => `${v.name}=${encodeURIComponent(v.value)}`)\n        .join(\"; \");\n}\n/**\n * Create a new {@link ArcjetNext} client. Always build your initial client\n * outside of a request handler so it persists across requests. If you need to\n * augment a client inside a handler, call the `withRule()` function on the base\n * client.\n *\n * @param options - Arcjet configuration options to apply to all requests.\n */\nfunction arcjet(options) {\n    const client = options.client ?? createRemoteClient();\n    const log = options.log\n        ? options.log\n        : new Logger({\n            level: logLevel(process.env),\n        });\n    const proxies = Array.isArray(options.proxies)\n        ? options.proxies.map(parseProxy)\n        : undefined;\n    if (isDevelopment(process.env)) {\n        log.warn(\"Arcjet will use 127.0.0.1 when missing public IP address in development mode\");\n    }\n    function toArcjetRequest(request, props) {\n        // We construct an ArcjetHeaders to normalize over Headers\n        const headers = new ArcjetHeaders(request.headers);\n        let ip = findIP({\n            ip: request.ip,\n            socket: request.socket,\n            info: request.info,\n            requestContext: request.requestContext,\n            headers,\n        }, { platform: platform(process.env), proxies });\n        if (ip === \"\") {\n            // If the `ip` is empty but we're in development mode, we default the IP\n            // so the request doesn't fail.\n            if (isDevelopment(process.env)) {\n                ip = \"127.0.0.1\";\n            }\n            else {\n                log.warn(`Client IP address is missing. If this is a dev environment set the ARCJET_ENV env var to \"development\"`);\n            }\n        }\n        const method = request.method ?? \"\";\n        const host = headers.get(\"host\") ?? \"\";\n        let path = \"\";\n        let query = \"\";\n        let protocol = \"\";\n        // TODO(#36): nextUrl has formatting logic when you `toString` but\n        // we don't account for that here\n        if (typeof request.nextUrl !== \"undefined\") {\n            path = request.nextUrl.pathname ?? \"\";\n            if (typeof request.nextUrl.search !== \"undefined\") {\n                query = request.nextUrl.search;\n            }\n            if (typeof request.nextUrl.protocol !== \"undefined\") {\n                protocol = request.nextUrl.protocol;\n            }\n        }\n        else {\n            if (typeof request.socket?.encrypted !== \"undefined\") {\n                protocol = request.socket.encrypted ? \"https:\" : \"http:\";\n            }\n            else {\n                protocol = \"http:\";\n            }\n            // Do some very simple validation, but also try/catch around URL parsing\n            if (typeof request.url !== \"undefined\" &&\n                request.url !== \"\" &&\n                host !== \"\") {\n                try {\n                    const url = new URL(request.url, `${protocol}//${host}`);\n                    path = url.pathname;\n                    query = url.search;\n                    protocol = url.protocol;\n                }\n                catch {\n                    // If the parsing above fails, just set the path as whatever url we\n                    // received.\n                    path = request.url ?? \"\";\n                    log.warn('Unable to parse URL. Using \"%s\" as `path`.', path);\n                }\n            }\n            else {\n                path = request.url ?? \"\";\n            }\n        }\n        const cookies = cookiesToString(request.cookies);\n        const extra = {};\n        // If we're running on Vercel, we can add some extra information\n        if (process.env[\"VERCEL\"]) {\n            // Vercel ID https://vercel.com/docs/concepts/edge-network/headers\n            extra[\"vercel-id\"] = headers.get(\"x-vercel-id\") ?? \"\";\n            // Vercel deployment URL\n            // https://vercel.com/docs/concepts/edge-network/headers\n            extra[\"vercel-deployment-url\"] =\n                headers.get(\"x-vercel-deployment-url\") ?? \"\";\n            // Vercel git commit SHA\n            // https://vercel.com/docs/concepts/projects/environment-variables/system-environment-variables\n            extra[\"vercel-git-commit-sha\"] =\n                process.env[\"VERCEL_GIT_COMMIT_SHA\"] ?? \"\";\n            extra[\"vercel-git-commit-sha\"] =\n                process.env[\"VERCEL_GIT_COMMIT_SHA\"] ?? \"\";\n        }\n        return {\n            ...props,\n            ...extra,\n            ip,\n            method,\n            protocol,\n            host,\n            path,\n            headers,\n            cookies,\n            query,\n        };\n    }\n    function withClient(aj) {\n        return Object.freeze({\n            withRule(rule) {\n                const client = aj.withRule(rule);\n                return withClient(client);\n            },\n            async protect(request, ...[props]) {\n                // TODO(#220): The generic manipulations get really mad here, so we cast\n                // Further investigation makes it seem like it has something to do with\n                // the definition of `props` in the signature but it's hard to track down\n                const req = toArcjetRequest(request, props ?? {});\n                const getBody = async () => {\n                    try {\n                        if (typeof request.clone === \"function\") {\n                            const cloned = request.clone();\n                            // Awaited to throw if it rejects and we'll just return undefined\n                            const body = await cloned.text();\n                            return body;\n                        }\n                        else if (typeof request.body === \"string\") {\n                            return request.body;\n                        }\n                        else if (typeof request.body !== \"undefined\" &&\n                            // BigInt cannot be serialized with JSON.stringify\n                            typeof request.body !== \"bigint\" &&\n                            // The body will be null if there was no body with the request.\n                            // Reference:\n                            // https://nextjs.org/docs/pages/building-your-application/routing/api-routes#request-helpers\n                            request.body !== null) {\n                            return JSON.stringify(request.body);\n                        }\n                        else {\n                            log.warn(\"no body available\");\n                            return;\n                        }\n                    }\n                    catch (e) {\n                        log.error(\"failed to get request body: %s\", errorMessage(e));\n                        return;\n                    }\n                };\n                return aj.protect({ getBody }, req);\n            },\n        });\n    }\n    const aj = core__default({ ...options, client, log });\n    return withClient(aj);\n}\n/**\n * Protects your Next.js application using Arcjet middleware.\n *\n * @param arcjet An instantiated Arcjet SDK\n * @param middleware Any existing middleware you'd like to be called after\n * Arcjet decides a request is allowed.\n * @returns If the request is allowed, the next middleware or handler will be\n * called. If the request is denied, a `Response` will be returned immediately\n * and the no further middleware or handlers will be called.\n */\nfunction createMiddleware(arcjet, existingMiddleware) {\n    return async function middleware(request, event) {\n        const decision = await arcjet.protect(request);\n        if (decision.isDenied()) {\n            // TODO(#222): Content type negotiation using `Accept` header\n            if (decision.reason.isRateLimit()) {\n                return NextResponse.json({ code: 429, message: \"Too Many Requests\" }, { status: 429 });\n            }\n            else {\n                return NextResponse.json({ code: 403, message: \"Forbidden\" }, { status: 403 });\n            }\n        }\n        else {\n            if (typeof existingMiddleware === \"function\") {\n                return existingMiddleware(request, event);\n            }\n            else {\n                return NextResponse.next();\n            }\n        }\n    };\n}\nfunction isNextApiResponse(val) {\n    if (val === null) {\n        return false;\n    }\n    if (typeof val !== \"object\") {\n        return false;\n    }\n    if (!(\"status\" in val)) {\n        return false;\n    }\n    if (!(\"json\" in val)) {\n        return false;\n    }\n    if (typeof val.status !== \"function\" || typeof val.json !== \"function\") {\n        return false;\n    }\n    return true;\n}\n/**\n * Wraps a Next.js page route, edge middleware, or an API route running on the\n * Edge Runtime.\n *\n * @param arcjet An instantiated Arcjet SDK\n * @param handler The request handler to wrap\n * @returns If the request is allowed, the wrapped `handler` will be called. If\n * the request is denied, a `Response` will be returned based immediately and\n * the wrapped `handler` will never be called.\n */\nfunction withArcjet(arcjet, handler) {\n    return async (...args) => {\n        const request = args[0];\n        const response = args[1];\n        const decision = await arcjet.protect(request);\n        if (decision.isDenied()) {\n            if (isNextApiResponse(response)) {\n                // TODO(#222): Content type negotiation using `Accept` header\n                if (decision.reason.isRateLimit()) {\n                    return response\n                        .status(429)\n                        .json({ code: 429, message: \"Too Many Requests\" });\n                }\n                else {\n                    return response.status(403).json({ code: 403, message: \"Forbidden\" });\n                }\n            }\n            else {\n                // TODO(#222): Content type negotiation using `Accept` header\n                if (decision.reason.isRateLimit()) {\n                    return NextResponse.json({ code: 429, message: \"Too Many Requests\" }, { status: 429 });\n                }\n                else {\n                    return NextResponse.json({ code: 403, message: \"Forbidden\" }, { status: 403 });\n                }\n            }\n        }\n        else {\n            return handler(...args);\n        }\n    };\n}\n\nexport { createMiddleware, createRemoteClient, arcjet as default, request, withArcjet };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAEA,eAAe;IACX,MAAM,OAAO,MAAM,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD;IACzB,MAAM,OAAO,MAAM,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD;IACzB,MAAM,gBAAgB,KACjB,MAAM,GACN,GAAG,CAAC,CAAC,SAAW;YAAC,OAAO,IAAI;YAAE,OAAO,KAAK;SAAC;IAChD,OAAO;QACH,SAAS;QACT,SAAS,OAAO,WAAW,CAAC;IAChC;AACJ;AACA,wCAAwC;AACxC,SAAS,aAAa,GAAG;IACrB,IAAI,KAAK;QACL,IAAI,OAAO,QAAQ,UAAU;YACzB,OAAO;QACX;QACA,IAAI,OAAO,QAAQ,YACf,aAAa,OACb,OAAO,IAAI,OAAO,KAAK,UAAU;YACjC,OAAO,IAAI,OAAO;QACtB;IACJ;IACA,OAAO;AACX;AACA,SAAS,mBAAmB,OAAO;IAC/B,2EAA2E;IAC3E,4DAA4D;IAC5D,MAAM,MAAM,SAAS,WAAW,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,GAAG;IACnD,6EAA6E;IAC7E,oCAAoC;IACpC,MAAM,UAAU,SAAS,WAAW,CAAC,CAAA,GAAA,gJAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,GAAG,IAAI,OAAO,GAAG;IAC5E,sEAAsE;IACtE,MAAM,YAAY,CAAA,GAAA,8JAAA,CAAA,kBAAe,AAAD,EAAE;IAClC,MAAM,WAAW;IACjB,MAAM,aAAa;IACnB,OAAO,CAAA,GAAA,sJAAA,CAAA,eAAY,AAAD,EAAE;QAChB;QACA,SAAS;QACT;QACA;QACA;IACJ;AACJ;AACA,SAAS,WAAW,GAAG;IACnB,OAAO,OAAO,KAAK,CAAC,OAAO,QAAQ,CAAC,KAAK;AAC7C;AACA,SAAS,eAAe,OAAO;IAC3B,IAAI,OAAO,YAAY,aAAa;QAChC,OAAO,EAAE;IACb;IACA,IAAI,WAAW,UAAU;QACrB,OAAO,MAAM,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,GAAG,OAAO,GAAK;IACpD,OACK;QACD,OAAO,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM,GAAK,CAAC;gBACnD;gBACA,OAAO,SAAS;YACpB,CAAC;IACL;AACJ;AACA,SAAS,gBAAgB,OAAO;IAC5B,yEAAyE;IACzE,2DAA2D;IAC3D,OAAO,eAAe,SACjB,GAAG,CAAC,CAAC,IAAM,GAAG,EAAE,IAAI,CAAC,CAAC,EAAE,mBAAmB,EAAE,KAAK,GAAG,EACrD,IAAI,CAAC;AACd;AACA;;;;;;;CAOC,GACD,SAAS,OAAO,OAAO;IACnB,MAAM,SAAS,QAAQ,MAAM,IAAI;IACjC,MAAM,MAAM,QAAQ,GAAG,GACjB,QAAQ,GAAG,GACX,IAAI,mJAAA,CAAA,SAAM,CAAC;QACT,OAAO,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,GAAG;IAC/B;IACJ,MAAM,UAAU,MAAM,OAAO,CAAC,QAAQ,OAAO,IACvC,QAAQ,OAAO,CAAC,GAAG,CAAC,+IAAA,CAAA,aAAU,IAC9B;IACN,IAAI,CAAA,GAAA,gJAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,GAAG,GAAG;QAC5B,IAAI,IAAI,CAAC;IACb;IACA,SAAS,gBAAgB,OAAO,EAAE,KAAK;QACnC,0DAA0D;QAC1D,MAAM,UAAU,IAAI,oJAAA,CAAA,UAAa,CAAC,QAAQ,OAAO;QACjD,IAAI,KAAK,CAAA,GAAA,+IAAA,CAAA,UAAM,AAAD,EAAE;YACZ,IAAI,QAAQ,EAAE;YACd,QAAQ,QAAQ,MAAM;YACtB,MAAM,QAAQ,IAAI;YAClB,gBAAgB,QAAQ,cAAc;YACtC;QACJ,GAAG;YAAE,UAAU,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,GAAG;YAAG;QAAQ;QAC9C,IAAI,OAAO,IAAI;YACX,wEAAwE;YACxE,+BAA+B;YAC/B,IAAI,CAAA,GAAA,gJAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,GAAG,GAAG;gBAC5B,KAAK;YACT,OACK;gBACD,IAAI,IAAI,CAAC,CAAC,sGAAsG,CAAC;YACrH;QACJ;QACA,MAAM,SAAS,QAAQ,MAAM,IAAI;QACjC,MAAM,OAAO,QAAQ,GAAG,CAAC,WAAW;QACpC,IAAI,OAAO;QACX,IAAI,QAAQ;QACZ,IAAI,WAAW;QACf,kEAAkE;QAClE,iCAAiC;QACjC,IAAI,OAAO,QAAQ,OAAO,KAAK,aAAa;YACxC,OAAO,QAAQ,OAAO,CAAC,QAAQ,IAAI;YACnC,IAAI,OAAO,QAAQ,OAAO,CAAC,MAAM,KAAK,aAAa;gBAC/C,QAAQ,QAAQ,OAAO,CAAC,MAAM;YAClC;YACA,IAAI,OAAO,QAAQ,OAAO,CAAC,QAAQ,KAAK,aAAa;gBACjD,WAAW,QAAQ,OAAO,CAAC,QAAQ;YACvC;QACJ,OACK;YACD,IAAI,OAAO,QAAQ,MAAM,EAAE,cAAc,aAAa;gBAClD,WAAW,QAAQ,MAAM,CAAC,SAAS,GAAG,WAAW;YACrD,OACK;gBACD,WAAW;YACf;YACA,wEAAwE;YACxE,IAAI,OAAO,QAAQ,GAAG,KAAK,eACvB,QAAQ,GAAG,KAAK,MAChB,SAAS,IAAI;gBACb,IAAI;oBACA,MAAM,MAAM,IAAI,IAAI,QAAQ,GAAG,EAAE,GAAG,SAAS,EAAE,EAAE,MAAM;oBACvD,OAAO,IAAI,QAAQ;oBACnB,QAAQ,IAAI,MAAM;oBAClB,WAAW,IAAI,QAAQ;gBAC3B,EACA,OAAM;oBACF,mEAAmE;oBACnE,YAAY;oBACZ,OAAO,QAAQ,GAAG,IAAI;oBACtB,IAAI,IAAI,CAAC,8CAA8C;gBAC3D;YACJ,OACK;gBACD,OAAO,QAAQ,GAAG,IAAI;YAC1B;QACJ;QACA,MAAM,UAAU,gBAAgB,QAAQ,OAAO;QAC/C,MAAM,QAAQ,CAAC;QACf,gEAAgE;QAChE,IAAI,QAAQ,GAAG,CAAC,SAAS,EAAE;YACvB,kEAAkE;YAClE,KAAK,CAAC,YAAY,GAAG,QAAQ,GAAG,CAAC,kBAAkB;YACnD,wBAAwB;YACxB,wDAAwD;YACxD,KAAK,CAAC,wBAAwB,GAC1B,QAAQ,GAAG,CAAC,8BAA8B;YAC9C,wBAAwB;YACxB,+FAA+F;YAC/F,KAAK,CAAC,wBAAwB,GAC1B,QAAQ,GAAG,CAAC,wBAAwB,IAAI;YAC5C,KAAK,CAAC,wBAAwB,GAC1B,QAAQ,GAAG,CAAC,wBAAwB,IAAI;QAChD;QACA,OAAO;YACH,GAAG,KAAK;YACR,GAAG,KAAK;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACJ;IACJ;IACA,SAAS,WAAW,EAAE;QAClB,OAAO,OAAO,MAAM,CAAC;YACjB,UAAS,IAAI;gBACT,MAAM,SAAS,GAAG,QAAQ,CAAC;gBAC3B,OAAO,WAAW;YACtB;YACA,MAAM,SAAQ,OAAO,EAAE,GAAG,CAAC,MAAM;gBAC7B,wEAAwE;gBACxE,uEAAuE;gBACvE,yEAAyE;gBACzE,MAAM,MAAM,gBAAgB,SAAS,SAAS,CAAC;gBAC/C,MAAM,UAAU;oBACZ,IAAI;wBACA,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY;4BACrC,MAAM,SAAS,QAAQ,KAAK;4BAC5B,iEAAiE;4BACjE,MAAM,OAAO,MAAM,OAAO,IAAI;4BAC9B,OAAO;wBACX,OACK,IAAI,OAAO,QAAQ,IAAI,KAAK,UAAU;4BACvC,OAAO,QAAQ,IAAI;wBACvB,OACK,IAAI,OAAO,QAAQ,IAAI,KAAK,eAC7B,kDAAkD;wBAClD,OAAO,QAAQ,IAAI,KAAK,YACxB,+DAA+D;wBAC/D,aAAa;wBACb,6FAA6F;wBAC7F,QAAQ,IAAI,KAAK,MAAM;4BACvB,OAAO,KAAK,SAAS,CAAC,QAAQ,IAAI;wBACtC,OACK;4BACD,IAAI,IAAI,CAAC;4BACT;wBACJ;oBACJ,EACA,OAAO,GAAG;wBACN,IAAI,KAAK,CAAC,kCAAkC,aAAa;wBACzD;oBACJ;gBACJ;gBACA,OAAO,GAAG,OAAO,CAAC;oBAAE;gBAAQ,GAAG;YACnC;QACJ;IACJ;IACA,MAAM,KAAK,CAAA,GAAA,uJAAA,CAAA,UAAa,AAAD,EAAE;QAAE,GAAG,OAAO;QAAE;QAAQ;IAAI;IACnD,OAAO,WAAW;AACtB;AACA;;;;;;;;;CASC,GACD,SAAS,iBAAiB,MAAM,EAAE,kBAAkB;IAChD,OAAO,eAAe,WAAW,OAAO,EAAE,KAAK;QAC3C,MAAM,WAAW,MAAM,OAAO,OAAO,CAAC;QACtC,IAAI,SAAS,QAAQ,IAAI;YACrB,6DAA6D;YAC7D,IAAI,SAAS,MAAM,CAAC,WAAW,IAAI;gBAC/B,OAAO,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,MAAM;oBAAK,SAAS;gBAAoB,GAAG;oBAAE,QAAQ;gBAAI;YACxF,OACK;gBACD,OAAO,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,MAAM;oBAAK,SAAS;gBAAY,GAAG;oBAAE,QAAQ;gBAAI;YAChF;QACJ,OACK;YACD,IAAI,OAAO,uBAAuB,YAAY;gBAC1C,OAAO,mBAAmB,SAAS;YACvC,OACK;gBACD,OAAO,sIAAA,CAAA,eAAY,CAAC,IAAI;YAC5B;QACJ;IACJ;AACJ;AACA,SAAS,kBAAkB,GAAG;IAC1B,IAAI,QAAQ,MAAM;QACd,OAAO;IACX;IACA,IAAI,OAAO,QAAQ,UAAU;QACzB,OAAO;IACX;IACA,IAAI,CAAC,CAAC,YAAY,GAAG,GAAG;QACpB,OAAO;IACX;IACA,IAAI,CAAC,CAAC,UAAU,GAAG,GAAG;QAClB,OAAO;IACX;IACA,IAAI,OAAO,IAAI,MAAM,KAAK,cAAc,OAAO,IAAI,IAAI,KAAK,YAAY;QACpE,OAAO;IACX;IACA,OAAO;AACX;AACA;;;;;;;;;CASC,GACD,SAAS,WAAW,MAAM,EAAE,OAAO;IAC/B,OAAO,OAAO,GAAG;QACb,MAAM,UAAU,IAAI,CAAC,EAAE;QACvB,MAAM,WAAW,IAAI,CAAC,EAAE;QACxB,MAAM,WAAW,MAAM,OAAO,OAAO,CAAC;QACtC,IAAI,SAAS,QAAQ,IAAI;YACrB,IAAI,kBAAkB,WAAW;gBAC7B,6DAA6D;gBAC7D,IAAI,SAAS,MAAM,CAAC,WAAW,IAAI;oBAC/B,OAAO,SACF,MAAM,CAAC,KACP,IAAI,CAAC;wBAAE,MAAM;wBAAK,SAAS;oBAAoB;gBACxD,OACK;oBACD,OAAO,SAAS,MAAM,CAAC,KAAK,IAAI,CAAC;wBAAE,MAAM;wBAAK,SAAS;oBAAY;gBACvE;YACJ,OACK;gBACD,6DAA6D;gBAC7D,IAAI,SAAS,MAAM,CAAC,WAAW,IAAI;oBAC/B,OAAO,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;wBAAE,MAAM;wBAAK,SAAS;oBAAoB,GAAG;wBAAE,QAAQ;oBAAI;gBACxF,OACK;oBACD,OAAO,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;wBAAE,MAAM;wBAAK,SAAS;oBAAY,GAAG;wBAAE,QAAQ;oBAAI;gBAChF;YACJ;QACJ,OACK;YACD,OAAO,WAAW;QACtB;IACJ;AACJ", "ignoreList": [0]}}, {"offset": {"line": 6235, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/map-obj/index.js"], "sourcesContent": ["'use strict';\n\nconst isObject = value => typeof value === 'object' && value !== null;\nconst mapObjectSkip = Symbol('skip');\n\n// Customized for this use-case\nconst isObjectCustom = value =>\n\tisObject(value) &&\n\t!(value instanceof RegExp) &&\n\t!(value instanceof Error) &&\n\t!(value instanceof Date);\n\nconst mapObject = (object, mapper, options, isSeen = new WeakMap()) => {\n\toptions = {\n\t\tdeep: false,\n\t\ttarget: {},\n\t\t...options\n\t};\n\n\tif (isSeen.has(object)) {\n\t\treturn isSeen.get(object);\n\t}\n\n\tisSeen.set(object, options.target);\n\n\tconst {target} = options;\n\tdelete options.target;\n\n\tconst mapArray = array => array.map(element => isObjectCustom(element) ? mapObject(element, mapper, options, isSeen) : element);\n\tif (Array.isArray(object)) {\n\t\treturn mapArray(object);\n\t}\n\n\tfor (const [key, value] of Object.entries(object)) {\n\t\tconst mapResult = mapper(key, value, object);\n\n\t\tif (mapResult === mapObjectSkip) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tlet [newKey, newValue, {shouldRecurse = true} = {}] = mapResult;\n\n\t\t// Drop `__proto__` keys.\n\t\tif (newKey === '__proto__') {\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (options.deep && shouldRecurse && isObjectCustom(newValue)) {\n\t\t\tnewValue = Array.isArray(newValue) ?\n\t\t\t\tmapArray(newValue) :\n\t\t\t\tmapObject(newValue, mapper, options, isSeen);\n\t\t}\n\n\t\ttarget[newKey] = newValue;\n\t}\n\n\treturn target;\n};\n\nmodule.exports = (object, mapper, options) => {\n\tif (!isObject(object)) {\n\t\tthrow new TypeError(`Expected an object, got \\`${object}\\` (${typeof object})`);\n\t}\n\n\treturn mapObject(object, mapper, options);\n};\n\nmodule.exports.mapObjectSkip = mapObjectSkip;\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,WAAW,CAAA,QAAS,OAAO,UAAU,YAAY,UAAU;AACjE,MAAM,gBAAgB,OAAO;AAE7B,+BAA+B;AAC/B,MAAM,iBAAiB,CAAA,QACtB,SAAS,UACT,CAAC,CAAC,iBAAiB,MAAM,KACzB,CAAC,CAAC,iBAAiB,KAAK,KACxB,CAAC,CAAC,iBAAiB,IAAI;AAExB,MAAM,YAAY,CAAC,QAAQ,QAAQ,SAAS,SAAS,IAAI,SAAS;IACjE,UAAU;QACT,MAAM;QACN,QAAQ,CAAC;QACT,GAAG,OAAO;IACX;IAEA,IAAI,OAAO,GAAG,CAAC,SAAS;QACvB,OAAO,OAAO,GAAG,CAAC;IACnB;IAEA,OAAO,GAAG,CAAC,QAAQ,QAAQ,MAAM;IAEjC,MAAM,EAAC,MAAM,EAAC,GAAG;IACjB,OAAO,QAAQ,MAAM;IAErB,MAAM,WAAW,CAAA,QAAS,MAAM,GAAG,CAAC,CAAA,UAAW,eAAe,WAAW,UAAU,SAAS,QAAQ,SAAS,UAAU;IACvH,IAAI,MAAM,OAAO,CAAC,SAAS;QAC1B,OAAO,SAAS;IACjB;IAEA,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,QAAS;QAClD,MAAM,YAAY,OAAO,KAAK,OAAO;QAErC,IAAI,cAAc,eAAe;YAChC;QACD;QAEA,IAAI,CAAC,QAAQ,UAAU,EAAC,gBAAgB,IAAI,EAAC,GAAG,CAAC,CAAC,CAAC,GAAG;QAEtD,yBAAyB;QACzB,IAAI,WAAW,aAAa;YAC3B;QACD;QAEA,IAAI,QAAQ,IAAI,IAAI,iBAAiB,eAAe,WAAW;YAC9D,WAAW,MAAM,OAAO,CAAC,YACxB,SAAS,YACT,UAAU,UAAU,QAAQ,SAAS;QACvC;QAEA,MAAM,CAAC,OAAO,GAAG;IAClB;IAEA,OAAO;AACR;AAEA,OAAO,OAAO,GAAG,CAAC,QAAQ,QAAQ;IACjC,IAAI,CAAC,SAAS,SAAS;QACtB,MAAM,IAAI,UAAU,CAAC,0BAA0B,EAAE,OAAO,IAAI,EAAE,OAAO,OAAO,CAAC,CAAC;IAC/E;IAEA,OAAO,UAAU,QAAQ,QAAQ;AAClC;AAEA,OAAO,OAAO,CAAC,aAAa,GAAG", "ignoreList": [0]}}, {"offset": {"line": 6286, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/tslib/tslib.es6.mjs"], "sourcesContent": ["/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;8EAa8E,GAC9E,8DAA8D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE9D,IAAI,gBAAgB,SAAS,CAAC,EAAE,CAAC;IAC/B,gBAAgB,OAAO,cAAc,IAChC,CAAA;QAAE,WAAW,EAAE;IAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;QAAI,EAAE,SAAS,GAAG;IAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;QAAI,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IACpG,OAAO,cAAc,GAAG;AAC1B;AAEO,SAAS,UAAU,CAAC,EAAE,CAAC;IAC5B,IAAI,OAAO,MAAM,cAAc,MAAM,MACjC,MAAM,IAAI,UAAU,yBAAyB,OAAO,KAAK;IAC7D,cAAc,GAAG;IACjB,SAAS;QAAO,IAAI,CAAC,WAAW,GAAG;IAAG;IACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;AACrF;AAEO,IAAI,WAAW;IACpB,WAAW,OAAO,MAAM,IAAI,SAAS,SAAS,CAAC;QAC3C,IAAK,IAAI,GAAG,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAI,GAAG,IAAK;YACjD,IAAI,SAAS,CAAC,EAAE;YAChB,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAChF;QACA,OAAO;IACX;IACA,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAC9B;AAEO,SAAS,OAAO,CAAC,EAAE,CAAC;IACzB,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,KAAK,GAC9E,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACf,IAAI,KAAK,QAAQ,OAAO,OAAO,qBAAqB,KAAK,YACrD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,qBAAqB,CAAC,IAAI,IAAI,EAAE,MAAM,EAAE,IAAK;QACpE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GACzE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACzB;IACJ,OAAO;AACT;AAEO,SAAS,WAAW,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI;IACtD,IAAI,IAAI,UAAU,MAAM,EAAE,IAAI,IAAI,IAAI,SAAS,SAAS,OAAO,OAAO,OAAO,wBAAwB,CAAC,QAAQ,OAAO,MAAM;IAC3H,IAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,QAAQ,KAAK,YAAY,IAAI,QAAQ,QAAQ,CAAC,YAAY,QAAQ,KAAK;SACpH,IAAK,IAAI,IAAI,WAAW,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK,IAAI,IAAI,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,QAAQ,KAAK,KAAK,EAAE,QAAQ,IAAI,KAAK;IAChJ,OAAO,IAAI,KAAK,KAAK,OAAO,cAAc,CAAC,QAAQ,KAAK,IAAI;AAC9D;AAEO,SAAS,QAAQ,UAAU,EAAE,SAAS;IAC3C,OAAO,SAAU,MAAM,EAAE,GAAG;QAAI,UAAU,QAAQ,KAAK;IAAa;AACtE;AAEO,SAAS,aAAa,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE,iBAAiB;IACrG,SAAS,OAAO,CAAC;QAAI,IAAI,MAAM,KAAK,KAAK,OAAO,MAAM,YAAY,MAAM,IAAI,UAAU;QAAsB,OAAO;IAAG;IACtH,IAAI,OAAO,UAAU,IAAI,EAAE,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;IACzF,IAAI,SAAS,CAAC,gBAAgB,OAAO,SAAS,CAAC,SAAS,GAAG,OAAO,KAAK,SAAS,GAAG;IACnF,IAAI,aAAa,gBAAgB,CAAC,SAAS,OAAO,wBAAwB,CAAC,QAAQ,UAAU,IAAI,IAAI,CAAC,CAAC;IACvG,IAAI,GAAG,OAAO;IACd,IAAK,IAAI,IAAI,WAAW,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAC7C,IAAI,UAAU,CAAC;QACf,IAAK,IAAI,KAAK,UAAW,OAAO,CAAC,EAAE,GAAG,MAAM,WAAW,CAAC,IAAI,SAAS,CAAC,EAAE;QACxE,IAAK,IAAI,KAAK,UAAU,MAAM,CAAE,QAAQ,MAAM,CAAC,EAAE,GAAG,UAAU,MAAM,CAAC,EAAE;QACvE,QAAQ,cAAc,GAAG,SAAU,CAAC;YAAI,IAAI,MAAM,MAAM,IAAI,UAAU;YAA2D,kBAAkB,IAAI,CAAC,OAAO,KAAK;QAAQ;QAC5K,IAAI,SAAS,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,SAAS,aAAa;YAAE,KAAK,WAAW,GAAG;YAAE,KAAK,WAAW,GAAG;QAAC,IAAI,UAAU,CAAC,IAAI,EAAE;QACtH,IAAI,SAAS,YAAY;YACrB,IAAI,WAAW,KAAK,GAAG;YACvB,IAAI,WAAW,QAAQ,OAAO,WAAW,UAAU,MAAM,IAAI,UAAU;YACvE,IAAI,IAAI,OAAO,OAAO,GAAG,GAAG,WAAW,GAAG,GAAG;YAC7C,IAAI,IAAI,OAAO,OAAO,GAAG,GAAG,WAAW,GAAG,GAAG;YAC7C,IAAI,IAAI,OAAO,OAAO,IAAI,GAAG,aAAa,OAAO,CAAC;QACtD,OACK,IAAI,IAAI,OAAO,SAAS;YACzB,IAAI,SAAS,SAAS,aAAa,OAAO,CAAC;iBACtC,UAAU,CAAC,IAAI,GAAG;QAC3B;IACJ;IACA,IAAI,QAAQ,OAAO,cAAc,CAAC,QAAQ,UAAU,IAAI,EAAE;IAC1D,OAAO;AACT;;AAEO,SAAS,kBAAkB,OAAO,EAAE,YAAY,EAAE,KAAK;IAC5D,IAAI,WAAW,UAAU,MAAM,GAAG;IAClC,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC1C,QAAQ,WAAW,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,SAAS,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC;IACnF;IACA,OAAO,WAAW,QAAQ,KAAK;AACjC;;AAEO,SAAS,UAAU,CAAC;IACzB,OAAO,OAAO,MAAM,WAAW,IAAI,GAAG,MAAM,CAAC;AAC/C;;AAEO,SAAS,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM;IAC/C,IAAI,OAAO,SAAS,UAAU,OAAO,KAAK,WAAW,GAAG,IAAI,MAAM,CAAC,KAAK,WAAW,EAAE,OAAO;IAC5F,OAAO,OAAO,cAAc,CAAC,GAAG,QAAQ;QAAE,cAAc;QAAM,OAAO,SAAS,GAAG,MAAM,CAAC,QAAQ,KAAK,QAAQ;IAAK;AACpH;;AAEO,SAAS,WAAW,WAAW,EAAE,aAAa;IACnD,IAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,QAAQ,KAAK,YAAY,OAAO,QAAQ,QAAQ,CAAC,aAAa;AAClH;AAEO,SAAS,UAAU,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS;IACzD,SAAS,MAAM,KAAK;QAAI,OAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,OAAO;YAAI,QAAQ;QAAQ;IAAI;IAC3G,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,EAAE,SAAU,OAAO,EAAE,MAAM;QACrD,SAAS,UAAU,KAAK;YAAI,IAAI;gBAAE,KAAK,UAAU,IAAI,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC1F,SAAS,SAAS,KAAK;YAAI,IAAI;gBAAE,KAAK,SAAS,CAAC,QAAQ,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC7F,SAAS,KAAK,MAAM;YAAI,OAAO,IAAI,GAAG,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,IAAI,CAAC,WAAW;QAAW;QAC7G,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,CAAC,EAAE,IAAI;IACtE;AACF;AAEO,SAAS,YAAY,OAAO,EAAE,IAAI;IACvC,IAAI,IAAI;QAAE,OAAO;QAAG,MAAM;YAAa,IAAI,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE;YAAE,OAAO,CAAC,CAAC,EAAE;QAAE;QAAG,MAAM,EAAE;QAAE,KAAK,EAAE;IAAC,GAAG,GAAG,GAAG,GAAG,IAAI,OAAO,MAAM,CAAC,CAAC,OAAO,aAAa,aAAa,WAAW,MAAM,EAAE,SAAS;IAC/L,OAAO,EAAE,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC,QAAQ,GAAG,KAAK,IAAI,CAAC,CAAC,SAAS,GAAG,KAAK,IAAI,OAAO,WAAW,cAAc,CAAC,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QAAa,OAAO,IAAI;IAAE,CAAC,GAAG;;IAC1J,SAAS,KAAK,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,KAAK;gBAAC;gBAAG;aAAE;QAAG;IAAG;IACjE,SAAS,KAAK,EAAE;QACZ,IAAI,GAAG,MAAM,IAAI,UAAU;QAC3B,MAAO,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAG,IAAI;YAC1C,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO;YAC3J,IAAI,IAAI,GAAG,GAAG,KAAK;gBAAC,EAAE,CAAC,EAAE,GAAG;gBAAG,EAAE,KAAK;aAAC;YACvC,OAAQ,EAAE,CAAC,EAAE;gBACT,KAAK;gBAAG,KAAK;oBAAG,IAAI;oBAAI;gBACxB,KAAK;oBAAG,EAAE,KAAK;oBAAI,OAAO;wBAAE,OAAO,EAAE,CAAC,EAAE;wBAAE,MAAM;oBAAM;gBACtD,KAAK;oBAAG,EAAE,KAAK;oBAAI,IAAI,EAAE,CAAC,EAAE;oBAAE,KAAK;wBAAC;qBAAE;oBAAE;gBACxC,KAAK;oBAAG,KAAK,EAAE,GAAG,CAAC,GAAG;oBAAI,EAAE,IAAI,CAAC,GAAG;oBAAI;gBACxC;oBACI,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG;wBAAE,IAAI;wBAAG;oBAAU;oBAC3G,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,KAAM,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,AAAC,GAAG;wBAAE,EAAE,KAAK,GAAG,EAAE,CAAC,EAAE;wBAAE;oBAAO;oBACrF,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,IAAI;wBAAI;oBAAO;oBACpE,IAAI,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,EAAE,GAAG,CAAC,IAAI,CAAC;wBAAK;oBAAO;oBAClE,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,GAAG;oBACnB,EAAE,IAAI,CAAC,GAAG;oBAAI;YACtB;YACA,KAAK,KAAK,IAAI,CAAC,SAAS;QAC5B,EAAE,OAAO,GAAG;YAAE,KAAK;gBAAC;gBAAG;aAAE;YAAE,IAAI;QAAG,SAAU;YAAE,IAAI,IAAI;QAAG;QACzD,IAAI,EAAE,CAAC,EAAE,GAAG,GAAG,MAAM,EAAE,CAAC,EAAE;QAAE,OAAO;YAAE,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,KAAK;YAAG,MAAM;QAAK;IACnF;AACF;AAEO,IAAI,kBAAkB,OAAO,MAAM,GAAI,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;IAChE,IAAI,OAAO,WAAW,KAAK;IAC3B,IAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG;IAC9C,IAAI,CAAC,QAAQ,CAAC,SAAS,OAAO,CAAC,EAAE,UAAU,GAAG,KAAK,QAAQ,IAAI,KAAK,YAAY,GAAG;QAC/E,OAAO;YAAE,YAAY;YAAM,KAAK;gBAAa,OAAO,CAAC,CAAC,EAAE;YAAE;QAAE;IAChE;IACA,OAAO,cAAc,CAAC,GAAG,IAAI;AAC/B,IAAM,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;IACxB,IAAI,OAAO,WAAW,KAAK;IAC3B,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE;AACd;AAEO,SAAS,aAAa,CAAC,EAAE,CAAC;IAC/B,IAAK,IAAI,KAAK,EAAG,IAAI,MAAM,aAAa,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,gBAAgB,GAAG,GAAG;AAC7G;AAEO,SAAS,SAAS,CAAC;IACxB,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACtD;AAEO,SAAS,OAAO,CAAC,EAAE,CAAC;IACzB,IAAI,IAAI,OAAO,WAAW,cAAc,CAAC,CAAC,OAAO,QAAQ,CAAC;IAC1D,IAAI,CAAC,GAAG,OAAO;IACf,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,KAAK,EAAE,EAAE;IAC/B,IAAI;QACA,MAAO,CAAC,MAAM,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,IAAI,CAAE,GAAG,IAAI,CAAC,EAAE,KAAK;IAC7E,EACA,OAAO,OAAO;QAAE,IAAI;YAAE,OAAO;QAAM;IAAG,SAC9B;QACJ,IAAI;YACA,IAAI,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,IAAI,CAAC;QAClD,SACQ;YAAE,IAAI,GAAG,MAAM,EAAE,KAAK;QAAE;IACpC;IACA,OAAO;AACT;AAGO,SAAS;IACd,IAAK,IAAI,KAAK,EAAE,EAAE,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAC3C,KAAK,GAAG,MAAM,CAAC,OAAO,SAAS,CAAC,EAAE;IACtC,OAAO;AACT;AAGO,SAAS;IACd,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK,UAAU,MAAM,EAAE,IAAI,IAAI,IAAK,KAAK,SAAS,CAAC,EAAE,CAAC,MAAM;IACnF,IAAK,IAAI,IAAI,MAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,IACzC,IAAK,IAAI,IAAI,SAAS,CAAC,EAAE,EAAE,IAAI,GAAG,KAAK,EAAE,MAAM,EAAE,IAAI,IAAI,KAAK,IAC1D,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACnB,OAAO;AACT;AAEO,SAAS,cAAc,EAAE,EAAE,IAAI,EAAE,IAAI;IAC1C,IAAI,QAAQ,UAAU,MAAM,KAAK,GAAG,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,GAAG,IAAK;QACjF,IAAI,MAAM,CAAC,CAAC,KAAK,IAAI,GAAG;YACpB,IAAI,CAAC,IAAI,KAAK,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG;YAClD,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;QACnB;IACJ;IACA,OAAO,GAAG,MAAM,CAAC,MAAM,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;AACpD;AAEO,SAAS,QAAQ,CAAC;IACvB,OAAO,IAAI,YAAY,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,QAAQ;AACpE;AAEO,SAAS,iBAAiB,OAAO,EAAE,UAAU,EAAE,SAAS;IAC7D,IAAI,CAAC,OAAO,aAAa,EAAE,MAAM,IAAI,UAAU;IAC/C,IAAI,IAAI,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,GAAG,GAAG,IAAI,EAAE;IAC7D,OAAO,IAAI,OAAO,MAAM,CAAC,CAAC,OAAO,kBAAkB,aAAa,gBAAgB,MAAM,EAAE,SAAS,GAAG,KAAK,SAAS,KAAK,UAAU,KAAK,UAAU,cAAc,CAAC,CAAC,OAAO,aAAa,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG;;IACtN,SAAS,YAAY,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG;QAAS;IAAG;IAC9F,SAAS,KAAK,CAAC,EAAE,CAAC;QAAI,IAAI,CAAC,CAAC,EAAE,EAAE;YAAE,CAAC,CAAC,EAAE,GAAG,SAAU,CAAC;gBAAI,OAAO,IAAI,QAAQ,SAAU,CAAC,EAAE,CAAC;oBAAI,EAAE,IAAI,CAAC;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE,IAAI,KAAK,OAAO,GAAG;gBAAI;YAAI;YAAG,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE;QAAG;IAAE;IACvK,SAAS,OAAO,CAAC,EAAE,CAAC;QAAI,IAAI;YAAE,KAAK,CAAC,CAAC,EAAE,CAAC;QAAK,EAAE,OAAO,GAAG;YAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAAI;IAAE;IACjF,SAAS,KAAK,CAAC;QAAI,EAAE,KAAK,YAAY,UAAU,QAAQ,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,UAAU,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;IAAI;IACvH,SAAS,QAAQ,KAAK;QAAI,OAAO,QAAQ;IAAQ;IACjD,SAAS,OAAO,KAAK;QAAI,OAAO,SAAS;IAAQ;IACjD,SAAS,OAAO,CAAC,EAAE,CAAC;QAAI,IAAI,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;IAAG;AACnF;AAEO,SAAS,iBAAiB,CAAC;IAChC,IAAI,GAAG;IACP,OAAO,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,SAAS,SAAU,CAAC;QAAI,MAAM;IAAG,IAAI,KAAK,WAAW,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG;;IAC1I,SAAS,KAAK,CAAC,EAAE,CAAC;QAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,SAAU,CAAC;YAAI,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI;gBAAE,OAAO,QAAQ,CAAC,CAAC,EAAE,CAAC;gBAAK,MAAM;YAAM,IAAI,IAAI,EAAE,KAAK;QAAG,IAAI;IAAG;AACvI;AAEO,SAAS,cAAc,CAAC;IAC7B,IAAI,CAAC,OAAO,aAAa,EAAE,MAAM,IAAI,UAAU;IAC/C,IAAI,IAAI,CAAC,CAAC,OAAO,aAAa,CAAC,EAAE;IACjC,OAAO,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,aAAa,aAAa,SAAS,KAAK,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,UAAU,KAAK,WAAW,CAAC,CAAC,OAAO,aAAa,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG,CAAC;;IAC/M,SAAS,KAAK,CAAC;QAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,SAAU,CAAC;YAAI,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;gBAAI,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,OAAO,SAAS,QAAQ,EAAE,IAAI,EAAE,EAAE,KAAK;YAAG;QAAI;IAAG;IAC/J,SAAS,OAAO,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;QAAI,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;YAAI,QAAQ;gBAAE,OAAO;gBAAG,MAAM;YAAE;QAAI,GAAG;IAAS;AAC7H;AAEO,SAAS,qBAAqB,MAAM,EAAE,GAAG;IAC9C,IAAI,OAAO,cAAc,EAAE;QAAE,OAAO,cAAc,CAAC,QAAQ,OAAO;YAAE,OAAO;QAAI;IAAI,OAAO;QAAE,OAAO,GAAG,GAAG;IAAK;IAC9G,OAAO;AACT;;AAEA,IAAI,qBAAqB,OAAO,MAAM,GAAI,SAAS,CAAC,EAAE,CAAC;IACrD,OAAO,cAAc,CAAC,GAAG,WAAW;QAAE,YAAY;QAAM,OAAO;IAAE;AACnE,IAAK,SAAS,CAAC,EAAE,CAAC;IAChB,CAAC,CAAC,UAAU,GAAG;AACjB;AAEA,IAAI,UAAU,SAAS,CAAC;IACtB,UAAU,OAAO,mBAAmB,IAAI,SAAU,CAAC;QACjD,IAAI,KAAK,EAAE;QACX,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,MAAM,CAAC,GAAG;QACjF,OAAO;IACT;IACA,OAAO,QAAQ;AACjB;AAEO,SAAS,aAAa,GAAG;IAC9B,IAAI,OAAO,IAAI,UAAU,EAAE,OAAO;IAClC,IAAI,SAAS,CAAC;IACd,IAAI,OAAO,MAAM;QAAA,IAAK,IAAI,IAAI,QAAQ,MAAM,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK,IAAI,CAAC,CAAC,EAAE,KAAK,WAAW,gBAAgB,QAAQ,KAAK,CAAC,CAAC,EAAE;IAAC;IAChI,mBAAmB,QAAQ;IAC3B,OAAO;AACT;AAEO,SAAS,gBAAgB,GAAG;IACjC,OAAO,AAAC,OAAO,IAAI,UAAU,GAAI,MAAM;QAAE,SAAS;IAAI;AACxD;AAEO,SAAS,uBAAuB,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IAC7D,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE,KAAK,GAAG,MAAM,GAAG,CAAC;AACtF;AAEO,SAAS,uBAAuB,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACpE,IAAI,SAAS,KAAK,MAAM,IAAI,UAAU;IACtC,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,AAAC,SAAS,MAAM,EAAE,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,KAAK,GAAG,QAAQ,MAAM,GAAG,CAAC,UAAU,QAAS;AACtG;AAEO,SAAS,sBAAsB,KAAK,EAAE,QAAQ;IACnD,IAAI,aAAa,QAAS,OAAO,aAAa,YAAY,OAAO,aAAa,YAAa,MAAM,IAAI,UAAU;IAC/G,OAAO,OAAO,UAAU,aAAa,aAAa,QAAQ,MAAM,GAAG,CAAC;AACtE;AAEO,SAAS,wBAAwB,GAAG,EAAE,KAAK,EAAE,KAAK;IACvD,IAAI,UAAU,QAAQ,UAAU,KAAK,GAAG;QACtC,IAAI,OAAO,UAAU,YAAY,OAAO,UAAU,YAAY,MAAM,IAAI,UAAU;QAClF,IAAI,SAAS;QACb,IAAI,OAAO;YACT,IAAI,CAAC,OAAO,YAAY,EAAE,MAAM,IAAI,UAAU;YAC9C,UAAU,KAAK,CAAC,OAAO,YAAY,CAAC;QACtC;QACA,IAAI,YAAY,KAAK,GAAG;YACtB,IAAI,CAAC,OAAO,OAAO,EAAE,MAAM,IAAI,UAAU;YACzC,UAAU,KAAK,CAAC,OAAO,OAAO,CAAC;YAC/B,IAAI,OAAO,QAAQ;QACrB;QACA,IAAI,OAAO,YAAY,YAAY,MAAM,IAAI,UAAU;QACvD,IAAI,OAAO,UAAU;YAAa,IAAI;gBAAE,MAAM,IAAI,CAAC,IAAI;YAAG,EAAE,OAAO,GAAG;gBAAE,OAAO,QAAQ,MAAM,CAAC;YAAI;QAAE;QACpG,IAAI,KAAK,CAAC,IAAI,CAAC;YAAE,OAAO;YAAO,SAAS;YAAS,OAAO;QAAM;IAChE,OACK,IAAI,OAAO;QACd,IAAI,KAAK,CAAC,IAAI,CAAC;YAAE,OAAO;QAAK;IAC/B;IACA,OAAO;AACT;AAEA,IAAI,mBAAmB,OAAO,oBAAoB,aAAa,kBAAkB,SAAU,KAAK,EAAE,UAAU,EAAE,OAAO;IACnH,IAAI,IAAI,IAAI,MAAM;IAClB,OAAO,EAAE,IAAI,GAAG,mBAAmB,EAAE,KAAK,GAAG,OAAO,EAAE,UAAU,GAAG,YAAY;AACjF;AAEO,SAAS,mBAAmB,GAAG;IACpC,SAAS,KAAK,CAAC;QACb,IAAI,KAAK,GAAG,IAAI,QAAQ,GAAG,IAAI,iBAAiB,GAAG,IAAI,KAAK,EAAE,8CAA8C;QAC5G,IAAI,QAAQ,GAAG;IACjB;IACA,IAAI,GAAG,IAAI;IACX,SAAS;QACP,MAAO,IAAI,IAAI,KAAK,CAAC,GAAG,GAAI;YAC1B,IAAI;gBACF,IAAI,CAAC,EAAE,KAAK,IAAI,MAAM,GAAG,OAAO,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,QAAQ,OAAO,GAAG,IAAI,CAAC;gBACjF,IAAI,EAAE,OAAO,EAAE;oBACb,IAAI,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK;oBACnC,IAAI,EAAE,KAAK,EAAE,OAAO,KAAK,GAAG,QAAQ,OAAO,CAAC,QAAQ,IAAI,CAAC,MAAM,SAAS,CAAC;wBAAI,KAAK;wBAAI,OAAO;oBAAQ;gBACvG,OACK,KAAK;YACZ,EACA,OAAO,GAAG;gBACR,KAAK;YACP;QACF;QACA,IAAI,MAAM,GAAG,OAAO,IAAI,QAAQ,GAAG,QAAQ,MAAM,CAAC,IAAI,KAAK,IAAI,QAAQ,OAAO;QAC9E,IAAI,IAAI,QAAQ,EAAE,MAAM,IAAI,KAAK;IACnC;IACA,OAAO;AACT;AAEO,SAAS,iCAAiC,IAAI,EAAE,WAAW;IAChE,IAAI,OAAO,SAAS,YAAY,WAAW,IAAI,CAAC,OAAO;QACnD,OAAO,KAAK,OAAO,CAAC,oDAAoD,SAAU,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE;YAChG,OAAO,MAAM,cAAc,SAAS,QAAQ,KAAK,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,IAAK,IAAI,MAAM,MAAM,GAAG,WAAW,KAAK;QAC7G;IACJ;IACA,OAAO;AACT;uCAEe;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF", "ignoreList": [0]}}, {"offset": {"line": 6886, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["turbopack:///[project]/node_modules/lower-case/src/index.ts"], "sourceRoot": "", "sourcesContent": ["/**\n * Locale character mapping rules.\n */\ninterface Locale {\n  regexp: RegExp;\n  map: Record<string, string>;\n}\n\n/**\n * Source: ftp://ftp.unicode.org/Public/UCD/latest/ucd/SpecialCasing.txt\n */\nconst SUPPORTED_LOCALE: Record<string, Locale> = {\n  tr: {\n    regexp: /\\u0130|\\u0049|\\u0049\\u0307/g,\n    map: {\n      İ: \"\\u0069\",\n      I: \"\\u0131\",\n      İ: \"\\u0069\",\n    },\n  },\n  az: {\n    regexp: /\\u0130/g,\n    map: {\n      İ: \"\\u0069\",\n      I: \"\\u0131\",\n      İ: \"\\u0069\",\n    },\n  },\n  lt: {\n    regexp: /\\u0049|\\u004A|\\u012E|\\u00CC|\\u00CD|\\u0128/g,\n    map: {\n      I: \"\\u0069\\u0307\",\n      J: \"\\u006A\\u0307\",\n      Į: \"\\u012F\\u0307\",\n      Ì: \"\\u0069\\u0307\\u0300\",\n      Í: \"\\u0069\\u0307\\u0301\",\n      Ĩ: \"\\u0069\\u0307\\u0303\",\n    },\n  },\n};\n\n/**\n * Localized lower case.\n */\nexport function localeLowerCase(str: string, locale: string) {\n  const lang = SUPPORTED_LOCALE[locale.toLowerCase()];\n  if (lang) return lowerCase(str.replace(lang.regexp, (m) => lang.map[m]));\n  return lowerCase(str);\n}\n\n/**\n * Lower case as a function.\n */\nexport function lowerCase(str: string) {\n  return str.toLowerCase();\n}\n"], "names": [], "mappings": "AAQA;;GAEG;;;;AACH,IAAM,gBAAgB,GAA2B;IAC/C,EAAE,EAAE;QACF,MAAM,EAAE,6BAA6B;QACrC,GAAG,EAAE;YACH,CAAC,EAAE,QAAQ;YACX,CAAC,EAAE,QAAQ;YACX,EAAE,EAAE,QAAQ;SACb;KACF;IACD,EAAE,EAAE;QACF,MAAM,EAAE,SAAS;QACjB,GAAG,EAAE;YACH,CAAC,EAAE,QAAQ;YACX,CAAC,EAAE,QAAQ;YACX,EAAE,EAAE,QAAQ;SACb;KACF;IACD,EAAE,EAAE;QACF,MAAM,EAAE,4CAA4C;QACpD,GAAG,EAAE;YACH,CAAC,EAAE,cAAc;YACjB,CAAC,EAAE,cAAc;YACjB,CAAC,EAAE,cAAc;YACjB,CAAC,EAAE,oBAAoB;YACvB,CAAC,EAAE,oBAAoB;YACvB,CAAC,EAAE,oBAAoB;SACxB;KACF;CACF,CAAC;AAKI,SAAU,eAAe,CAAC,GAAW,EAAE,MAAc;IACzD,IAAM,IAAI,GAAG,gBAAgB,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;IACpD,IAAI,IAAI,EAAE,OAAO,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,SAAC,CAAC;QAAK,OAAA,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAAX,CAAW,CAAC,CAAC,CAAC;IACzE,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;AACxB,CAAC;AAKK,SAAU,SAAS,CAAC,GAAW;IACnC,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC;AAC3B,CAAC", "ignoreList": [0]}}, {"offset": {"line": 6937, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["turbopack:///[project]/node_modules/no-case/src/index.ts"], "sourceRoot": "", "sourcesContent": ["import { lowerCase } from \"lower-case\";\n\nexport interface Options {\n  splitRegexp?: RegExp | RegExp[];\n  stripRegexp?: RegExp | RegExp[];\n  delimiter?: string;\n  transform?: (part: string, index: number, parts: string[]) => string;\n}\n\n// Support camel case (\"camelCase\" -> \"camel Case\" and \"CAMELCase\" -> \"CAMEL Case\").\nconst DEFAULT_SPLIT_REGEXP = [/([a-z0-9])([A-Z])/g, /([A-Z])([A-Z][a-z])/g];\n\n// Remove all non-word characters.\nconst DEFAULT_STRIP_REGEXP = /[^A-Z0-9]+/gi;\n\n/**\n * Normalize the string into something other libraries can manipulate easier.\n */\nexport function noCase(input: string, options: Options = {}) {\n  const {\n    splitRegexp = DEFAULT_SPLIT_REGEXP,\n    stripRegexp = DEFAULT_STRIP_REGEXP,\n    transform = lowerCase,\n    delimiter = \" \",\n  } = options;\n\n  let result = replace(\n    replace(input, splitRegexp, \"$1\\0$2\"),\n    stripRegexp,\n    \"\\0\"\n  );\n  let start = 0;\n  let end = result.length;\n\n  // Trim the delimiter from around the output string.\n  while (result.charAt(start) === \"\\0\") start++;\n  while (result.charAt(end - 1) === \"\\0\") end--;\n\n  // Transform each token independently.\n  return result.slice(start, end).split(\"\\0\").map(transform).join(delimiter);\n}\n\n/**\n * Replace `re` in the input string with the replacement value.\n */\nfunction replace(input: string, re: RegExp | RegExp[], value: string) {\n  if (re instanceof RegExp) return input.replace(re, value);\n  return re.reduce((input, re) => input.replace(re, value), input);\n}\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;;AASvC,oFAAoF;AACpF,IAAM,oBAAoB,GAAG;IAAC,oBAAoB;IAAE,sBAAsB;CAAC,CAAC;AAE5E,kCAAkC;AAClC,IAAM,oBAAoB,GAAG,cAAc,CAAC;AAKtC,SAAU,MAAM,CAAC,KAAa,EAAE,OAAqB;IAArB,IAAA,YAAA,KAAA,GAAA;QAAA,UAAA,CAAA,CAAqB;IAAA;IAEvD,IAAA,KAIE,OAAO,CAAA,WAJyB,EAAlC,WAAW,GAAA,OAAA,KAAA,IAAG,oBAAoB,GAAA,EAAA,EAClC,KAGE,OAAO,CAAA,WAHyB,EAAlC,WAAW,GAAA,OAAA,KAAA,IAAG,oBAAoB,GAAA,EAAA,EAClC,KAEE,OAAO,CAAA,SAFY,EAArB,SAAS,GAAA,OAAA,KAAA,qKAAG,YAAS,GAAA,EAAA,EACrB,KACE,OAAO,CAAA,SADM,EAAf,SAAS,GAAA,OAAA,KAAA,IAAG,GAAG,GAAA,EAAA,CACL;IAEZ,IAAI,MAAM,GAAG,OAAO,CAClB,OAAO,CAAC,KAAK,EAAE,WAAW,EAAE,QAAQ,CAAC,EACrC,WAAW,EACX,IAAI,CACL,CAAC;IACF,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;IAExB,oDAAoD;IACpD,MAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,IAAI,CAAE,KAAK,EAAE,CAAC;IAC9C,MAAO,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,IAAI,CAAE,GAAG,EAAE,CAAC;IAE9C,sCAAsC;IACtC,OAAO,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC7E,CAAC;AAED;;GAEG,CACH,SAAS,OAAO,CAAC,KAAa,EAAE,EAAqB,EAAE,KAAa;IAClE,IAAI,EAAE,YAAY,MAAM,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IAC1D,OAAO,EAAE,CAAC,MAAM,CAAC,SAAC,KAAK,EAAE,EAAE;QAAK,OAAA,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC;IAAxB,CAAwB,EAAE,KAAK,CAAC,CAAC;AACnE,CAAC", "ignoreList": [0]}}, {"offset": {"line": 6977, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["turbopack:///[project]/node_modules/dot-case/src/index.ts"], "sourceRoot": "", "sourcesContent": ["import { noCase, Options } from \"no-case\";\n\nexport { Options };\n\nexport function dotCase(input: string, options: Options = {}) {\n  return noCase(input, {\n    delimiter: \".\",\n    ...options,\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,MAAM,EAAW,MAAM,SAAS,CAAC;;;AAIpC,SAAU,OAAO,CAAC,KAAa,EAAE,OAAqB;IAArB,IAAA,YAAA,KAAA,GAAA;QAAA,UAAA,CAAA,CAAqB;IAAA;IAC1D,yKAAO,SAAA,AAAM,EAAC,KAAK,EAAA,CAAA,GAAA,8IAAA,CAAA,WAAA,EAAA;QACjB,SAAS,EAAE,GAAG;IAAA,GACX,OAAO,EACV,CAAC;AACL,CAAC", "ignoreList": [0]}}, {"offset": {"line": 6998, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["turbopack:///[project]/node_modules/snake-case/src/index.ts"], "sourceRoot": "", "sourcesContent": ["import { dotCase, Options } from \"dot-case\";\n\nexport { Options };\n\nexport function snakeCase(input: string, options: Options = {}) {\n  return dotCase(input, {\n    delimiter: \"_\",\n    ...options,\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,OAAO,EAAW,MAAM,UAAU,CAAC;;;AAItC,SAAU,SAAS,CAAC,KAAa,EAAE,OAAqB;IAArB,IAAA,YAAA,KAAA,GAAA;QAAA,UAAA,CAAA,CAAqB;IAAA;IAC5D,0KAAO,UAAA,AAAO,EAAC,KAAK,EAAA,CAAA,GAAA,8IAAA,CAAA,WAAA,EAAA;QAClB,SAAS,EAAE,GAAG;IAAA,GACX,OAAO,EACV,CAAC;AACL,CAAC", "ignoreList": [0]}}, {"offset": {"line": 7018, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/snakecase-keys/index.js"], "sourcesContent": ["'use strict'\n\nconst map = require('map-obj')\nconst { snakeCase } = require('snake-case')\n\nconst PlainObjectConstructor = {}.constructor\n\nmodule.exports = function (obj, options) {\n  if (Array.isArray(obj)) {\n    if (obj.some(item => item.constructor !== PlainObjectConstructor)) {\n      throw new Error('obj must be array of plain objects')\n    }\n  } else {\n    if (obj.constructor !== PlainObjectConstructor) {\n      throw new Error('obj must be an plain object')\n    }\n  }\n\n  options = Object.assign({ deep: true, exclude: [], parsingOptions: {} }, options)\n\n  return map(obj, function (key, val) {\n    return [\n      matches(options.exclude, key) ? key : snakeCase(key, options.parsingOptions),\n      val,\n      mapperOptions(key, val, options)\n    ]\n  }, options)\n}\n\nfunction matches (patterns, value) {\n  return patterns.some(function (pattern) {\n    return typeof pattern === 'string'\n      ? pattern === value\n      : pattern.test(value)\n  })\n}\n\nfunction mapperOptions (key, val, options) {\n  return options.shouldRecurse\n    ? { shouldRecurse: options.shouldRecurse(key, val) }\n    : undefined\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,EAAE,SAAS,EAAE;AAEnB,MAAM,yBAAyB,CAAC,EAAE,WAAW;AAE7C,OAAO,OAAO,GAAG,SAAU,GAAG,EAAE,OAAO;IACrC,IAAI,MAAM,OAAO,CAAC,MAAM;QACtB,IAAI,IAAI,IAAI,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK,yBAAyB;YACjE,MAAM,IAAI,MAAM;QAClB;IACF,OAAO;QACL,IAAI,IAAI,WAAW,KAAK,wBAAwB;YAC9C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,UAAU,OAAO,MAAM,CAAC;QAAE,MAAM;QAAM,SAAS,EAAE;QAAE,gBAAgB,CAAC;IAAE,GAAG;IAEzE,OAAO,IAAI,KAAK,SAAU,GAAG,EAAE,GAAG;QAChC,OAAO;YACL,QAAQ,QAAQ,OAAO,EAAE,OAAO,MAAM,UAAU,KAAK,QAAQ,cAAc;YAC3E;YACA,cAAc,KAAK,KAAK;SACzB;IACH,GAAG;AACL;AAEA,SAAS,QAAS,QAAQ,EAAE,KAAK;IAC/B,OAAO,SAAS,IAAI,CAAC,SAAU,OAAO;QACpC,OAAO,OAAO,YAAY,WACtB,YAAY,QACZ,QAAQ,IAAI,CAAC;IACnB;AACF;AAEA,SAAS,cAAe,GAAG,EAAE,GAAG,EAAE,OAAO;IACvC,OAAO,QAAQ,aAAa,GACxB;QAAE,eAAe,QAAQ,aAAa,CAAC,KAAK;IAAK,IACjD;AACN", "ignoreList": [0]}}, {"offset": {"line": 7060, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["turbopack:///[project]/node_modules/cookie/src/index.ts"], "sourceRoot": "", "sourcesContent": ["/**\n * RegExp to match cookie-name in RFC 6265 sec 4.1.1\n * This refers out to the obsoleted definition of token in RFC 2616 sec 2.2\n * which has been replaced by the token definition in RFC 7230 appendix B.\n *\n * cookie-name       = token\n * token             = 1*tchar\n * tchar             = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" /\n *                     \"*\" / \"+\" / \"-\" / \".\" / \"^\" / \"_\" /\n *                     \"`\" / \"|\" / \"~\" / DIGIT / ALPHA\n *\n * Note: Allowing more characters - https://github.com/jshttp/cookie/issues/191\n * Allow same range as cookie value, except `=`, which delimits end of name.\n */\nconst cookieNameRegExp = /^[\\u0021-\\u003A\\u003C\\u003E-\\u007E]+$/;\n\n/**\n * RegExp to match cookie-value in RFC 6265 sec 4.1.1\n *\n * cookie-value      = *cookie-octet / ( DQUOTE *cookie-octet DQUOTE )\n * cookie-octet      = %x21 / %x23-2B / %x2D-3A / %x3C-5B / %x5D-7E\n *                     ; US-ASCII characters excluding CTLs,\n *                     ; whitespace DQUOTE, comma, semicolon,\n *                     ; and backslash\n *\n * Allowing more characters: https://github.com/jshttp/cookie/issues/191\n * Comma, backslash, and DQUOTE are not part of the parsing algorithm.\n */\nconst cookieValueRegExp = /^[\\u0021-\\u003A\\u003C-\\u007E]*$/;\n\n/**\n * RegExp to match domain-value in RFC 6265 sec 4.1.1\n *\n * domain-value      = <subdomain>\n *                     ; defined in [RFC1034], Section 3.5, as\n *                     ; enhanced by [RFC1123], Section 2.1\n * <subdomain>       = <label> | <subdomain> \".\" <label>\n * <label>           = <let-dig> [ [ <ldh-str> ] <let-dig> ]\n *                     Labels must be 63 characters or less.\n *                     'let-dig' not 'letter' in the first char, per RFC1123\n * <ldh-str>         = <let-dig-hyp> | <let-dig-hyp> <ldh-str>\n * <let-dig-hyp>     = <let-dig> | \"-\"\n * <let-dig>         = <letter> | <digit>\n * <letter>          = any one of the 52 alphabetic characters A through Z in\n *                     upper case and a through z in lower case\n * <digit>           = any one of the ten digits 0 through 9\n *\n * Keep support for leading dot: https://github.com/jshttp/cookie/issues/173\n *\n * > (Note that a leading %x2E (\".\"), if present, is ignored even though that\n * character is not permitted, but a trailing %x2E (\".\"), if present, will\n * cause the user agent to ignore the attribute.)\n */\nconst domainValueRegExp =\n  /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;\n\n/**\n * RegExp to match path-value in RFC 6265 sec 4.1.1\n *\n * path-value        = <any CHAR except CTLs or \";\">\n * CHAR              = %x01-7F\n *                     ; defined in RFC 5234 appendix B.1\n */\nconst pathValueRegExp = /^[\\u0020-\\u003A\\u003D-\\u007E]*$/;\n\nconst __toString = Object.prototype.toString;\n\nconst NullObject = /* @__PURE__ */ (() => {\n  const C = function () {};\n  C.prototype = Object.create(null);\n  return C;\n})() as unknown as { new (): any };\n\n/**\n * Parse options.\n */\nexport interface ParseOptions {\n  /**\n   * Specifies a function that will be used to decode a [cookie-value](https://datatracker.ietf.org/doc/html/rfc6265#section-4.1.1).\n   * Since the value of a cookie has a limited character set (and must be a simple string), this function can be used to decode\n   * a previously-encoded cookie value into a JavaScript string.\n   *\n   * The default function is the global `decodeURIComponent`, wrapped in a `try..catch`. If an error\n   * is thrown it will return the cookie's original value. If you provide your own encode/decode\n   * scheme you must ensure errors are appropriately handled.\n   *\n   * @default decode\n   */\n  decode?: (str: string) => string | undefined;\n}\n\n/**\n * Parse a cookie header.\n *\n * Parse the given cookie header string into an object\n * The object has the various cookies as keys(names) => values\n */\nexport function parse(\n  str: string,\n  options?: ParseOptions,\n): Record<string, string | undefined> {\n  const obj: Record<string, string | undefined> = new NullObject();\n  const len = str.length;\n  // RFC 6265 sec 4.1.1, RFC 2616 2.2 defines a cookie name consists of one char minimum, plus '='.\n  if (len < 2) return obj;\n\n  const dec = options?.decode || decode;\n  let index = 0;\n\n  do {\n    const eqIdx = str.indexOf(\"=\", index);\n    if (eqIdx === -1) break; // No more cookie pairs.\n\n    const colonIdx = str.indexOf(\";\", index);\n    const endIdx = colonIdx === -1 ? len : colonIdx;\n\n    if (eqIdx > endIdx) {\n      // backtrack on prior semicolon\n      index = str.lastIndexOf(\";\", eqIdx - 1) + 1;\n      continue;\n    }\n\n    const keyStartIdx = startIndex(str, index, eqIdx);\n    const keyEndIdx = endIndex(str, eqIdx, keyStartIdx);\n    const key = str.slice(keyStartIdx, keyEndIdx);\n\n    // only assign once\n    if (obj[key] === undefined) {\n      let valStartIdx = startIndex(str, eqIdx + 1, endIdx);\n      let valEndIdx = endIndex(str, endIdx, valStartIdx);\n\n      const value = dec(str.slice(valStartIdx, valEndIdx));\n      obj[key] = value;\n    }\n\n    index = endIdx + 1;\n  } while (index < len);\n\n  return obj;\n}\n\nfunction startIndex(str: string, index: number, max: number) {\n  do {\n    const code = str.charCodeAt(index);\n    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index;\n  } while (++index < max);\n  return max;\n}\n\nfunction endIndex(str: string, index: number, min: number) {\n  while (index > min) {\n    const code = str.charCodeAt(--index);\n    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index + 1;\n  }\n  return min;\n}\n\n/**\n * Serialize options.\n */\nexport interface SerializeOptions {\n  /**\n   * Specifies a function that will be used to encode a [cookie-value](https://datatracker.ietf.org/doc/html/rfc6265#section-4.1.1).\n   * Since value of a cookie has a limited character set (and must be a simple string), this function can be used to encode\n   * a value into a string suited for a cookie's value, and should mirror `decode` when parsing.\n   *\n   * @default encodeURIComponent\n   */\n  encode?: (str: string) => string;\n  /**\n   * Specifies the `number` (in seconds) to be the value for the [`Max-Age` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.2).\n   *\n   * The [cookie storage model specification](https://tools.ietf.org/html/rfc6265#section-5.3) states that if both `expires` and\n   * `maxAge` are set, then `maxAge` takes precedence, but it is possible not all clients by obey this,\n   * so if both are set, they should point to the same date and time.\n   */\n  maxAge?: number;\n  /**\n   * Specifies the `Date` object to be the value for the [`Expires` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.1).\n   * When no expiration is set clients consider this a \"non-persistent cookie\" and delete it the current session is over.\n   *\n   * The [cookie storage model specification](https://tools.ietf.org/html/rfc6265#section-5.3) states that if both `expires` and\n   * `maxAge` are set, then `maxAge` takes precedence, but it is possible not all clients by obey this,\n   * so if both are set, they should point to the same date and time.\n   */\n  expires?: Date;\n  /**\n   * Specifies the value for the [`Domain` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.3).\n   * When no domain is set clients consider the cookie to apply to the current domain only.\n   */\n  domain?: string;\n  /**\n   * Specifies the value for the [`Path` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.4).\n   * When no path is set, the path is considered the [\"default path\"](https://tools.ietf.org/html/rfc6265#section-5.1.4).\n   */\n  path?: string;\n  /**\n   * Enables the [`HttpOnly` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.6).\n   * When enabled, clients will not allow client-side JavaScript to see the cookie in `document.cookie`.\n   */\n  httpOnly?: boolean;\n  /**\n   * Enables the [`Secure` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.5).\n   * When enabled, clients will only send the cookie back if the browser has a HTTPS connection.\n   */\n  secure?: boolean;\n  /**\n   * Enables the [`Partitioned` `Set-Cookie` attribute](https://tools.ietf.org/html/draft-cutler-httpbis-partitioned-cookies/).\n   * When enabled, clients will only send the cookie back when the current domain _and_ top-level domain matches.\n   *\n   * This is an attribute that has not yet been fully standardized, and may change in the future.\n   * This also means clients may ignore this attribute until they understand it. More information\n   * about can be found in [the proposal](https://github.com/privacycg/CHIPS).\n   */\n  partitioned?: boolean;\n  /**\n   * Specifies the value for the [`Priority` `Set-Cookie` attribute](https://tools.ietf.org/html/draft-west-cookie-priority-00#section-4.1).\n   *\n   * - `'low'` will set the `Priority` attribute to `Low`.\n   * - `'medium'` will set the `Priority` attribute to `Medium`, the default priority when not set.\n   * - `'high'` will set the `Priority` attribute to `High`.\n   *\n   * More information about priority levels can be found in [the specification](https://tools.ietf.org/html/draft-west-cookie-priority-00#section-4.1).\n   */\n  priority?: \"low\" | \"medium\" | \"high\";\n  /**\n   * Specifies the value for the [`SameSite` `Set-Cookie` attribute](https://tools.ietf.org/html/draft-ietf-httpbis-rfc6265bis-09#section-5.4.7).\n   *\n   * - `true` will set the `SameSite` attribute to `Strict` for strict same site enforcement.\n   * - `'lax'` will set the `SameSite` attribute to `Lax` for lax same site enforcement.\n   * - `'none'` will set the `SameSite` attribute to `None` for an explicit cross-site cookie.\n   * - `'strict'` will set the `SameSite` attribute to `Strict` for strict same site enforcement.\n   *\n   * More information about enforcement levels can be found in [the specification](https://tools.ietf.org/html/draft-ietf-httpbis-rfc6265bis-09#section-5.4.7).\n   */\n  sameSite?: boolean | \"lax\" | \"strict\" | \"none\";\n}\n\n/**\n * Serialize data into a cookie header.\n *\n * Serialize a name value pair into a cookie string suitable for\n * http headers. An optional options object specifies cookie parameters.\n *\n * serialize('foo', 'bar', { httpOnly: true })\n *   => \"foo=bar; httpOnly\"\n */\nexport function serialize(\n  name: string,\n  val: string,\n  options?: SerializeOptions,\n): string {\n  const enc = options?.encode || encodeURIComponent;\n\n  if (!cookieNameRegExp.test(name)) {\n    throw new TypeError(`argument name is invalid: ${name}`);\n  }\n\n  const value = enc(val);\n\n  if (!cookieValueRegExp.test(value)) {\n    throw new TypeError(`argument val is invalid: ${val}`);\n  }\n\n  let str = name + \"=\" + value;\n  if (!options) return str;\n\n  if (options.maxAge !== undefined) {\n    if (!Number.isInteger(options.maxAge)) {\n      throw new TypeError(`option maxAge is invalid: ${options.maxAge}`);\n    }\n\n    str += \"; Max-Age=\" + options.maxAge;\n  }\n\n  if (options.domain) {\n    if (!domainValueRegExp.test(options.domain)) {\n      throw new TypeError(`option domain is invalid: ${options.domain}`);\n    }\n\n    str += \"; Domain=\" + options.domain;\n  }\n\n  if (options.path) {\n    if (!pathValueRegExp.test(options.path)) {\n      throw new TypeError(`option path is invalid: ${options.path}`);\n    }\n\n    str += \"; Path=\" + options.path;\n  }\n\n  if (options.expires) {\n    if (\n      !isDate(options.expires) ||\n      !Number.isFinite(options.expires.valueOf())\n    ) {\n      throw new TypeError(`option expires is invalid: ${options.expires}`);\n    }\n\n    str += \"; Expires=\" + options.expires.toUTCString();\n  }\n\n  if (options.httpOnly) {\n    str += \"; HttpOnly\";\n  }\n\n  if (options.secure) {\n    str += \"; Secure\";\n  }\n\n  if (options.partitioned) {\n    str += \"; Partitioned\";\n  }\n\n  if (options.priority) {\n    const priority =\n      typeof options.priority === \"string\"\n        ? options.priority.toLowerCase()\n        : undefined;\n    switch (priority) {\n      case \"low\":\n        str += \"; Priority=Low\";\n        break;\n      case \"medium\":\n        str += \"; Priority=Medium\";\n        break;\n      case \"high\":\n        str += \"; Priority=High\";\n        break;\n      default:\n        throw new TypeError(`option priority is invalid: ${options.priority}`);\n    }\n  }\n\n  if (options.sameSite) {\n    const sameSite =\n      typeof options.sameSite === \"string\"\n        ? options.sameSite.toLowerCase()\n        : options.sameSite;\n    switch (sameSite) {\n      case true:\n      case \"strict\":\n        str += \"; SameSite=Strict\";\n        break;\n      case \"lax\":\n        str += \"; SameSite=Lax\";\n        break;\n      case \"none\":\n        str += \"; SameSite=None\";\n        break;\n      default:\n        throw new TypeError(`option sameSite is invalid: ${options.sameSite}`);\n    }\n  }\n\n  return str;\n}\n\n/**\n * URL-decode string value. Optimized to skip native call when no %.\n */\nfunction decode(str: string): string {\n  if (str.indexOf(\"%\") === -1) return str;\n\n  try {\n    return decodeURIComponent(str);\n  } catch (e) {\n    return str;\n  }\n}\n\n/**\n * Determine if value is a Date.\n */\nfunction isDate(val: any): val is Date {\n  return __toString.call(val) === \"[object Date]\";\n}\n"], "names": [], "mappings": ";;;;AAiGA,QAAA,KAAA,GAAA,MA0CC;AA4GD,QAAA,SAAA,GAAA,UA6GC;AApWD;;;;;;;;;;;;;GAaG,CACH,MAAM,gBAAgB,GAAG,uCAAuC,CAAC;AAEjE;;;;;;;;;;;GAWG,CACH,MAAM,iBAAiB,GAAG,iCAAiC,CAAC;AAE5D;;;;;;;;;;;;;;;;;;;;;;GAsBG,CACH,MAAM,iBAAiB,GACrB,qFAAqF,CAAC;AAExF;;;;;;GAMG,CACH,MAAM,eAAe,GAAG,iCAAiC,CAAC;AAE1D,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAE7C,MAAM,UAAU,GAAG,aAAA,EAAe,CAAC,CAAC,GAAG,EAAE;IACvC,MAAM,CAAC,GAAG,YAAa,CAAC,CAAC;IACzB,CAAC,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAClC,OAAO,CAAC,CAAC;AACX,CAAC,CAAC,EAAgC,CAAC;AAoBnC;;;;;GAKG,CACH,SAAgB,KAAK,CACnB,GAAW,EACX,OAAsB;IAEtB,MAAM,GAAG,GAAuC,IAAI,UAAU,EAAE,CAAC;IACjE,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;IACvB,iGAAiG;IACjG,IAAI,GAAG,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC;IAExB,MAAM,GAAG,GAAG,OAAO,EAAE,MAAM,IAAI,MAAM,CAAC;IACtC,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,GAAG,CAAC;QACF,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACtC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,wBAAwB;QAEjD,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACzC,MAAM,MAAM,GAAG,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC;QAEhD,IAAI,KAAK,GAAG,MAAM,EAAE,CAAC;YACnB,+BAA+B;YAC/B,KAAK,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAC5C,SAAS;QACX,CAAC;QAED,MAAM,WAAW,GAAG,UAAU,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAClD,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;QACpD,MAAM,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QAE9C,mBAAmB;QACnB,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE,CAAC;YAC3B,IAAI,WAAW,GAAG,UAAU,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;YACrD,IAAI,SAAS,GAAG,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;YAEnD,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC;YACrD,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACnB,CAAC;QAED,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC;IACrB,CAAC,OAAQ,KAAK,GAAG,GAAG,CAAE;IAEtB,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,UAAU,CAAC,GAAW,EAAE,KAAa,EAAE,GAAW;IACzD,GAAG,CAAC;QACF,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,IAAI,KAAK,IAAI,CAAC,KAAA,EAAO,KAAI,IAAI,KAAK,IAAI,CAAC,MAAA,EAAQ,GAAE,OAAO,KAAK,CAAC;IACpE,CAAC,OAAQ,EAAE,KAAK,GAAG,GAAG,CAAE;IACxB,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,QAAQ,CAAC,GAAW,EAAE,KAAa,EAAE,GAAW;IACvD,MAAO,KAAK,GAAG,GAAG,CAAE,CAAC;QACnB,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,CAAC;QACrC,IAAI,IAAI,KAAK,IAAI,CAAC,KAAA,EAAO,KAAI,IAAI,KAAK,IAAI,CAAC,MAAA,EAAQ,GAAE,OAAO,KAAK,GAAG,CAAC,CAAC;IACxE,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAmFD;;;;;;;;GAQG,CACH,SAAgB,SAAS,CACvB,IAAY,EACZ,GAAW,EACX,OAA0B;IAE1B,MAAM,GAAG,GAAG,OAAO,EAAE,MAAM,IAAI,kBAAkB,CAAC;IAElD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACjC,MAAM,IAAI,SAAS,CAAC,CAAA,0BAAA,EAA6B,IAAI,EAAE,CAAC,CAAC;IAC3D,CAAC;IAED,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAEvB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QACnC,MAAM,IAAI,SAAS,CAAC,CAAA,yBAAA,EAA4B,GAAG,EAAE,CAAC,CAAC;IACzD,CAAC;IAED,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC;IAC7B,IAAI,CAAC,OAAO,EAAE,OAAO,GAAG,CAAC;IAEzB,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,SAAS,CAAC,CAAA,0BAAA,EAA6B,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QACrE,CAAC;QAED,GAAG,IAAI,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC;IACvC,CAAC;IAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACnB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,SAAS,CAAC,CAAA,0BAAA,EAA6B,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QACrE,CAAC;QAED,GAAG,IAAI,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC;IACtC,CAAC;IAED,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;QACjB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACxC,MAAM,IAAI,SAAS,CAAC,CAAA,wBAAA,EAA2B,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;QACjE,CAAC;QAED,GAAG,IAAI,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC;IAClC,CAAC;IAED,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;QACpB,IACE,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,IACxB,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,EAC3C,CAAC;YACD,MAAM,IAAI,SAAS,CAAC,CAAA,2BAAA,EAA8B,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QACvE,CAAC;QAED,GAAG,IAAI,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;IACtD,CAAC;IAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QACrB,GAAG,IAAI,YAAY,CAAC;IACtB,CAAC;IAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACnB,GAAG,IAAI,UAAU,CAAC;IACpB,CAAC;IAED,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;QACxB,GAAG,IAAI,eAAe,CAAC;IACzB,CAAC;IAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QACrB,MAAM,QAAQ,GACZ,OAAO,OAAO,CAAC,QAAQ,KAAK,QAAQ,GAChC,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,GAC9B,SAAS,CAAC;QAChB,OAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,KAAK;gBACR,GAAG,IAAI,gBAAgB,CAAC;gBACxB,MAAM;YACR,KAAK,QAAQ;gBACX,GAAG,IAAI,mBAAmB,CAAC;gBAC3B,MAAM;YACR,KAAK,MAAM;gBACT,GAAG,IAAI,iBAAiB,CAAC;gBACzB,MAAM;YACR;gBACE,MAAM,IAAI,SAAS,CAAC,CAAA,4BAAA,EAA+B,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QACrB,MAAM,QAAQ,GACZ,OAAO,OAAO,CAAC,QAAQ,KAAK,QAAQ,GAChC,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,GAC9B,OAAO,CAAC,QAAQ,CAAC;QACvB,OAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,IAAI,CAAC;YACV,KAAK,QAAQ;gBACX,GAAG,IAAI,mBAAmB,CAAC;gBAC3B,MAAM;YACR,KAAK,KAAK;gBACR,GAAG,IAAI,gBAAgB,CAAC;gBACxB,MAAM;YACR,KAAK,MAAM;gBACT,GAAG,IAAI,iBAAiB,CAAC;gBACzB,MAAM;YACR;gBACE,MAAM,IAAI,SAAS,CAAC,CAAA,4BAAA,EAA+B,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;GAEG,CACH,SAAS,MAAM,CAAC,GAAW;IACzB,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC;IAExC,IAAI,CAAC;QACH,OAAO,kBAAkB,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,GAAG,CAAC;IACb,CAAC;AACH,CAAC;AAED;;GAEG,CACH,SAAS,MAAM,CAAC,GAAQ;IACtB,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,eAAe,CAAC;AAClD,CAAC", "ignoreList": [0]}}, {"offset": {"line": 7286, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/routing/config.js"], "sourcesContent": ["function receiveRoutingConfig(input) {\n  return {\n    ...input,\n    localePrefix: receiveLocalePrefixConfig(input.localePrefix),\n    localeCookie: receiveLocaleCookie(input.localeCookie),\n    localeDetection: input.localeDetection ?? true,\n    alternateLinks: input.alternateLinks ?? true\n  };\n}\nfunction receiveLocaleCookie(localeCookie) {\n  return localeCookie ?? true ? {\n    name: 'NEXT_LOCALE',\n    sameSite: 'lax',\n    ...(typeof localeCookie === 'object' && localeCookie)\n\n    // `path` needs to be provided based on a detected base path\n    // that depends on the environment when setting a cookie\n  } : false;\n}\nfunction receiveLocalePrefixConfig(localePrefix) {\n  return typeof localePrefix === 'object' ? localePrefix : {\n    mode: localePrefix || 'always'\n  };\n}\n\nexport { receiveRoutingConfig };\n"], "names": [], "mappings": ";;;AAAA,SAAS,qBAAqB,KAAK;IACjC,OAAO;QACL,GAAG,KAAK;QACR,cAAc,0BAA0B,MAAM,YAAY;QAC1D,cAAc,oBAAoB,MAAM,YAAY;QACpD,iBAAiB,MAAM,eAAe,IAAI;QAC1C,gBAAgB,MAAM,cAAc,IAAI;IAC1C;AACF;AACA,SAAS,oBAAoB,YAAY;IACvC,OAAO,gBAAgB,OAAO;QAC5B,MAAM;QACN,UAAU;QACV,GAAI,OAAO,iBAAiB,YAAY,YAAY;IAItD,IAAI;AACN;AACA,SAAS,0BAA0B,YAAY;IAC7C,OAAO,OAAO,iBAAiB,WAAW,eAAe;QACvD,MAAM,gBAAgB;IACxB;AACF", "ignoreList": [0]}}, {"offset": {"line": 7317, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/shared/constants.js"], "sourcesContent": ["// Used to read the locale from the middleware\nconst HEADER_LOCALE_NAME = 'X-NEXT-INTL-LOCALE';\n\nexport { HEADER_LOCALE_NAME };\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;AAC9C,MAAM,qBAAqB", "ignoreList": [0]}}, {"offset": {"line": 7329, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/shared/utils.js"], "sourcesContent": ["function isRelativeHref(href) {\n  const pathname = typeof href === 'object' ? href.pathname : href;\n  return pathname != null && !pathname.startsWith('/');\n}\nfunction isLocalHref(href) {\n  if (typeof href === 'object') {\n    return href.host == null && href.hostname == null;\n  } else {\n    const hasProtocol = /^[a-z]+:/i.test(href);\n    return !hasProtocol;\n  }\n}\nfunction isLocalizableHref(href) {\n  return isLocalHref(href) && !isRelativeHref(href);\n}\nfunction unprefixPathname(pathname, prefix) {\n  return pathname.replace(new RegExp(`^${prefix}`), '') || '/';\n}\nfunction prefixPathname(prefix, pathname) {\n  let localizedHref = prefix;\n\n  // Avoid trailing slashes\n  if (/^\\/(\\?.*)?$/.test(pathname)) {\n    pathname = pathname.slice(1);\n  }\n  localizedHref += pathname;\n  return localizedHref;\n}\nfunction hasPathnamePrefixed(prefix, pathname) {\n  return pathname === prefix || pathname.startsWith(`${prefix}/`);\n}\nfunction hasTrailingSlash() {\n  try {\n    // Provided via `env` setting in `next.config.js` via the plugin\n    return process.env._next_intl_trailing_slash === 'true';\n  } catch {\n    return false;\n  }\n}\nfunction getLocalizedTemplate(pathnameConfig, locale, internalTemplate) {\n  return typeof pathnameConfig === 'string' ? pathnameConfig : pathnameConfig[locale] || internalTemplate;\n}\nfunction normalizeTrailingSlash(pathname) {\n  const trailingSlash = hasTrailingSlash();\n  if (pathname !== '/') {\n    const pathnameEndsWithSlash = pathname.endsWith('/');\n    if (trailingSlash && !pathnameEndsWithSlash) {\n      pathname += '/';\n    } else if (!trailingSlash && pathnameEndsWithSlash) {\n      pathname = pathname.slice(0, -1);\n    }\n  }\n  return pathname;\n}\nfunction matchesPathname(/** E.g. `/users/[userId]-[userName]` */\ntemplate, /** E.g. `/users/23-jane` */\npathname) {\n  const normalizedTemplate = normalizeTrailingSlash(template);\n  const normalizedPathname = normalizeTrailingSlash(pathname);\n  const regex = templateToRegex(normalizedTemplate);\n  return regex.test(normalizedPathname);\n}\nfunction getLocalePrefix(locale, localePrefix) {\n  return localePrefix.mode !== 'never' && localePrefix.prefixes?.[locale] ||\n  // We return a prefix even if `mode: 'never'`. It's up to the consumer\n  // to decide to use it or not.\n  getLocaleAsPrefix(locale);\n}\nfunction getLocaleAsPrefix(locale) {\n  return '/' + locale;\n}\nfunction templateToRegex(template) {\n  const regexPattern = template\n  // Replace optional catchall ('[[...slug]]')\n  .replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g, '?(.*)')\n  // Replace catchall ('[...slug]')\n  .replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g, '(.+)')\n  // Replace regular parameter ('[slug]')\n  .replace(/\\[([^\\]]+)\\]/g, '([^/]+)');\n  return new RegExp(`^${regexPattern}$`);\n}\nfunction isOptionalCatchAllSegment(pathname) {\n  return pathname.includes('[[...');\n}\nfunction isCatchAllSegment(pathname) {\n  return pathname.includes('[...');\n}\nfunction isDynamicSegment(pathname) {\n  return pathname.includes('[');\n}\nfunction comparePathnamePairs(a, b) {\n  const pathA = a.split('/');\n  const pathB = b.split('/');\n  const maxLength = Math.max(pathA.length, pathB.length);\n  for (let i = 0; i < maxLength; i++) {\n    const segmentA = pathA[i];\n    const segmentB = pathB[i];\n\n    // If one of the paths ends, prioritize the shorter path\n    if (!segmentA && segmentB) return -1;\n    if (segmentA && !segmentB) return 1;\n    if (!segmentA && !segmentB) continue;\n\n    // Prioritize static segments over dynamic segments\n    if (!isDynamicSegment(segmentA) && isDynamicSegment(segmentB)) return -1;\n    if (isDynamicSegment(segmentA) && !isDynamicSegment(segmentB)) return 1;\n\n    // Prioritize non-catch-all segments over catch-all segments\n    if (!isCatchAllSegment(segmentA) && isCatchAllSegment(segmentB)) return -1;\n    if (isCatchAllSegment(segmentA) && !isCatchAllSegment(segmentB)) return 1;\n\n    // Prioritize non-optional catch-all segments over optional catch-all segments\n    if (!isOptionalCatchAllSegment(segmentA) && isOptionalCatchAllSegment(segmentB)) {\n      return -1;\n    }\n    if (isOptionalCatchAllSegment(segmentA) && !isOptionalCatchAllSegment(segmentB)) {\n      return 1;\n    }\n    if (segmentA === segmentB) continue;\n  }\n\n  // Both pathnames are completely static\n  return 0;\n}\nfunction getSortedPathnames(pathnames) {\n  return pathnames.sort(comparePathnamePairs);\n}\nfunction isPromise(value) {\n  // https://github.com/amannn/next-intl/issues/1711\n  return typeof value.then === 'function';\n}\n\nexport { getLocaleAsPrefix, getLocalePrefix, getLocalizedTemplate, getSortedPathnames, hasPathnamePrefixed, isLocalizableHref, isPromise, matchesPathname, normalizeTrailingSlash, prefixPathname, templateToRegex, unprefixPathname };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,SAAS,eAAe,IAAI;IAC1B,MAAM,WAAW,OAAO,SAAS,WAAW,KAAK,QAAQ,GAAG;IAC5D,OAAO,YAAY,QAAQ,CAAC,SAAS,UAAU,CAAC;AAClD;AACA,SAAS,YAAY,IAAI;IACvB,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,KAAK,IAAI,IAAI,QAAQ,KAAK,QAAQ,IAAI;IAC/C,OAAO;QACL,MAAM,cAAc,YAAY,IAAI,CAAC;QACrC,OAAO,CAAC;IACV;AACF;AACA,SAAS,kBAAkB,IAAI;IAC7B,OAAO,YAAY,SAAS,CAAC,eAAe;AAC9C;AACA,SAAS,iBAAiB,QAAQ,EAAE,MAAM;IACxC,OAAO,SAAS,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,QAAQ,GAAG,OAAO;AAC3D;AACA,SAAS,eAAe,MAAM,EAAE,QAAQ;IACtC,IAAI,gBAAgB;IAEpB,yBAAyB;IACzB,IAAI,cAAc,IAAI,CAAC,WAAW;QAChC,WAAW,SAAS,KAAK,CAAC;IAC5B;IACA,iBAAiB;IACjB,OAAO;AACT;AACA,SAAS,oBAAoB,MAAM,EAAE,QAAQ;IAC3C,OAAO,aAAa,UAAU,SAAS,UAAU,CAAC,GAAG,OAAO,CAAC,CAAC;AAChE;AACA,SAAS;IACP,IAAI;QACF,gEAAgE;QAChE,OAAO,QAAQ,GAAG,CAAC,yBAAyB,KAAK;IACnD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AACA,SAAS,qBAAqB,cAAc,EAAE,MAAM,EAAE,gBAAgB;IACpE,OAAO,OAAO,mBAAmB,WAAW,iBAAiB,cAAc,CAAC,OAAO,IAAI;AACzF;AACA,SAAS,uBAAuB,QAAQ;IACtC,MAAM,gBAAgB;IACtB,IAAI,aAAa,KAAK;QACpB,MAAM,wBAAwB,SAAS,QAAQ,CAAC;QAChD,IAAI,iBAAiB,CAAC,uBAAuB;YAC3C,YAAY;QACd,OAAO,IAAI,CAAC,iBAAiB,uBAAuB;YAClD,WAAW,SAAS,KAAK,CAAC,GAAG,CAAC;QAChC;IACF;IACA,OAAO;AACT;AACA,SAAS,gBAAgB,sCAAsC,GAC/D,QAAQ,EAAE,0BAA0B,GACpC,QAAQ;IACN,MAAM,qBAAqB,uBAAuB;IAClD,MAAM,qBAAqB,uBAAuB;IAClD,MAAM,QAAQ,gBAAgB;IAC9B,OAAO,MAAM,IAAI,CAAC;AACpB;AACA,SAAS,gBAAgB,MAAM,EAAE,YAAY;IAC3C,OAAO,aAAa,IAAI,KAAK,WAAW,aAAa,QAAQ,EAAE,CAAC,OAAO,IACvE,sEAAsE;IACtE,8BAA8B;IAC9B,kBAAkB;AACpB;AACA,SAAS,kBAAkB,MAAM;IAC/B,OAAO,MAAM;AACf;AACA,SAAS,gBAAgB,QAAQ;IAC/B,MAAM,eAAe,QACrB,4CAA4C;KAC3C,OAAO,CAAC,2BAA2B,QACpC,iCAAiC;KAChC,OAAO,CAAC,uBAAuB,OAChC,uCAAuC;KACtC,OAAO,CAAC,iBAAiB;IAC1B,OAAO,IAAI,OAAO,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;AACvC;AACA,SAAS,0BAA0B,QAAQ;IACzC,OAAO,SAAS,QAAQ,CAAC;AAC3B;AACA,SAAS,kBAAkB,QAAQ;IACjC,OAAO,SAAS,QAAQ,CAAC;AAC3B;AACA,SAAS,iBAAiB,QAAQ;IAChC,OAAO,SAAS,QAAQ,CAAC;AAC3B;AACA,SAAS,qBAAqB,CAAC,EAAE,CAAC;IAChC,MAAM,QAAQ,EAAE,KAAK,CAAC;IACtB,MAAM,QAAQ,EAAE,KAAK,CAAC;IACtB,MAAM,YAAY,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,MAAM,MAAM;IACrD,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;QAClC,MAAM,WAAW,KAAK,CAAC,EAAE;QACzB,MAAM,WAAW,KAAK,CAAC,EAAE;QAEzB,wDAAwD;QACxD,IAAI,CAAC,YAAY,UAAU,OAAO,CAAC;QACnC,IAAI,YAAY,CAAC,UAAU,OAAO;QAClC,IAAI,CAAC,YAAY,CAAC,UAAU;QAE5B,mDAAmD;QACnD,IAAI,CAAC,iBAAiB,aAAa,iBAAiB,WAAW,OAAO,CAAC;QACvE,IAAI,iBAAiB,aAAa,CAAC,iBAAiB,WAAW,OAAO;QAEtE,4DAA4D;QAC5D,IAAI,CAAC,kBAAkB,aAAa,kBAAkB,WAAW,OAAO,CAAC;QACzE,IAAI,kBAAkB,aAAa,CAAC,kBAAkB,WAAW,OAAO;QAExE,8EAA8E;QAC9E,IAAI,CAAC,0BAA0B,aAAa,0BAA0B,WAAW;YAC/E,OAAO,CAAC;QACV;QACA,IAAI,0BAA0B,aAAa,CAAC,0BAA0B,WAAW;YAC/E,OAAO;QACT;QACA,IAAI,aAAa,UAAU;IAC7B;IAEA,uCAAuC;IACvC,OAAO;AACT;AACA,SAAS,mBAAmB,SAAS;IACnC,OAAO,UAAU,IAAI,CAAC;AACxB;AACA,SAAS,UAAU,KAAK;IACtB,kDAAkD;IAClD,OAAO,OAAO,MAAM,IAAI,KAAK;AAC/B", "ignoreList": [0]}}, {"offset": {"line": 7469, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/middleware/utils.js"], "sourcesContent": ["import { normalizeTrailingSlash, getSortedPathnames, matchesPathname, prefixPathname, getLocalePrefix, templateToRegex, getLocalizedTemplate } from '../shared/utils.js';\n\nfunction getInternalTemplate(pathnames, pathname, locale) {\n  const sortedPathnames = getSortedPathnames(Object.keys(pathnames));\n\n  // Try to find a localized pathname that matches\n  for (const internalPathname of sortedPathnames) {\n    const localizedPathnamesOrPathname = pathnames[internalPathname];\n    if (typeof localizedPathnamesOrPathname === 'string') {\n      const localizedPathname = localizedPathnamesOrPathname;\n      if (matchesPathname(localizedPathname, pathname)) {\n        return [undefined, internalPathname];\n      }\n    } else {\n      // Prefer the entry with the current locale in case multiple\n      // localized pathnames match the current pathname\n      const sortedEntries = Object.entries(localizedPathnamesOrPathname);\n      const curLocaleIndex = sortedEntries.findIndex(([entryLocale]) => entryLocale === locale);\n      if (curLocaleIndex > 0) {\n        sortedEntries.unshift(sortedEntries.splice(curLocaleIndex, 1)[0]);\n      }\n      for (const [entryLocale] of sortedEntries) {\n        const localizedTemplate = getLocalizedTemplate(pathnames[internalPathname], entryLocale, internalPathname);\n        if (matchesPathname(localizedTemplate, pathname)) {\n          return [entryLocale, internalPathname];\n        }\n      }\n    }\n  }\n\n  // Try to find an internal pathname that matches (this can be the case\n  // if all localized pathnames are different from the internal pathnames)\n  for (const internalPathname of Object.keys(pathnames)) {\n    if (matchesPathname(internalPathname, pathname)) {\n      return [undefined, internalPathname];\n    }\n  }\n\n  // No match\n  return [undefined, undefined];\n}\nfunction formatTemplatePathname(sourcePathname, sourceTemplate, targetTemplate, prefix) {\n  const params = getRouteParams(sourceTemplate, sourcePathname);\n  let targetPathname = '';\n  targetPathname += formatPathnameTemplate(targetTemplate, params);\n\n  // A pathname with an optional catchall like `/categories/[[...slug]]`\n  // should be normalized to `/categories` if the catchall is not present\n  // and no trailing slash is configured\n  targetPathname = normalizeTrailingSlash(targetPathname);\n  return targetPathname;\n}\n\n/**\n * Removes potential prefixes from the pathname.\n */\nfunction getNormalizedPathname(pathname, locales, localePrefix) {\n  // Add trailing slash for consistent handling\n  // both for the root as well as nested paths\n  if (!pathname.endsWith('/')) {\n    pathname += '/';\n  }\n  const localePrefixes = getLocalePrefixes(locales, localePrefix);\n  const regex = new RegExp(`^(${localePrefixes.map(([, prefix]) => prefix.replaceAll('/', '\\\\/')).join('|')})/(.*)`, 'i');\n  const match = pathname.match(regex);\n  let result = match ? '/' + match[2] : pathname;\n  if (result !== '/') {\n    result = normalizeTrailingSlash(result);\n  }\n  return result;\n}\nfunction getLocalePrefixes(locales, localePrefix, sort = true) {\n  const prefixes = locales.map(locale => [locale, getLocalePrefix(locale, localePrefix)]);\n  if (sort) {\n    // More specific ones first\n    prefixes.sort((a, b) => b[1].length - a[1].length);\n  }\n  return prefixes;\n}\nfunction getPathnameMatch(pathname, locales, localePrefix, domain) {\n  const localePrefixes = getLocalePrefixes(locales, localePrefix);\n\n  // Sort to prioritize domain locales\n  if (domain) {\n    localePrefixes.sort(([localeA], [localeB]) => {\n      if (localeA === domain.defaultLocale) return -1;\n      if (localeB === domain.defaultLocale) return 1;\n      const isLocaleAInDomain = domain.locales.includes(localeA);\n      const isLocaleBInDomain = domain.locales.includes(localeB);\n      if (isLocaleAInDomain && !isLocaleBInDomain) return -1;\n      if (!isLocaleAInDomain && isLocaleBInDomain) return 1;\n      return 0;\n    });\n  }\n  for (const [locale, prefix] of localePrefixes) {\n    let exact, matches;\n    if (pathname === prefix || pathname.startsWith(prefix + '/')) {\n      exact = matches = true;\n    } else {\n      const normalizedPathname = pathname.toLowerCase();\n      const normalizedPrefix = prefix.toLowerCase();\n      if (normalizedPathname === normalizedPrefix || normalizedPathname.startsWith(normalizedPrefix + '/')) {\n        exact = false;\n        matches = true;\n      }\n    }\n    if (matches) {\n      return {\n        locale,\n        prefix,\n        matchedPrefix: pathname.slice(0, prefix.length),\n        exact\n      };\n    }\n  }\n}\nfunction getRouteParams(template, pathname) {\n  const normalizedPathname = normalizeTrailingSlash(pathname);\n  const normalizedTemplate = normalizeTrailingSlash(template);\n  const regex = templateToRegex(normalizedTemplate);\n  const match = regex.exec(normalizedPathname);\n  if (!match) return undefined;\n  const params = {};\n  for (let i = 1; i < match.length; i++) {\n    const key = normalizedTemplate.match(/\\[([^\\]]+)\\]/g)?.[i - 1].replace(/[[\\]]/g, '');\n    if (key) params[key] = match[i];\n  }\n  return params;\n}\nfunction formatPathnameTemplate(template, params) {\n  if (!params) return template;\n\n  // Simplify syntax for optional catchall ('[[...slug]]') so\n  // we can replace the value with simple interpolation\n  template = template.replace(/\\[\\[/g, '[').replace(/\\]\\]/g, ']');\n  let result = template;\n  Object.entries(params).forEach(([key, value]) => {\n    result = result.replace(`[${key}]`, value);\n  });\n  return result;\n}\nfunction formatPathname(pathname, prefix, search) {\n  let result = pathname;\n  if (prefix) {\n    result = prefixPathname(prefix, result);\n  }\n  if (search) {\n    result += search;\n  }\n  return result;\n}\nfunction getHost(requestHeaders) {\n  return requestHeaders.get('x-forwarded-host') ?? requestHeaders.get('host') ?? undefined;\n}\nfunction isLocaleSupportedOnDomain(locale, domain) {\n  return domain.defaultLocale === locale || domain.locales.includes(locale);\n}\nfunction getBestMatchingDomain(curHostDomain, locale, domainsConfig) {\n  let domainConfig;\n\n  // Prio 1: Stay on current domain\n  if (curHostDomain && isLocaleSupportedOnDomain(locale, curHostDomain)) {\n    domainConfig = curHostDomain;\n  }\n\n  // Prio 2: Use alternative domain with matching default locale\n  if (!domainConfig) {\n    domainConfig = domainsConfig.find(cur => cur.defaultLocale === locale);\n  }\n\n  // Prio 3: Use alternative domain that supports the locale\n  if (!domainConfig) {\n    domainConfig = domainsConfig.find(cur => cur.locales.includes(locale));\n  }\n  return domainConfig;\n}\nfunction applyBasePath(pathname, basePath) {\n  return normalizeTrailingSlash(basePath + pathname);\n}\nfunction getLocaleAsPrefix(locale) {\n  return `/${locale}`;\n}\nfunction sanitizePathname(pathname) {\n  // Sanitize malicious URIs, e.g.:\n  // '/en/\\\\example.org → /en/%5C%5Cexample.org'\n  // '/en////example.org → /en/example.org'\n  return pathname.replace(/\\\\/g, '%5C').replace(/\\/+/g, '/');\n}\n\nexport { applyBasePath, formatPathname, formatPathnameTemplate, formatTemplatePathname, getBestMatchingDomain, getHost, getInternalTemplate, getLocaleAsPrefix, getLocalePrefixes, getNormalizedPathname, getPathnameMatch, getRouteParams, isLocaleSupportedOnDomain, sanitizePathname };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;;AAEA,SAAS,oBAAoB,SAAS,EAAE,QAAQ,EAAE,MAAM;IACtD,MAAM,kBAAkB,CAAA,GAAA,qLAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,IAAI,CAAC;IAEvD,gDAAgD;IAChD,KAAK,MAAM,oBAAoB,gBAAiB;QAC9C,MAAM,+BAA+B,SAAS,CAAC,iBAAiB;QAChE,IAAI,OAAO,iCAAiC,UAAU;YACpD,MAAM,oBAAoB;YAC1B,IAAI,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE,mBAAmB,WAAW;gBAChD,OAAO;oBAAC;oBAAW;iBAAiB;YACtC;QACF,OAAO;YACL,4DAA4D;YAC5D,iDAAiD;YACjD,MAAM,gBAAgB,OAAO,OAAO,CAAC;YACrC,MAAM,iBAAiB,cAAc,SAAS,CAAC,CAAC,CAAC,YAAY,GAAK,gBAAgB;YAClF,IAAI,iBAAiB,GAAG;gBACtB,cAAc,OAAO,CAAC,cAAc,MAAM,CAAC,gBAAgB,EAAE,CAAC,EAAE;YAClE;YACA,KAAK,MAAM,CAAC,YAAY,IAAI,cAAe;gBACzC,MAAM,oBAAoB,CAAA,GAAA,qLAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS,CAAC,iBAAiB,EAAE,aAAa;gBACzF,IAAI,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE,mBAAmB,WAAW;oBAChD,OAAO;wBAAC;wBAAa;qBAAiB;gBACxC;YACF;QACF;IACF;IAEA,sEAAsE;IACtE,wEAAwE;IACxE,KAAK,MAAM,oBAAoB,OAAO,IAAI,CAAC,WAAY;QACrD,IAAI,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE,kBAAkB,WAAW;YAC/C,OAAO;gBAAC;gBAAW;aAAiB;QACtC;IACF;IAEA,WAAW;IACX,OAAO;QAAC;QAAW;KAAU;AAC/B;AACA,SAAS,uBAAuB,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM;IACpF,MAAM,SAAS,eAAe,gBAAgB;IAC9C,IAAI,iBAAiB;IACrB,kBAAkB,uBAAuB,gBAAgB;IAEzD,sEAAsE;IACtE,uEAAuE;IACvE,sCAAsC;IACtC,iBAAiB,CAAA,GAAA,qLAAA,CAAA,yBAAsB,AAAD,EAAE;IACxC,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,sBAAsB,QAAQ,EAAE,OAAO,EAAE,YAAY;IAC5D,6CAA6C;IAC7C,4CAA4C;IAC5C,IAAI,CAAC,SAAS,QAAQ,CAAC,MAAM;QAC3B,YAAY;IACd;IACA,MAAM,iBAAiB,kBAAkB,SAAS;IAClD,MAAM,QAAQ,IAAI,OAAO,CAAC,EAAE,EAAE,eAAe,GAAG,CAAC,CAAC,GAAG,OAAO,GAAK,OAAO,UAAU,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,MAAM,CAAC,EAAE;IACnH,MAAM,QAAQ,SAAS,KAAK,CAAC;IAC7B,IAAI,SAAS,QAAQ,MAAM,KAAK,CAAC,EAAE,GAAG;IACtC,IAAI,WAAW,KAAK;QAClB,SAAS,CAAA,GAAA,qLAAA,CAAA,yBAAsB,AAAD,EAAE;IAClC;IACA,OAAO;AACT;AACA,SAAS,kBAAkB,OAAO,EAAE,YAAY,EAAE,OAAO,IAAI;IAC3D,MAAM,WAAW,QAAQ,GAAG,CAAC,CAAA,SAAU;YAAC;YAAQ,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ;SAAc;IACtF,IAAI,MAAM;QACR,2BAA2B;QAC3B,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,MAAM;IACnD;IACA,OAAO;AACT;AACA,SAAS,iBAAiB,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM;IAC/D,MAAM,iBAAiB,kBAAkB,SAAS;IAElD,oCAAoC;IACpC,IAAI,QAAQ;QACV,eAAe,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ;YACvC,IAAI,YAAY,OAAO,aAAa,EAAE,OAAO,CAAC;YAC9C,IAAI,YAAY,OAAO,aAAa,EAAE,OAAO;YAC7C,MAAM,oBAAoB,OAAO,OAAO,CAAC,QAAQ,CAAC;YAClD,MAAM,oBAAoB,OAAO,OAAO,CAAC,QAAQ,CAAC;YAClD,IAAI,qBAAqB,CAAC,mBAAmB,OAAO,CAAC;YACrD,IAAI,CAAC,qBAAqB,mBAAmB,OAAO;YACpD,OAAO;QACT;IACF;IACA,KAAK,MAAM,CAAC,QAAQ,OAAO,IAAI,eAAgB;QAC7C,IAAI,OAAO;QACX,IAAI,aAAa,UAAU,SAAS,UAAU,CAAC,SAAS,MAAM;YAC5D,QAAQ,UAAU;QACpB,OAAO;YACL,MAAM,qBAAqB,SAAS,WAAW;YAC/C,MAAM,mBAAmB,OAAO,WAAW;YAC3C,IAAI,uBAAuB,oBAAoB,mBAAmB,UAAU,CAAC,mBAAmB,MAAM;gBACpG,QAAQ;gBACR,UAAU;YACZ;QACF;QACA,IAAI,SAAS;YACX,OAAO;gBACL;gBACA;gBACA,eAAe,SAAS,KAAK,CAAC,GAAG,OAAO,MAAM;gBAC9C;YACF;QACF;IACF;AACF;AACA,SAAS,eAAe,QAAQ,EAAE,QAAQ;IACxC,MAAM,qBAAqB,CAAA,GAAA,qLAAA,CAAA,yBAAsB,AAAD,EAAE;IAClD,MAAM,qBAAqB,CAAA,GAAA,qLAAA,CAAA,yBAAsB,AAAD,EAAE;IAClD,MAAM,QAAQ,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE;IAC9B,MAAM,QAAQ,MAAM,IAAI,CAAC;IACzB,IAAI,CAAC,OAAO,OAAO;IACnB,MAAM,SAAS,CAAC;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,MAAM,MAAM,mBAAmB,KAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC,QAAQ,UAAU;QACjF,IAAI,KAAK,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;IACjC;IACA,OAAO;AACT;AACA,SAAS,uBAAuB,QAAQ,EAAE,MAAM;IAC9C,IAAI,CAAC,QAAQ,OAAO;IAEpB,2DAA2D;IAC3D,qDAAqD;IACrD,WAAW,SAAS,OAAO,CAAC,SAAS,KAAK,OAAO,CAAC,SAAS;IAC3D,IAAI,SAAS;IACb,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC1C,SAAS,OAAO,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE;IACtC;IACA,OAAO;AACT;AACA,SAAS,eAAe,QAAQ,EAAE,MAAM,EAAE,MAAM;IAC9C,IAAI,SAAS;IACb,IAAI,QAAQ;QACV,SAAS,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ;IAClC;IACA,IAAI,QAAQ;QACV,UAAU;IACZ;IACA,OAAO;AACT;AACA,SAAS,QAAQ,cAAc;IAC7B,OAAO,eAAe,GAAG,CAAC,uBAAuB,eAAe,GAAG,CAAC,WAAW;AACjF;AACA,SAAS,0BAA0B,MAAM,EAAE,MAAM;IAC/C,OAAO,OAAO,aAAa,KAAK,UAAU,OAAO,OAAO,CAAC,QAAQ,CAAC;AACpE;AACA,SAAS,sBAAsB,aAAa,EAAE,MAAM,EAAE,aAAa;IACjE,IAAI;IAEJ,iCAAiC;IACjC,IAAI,iBAAiB,0BAA0B,QAAQ,gBAAgB;QACrE,eAAe;IACjB;IAEA,8DAA8D;IAC9D,IAAI,CAAC,cAAc;QACjB,eAAe,cAAc,IAAI,CAAC,CAAA,MAAO,IAAI,aAAa,KAAK;IACjE;IAEA,0DAA0D;IAC1D,IAAI,CAAC,cAAc;QACjB,eAAe,cAAc,IAAI,CAAC,CAAA,MAAO,IAAI,OAAO,CAAC,QAAQ,CAAC;IAChE;IACA,OAAO;AACT;AACA,SAAS,cAAc,QAAQ,EAAE,QAAQ;IACvC,OAAO,CAAA,GAAA,qLAAA,CAAA,yBAAsB,AAAD,EAAE,WAAW;AAC3C;AACA,SAAS,kBAAkB,MAAM;IAC/B,OAAO,CAAC,CAAC,EAAE,QAAQ;AACrB;AACA,SAAS,iBAAiB,QAAQ;IAChC,iCAAiC;IACjC,8CAA8C;IAC9C,yCAAyC;IACzC,OAAO,SAAS,OAAO,CAAC,OAAO,OAAO,OAAO,CAAC,QAAQ;AACxD", "ignoreList": [0]}}, {"offset": {"line": 7684, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/middleware/getAlternateLinksHeaderValue.js"], "sourcesContent": ["import { normalizeTrailingSlash } from '../shared/utils.js';\nimport { getHost, getNormalizedPathname, getLocalePrefixes, isLocaleSupportedOnDomain, applyBasePath, formatTemplatePathname } from './utils.js';\n\n/**\n * See https://developers.google.com/search/docs/specialty/international/localized-versions\n */\nfunction getAlternateLinksHeaderValue({\n  internalTemplateName,\n  localizedPathnames,\n  request,\n  resolvedLocale,\n  routing\n}) {\n  const normalizedUrl = request.nextUrl.clone();\n  const host = getHost(request.headers);\n  if (host) {\n    normalizedUrl.port = '';\n    normalizedUrl.host = host;\n  }\n  normalizedUrl.protocol = request.headers.get('x-forwarded-proto') ?? normalizedUrl.protocol;\n  normalizedUrl.pathname = getNormalizedPathname(normalizedUrl.pathname, routing.locales, routing.localePrefix);\n  function getAlternateEntry(url, locale) {\n    url.pathname = normalizeTrailingSlash(url.pathname);\n    if (request.nextUrl.basePath) {\n      url = new URL(url);\n      url.pathname = applyBasePath(url.pathname, request.nextUrl.basePath);\n    }\n    return `<${url.toString()}>; rel=\"alternate\"; hreflang=\"${locale}\"`;\n  }\n  function getLocalizedPathname(pathname, locale) {\n    if (localizedPathnames && typeof localizedPathnames === 'object') {\n      const sourceTemplate = localizedPathnames[resolvedLocale];\n      return formatTemplatePathname(pathname, sourceTemplate ?? internalTemplateName, localizedPathnames[locale] ?? internalTemplateName);\n    } else {\n      return pathname;\n    }\n  }\n  const links = getLocalePrefixes(routing.locales, routing.localePrefix, false).flatMap(([locale, prefix]) => {\n    function prefixPathname(pathname) {\n      if (pathname === '/') {\n        return prefix;\n      } else {\n        return prefix + pathname;\n      }\n    }\n    let url;\n    if (routing.domains) {\n      const domainConfigs = routing.domains.filter(cur => isLocaleSupportedOnDomain(locale, cur));\n      return domainConfigs.map(domainConfig => {\n        url = new URL(normalizedUrl);\n        url.port = '';\n        url.host = domainConfig.domain;\n\n        // Important: Use `normalizedUrl` here, as `url` potentially uses\n        // a `basePath` that automatically gets applied to the pathname\n        url.pathname = getLocalizedPathname(normalizedUrl.pathname, locale);\n        if (locale !== domainConfig.defaultLocale || routing.localePrefix.mode === 'always') {\n          url.pathname = prefixPathname(url.pathname);\n        }\n        return getAlternateEntry(url, locale);\n      });\n    } else {\n      let pathname;\n      if (localizedPathnames && typeof localizedPathnames === 'object') {\n        pathname = getLocalizedPathname(normalizedUrl.pathname, locale);\n      } else {\n        pathname = normalizedUrl.pathname;\n      }\n      if (locale !== routing.defaultLocale || routing.localePrefix.mode === 'always') {\n        pathname = prefixPathname(pathname);\n      }\n      url = new URL(pathname, normalizedUrl);\n    }\n    return getAlternateEntry(url, locale);\n  });\n\n  // Add x-default entry\n  const shouldAddXDefault =\n  // For domain-based routing there is no reasonable x-default\n  !routing.domains || routing.domains.length === 0;\n  if (shouldAddXDefault) {\n    const localizedPathname = getLocalizedPathname(normalizedUrl.pathname, routing.defaultLocale);\n    if (localizedPathname) {\n      const url = new URL(localizedPathname, normalizedUrl);\n      links.push(getAlternateEntry(url, 'x-default'));\n    }\n  }\n  return links.join(', ');\n}\n\nexport { getAlternateLinksHeaderValue as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA;;CAEC,GACD,SAAS,6BAA6B,EACpC,oBAAoB,EACpB,kBAAkB,EAClB,OAAO,EACP,cAAc,EACd,OAAO,EACR;IACC,MAAM,gBAAgB,QAAQ,OAAO,CAAC,KAAK;IAC3C,MAAM,OAAO,CAAA,GAAA,yLAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,OAAO;IACpC,IAAI,MAAM;QACR,cAAc,IAAI,GAAG;QACrB,cAAc,IAAI,GAAG;IACvB;IACA,cAAc,QAAQ,GAAG,QAAQ,OAAO,CAAC,GAAG,CAAC,wBAAwB,cAAc,QAAQ;IAC3F,cAAc,QAAQ,GAAG,CAAA,GAAA,yLAAA,CAAA,wBAAqB,AAAD,EAAE,cAAc,QAAQ,EAAE,QAAQ,OAAO,EAAE,QAAQ,YAAY;IAC5G,SAAS,kBAAkB,GAAG,EAAE,MAAM;QACpC,IAAI,QAAQ,GAAG,CAAA,GAAA,qLAAA,CAAA,yBAAsB,AAAD,EAAE,IAAI,QAAQ;QAClD,IAAI,QAAQ,OAAO,CAAC,QAAQ,EAAE;YAC5B,MAAM,IAAI,IAAI;YACd,IAAI,QAAQ,GAAG,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,QAAQ,EAAE,QAAQ,OAAO,CAAC,QAAQ;QACrE;QACA,OAAO,CAAC,CAAC,EAAE,IAAI,QAAQ,GAAG,8BAA8B,EAAE,OAAO,CAAC,CAAC;IACrE;IACA,SAAS,qBAAqB,QAAQ,EAAE,MAAM;QAC5C,IAAI,sBAAsB,OAAO,uBAAuB,UAAU;YAChE,MAAM,iBAAiB,kBAAkB,CAAC,eAAe;YACzD,OAAO,CAAA,GAAA,yLAAA,CAAA,yBAAsB,AAAD,EAAE,UAAU,kBAAkB,sBAAsB,kBAAkB,CAAC,OAAO,IAAI;QAChH,OAAO;YACL,OAAO;QACT;IACF;IACA,MAAM,QAAQ,CAAA,GAAA,yLAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,OAAO,EAAE,QAAQ,YAAY,EAAE,OAAO,OAAO,CAAC,CAAC,CAAC,QAAQ,OAAO;QACrG,SAAS,eAAe,QAAQ;YAC9B,IAAI,aAAa,KAAK;gBACpB,OAAO;YACT,OAAO;gBACL,OAAO,SAAS;YAClB;QACF;QACA,IAAI;QACJ,IAAI,QAAQ,OAAO,EAAE;YACnB,MAAM,gBAAgB,QAAQ,OAAO,CAAC,MAAM,CAAC,CAAA,MAAO,CAAA,GAAA,yLAAA,CAAA,4BAAyB,AAAD,EAAE,QAAQ;YACtF,OAAO,cAAc,GAAG,CAAC,CAAA;gBACvB,MAAM,IAAI,IAAI;gBACd,IAAI,IAAI,GAAG;gBACX,IAAI,IAAI,GAAG,aAAa,MAAM;gBAE9B,iEAAiE;gBACjE,+DAA+D;gBAC/D,IAAI,QAAQ,GAAG,qBAAqB,cAAc,QAAQ,EAAE;gBAC5D,IAAI,WAAW,aAAa,aAAa,IAAI,QAAQ,YAAY,CAAC,IAAI,KAAK,UAAU;oBACnF,IAAI,QAAQ,GAAG,eAAe,IAAI,QAAQ;gBAC5C;gBACA,OAAO,kBAAkB,KAAK;YAChC;QACF,OAAO;YACL,IAAI;YACJ,IAAI,sBAAsB,OAAO,uBAAuB,UAAU;gBAChE,WAAW,qBAAqB,cAAc,QAAQ,EAAE;YAC1D,OAAO;gBACL,WAAW,cAAc,QAAQ;YACnC;YACA,IAAI,WAAW,QAAQ,aAAa,IAAI,QAAQ,YAAY,CAAC,IAAI,KAAK,UAAU;gBAC9E,WAAW,eAAe;YAC5B;YACA,MAAM,IAAI,IAAI,UAAU;QAC1B;QACA,OAAO,kBAAkB,KAAK;IAChC;IAEA,sBAAsB;IACtB,MAAM,oBACN,4DAA4D;IAC5D,CAAC,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,KAAK;IAC/C,IAAI,mBAAmB;QACrB,MAAM,oBAAoB,qBAAqB,cAAc,QAAQ,EAAE,QAAQ,aAAa;QAC5F,IAAI,mBAAmB;YACrB,MAAM,MAAM,IAAI,IAAI,mBAAmB;YACvC,MAAM,IAAI,CAAC,kBAAkB,KAAK;QACpC;IACF;IACA,OAAO,MAAM,IAAI,CAAC;AACpB", "ignoreList": [0]}}, {"offset": {"line": 7774, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/middleware/resolveLocale.js"], "sourcesContent": ["import { match } from '@formatjs/intl-localematcher';\nimport <PERSON>egotiator from 'negotiator';\nimport { getPathnameMatch, isLocaleSupportedOnDomain, getHost } from './utils.js';\n\nfunction findDomainFromHost(requestHeaders, domains) {\n  const host = getHost(requestHeaders);\n  if (host) {\n    return domains.find(cur => cur.domain === host);\n  }\n  return undefined;\n}\nfunction orderLocales(locales) {\n  // Workaround for https://github.com/formatjs/formatjs/issues/4469\n  return locales.slice().sort((a, b) => b.length - a.length);\n}\nfunction getAcceptLanguageLocale(requestHeaders, locales, defaultLocale) {\n  let locale;\n  const languages = new Negotiator({\n    headers: {\n      'accept-language': requestHeaders.get('accept-language') || undefined\n    }\n  }).languages();\n  try {\n    const orderedLocales = orderLocales(locales);\n    locale = match(languages, orderedLocales, defaultLocale);\n  } catch {\n    // Invalid language\n  }\n  return locale;\n}\nfunction getLocaleFromCookie(routing, requestCookies) {\n  if (routing.localeCookie && requestCookies.has(routing.localeCookie.name)) {\n    const value = requestCookies.get(routing.localeCookie.name)?.value;\n    if (value && routing.locales.includes(value)) {\n      return value;\n    }\n  }\n}\nfunction resolveLocaleFromPrefix(routing, requestHeaders, requestCookies, pathname) {\n  let locale;\n\n  // Prio 1: Use route prefix\n  if (pathname) {\n    locale = getPathnameMatch(pathname, routing.locales, routing.localePrefix)?.locale;\n  }\n\n  // Prio 2: Use existing cookie\n  if (!locale && routing.localeDetection) {\n    locale = getLocaleFromCookie(routing, requestCookies);\n  }\n\n  // Prio 3: Use the `accept-language` header\n  if (!locale && routing.localeDetection) {\n    locale = getAcceptLanguageLocale(requestHeaders, routing.locales, routing.defaultLocale);\n  }\n\n  // Prio 4: Use default locale\n  if (!locale) {\n    locale = routing.defaultLocale;\n  }\n  return locale;\n}\nfunction resolveLocaleFromDomain(routing, requestHeaders, requestCookies, pathname) {\n  const domains = routing.domains;\n  const domain = findDomainFromHost(requestHeaders, domains);\n  if (!domain) {\n    return {\n      locale: resolveLocaleFromPrefix(routing, requestHeaders, requestCookies, pathname)\n    };\n  }\n  let locale;\n\n  // Prio 1: Use route prefix\n  if (pathname) {\n    const prefixLocale = getPathnameMatch(pathname, routing.locales, routing.localePrefix, domain)?.locale;\n    if (prefixLocale) {\n      if (isLocaleSupportedOnDomain(prefixLocale, domain)) {\n        locale = prefixLocale;\n      } else {\n        // Causes a redirect to a domain that supports the locale\n        return {\n          locale: prefixLocale,\n          domain\n        };\n      }\n    }\n  }\n\n  // Prio 2: Use existing cookie\n  if (!locale && routing.localeDetection) {\n    const cookieLocale = getLocaleFromCookie(routing, requestCookies);\n    if (cookieLocale) {\n      if (isLocaleSupportedOnDomain(cookieLocale, domain)) {\n        locale = cookieLocale;\n      }\n    }\n  }\n\n  // Prio 3: Use the `accept-language` header\n  if (!locale && routing.localeDetection) {\n    const headerLocale = getAcceptLanguageLocale(requestHeaders, domain.locales, domain.defaultLocale);\n    if (headerLocale) {\n      locale = headerLocale;\n    }\n  }\n\n  // Prio 4: Use default locale\n  if (!locale) {\n    locale = domain.defaultLocale;\n  }\n  return {\n    locale,\n    domain\n  };\n}\nfunction resolveLocale(routing, requestHeaders, requestCookies, pathname) {\n  if (routing.domains) {\n    return resolveLocaleFromDomain(routing, requestHeaders, requestCookies, pathname);\n  } else {\n    return {\n      locale: resolveLocaleFromPrefix(routing, requestHeaders, requestCookies, pathname)\n    };\n  }\n}\n\nexport { resolveLocale as default, getAcceptLanguageLocale };\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;;;;AAEA,SAAS,mBAAmB,cAAc,EAAE,OAAO;IACjD,MAAM,OAAO,CAAA,GAAA,yLAAA,CAAA,UAAO,AAAD,EAAE;IACrB,IAAI,MAAM;QACR,OAAO,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK;IAC5C;IACA,OAAO;AACT;AACA,SAAS,aAAa,OAAO;IAC3B,kEAAkE;IAClE,OAAO,QAAQ,KAAK,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM;AAC3D;AACA,SAAS,wBAAwB,cAAc,EAAE,OAAO,EAAE,aAAa;IACrE,IAAI;IACJ,MAAM,YAAY,IAAI,2IAAA,CAAA,UAAU,CAAC;QAC/B,SAAS;YACP,mBAAmB,eAAe,GAAG,CAAC,sBAAsB;QAC9D;IACF,GAAG,SAAS;IACZ,IAAI;QACF,MAAM,iBAAiB,aAAa;QACpC,SAAS,CAAA,GAAA,2LAAA,CAAA,QAAK,AAAD,EAAE,WAAW,gBAAgB;IAC5C,EAAE,OAAM;IACN,mBAAmB;IACrB;IACA,OAAO;AACT;AACA,SAAS,oBAAoB,OAAO,EAAE,cAAc;IAClD,IAAI,QAAQ,YAAY,IAAI,eAAe,GAAG,CAAC,QAAQ,YAAY,CAAC,IAAI,GAAG;QACzE,MAAM,QAAQ,eAAe,GAAG,CAAC,QAAQ,YAAY,CAAC,IAAI,GAAG;QAC7D,IAAI,SAAS,QAAQ,OAAO,CAAC,QAAQ,CAAC,QAAQ;YAC5C,OAAO;QACT;IACF;AACF;AACA,SAAS,wBAAwB,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ;IAChF,IAAI;IAEJ,2BAA2B;IAC3B,IAAI,UAAU;QACZ,SAAS,CAAA,GAAA,yLAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,QAAQ,OAAO,EAAE,QAAQ,YAAY,GAAG;IAC9E;IAEA,8BAA8B;IAC9B,IAAI,CAAC,UAAU,QAAQ,eAAe,EAAE;QACtC,SAAS,oBAAoB,SAAS;IACxC;IAEA,2CAA2C;IAC3C,IAAI,CAAC,UAAU,QAAQ,eAAe,EAAE;QACtC,SAAS,wBAAwB,gBAAgB,QAAQ,OAAO,EAAE,QAAQ,aAAa;IACzF;IAEA,6BAA6B;IAC7B,IAAI,CAAC,QAAQ;QACX,SAAS,QAAQ,aAAa;IAChC;IACA,OAAO;AACT;AACA,SAAS,wBAAwB,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ;IAChF,MAAM,UAAU,QAAQ,OAAO;IAC/B,MAAM,SAAS,mBAAmB,gBAAgB;IAClD,IAAI,CAAC,QAAQ;QACX,OAAO;YACL,QAAQ,wBAAwB,SAAS,gBAAgB,gBAAgB;QAC3E;IACF;IACA,IAAI;IAEJ,2BAA2B;IAC3B,IAAI,UAAU;QACZ,MAAM,eAAe,CAAA,GAAA,yLAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,QAAQ,OAAO,EAAE,QAAQ,YAAY,EAAE,SAAS;QAChG,IAAI,cAAc;YAChB,IAAI,CAAA,GAAA,yLAAA,CAAA,4BAAyB,AAAD,EAAE,cAAc,SAAS;gBACnD,SAAS;YACX,OAAO;gBACL,yDAAyD;gBACzD,OAAO;oBACL,QAAQ;oBACR;gBACF;YACF;QACF;IACF;IAEA,8BAA8B;IAC9B,IAAI,CAAC,UAAU,QAAQ,eAAe,EAAE;QACtC,MAAM,eAAe,oBAAoB,SAAS;QAClD,IAAI,cAAc;YAChB,IAAI,CAAA,GAAA,yLAAA,CAAA,4BAAyB,AAAD,EAAE,cAAc,SAAS;gBACnD,SAAS;YACX;QACF;IACF;IAEA,2CAA2C;IAC3C,IAAI,CAAC,UAAU,QAAQ,eAAe,EAAE;QACtC,MAAM,eAAe,wBAAwB,gBAAgB,OAAO,OAAO,EAAE,OAAO,aAAa;QACjG,IAAI,cAAc;YAChB,SAAS;QACX;IACF;IAEA,6BAA6B;IAC7B,IAAI,CAAC,QAAQ;QACX,SAAS,OAAO,aAAa;IAC/B;IACA,OAAO;QACL;QACA;IACF;AACF;AACA,SAAS,cAAc,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ;IACtE,IAAI,QAAQ,OAAO,EAAE;QACnB,OAAO,wBAAwB,SAAS,gBAAgB,gBAAgB;IAC1E,OAAO;QACL,OAAO;YACL,QAAQ,wBAAwB,SAAS,gBAAgB,gBAAgB;QAC3E;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 7904, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/middleware/syncCookie.js"], "sourcesContent": ["import { getAcceptLanguageLocale } from './resolveLocale.js';\n\nfunction syncCookie(request, response, locale, routing, domain) {\n  if (!routing.localeCookie) return;\n  const {\n    name,\n    ...rest\n  } = routing.localeCookie;\n  const acceptLanguageLocale = getAcceptLanguageLocale(request.headers, domain?.locales || routing.locales, routing.defaultLocale);\n  const hasLocaleCookie = request.cookies.has(name);\n  const hasOutdatedCookie = hasLocaleCookie && request.cookies.get(name)?.value !== locale;\n  if (hasLocaleCookie ? hasOutdatedCookie : acceptLanguageLocale !== locale) {\n    response.cookies.set(name, locale, {\n      path: request.nextUrl.basePath || undefined,\n      ...rest\n    });\n  }\n}\n\nexport { syncCookie as default };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,WAAW,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;IAC5D,IAAI,CAAC,QAAQ,YAAY,EAAE;IAC3B,MAAM,EACJ,IAAI,EACJ,GAAG,MACJ,GAAG,QAAQ,YAAY;IACxB,MAAM,uBAAuB,CAAA,GAAA,iMAAA,CAAA,0BAAuB,AAAD,EAAE,QAAQ,OAAO,EAAE,QAAQ,WAAW,QAAQ,OAAO,EAAE,QAAQ,aAAa;IAC/H,MAAM,kBAAkB,QAAQ,OAAO,CAAC,GAAG,CAAC;IAC5C,MAAM,oBAAoB,mBAAmB,QAAQ,OAAO,CAAC,GAAG,CAAC,OAAO,UAAU;IAClF,IAAI,kBAAkB,oBAAoB,yBAAyB,QAAQ;QACzE,SAAS,OAAO,CAAC,GAAG,CAAC,MAAM,QAAQ;YACjC,MAAM,QAAQ,OAAO,CAAC,QAAQ,IAAI;YAClC,GAAG,IAAI;QACT;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 7929, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/middleware/middleware.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { receiveRoutingConfig } from '../routing/config.js';\nimport { HEADER_LOCALE_NAME } from '../shared/constants.js';\nimport { matchesPathname, normalizeTrailingSlash, getLocalePrefix, getLocalizedTemplate } from '../shared/utils.js';\nimport getAlternateLinksHeaderValue from './getAlternateLinksHeaderValue.js';\nimport resolveLocale from './resolveLocale.js';\nimport syncCookie from './syncCookie.js';\nimport { sanitizePathname, isLocaleSupportedOnDomain, getNormalizedPathname, getPathnameMatch, getInternalTemplate, formatTemplatePathname, formatPathname, getBestMatchingDomain, applyBasePath, getLocaleAsPrefix } from './utils.js';\n\nfunction createMiddleware(routing) {\n  const resolvedRouting = receiveRoutingConfig(routing);\n  return function middleware(request) {\n    let unsafeExternalPathname;\n    try {\n      // Resolve potential foreign symbols (e.g. /ja/%E7%B4%84 → /ja/約))\n      unsafeExternalPathname = decodeURI(request.nextUrl.pathname);\n    } catch {\n      // In case an invalid pathname is encountered, forward\n      // it to Next.js which in turn responds with a 400\n      return NextResponse.next();\n    }\n\n    // Sanitize malicious URIs to prevent open redirect attacks due to\n    // decodeURI doesn't escape encoded backslashes ('%5C' & '%5c')\n    const externalPathname = sanitizePathname(unsafeExternalPathname);\n    const {\n      domain,\n      locale\n    } = resolveLocale(resolvedRouting, request.headers, request.cookies, externalPathname);\n    const hasMatchedDefaultLocale = domain ? domain.defaultLocale === locale : locale === resolvedRouting.defaultLocale;\n    const domainsConfig = resolvedRouting.domains?.filter(curDomain => isLocaleSupportedOnDomain(locale, curDomain)) || [];\n    const hasUnknownHost = resolvedRouting.domains != null && !domain;\n    function rewrite(url) {\n      const urlObj = new URL(url, request.url);\n      if (request.nextUrl.basePath) {\n        urlObj.pathname = applyBasePath(urlObj.pathname, request.nextUrl.basePath);\n      }\n      const headers = new Headers(request.headers);\n      headers.set(HEADER_LOCALE_NAME, locale);\n      return NextResponse.rewrite(urlObj, {\n        request: {\n          headers\n        }\n      });\n    }\n    function redirect(url, redirectDomain) {\n      const urlObj = new URL(url, request.url);\n      urlObj.pathname = normalizeTrailingSlash(urlObj.pathname);\n      if (domainsConfig.length > 0 && !redirectDomain && domain) {\n        const bestMatchingDomain = getBestMatchingDomain(domain, locale, domainsConfig);\n        if (bestMatchingDomain) {\n          redirectDomain = bestMatchingDomain.domain;\n          if (bestMatchingDomain.defaultLocale === locale && resolvedRouting.localePrefix.mode === 'as-needed') {\n            urlObj.pathname = getNormalizedPathname(urlObj.pathname, resolvedRouting.locales, resolvedRouting.localePrefix);\n          }\n        }\n      }\n      if (redirectDomain) {\n        urlObj.host = redirectDomain;\n        if (request.headers.get('x-forwarded-host')) {\n          urlObj.protocol = request.headers.get('x-forwarded-proto') ?? request.nextUrl.protocol;\n          const redirectDomainPort = redirectDomain.split(':')[1];\n          urlObj.port = redirectDomainPort ?? request.headers.get('x-forwarded-port') ?? '';\n        }\n      }\n      if (request.nextUrl.basePath) {\n        urlObj.pathname = applyBasePath(urlObj.pathname, request.nextUrl.basePath);\n      }\n      hasRedirected = true;\n      return NextResponse.redirect(urlObj.toString());\n    }\n    const unprefixedExternalPathname = getNormalizedPathname(externalPathname, resolvedRouting.locales, resolvedRouting.localePrefix);\n    const pathnameMatch = getPathnameMatch(externalPathname, resolvedRouting.locales, resolvedRouting.localePrefix, domain);\n    const hasLocalePrefix = pathnameMatch != null;\n    const isUnprefixedRouting = resolvedRouting.localePrefix.mode === 'never' || hasMatchedDefaultLocale && resolvedRouting.localePrefix.mode === 'as-needed';\n    let response;\n    let internalTemplateName;\n    let hasRedirected;\n    let unprefixedInternalPathname = unprefixedExternalPathname;\n    const pathnames = resolvedRouting.pathnames;\n    if (pathnames) {\n      let resolvedTemplateLocale;\n      [resolvedTemplateLocale, internalTemplateName] = getInternalTemplate(pathnames, unprefixedExternalPathname, locale);\n      if (internalTemplateName) {\n        const pathnameConfig = pathnames[internalTemplateName];\n        const localeTemplate = getLocalizedTemplate(pathnameConfig, locale, internalTemplateName);\n        if (matchesPathname(localeTemplate, unprefixedExternalPathname)) {\n          unprefixedInternalPathname = formatTemplatePathname(unprefixedExternalPathname, localeTemplate, internalTemplateName);\n        } else {\n          let sourceTemplate;\n          if (resolvedTemplateLocale) {\n            // A localized pathname from another locale has matched\n            sourceTemplate = getLocalizedTemplate(pathnameConfig, resolvedTemplateLocale, internalTemplateName);\n          } else {\n            // An internal pathname has matched that\n            // doesn't have a localized pathname\n            sourceTemplate = internalTemplateName;\n          }\n          const localePrefix = isUnprefixedRouting ? undefined : getLocalePrefix(locale, resolvedRouting.localePrefix);\n          const template = formatTemplatePathname(unprefixedExternalPathname, sourceTemplate, localeTemplate);\n          response = redirect(formatPathname(template, localePrefix, request.nextUrl.search));\n        }\n      }\n    }\n    if (!response) {\n      if (unprefixedInternalPathname === '/' && !hasLocalePrefix) {\n        if (isUnprefixedRouting) {\n          response = rewrite(formatPathname(unprefixedInternalPathname, getLocaleAsPrefix(locale), request.nextUrl.search));\n        } else {\n          response = redirect(formatPathname(unprefixedExternalPathname, getLocalePrefix(locale, resolvedRouting.localePrefix), request.nextUrl.search));\n        }\n      } else {\n        const internalHref = formatPathname(unprefixedInternalPathname, getLocaleAsPrefix(locale), request.nextUrl.search);\n        if (hasLocalePrefix) {\n          const externalHref = formatPathname(unprefixedExternalPathname, pathnameMatch.prefix, request.nextUrl.search);\n          if (resolvedRouting.localePrefix.mode === 'never') {\n            response = redirect(formatPathname(unprefixedExternalPathname, undefined, request.nextUrl.search));\n          } else if (pathnameMatch.exact) {\n            if (hasMatchedDefaultLocale && isUnprefixedRouting) {\n              response = redirect(formatPathname(unprefixedExternalPathname, undefined, request.nextUrl.search));\n            } else {\n              if (resolvedRouting.domains) {\n                const pathDomain = getBestMatchingDomain(domain, pathnameMatch.locale, domainsConfig);\n                if (domain?.domain !== pathDomain?.domain && !hasUnknownHost) {\n                  response = redirect(externalHref, pathDomain?.domain);\n                } else {\n                  response = rewrite(internalHref);\n                }\n              } else {\n                response = rewrite(internalHref);\n              }\n            }\n          } else {\n            response = redirect(externalHref);\n          }\n        } else {\n          if (isUnprefixedRouting) {\n            response = rewrite(internalHref);\n          } else {\n            response = redirect(formatPathname(unprefixedExternalPathname, getLocalePrefix(locale, resolvedRouting.localePrefix), request.nextUrl.search));\n          }\n        }\n      }\n    }\n    syncCookie(request, response, locale, resolvedRouting, domain);\n    if (!hasRedirected && resolvedRouting.localePrefix.mode !== 'never' && resolvedRouting.alternateLinks && resolvedRouting.locales.length > 1) {\n      response.headers.set('Link', getAlternateLinksHeaderValue({\n        routing: resolvedRouting,\n        internalTemplateName,\n        localizedPathnames: internalTemplateName != null && pathnames ? pathnames[internalTemplateName] : undefined,\n        request,\n        resolvedLocale: locale\n      }));\n    }\n    return response;\n  };\n}\n\nexport { createMiddleware as default };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,SAAS,iBAAiB,OAAO;IAC/B,MAAM,kBAAkB,CAAA,GAAA,uLAAA,CAAA,uBAAoB,AAAD,EAAE;IAC7C,OAAO,SAAS,WAAW,OAAO;QAChC,IAAI;QACJ,IAAI;YACF,kEAAkE;YAClE,yBAAyB,UAAU,QAAQ,OAAO,CAAC,QAAQ;QAC7D,EAAE,OAAM;YACN,sDAAsD;YACtD,kDAAkD;YAClD,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;QAC1B;QAEA,kEAAkE;QAClE,+DAA+D;QAC/D,MAAM,mBAAmB,CAAA,GAAA,yLAAA,CAAA,mBAAgB,AAAD,EAAE;QAC1C,MAAM,EACJ,MAAM,EACN,MAAM,EACP,GAAG,CAAA,GAAA,iMAAA,CAAA,UAAa,AAAD,EAAE,iBAAiB,QAAQ,OAAO,EAAE,QAAQ,OAAO,EAAE;QACrE,MAAM,0BAA0B,SAAS,OAAO,aAAa,KAAK,SAAS,WAAW,gBAAgB,aAAa;QACnH,MAAM,gBAAgB,gBAAgB,OAAO,EAAE,OAAO,CAAA,YAAa,CAAA,GAAA,yLAAA,CAAA,4BAAyB,AAAD,EAAE,QAAQ,eAAe,EAAE;QACtH,MAAM,iBAAiB,gBAAgB,OAAO,IAAI,QAAQ,CAAC;QAC3D,SAAS,QAAQ,GAAG;YAClB,MAAM,SAAS,IAAI,IAAI,KAAK,QAAQ,GAAG;YACvC,IAAI,QAAQ,OAAO,CAAC,QAAQ,EAAE;gBAC5B,OAAO,QAAQ,GAAG,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,QAAQ,EAAE,QAAQ,OAAO,CAAC,QAAQ;YAC3E;YACA,MAAM,UAAU,IAAI,QAAQ,QAAQ,OAAO;YAC3C,QAAQ,GAAG,CAAC,yLAAA,CAAA,qBAAkB,EAAE;YAChC,OAAO,6LAAA,CAAA,eAAY,CAAC,OAAO,CAAC,QAAQ;gBAClC,SAAS;oBACP;gBACF;YACF;QACF;QACA,SAAS,SAAS,GAAG,EAAE,cAAc;YACnC,MAAM,SAAS,IAAI,IAAI,KAAK,QAAQ,GAAG;YACvC,OAAO,QAAQ,GAAG,CAAA,GAAA,qLAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO,QAAQ;YACxD,IAAI,cAAc,MAAM,GAAG,KAAK,CAAC,kBAAkB,QAAQ;gBACzD,MAAM,qBAAqB,CAAA,GAAA,yLAAA,CAAA,wBAAqB,AAAD,EAAE,QAAQ,QAAQ;gBACjE,IAAI,oBAAoB;oBACtB,iBAAiB,mBAAmB,MAAM;oBAC1C,IAAI,mBAAmB,aAAa,KAAK,UAAU,gBAAgB,YAAY,CAAC,IAAI,KAAK,aAAa;wBACpG,OAAO,QAAQ,GAAG,CAAA,GAAA,yLAAA,CAAA,wBAAqB,AAAD,EAAE,OAAO,QAAQ,EAAE,gBAAgB,OAAO,EAAE,gBAAgB,YAAY;oBAChH;gBACF;YACF;YACA,IAAI,gBAAgB;gBAClB,OAAO,IAAI,GAAG;gBACd,IAAI,QAAQ,OAAO,CAAC,GAAG,CAAC,qBAAqB;oBAC3C,OAAO,QAAQ,GAAG,QAAQ,OAAO,CAAC,GAAG,CAAC,wBAAwB,QAAQ,OAAO,CAAC,QAAQ;oBACtF,MAAM,qBAAqB,eAAe,KAAK,CAAC,IAAI,CAAC,EAAE;oBACvD,OAAO,IAAI,GAAG,sBAAsB,QAAQ,OAAO,CAAC,GAAG,CAAC,uBAAuB;gBACjF;YACF;YACA,IAAI,QAAQ,OAAO,CAAC,QAAQ,EAAE;gBAC5B,OAAO,QAAQ,GAAG,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,QAAQ,EAAE,QAAQ,OAAO,CAAC,QAAQ;YAC3E;YACA,gBAAgB;YAChB,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,OAAO,QAAQ;QAC9C;QACA,MAAM,6BAA6B,CAAA,GAAA,yLAAA,CAAA,wBAAqB,AAAD,EAAE,kBAAkB,gBAAgB,OAAO,EAAE,gBAAgB,YAAY;QAChI,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,mBAAgB,AAAD,EAAE,kBAAkB,gBAAgB,OAAO,EAAE,gBAAgB,YAAY,EAAE;QAChH,MAAM,kBAAkB,iBAAiB;QACzC,MAAM,sBAAsB,gBAAgB,YAAY,CAAC,IAAI,KAAK,WAAW,2BAA2B,gBAAgB,YAAY,CAAC,IAAI,KAAK;QAC9I,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,6BAA6B;QACjC,MAAM,YAAY,gBAAgB,SAAS;QAC3C,IAAI,WAAW;YACb,IAAI;YACJ,CAAC,wBAAwB,qBAAqB,GAAG,CAAA,GAAA,yLAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW,4BAA4B;YAC5G,IAAI,sBAAsB;gBACxB,MAAM,iBAAiB,SAAS,CAAC,qBAAqB;gBACtD,MAAM,iBAAiB,CAAA,GAAA,qLAAA,CAAA,uBAAoB,AAAD,EAAE,gBAAgB,QAAQ;gBACpE,IAAI,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,6BAA6B;oBAC/D,6BAA6B,CAAA,GAAA,yLAAA,CAAA,yBAAsB,AAAD,EAAE,4BAA4B,gBAAgB;gBAClG,OAAO;oBACL,IAAI;oBACJ,IAAI,wBAAwB;wBAC1B,uDAAuD;wBACvD,iBAAiB,CAAA,GAAA,qLAAA,CAAA,uBAAoB,AAAD,EAAE,gBAAgB,wBAAwB;oBAChF,OAAO;wBACL,wCAAwC;wBACxC,oCAAoC;wBACpC,iBAAiB;oBACnB;oBACA,MAAM,eAAe,sBAAsB,YAAY,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,gBAAgB,YAAY;oBAC3G,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,yBAAsB,AAAD,EAAE,4BAA4B,gBAAgB;oBACpF,WAAW,SAAS,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,cAAc,QAAQ,OAAO,CAAC,MAAM;gBACnF;YACF;QACF;QACA,IAAI,CAAC,UAAU;YACb,IAAI,+BAA+B,OAAO,CAAC,iBAAiB;gBAC1D,IAAI,qBAAqB;oBACvB,WAAW,QAAQ,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD,EAAE,4BAA4B,CAAA,GAAA,yLAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,QAAQ,OAAO,CAAC,MAAM;gBACjH,OAAO;oBACL,WAAW,SAAS,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD,EAAE,4BAA4B,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,gBAAgB,YAAY,GAAG,QAAQ,OAAO,CAAC,MAAM;gBAC9I;YACF,OAAO;gBACL,MAAM,eAAe,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD,EAAE,4BAA4B,CAAA,GAAA,yLAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,QAAQ,OAAO,CAAC,MAAM;gBACjH,IAAI,iBAAiB;oBACnB,MAAM,eAAe,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD,EAAE,4BAA4B,cAAc,MAAM,EAAE,QAAQ,OAAO,CAAC,MAAM;oBAC5G,IAAI,gBAAgB,YAAY,CAAC,IAAI,KAAK,SAAS;wBACjD,WAAW,SAAS,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD,EAAE,4BAA4B,WAAW,QAAQ,OAAO,CAAC,MAAM;oBAClG,OAAO,IAAI,cAAc,KAAK,EAAE;wBAC9B,IAAI,2BAA2B,qBAAqB;4BAClD,WAAW,SAAS,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD,EAAE,4BAA4B,WAAW,QAAQ,OAAO,CAAC,MAAM;wBAClG,OAAO;4BACL,IAAI,gBAAgB,OAAO,EAAE;gCAC3B,MAAM,aAAa,CAAA,GAAA,yLAAA,CAAA,wBAAqB,AAAD,EAAE,QAAQ,cAAc,MAAM,EAAE;gCACvE,IAAI,QAAQ,WAAW,YAAY,UAAU,CAAC,gBAAgB;oCAC5D,WAAW,SAAS,cAAc,YAAY;gCAChD,OAAO;oCACL,WAAW,QAAQ;gCACrB;4BACF,OAAO;gCACL,WAAW,QAAQ;4BACrB;wBACF;oBACF,OAAO;wBACL,WAAW,SAAS;oBACtB;gBACF,OAAO;oBACL,IAAI,qBAAqB;wBACvB,WAAW,QAAQ;oBACrB,OAAO;wBACL,WAAW,SAAS,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD,EAAE,4BAA4B,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,gBAAgB,YAAY,GAAG,QAAQ,OAAO,CAAC,MAAM;oBAC9I;gBACF;YACF;QACF;QACA,CAAA,GAAA,8LAAA,CAAA,UAAU,AAAD,EAAE,SAAS,UAAU,QAAQ,iBAAiB;QACvD,IAAI,CAAC,iBAAiB,gBAAgB,YAAY,CAAC,IAAI,KAAK,WAAW,gBAAgB,cAAc,IAAI,gBAAgB,OAAO,CAAC,MAAM,GAAG,GAAG;YAC3I,SAAS,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAA,GAAA,gNAAA,CAAA,UAA4B,AAAD,EAAE;gBACxD,SAAS;gBACT;gBACA,oBAAoB,wBAAwB,QAAQ,YAAY,SAAS,CAAC,qBAAqB,GAAG;gBAClG;gBACA,gBAAgB;YAClB;QACF;QACA,OAAO;IACT;AACF", "ignoreList": [0]}}, {"offset": {"line": 8100, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/routing/defineRouting.js"], "sourcesContent": ["function defineRouting(config) {\n  if (config.domains) {\n    validateUniqueLocalesPerDomain(config.domains);\n  }\n  return config;\n}\nfunction validateUniqueLocalesPerDomain(domains) {\n  const domainsByLocale = new Map();\n  for (const {\n    domain,\n    locales\n  } of domains) {\n    for (const locale of locales) {\n      const localeDomains = domainsByLocale.get(locale) || new Set();\n      localeDomains.add(domain);\n      domainsByLocale.set(locale, localeDomains);\n    }\n  }\n  const duplicateLocaleMessages = Array.from(domainsByLocale.entries()).filter(([, localeDomains]) => localeDomains.size > 1).map(([locale, localeDomains]) => `- \"${locale}\" is used by: ${Array.from(localeDomains).join(', ')}`);\n  if (duplicateLocaleMessages.length > 0) {\n    console.warn('Locales are expected to be unique per domain, but found overlap:\\n' + duplicateLocaleMessages.join('\\n') + '\\nPlease see https://next-intl.dev/docs/routing#domains');\n  }\n}\n\nexport { defineRouting as default };\n"], "names": [], "mappings": ";;;AAAA,SAAS,cAAc,MAAM;IAC3B,IAAI,OAAO,OAAO,EAAE;QAClB,+BAA+B,OAAO,OAAO;IAC/C;IACA,OAAO;AACT;AACA,SAAS,+BAA+B,OAAO;IAC7C,MAAM,kBAAkB,IAAI;IAC5B,KAAK,MAAM,EACT,MAAM,EACN,OAAO,EACR,IAAI,QAAS;QACZ,KAAK,MAAM,UAAU,QAAS;YAC5B,MAAM,gBAAgB,gBAAgB,GAAG,CAAC,WAAW,IAAI;YACzD,cAAc,GAAG,CAAC;YAClB,gBAAgB,GAAG,CAAC,QAAQ;QAC9B;IACF;IACA,MAAM,0BAA0B,MAAM,IAAI,CAAC,gBAAgB,OAAO,IAAI,MAAM,CAAC,CAAC,GAAG,cAAc,GAAK,cAAc,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,QAAQ,cAAc,GAAK,CAAC,GAAG,EAAE,OAAO,cAAc,EAAE,MAAM,IAAI,CAAC,eAAe,IAAI,CAAC,OAAO;IAChO,IAAI,wBAAwB,MAAM,GAAG,GAAG;QACtC,QAAQ,IAAI,CAAC,uEAAuE,wBAAwB,IAAI,CAAC,QAAQ;IAC3H;AACF", "ignoreList": [0]}}, {"offset": {"line": 8139, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/negotiator/lib/charset.js"], "sourcesContent": ["/**\n * negotiator\n * Copyright(c) 2012 <PERSON>\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = preferredCharsets;\nmodule.exports.preferredCharsets = preferredCharsets;\n\n/**\n * Module variables.\n * @private\n */\n\nvar simpleCharsetRegExp = /^\\s*([^\\s;]+)\\s*(?:;(.*))?$/;\n\n/**\n * Parse the Accept-Charset header.\n * @private\n */\n\nfunction parseAcceptCharset(accept) {\n  var accepts = accept.split(',');\n\n  for (var i = 0, j = 0; i < accepts.length; i++) {\n    var charset = parseCharset(accepts[i].trim(), i);\n\n    if (charset) {\n      accepts[j++] = charset;\n    }\n  }\n\n  // trim accepts\n  accepts.length = j;\n\n  return accepts;\n}\n\n/**\n * Parse a charset from the Accept-Charset header.\n * @private\n */\n\nfunction parseCharset(str, i) {\n  var match = simpleCharsetRegExp.exec(str);\n  if (!match) return null;\n\n  var charset = match[1];\n  var q = 1;\n  if (match[2]) {\n    var params = match[2].split(';')\n    for (var j = 0; j < params.length; j++) {\n      var p = params[j].trim().split('=');\n      if (p[0] === 'q') {\n        q = parseFloat(p[1]);\n        break;\n      }\n    }\n  }\n\n  return {\n    charset: charset,\n    q: q,\n    i: i\n  };\n}\n\n/**\n * Get the priority of a charset.\n * @private\n */\n\nfunction getCharsetPriority(charset, accepted, index) {\n  var priority = {o: -1, q: 0, s: 0};\n\n  for (var i = 0; i < accepted.length; i++) {\n    var spec = specify(charset, accepted[i], index);\n\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority = spec;\n    }\n  }\n\n  return priority;\n}\n\n/**\n * Get the specificity of the charset.\n * @private\n */\n\nfunction specify(charset, spec, index) {\n  var s = 0;\n  if(spec.charset.toLowerCase() === charset.toLowerCase()){\n    s |= 1;\n  } else if (spec.charset !== '*' ) {\n    return null\n  }\n\n  return {\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s\n  }\n}\n\n/**\n * Get the preferred charsets from an Accept-Charset header.\n * @public\n */\n\nfunction preferredCharsets(accept, provided) {\n  // RFC 2616 sec 14.2: no header = *\n  var accepts = parseAcceptCharset(accept === undefined ? '*' : accept || '');\n\n  if (!provided) {\n    // sorted list of all charsets\n    return accepts\n      .filter(isQuality)\n      .sort(compareSpecs)\n      .map(getFullCharset);\n  }\n\n  var priorities = provided.map(function getPriority(type, index) {\n    return getCharsetPriority(type, accepts, index);\n  });\n\n  // sorted list of accepted charsets\n  return priorities.filter(isQuality).sort(compareSpecs).map(function getCharset(priority) {\n    return provided[priorities.indexOf(priority)];\n  });\n}\n\n/**\n * Compare two specs.\n * @private\n */\n\nfunction compareSpecs(a, b) {\n  return (b.q - a.q) || (b.s - a.s) || (a.o - b.o) || (a.i - b.i) || 0;\n}\n\n/**\n * Get full charset string.\n * @private\n */\n\nfunction getFullCharset(spec) {\n  return spec.charset;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\n\nfunction isQuality(spec) {\n  return spec.q > 0;\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED;AAEA;;;CAGC,GAED,OAAO,OAAO,GAAG;AACjB,OAAO,OAAO,CAAC,iBAAiB,GAAG;AAEnC;;;CAGC,GAED,IAAI,sBAAsB;AAE1B;;;CAGC,GAED,SAAS,mBAAmB,MAAM;IAChC,IAAI,UAAU,OAAO,KAAK,CAAC;IAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QAC9C,IAAI,UAAU,aAAa,OAAO,CAAC,EAAE,CAAC,IAAI,IAAI;QAE9C,IAAI,SAAS;YACX,OAAO,CAAC,IAAI,GAAG;QACjB;IACF;IAEA,eAAe;IACf,QAAQ,MAAM,GAAG;IAEjB,OAAO;AACT;AAEA;;;CAGC,GAED,SAAS,aAAa,GAAG,EAAE,CAAC;IAC1B,IAAI,QAAQ,oBAAoB,IAAI,CAAC;IACrC,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI,UAAU,KAAK,CAAC,EAAE;IACtB,IAAI,IAAI;IACR,IAAI,KAAK,CAAC,EAAE,EAAE;QACZ,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,IAAI,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC;YAC/B,IAAI,CAAC,CAAC,EAAE,KAAK,KAAK;gBAChB,IAAI,WAAW,CAAC,CAAC,EAAE;gBACnB;YACF;QACF;IACF;IAEA,OAAO;QACL,SAAS;QACT,GAAG;QACH,GAAG;IACL;AACF;AAEA;;;CAGC,GAED,SAAS,mBAAmB,OAAO,EAAE,QAAQ,EAAE,KAAK;IAClD,IAAI,WAAW;QAAC,GAAG,CAAC;QAAG,GAAG;QAAG,GAAG;IAAC;IAEjC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;QACxC,IAAI,OAAO,QAAQ,SAAS,QAAQ,CAAC,EAAE,EAAE;QAEzC,IAAI,QAAQ,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,GAAG;YACnF,WAAW;QACb;IACF;IAEA,OAAO;AACT;AAEA;;;CAGC,GAED,SAAS,QAAQ,OAAO,EAAE,IAAI,EAAE,KAAK;IACnC,IAAI,IAAI;IACR,IAAG,KAAK,OAAO,CAAC,WAAW,OAAO,QAAQ,WAAW,IAAG;QACtD,KAAK;IACP,OAAO,IAAI,KAAK,OAAO,KAAK,KAAM;QAChC,OAAO;IACT;IAEA,OAAO;QACL,GAAG;QACH,GAAG,KAAK,CAAC;QACT,GAAG,KAAK,CAAC;QACT,GAAG;IACL;AACF;AAEA;;;CAGC,GAED,SAAS,kBAAkB,MAAM,EAAE,QAAQ;IACzC,mCAAmC;IACnC,IAAI,UAAU,mBAAmB,WAAW,YAAY,MAAM,UAAU;IAExE,IAAI,CAAC,UAAU;QACb,8BAA8B;QAC9B,OAAO,QACJ,MAAM,CAAC,WACP,IAAI,CAAC,cACL,GAAG,CAAC;IACT;IAEA,IAAI,aAAa,SAAS,GAAG,CAAC,SAAS,YAAY,IAAI,EAAE,KAAK;QAC5D,OAAO,mBAAmB,MAAM,SAAS;IAC3C;IAEA,mCAAmC;IACnC,OAAO,WAAW,MAAM,CAAC,WAAW,IAAI,CAAC,cAAc,GAAG,CAAC,SAAS,WAAW,QAAQ;QACrF,OAAO,QAAQ,CAAC,WAAW,OAAO,CAAC,UAAU;IAC/C;AACF;AAEA;;;CAGC,GAED,SAAS,aAAa,CAAC,EAAE,CAAC;IACxB,OAAO,AAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAK;AACrE;AAEA;;;CAGC,GAED,SAAS,eAAe,IAAI;IAC1B,OAAO,KAAK,OAAO;AACrB;AAEA;;;CAGC,GAED,SAAS,UAAU,IAAI;IACrB,OAAO,KAAK,CAAC,GAAG;AAClB", "ignoreList": [0]}}, {"offset": {"line": 8269, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/negotiator/lib/encoding.js"], "sourcesContent": ["/**\n * negotiator\n * Copyright(c) 2012 <PERSON>\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = preferredEncodings;\nmodule.exports.preferredEncodings = preferredEncodings;\n\n/**\n * Module variables.\n * @private\n */\n\nvar simpleEncodingRegExp = /^\\s*([^\\s;]+)\\s*(?:;(.*))?$/;\n\n/**\n * Parse the Accept-Encoding header.\n * @private\n */\n\nfunction parseAcceptEncoding(accept) {\n  var accepts = accept.split(',');\n  var hasIdentity = false;\n  var minQuality = 1;\n\n  for (var i = 0, j = 0; i < accepts.length; i++) {\n    var encoding = parseEncoding(accepts[i].trim(), i);\n\n    if (encoding) {\n      accepts[j++] = encoding;\n      hasIdentity = hasIdentity || specify('identity', encoding);\n      minQuality = Math.min(minQuality, encoding.q || 1);\n    }\n  }\n\n  if (!hasIdentity) {\n    /*\n     * If identity doesn't explicitly appear in the accept-encoding header,\n     * it's added to the list of acceptable encoding with the lowest q\n     */\n    accepts[j++] = {\n      encoding: 'identity',\n      q: minQuality,\n      i: i\n    };\n  }\n\n  // trim accepts\n  accepts.length = j;\n\n  return accepts;\n}\n\n/**\n * Parse an encoding from the Accept-Encoding header.\n * @private\n */\n\nfunction parseEncoding(str, i) {\n  var match = simpleEncodingRegExp.exec(str);\n  if (!match) return null;\n\n  var encoding = match[1];\n  var q = 1;\n  if (match[2]) {\n    var params = match[2].split(';');\n    for (var j = 0; j < params.length; j++) {\n      var p = params[j].trim().split('=');\n      if (p[0] === 'q') {\n        q = parseFloat(p[1]);\n        break;\n      }\n    }\n  }\n\n  return {\n    encoding: encoding,\n    q: q,\n    i: i\n  };\n}\n\n/**\n * Get the priority of an encoding.\n * @private\n */\n\nfunction getEncodingPriority(encoding, accepted, index) {\n  var priority = {encoding: encoding, o: -1, q: 0, s: 0};\n\n  for (var i = 0; i < accepted.length; i++) {\n    var spec = specify(encoding, accepted[i], index);\n\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority = spec;\n    }\n  }\n\n  return priority;\n}\n\n/**\n * Get the specificity of the encoding.\n * @private\n */\n\nfunction specify(encoding, spec, index) {\n  var s = 0;\n  if(spec.encoding.toLowerCase() === encoding.toLowerCase()){\n    s |= 1;\n  } else if (spec.encoding !== '*' ) {\n    return null\n  }\n\n  return {\n    encoding: encoding,\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s\n  }\n};\n\n/**\n * Get the preferred encodings from an Accept-Encoding header.\n * @public\n */\n\nfunction preferredEncodings(accept, provided, preferred) {\n  var accepts = parseAcceptEncoding(accept || '');\n\n  var comparator = preferred ? function comparator (a, b) {\n    if (a.q !== b.q) {\n      return b.q - a.q // higher quality first\n    }\n\n    var aPreferred = preferred.indexOf(a.encoding)\n    var bPreferred = preferred.indexOf(b.encoding)\n\n    if (aPreferred === -1 && bPreferred === -1) {\n      // consider the original specifity/order\n      return (b.s - a.s) || (a.o - b.o) || (a.i - b.i)\n    }\n\n    if (aPreferred !== -1 && bPreferred !== -1) {\n      return aPreferred - bPreferred // consider the preferred order\n    }\n\n    return aPreferred === -1 ? 1 : -1 // preferred first\n  } : compareSpecs;\n\n  if (!provided) {\n    // sorted list of all encodings\n    return accepts\n      .filter(isQuality)\n      .sort(comparator)\n      .map(getFullEncoding);\n  }\n\n  var priorities = provided.map(function getPriority(type, index) {\n    return getEncodingPriority(type, accepts, index);\n  });\n\n  // sorted list of accepted encodings\n  return priorities.filter(isQuality).sort(comparator).map(function getEncoding(priority) {\n    return provided[priorities.indexOf(priority)];\n  });\n}\n\n/**\n * Compare two specs.\n * @private\n */\n\nfunction compareSpecs(a, b) {\n  return (b.q - a.q) || (b.s - a.s) || (a.o - b.o) || (a.i - b.i);\n}\n\n/**\n * Get full encoding string.\n * @private\n */\n\nfunction getFullEncoding(spec) {\n  return spec.encoding;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\n\nfunction isQuality(spec) {\n  return spec.q > 0;\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED;AAEA;;;CAGC,GAED,OAAO,OAAO,GAAG;AACjB,OAAO,OAAO,CAAC,kBAAkB,GAAG;AAEpC;;;CAGC,GAED,IAAI,uBAAuB;AAE3B;;;CAGC,GAED,SAAS,oBAAoB,MAAM;IACjC,IAAI,UAAU,OAAO,KAAK,CAAC;IAC3B,IAAI,cAAc;IAClB,IAAI,aAAa;IAEjB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QAC9C,IAAI,WAAW,cAAc,OAAO,CAAC,EAAE,CAAC,IAAI,IAAI;QAEhD,IAAI,UAAU;YACZ,OAAO,CAAC,IAAI,GAAG;YACf,cAAc,eAAe,QAAQ,YAAY;YACjD,aAAa,KAAK,GAAG,CAAC,YAAY,SAAS,CAAC,IAAI;QAClD;IACF;IAEA,IAAI,CAAC,aAAa;QAChB;;;KAGC,GACD,OAAO,CAAC,IAAI,GAAG;YACb,UAAU;YACV,GAAG;YACH,GAAG;QACL;IACF;IAEA,eAAe;IACf,QAAQ,MAAM,GAAG;IAEjB,OAAO;AACT;AAEA;;;CAGC,GAED,SAAS,cAAc,GAAG,EAAE,CAAC;IAC3B,IAAI,QAAQ,qBAAqB,IAAI,CAAC;IACtC,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI,WAAW,KAAK,CAAC,EAAE;IACvB,IAAI,IAAI;IACR,IAAI,KAAK,CAAC,EAAE,EAAE;QACZ,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,IAAI,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC;YAC/B,IAAI,CAAC,CAAC,EAAE,KAAK,KAAK;gBAChB,IAAI,WAAW,CAAC,CAAC,EAAE;gBACnB;YACF;QACF;IACF;IAEA,OAAO;QACL,UAAU;QACV,GAAG;QACH,GAAG;IACL;AACF;AAEA;;;CAGC,GAED,SAAS,oBAAoB,QAAQ,EAAE,QAAQ,EAAE,KAAK;IACpD,IAAI,WAAW;QAAC,UAAU;QAAU,GAAG,CAAC;QAAG,GAAG;QAAG,GAAG;IAAC;IAErD,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;QACxC,IAAI,OAAO,QAAQ,UAAU,QAAQ,CAAC,EAAE,EAAE;QAE1C,IAAI,QAAQ,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,GAAG;YACnF,WAAW;QACb;IACF;IAEA,OAAO;AACT;AAEA;;;CAGC,GAED,SAAS,QAAQ,QAAQ,EAAE,IAAI,EAAE,KAAK;IACpC,IAAI,IAAI;IACR,IAAG,KAAK,QAAQ,CAAC,WAAW,OAAO,SAAS,WAAW,IAAG;QACxD,KAAK;IACP,OAAO,IAAI,KAAK,QAAQ,KAAK,KAAM;QACjC,OAAO;IACT;IAEA,OAAO;QACL,UAAU;QACV,GAAG;QACH,GAAG,KAAK,CAAC;QACT,GAAG,KAAK,CAAC;QACT,GAAG;IACL;AACF;;AAEA;;;CAGC,GAED,SAAS,mBAAmB,MAAM,EAAE,QAAQ,EAAE,SAAS;IACrD,IAAI,UAAU,oBAAoB,UAAU;IAE5C,IAAI,aAAa,YAAY,SAAS,WAAY,CAAC,EAAE,CAAC;QACpD,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE;YACf,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,uBAAuB;;QAC1C;QAEA,IAAI,aAAa,UAAU,OAAO,CAAC,EAAE,QAAQ;QAC7C,IAAI,aAAa,UAAU,OAAO,CAAC,EAAE,QAAQ;QAE7C,IAAI,eAAe,CAAC,KAAK,eAAe,CAAC,GAAG;YAC1C,wCAAwC;YACxC,OAAO,AAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC;QACjD;QAEA,IAAI,eAAe,CAAC,KAAK,eAAe,CAAC,GAAG;YAC1C,OAAO,aAAa,WAAW,+BAA+B;;QAChE;QAEA,OAAO,eAAe,CAAC,IAAI,IAAI,CAAC,EAAE,kBAAkB;;IACtD,IAAI;IAEJ,IAAI,CAAC,UAAU;QACb,+BAA+B;QAC/B,OAAO,QACJ,MAAM,CAAC,WACP,IAAI,CAAC,YACL,GAAG,CAAC;IACT;IAEA,IAAI,aAAa,SAAS,GAAG,CAAC,SAAS,YAAY,IAAI,EAAE,KAAK;QAC5D,OAAO,oBAAoB,MAAM,SAAS;IAC5C;IAEA,oCAAoC;IACpC,OAAO,WAAW,MAAM,CAAC,WAAW,IAAI,CAAC,YAAY,GAAG,CAAC,SAAS,YAAY,QAAQ;QACpF,OAAO,QAAQ,CAAC,WAAW,OAAO,CAAC,UAAU;IAC/C;AACF;AAEA;;;CAGC,GAED,SAAS,aAAa,CAAC,EAAE,CAAC;IACxB,OAAO,AAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC;AAChE;AAEA;;;CAGC,GAED,SAAS,gBAAgB,IAAI;IAC3B,OAAO,KAAK,QAAQ;AACtB;AAEA;;;CAGC,GAED,SAAS,UAAU,IAAI;IACrB,OAAO,KAAK,CAAC,GAAG;AAClB", "ignoreList": [0]}}, {"offset": {"line": 8433, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/negotiator/lib/language.js"], "sourcesContent": ["/**\n * negotiator\n * Copyright(c) 2012 <PERSON>\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = preferredLanguages;\nmodule.exports.preferredLanguages = preferredLanguages;\n\n/**\n * Module variables.\n * @private\n */\n\nvar simpleLanguageRegExp = /^\\s*([^\\s\\-;]+)(?:-([^\\s;]+))?\\s*(?:;(.*))?$/;\n\n/**\n * Parse the Accept-Language header.\n * @private\n */\n\nfunction parseAcceptLanguage(accept) {\n  var accepts = accept.split(',');\n\n  for (var i = 0, j = 0; i < accepts.length; i++) {\n    var language = parseLanguage(accepts[i].trim(), i);\n\n    if (language) {\n      accepts[j++] = language;\n    }\n  }\n\n  // trim accepts\n  accepts.length = j;\n\n  return accepts;\n}\n\n/**\n * Parse a language from the Accept-Language header.\n * @private\n */\n\nfunction parseLanguage(str, i) {\n  var match = simpleLanguageRegExp.exec(str);\n  if (!match) return null;\n\n  var prefix = match[1]\n  var suffix = match[2]\n  var full = prefix\n\n  if (suffix) full += \"-\" + suffix;\n\n  var q = 1;\n  if (match[3]) {\n    var params = match[3].split(';')\n    for (var j = 0; j < params.length; j++) {\n      var p = params[j].split('=');\n      if (p[0] === 'q') q = parseFloat(p[1]);\n    }\n  }\n\n  return {\n    prefix: prefix,\n    suffix: suffix,\n    q: q,\n    i: i,\n    full: full\n  };\n}\n\n/**\n * Get the priority of a language.\n * @private\n */\n\nfunction getLanguagePriority(language, accepted, index) {\n  var priority = {o: -1, q: 0, s: 0};\n\n  for (var i = 0; i < accepted.length; i++) {\n    var spec = specify(language, accepted[i], index);\n\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority = spec;\n    }\n  }\n\n  return priority;\n}\n\n/**\n * Get the specificity of the language.\n * @private\n */\n\nfunction specify(language, spec, index) {\n  var p = parseLanguage(language)\n  if (!p) return null;\n  var s = 0;\n  if(spec.full.toLowerCase() === p.full.toLowerCase()){\n    s |= 4;\n  } else if (spec.prefix.toLowerCase() === p.full.toLowerCase()) {\n    s |= 2;\n  } else if (spec.full.toLowerCase() === p.prefix.toLowerCase()) {\n    s |= 1;\n  } else if (spec.full !== '*' ) {\n    return null\n  }\n\n  return {\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s\n  }\n};\n\n/**\n * Get the preferred languages from an Accept-Language header.\n * @public\n */\n\nfunction preferredLanguages(accept, provided) {\n  // RFC 2616 sec 14.4: no header = *\n  var accepts = parseAcceptLanguage(accept === undefined ? '*' : accept || '');\n\n  if (!provided) {\n    // sorted list of all languages\n    return accepts\n      .filter(isQuality)\n      .sort(compareSpecs)\n      .map(getFullLanguage);\n  }\n\n  var priorities = provided.map(function getPriority(type, index) {\n    return getLanguagePriority(type, accepts, index);\n  });\n\n  // sorted list of accepted languages\n  return priorities.filter(isQuality).sort(compareSpecs).map(function getLanguage(priority) {\n    return provided[priorities.indexOf(priority)];\n  });\n}\n\n/**\n * Compare two specs.\n * @private\n */\n\nfunction compareSpecs(a, b) {\n  return (b.q - a.q) || (b.s - a.s) || (a.o - b.o) || (a.i - b.i) || 0;\n}\n\n/**\n * Get full language string.\n * @private\n */\n\nfunction getFullLanguage(spec) {\n  return spec.full;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\n\nfunction isQuality(spec) {\n  return spec.q > 0;\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED;AAEA;;;CAGC,GAED,OAAO,OAAO,GAAG;AACjB,OAAO,OAAO,CAAC,kBAAkB,GAAG;AAEpC;;;CAGC,GAED,IAAI,uBAAuB;AAE3B;;;CAGC,GAED,SAAS,oBAAoB,MAAM;IACjC,IAAI,UAAU,OAAO,KAAK,CAAC;IAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QAC9C,IAAI,WAAW,cAAc,OAAO,CAAC,EAAE,CAAC,IAAI,IAAI;QAEhD,IAAI,UAAU;YACZ,OAAO,CAAC,IAAI,GAAG;QACjB;IACF;IAEA,eAAe;IACf,QAAQ,MAAM,GAAG;IAEjB,OAAO;AACT;AAEA;;;CAGC,GAED,SAAS,cAAc,GAAG,EAAE,CAAC;IAC3B,IAAI,QAAQ,qBAAqB,IAAI,CAAC;IACtC,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI,SAAS,KAAK,CAAC,EAAE;IACrB,IAAI,SAAS,KAAK,CAAC,EAAE;IACrB,IAAI,OAAO;IAEX,IAAI,QAAQ,QAAQ,MAAM;IAE1B,IAAI,IAAI;IACR,IAAI,KAAK,CAAC,EAAE,EAAE;QACZ,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,IAAI,IAAI,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC;YACxB,IAAI,CAAC,CAAC,EAAE,KAAK,KAAK,IAAI,WAAW,CAAC,CAAC,EAAE;QACvC;IACF;IAEA,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,GAAG;QACH,GAAG;QACH,MAAM;IACR;AACF;AAEA;;;CAGC,GAED,SAAS,oBAAoB,QAAQ,EAAE,QAAQ,EAAE,KAAK;IACpD,IAAI,WAAW;QAAC,GAAG,CAAC;QAAG,GAAG;QAAG,GAAG;IAAC;IAEjC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;QACxC,IAAI,OAAO,QAAQ,UAAU,QAAQ,CAAC,EAAE,EAAE;QAE1C,IAAI,QAAQ,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,GAAG;YACnF,WAAW;QACb;IACF;IAEA,OAAO;AACT;AAEA;;;CAGC,GAED,SAAS,QAAQ,QAAQ,EAAE,IAAI,EAAE,KAAK;IACpC,IAAI,IAAI,cAAc;IACtB,IAAI,CAAC,GAAG,OAAO;IACf,IAAI,IAAI;IACR,IAAG,KAAK,IAAI,CAAC,WAAW,OAAO,EAAE,IAAI,CAAC,WAAW,IAAG;QAClD,KAAK;IACP,OAAO,IAAI,KAAK,MAAM,CAAC,WAAW,OAAO,EAAE,IAAI,CAAC,WAAW,IAAI;QAC7D,KAAK;IACP,OAAO,IAAI,KAAK,IAAI,CAAC,WAAW,OAAO,EAAE,MAAM,CAAC,WAAW,IAAI;QAC7D,KAAK;IACP,OAAO,IAAI,KAAK,IAAI,KAAK,KAAM;QAC7B,OAAO;IACT;IAEA,OAAO;QACL,GAAG;QACH,GAAG,KAAK,CAAC;QACT,GAAG,KAAK,CAAC;QACT,GAAG;IACL;AACF;;AAEA;;;CAGC,GAED,SAAS,mBAAmB,MAAM,EAAE,QAAQ;IAC1C,mCAAmC;IACnC,IAAI,UAAU,oBAAoB,WAAW,YAAY,MAAM,UAAU;IAEzE,IAAI,CAAC,UAAU;QACb,+BAA+B;QAC/B,OAAO,QACJ,MAAM,CAAC,WACP,IAAI,CAAC,cACL,GAAG,CAAC;IACT;IAEA,IAAI,aAAa,SAAS,GAAG,CAAC,SAAS,YAAY,IAAI,EAAE,KAAK;QAC5D,OAAO,oBAAoB,MAAM,SAAS;IAC5C;IAEA,oCAAoC;IACpC,OAAO,WAAW,MAAM,CAAC,WAAW,IAAI,CAAC,cAAc,GAAG,CAAC,SAAS,YAAY,QAAQ;QACtF,OAAO,QAAQ,CAAC,WAAW,OAAO,CAAC,UAAU;IAC/C;AACF;AAEA;;;CAGC,GAED,SAAS,aAAa,CAAC,EAAE,CAAC;IACxB,OAAO,AAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAK;AACrE;AAEA;;;CAGC,GAED,SAAS,gBAAgB,IAAI;IAC3B,OAAO,KAAK,IAAI;AAClB;AAEA;;;CAGC,GAED,SAAS,UAAU,IAAI;IACrB,OAAO,KAAK,CAAC,GAAG;AAClB", "ignoreList": [0]}}, {"offset": {"line": 8572, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/negotiator/lib/mediaType.js"], "sourcesContent": ["/**\n * negotiator\n * Copyright(c) 2012 <PERSON>\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = preferredMediaTypes;\nmodule.exports.preferredMediaTypes = preferredMediaTypes;\n\n/**\n * Module variables.\n * @private\n */\n\nvar simpleMediaTypeRegExp = /^\\s*([^\\s\\/;]+)\\/([^;\\s]+)\\s*(?:;(.*))?$/;\n\n/**\n * Parse the Accept header.\n * @private\n */\n\nfunction parseAccept(accept) {\n  var accepts = splitMediaTypes(accept);\n\n  for (var i = 0, j = 0; i < accepts.length; i++) {\n    var mediaType = parseMediaType(accepts[i].trim(), i);\n\n    if (mediaType) {\n      accepts[j++] = mediaType;\n    }\n  }\n\n  // trim accepts\n  accepts.length = j;\n\n  return accepts;\n}\n\n/**\n * Parse a media type from the Accept header.\n * @private\n */\n\nfunction parseMediaType(str, i) {\n  var match = simpleMediaTypeRegExp.exec(str);\n  if (!match) return null;\n\n  var params = Object.create(null);\n  var q = 1;\n  var subtype = match[2];\n  var type = match[1];\n\n  if (match[3]) {\n    var kvps = splitParameters(match[3]).map(splitKeyValuePair);\n\n    for (var j = 0; j < kvps.length; j++) {\n      var pair = kvps[j];\n      var key = pair[0].toLowerCase();\n      var val = pair[1];\n\n      // get the value, unwrapping quotes\n      var value = val && val[0] === '\"' && val[val.length - 1] === '\"'\n        ? val.slice(1, -1)\n        : val;\n\n      if (key === 'q') {\n        q = parseFloat(value);\n        break;\n      }\n\n      // store parameter\n      params[key] = value;\n    }\n  }\n\n  return {\n    type: type,\n    subtype: subtype,\n    params: params,\n    q: q,\n    i: i\n  };\n}\n\n/**\n * Get the priority of a media type.\n * @private\n */\n\nfunction getMediaTypePriority(type, accepted, index) {\n  var priority = {o: -1, q: 0, s: 0};\n\n  for (var i = 0; i < accepted.length; i++) {\n    var spec = specify(type, accepted[i], index);\n\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority = spec;\n    }\n  }\n\n  return priority;\n}\n\n/**\n * Get the specificity of the media type.\n * @private\n */\n\nfunction specify(type, spec, index) {\n  var p = parseMediaType(type);\n  var s = 0;\n\n  if (!p) {\n    return null;\n  }\n\n  if(spec.type.toLowerCase() == p.type.toLowerCase()) {\n    s |= 4\n  } else if(spec.type != '*') {\n    return null;\n  }\n\n  if(spec.subtype.toLowerCase() == p.subtype.toLowerCase()) {\n    s |= 2\n  } else if(spec.subtype != '*') {\n    return null;\n  }\n\n  var keys = Object.keys(spec.params);\n  if (keys.length > 0) {\n    if (keys.every(function (k) {\n      return spec.params[k] == '*' || (spec.params[k] || '').toLowerCase() == (p.params[k] || '').toLowerCase();\n    })) {\n      s |= 1\n    } else {\n      return null\n    }\n  }\n\n  return {\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s,\n  }\n}\n\n/**\n * Get the preferred media types from an Accept header.\n * @public\n */\n\nfunction preferredMediaTypes(accept, provided) {\n  // RFC 2616 sec 14.2: no header = */*\n  var accepts = parseAccept(accept === undefined ? '*/*' : accept || '');\n\n  if (!provided) {\n    // sorted list of all types\n    return accepts\n      .filter(isQuality)\n      .sort(compareSpecs)\n      .map(getFullType);\n  }\n\n  var priorities = provided.map(function getPriority(type, index) {\n    return getMediaTypePriority(type, accepts, index);\n  });\n\n  // sorted list of accepted types\n  return priorities.filter(isQuality).sort(compareSpecs).map(function getType(priority) {\n    return provided[priorities.indexOf(priority)];\n  });\n}\n\n/**\n * Compare two specs.\n * @private\n */\n\nfunction compareSpecs(a, b) {\n  return (b.q - a.q) || (b.s - a.s) || (a.o - b.o) || (a.i - b.i) || 0;\n}\n\n/**\n * Get full type string.\n * @private\n */\n\nfunction getFullType(spec) {\n  return spec.type + '/' + spec.subtype;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\n\nfunction isQuality(spec) {\n  return spec.q > 0;\n}\n\n/**\n * Count the number of quotes in a string.\n * @private\n */\n\nfunction quoteCount(string) {\n  var count = 0;\n  var index = 0;\n\n  while ((index = string.indexOf('\"', index)) !== -1) {\n    count++;\n    index++;\n  }\n\n  return count;\n}\n\n/**\n * Split a key value pair.\n * @private\n */\n\nfunction splitKeyValuePair(str) {\n  var index = str.indexOf('=');\n  var key;\n  var val;\n\n  if (index === -1) {\n    key = str;\n  } else {\n    key = str.slice(0, index);\n    val = str.slice(index + 1);\n  }\n\n  return [key, val];\n}\n\n/**\n * Split an Accept header into media types.\n * @private\n */\n\nfunction splitMediaTypes(accept) {\n  var accepts = accept.split(',');\n\n  for (var i = 1, j = 0; i < accepts.length; i++) {\n    if (quoteCount(accepts[j]) % 2 == 0) {\n      accepts[++j] = accepts[i];\n    } else {\n      accepts[j] += ',' + accepts[i];\n    }\n  }\n\n  // trim accepts\n  accepts.length = j + 1;\n\n  return accepts;\n}\n\n/**\n * Split a string of parameters.\n * @private\n */\n\nfunction splitParameters(str) {\n  var parameters = str.split(';');\n\n  for (var i = 1, j = 0; i < parameters.length; i++) {\n    if (quoteCount(parameters[j]) % 2 == 0) {\n      parameters[++j] = parameters[i];\n    } else {\n      parameters[j] += ';' + parameters[i];\n    }\n  }\n\n  // trim parameters\n  parameters.length = j + 1;\n\n  for (var i = 0; i < parameters.length; i++) {\n    parameters[i] = parameters[i].trim();\n  }\n\n  return parameters;\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED;AAEA;;;CAGC,GAED,OAAO,OAAO,GAAG;AACjB,OAAO,OAAO,CAAC,mBAAmB,GAAG;AAErC;;;CAGC,GAED,IAAI,wBAAwB;AAE5B;;;CAGC,GAED,SAAS,YAAY,MAAM;IACzB,IAAI,UAAU,gBAAgB;IAE9B,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QAC9C,IAAI,YAAY,eAAe,OAAO,CAAC,EAAE,CAAC,IAAI,IAAI;QAElD,IAAI,WAAW;YACb,OAAO,CAAC,IAAI,GAAG;QACjB;IACF;IAEA,eAAe;IACf,QAAQ,MAAM,GAAG;IAEjB,OAAO;AACT;AAEA;;;CAGC,GAED,SAAS,eAAe,GAAG,EAAE,CAAC;IAC5B,IAAI,QAAQ,sBAAsB,IAAI,CAAC;IACvC,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI,SAAS,OAAO,MAAM,CAAC;IAC3B,IAAI,IAAI;IACR,IAAI,UAAU,KAAK,CAAC,EAAE;IACtB,IAAI,OAAO,KAAK,CAAC,EAAE;IAEnB,IAAI,KAAK,CAAC,EAAE,EAAE;QACZ,IAAI,OAAO,gBAAgB,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC;QAEzC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,IAAI,OAAO,IAAI,CAAC,EAAE;YAClB,IAAI,MAAM,IAAI,CAAC,EAAE,CAAC,WAAW;YAC7B,IAAI,MAAM,IAAI,CAAC,EAAE;YAEjB,mCAAmC;YACnC,IAAI,QAAQ,OAAO,GAAG,CAAC,EAAE,KAAK,OAAO,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,KAAK,MACzD,IAAI,KAAK,CAAC,GAAG,CAAC,KACd;YAEJ,IAAI,QAAQ,KAAK;gBACf,IAAI,WAAW;gBACf;YACF;YAEA,kBAAkB;YAClB,MAAM,CAAC,IAAI,GAAG;QAChB;IACF;IAEA,OAAO;QACL,MAAM;QACN,SAAS;QACT,QAAQ;QACR,GAAG;QACH,GAAG;IACL;AACF;AAEA;;;CAGC,GAED,SAAS,qBAAqB,IAAI,EAAE,QAAQ,EAAE,KAAK;IACjD,IAAI,WAAW;QAAC,GAAG,CAAC;QAAG,GAAG;QAAG,GAAG;IAAC;IAEjC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;QACxC,IAAI,OAAO,QAAQ,MAAM,QAAQ,CAAC,EAAE,EAAE;QAEtC,IAAI,QAAQ,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,GAAG;YACnF,WAAW;QACb;IACF;IAEA,OAAO;AACT;AAEA;;;CAGC,GAED,SAAS,QAAQ,IAAI,EAAE,IAAI,EAAE,KAAK;IAChC,IAAI,IAAI,eAAe;IACvB,IAAI,IAAI;IAER,IAAI,CAAC,GAAG;QACN,OAAO;IACT;IAEA,IAAG,KAAK,IAAI,CAAC,WAAW,MAAM,EAAE,IAAI,CAAC,WAAW,IAAI;QAClD,KAAK;IACP,OAAO,IAAG,KAAK,IAAI,IAAI,KAAK;QAC1B,OAAO;IACT;IAEA,IAAG,KAAK,OAAO,CAAC,WAAW,MAAM,EAAE,OAAO,CAAC,WAAW,IAAI;QACxD,KAAK;IACP,OAAO,IAAG,KAAK,OAAO,IAAI,KAAK;QAC7B,OAAO;IACT;IAEA,IAAI,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM;IAClC,IAAI,KAAK,MAAM,GAAG,GAAG;QACnB,IAAI,KAAK,KAAK,CAAC,SAAU,CAAC;YACxB,OAAO,KAAK,MAAM,CAAC,EAAE,IAAI,OAAO,CAAC,KAAK,MAAM,CAAC,EAAE,IAAI,EAAE,EAAE,WAAW,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,EAAE,EAAE,WAAW;QACzG,IAAI;YACF,KAAK;QACP,OAAO;YACL,OAAO;QACT;IACF;IAEA,OAAO;QACL,GAAG;QACH,GAAG,KAAK,CAAC;QACT,GAAG,KAAK,CAAC;QACT,GAAG;IACL;AACF;AAEA;;;CAGC,GAED,SAAS,oBAAoB,MAAM,EAAE,QAAQ;IAC3C,qCAAqC;IACrC,IAAI,UAAU,YAAY,WAAW,YAAY,QAAQ,UAAU;IAEnE,IAAI,CAAC,UAAU;QACb,2BAA2B;QAC3B,OAAO,QACJ,MAAM,CAAC,WACP,IAAI,CAAC,cACL,GAAG,CAAC;IACT;IAEA,IAAI,aAAa,SAAS,GAAG,CAAC,SAAS,YAAY,IAAI,EAAE,KAAK;QAC5D,OAAO,qBAAqB,MAAM,SAAS;IAC7C;IAEA,gCAAgC;IAChC,OAAO,WAAW,MAAM,CAAC,WAAW,IAAI,CAAC,cAAc,GAAG,CAAC,SAAS,QAAQ,QAAQ;QAClF,OAAO,QAAQ,CAAC,WAAW,OAAO,CAAC,UAAU;IAC/C;AACF;AAEA;;;CAGC,GAED,SAAS,aAAa,CAAC,EAAE,CAAC;IACxB,OAAO,AAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAK;AACrE;AAEA;;;CAGC,GAED,SAAS,YAAY,IAAI;IACvB,OAAO,KAAK,IAAI,GAAG,MAAM,KAAK,OAAO;AACvC;AAEA;;;CAGC,GAED,SAAS,UAAU,IAAI;IACrB,OAAO,KAAK,CAAC,GAAG;AAClB;AAEA;;;CAGC,GAED,SAAS,WAAW,MAAM;IACxB,IAAI,QAAQ;IACZ,IAAI,QAAQ;IAEZ,MAAO,CAAC,QAAQ,OAAO,OAAO,CAAC,KAAK,MAAM,MAAM,CAAC,EAAG;QAClD;QACA;IACF;IAEA,OAAO;AACT;AAEA;;;CAGC,GAED,SAAS,kBAAkB,GAAG;IAC5B,IAAI,QAAQ,IAAI,OAAO,CAAC;IACxB,IAAI;IACJ,IAAI;IAEJ,IAAI,UAAU,CAAC,GAAG;QAChB,MAAM;IACR,OAAO;QACL,MAAM,IAAI,KAAK,CAAC,GAAG;QACnB,MAAM,IAAI,KAAK,CAAC,QAAQ;IAC1B;IAEA,OAAO;QAAC;QAAK;KAAI;AACnB;AAEA;;;CAGC,GAED,SAAS,gBAAgB,MAAM;IAC7B,IAAI,UAAU,OAAO,KAAK,CAAC;IAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QAC9C,IAAI,WAAW,OAAO,CAAC,EAAE,IAAI,KAAK,GAAG;YACnC,OAAO,CAAC,EAAE,EAAE,GAAG,OAAO,CAAC,EAAE;QAC3B,OAAO;YACL,OAAO,CAAC,EAAE,IAAI,MAAM,OAAO,CAAC,EAAE;QAChC;IACF;IAEA,eAAe;IACf,QAAQ,MAAM,GAAG,IAAI;IAErB,OAAO;AACT;AAEA;;;CAGC,GAED,SAAS,gBAAgB,GAAG;IAC1B,IAAI,aAAa,IAAI,KAAK,CAAC;IAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;QACjD,IAAI,WAAW,UAAU,CAAC,EAAE,IAAI,KAAK,GAAG;YACtC,UAAU,CAAC,EAAE,EAAE,GAAG,UAAU,CAAC,EAAE;QACjC,OAAO;YACL,UAAU,CAAC,EAAE,IAAI,MAAM,UAAU,CAAC,EAAE;QACtC;IACF;IAEA,kBAAkB;IAClB,WAAW,MAAM,GAAG,IAAI;IAExB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;QAC1C,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC,IAAI;IACpC;IAEA,OAAO;AACT", "ignoreList": [0]}}, {"offset": {"line": 8796, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/negotiator/index.js"], "sourcesContent": ["/*!\n * negotiator\n * Copyright(c) 2012 <PERSON>\n * Copyright(c) 2012-2014 <PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\nvar preferredCharsets = require('./lib/charset')\nvar preferredEncodings = require('./lib/encoding')\nvar preferredLanguages = require('./lib/language')\nvar preferredMediaTypes = require('./lib/mediaType')\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = Negotiator;\nmodule.exports.Negotiator = Negotiator;\n\n/**\n * Create a Negotiator instance from a request.\n * @param {object} request\n * @public\n */\n\nfunction Negotiator(request) {\n  if (!(this instanceof Negotiator)) {\n    return new Negotiator(request);\n  }\n\n  this.request = request;\n}\n\nNegotiator.prototype.charset = function charset(available) {\n  var set = this.charsets(available);\n  return set && set[0];\n};\n\nNegotiator.prototype.charsets = function charsets(available) {\n  return preferredCharsets(this.request.headers['accept-charset'], available);\n};\n\nNegotiator.prototype.encoding = function encoding(available, opts) {\n  var set = this.encodings(available, opts);\n  return set && set[0];\n};\n\nNegotiator.prototype.encodings = function encodings(available, options) {\n  var opts = options || {};\n  return preferredEncodings(this.request.headers['accept-encoding'], available, opts.preferred);\n};\n\nNegotiator.prototype.language = function language(available) {\n  var set = this.languages(available);\n  return set && set[0];\n};\n\nNegotiator.prototype.languages = function languages(available) {\n  return preferredLanguages(this.request.headers['accept-language'], available);\n};\n\nNegotiator.prototype.mediaType = function mediaType(available) {\n  var set = this.mediaTypes(available);\n  return set && set[0];\n};\n\nNegotiator.prototype.mediaTypes = function mediaTypes(available) {\n  return preferredMediaTypes(this.request.headers.accept, available);\n};\n\n// Backwards compatibility\nNegotiator.prototype.preferredCharset = Negotiator.prototype.charset;\nNegotiator.prototype.preferredCharsets = Negotiator.prototype.charsets;\nNegotiator.prototype.preferredEncoding = Negotiator.prototype.encoding;\nNegotiator.prototype.preferredEncodings = Negotiator.prototype.encodings;\nNegotiator.prototype.preferredLanguage = Negotiator.prototype.language;\nNegotiator.prototype.preferredLanguages = Negotiator.prototype.languages;\nNegotiator.prototype.preferredMediaType = Negotiator.prototype.mediaType;\nNegotiator.prototype.preferredMediaTypes = Negotiator.prototype.mediaTypes;\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ;;;CAGC,GAED,OAAO,OAAO,GAAG;AACjB,OAAO,OAAO,CAAC,UAAU,GAAG;AAE5B;;;;CAIC,GAED,SAAS,WAAW,OAAO;IACzB,IAAI,CAAC,CAAC,IAAI,YAAY,UAAU,GAAG;QACjC,OAAO,IAAI,WAAW;IACxB;IAEA,IAAI,CAAC,OAAO,GAAG;AACjB;AAEA,WAAW,SAAS,CAAC,OAAO,GAAG,SAAS,QAAQ,SAAS;IACvD,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC;IACxB,OAAO,OAAO,GAAG,CAAC,EAAE;AACtB;AAEA,WAAW,SAAS,CAAC,QAAQ,GAAG,SAAS,SAAS,SAAS;IACzD,OAAO,kBAAkB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,EAAE;AACnE;AAEA,WAAW,SAAS,CAAC,QAAQ,GAAG,SAAS,SAAS,SAAS,EAAE,IAAI;IAC/D,IAAI,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW;IACpC,OAAO,OAAO,GAAG,CAAC,EAAE;AACtB;AAEA,WAAW,SAAS,CAAC,SAAS,GAAG,SAAS,UAAU,SAAS,EAAE,OAAO;IACpE,IAAI,OAAO,WAAW,CAAC;IACvB,OAAO,mBAAmB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,EAAE,WAAW,KAAK,SAAS;AAC9F;AAEA,WAAW,SAAS,CAAC,QAAQ,GAAG,SAAS,SAAS,SAAS;IACzD,IAAI,MAAM,IAAI,CAAC,SAAS,CAAC;IACzB,OAAO,OAAO,GAAG,CAAC,EAAE;AACtB;AAEA,WAAW,SAAS,CAAC,SAAS,GAAG,SAAS,UAAU,SAAS;IAC3D,OAAO,mBAAmB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,EAAE;AACrE;AAEA,WAAW,SAAS,CAAC,SAAS,GAAG,SAAS,UAAU,SAAS;IAC3D,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC;IAC1B,OAAO,OAAO,GAAG,CAAC,EAAE;AACtB;AAEA,WAAW,SAAS,CAAC,UAAU,GAAG,SAAS,WAAW,SAAS;IAC7D,OAAO,oBAAoB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE;AAC1D;AAEA,0BAA0B;AAC1B,WAAW,SAAS,CAAC,gBAAgB,GAAG,WAAW,SAAS,CAAC,OAAO;AACpE,WAAW,SAAS,CAAC,iBAAiB,GAAG,WAAW,SAAS,CAAC,QAAQ;AACtE,WAAW,SAAS,CAAC,iBAAiB,GAAG,WAAW,SAAS,CAAC,QAAQ;AACtE,WAAW,SAAS,CAAC,kBAAkB,GAAG,WAAW,SAAS,CAAC,SAAS;AACxE,WAAW,SAAS,CAAC,iBAAiB,GAAG,WAAW,SAAS,CAAC,QAAQ;AACtE,WAAW,SAAS,CAAC,kBAAkB,GAAG,WAAW,SAAS,CAAC,SAAS;AACxE,WAAW,SAAS,CAAC,kBAAkB,GAAG,WAAW,SAAS,CAAC,SAAS;AACxE,WAAW,SAAS,CAAC,mBAAmB,GAAG,WAAW,SAAS,CAAC,UAAU", "ignoreList": [0]}}]}