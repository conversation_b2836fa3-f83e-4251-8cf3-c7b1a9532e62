"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArgumentsValue = void 0;
const value_1 = require("./value");
class ArgumentsValue extends value_1.Value {
    value;
    constructor(value) {
        super();
        this.value = value;
    }
    render(output) {
        output.append('(');
        let first = true;
        for (const value of this.value) {
            if (!first) {
                output.append(',');
                output.cosmeticWhitespace();
            }
            first = false;
            value.render(output);
        }
        output.append(')');
    }
}
exports.ArgumentsValue = ArgumentsValue;
//# sourceMappingURL=args.js.map