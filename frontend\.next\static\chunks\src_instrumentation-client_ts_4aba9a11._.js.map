{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/instrumentation-client.ts"], "sourcesContent": ["// This file configures the initialization of Sentry on the client.\r\n// The added config here will be used whenever a users loads a page in their browser.\r\n// https://docs.sentry.io/platforms/javascript/guides/nextjs/\r\nimport * as Sentry from '@sentry/nextjs';\r\nimport * as Spotlight from '@spotlightjs/spotlight';\r\n\r\nif (!process.env.NEXT_PUBLIC_SENTRY_DISABLED) {\r\n  Sentry.init({\r\n    dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,\r\n\r\n    // Add optional integrations for additional features\r\n    integrations: [\r\n      Sentry.replayIntegration(),\r\n    ],\r\n\r\n    // Adds request headers and IP for users, for more info visit\r\n    sendDefaultPii: true,\r\n\r\n    // Define how likely traces are sampled. Adjust this value in production, or use tracesSampler for greater control.\r\n    tracesSampleRate: 1,\r\n\r\n    // Define how likely Replay events are sampled.\r\n    // This sets the sample rate to be 10%. You may want this to be 100% while\r\n    // in development and sample at a lower rate in production\r\n    replaysSessionSampleRate: 0.1,\r\n\r\n    // Define how likely Replay events are sampled when an error occurs.\r\n    replaysOnErrorSampleRate: 1.0,\r\n\r\n    // Setting this option to true will print useful information to the console while you're setting up Sentry.\r\n    debug: false,\r\n  });\r\n\r\n  if (process.env.NODE_ENV === 'development') {\r\n    Spotlight.init();\r\n  }\r\n}\r\n\r\nexport const onRouterTransitionStart = Sentry.captureRouterTransitionStart;\r\n"], "names": [], "mappings": "AAAA,mEAAmE;AACnE,qFAAqF;AACrF,6DAA6D;;;;AAIxD;AAHL;AAAA;AAAA;AACA;AAAA;;;AAEA,IAAI,CAAC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE;IAC5C,CAAA,GAAA,wLAAA,CAAA,OAAW,AAAD,EAAE;QACV,KAAK,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB;QAEvC,oDAAoD;QACpD,cAAc;YACZ,CAAA,GAAA,iLAAA,CAAA,oBAAwB,AAAD;SACxB;QAED,6DAA6D;QAC7D,gBAAgB;QAEhB,mHAAmH;QACnH,kBAAkB;QAElB,+CAA+C;QAC/C,0EAA0E;QAC1E,0DAA0D;QAC1D,0BAA0B;QAE1B,oEAAoE;QACpE,0BAA0B;QAE1B,2GAA2G;QAC3G,OAAO;IACT;IAEA,wCAA4C;QAC1C,CAAA,GAAA,0KAAA,CAAA,OAAc,AAAD;IACf;AACF;AAEO,MAAM,0BAA0B,6MAAA,CAAA,+BAAmC", "debugId": null}}]}